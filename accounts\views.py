from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse


def accueil(request):
    """Page d'accueil publique"""
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')
    return render(request, 'accounts/accueil.html')


def onboarding(request):
    """Formulaire de demande d'inscription"""
    if request.method == 'POST':
        from .forms import OnboardingForm
        form = OnboardingForm(request.POST)
        if form.is_valid():
            demande = form.save()

            # TODO: Envoyer un email de confirmation à l'administrateur
            # TODO: Envoyer une notification au super admin

            messages.success(
                request,
                f"Votre demande d'inscription pour '{demande.nom_organisation}' a été soumise avec succès. "
                "Vous recevrez un email de confirmation une fois votre demande validée par notre équipe."
            )
            return redirect('accounts:onboarding_confirmation')
    else:
        from .forms import OnboardingForm
        form = OnboardingForm()

    context = {
        'form': form
    }
    return render(request, 'accounts/onboarding.html', context)


def onboarding_confirmation(request):
    """Page de confirmation après demande d'inscription"""
    return render(request, 'accounts/onboarding_confirmation.html')


def login_view(request):
    """Vue de connexion"""
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        # TODO: Implémenter la logique de connexion
        pass

    return render(request, 'accounts/login.html')


def logout_view(request):
    """Vue de déconnexion"""
    logout(request)
    messages.success(request, "Vous avez été déconnecté avec succès.")
    return redirect('accounts:accueil')


@login_required
def dashboard(request):
    """Dashboard principal après connexion"""
    # TODO: Rediriger vers la liste des organisations pour l'instant
    return redirect('organisations:liste')


@login_required
def profil(request):
    """Vue du profil utilisateur"""
    return render(request, 'accounts/profil.html')


@login_required
def modifier_profil(request):
    """Modification du profil utilisateur"""
    # TODO: Implémenter la modification du profil
    return render(request, 'accounts/modifier_profil.html')


@login_required
def liste_utilisateurs(request):
    """Liste des utilisateurs de l'organisation (pour admin)"""
    # TODO: Implémenter la liste des utilisateurs
    return render(request, 'accounts/liste_utilisateurs.html')


@login_required
def inviter_utilisateur(request):
    """Inviter un nouvel utilisateur (pour admin)"""
    # TODO: Implémenter l'invitation d'utilisateur
    return render(request, 'accounts/inviter_utilisateur.html')


@login_required
def modifier_utilisateur(request, pk):
    """Modifier un utilisateur (pour admin)"""
    # TODO: Implémenter la modification d'utilisateur
    return HttpResponse(f"Modifier utilisateur {pk}")


@login_required
def desactiver_utilisateur(request, pk):
    """Désactiver un utilisateur (pour admin)"""
    # TODO: Implémenter la désactivation d'utilisateur
    return HttpResponse(f"Désactiver utilisateur {pk}")
