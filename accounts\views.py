from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse


def accueil(request):
    """Page d'accueil publique"""
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')
    return render(request, 'accounts/accueil.html')


def onboarding(request):
    """Formulaire de demande d'inscription"""
    if request.method == 'POST':
        from .forms import OnboardingForm
        from .services import NotificationService

        form = OnboardingForm(request.POST)
        if form.is_valid():
            demande = form.save()

            # Envoyer une notification aux super admins
            try:
                NotificationService.notifier_nouvelle_demande(demande)
            except Exception as e:
                # Log l'erreur mais ne fait pas échouer la soumission
                print(f"Erreur lors de l'envoi de la notification: {e}")

            messages.success(
                request,
                f"Votre demande d'inscription pour '{demande.nom_organisation}' a été soumise avec succès. "
                "Vous recevrez un email de confirmation une fois votre demande validée par notre équipe."
            )
            return redirect('accounts:onboarding_confirmation')
    else:
        from .forms import OnboardingForm
        form = OnboardingForm()

    context = {
        'form': form
    }
    return render(request, 'accounts/onboarding.html', context)


def onboarding_confirmation(request):
    """Page de confirmation après demande d'inscription"""
    return render(request, 'accounts/onboarding_confirmation.html')


def login_view(request):
    """Vue de connexion"""
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        from .forms import LoginForm
        form = LoginForm(request, data=request.POST)

        if form.is_valid():
            user = form.get_user()

            # Connecter l'utilisateur
            from django.contrib.auth import login
            login(request, user)

            # Gérer "Se souvenir de moi"
            if not form.cleaned_data.get('se_souvenir'):
                request.session.set_expiry(0)  # Session expire à la fermeture du navigateur

            # Message de bienvenue
            from organisations.models import MembreOrganisation
            membre = MembreOrganisation.objects.filter(
                utilisateur=user,
                actif=True
            ).first()

            if membre:
                messages.success(
                    request,
                    f"Bienvenue {user.get_full_name() or user.username} ! "
                    f"Vous êtes connecté à {membre.organisation.nom}."
                )

            # Redirection
            next_url = request.GET.get('next')
            if next_url:
                return redirect(next_url)
            return redirect('accounts:dashboard')
    else:
        from .forms import LoginForm
        form = LoginForm(request)

    context = {
        'form': form
    }
    return render(request, 'accounts/login.html', context)


def logout_view(request):
    """Vue de déconnexion"""
    logout(request)
    messages.success(request, "Vous avez été déconnecté avec succès.")
    return redirect('accounts:accueil')


@login_required
def dashboard(request):
    """Dashboard principal après connexion"""
    from organisations.models import MembreOrganisation

    # Récupérer l'organisation de l'utilisateur
    membre = MembreOrganisation.objects.filter(
        utilisateur=request.user,
        actif=True
    ).select_related('organisation').first()

    if not membre:
        messages.error(
            request,
            "Votre compte n'est associé à aucune organisation active. "
            "Contactez l'administrateur."
        )
        return redirect('accounts:logout')

    # Pour l'instant, rediriger vers la liste des organisations
    # Plus tard, nous créerons un vrai dashboard
    return redirect('organisations:liste')


@login_required
def profil(request):
    """Vue du profil utilisateur"""
    return render(request, 'accounts/profil.html')


@login_required
def modifier_profil(request):
    """Modification du profil utilisateur"""
    # TODO: Implémenter la modification du profil
    return render(request, 'accounts/modifier_profil.html')


@login_required
def liste_utilisateurs(request):
    """Liste des utilisateurs de l'organisation (pour admin)"""
    # TODO: Implémenter la liste des utilisateurs
    return render(request, 'accounts/liste_utilisateurs.html')


@login_required
def inviter_utilisateur(request):
    """Inviter un nouvel utilisateur (pour admin)"""
    # TODO: Implémenter l'invitation d'utilisateur
    return render(request, 'accounts/inviter_utilisateur.html')


@login_required
def modifier_utilisateur(request, pk):
    """Modifier un utilisateur (pour admin)"""
    # TODO: Implémenter la modification d'utilisateur
    return HttpResponse(f"Modifier utilisateur {pk}")


@login_required
def desactiver_utilisateur(request, pk):
    """Désactiver un utilisateur (pour admin)"""
    # TODO: Implémenter la désactivation d'utilisateur
    return HttpResponse(f"Désactiver utilisateur {pk}")
