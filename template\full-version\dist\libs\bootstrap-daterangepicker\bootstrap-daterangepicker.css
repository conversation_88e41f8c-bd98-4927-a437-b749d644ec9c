/* Default */
.daterangepicker {
  position: absolute;
  z-index: 1074;
  display: none;
  background-color: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow);
  margin-block-start: 0.125rem;
  padding-block: 0 0.5rem;
  padding-inline: 0;
  border-radius: 0.375rem;
  /* selected date range styles */
  /* select dropdown styles */
  /* time picker styles */
  /* ranges styles */
}
.daterangepicker .calendar-table {
  /* prev & next arrow wrapper styles */
  /* prev & next arrow default styles with border */
  /* picker table styles */
}
.daterangepicker .calendar-table .next,
.daterangepicker .calendar-table .prev {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  block-size: 2.5rem;
  color: var(--bs-body-color);
  inline-size: 2.5rem;
  inset-block-start: 0.65rem;
  min-inline-size: unset;
  border-radius: 50rem;
}
.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
  display: inline-block;
  border-width: 0 2px 2px 0;
  border-style: solid;
  border-color: var(--bs-body-color);
  block-size: 0.5rem;
  inline-size: 0.5rem;
  border-radius: 0;
}
.daterangepicker .calendar-table .prev span {
  margin-inline-end: -0.25rem;
  transform: rotate(135deg);
}
:dir(rtl) .daterangepicker .calendar-table .prev span {
  transform: rotate(-45deg);
}
.daterangepicker .calendar-table .next span {
  margin-inline-start: -0.25rem;
  transform: rotate(-45deg);
}
:dir(rtl) .daterangepicker .calendar-table .next span {
  margin-inline: 0;
  transform: rotate(135deg);
}
.daterangepicker .calendar-table table {
  border: 0;
  margin: 0;
  border-collapse: collapse;
  border-spacing: 0;
  inline-size: 100%;
}
.daterangepicker .calendar-table table thead tr:first-child {
  position: relative;
  block-size: 3.25rem;
}
.daterangepicker .calendar-table table thead tr:last-child th {
  color: var(--bs-heading-color);
  font-size: 0.8125rem;
  font-weight: 500;
  border-radius: 0;
}
.daterangepicker .calendar-table table thead th select {
  background-color: transparent;
}
.daterangepicker .calendar-table table th.month {
  inline-size: auto;
}
.daterangepicker .calendar-table table td {
  inline-size: 2.25rem;
  border-radius: 50rem;
  /* week header styles */
  /* active date styles */
  /* today date styles */
  /* hover & not in range date styles */
}
.daterangepicker .calendar-table table td.start-date:not(.end-date) {
  border-end-end-radius: 0;
  border-start-end-radius: 0;
}
.daterangepicker .calendar-table table td.end-date:not(.start-date) {
  border-end-start-radius: 0;
  border-start-start-radius: 0;
}
.daterangepicker .calendar-table table td.start-date:not(.end-date, .off), .daterangepicker .calendar-table table td.end-date:not(.start-date, .off) {
  border: 0;
  background-color: var(--bs-primary);
  color: var(--bs-primary-contrast);
}
.daterangepicker .calendar-table table td.start-date:not(.end-date, .off):hover, .daterangepicker .calendar-table table td.end-date:not(.start-date, .off):hover {
  background-color: var(--bs-primary);
}
.daterangepicker .calendar-table table td.off {
  color: var(--bs-secondary-color);
}
.daterangepicker .calendar-table table td.week {
  color: var(--bs-heading-color);
  font-weight: 400;
}
.daterangepicker .calendar-table table td.active.today.start-date:not(.off), .daterangepicker .calendar-table table td.active.today.end-date:not(.off), .daterangepicker .calendar-table table td.active:not(.off) {
  background: var(--bs-primary);
  color: var(--bs-primary-contrast);
}
.daterangepicker .calendar-table table td.today, .daterangepicker .calendar-table table td.today.active, .daterangepicker .calendar-table table td.today:hover {
  background: var(--bs-primary-bg-subtle);
  color: var(--bs-primary);
}
.daterangepicker .calendar-table table td.in-range:not(.start-date, .end-date) {
  border-radius: 0;
  box-shadow: none;
}
.daterangepicker .calendar-table table td.in-range:not(.start-date, .end-date, .off), .daterangepicker .calendar-table table td.in-range:not(.start-date, .end-date, .off):hover {
  background-color: var(--bs-primary-bg-subtle);
  color: var(--bs-primary);
}
.daterangepicker .calendar-table table th {
  block-size: 2.75rem;
}
.daterangepicker .calendar-table table th,
.daterangepicker .calendar-table table td {
  block-size: 2.25rem;
  cursor: pointer;
  line-height: calc(2.25rem - 2px);
  min-inline-size: 2.25rem;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
}
.daterangepicker .calendar-table table th.available:not(.active, .in-range):hover,
.daterangepicker .calendar-table table td.available:not(.active, .in-range):hover {
  background-color: var(--bs-gray-50);
}
.daterangepicker .calendar-table table td.disabled,
.daterangepicker .calendar-table table option.disabled {
  color: var(--bs-secondary-color);
  cursor: not-allowed;
  text-decoration: line-through;
}
.daterangepicker .input-mini.active {
  border-color: var(--bs-primary);
}
.daterangepicker:not(.single) {
  /* responsive above md */
}
@media (min-width: 768px) {
  .daterangepicker:not(.single) .drp-calendar {
    float: inline-start;
  }
}
.daterangepicker:not(.single) .drp-selected {
  display: inline-block;
  padding: 0;
  inline-size: auto;
}
.daterangepicker .drp-selected {
  display: block;
  font-size: 0.8125rem;
  inline-size: 100%;
  padding-block-end: 0.8rem;
}
.daterangepicker.auto-apply .drp-buttons {
  display: none;
}
.daterangepicker.show-calendar .drp-calendar, .daterangepicker.show-calendar .drp-buttons {
  display: block;
}
.daterangepicker .drp-calendar {
  display: none;
  padding: 0.8rem;
}
.daterangepicker .drp-calendar.single .calendar-table {
  border: 0;
}
.daterangepicker.single .drp-selected {
  display: none;
}
.daterangepicker.single .daterangepicker .ranges,
.daterangepicker.single .drp-calendar {
  float: none;
}
.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  padding: 1px;
  border: 0;
  margin: 0;
  block-size: auto;
  cursor: default;
}
.daterangepicker select:focus-visible {
  outline: 0;
}
.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
  padding: 2px;
  border: 1px solid transparent;
  background: var(--bs-gray-100);
  color: var(--bs-body-color);
  font-size: 0.8125rem;
  inline-size: 3.125rem;
  margin-block: 0;
  margin-inline: auto;
  outline: 0;
  border-radius: 0.25rem;
}
.daterangepicker select.hourselect option, .daterangepicker select.minuteselect option, .daterangepicker select.secondselect option, .daterangepicker select.ampmselect option {
  background: var(--bs-paper-bg);
}
.daterangepicker select.monthselect {
  margin-inline-end: 4%;
}
.daterangepicker select.yearselect {
  inline-size: 40%;
}
.daterangepicker .calendar-time {
  position: relative;
  line-height: 30px;
  margin-block: 0;
  margin-inline: auto;
  text-align: center;
}
.daterangepicker .calendar-time select.disabled {
  color: var(--bs-secondary-color);
  cursor: not-allowed;
}
.daterangepicker .drp-buttons {
  display: none;
  padding: 0.8rem;
  clear: both;
  text-align: end;
  vertical-align: middle;
}
.daterangepicker .drp-buttons .btn {
  margin-inline-start: 0.96rem;
}
.daterangepicker .ranges {
  margin: 0;
  float: inline-start;
  text-align: start;
}
.daterangepicker .ranges ul {
  inline-size: 100%;
  list-style: none;
  margin-block: 0;
  margin-inline: auto;
  padding-block: 0.5rem;
  padding-inline: 0;
}
.daterangepicker .ranges ul li {
  cursor: pointer;
  padding-block: 0.543rem;
  padding-inline: 1.25rem;
}
.daterangepicker .ranges ul li:hover {
  background-color: var(--bs-gray-50);
}
.daterangepicker .ranges ul li.active {
  background-color: var(--bs-primary-bg-subtle);
  color: var(--bs-primary);
}
.daterangepicker.show-calendar .ranges:empty {
  display: none;
}
