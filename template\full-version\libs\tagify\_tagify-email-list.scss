.tagify-email-list {
  display: inline-block;
  border: none;
  min-inline-size: 0;
  &.tagify {
    padding: 0;
    margin-inline-start: calc(var(--tagify-item-margin) * -1);
  }
  &.tagify,
  &.tagify--focus {
    border: none;
    box-shadow: none;
    padding-block: 0 var(--tagify-item-margin);
  }
  .tagify__tag {
    line-height: 1.7rem;
    margin-block-end: 0;
    margin-inline-end: .625rem;
    > div {
      padding-inline-end: calc(var(--tagify-item-padding) * 3);
      padding-inline-start: calc(var(--tagify-item-padding) * 2);
    }
    &:only-of-type {
      > div {
        padding-inline: calc(var(--tagify-item-padding) * 2);
      }
    }
    &:hover {
      .tagify__tag__removeBtn {
        &:not(:hover) {
          background-color: var(--tag-remove-btn-color);
          color: var(--tag-text-color);
        }
      }
    }
  }

  /* Do not show the "remove tag" (x) button when only a single tag remains */
  .tagify__tag:only-of-type .tagify__tag__removeBtn {
    display: none;
  }
  .tagify__tag__removeBtn {
    position: absolute;
    z-index: 1;
    font-size: inherit;
    inset-inline-end: 0;
    margin-inline: -20px 6px;
    opacity: 0;
    transform: translateX(-100%) scale(.5);
    transition: .12s;
  }
  .tagify__tag:not(.tagify__tag--editable):hover .tagify__tag__removeBtn {
    opacity: 1;
    transform: none;
  }
  .tagify__input {
    display: none;
  }
}
