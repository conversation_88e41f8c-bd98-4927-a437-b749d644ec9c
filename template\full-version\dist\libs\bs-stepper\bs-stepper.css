/*!
 * bsStepper v1.7.0 (https://github.com/<PERSON>-<PERSON>/bs-stepper)
 * Copyright 2018 - 2019 Johann-<PERSON> <<EMAIL>>
 * Licensed under MIT (https://github.com/<PERSON>-<PERSON>/bs-stepper/blob/master/LICENSE)
 */
.bs-stepper .step-trigger {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 20px;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.5;
  color: #6c757d;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: none;
  border-radius: 0.25rem;
  transition: background-color 0.15s ease-out, color 0.15s ease-out;
}

.bs-stepper .step-trigger:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.bs-stepper .step-trigger:disabled,
.bs-stepper .step-trigger.disabled {
  pointer-events: none;
  opacity: 0.65;
}

.bs-stepper .step-trigger:focus {
  color: #007bff;
  outline: none;
}

.bs-stepper .step-trigger:hover {
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.06);
}

@media (max-width: 520px) {
  .bs-stepper .step-trigger {
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 10px;
  }
}
.bs-stepper-label {
  display: inline-block;
  margin: 0.25rem;
}

.bs-stepper-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}

@media (max-width: 520px) {
  .bs-stepper-header {
    margin: 0 -10px;
    text-align: center;
  }
}
.bs-stepper-line,
.bs-stepper .line {
  -ms-flex: 1 0 32px;
  flex: 1 0 32px;
  min-width: 1px;
  min-height: 1px;
  margin: auto;
  background-color: rgba(0, 0, 0, 0.12);
}

@media (max-width: 400px) {
  .bs-stepper-line,
  .bs-stepper .line {
    -ms-flex-preferred-size: 20px;
    flex-basis: 20px;
  }
}
.bs-stepper-circle {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-line-pack: center;
  align-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 2em;
  height: 2em;
  padding: 0.5em 0;
  margin: 0.25rem;
  line-height: 1em;
  color: #fff;
  background-color: #6c757d;
  border-radius: 1em;
}

.active .bs-stepper-circle {
  background-color: #007bff;
}

.bs-stepper-content {
  padding: 0 20px 20px;
}

@media (max-width: 520px) {
  .bs-stepper-content {
    padding: 0;
  }
}
.bs-stepper.vertical {
  display: -ms-flexbox;
  display: flex;
}

.bs-stepper.vertical .bs-stepper-header {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-align: stretch;
  align-items: stretch;
  margin: 0;
}

.bs-stepper.vertical .bs-stepper-pane,
.bs-stepper.vertical .content {
  display: block;
}

.bs-stepper.vertical .bs-stepper-pane:not(.fade),
.bs-stepper.vertical .content:not(.fade) {
  display: block;
  visibility: hidden;
}

.bs-stepper-pane:not(.fade),
.bs-stepper .content:not(.fade) {
  display: none;
}

.bs-stepper .content.fade,
.bs-stepper-pane.fade {
  visibility: hidden;
  transition-duration: 0.3s;
  transition-property: opacity;
}

.bs-stepper-pane.fade.active,
.bs-stepper .content.fade.active {
  visibility: visible;
  opacity: 1;
}

.bs-stepper-pane.active:not(.fade),
.bs-stepper .content.active:not(.fade) {
  display: block;
  visibility: visible;
}

.bs-stepper-pane.dstepper-block,
.bs-stepper .content.dstepper-block {
  display: block;
}

.bs-stepper:not(.vertical) .bs-stepper-pane.dstepper-none,
.bs-stepper:not(.vertical) .content.dstepper-none {
  display: none;
}

.vertical .bs-stepper-pane.fade.dstepper-none,
.vertical .content.fade.dstepper-none {
  visibility: hidden;
}


/* Default Styles */
.bs-stepper {
  border-radius: 0.5rem;
  background-color: var(--bs-paper-bg);
  /* stepper content padding */
  /* Remove borders from wizard modern */
}
.bs-stepper .line {
  flex: 0;
  margin: 0;
  background-color: transparent;
  min-block-size: auto;
  min-inline-size: auto;
}
.bs-stepper .line .icon-base {
  color: var(--bs-secondary-color);
}
.bs-stepper .bs-stepper-header {
  border-block-end: 1px solid var(--bs-border-color);
  padding-block: 1.25rem;
  padding-inline: 1.25rem;
}
.bs-stepper .bs-stepper-header .line {
  flex: 0;
  margin: 0;
  background-color: transparent;
  min-block-size: auto;
  min-inline-size: auto;
}
.bs-stepper .bs-stepper-header .line .icon-base {
  block-size: 1.25rem;
  font-size: 1.25rem;
  inline-size: 1.25rem;
}
:dir(rtl) .bs-stepper .bs-stepper-header .line .icon-base {
  transform: rotate(180deg);
}
.bs-stepper .bs-stepper-header .step .step-trigger {
  flex-wrap: nowrap;
  font-size: 0.9375rem;
  font-weight: 500;
  line-height: 1.375;
  padding-block: 0;
  padding-inline: 0.5rem;
}
.bs-stepper .bs-stepper-header .step .step-trigger .bs-stepper-label {
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  margin: 0;
  color: var(--bs-body-color);
  font-size: 0.9375rem;
  font-weight: 500;
  line-height: 1.375;
  margin-inline-start: 0.5rem;
  max-inline-size: 224px;
  text-align: start;
  text-overflow: ellipsis;
}
.bs-stepper .bs-stepper-header .step .step-trigger .bs-stepper-label .bs-stepper-number {
  color: var(--bs-secondary-color);
  font-size: 1.5rem;
  font-weight: 500;
}
.bs-stepper .bs-stepper-header .step .step-trigger .bs-stepper-label .bs-stepper-title {
  color: var(--bs-heading-color);
  font-size: 0.9375rem;
  font-weight: 500;
}
.bs-stepper .bs-stepper-header .step .step-trigger .bs-stepper-label .bs-stepper-subtitle {
  color: var(--bs-secondary-color);
  font-size: 0.8125rem;
  font-weight: 400;
}
.bs-stepper .bs-stepper-header .step .step-trigger:hover {
  background-color: transparent;
}
.bs-stepper .bs-stepper-header .step .step-trigger:focus {
  color: inherit;
}
@media (max-width: 991.98px) {
  .bs-stepper .bs-stepper-header .step .step-trigger {
    padding-block: 0.625rem;
    padding-inline: 0;
  }
}
.bs-stepper .bs-stepper-header .step .step-trigger:disabled {
  opacity: 1;
}
.bs-stepper .bs-stepper-header .step .step-trigger:disabled .bs-stepper-label .bs-stepper-number {
  color: var(--bs-secondary-color);
}
.bs-stepper .bs-stepper-header .step.active .bs-stepper-circle {
  border: 5px solid var(--bs-primary);
  background-color: transparent;
  color: var(--bs-primary-contrast);
  border-radius: 50%;
}
.bs-stepper .bs-stepper-header .step.active .bs-stepper-icon svg {
  fill: var(--bs-primary);
}
.bs-stepper .bs-stepper-header .step.active .bs-stepper-icon .icon-base {
  color: var(--bs-primary);
}
.bs-stepper .bs-stepper-header .step.active .bs-stepper-label .bs-stepper-number {
  color: var(--bs-heading-color);
}
.bs-stepper .bs-stepper-header .step.active .bs-stepper-label .bs-stepper-subtitle {
  color: var(--bs-body-color);
}
.bs-stepper .bs-stepper-header .step:not(.active) .bs-stepper-circle {
  border: 3px solid rgba(var(--bs-primary-rgb), 0.16);
  background-color: transparent;
  color: var(--bs-gray-400);
  border-radius: 50%;
}
.bs-stepper .bs-stepper-header .step:not(.crossed) + .line {
  border-color: var(--bs-primary-bg-subtle);
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger {
  /* stepper icons color */
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger .bs-stepper-label .bs-stepper-number {
  color: var(--bs-headings-color);
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger .bs-stepper-label .bs-stepper-subtitle {
  color: var(--bs-body-color);
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger .bs-stepper-circle {
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger .bs-stepper-circle .icon-base {
  color: var(--bs-primary-contrast);
  visibility: visible;
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger .bs-stepper-icon svg {
  fill: var(--bs-primary);
}
.bs-stepper .bs-stepper-header .step.crossed .step-trigger .bs-stepper-icon .icon-base {
  color: var(--bs-primary);
}
.bs-stepper .bs-stepper-header .step.crossed + .line {
  border-color: var(--bs-primary);
}
.bs-stepper .bs-stepper-header .step .bs-stepper-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  padding: unset;
  block-size: 1.25rem;
  font-size: 1.125rem;
  font-weight: 500;
  inline-size: 1.25rem;
}
.bs-stepper .bs-stepper-header .step .bs-stepper-circle .icon-base {
  block-size: 0.875rem;
  font-size: 0.875rem;
  inline-size: 0.875rem;
  visibility: hidden;
}
.bs-stepper .bs-stepper-content {
  padding-block: 1.25rem;
  padding-inline: 1.25rem;
  border-radius: 0.5rem;
}
:dir(rtl) .bs-stepper .bs-stepper-content .btn-next .icon-base,
:dir(rtl) .bs-stepper .bs-stepper-content .btn-prev .icon-base {
  transform: rotate(180deg);
}
.bs-stepper.vertical .bs-stepper-header {
  border-block-end: none;
  min-inline-size: 18rem;
}
@media (max-width: 991.98px) {
  .bs-stepper.vertical .bs-stepper-header {
    border-block-end: 1px solid var(--bs-border-color);
    border-inline-end: none;
    border-inline-start: none;
  }
}
.bs-stepper.vertical .bs-stepper-header .step .step-trigger {
  padding-block: 0.5rem;
  padding-inline: 0;
}
.bs-stepper.vertical .bs-stepper-header .step:first-child .step-trigger {
  padding-block-start: 0;
}
.bs-stepper.vertical .bs-stepper-header .step:last-child .step-trigger {
  padding-block-end: 0;
}
.bs-stepper.vertical .bs-stepper-header .step.crossed + .line::before {
  background-color: var(--bs-primary);
}
.bs-stepper.vertical .bs-stepper-header .line {
  position: relative;
  border: none;
  min-block-size: 1px;
}
.bs-stepper.vertical .bs-stepper-header .line::before {
  position: absolute;
  display: block;
  border-radius: 3px;
  block-size: 2.5rem;
  content: "";
  inline-size: 3px;
  inset-block-start: -0.75rem;
  inset-inline-start: 0.8rem;
}
.bs-stepper.vertical .bs-stepper-content {
  inline-size: 100%;
}
.bs-stepper.vertical .bs-stepper-content .content:not(.active) {
  display: none;
}
.bs-stepper.vertical.wizard-icons .step {
  padding-block: 0.75rem;
  padding-inline: 0;
  text-align: center;
}
.bs-stepper.wizard-icons .bs-stepper-header {
  justify-content: center;
}
.bs-stepper.wizard-icons .bs-stepper-header .step .step-trigger {
  flex-direction: column;
  padding: 1.25rem;
  gap: 0.5rem;
}
.bs-stepper.wizard-icons .bs-stepper-header .step .step-trigger .bs-stepper-icon svg {
  block-size: 3.75rem;
  fill: var(--bs-heading-color);
  inline-size: 3.75rem;
}
.bs-stepper.wizard-icons .bs-stepper-header .step .step-trigger .bs-stepper-icon .icon-base {
  fill: var(--bs-heading-color);
  font-size: 1.6rem;
}
.bs-stepper.wizard-icons .bs-stepper-header .step.active .step-trigger .bs-stepper-icon svg {
  fill: var(--bs-primary);
}
.bs-stepper.wizard-icons .bs-stepper-header .step.active .step-trigger .bs-stepper-label,
.bs-stepper.wizard-icons .bs-stepper-header .step.active .step-trigger .bs-stepper-icon .icon-base {
  color: var(--bs-primary);
}
.bs-stepper.wizard-icons .bs-stepper-header .step:first-child .step-trigger {
  padding-inline-start: 1.25rem;
}
.bs-stepper.wizard-icons .bs-stepper-header .step:last-child .step-trigger {
  padding-inline-end: 1.25rem;
}
@media (min-width: 992px) {
  .bs-stepper.wizard-icons .bs-stepper-header {
    justify-content: space-around;
  }
}
.bs-stepper.wizard-icons .step.crossed .step-trigger .bs-stepper-label {
  color: var(--bs-primary);
}
.bs-stepper.wizard-icons .step.crossed .step-trigger .bs-stepper-icon svg {
  fill: var(--bs-primary);
}
.bs-stepper.wizard-icons .step.crossed .step-trigger .bs-stepper-icon .icon-base {
  color: var(--bs-primary);
}
.bs-stepper.wizard-icons .step.crossed + .line .icon-base {
  color: var(--bs-primary);
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header {
  min-inline-size: 15rem;
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header .step .avatar-initial {
  background-color: var(--bs-gray-50);
  color: var(--bs-heading-color);
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header .step.crossed .avatar-initial {
  background-color: rgba(var(--bs-primary-rgb), 0.16);
  color: var(--bs-primary);
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header .step.active .avatar-initial {
  background-color: var(--bs-primary);
  box-shadow: var(--bs-box-shadow-xs);
  color: var(--bs-primary-contrast);
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header .step .step-trigger {
  padding-block: 0.875rem;
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header .step:first-child .step-trigger {
  padding-block-start: 0;
}
.bs-stepper.wizard-vertical-icons.vertical .bs-stepper-header .step:last-child .step-trigger {
  padding-block-end: 0;
}
.bs-stepper:not(.wizard-icons):not(.vertical) .bs-stepper-header .line {
  flex-basis: auto;
  border-width: 0;
  border-style: solid;
  border-radius: 3px;
  border-block-start-width: 3.9px;
  inline-size: 100%;
}
.bs-stepper:not(.wizard-icons) .bs-stepper-header .line::before {
  background-color: var(--bs-primary-bg-subtle);
}
.bs-stepper:not(.wizard-icons):not(.wizard-vertical-icons).vertical .bs-stepper-header .step:not(:last-child) {
  margin-block-end: 0.5rem;
}
.bs-stepper:not(.wizard-icons):not(.wizard-vertical-icons).vertical .bs-stepper-header .step:not(:first-child) {
  margin-block-start: 0.5rem;
}
@media (min-width: 992px) {
  .bs-stepper:not(.wizard-icons):not(.wizard-vertical-icons).vertical .bs-stepper-header .step .step-trigger {
    padding: 0;
  }
}
.bs-stepper:not(.wizard-icons):not(.wizard-vertical-icons).vertical .bs-stepper-header .line::before {
  inset-block-start: -1.2rem;
}
.bs-stepper.wizard-modern {
  background-color: transparent;
}
.bs-stepper.wizard-modern .bs-stepper-header {
  border-block-end: none;
}
.bs-stepper.wizard-modern .bs-stepper-content {
  border-radius: 0.375rem;
  background-color: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow);
}
.bs-stepper:not(.wizard-modern) {
  box-shadow: var(--bs-box-shadow);
}

/* Media Queries */
@media (max-width: 991.98px) {
  .bs-stepper .bs-stepper-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .bs-stepper .bs-stepper-header .step .step-trigger {
    flex-direction: row;
  }
  .bs-stepper .bs-stepper-header .step .step-trigger .bs-stepper-label {
    margin-inline-start: 0.35rem;
  }
  .bs-stepper .bs-stepper-header .step:first-child .step-trigger {
    padding-block-start: 0;
  }
  .bs-stepper .bs-stepper-header .step:last-child .step-trigger {
    padding-block-end: 0;
  }
  .bs-stepper.vertical {
    flex-direction: column;
  }
  .bs-stepper.vertical .bs-stepper-header {
    align-items: flex-start;
  }
  .bs-stepper.vertical.wizard-icons .bs-stepper-header .line::before {
    inset-inline-start: 0.75rem;
    margin-inline-start: 0;
  }
  .bs-stepper.wizard-icons .bs-stepper-header .step:first-child .step-trigger {
    padding-inline-start: 1.25rem;
  }
  .bs-stepper.wizard-icons .bs-stepper-header .step .step-trigger {
    flex-direction: row;
  }
  .bs-stepper:not(.wizard-icons):not(.vertical) .bs-stepper-header .line {
    position: relative;
    border: none;
    min-block-size: 1px;
  }
  .bs-stepper:not(.wizard-icons):not(.vertical) .bs-stepper-header .line::before {
    position: absolute;
    display: block;
    border-radius: 3px;
    block-size: 2.5rem;
    content: "";
    inline-size: 3px;
    inset-block-start: -0.75rem;
    inset-inline-start: 0.8rem;
  }
  .bs-stepper:not(.vertical) .bs-stepper-header .line .icon-base {
    display: none;
  }
  .bs-stepper:not(.vertical) .bs-stepper-header .step:not(:first-child) .step-trigger {
    margin-block-start: 1rem;
  }
}
/* Styles for Modal example Create App wizard */
#wizard-create-app.vertical .bs-stepper-header {
  min-inline-size: 15rem;
}

@media (max-width: 520px) {
  /* To set minus margin in mobile screen
  as that affects border */
  .bs-stepper-header {
    margin: 0;
  }
}
