/* Editor */
/* ******************************************************************************* */
/* common styles */
.ql-container {
  position: relative;
  display: block;
  margin: 0;
}
.ql-container .ql-editor {
  display: block;
  outline: none;
  overflow-y: auto;
  tab-size: 4;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-container .ql-editor.ql-blank::before {
  position: absolute;
  color: var(--bs-secondary-color);
  content: attr(data-placeholder);
  font-size: 16px;
  font-style: italic;
  pointer-events: none;
}

/* Themes */
.ql-snow,
.ql-bubble {
  box-sizing: border-box;
}
.ql-snow .ql-hidden,
.ql-bubble .ql-hidden {
  display: none !important;
}
.ql-snow .ql-transparent,
.ql-bubble .ql-transparent {
  opacity: 0.4;
}
.ql-snow .ql-thin,
.ql-snow .ql-stroke.ql-thin,
.ql-bubble .ql-thin,
.ql-bubble .ql-stroke.ql-thin {
  stroke-width: 1;
}
.ql-snow .ql-direction.ql-active svg:last-child,
.ql-bubble .ql-direction.ql-active svg:last-child {
  display: inline;
}
.ql-snow .ql-direction.ql-active svg:first-child,
.ql-bubble .ql-direction.ql-active svg:first-child {
  display: none;
}
.ql-snow .ql-direction svg:last-child,
.ql-bubble .ql-direction svg:last-child {
  display: none;
}
.ql-snow.ql-toolbar, .ql-snow .ql-toolbar,
.ql-bubble.ql-toolbar,
.ql-bubble .ql-toolbar {
  padding: 0.5rem;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.ql-snow.ql-toolbar::after, .ql-snow .ql-toolbar::after,
.ql-bubble.ql-toolbar::after,
.ql-bubble .ql-toolbar::after {
  display: table;
  clear: both;
  content: "";
}
.ql-snow.ql-toolbar button, .ql-snow .ql-toolbar button,
.ql-bubble.ql-toolbar button,
.ql-bubble .ql-toolbar button {
  display: inline-block;
  border: none;
  background: none;
  block-size: 1.5rem;
  cursor: pointer;
  float: inline-start;
  inline-size: 1.75rem;
  padding-block: 0.1875rem;
  padding-inline: 0.3125rem;
}
.ql-snow.ql-toolbar button svg, .ql-snow .ql-toolbar button svg,
.ql-bubble.ql-toolbar button svg,
.ql-bubble .ql-toolbar button svg {
  block-size: 100%;
  float: inline-start;
}
.ql-snow.ql-toolbar .ql-picker-options, .ql-snow .ql-toolbar .ql-picker-options,
.ql-bubble.ql-toolbar .ql-picker-options,
.ql-bubble .ql-toolbar .ql-picker-options {
  box-shadow: var(--bs-box-shadow-lg);
}
.ql-snow.ql-toolbar input.ql-image[type=file], .ql-snow .ql-toolbar input.ql-image[type=file],
.ql-bubble.ql-toolbar input.ql-image[type=file],
.ql-bubble .ql-toolbar input.ql-image[type=file] {
  display: none;
}
.ql-snow .ql-tooltip,
.ql-bubble .ql-tooltip {
  position: absolute;
}
.ql-snow .ql-tooltip:not(.ql-flip),
.ql-bubble .ql-tooltip:not(.ql-flip) {
  transform: translateY(0.625rem);
}
.ql-snow .ql-tooltip a,
.ql-bubble .ql-tooltip a {
  cursor: pointer;
}
.ql-snow .ql-formats,
.ql-bubble .ql-formats {
  display: inline-block;
  margin-inline-end: 0.9375rem;
  vertical-align: middle;
}
.ql-snow .ql-formats::after,
.ql-bubble .ql-formats::after {
  display: table;
  clear: both;
  content: "";
}
.ql-snow .ql-picker,
.ql-bubble .ql-picker {
  position: relative;
  display: inline-block;
  block-size: 1.5rem;
  float: inline-start;
  vertical-align: middle;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-options,
.ql-bubble .ql-picker.ql-expanded .ql-picker-options {
  z-index: 1;
  display: block;
  inset-block-start: 100%;
  margin-block-start: -0.0625rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""])::before, .ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""])::before, .ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-bubble .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-bubble .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-bubble .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-bubble .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""])::before {
  content: attr(data-label);
}
.ql-snow .ql-picker.ql-header,
.ql-bubble .ql-picker.ql-header {
  inline-size: 6.125rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item::before {
  content: "Normal";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "Heading 1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "Heading 2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "Heading 3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "Heading 4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "Heading 5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "Heading 6";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  font-size: 2.875rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  font-size: 2.375rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  font-size: 1.75rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  font-size: 1.5rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  font-size: 1.125rem;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before,
.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  font-size: 0.9375rem;
}
.ql-snow .ql-picker.ql-font,
.ql-bubble .ql-picker.ql-font {
  inline-size: 6.75rem;
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before,
.ql-bubble .ql-picker.ql-font .ql-picker-label::before,
.ql-bubble .ql-picker.ql-font .ql-picker-item::before {
  content: "Sans Serif";
  font-family: "Inter", -apple-system, blinkmacsystemfont, "Segoe UI", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before,
.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: "Serif";
  font-family: georgia, "Times New Roman", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before,
.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: "Monospace";
  font-family: "SFMono-Regular", menlo, monaco, consolas, "Liberation Mono", "Courier New", monospace;
}
.ql-snow .ql-picker.ql-size,
.ql-bubble .ql-picker.ql-size {
  inline-size: 6.125rem;
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before,
.ql-bubble .ql-picker.ql-size .ql-picker-label::before,
.ql-bubble .ql-picker.ql-size .ql-picker-item::before {
  content: "Normal";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before,
.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: "Small";
  font-size: 0.8125rem;
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before,
.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: "Large";
  font-size: 1.0625rem;
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before,
.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: "Huge";
  font-size: 1.25rem;
}
.ql-snow .ql-picker:not(.ql-color-picker, .ql-icon-picker) svg,
.ql-bubble .ql-picker:not(.ql-color-picker, .ql-icon-picker) svg {
  position: absolute;
  inline-size: 1.125rem;
  inset-block-start: 50%;
  inset-inline-end: 0;
  margin-block-start: -0.5625rem;
}
.ql-snow .ql-picker-label,
.ql-bubble .ql-picker-label {
  position: relative;
  display: inline-block;
  border: 0.0625rem solid transparent;
  block-size: 100%;
  cursor: pointer;
  inline-size: 100%;
  padding-inline: 0.5rem 0.125rem;
}
.ql-snow .ql-picker-label::before,
.ql-bubble .ql-picker-label::before {
  display: inline-block;
  line-height: 1.375rem;
}
.ql-snow .ql-picker-options,
.ql-bubble .ql-picker-options {
  position: absolute;
  display: none;
  min-inline-size: 100%;
  padding-block: 0.25rem;
  padding-inline: 0.5rem;
  white-space: nowrap;
}
.ql-snow .ql-picker-options .ql-picker-item,
.ql-bubble .ql-picker-options .ql-picker-item {
  display: block;
  cursor: pointer;
  padding-block: 0.3125rem;
}
.ql-snow .ql-color-picker,
.ql-snow .ql-icon-picker,
.ql-bubble .ql-color-picker,
.ql-bubble .ql-icon-picker {
  inline-size: 1.75rem;
}
.ql-snow .ql-color-picker .ql-picker-label,
.ql-snow .ql-icon-picker .ql-picker-label,
.ql-bubble .ql-color-picker .ql-picker-label,
.ql-bubble .ql-icon-picker .ql-picker-label {
  padding-block: 0.125rem;
  padding-inline: 0.25rem;
}
.ql-snow .ql-icon-picker .ql-picker-options,
.ql-bubble .ql-icon-picker .ql-picker-options {
  padding-block: 0.25rem;
  padding-inline: 0;
}
.ql-snow .ql-icon-picker .ql-picker-item,
.ql-bubble .ql-icon-picker .ql-picker-item {
  block-size: 1.5rem;
  inline-size: 1.5rem;
  padding-block: 0.125rem;
  padding-inline: 0.25rem;
}
.ql-snow .ql-color-picker .ql-picker-options,
.ql-bubble .ql-color-picker .ql-picker-options {
  inline-size: 9.5rem;
  padding-block: 0.1875rem;
  padding-inline: 0.3125rem;
}
.ql-snow .ql-color-picker .ql-picker-item,
.ql-bubble .ql-color-picker .ql-picker-item {
  padding: 0;
  border: 0.0625rem solid transparent;
  margin: 0.125rem;
  block-size: 1rem;
  float: inline-start;
  inline-size: 1rem;
}
.ql-snow .ql-color-picker.ql-background .ql-picker-item,
.ql-bubble .ql-color-picker.ql-background .ql-picker-item {
  background-color: var(--bs-paper-bg);
}
.ql-snow .ql-color-picker.ql-color .ql-picker-item,
.ql-bubble .ql-color-picker.ql-color .ql-picker-item {
  background-color: var(--bs-pure-black);
}
.ql-snow .ql-fill,
.ql-snow .ql-stroke.ql-fill,
.ql-bubble .ql-fill,
.ql-bubble .ql-stroke.ql-fill {
  fill: var(--bs-body-color);
}
:dir(rtl) .ql-snow .ql-italic svg,
:dir(rtl) .ql-snow .ql-list svg,
:dir(rtl) .ql-snow .ql-indent svg,
:dir(rtl) .ql-snow .ql-direction svg,
:dir(rtl) .ql-snow .ql-align svg,
:dir(rtl) .ql-snow .ql-link svg,
:dir(rtl) .ql-snow .ql-image svg,
:dir(rtl) .ql-snow .ql-clean svg,
:dir(rtl) .ql-bubble .ql-italic svg,
:dir(rtl) .ql-bubble .ql-list svg,
:dir(rtl) .ql-bubble .ql-indent svg,
:dir(rtl) .ql-bubble .ql-direction svg,
:dir(rtl) .ql-bubble .ql-align svg,
:dir(rtl) .ql-bubble .ql-link svg,
:dir(rtl) .ql-bubble .ql-image svg,
:dir(rtl) .ql-bubble .ql-clean svg {
  transform: scaleX(-1);
}

.ql-snow + .ql-container, .ql-snow + .ql-container .ql-editor {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.ql-snow.ql-toolbar,
.ql-snow .ql-toolbar {
  border: 0.0625rem solid color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg));
  background: var(--bs-paper-bg);
  background-clip: padding-box;
}
.ql-snow.ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar button:hover,
.ql-snow .ql-toolbar button:focus,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--bs-primary);
}
.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--bs-primary);
}
.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--bs-primary);
}
.ql-snow.ql-toolbar + .ql-container.ql-snow {
  border-block-start: 0;
}
.ql-snow.ql-toolbar + .ql-container.ql-snow .ql-editor, .ql-snow.ql-toolbar + .ql-container.ql-snow {
  border-bottom-right-radius: var(--bs-border-radius-lg);
  border-bottom-left-radius: var(--bs-border-radius-lg);
}
.ql-snow .ql-editor {
  background: var(--bs-paper-bg);
  min-block-size: 15rem;
  padding-block: calc(1rem * 0.5);
  padding-inline: 1rem;
}
.ql-snow .ql-editor.ql-blank::before {
  padding-block: 0;
  padding-inline: 0 1rem;
}
.ql-snow .ql-editor .ql-code-block-container {
  position: relative;
  overflow: visible;
  border-radius: 0.188rem;
  background-color: var(--bs-dark);
  color: var(--bs-white);
  margin-block: 0.3125rem;
  padding-block: 0.4375rem;
  padding-inline: 0.625rem;
}
.ql-snow .ql-editor .ql-code-block-container .ql-ui {
  position: absolute;
  inset-block-start: 0.3125rem;
  inset-inline-end: 0.3125rem;
}
.ql-snow .ql-picker {
  color: var(--bs-body-color);
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  z-index: 2;
  border-color: color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg));
  color: var(--bs-secondary-color);
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: var(--bs-secondary-color);
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: var(--bs-secondary-color);
}
.ql-snow .ql-stroke {
  fill: none;
  stroke: var(--bs-body-color);
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}
.ql-snow .ql-stroke-miter {
  fill: none;
  stroke: var(--bs-body-color);
  stroke-miterlimit: 10;
  stroke-width: 2;
}
.ql-snow .ql-picker-label {
  border: 0.0625rem solid transparent;
}
.ql-snow .ql-picker-options {
  border: 0.0625rem solid rgba(var(--bs-white-rgb), 0.05);
  background-clip: padding-box;
  background-color: var(--bs-paper-bg);
}
.ql-snow .ql-color-picker .ql-picker-item.ql-selected,
.ql-snow .ql-color-picker .ql-picker-item:hover {
  border-color: var(--bs-pure-black);
}
.ql-snow .ql-tooltip {
  display: flex;
  border: 0 solid var(--bs-border-color);
  border-radius: 0.375rem;
  background-clip: padding-box;
  background-color: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow-lg);
  color: var(--bs-body-color);
  padding-block: 0.3125rem;
  padding-inline: 0.75rem;
  white-space: nowrap;
}
.ql-snow .ql-tooltip::before {
  content: "Visit URL:";
  line-height: 1.625rem;
  margin-inline-end: 0.5rem;
}
.ql-snow .ql-tooltip input[type=text] {
  display: none;
  border: 0.0625rem solid color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg));
  margin: 0;
  background-color: var(--bs-paper-bg);
  font-size: 0.8125rem;
  inline-size: 10.625rem;
  padding-block: 0.1875rem;
  padding-inline: 0.3125rem;
}
.ql-snow .ql-tooltip a.ql-action::after {
  border-inline-end: 0.0625rem solid var(--bs-secondary-color);
  content: "Edit";
  margin-inline-start: 1rem;
  padding-inline-end: 0.5rem;
}
.ql-snow .ql-tooltip a.ql-remove::before {
  content: "Remove";
  margin-inline-start: 0.5rem;
}
.ql-snow .ql-tooltip a {
  line-height: 1.625rem;
}
.ql-snow .ql-tooltip.ql-editing a.ql-preview, .ql-snow .ql-tooltip.ql-editing a.ql-remove {
  display: none;
}
.ql-snow .ql-tooltip.ql-editing input[type=text] {
  display: inline-block;
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-inline-end: 0;
  content: "Save";
  padding-inline-end: 0;
}
.ql-snow .ql-tooltip[data-mode=link]::before {
  content: "Enter link:";
}
.ql-snow .ql-tooltip[data-mode=formula]::before {
  content: "Enter formula:";
}
.ql-snow .ql-tooltip[data-mode=video]::before {
  content: "Enter video:";
}
.ql-snow.ql-container {
  border: 0.0625rem solid color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg));
}

.ql-bubble {
  --bs-bubble-tooltip-bg: #444;
  --bs-bubble-tooltip-color: #ccc;
}
.ql-bubble.ql-toolbar button:hover,
.ql-bubble.ql-toolbar button:focus,
.ql-bubble.ql-toolbar button.ql-active,
.ql-bubble.ql-toolbar .ql-picker-label:hover,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active,
.ql-bubble.ql-toolbar .ql-picker-item:hover,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected,
.ql-bubble .ql-toolbar button:hover,
.ql-bubble .ql-toolbar button:focus,
.ql-bubble .ql-toolbar button.ql-active,
.ql-bubble .ql-toolbar .ql-picker-label:hover,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active,
.ql-bubble .ql-toolbar .ql-picker-item:hover,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--bs-white);
}
.ql-bubble.ql-toolbar button:hover .ql-stroke,
.ql-bubble.ql-toolbar button:hover .ql-stroke-miter,
.ql-bubble.ql-toolbar button:focus .ql-stroke,
.ql-bubble.ql-toolbar button:focus .ql-stroke-miter,
.ql-bubble.ql-toolbar button.ql-active .ql-stroke,
.ql-bubble.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-bubble .ql-toolbar button:hover .ql-stroke,
.ql-bubble .ql-toolbar button:hover .ql-stroke-miter,
.ql-bubble .ql-toolbar button:focus .ql-stroke,
.ql-bubble .ql-toolbar button:focus .ql-stroke-miter,
.ql-bubble .ql-toolbar button.ql-active .ql-stroke,
.ql-bubble .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--bs-white);
}
.ql-bubble.ql-toolbar button:hover .ql-fill,
.ql-bubble.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar button:focus .ql-fill,
.ql-bubble.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar button.ql-active .ql-fill,
.ql-bubble.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar button:hover .ql-fill,
.ql-bubble .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar button:focus .ql-fill,
.ql-bubble .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar button.ql-active .ql-fill,
.ql-bubble .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--bs-white);
}
@media (pointer: coarse) {
  .ql-bubble.ql-toolbar button:hover:not(.ql-active),
  .ql-bubble .ql-toolbar button:hover:not(.ql-active) {
    color: var(--bs-bubble-tooltip-color);
  }
  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-fill,
  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,
  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-fill,
  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {
    fill: var(--bs-bubble-tooltip-color);
  }
  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke,
  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,
  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke,
  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {
    stroke: var(--bs-bubble-tooltip-color);
  }
}
.ql-bubble .ql-stroke {
  fill: none;
  stroke: var(--bs-bubble-tooltip-color);
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}
.ql-bubble .ql-stroke-miter {
  fill: none;
  stroke: var(--bs-bubble-tooltip-color);
  stroke-miterlimit: 10;
  stroke-width: 2;
}
.ql-bubble .ql-fill,
.ql-bubble .ql-stroke.ql-fill {
  fill: var(--bs-bubble-tooltip-color);
}
.ql-bubble .ql-picker {
  color: var(--bs-bubble-tooltip-color);
}
.ql-bubble .ql-picker-options {
  background-color: var(--bs-bubble-tooltip-bg);
}
.ql-bubble .ql-toolbar .ql-formats {
  margin-block: 0.5rem;
  margin-inline: 0 0.75rem;
}
.ql-bubble .ql-toolbar .ql-formats:first-child {
  margin-inline-start: 0.75rem;
}
.ql-bubble .ql-tooltip-arrow {
  position: absolute;
  display: block;
  border-inline-end: 0.375rem solid transparent;
  border-inline-start: 0.375rem solid transparent;
  content: " ";
  inset-inline-start: 50%;
  margin-inline-start: -0.375rem;
}
.ql-bubble .ql-tooltip {
  z-index: 1090;
  border-radius: 0.375rem;
  background-color: var(--bs-bubble-tooltip-bg);
  color: var(--bs-white);
}
.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow {
  border-block-end: 0.375rem solid var(--bs-bubble-tooltip-bg);
  inset-block-start: -0.375rem;
}
.ql-bubble .ql-tooltip.ql-flip .ql-tooltip-arrow {
  border-block-start: 0.375rem solid var(--bs-bubble-tooltip-bg);
  inset-block-end: -0.375rem;
}
.ql-bubble .ql-tooltip-editor {
  display: none;
}
.ql-bubble .ql-tooltip-editor input[type=text] {
  border: none;
  background: transparent;
  color: var(--bs-white);
  font-size: 0.8125rem;
  inline-size: 100%;
  padding-block: 0.625rem;
  padding-inline: 1.25rem;
}
.ql-bubble .ql-tooltip-editor a {
  position: absolute;
  inset-block-start: 0.625rem;
  inset-inline-end: 1.25rem;
}
.ql-bubble .ql-editing .ql-tooltip-editor {
  display: block;
}

/* dark styles */
[data-bs-theme=dark] .ql-bubble {
  --bs-bubble-tooltip-bg: var(--bs-body-bg);
}
