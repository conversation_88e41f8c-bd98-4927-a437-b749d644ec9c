/* notiflix
 ******************************************************************************* */
/*!
* Notiflix (https://notiflix.github.io)
* Version: 3.2.8
* Author: <PERSON><PERSON><PERSON> (https://github.com/furcan)
* Copyright 2019 - 2025 Notiflix, MIT License (https://opensource.org/licenses/MIT)
*/
/* NOTIFLIX.NOTIFY: begin */
/* Notiflix: Notify wrap: begin */
[id^=NotiflixNotifyWrap] {
  pointer-events: none;
  position: fixed;
  z-index: 4001;
  opacity: 1;
  right: 10px;
  top: 10px;
  width: 280px;
  max-width: 96%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: transparent;
}

[id^=NotiflixNotifyWrap].nx-flex-center-center {
  max-height: calc(100vh - 20px);
  overflow-x: hidden;
  overflow-y: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}

[id^=NotiflixNotifyWrap]::-webkit-scrollbar {
  width: 0;
  height: 0;
}

[id^=NotiflixNotifyWrap]::-webkit-scrollbar-thumb {
  background: transparent;
}

[id^=NotiflixNotifyWrap]::-webkit-scrollbar-track {
  background: transparent;
}

[id^=NotiflixNotifyWrap] * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* Notiflix: Notify wrap: end */
/* Notiflix: Notify overlay: begin */
[id^=NotiflixNotifyOverlay] {
  -webkit-transition: background 0.3s ease-in-out;
  -o-transition: background 0.3s ease-in-out;
  transition: background 0.3s ease-in-out;
}

/* Notiflix: Notify overlay: end */
/* Notiflix: Notify content: begin */
[id^=NotiflixNotifyWrap] > div {
  pointer-events: all;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-family: "Quicksand", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  width: 100%;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  margin: 0 0 10px;
  border-radius: 5px;
  background: #1e1e1e;
  color: #fff;
  padding: 10px 12px;
  font-size: 14px;
  line-height: 1.4;
}

[id^=NotiflixNotifyWrap] > div:last-child {
  margin: 0;
}

[id^=NotiflixNotifyWrap] > div.nx-with-callback {
  cursor: pointer;
}

[id^=NotiflixNotifyWrap] > div.nx-with-icon {
  padding: 8px;
  min-height: 56px;
}

[id^=NotiflixNotifyWrap] > div.nx-paused {
  cursor: auto;
}

[id^=NotiflixNotifyWrap] > div.nx-notify-click-to-close {
  cursor: pointer;
}

[id^=NotiflixNotifyWrap] > div.nx-with-close-button {
  padding: 10px 36px 10px 12px;
}

[id^=NotiflixNotifyWrap] > div.nx-with-icon.nx-with-close-button {
  padding: 6px 36px 6px 6px;
}

[id^=NotiflixNotifyWrap] > div > span.nx-message {
  cursor: inherit;
  font-weight: normal;
  font-family: inherit !important;
  word-break: break-all;
  word-break: break-word;
}

[id^=NotiflixNotifyWrap] > div > span.nx-close-button {
  cursor: pointer;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  position: absolute;
  right: 8px;
  top: 0;
  bottom: 0;
  margin: auto;
  color: inherit;
  width: 20px;
  height: 20px;
}

[id^=NotiflixNotifyWrap] > div > span.nx-close-button:hover {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

[id^=NotiflixNotifyWrap] > div > span.nx-close-button > svg {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 2px;
  top: 2px;
}

/* Notiflix: Notify content: end */
/* Notiflix: Notify icon: begin */
[id^=NotiflixNotifyWrap] > div > .nx-message-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  font-size: 30px;
  line-height: 40px;
  text-align: center;
  left: 8px;
  top: 0;
  bottom: 0;
  margin: auto;
  border-radius: inherit;
}

[id^=NotiflixNotifyWrap] > div > .nx-message-icon-fa.nx-message-icon-fa-shadow {
  color: inherit;
  background: rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: inset 0 0 34px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 34px rgba(0, 0, 0, 0.2);
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

[id^=NotiflixNotifyWrap] > div > span.nx-with-icon {
  position: relative;
  float: left;
  width: calc(100% - 40px);
  margin: 0 0 0 40px;
  padding: 0 0 0 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* Notiflix: Notify icon: end */
/* Notiflix: Notify rtl: begin */
[id^=NotiflixNotifyWrap] > div.nx-rtl-on > .nx-message-icon {
  left: auto;
  right: 8px;
}

[id^=NotiflixNotifyWrap] > div.nx-rtl-on > span.nx-with-icon {
  padding: 0 10px 0 0;
  margin: 0 40px 0 0;
}

[id^=NotiflixNotifyWrap] > div.nx-rtl-on > span.nx-close-button {
  right: auto;
  left: 8px;
}

[id^=NotiflixNotifyWrap] > div.nx-with-icon.nx-with-close-button.nx-rtl-on {
  padding: 6px 6px 6px 36px;
}

[id^=NotiflixNotifyWrap] > div.nx-with-close-button.nx-rtl-on {
  padding: 10px 12px 10px 36px;
}

/* Notiflix: Notify rtl: end */
/* Notiflix: Notify animation => fade: begin */
[id^=NotiflixNotifyOverlay].nx-with-animation,
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-fade {
  -webkit-animation: notify-animation-fade 0.3s ease-in-out 0s normal;
  animation: notify-animation-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes notify-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Notify animation => fade: end */
/* Notiflix: Notify animation => zoom: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-zoom {
  -webkit-animation: notify-animation-zoom 0.3s ease-in-out 0s normal;
  animation: notify-animation-zoom 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-animation-zoom {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes notify-animation-zoom {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
/* Notiflix: Notify animation => zoom: end */
/* Notiflix: Notify animation => from right: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-right {
  -webkit-animation: notify-animation-from-right 0.3s ease-in-out 0s normal;
  animation: notify-animation-from-right 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-animation-from-right {
  0% {
    right: -300px;
    opacity: 0;
  }
  50% {
    right: 8px;
    opacity: 1;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
@keyframes notify-animation-from-right {
  0% {
    right: -300px;
    opacity: 0;
  }
  50% {
    right: 8px;
    opacity: 1;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
/* Notiflix: Notify animation => from right: end */
/* Notiflix: Notify animation => from left: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-left {
  -webkit-animation: notify-animation-from-left 0.3s ease-in-out 0s normal;
  animation: notify-animation-from-left 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-animation-from-left {
  0% {
    left: -300px;
    opacity: 0;
  }
  50% {
    left: 8px;
    opacity: 1;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}
@keyframes notify-animation-from-left {
  0% {
    left: -300px;
    opacity: 0;
  }
  50% {
    left: 8px;
    opacity: 1;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}
/* Notiflix: Notify animation => from left: end */
/* Notiflix: Notify animation => from top: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-top {
  -webkit-animation: notify-animation-from-top 0.3s ease-in-out 0s normal;
  animation: notify-animation-from-top 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-animation-from-top {
  0% {
    top: -50px;
    opacity: 0;
  }
  50% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: 0;
    opacity: 1;
  }
}
@keyframes notify-animation-from-top {
  0% {
    top: -50px;
    opacity: 0;
  }
  50% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: 0;
    opacity: 1;
  }
}
/* Notiflix: Notify animation => from top: end */
/* Notiflix: Notify animation => from bottom: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-bottom {
  -webkit-animation: notify-animation-from-bottom 0.3s ease-in-out 0s normal;
  animation: notify-animation-from-bottom 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-animation-from-bottom {
  0% {
    bottom: -50px;
    opacity: 0;
  }
  50% {
    bottom: 8px;
    opacity: 1;
  }
  100% {
    bottom: 0;
    opacity: 1;
  }
}
@keyframes notify-animation-from-bottom {
  0% {
    bottom: -50px;
    opacity: 0;
  }
  50% {
    bottom: 8px;
    opacity: 1;
  }
  100% {
    bottom: 0;
    opacity: 1;
  }
}
/* Notiflix: Notify animation => from bottom: end */
/* Notiflix: Notify animation remove => fade: begin */
[id^=NotiflixNotifyOverlay].nx-with-animation.nx-remove,
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-fade.nx-remove {
  opacity: 0;
  -webkit-animation: notify-remove-fade 0.3s ease-in-out 0s normal;
  animation: notify-remove-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-remove-fade {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes notify-remove-fade {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* Notiflix: Notify animation remove => fade: end */
/* Notiflix: Notify animation remove => zoom: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-zoom.nx-remove {
  -webkit-transform: scale(0);
  transform: scale(0);
  -webkit-animation: notify-remove-zoom 0.3s ease-in-out 0s normal;
  animation: notify-remove-zoom 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-remove-zoom {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
@keyframes notify-remove-zoom {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
/* Notiflix: Notify animation remove => zoom: end */
/* Notiflix: Notify animation remove => from top: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-top.nx-remove {
  opacity: 0;
  -webkit-animation: notify-remove-to-top 0.3s ease-in-out 0s normal;
  animation: notify-remove-to-top 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-remove-to-top {
  0% {
    top: 0;
    opacity: 1;
  }
  50% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: -50px;
    opacity: 0;
  }
}
@keyframes notify-remove-to-top {
  0% {
    top: 0;
    opacity: 1;
  }
  50% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: -50px;
    opacity: 0;
  }
}
/* Notiflix: Notify animation remove => from top: end */
/* Notiflix: Notify animation remove => from right: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-right.nx-remove {
  opacity: 0;
  -webkit-animation: notify-remove-to-right 0.3s ease-in-out 0s normal;
  animation: notify-remove-to-right 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-remove-to-right {
  0% {
    right: 0;
    opacity: 1;
  }
  50% {
    right: 8px;
    opacity: 1;
  }
  100% {
    right: -300px;
    opacity: 0;
  }
}
@keyframes notify-remove-to-right {
  0% {
    right: 0;
    opacity: 1;
  }
  50% {
    right: 8px;
    opacity: 1;
  }
  100% {
    right: -300px;
    opacity: 0;
  }
}
/* Notiflix: Notify animation remove => from right: end */
/* Notiflix: Notify animation remove => from bottom: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-bottom.nx-remove {
  opacity: 0;
  -webkit-animation: notify-remove-to-bottom 0.3s ease-in-out 0s normal;
  animation: notify-remove-to-bottom 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-remove-to-bottom {
  0% {
    bottom: 0;
    opacity: 1;
  }
  50% {
    bottom: 8px;
    opacity: 1;
  }
  100% {
    bottom: -50px;
    opacity: 0;
  }
}
@keyframes notify-remove-to-bottom {
  0% {
    bottom: 0;
    opacity: 1;
  }
  50% {
    bottom: 8px;
    opacity: 1;
  }
  100% {
    bottom: -50px;
    opacity: 0;
  }
}
/* Notiflix: Notify animation remove => from bottom: end */
/* Notiflix: Notify animation remove => from left: begin */
[id^=NotiflixNotifyWrap] > div.nx-with-animation.nx-from-left.nx-remove {
  opacity: 0;
  -webkit-animation: notify-remove-to-left 0.3s ease-in-out 0s normal;
  animation: notify-remove-to-left 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes notify-remove-to-left {
  0% {
    left: 0;
    opacity: 1;
  }
  50% {
    left: 8px;
    opacity: 1;
  }
  100% {
    left: -300px;
    opacity: 0;
  }
}
@keyframes notify-remove-to-left {
  0% {
    left: 0;
    opacity: 1;
  }
  50% {
    left: 8px;
    opacity: 1;
  }
  100% {
    left: -300px;
    opacity: 0;
  }
}
/* Notiflix: Notify animation remove => from left: end */
/* NOTIFLIX.NOTIFY: end */
/* NOTIFLIX.REPORT: begin */
/* Notiflix: Report wrap: begin */
[id^=NotiflixReportWrap] {
  position: fixed;
  z-index: 4002;
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-family: "Quicksand", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  left: 0;
  top: 0;
  padding: 10px;
  color: #1e1e1e;
  border-radius: 25px;
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

[id^=NotiflixReportWrap] * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* Notiflix: Report wrap: end */
/* Notiflix: Report content: begin */
[id^=NotiflixReportWrap] > div[class*=-overlay] {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(255, 255, 255, 0.5);
  position: fixed;
  z-index: 0;
}

[id^=NotiflixReportWrap] > div.nx-report-click-to-close {
  cursor: pointer;
}

[id^=NotiflixReportWrap] > div[class*=-content] {
  width: 320px;
  max-width: 100%;
  max-height: 96vh;
  overflow-x: hidden;
  overflow-y: auto;
  border-radius: inherit;
  padding: 10px;
  -webkit-filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.05));
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.05));
  border: 1px solid rgba(0, 0, 0, 0.03);
  background: #f8f8f8;
  position: relative;
  z-index: 1;
}

[id^=NotiflixReportWrap] > div[class*=-content]::-webkit-scrollbar {
  width: 0;
  height: 0;
}

[id^=NotiflixReportWrap] > div[class*=-content]::-webkit-scrollbar-thumb {
  background: transparent;
}

[id^=NotiflixReportWrap] > div[class*=-content]::-webkit-scrollbar-track {
  background: transparent;
}

[id^=NotiflixReportWrap] > div[class*=-content] > div[class$=-icon] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 110px;
  height: 110px;
  display: block;
  margin: 6px auto 12px;
}

[id^=NotiflixReportWrap] > div[class*=-content] > div[class$=-icon] svg {
  min-width: 100%;
  max-width: 100%;
  height: auto;
}

[id^=NotiflixReportWrap] > * > h5 {
  word-break: break-all;
  word-break: break-word;
  font-family: inherit !important;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  margin: 0 0 10px;
  padding: 0 0 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  float: left;
  width: 100%;
  text-align: center;
}

[id^=NotiflixReportWrap] > * > p {
  word-break: break-all;
  word-break: break-word;
  font-family: inherit !important;
  font-size: 13px;
  line-height: 1.4;
  font-weight: normal;
  float: left;
  width: 100%;
  padding: 0 10px;
  margin: 0 0 10px;
}

[id^=NotiflixReportWrap] a#NXReportButton {
  word-break: break-all;
  word-break: break-word;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-family: inherit !important;
  -webkit-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  cursor: pointer;
  float: right;
  padding: 7px 17px;
  background: #32c682;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 500;
  border-radius: inherit !important;
  color: #fff;
}

[id^=NotiflixReportWrap] a#NXReportButton:hover {
  -webkit-box-shadow: inset 0 -60px 5px -5px rgba(0, 0, 0, 0.25);
  box-shadow: inset 0 -60px 5px -5px rgba(0, 0, 0, 0.25);
}

[id^=NotiflixReportWrap].nx-rtl-on a#NXReportButton {
  float: left;
}

/* Notiflix: Report content: end */
/* Notiflix: Report overlay animation => fade: begin */
[id^=NotiflixReportWrap] > div[class*=-overlay].nx-with-animation {
  -webkit-animation: report-overlay-animation 0.3s ease-in-out 0s normal;
  animation: report-overlay-animation 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes report-overlay-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes report-overlay-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Report overlay animation => fade: end */
/* Notiflix: Report content animation => fade: begin */
[id^=NotiflixReportWrap] > div[class*=-content].nx-with-animation.nx-fade {
  -webkit-animation: report-animation-fade 0.3s ease-in-out 0s normal;
  animation: report-animation-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes report-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes report-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Report content animation => fade: end */
/* Notiflix: Report content animation => zoom: begin */
[id^=NotiflixReportWrap] > div[class*=-content].nx-with-animation.nx-zoom {
  -webkit-animation: report-animation-zoom 0.3s ease-in-out 0s normal;
  animation: report-animation-zoom 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes report-animation-zoom {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes report-animation-zoom {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
/* Notiflix: Report content animation => zoom: end */
/* Notiflix: Report overlay animation remove => fade: begin */
[id^=NotiflixReportWrap].nx-remove > div[class*=-overlay].nx-with-animation {
  opacity: 0;
  -webkit-animation: report-overlay-animation-remove 0.3s ease-in-out 0s normal;
  animation: report-overlay-animation-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes report-overlay-animation-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes report-overlay-animation-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* Notiflix: Report overlay animation remove => fade: end */
/* Notiflix: Report content animation remove => fade: begin */
[id^=NotiflixReportWrap].nx-remove > div[class*=-content].nx-with-animation.nx-fade {
  opacity: 0;
  -webkit-animation: report-animation-fade-remove 0.3s ease-in-out 0s normal;
  animation: report-animation-fade-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes report-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes report-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* Notiflix: Report content animation remove => fade: end */
/* Notiflix: Report content animation remove => zoom: begin */
[id^=NotiflixReportWrap].nx-remove > div[class*=-content].nx-with-animation.nx-zoom {
  opacity: 0;
  -webkit-animation: report-animation-zoom-remove 0.3s ease-in-out 0s normal;
  animation: report-animation-zoom-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes report-animation-zoom-remove {
  0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
@keyframes report-animation-zoom-remove {
  0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
/* Notiflix: Report content animation remove => zoom: end */
/* NOTIFLIX.REPORT: end */
/* NOTIFLIX.CONFIRM: begin */
/* Notiflix: Confirm wrap: begin */
[id^=NotiflixConfirmWrap] {
  position: fixed;
  z-index: 4003;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: transparent;
  font-family: "Quicksand", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

[id^=NotiflixConfirmWrap].nx-position-center-top {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

[id^=NotiflixConfirmWrap].nx-position-center-bottom {
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

[id^=NotiflixConfirmWrap].nx-position-left-top {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

[id^=NotiflixConfirmWrap].nx-position-left-center {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
}

[id^=NotiflixConfirmWrap].nx-position-left-bottom {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

[id^=NotiflixConfirmWrap].nx-position-right-top {
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

[id^=NotiflixConfirmWrap].nx-position-right-center {
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
}

[id^=NotiflixConfirmWrap].nx-position-right-bottom {
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

[id^=NotiflixConfirmWrap] * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* Notiflix: Confirm wrap: end */
/* Notiflix: Confirm overlay: begin */
[id^=NotiflixConfirmWrap] > div[class*=-overlay] {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(255, 255, 255, 0.5);
  position: fixed;
  z-index: 0;
}

/* Notiflix: Confirm overlay: end */
/* Notiflix: Confirm overlay animation => fade: begin */
/* stylelint-disable no-descending-specificity */
[id^=NotiflixConfirmWrap] > div[class*=-overlay].nx-with-animation {
  -webkit-animation: confirm-overlay-animation 0.3s ease-in-out 0s normal;
  animation: confirm-overlay-animation 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes confirm-overlay-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes confirm-overlay-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* stylelint-enable no-descending-specificity */
/* Notiflix: Confirm overlay animation => fade: end */
/* Notiflix: Confirm overlay animation remove => fade: begin */
/* stylelint-disable no-descending-specificity */
[id^=NotiflixConfirmWrap].nx-remove > div[class*=-overlay].nx-with-animation {
  opacity: 0;
  -webkit-animation: confirm-overlay-animation-remove 0.3s ease-in-out 0s normal;
  animation: confirm-overlay-animation-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes confirm-overlay-animation-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes confirm-overlay-animation-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* stylelint-enable no-descending-specificity */
/* Notiflix: Confirm overlay animation remove => fade: end */
/* Notiflix: Confirm content: begin */
/* stylelint-disable no-descending-specificity */
[id^=NotiflixConfirmWrap] > div[class*=-content] {
  width: 300px;
  max-width: 100%;
  max-height: 96vh;
  overflow-x: hidden;
  overflow-y: auto;
  border-radius: 25px;
  padding: 10px;
  margin: 0;
  -webkit-filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.05));
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.05));
  background: #f8f8f8;
  color: #1e1e1e;
  position: relative;
  z-index: 1;
  text-align: center;
}

/* stylelint-enable no-descending-specificity */
[id^=NotiflixConfirmWrap] > div[class*=-content]::-webkit-scrollbar {
  width: 0;
  height: 0;
}

[id^=NotiflixConfirmWrap] > div[class*=-content]::-webkit-scrollbar-thumb {
  background: transparent;
}

[id^=NotiflixConfirmWrap] > div[class*=-content]::-webkit-scrollbar-track {
  background: transparent;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] {
  float: left;
  width: 100%;
  text-align: inherit;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > h5 {
  float: left;
  width: 100%;
  margin: 0;
  padding: 0 0 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #32c682;
  font-family: inherit !important;
  font-size: 16px;
  line-height: 1.4;
  font-weight: 500;
  text-align: inherit;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div {
  font-family: inherit !important;
  margin: 15px 0 20px;
  padding: 0 10px;
  float: left;
  width: 100%;
  font-size: 14px;
  line-height: 1.4;
  font-weight: normal;
  color: inherit;
  text-align: inherit;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div > div {
  font-family: inherit !important;
  float: left;
  width: 100%;
  margin: 15px 0 0;
  padding: 0;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div > div > input {
  font-family: inherit !important;
  float: left;
  width: 100%;
  height: 40px;
  margin: 0;
  padding: 0 15px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 25px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  -webkit-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  text-align: left;
}

[id^=NotiflixConfirmWrap].nx-rtl-on > div[class*=-content] > div[class*=-head] > div > div > input {
  text-align: right;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div > div > input:hover {
  border-color: rgba(0, 0, 0, 0.1);
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div > div > input:focus {
  border-color: rgba(0, 0, 0, 0.3);
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div > div > input.nx-validation-failure {
  border-color: #ff5549;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-head] > div > div > input.nx-validation-success {
  border-color: #32c682;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-buttons] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-radius: inherit;
  float: left;
  width: 100%;
  text-align: inherit;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-buttons] > a {
  cursor: pointer;
  font-family: inherit !important;
  -webkit-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  float: left;
  width: 48%;
  padding: 9px 5px;
  border-radius: inherit !important;
  font-weight: 500;
  font-size: 15px;
  line-height: 1.4;
  color: #f8f8f8;
  text-align: inherit;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-buttons] > a.nx-confirm-button-ok {
  margin: 0 2% 0 0;
  background: #32c682;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-buttons] > a.nx-confirm-button-cancel {
  margin: 0 0 0 2%;
  background: #a9a9a9;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-buttons] > a.nx-full {
  margin: 0;
  width: 100%;
}

[id^=NotiflixConfirmWrap] > div[class*=-content] > div[class*=-buttons] > a:hover {
  -webkit-box-shadow: inset 0 -60px 5px -5px rgba(0, 0, 0, 0.25);
  box-shadow: inset 0 -60px 5px -5px rgba(0, 0, 0, 0.25);
}

/* Notiflix: Confirm content: end */
/* Notiflix: Confirm rtl: begin */
[id^=NotiflixConfirmWrap].nx-rtl-on > div[class*=-content] > div[class*=-buttons],
[id^=NotiflixConfirmWrap].nx-rtl-on > div[class*=-content] > div[class*=-buttons] > a {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
}

/* Notiflix: Confirm rtl: end */
/* Notiflix: Confirm content animation => fade: begin */
[id^=NotiflixConfirmWrap].nx-with-animation.nx-fade > div[class*=-content] {
  -webkit-animation: confirm-animation-fade 0.3s ease-in-out 0s normal;
  animation: confirm-animation-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes confirm-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes confirm-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Confirm content animation => fade: end */
/* Notiflix: Confirm content animation => zoom: begin */
[id^=NotiflixConfirmWrap].nx-with-animation.nx-zoom > div[class*=-content] {
  -webkit-animation: confirm-animation-zoom 0.3s ease-in-out 0s normal;
  animation: confirm-animation-zoom 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes confirm-animation-zoom {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes confirm-animation-zoom {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
/* Notiflix: Confirm content animation => zoom: end */
/* Notiflix: Confirm content animation remove => fade: begin */
[id^=NotiflixConfirmWrap].nx-with-animation.nx-fade.nx-remove > div[class*=-content] {
  opacity: 0;
  -webkit-animation: confirm-animation-fade-remove 0.3s ease-in-out 0s normal;
  animation: confirm-animation-fade-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes confirm-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes confirm-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* Notiflix: Confirm content animation remove => fade: end */
/* Notiflix: Confirm content animation remove => zoom: begin */
[id^=NotiflixConfirmWrap].nx-with-animation.nx-zoom.nx-remove > div[class*=-content] {
  opacity: 0;
  -webkit-animation: confirm-animation-zoom-remove 0.3s ease-in-out 0s normal;
  animation: confirm-animation-zoom-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes confirm-animation-zoom-remove {
  0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
@keyframes confirm-animation-zoom-remove {
  0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
/* Notiflix: Confirm content animation remove => zoom: end */
/* NOTIFLIX.CONFIRM: end */
/* NOTIFLIX.LOADING: begin */
/* Notiflix: Loading wrap: begin */
[id^=NotiflixLoadingWrap] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: fixed;
  z-index: 4000;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.8);
  font-family: "Quicksand", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

[id^=NotiflixLoadingWrap] * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

[id^=NotiflixLoadingWrap].nx-loading-click-to-close {
  cursor: pointer;
}

/* Notiflix: Loading wrap: end */
/* Notiflix: Loading content: begin */
[id^=NotiflixLoadingWrap] > div[class*=-icon] {
  width: 60px;
  height: 60px;
  position: relative;
  -webkit-transition: top 0.2s ease-in-out;
  -o-transition: top 0.2s ease-in-out;
  transition: top 0.2s ease-in-out;
  margin: 0 auto;
}

/* stylelint-disable no-descending-specificity */
[id^=NotiflixLoadingWrap] > div[class*=-icon] img,
[id^=NotiflixLoadingWrap] > div[class*=-icon] svg {
  max-width: unset;
  max-height: unset;
  width: 100%;
  height: auto;
  position: absolute;
  left: 0;
  top: 0;
}

[id^=NotiflixLoadingWrap] > p {
  position: relative;
  margin: 10px auto 0;
  font-family: inherit !important;
  font-weight: normal;
  font-size: 15px;
  line-height: 1.4;
  padding: 0 10px;
  width: 100%;
  text-align: center;
}

/* stylelint-enable no-descending-specificity */
/* Notiflix: Loading content: end */
/* Notiflix: Loading animation => fade: begin */
[id^=NotiflixLoadingWrap].nx-with-animation {
  -webkit-animation: loading-animation-fade 0.3s ease-in-out 0s normal;
  animation: loading-animation-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes loading-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes loading-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Loading animation => fade: end */
/* Notiflix: Loading animation remove => fade: begin */
[id^=NotiflixLoadingWrap].nx-with-animation.nx-remove {
  opacity: 0;
  -webkit-animation: loading-animation-fade-remove 0.3s ease-in-out 0s normal;
  animation: loading-animation-fade-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes loading-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes loading-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* Notiflix: Loading animation remove => fade: end */
/* Notiflix: Loading animation new message => fade: begin */
[id^=NotiflixLoadingWrap] > p.nx-loading-message-new {
  -webkit-animation: loading-new-message-fade 0.3s ease-in-out 0s normal;
  animation: loading-new-message-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes loading-new-message-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes loading-new-message-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Loading animation new message => fade: end */
/* NOTIFLIX.LOADING: end */
/* NOTIFLIX.BLOCK: begin */
/* Notiflix: Block wrap: begin */
[id^=NotiflixBlockWrap] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
  z-index: 1000;
  font-family: "Quicksand", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background: rgba(255, 255, 255, 0.9);
  text-align: center;
  animation-duration: 400ms;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  border-radius: inherit;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

[id^=NotiflixBlockWrap] * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* Notiflix: Block wrap: end */
/* Notiflix: Block content: begin */
[id^=NotiflixBlockWrap] > span[class*=-icon] {
  display: block;
  width: 45px;
  height: 45px;
  position: relative;
  margin: 0 auto;
}

/* stylelint-disable no-descending-specificity */
[id^=NotiflixBlockWrap] > span[class*=-icon] svg {
  width: inherit;
  height: inherit;
}

/* stylelint-enable no-descending-specificity */
[id^=NotiflixBlockWrap] > span[class*=-message] {
  position: relative;
  display: block;
  width: 100%;
  margin: 10px auto 0;
  padding: 0 10px;
  font-family: inherit !important;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.4;
}

/* Notiflix: Block content: end */
/* Notiflix: Block animation => fade: begin */
[id^=NotiflixBlockWrap].nx-with-animation {
  -webkit-animation: block-animation-fade 0.3s ease-in-out 0s normal;
  animation: block-animation-fade 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes block-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes block-animation-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Notiflix: Block animation => fade: end */
/* Notiflix: Block animation remove => fade: begin */
[id^=NotiflixBlockWrap].nx-with-animation.nx-remove {
  opacity: 0;
  -webkit-animation: block-animation-fade-remove 0.3s ease-in-out 0s normal;
  animation: block-animation-fade-remove 0.3s ease-in-out 0s normal;
}

@-webkit-keyframes block-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes block-animation-fade-remove {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* Notiflix: Block animation remove => fade: end */
/* NOTIFLIX.BLOCK: end */
.notiflix-block .sk-wave,
.notiflix-loading .sk-wave {
  --sk-color: var(--bs-white);
}
