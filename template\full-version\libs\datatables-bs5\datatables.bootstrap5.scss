@import "../../scss/_bootstrap-extended/include";
@import "datatables.net-bs5/css/dataTables.bootstrap5";

/* FIX: broken ui in tablet devices */
@media (hover: none) and (pointer: coarse) {
  table.dataTable {
    display: table;
    overflow-x: auto;
  }
}

/* Card header inside the datatable */
div.dt-container {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dt-buttons .dt-button-down-arrow {
    display: none;
  }

  /* Remove left and right border from datatable with table-bordered class */
  .table.table-bordered.dataTable {
    &.dt-complex-header,
    &.dt-column-search {
      thead tr th {
        border-width: $border-width;
        &:first-child {
          border-inline-start-width: 0;
        }
        &:last-child {
          border-inline-end-width: 0;
        }
      }
    }
    th,
    td {
      &:last-child {
        border-width: 0;
      }
    }
    thead tr {
      border-block-end-width: 0;
      border-block-start-width: 0;
    }
    tbody tr:first-child {
      border-block-start-width: 0;
    }
  }

  /* Responsive datatable in desktop screen */
  @media screen and (width >= 1399.98px) {
    table.table-responsive {
      display: table;
    }
  }
  div.dt-search {
    input {
      margin-inline: .5rem 0;
    }
  }

  div.dt-layout-end > *:not(:first-child) {
    margin-inline: 0;
  }
  table.table-bordered.dataTable tr {
    td,
    th,
    th:first-child {
      border-inline-start-width: 0 !important;
    }
  }

  table.dataTable {
    border-collapse: collapse;
    inline-size: 100%;
    margin-block: 0 $spacer;

    /* Checkbox height & width for datatables checkboxes */
    .form-check-input {
      block-size: $form-datatables-check-input-size;
      inline-size: $form-datatables-check-input-size;
    }

    thead {
      > tr {
        > th,
        > td {
          &.dt-orderable-asc,
          &.dt-orderable-desc {
            &:hover {
              outline: none !important;
            }
          }
        }
      }
      th {
        /* Used while complex headers */
        vertical-align: middle;
      }
    }
    .dt-control.dt-orderable-none .dt-column-order::before {
      opacity: 0;
    }
    td.dt-control::before {
      border: 2px solid;
      block-size: .55em;
      border-block-start: 0;
      border-inline-start: 0;
      inline-size: .55em;
      transform: rotate(-45deg);
      :dir(rtl) & {
        transform: rotate(-315deg);
      }
    }
    tr.dt-hasChild td.dt-control::before {
      border-block-start: 0;
      border-inline-start: 0;
      transform: rotate(45deg);
      :dir(rtl) & {
        transform: rotate(-45deg);
      }
    }
    &.table tbody > tr {
      &.selected,
      & > .selected {
        background-color: rgba(var(--#{$prefix}primary-rgb), .08);
        > * {
          box-shadow: none;
          color: var(--#{$prefix}body-color);
        }
        a {
          color: var(--#{$prefix}body-color);
        }
      }
    }
  }

  div.dt-scroll-body {
    border-block-end-width: 0;
  }

  @media screen and (width <= 575.98px) {
    .card-header {
      .dt-action-buttons {
        padding-block-start: 1rem;
      }
    }
    .dtr-bs-modal.modal {
      .modal-body {
        padding: 0;
        .table {
          margin-block-end: 0;
        }
      }
    }
  }

  @media screen and (width <= 767.98px) {
    div.dt-info {
      padding-block: 0 $table-cell-padding-y;
    }
  }

  div.dt-length,
  div.dt-search {
    margin-block: $spacer * 1.25;
  }

  div.dt-length select {
    margin-inline: .5rem;
  }

  /* common style for light / dark */
  div.dt-paging ul.pagination .page-link {
    padding: $pagination-padding-y;
    font-size: $font-size-sm;
    line-height: $pagination-line-height;
    &.first,
    &.last,
    &.next,
    &.prev,
    &.previous {
      padding-block: $pagination-padding-y - .0845rem;
      padding-inline: $pagination-padding-y - .0845rem;
    }
  }

  @media screen and (width <= 575.98px) {
    div.dt-paging ul.pagination .page-link {
      min-block-size:
        calc(
          #{"#{($font-size-sm * $pagination-line-height) + ($pagination-padding-y-sm * 2)} + calc(#{$pagination-border-width} * 2)"}
        );
      min-inline-size:
        calc(
          #{"#{($font-size-sm * $pagination-line-height) + ($pagination-padding-x-sm * 2.356)} + calc(#{$pagination-border-width} * 2)"}
        );
      padding-block: $pagination-padding-y-sm;
      padding-inline: $pagination-padding-x-sm;
    }
    div.dt-paging ul.pagination {
      .page-item {
        .next,
        .previous,
        .first,
        .last {
          &.page-link {
            padding: calc($pagination-padding-y * .35);
          }
        }
      }
    }
  }
  div.dt-info .select-info .select-item {
    margin-inline: .5rem;
  }
}

/* DataTable within card */
div.card-datatable {
  padding-block-end: $card-spacer-x-sm;
  div.dt-info {
    color: var(--#{$prefix}body-color);
  }
}

.table-bordered > :not(caption) > * > * {
  border-width: 0;
}


/* overrides styles of library for rtl */
table.dataTable {
  &,
  thead,
  tfoot {
    th,
    td {
      &,
      &.dt-type-numeric,
      &.dt-type-date {
        text-align: start;
      }
    }
  }
  thead {
    .dt-orderable-asc.dt-orderable-desc,
    .dt-ordering-desc,
    .dt-ordering-asc {
      .dt-column-order {
        inset-inline: auto 1em;
        &::before,
        &::after {
          background-color: var(--#{$prefix}heading-color);
          block-size: 1.125rem;
          content: "";
          inline-size: 1.125rem;
          mask-repeat: no-repeat;
          mask-size: 100% 100%;
          visibility: hidden;
        }

        &::before {
          mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='m6.293 13.293l1.414 1.414L12 10.414l4.293 4.293l1.414-1.414L12 7.586z'/%3E%3C/svg%3E");
        }
        &::after {
          mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M16.293 9.293L12 13.586L7.707 9.293l-1.414 1.414L12 16.414l5.707-5.707z'/%3E%3C/svg%3E");
        }
      }
      &:hover {
        .dt-column-order {
          &::before,
          &::after {
            visibility: visible;
          }
        }
      }
    }
  }
}

.dt-column-search tr th {
  border-block-end-color: var(--#{$prefix}table-border-color) !important;
}

/* text align styles in modal */
.modal.dtr-bs-modal .text-xxl-center {
  text-align: start !important;
}
