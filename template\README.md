<p align="center"></p>

<p align="center">
   <a href="https://1.envato.market/materialize_admin" target="_blank">
      <img src="https://cdn.pixinvent.com/pi-assets/materialize/admin-template/logo/logo.png" alt="materialize-logo" width="60px" height="auto">
   </a>
</p>

<h1 align="center">
   <a href="https://1.envato.market/materialize_admin" target="_blank" align="center">
      Materialize - Material Design HTML Admin Template
   </a>
</h1>

<p align="center">Most developer friendly, Powerful & highly customizable HTML Admin Dashboard Template.</p>

<p align="center">
  <a href="https://twitter.com/pixinvents" target="_blank">
     <img alt="Twitter Follow" src="https://img.shields.io/twitter/follow/pixinvents">
  </a>
</p>

<p align="center">
   <a href="https://1.envato.market/materialize_admin" target="_blank" align="center">
      <img src="https://cdn.pixinvent.com/pi-assets/materialize/admin-template/banner/banner.png" alt="Materialize - Material Design HTML Admin Template">
   </a>
</p>

## Introduction

Materialize - Material Design HTML Admin Template – is the most developer friendly & highly customizable Admin Dashboard Template built with Bootstrap 5.

If you’re a developer looking for an admin dashboard that is developer-friendly, rich with features, and highly customizable look no further than Materialize. We’ve followed the highest industry standards to bring you the very best admin template that is not only fast and easy to use but highly scalable. Offering ultimate convenience and flexibility, you’ll be able to build whatever application you want with very little hassle.

Build premium quality applications with ease. Use our innovative admin template to create eye-catching, high-quality WebApps. Your apps will be completely responsive, ensuring they’ll look stunning and function flawlessly on desktops, tablets, and mobile devices.

[View Demo](https://demos.pixinvent.com/materialize-html-admin-template/html/vertical-menu-template/)

## Installation ⚒️

Please [visit](https://demos.pixinvent.com/materialize-html-admin-template/documentation/installation-build.html) our docs for installation guide.

## Documentation 📜

Check out our live [Documentation](https://demos.pixinvent.com/materialize-html-admin-template/documentation/)

## Support 👨‍💻

We use Product purchase code as support tickets to manage Item support.

Make sure you use our [Support Portal](https://pixinvent.ticksy.com/) to create a support ticket.

## Why forking is disabled? 🔒

You will lose repository access when your support expires. If you have forked the repo and you lose the access to our repo then your fork also got deleted by GitHub.

Hence, as precautions we disabled forking our repo.

**Solution**

However, you can still keep your repo even after your support expires by setting our repo as upstream:

1. Create a new **private repo** in your GitHub. Make sure it's a private repo. You can't share our code publicly as per license.
2. Clone our repo: `git clone https://github.com/pixinvent/materialize-bootstrap-html-admin-template`
3. Navigate to cloned directory
4. Remove our repo as origin by running command: `git remote remove origin`
5. Add your newly created repo as origin by running command: `git remote add origin YOUR_NEWLY_CREATE_REPO_GIT_URL`
6. Add our repo as upstream by running command: `git remote add upstream https://github.com/pixinvent/materialize-bootstrap-html-admin-template`
7. Push the code to GitHub by running command: `git push -u origin main`

Now, whenever you want to pull the latest changes from our repo just pull the changes by running command: `git pull upstream main`

Cheers 🥂

## Contributing 🦸

Contributions are always welcome and recommended! Here is how:

- Clone our repo `git clone https://github.com/pixinvent/materialize-bootstrap-html-admin-template`
- create a new branch based on branch you want to make Pull request to. For example if you want to make pull request to main branch, create new branch based on main branch.
- Push your changes to new branch
- Create pull request

### Contribution Requirements 🧰

- When you contribute, you agree to give a non-exclusive license to PixInvent to use that contribution in any context as we (PixInvent) see appropriate.
- If you use content provided by another party, it must be appropriately licensed using an open source license.
- Contributions are only accepted through Github pull requests.
- Finally, contributed code must work in all supported browsers.

## Useful Links 🎁

- [Vue CheatSheet](https://vue-cheatsheet.themeselection.com/)
- [Freebies](https://themeselection.com/item/category/freebies/)
- [Free Admin Templates](https://themeselection.com/item/category/free-admin-templates/)
- [Bootstrap 5 CheatSheet](https://bootstrap-cheatsheet.themeselection.com/)

## Social Media 🌍

- [X](https://x.com/Theme_Selection)
- [Facebook](https://www.facebook.com/ThemeSelections/)
- [Pinterest](https://pinterest.com/themeselect/)
- [Instagram](https://www.instagram.com/themeselection/)
- [Discord](https://discord.gg/kBHkY7DekX)
- [YouTube](https://www.youtube.com/channel/UCuryo5s0CW4aP83itLjIdZg)
