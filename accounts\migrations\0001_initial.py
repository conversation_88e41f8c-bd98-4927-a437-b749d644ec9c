# Generated by Django 5.0.14 on 2025-06-27 15:58

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("organisations", "0002_alter_organisation_telephone"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DemandeInscription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "nom_organisation",
                    models.CharField(
                        help_text="Nom complet de l'établissement hospitalier",
                        max_length=200,
                        verbose_name="Nom de l'organisation",
                    ),
                ),
                (
                    "adresse_organisation",
                    models.TextField(
                        help_text="Adresse postale complète",
                        verbose_name="Adresse de l'organisation",
                    ),
                ),
                (
                    "telephone_organisation",
                    models.Char<PERSON>ield(
                        help_text="Numéro de téléphone au format international",
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Format de téléphone invalide. Utilisez le format international.",
                                regex="^\\+[1-9]\\d{1,14}$",
                            )
                        ],
                        verbose_name="Téléphone de l'organisation",
                    ),
                ),
                (
                    "email_organisation",
                    models.EmailField(
                        help_text="Adresse email principale de l'organisation",
                        max_length=254,
                        verbose_name="Email de l'organisation",
                    ),
                ),
                (
                    "type_abonnement_demande",
                    models.CharField(
                        choices=[("GRATUIT", "Gratuit"), ("PREMIUM", "Premium")],
                        default="GRATUIT",
                        help_text="Type d'abonnement souhaité",
                        max_length=10,
                        verbose_name="Type d'abonnement demandé",
                    ),
                ),
                (
                    "prenom_admin",
                    models.CharField(
                        help_text="Prénom de la personne qui sera administrateur de l'organisation",
                        max_length=100,
                        verbose_name="Prénom de l'administrateur",
                    ),
                ),
                (
                    "nom_admin",
                    models.CharField(
                        help_text="Nom de famille de l'administrateur",
                        max_length=100,
                        verbose_name="Nom de l'administrateur",
                    ),
                ),
                (
                    "email_admin",
                    models.EmailField(
                        help_text="Adresse email qui servira pour la connexion",
                        max_length=254,
                        unique=True,
                        verbose_name="Email de l'administrateur",
                    ),
                ),
                (
                    "telephone_admin",
                    models.CharField(
                        help_text="Numéro de téléphone personnel",
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Format de téléphone invalide. Utilisez le format international.",
                                regex="^\\+[1-9]\\d{1,14}$",
                            )
                        ],
                        verbose_name="Téléphone de l'administrateur",
                    ),
                ),
                (
                    "mot_de_passe",
                    models.CharField(
                        help_text="Mot de passe pour le compte administrateur (sera hashé)",
                        max_length=128,
                        verbose_name="Mot de passe",
                    ),
                ),
                (
                    "statut",
                    models.CharField(
                        choices=[
                            ("EN_ATTENTE", "En attente de validation"),
                            ("VALIDEE", "Validée et activée"),
                            ("REJETEE", "Rejetée"),
                        ],
                        default="EN_ATTENTE",
                        max_length=15,
                        verbose_name="Statut de la demande",
                    ),
                ),
                (
                    "date_demande",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        verbose_name="Date de la demande",
                    ),
                ),
                (
                    "date_traitement",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date de traitement"
                    ),
                ),
                (
                    "commentaire_admin",
                    models.TextField(
                        blank=True,
                        help_text="Commentaire lors de la validation ou du rejet",
                        verbose_name="Commentaire du super admin",
                    ),
                ),
                (
                    "organisation_creee",
                    models.OneToOneField(
                        blank=True,
                        help_text="Organisation créée suite à la validation",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisation",
                        verbose_name="Organisation créée",
                    ),
                ),
                (
                    "traite_par",
                    models.ForeignKey(
                        blank=True,
                        help_text="Super admin qui a traité la demande",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Traité par",
                    ),
                ),
                (
                    "utilisateur_cree",
                    models.OneToOneField(
                        blank=True,
                        help_text="Compte utilisateur créé pour l'administrateur",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="demande_inscription",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Utilisateur créé",
                    ),
                ),
            ],
            options={
                "verbose_name": "Demande d'inscription",
                "verbose_name_plural": "Demandes d'inscription",
                "ordering": ["-date_demande"],
            },
        ),
    ]
