/* Functional styling;
 * These styles are required for noUiSlider to function.
 * You don't need to change these rules to apply your design.
 */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-target {
  position: relative;
}

.noUi-base,
.noUi-connects {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

/* Wrapper for all connect elements.
 */
.noUi-connects {
  overflow: hidden;
  z-index: 0;
}

.noUi-connect,
.noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  -webkit-transform-style: preserve-3d;
  transform-origin: 0 0;
  transform-style: flat;
}

/* Offset direction
 */
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  left: 0;
  right: auto;
}

/* Give origins 0 height/width so they don't interfere with clicking the
 * connect elements.
 */
.noUi-vertical .noUi-origin {
  top: -100%;
  width: 0;
}

.noUi-horizontal .noUi-origin {
  height: 0;
}

.noUi-handle {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
}

.noUi-touch-area {
  height: 100%;
  width: 100%;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  -webkit-transition: transform 0.3s;
  transition: transform 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

/* Slider size and handle placement;
 */
.noUi-horizontal {
  height: 18px;
}

.noUi-horizontal .noUi-handle {
  width: 34px;
  height: 28px;
  right: -17px;
  top: -6px;
}

.noUi-vertical {
  width: 18px;
}

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  right: -6px;
  bottom: -17px;
}

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -17px;
  right: auto;
}

/* Styling;
 * Giving the connect element a border radius causes issues with using transform: scale
 */
.noUi-target {
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #D3D3D3;
  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;
}

.noUi-connects {
  border-radius: 3px;
}

.noUi-connect {
  background: #3FB8AF;
}

/* Handles and cursors;
 */
.noUi-draggable {
  cursor: ew-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}

.noUi-handle {
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #FFF;
  cursor: default;
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
}

.noUi-active {
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;
}

/* Handle stripes;
 */
.noUi-handle:before,
.noUi-handle:after {
  content: "";
  display: block;
  position: absolute;
  height: 14px;
  width: 1px;
  background: #E8E7E6;
  left: 14px;
  top: 6px;
}

.noUi-handle:after {
  left: 17px;
}

.noUi-vertical .noUi-handle:before,
.noUi-vertical .noUi-handle:after {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px;
}

.noUi-vertical .noUi-handle:after {
  top: 17px;
}

/* Disabled state;
 */
[disabled] .noUi-connect {
  background: #B8B8B8;
}

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
  cursor: not-allowed;
}

/* Base;
 *
 */
.noUi-pips,
.noUi-pips * {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-pips {
  position: absolute;
  color: #999;
}

/* Values;
 *
 */
.noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center;
}

.noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}

/* Markings;
 *
 */
.noUi-marker {
  position: absolute;
  background: #CCC;
}

.noUi-marker-sub {
  background: #AAA;
}

.noUi-marker-large {
  background: #AAA;
}

/* Horizontal layout;
 *
 */
.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%;
}

.noUi-value-horizontal {
  -webkit-transform: translate(-50%, 50%);
  transform: translate(-50%, 50%);
}

.noUi-rtl .noUi-value-horizontal {
  -webkit-transform: translate(50%, 50%);
  transform: translate(50%, 50%);
}

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px;
}

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}

/* Vertical layout;
 *
 */
.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%;
}

.noUi-value-vertical {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  padding-left: 25px;
}

.noUi-rtl .noUi-value-vertical {
  -webkit-transform: translate(0, 50%);
  transform: translate(0, 50%);
}

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px;
}

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}

.noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
  white-space: nowrap;
}

.noUi-horizontal .noUi-tooltip {
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%;
}

.noUi-vertical .noUi-tooltip {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  top: 50%;
  right: 120%;
}

.noUi-horizontal .noUi-origin > .noUi-tooltip {
  -webkit-transform: translate(50%, 0);
  transform: translate(50%, 0);
  left: auto;
  bottom: 10px;
}

.noUi-vertical .noUi-origin > .noUi-tooltip {
  -webkit-transform: translate(0, -18px);
  transform: translate(0, -18px);
  top: auto;
  right: 28px;
}

.noUi-target {
  --bs-noUi-base-color: var(--bs-primary);
  --bs-noUi-thumb-shadow: var(--bs-primary-rgb);
  --bs-popover-bg: var(--bs-paper-bg);
  --bs-noUiSlider-line-color: var(--bs-primary-bg-subtle);
  --bs-noUiSlider-tooltip-bg: #282a42;
  position: relative;
  border-width: 0;
  background: var(--bs-noUiSlider-line-color);
  box-shadow: none;
  /* Handles and cursors */
  /* Tooltips */
  /* Slider size and handle placement */
  /* Horizontal layout */
  /* Vertical layout */
  /* Disabled state */
  /* Base */
  /* Values */
  /* Markings */
}
.noUi-target, .noUi-target * {
  box-sizing: border-box;
  touch-action: none;
  user-select: none;
}
.noUi-target .noUi-draggable {
  cursor: ew-resize;
}
.noUi-target .noUi-tooltip {
  position: absolute;
  display: block;
  border: none;
  border-radius: 0.25rem;
  background: var(--bs-noUiSlider-tooltip-bg);
  color: var(--bs-paper-bg);
  font-size: 0.8125rem;
  line-height: 1;
  padding-block: 0.25rem;
  padding-inline: 0.75rem;
  text-align: center;
  transition: transform 0.2s;
}
.noUi-target .noUi-tooltip::after {
  position: absolute;
  block-size: 0;
  clear: both;
  content: "";
  inline-size: 0;
}
.noUi-target.noUi-horizontal {
  block-size: 0.375rem;
}
.noUi-target.noUi-horizontal .noUi-handle.noUi-active .noUi-tooltip {
  transform: translate(-50%, 10%) scale(0.8, 0.8);
}
.noUi-target.noUi-horizontal .noUi-handle {
  block-size: 1.375rem;
  inline-size: 1.375rem;
  inset-block-start: -0.5rem;
  inset-inline: auto calc(-1.375rem * 0.5);
  transition: all 0.2s;
}
.noUi-target.noUi-horizontal .noUi-pips-horizontal {
  padding-block: 0.875rem 0;
  padding-inline: 0;
}
.noUi-target.noUi-horizontal .noUi-value-horizontal {
  padding-block-start: 0.125rem;
  transform: translate(-50%, 50%);
}
:dir(rtl) .noUi-target.noUi-horizontal .noUi-value-horizontal {
  transform: translate(50%, 50%);
}
.noUi-target.noUi-horizontal .noUi-marker-horizontal.noUi-marker {
  block-size: 0.5rem;
  inline-size: 1px;
}
.noUi-target.noUi-horizontal .noUi-tooltip {
  inset-block-end: 125%;
  transform: translate(-50%, -45%);
}
.noUi-target.noUi-horizontal .noUi-tooltip::after {
  border-block-start: 8px solid var(--bs-noUiSlider-tooltip-bg);
  border-inline-end: 8px solid transparent;
  border-inline-start: 8px solid transparent;
  content: "";
  inset-block-start: 1.25rem;
  inset-inline-start: 50%;
  transform: translateX(-50%);
}
:dir(rtl) .noUi-target.noUi-horizontal .noUi-tooltip::after {
  transform: translateX(50%);
}
.noUi-target.noUi-vertical {
  inline-size: 0.375rem;
}
.noUi-target.noUi-vertical .noUi-origin {
  inline-size: 0;
}
.noUi-target.noUi-vertical .noUi-handle {
  block-size: 1.375rem;
  inline-size: 1.375rem;
  inset-block-end: -1.375rem;
  inset-inline-end: -0.5175rem;
}
.noUi-target.noUi-vertical .noUi-handle.noUi-active .noUi-tooltip {
  transform: translate(10%, -50%) scale(0.8, 0.8);
}
:dir(rtl) .noUi-target.noUi-vertical .noUi-handle.noUi-active .noUi-tooltip {
  transform: translate(-10%, -50%) scale(0.8, 0.8);
}
:dir(rtl) .noUi-target.noUi-vertical .noUi-handle {
  inset-inline-start: -0.5175rem;
}
.noUi-target.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}
.noUi-target.noUi-vertical .noUi-pips-vertical {
  padding-block: 0;
  padding-inline: 0.875rem 0;
}
.noUi-target.noUi-vertical .noUi-value-vertical {
  padding-inline-start: 0.875rem;
}
.noUi-target.noUi-vertical .noUi-marker-vertical.noUi-marker {
  block-size: 1px;
  inline-size: 0.5rem;
}
.noUi-target.noUi-vertical .noUi-tooltip {
  inset-inline: auto 125%;
  transform: translate(-15%, -52%);
}
.noUi-target.noUi-vertical .noUi-tooltip::after {
  border-block-end: 8px solid transparent;
  border-block-start: 8px solid transparent;
  border-inline-start: 8px solid var(--bs-noUiSlider-tooltip-bg);
  content: "";
  inset-block-start: 14%;
  inset-inline-end: -5px;
}
:dir(rtl) .noUi-target.noUi-vertical .noUi-tooltip {
  transform: translate(15%, -52%);
}
.noUi-target .noUi-handle {
  border-width: 0.25rem;
  border-color: var(--bs-noUi-base-color);
  border-radius: var(--bs-border-radius-pill);
  backface-visibility: hidden;
  box-shadow: 0 0 0 1px rgb(var(--bs-pure-black)/10%), var(--bs-floating-component-shadow);
  cursor: pointer;
  outline: none;
  transform-origin: center;
  transition: transform 0.2s;
}
.noUi-target .noUi-handle::before, .noUi-target .noUi-handle::after {
  display: none;
}
.noUi-target .noUi-handle.noUi-active {
  transform: scale(1.4, 1.4);
}
.noUi-target .noUi-touch-area {
  block-size: 100%;
  inline-size: 100%;
}
.noUi-target.noUi-state-tap .noUi-connect,
.noUi-target.noUi-state-tap .noUi-origin {
  transition: inset-block-start 0.3s, inset-inline-end 0.3s, inset-block-end 0.3s, inset-inline-start 0.3s;
}
.noUi-target[disabled] {
  opacity: 0.45;
}
.noUi-target[disabled] .noUi-handle {
  box-shadow: 0 0 0 1px rgb(var(--bs-pure-black)/5%);
}
.noUi-target[disabled],
.noUi-target [disabled].noUi-handle, .noUi-target[disabled] .noUi-handle {
  cursor: not-allowed;
  pointer-events: none;
}
.noUi-target .noUi-pips {
  position: absolute;
  color: var(--bs-secondary-color);
}
.noUi-target .noUi-pips, .noUi-target .noUi-pips * {
  box-sizing: border-box;
}
.noUi-target .noUi-value {
  position: absolute;
  color: var(--bs-gray-500);
  font-size: 0.625rem;
  white-space: nowrap;
}
.noUi-target .noUi-marker {
  position: absolute;
  background: var(--bs-gray-500);
}
.noUi-target .noUi-base .noUi-connect {
  background: var(--bs-noUi-base-color);
}
.noUi-target .noUi-base .noUi-handle:hover {
  box-shadow: 0 0 0 0.5rem rgba(var(--bs-noUi-thumb-shadow), 0.16);
}
.noUi-target .noUi-base .noUi-handle:active, .noUi-target .noUi-base .noUi-handle:focus {
  box-shadow: 0 0 0 0.625rem rgba(var(--bs-noUi-thumb-shadow), 0.16);
}

.noUi-primary {
  --bs-noUi-base-color: var(--bs-primary);
  --bs-noUi-thumb-shadow: var(--bs-primary-rgb);
  --bs-noUiSlider-line-color: var(--bs-primary-bg-subtle);
}

.noUi-secondary {
  --bs-noUi-base-color: var(--bs-secondary);
  --bs-noUi-thumb-shadow: var(--bs-secondary-rgb);
  --bs-noUiSlider-line-color: var(--bs-secondary-bg-subtle);
}

.noUi-success {
  --bs-noUi-base-color: var(--bs-success);
  --bs-noUi-thumb-shadow: var(--bs-success-rgb);
  --bs-noUiSlider-line-color: var(--bs-success-bg-subtle);
}

.noUi-info {
  --bs-noUi-base-color: var(--bs-info);
  --bs-noUi-thumb-shadow: var(--bs-info-rgb);
  --bs-noUiSlider-line-color: var(--bs-info-bg-subtle);
}

.noUi-warning {
  --bs-noUi-base-color: var(--bs-warning);
  --bs-noUi-thumb-shadow: var(--bs-warning-rgb);
  --bs-noUiSlider-line-color: var(--bs-warning-bg-subtle);
}

.noUi-danger {
  --bs-noUi-base-color: var(--bs-danger);
  --bs-noUi-thumb-shadow: var(--bs-danger-rgb);
  --bs-noUiSlider-line-color: var(--bs-danger-bg-subtle);
}

.noUi-light {
  --bs-noUi-base-color: var(--bs-light);
  --bs-noUi-thumb-shadow: var(--bs-light-rgb);
  --bs-noUiSlider-line-color: var(--bs-light-bg-subtle);
}

.noUi-dark {
  --bs-noUi-base-color: var(--bs-dark);
  --bs-noUi-thumb-shadow: var(--bs-dark-rgb);
  --bs-noUiSlider-line-color: var(--bs-dark-bg-subtle);
}

.noUi-gray {
  --bs-noUi-base-color: var(--bs-gray);
  --bs-noUi-thumb-shadow: var(--bs-gray-rgb);
  --bs-noUiSlider-line-color: var(--bs-gray-bg-subtle);
}

[data-bs-theme=dark] .noUi-tooltip {
  --bs-noUiSlider-tooltip-bg: #f7f4ff;
}
