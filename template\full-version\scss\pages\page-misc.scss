/* Miscellaneous
******************************************************************************* */

@import "../_bootstrap-extended/include";

// Misc wrapper styles
.misc-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.25rem;
  min-block-size: 100vh;
  text-align: center;
}

// Misc background image styles
.misc-bg {
  position: absolute;
  inline-size: 100%;
  inset-block-end: 0;
  inset-inline-start: 0;
}

// Misc object styles

.misc-object {
  position: absolute;
  z-index: 1;
  inset-block-end: 8%;
  inset-inline-start: 16%;
  &:dir(rtl) {
    inset-inline-start: 10%;
  }
}

// Misc model style
.misc-model {
  position: relative;
  inset-block-end: 3rem;
}
