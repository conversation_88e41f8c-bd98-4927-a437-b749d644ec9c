!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("jQuery"));else if("function"==typeof define&&define.amd)define(["jQuery"],e);else{var n="object"==typeof exports?e(require("jQuery")):e(t.jQuery);for(var a in n)("object"==typeof exports?exports:t)[a]=n[a]}}(self,(function(__WEBPACK_EXTERNAL_MODULE_jquery__){return function(){var __webpack_modules__={"./libs/jstree/jstree.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var jstree_dist_jstree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jstree/dist/jstree */ "./node_modules/jstree/dist/jstree.js");\n/* harmony import */ var jstree_dist_jstree__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jstree_dist_jstree__WEBPACK_IMPORTED_MODULE_0__);\n\n\n//# sourceURL=webpack://Materialize/./libs/jstree/jstree.js?')},"./node_modules/jstree/dist/jstree.js":function(module,exports,__webpack_require__){eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*globals jQuery, define, module, exports, require, window, document, postMessage */\n(function (factory) {\n\t\"use strict\";\n\tif (true) {\n\t\t!(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(/*! jquery */ \"jquery\")], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t}\n\telse {}\n}(function ($, undefined) {\n\t\"use strict\";\n/*!\n * jsTree 3.3.17\n * http://jstree.com/\n *\n * Copyright (c) 2014 Ivan Bozhanov (http://vakata.com)\n *\n * Licensed same as jquery - under the terms of the MIT License\n *   http://www.opensource.org/licenses/mit-license.php\n */\n/*!\n * if using jslint please allow for the jQuery global and use following options:\n * jslint: loopfunc: true, browser: true, ass: true, bitwise: true, continue: true, nomen: true, plusplus: true, regexp: true, unparam: true, todo: true, white: true\n */\n/*jshint -W083 */\n\n\t// prevent another load? maybe there is a better way?\n\tif($.jstree) {\n\t\treturn;\n\t}\n\n\t/**\n\t * ### jsTree core functionality\n\t */\n\n\t// internal variables\n\tvar instance_counter = 0,\n\t\tccp_node = false,\n\t\tccp_mode = false,\n\t\tccp_inst = false,\n\t\tthemes_loaded = [],\n\t\tsrc = $('script:last').attr('src'),\n\t\tdocument = window.document; // local variable is always faster to access then a global\n\n\tvar setImmediate = window.setImmediate;\n\tvar Promise = window.Promise;\n\tif (!setImmediate && Promise) {\n\t\t// Good enough approximation of setImmediate\n\t\tsetImmediate = function (cb, arg) {\n\t\t\tPromise.resolve(arg).then(cb);\n\t\t};\n\t}\n\n\t/**\n\t * holds all jstree related functions and variables, including the actual class and methods to create, access and manipulate instances.\n\t * @name $.jstree\n\t */\n\t$.jstree = {\n\t\t/**\n\t\t * specifies the jstree version in use\n\t\t * @name $.jstree.version\n\t\t */\n\t\tversion : '3.3.17',\n\t\t/**\n\t\t * holds all the default options used when creating new instances\n\t\t * @name $.jstree.defaults\n\t\t */\n\t\tdefaults : {\n\t\t\t/**\n\t\t\t * configure which plugins will be active on an instance. Should be an array of strings, where each element is a plugin name. The default is `[]`\n\t\t\t * @name $.jstree.defaults.plugins\n\t\t\t */\n\t\t\tplugins : []\n\t\t},\n\t\t/**\n\t\t * stores all loaded jstree plugins (used internally)\n\t\t * @name $.jstree.plugins\n\t\t */\n\t\tplugins : {},\n\t\tpath : src && src.indexOf('/') !== -1 ? src.replace(/\\/[^\\/]+$/,'') : '',\n\t\tidregex : /[\\\\:&!^|()\\[\\]<>@*'+~#\";.,=\\- \\/${}%?`]/g,\n\t\troot : '#'\n\t};\n\t\n\t/**\n\t * creates a jstree instance\n\t * @name $.jstree.create(el [, options])\n\t * @param {DOMElement|jQuery|String} el the element to create the instance on, can be jQuery extended or a selector\n\t * @param {Object} options options for this instance (extends `$.jstree.defaults`)\n\t * @return {jsTree} the new instance\n\t */\n\t$.jstree.create = function (el, options) {\n\t\tvar tmp = new $.jstree.core(++instance_counter),\n\t\t\topt = options;\n\t\toptions = $.extend(true, {}, $.jstree.defaults, options);\n\t\tif(opt && opt.plugins) {\n\t\t\toptions.plugins = opt.plugins;\n\t\t}\n\t\t$.each(options.plugins, function (i, k) {\n\t\t\tif(i !== 'core') {\n\t\t\t\ttmp = tmp.plugin(k, options[k]);\n\t\t\t}\n\t\t});\n\t\t$(el).data('jstree', tmp);\n\t\ttmp.init(el, options);\n\t\treturn tmp;\n\t};\n\t/**\n\t * remove all traces of jstree from the DOM and destroy all instances\n\t * @name $.jstree.destroy()\n\t */\n\t$.jstree.destroy = function () {\n\t\t$('.jstree:jstree').jstree('destroy');\n\t\t$(document).off('.jstree');\n\t};\n\t/**\n\t * the jstree class constructor, used only internally\n\t * @private\n\t * @name $.jstree.core(id)\n\t * @param {Number} id this instance's index\n\t */\n\t$.jstree.core = function (id) {\n\t\tthis._id = id;\n\t\tthis._cnt = 0;\n\t\tthis._wrk = null;\n\t\tthis._data = {\n\t\t\tcore : {\n\t\t\t\tthemes : {\n\t\t\t\t\tname : false,\n\t\t\t\t\tdots : false,\n\t\t\t\t\ticons : false,\n\t\t\t\t\tellipsis : false\n\t\t\t\t},\n\t\t\t\tselected : [],\n\t\t\t\tlast_error : {},\n\t\t\t\tworking : false,\n\t\t\t\tworker_queue : [],\n\t\t\t\tfocused : null\n\t\t\t}\n\t\t};\n\t};\n\t/**\n\t * get a reference to an existing instance\n\t *\n\t * __Examples__\n\t *\n\t *\t// provided a container with an ID of \"tree\", and a nested node with an ID of \"branch\"\n\t *\t// all of there will return the same instance\n\t *\t$.jstree.reference('tree');\n\t *\t$.jstree.reference('#tree');\n\t *\t$.jstree.reference($('#tree'));\n\t *\t$.jstree.reference(document.getElementByID('tree'));\n\t *\t$.jstree.reference('branch');\n\t *\t$.jstree.reference('#branch');\n\t *\t$.jstree.reference($('#branch'));\n\t *\t$.jstree.reference(document.getElementByID('branch'));\n\t *\n\t * @name $.jstree.reference(needle)\n\t * @param {DOMElement|jQuery|String} needle\n\t * @return {jsTree|null} the instance or `null` if not found\n\t */\n\t$.jstree.reference = function (needle) {\n\t\tvar tmp = null,\n\t\t\tobj = null;\n\t\tif(needle && needle.id && (!needle.tagName || !needle.nodeType)) { needle = needle.id; }\n\n\t\tif(!obj || !obj.length) {\n\t\t\ttry { obj = $(needle); } catch (ignore) { }\n\t\t}\n\t\tif(!obj || !obj.length) {\n\t\t\ttry { obj = $('#' + needle.replace($.jstree.idregex,'\\\\$&')); } catch (ignore) { }\n\t\t}\n\t\tif(obj && obj.length && (obj = obj.closest('.jstree')).length && (obj = obj.data('jstree'))) {\n\t\t\ttmp = obj;\n\t\t}\n\t\telse {\n\t\t\t$('.jstree').each(function () {\n\t\t\t\tvar inst = $(this).data('jstree');\n\t\t\t\tif(inst && inst._model.data[needle]) {\n\t\t\t\t\ttmp = inst;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn tmp;\n\t};\n\t/**\n\t * Create an instance, get an instance or invoke a command on a instance.\n\t *\n\t * If there is no instance associated with the current node a new one is created and `arg` is used to extend `$.jstree.defaults` for this new instance. There would be no return value (chaining is not broken).\n\t *\n\t * If there is an existing instance and `arg` is a string the command specified by `arg` is executed on the instance, with any additional arguments passed to the function. If the function returns a value it will be returned (chaining could break depending on function).\n\t *\n\t * If there is an existing instance and `arg` is not a string the instance itself is returned (similar to `$.jstree.reference`).\n\t *\n\t * In any other case - nothing is returned and chaining is not broken.\n\t *\n\t * __Examples__\n\t *\n\t *\t$('#tree1').jstree(); // creates an instance\n\t *\t$('#tree2').jstree({ plugins : [] }); // create an instance with some options\n\t *\t$('#tree1').jstree('open_node', '#branch_1'); // call a method on an existing instance, passing additional arguments\n\t *\t$('#tree2').jstree(); // get an existing instance (or create an instance)\n\t *\t$('#tree2').jstree(true); // get an existing instance (will not create new instance)\n\t *\t$('#branch_1').jstree().select_node('#branch_1'); // get an instance (using a nested element and call a method)\n\t *\n\t * @name $().jstree([arg])\n\t * @param {String|Object} arg\n\t * @return {Mixed}\n\t */\n\t$.fn.jstree = function (arg) {\n\t\t// check for string argument\n\t\tvar is_method\t= (typeof arg === 'string'),\n\t\t\targs\t\t= Array.prototype.slice.call(arguments, 1),\n\t\t\tresult\t\t= null;\n\t\tif(arg === true && !this.length) { return false; }\n\t\tthis.each(function () {\n\t\t\t// get the instance (if there is one) and method (if it exists)\n\t\t\tvar instance = $.jstree.reference(this),\n\t\t\t\tmethod = is_method && instance ? instance[arg] : null;\n\t\t\t// if calling a method, and method is available - execute on the instance\n\t\t\tresult = is_method && method ?\n\t\t\t\tmethod.apply(instance, args) :\n\t\t\t\tnull;\n\t\t\t// if there is no instance and no method is being called - create one\n\t\t\tif(!instance && !is_method && (arg === undefined || $.isPlainObject(arg))) {\n\t\t\t\t$.jstree.create(this, arg);\n\t\t\t}\n\t\t\t// if there is an instance and no method is called - return the instance\n\t\t\tif( (instance && !is_method) || arg === true ) {\n\t\t\t\tresult = instance || false;\n\t\t\t}\n\t\t\t// if there was a method call which returned a result - break and return the value\n\t\t\tif(result !== null && result !== undefined) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t});\n\t\t// if there was a method call with a valid return value - return that, otherwise continue the chain\n\t\treturn result !== null && result !== undefined ?\n\t\t\tresult : this;\n\t};\n\t/**\n\t * used to find elements containing an instance\n\t *\n\t * __Examples__\n\t *\n\t *\t$('div:jstree').each(function () {\n\t *\t\t$(this).jstree('destroy');\n\t *\t});\n\t *\n\t * @name $(':jstree')\n\t * @return {jQuery}\n\t */\n\t$.expr.pseudos.jstree = $.expr.createPseudo(function(search) {\n\t\treturn function(a) {\n\t\t\treturn $(a).hasClass('jstree') &&\n\t\t\t\t$(a).data('jstree') !== undefined;\n\t\t};\n\t});\n\n\t/**\n\t * stores all defaults for the core\n\t * @name $.jstree.defaults.core\n\t */\n\t$.jstree.defaults.core = {\n\t\t/**\n\t\t * data configuration\n\t\t *\n\t\t * If left as `false` the HTML inside the jstree container element is used to populate the tree (that should be an unordered list with list items).\n\t\t *\n\t\t * You can also pass in a HTML string or a JSON array here.\n\t\t *\n\t\t * It is possible to pass in a standard jQuery-like AJAX config and jstree will automatically determine if the response is JSON or HTML and use that to populate the tree.\n\t\t * In addition to the standard jQuery ajax options here you can supply functions for `data` and `url`, the functions will be run in the current instance's scope and a param will be passed indicating which node is being loaded, the return value of those functions will be used.\n\t\t *\n\t\t * The last option is to specify a function, that function will receive the node being loaded as argument and a second param which is a function which should be called with the result.\n\t\t *\n\t\t * __Examples__\n\t\t *\n\t\t *\t// AJAX\n\t\t *\t$('#tree').jstree({\n\t\t *\t\t'core' : {\n\t\t *\t\t\t'data' : {\n\t\t *\t\t\t\t'url' : '/get/children/',\n\t\t *\t\t\t\t'data' : function (node) {\n\t\t *\t\t\t\t\treturn { 'id' : node.id };\n\t\t *\t\t\t\t}\n\t\t *\t\t\t}\n\t\t *\t\t});\n\t\t *\n\t\t *\t// direct data\n\t\t *\t$('#tree').jstree({\n\t\t *\t\t'core' : {\n\t\t *\t\t\t'data' : [\n\t\t *\t\t\t\t'Simple root node',\n\t\t *\t\t\t\t{\n\t\t *\t\t\t\t\t'id' : 'node_2',\n\t\t *\t\t\t\t\t'text' : 'Root node with options',\n\t\t *\t\t\t\t\t'state' : { 'opened' : true, 'selected' : true },\n\t\t *\t\t\t\t\t'children' : [ { 'text' : 'Child 1' }, 'Child 2']\n\t\t *\t\t\t\t}\n\t\t *\t\t\t]\n\t\t *\t\t}\n\t\t *\t});\n\t\t *\n\t\t *\t// function\n\t\t *\t$('#tree').jstree({\n\t\t *\t\t'core' : {\n\t\t *\t\t\t'data' : function (obj, callback) {\n\t\t *\t\t\t\tcallback.call(this, ['Root 1', 'Root 2']);\n\t\t *\t\t\t}\n\t\t *\t\t});\n\t\t *\n\t\t * @name $.jstree.defaults.core.data\n\t\t */\n\t\tdata\t\t\t: false,\n\t\t/**\n\t\t * configure the various strings used throughout the tree\n\t\t *\n\t\t * You can use an object where the key is the string you need to replace and the value is your replacement.\n\t\t * Another option is to specify a function which will be called with an argument of the needed string and should return the replacement.\n\t\t * If left as `false` no replacement is made.\n\t\t *\n\t\t * __Examples__\n\t\t *\n\t\t *\t$('#tree').jstree({\n\t\t *\t\t'core' : {\n\t\t *\t\t\t'strings' : {\n\t\t *\t\t\t\t'Loading ...' : 'Please wait ...'\n\t\t *\t\t\t}\n\t\t *\t\t}\n\t\t *\t});\n\t\t *\n\t\t * @name $.jstree.defaults.core.strings\n\t\t */\n\t\tstrings\t\t\t: false,\n\t\t/**\n\t\t * determines what happens when a user tries to modify the structure of the tree\n\t\t * If left as `false` all operations like create, rename, delete, move or copy are prevented.\n\t\t * You can set this to `true` to allow all interactions or use a function to have better control.\n\t\t *\n\t\t * __Examples__\n\t\t *\n\t\t *\t$('#tree').jstree({\n\t\t *\t\t'core' : {\n\t\t *\t\t\t'check_callback' : function (operation, node, node_parent, node_position, more) {\n\t\t *\t\t\t\t// operation can be 'create_node', 'rename_node', 'delete_node', 'move_node', 'copy_node' or 'edit'\n\t\t *\t\t\t\t// in case of 'rename_node' node_position is filled with the new node name\n\t\t *\t\t\t\treturn operation === 'rename_node' ? true : false;\n\t\t *\t\t\t}\n\t\t *\t\t}\n\t\t *\t});\n\t\t *\n\t\t * @name $.jstree.defaults.core.check_callback\n\t\t */\n\t\tcheck_callback\t: false,\n\t\t/**\n\t\t * a callback called with a single object parameter in the instance's scope when something goes wrong (operation prevented, ajax failed, etc)\n\t\t * @name $.jstree.defaults.core.error\n\t\t */\n\t\terror\t\t\t: $.noop,\n\t\t/**\n\t\t * the open / close animation duration in milliseconds - set this to `false` to disable the animation (default is `200`)\n\t\t * @name $.jstree.defaults.core.animation\n\t\t */\n\t\tanimation\t\t: 200,\n\t\t/**\n\t\t * a boolean indicating if multiple nodes can be selected\n\t\t * @name $.jstree.defaults.core.multiple\n\t\t */\n\t\tmultiple\t\t: true,\n\t\t/**\n\t\t * theme configuration object\n\t\t * @name $.jstree.defaults.core.themes\n\t\t */\n\t\tthemes\t\t\t: {\n\t\t\t/**\n\t\t\t * the name of the theme to use (if left as `false` the default theme is used)\n\t\t\t * @name $.jstree.defaults.core.themes.name\n\t\t\t */\n\t\t\tname\t\t\t: false,\n\t\t\t/**\n\t\t\t * the URL of the theme's CSS file, leave this as `false` if you have manually included the theme CSS (recommended). You can set this to `true` too which will try to autoload the theme.\n\t\t\t * @name $.jstree.defaults.core.themes.url\n\t\t\t */\n\t\t\turl\t\t\t\t: false,\n\t\t\t/**\n\t\t\t * the location of all jstree themes - only used if `url` is set to `true`\n\t\t\t * @name $.jstree.defaults.core.themes.dir\n\t\t\t */\n\t\t\tdir\t\t\t\t: false,\n\t\t\t/**\n\t\t\t * a boolean indicating if connecting dots are shown\n\t\t\t * @name $.jstree.defaults.core.themes.dots\n\t\t\t */\n\t\t\tdots\t\t\t: true,\n\t\t\t/**\n\t\t\t * a boolean indicating if node icons are shown\n\t\t\t * @name $.jstree.defaults.core.themes.icons\n\t\t\t */\n\t\t\ticons\t\t\t: true,\n\t\t\t/**\n\t\t\t * a boolean indicating if node ellipsis should be shown - this only works with a fixed with on the container\n\t\t\t * @name $.jstree.defaults.core.themes.ellipsis\n\t\t\t */\n\t\t\tellipsis\t\t: false,\n\t\t\t/**\n\t\t\t * a boolean indicating if the tree background is striped\n\t\t\t * @name $.jstree.defaults.core.themes.stripes\n\t\t\t */\n\t\t\tstripes\t\t\t: false,\n\t\t\t/**\n\t\t\t * a string (or boolean `false`) specifying the theme variant to use (if the theme supports variants)\n\t\t\t * @name $.jstree.defaults.core.themes.variant\n\t\t\t */\n\t\t\tvariant\t\t\t: false,\n\t\t\t/**\n\t\t\t * a boolean specifying if a reponsive version of the theme should kick in on smaller screens (if the theme supports it). Defaults to `false`.\n\t\t\t * @name $.jstree.defaults.core.themes.responsive\n\t\t\t */\n\t\t\tresponsive\t\t: false\n\t\t},\n\t\t/**\n\t\t * if left as `true` all parents of all selected nodes will be opened once the tree loads (so that all selected nodes are visible to the user)\n\t\t * @name $.jstree.defaults.core.expand_selected_onload\n\t\t */\n\t\texpand_selected_onload : true,\n\t\t/**\n\t\t * if left as `true` web workers will be used to parse incoming JSON data where possible, so that the UI will not be blocked by large requests. Workers are however about 30% slower. Defaults to `true`\n\t\t * @name $.jstree.defaults.core.worker\n\t\t */\n\t\tworker : true,\n\t\t/**\n\t\t * Force node text to plain text (and escape HTML). Defaults to `false`\n\t\t * @name $.jstree.defaults.core.force_text\n\t\t */\n\t\tforce_text : false,\n\t\t/**\n\t\t * Should the node be toggled if the text is double clicked. Defaults to `true`\n\t\t * @name $.jstree.defaults.core.dblclick_toggle\n\t\t */\n\t\tdblclick_toggle : true,\n\t\t/**\n\t\t * Should the loaded nodes be part of the state. Defaults to `false`\n\t\t * @name $.jstree.defaults.core.loaded_state\n\t\t */\n\t\tloaded_state : false,\n\t\t/**\n\t\t * Should the last active node be focused when the tree container is blurred and the focused again. This helps working with screen readers. Defaults to `true`\n\t\t * @name $.jstree.defaults.core.restore_focus\n\t\t */\n\t\trestore_focus : true,\n\t\t/**\n\t\t * Force to compute and set \"aria-setsize\" and \"aria-posinset\" explicitly for each treeitem. \n\t\t * Some browsers may compute incorrect elements position and produce wrong announcements for screen readers. Defaults to `false`\n\t\t * @name $.jstree.defaults.core.compute_elements_positions\n\t\t */\n\t\tcompute_elements_positions : false,\n\t\t/**\n\t\t * Default keyboard shortcuts (an object where each key is the button name or combo - like 'enter', 'ctrl-space', 'p', etc and the value is the function to execute in the instance's scope)\n\t\t * @name $.jstree.defaults.core.keyboard\n\t\t */\n\t\tkeyboard : {\n\t\t\t'ctrl-space': function (e) {\n\t\t\t\t// aria defines space only with Ctrl\n\t\t\t\te.type = \"click\";\n\t\t\t\t$(e.currentTarget).trigger(e);\n\t\t\t},\n\t\t\t'enter': function (e) {\n\t\t\t\t// enter\n\t\t\t\te.type = \"click\";\n\t\t\t\t$(e.currentTarget).trigger(e);\n\t\t\t},\n\t\t\t'left': function (e) {\n\t\t\t\t// left\n\t\t\t\te.preventDefault();\n\t\t\t\tif(this.is_open(e.currentTarget)) {\n\t\t\t\t\tthis.close_node(e.currentTarget);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tvar o = this.get_parent(e.currentTarget);\n\t\t\t\t\tif(o && o.id !== $.jstree.root) { this.get_node(o, true).children('.jstree-anchor').trigger('focus'); }\n\t\t\t\t}\n\t\t\t},\n\t\t\t'up': function (e) {\n\t\t\t\t// up\n\t\t\t\te.preventDefault();\n\t\t\t\tvar o = this.get_prev_dom(e.currentTarget);\n\t\t\t\tif(o && o.length) { o.children('.jstree-anchor').trigger('focus'); }\n\t\t\t},\n\t\t\t'right': function (e) {\n\t\t\t\t// right\n\t\t\t\te.preventDefault();\n\t\t\t\tif(this.is_closed(e.currentTarget)) {\n\t\t\t\t\tthis.open_node(e.currentTarget, function (o) { this.get_node(o, true).children('.jstree-anchor').trigger('focus'); });\n\t\t\t\t}\n\t\t\t\telse if (this.is_open(e.currentTarget)) {\n\t\t\t\t\tvar o = this.get_node(e.currentTarget, true).children('.jstree-children')[0];\n\t\t\t\t\tif(o) { $(this._firstChild(o)).children('.jstree-anchor').trigger('focus'); }\n\t\t\t\t}\n\t\t\t},\n\t\t\t'down': function (e) {\n\t\t\t\t// down\n\t\t\t\te.preventDefault();\n\t\t\t\tvar o = this.get_next_dom(e.currentTarget);\n\t\t\t\tif(o && o.length) { o.children('.jstree-anchor').trigger('focus'); }\n\t\t\t},\n\t\t\t'*': function (e) {\n\t\t\t\t// aria defines * on numpad as open_all - not very common\n\t\t\t\tthis.open_all();\n\t\t\t},\n\t\t\t'home': function (e) {\n\t\t\t\t// home\n\t\t\t\te.preventDefault();\n\t\t\t\tvar o = this._firstChild(this.get_container_ul()[0]);\n\t\t\t\tif(o) { $(o).children('.jstree-anchor').filter(':visible').trigger('focus'); }\n\t\t\t},\n\t\t\t'end': function (e) {\n\t\t\t\t// end\n\t\t\t\te.preventDefault();\n\t\t\t\tthis.element.find('.jstree-anchor').filter(':visible').last().trigger('focus');\n\t\t\t},\n\t\t\t'f2': function (e) {\n\t\t\t\t// f2 - safe to include - if check_callback is false it will fail\n\t\t\t\te.preventDefault();\n\t\t\t\tthis.edit(e.currentTarget);\n\t\t\t}\n\t\t},\n        /**\n\t\t * Should reselecting an already selected node trigger the select and changed callbacks\n\t\t * @name $.jstree.defaults.core.allow_reselect\n\t\t */\n        allow_reselect : false\n\t};\n\t$.jstree.core.prototype = {\n\t\t/**\n\t\t * used to decorate an instance with a plugin. Used internally.\n\t\t * @private\n\t\t * @name plugin(deco [, opts])\n\t\t * @param  {String} deco the plugin to decorate with\n\t\t * @param  {Object} opts options for the plugin\n\t\t * @return {jsTree}\n\t\t */\n\t\tplugin : function (deco, opts) {\n\t\t\tvar Child = $.jstree.plugins[deco];\n\t\t\tif(Child) {\n\t\t\t\tthis._data[deco] = {};\n\t\t\t\tChild.prototype = this;\n\t\t\t\treturn new Child(opts, this);\n\t\t\t}\n\t\t\treturn this;\n\t\t},\n\t\t/**\n\t\t * initialize the instance. Used internally.\n\t\t * @private\n\t\t * @name init(el, optons)\n\t\t * @param {DOMElement|jQuery|String} el the element we are transforming\n\t\t * @param {Object} options options for this instance\n\t\t * @trigger init.jstree, loading.jstree, loaded.jstree, ready.jstree, changed.jstree\n\t\t */\n\t\tinit : function (el, options) {\n\t\t\tthis._model = {\n\t\t\t\tdata : {},\n\t\t\t\tchanged : [],\n\t\t\t\tforce_full_redraw : false,\n\t\t\t\tredraw_timeout : false,\n\t\t\t\tdefault_state : {\n\t\t\t\t\tloaded : true,\n\t\t\t\t\topened : false,\n\t\t\t\t\tselected : false,\n\t\t\t\t\tdisabled : false\n\t\t\t\t}\n\t\t\t};\n\t\t\tthis._model.data[$.jstree.root] = {\n\t\t\t\tid : $.jstree.root,\n\t\t\t\tparent : null,\n\t\t\t\tparents : [],\n\t\t\t\tchildren : [],\n\t\t\t\tchildren_d : [],\n\t\t\t\tstate : { loaded : false }\n\t\t\t};\n\n\t\t\tthis.element = $(el).addClass('jstree jstree-' + this._id);\n\t\t\tthis.settings = options;\n\n\t\t\tthis._data.core.ready = false;\n\t\t\tthis._data.core.loaded = false;\n\t\t\tthis._data.core.rtl = (this.element.css(\"direction\") === \"rtl\");\n\t\t\tthis.element[this._data.core.rtl ? 'addClass' : 'removeClass'](\"jstree-rtl\");\n\t\t\tthis.element.attr('role','tree');\n\t\t\tif(this.settings.core.multiple) {\n\t\t\t\tthis.element.attr('aria-multiselectable', true);\n\t\t\t}\n\t\t\tif(!this.element.attr('tabindex')) {\n\t\t\t\tthis.element.attr('tabindex','0');\n\t\t\t}\n\n\t\t\tthis.bind();\n\t\t\t/**\n\t\t\t * triggered after all events are bound\n\t\t\t * @event\n\t\t\t * @name init.jstree\n\t\t\t */\n\t\t\tthis.trigger(\"init\");\n\n\t\t\tthis._data.core.original_container_html = this.element.find(\" > ul > li\").clone(true);\n\t\t\tthis._data.core.original_container_html\n\t\t\t\t.find(\"li\").addBack()\n\t\t\t\t.contents().filter(function() {\n\t\t\t\t\treturn this.nodeType === 3 && (!this.nodeValue || /^\\s+$/.test(this.nodeValue));\n\t\t\t\t})\n\t\t\t\t.remove();\n\t\t\tthis.element.html(\"<\"+\"ul class='jstree-container-ul jstree-children' role='group'><\"+\"li id='j\"+this._id+\"_loading' class='jstree-initial-node jstree-loading jstree-leaf jstree-last' role='none'><i class='jstree-icon jstree-ocl'></i><\"+\"a class='jstree-anchor' role='treeitem' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>\" + this.get_string(\"Loading ...\") + \"</a></li></ul>\");\n\t\t\tthis.element.attr('aria-activedescendant','j' + this._id + '_loading');\n\t\t\tthis._data.core.li_height = this.get_container_ul().children(\"li\").first().outerHeight() || 24;\n\t\t\tthis._data.core.node = this._create_prototype_node();\n\t\t\t/**\n\t\t\t * triggered after the loading text is shown and before loading starts\n\t\t\t * @event\n\t\t\t * @name loading.jstree\n\t\t\t */\n\t\t\tthis.trigger(\"loading\");\n\t\t\tthis.load_node($.jstree.root);\n\t\t},\n\t\t/**\n\t\t * destroy an instance\n\t\t * @name destroy()\n\t\t * @param  {Boolean} keep_html if not set to `true` the container will be emptied, otherwise the current DOM elements will be kept intact\n\t\t */\n\t\tdestroy : function (keep_html) {\n\t\t\t/**\n\t\t\t * triggered before the tree is destroyed\n\t\t\t * @event\n\t\t\t * @name destroy.jstree\n\t\t\t */\n\t\t\tthis.trigger(\"destroy\");\n\t\t\tif(this._wrk) {\n\t\t\t\ttry {\n\t\t\t\t\twindow.URL.revokeObjectURL(this._wrk);\n\t\t\t\t\tthis._wrk = null;\n\t\t\t\t}\n\t\t\t\tcatch (ignore) { }\n\t\t\t}\n\t\t\tif(!keep_html) { this.element.empty(); }\n\t\t\tthis.teardown();\n\t\t},\n\t\t/**\n\t\t * Create a prototype node\n\t\t * @name _create_prototype_node()\n\t\t * @return {DOMElement}\n\t\t */\n\t\t_create_prototype_node : function () {\n\t\t\tvar _node = document.createElement('LI'), _temp1, _temp2;\n\t\t\t_node.setAttribute('role', 'none');\n\t\t\t_temp1 = document.createElement('I');\n\t\t\t_temp1.className = 'jstree-icon jstree-ocl';\n\t\t\t_temp1.setAttribute('role', 'presentation');\n\t\t\t_node.appendChild(_temp1);\n\t\t\t_temp1 = document.createElement('A');\n\t\t\t_temp1.className = 'jstree-anchor';\n\t\t\t_temp1.setAttribute('href','#');\n\t\t\t_temp1.setAttribute('tabindex','-1');\n\t\t\t_temp1.setAttribute('role', 'treeitem');\n\t\t\t_temp2 = document.createElement('I');\n\t\t\t_temp2.className = 'jstree-icon jstree-themeicon';\n\t\t\t_temp2.setAttribute('role', 'presentation');\n\t\t\t_temp1.appendChild(_temp2);\n\t\t\t_node.appendChild(_temp1);\n\t\t\t_temp1 = _temp2 = null;\n\n\t\t\treturn _node;\n\t\t},\n\t\t_kbevent_to_func : function (e) {\n\t\t\tvar keys = {\n\t\t\t\t8: \"Backspace\", 9: \"Tab\", 13: \"Enter\", 19: \"Pause\", 27: \"Esc\",\n\t\t\t\t32: \"Space\", 33: \"PageUp\", 34: \"PageDown\", 35: \"End\", 36: \"Home\",\n\t\t\t\t37: \"Left\", 38: \"Up\", 39: \"Right\", 40: \"Down\", 44: \"Print\", 45: \"Insert\",\n\t\t\t\t46: \"Delete\", 96: \"Numpad0\", 97: \"Numpad1\", 98: \"Numpad2\", 99 : \"Numpad3\",\n\t\t\t\t100: \"Numpad4\", 101: \"Numpad5\", 102: \"Numpad6\", 103: \"Numpad7\",\n\t\t\t\t104: \"Numpad8\", 105: \"Numpad9\", '-13': \"NumpadEnter\", 112: \"F1\",\n\t\t\t\t113: \"F2\", 114: \"F3\", 115: \"F4\", 116: \"F5\", 117: \"F6\", 118: \"F7\",\n\t\t\t\t119: \"F8\", 120: \"F9\", 121: \"F10\", 122: \"F11\", 123: \"F12\", 144: \"Numlock\",\n\t\t\t\t145: \"Scrolllock\", 16: 'Shift', 17: 'Ctrl', 18: 'Alt',\n\t\t\t\t48: '0',  49: '1',  50: '2',  51: '3',  52: '4', 53:  '5',\n\t\t\t\t54: '6',  55: '7',  56: '8',  57: '9',  59: ';',  61: '=', 65:  'a',\n\t\t\t\t66: 'b',  67: 'c',  68: 'd',  69: 'e',  70: 'f',  71: 'g', 72:  'h',\n\t\t\t\t73: 'i',  74: 'j',  75: 'k',  76: 'l',  77: 'm',  78: 'n', 79:  'o',\n\t\t\t\t80: 'p',  81: 'q',  82: 'r',  83: 's',  84: 't',  85: 'u', 86:  'v',\n\t\t\t\t87: 'w',  88: 'x',  89: 'y',  90: 'z', 107: '+', 109: '-', 110: '.',\n\t\t\t\t186: ';', 187: '=', 188: ',', 189: '-', 190: '.', 191: '/', 192: '`',\n\t\t\t\t219: '[', 220: '\\\\',221: ']', 222: \"'\", 111: '/', 106: '*', 173: '-'\n\t\t\t};\n\t\t\tvar parts = [];\n\t\t\tif (e.ctrlKey) { parts.push('ctrl'); }\n\t\t\tif (e.altKey) { parts.push('alt'); }\n            if (e.shiftKey) { parts.push('shift'); }\n\t\t\tparts.push(keys[e.which] ? keys[e.which].toLowerCase() : e.which);\n            parts = parts.sort().join('-').toLowerCase();\n            if (parts === 'shift-shift' || parts === 'ctrl-ctrl' || parts === 'alt-alt') {\n                return null;\n            }\n\n\t\t\tvar kb = this.settings.core.keyboard, i, tmp;\n\t\t\tfor (i in kb) {\n\t\t\t\tif (kb.hasOwnProperty(i)) {\n\t\t\t\t\ttmp = i;\n\t\t\t\t\tif (tmp !== '-' && tmp !== '+') {\n\t\t\t\t\t\ttmp = tmp.replace('--', '-MINUS').replace('+-', '-MINUS').replace('++', '-PLUS').replace('-+', '-PLUS');\n\t\t\t\t\t\ttmp = tmp.split(/-|\\+/).sort().join('-').replace('MINUS', '-').replace('PLUS', '+').toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\tif (tmp === parts) {\n\t\t\t\t\t\treturn kb[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\t\t/**\n\t\t * part of the destroying of an instance. Used internally.\n\t\t * @private\n\t\t * @name teardown()\n\t\t */\n\t\tteardown : function () {\n\t\t\tthis.unbind();\n\t\t\tthis.element\n\t\t\t\t.removeClass('jstree')\n\t\t\t\t.removeData('jstree')\n\t\t\t\t.find(\"[class^='jstree']\")\n\t\t\t\t\t.addBack()\n\t\t\t\t\t.attr(\"class\", function () { return this.className.replace(/jstree[^ ]*|$/ig,''); });\n\t\t\tthis.element = null;\n\t\t},\n\t\t/**\n\t\t * bind all events. Used internally.\n\t\t * @private\n\t\t * @name bind()\n\t\t */\n\t\tbind : function () {\n\t\t\tvar word = '',\n\t\t\t\ttout = null,\n\t\t\t\twas_click = 0;\n\t\t\tthis.element\n\t\t\t\t.on(\"dblclick.jstree\", function (e) {\n\t\t\t\t\t\tif(e.target.tagName && e.target.tagName.toLowerCase() === \"input\") { return true; }\n\t\t\t\t\t\tif(document.selection && document.selection.empty) {\n\t\t\t\t\t\t\tdocument.selection.empty();\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif(window.getSelection) {\n\t\t\t\t\t\t\t\tvar sel = window.getSelection();\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tsel.removeAllRanges();\n\t\t\t\t\t\t\t\t\tsel.collapse();\n\t\t\t\t\t\t\t\t} catch (ignore) { }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t.on(\"mousedown.jstree\", function (e) {\n\t\t\t\t\t\tif(e.target === this.element[0]) {\n\t\t\t\t\t\t\te.preventDefault(); // prevent losing focus when clicking scroll arrows (FF, Chrome)\n\t\t\t\t\t\t\twas_click = +(new Date()); // ie does not allow to prevent losing focus\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"mousedown.jstree\", \".jstree-ocl\", function (e) {\n\t\t\t\t\t\te.preventDefault(); // prevent any node inside from losing focus when clicking the open/close icon\n\t\t\t\t\t})\n\t\t\t\t.on(\"click.jstree\", \".jstree-ocl\", function (e) {\n\t\t\t\t\t\tthis.toggle_node(e.target);\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"dblclick.jstree\", \".jstree-anchor\", function (e) {\n\t\t\t\t\t\tif(e.target.tagName && e.target.tagName.toLowerCase() === \"input\") { return true; }\n\t\t\t\t\t\tif(this.settings.core.dblclick_toggle) {\n\t\t\t\t\t\t\tthis.toggle_node(e.target);\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"click.jstree\", \".jstree-anchor\", function (e) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tif(e.currentTarget !== document.activeElement) { $(e.currentTarget).trigger('focus'); }\n\t\t\t\t\t\tthis.activate_node(e.currentTarget, e);\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on('keydown.jstree', '.jstree-anchor', function (e) {\n\t\t\t\t\t\tif(e.target.tagName && e.target.tagName.toLowerCase() === \"input\") { return true; }\n\t\t\t\t\t\tif(this._data.core.rtl) {\n\t\t\t\t\t\t\tif(e.which === 37) { e.which = 39; }\n\t\t\t\t\t\t\telse if(e.which === 39) { e.which = 37; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar f = this._kbevent_to_func(e);\n\t\t\t\t\t\tif (f) {\n\t\t\t\t\t\t\tvar r = f.call(this, e);\n\t\t\t\t\t\t\tif (r === false || r === true) {\n\t\t\t\t\t\t\t\treturn r;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"load_node.jstree\", function (e, data) {\n\t\t\t\t\t\tif(data.status) {\n\t\t\t\t\t\t\tif(data.node.id === $.jstree.root && !this._data.core.loaded) {\n\t\t\t\t\t\t\t\tthis._data.core.loaded = true;\n\t\t\t\t\t\t\t\tif(this._firstChild(this.get_container_ul()[0])) {\n\t\t\t\t\t\t\t\t\tthis.element.attr('aria-activedescendant',this._firstChild(this.get_container_ul()[0]).id);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t/**\n\t\t\t\t\t\t\t\t * triggered after the root node is loaded for the first time\n\t\t\t\t\t\t\t\t * @event\n\t\t\t\t\t\t\t\t * @name loaded.jstree\n\t\t\t\t\t\t\t\t */\n\t\t\t\t\t\t\t\tthis.trigger(\"loaded\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!this._data.core.ready) {\n\t\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\t\tif(this.element && !this.get_container_ul().find('.jstree-loading').length) {\n\t\t\t\t\t\t\t\t\t\tthis._data.core.ready = true;\n\t\t\t\t\t\t\t\t\t\tif(this._data.core.selected.length) {\n\t\t\t\t\t\t\t\t\t\t\tif(this.settings.core.expand_selected_onload) {\n\t\t\t\t\t\t\t\t\t\t\t\tvar tmp = [], i, j;\n\t\t\t\t\t\t\t\t\t\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\t\t\t\ttmp = tmp.concat(this._model.data[this._data.core.selected[i]].parents);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\ttmp = $.vakata.array_unique(tmp);\n\t\t\t\t\t\t\t\t\t\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.open_node(tmp[i], false, 0);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tthis.trigger('changed', { 'action' : 'ready', 'selected' : this._data.core.selected });\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t/**\n\t\t\t\t\t\t\t\t\t\t * triggered after all nodes are finished loading\n\t\t\t\t\t\t\t\t\t\t * @event\n\t\t\t\t\t\t\t\t\t\t * @name ready.jstree\n\t\t\t\t\t\t\t\t\t\t */\n\t\t\t\t\t\t\t\t\t\tthis.trigger(\"ready\");\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}.bind(this), 0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t// quick searching when the tree is focused\n\t\t\t\t.on('keypress.jstree', function (e) {\n\t\t\t\t\t\tif(e.target.tagName && e.target.tagName.toLowerCase() === \"input\") { return true; }\n\t\t\t\t\t\tif(tout) { clearTimeout(tout); }\n\t\t\t\t\t\ttout = setTimeout(function () {\n\t\t\t\t\t\t\tword = '';\n\t\t\t\t\t\t}, 500);\n\n\t\t\t\t\t\tvar chr = String.fromCharCode(e.which).toLowerCase(),\n\t\t\t\t\t\t\tcol = this.element.find('.jstree-anchor').filter(':visible'),\n\t\t\t\t\t\t\tind = col.index(document.activeElement) || 0,\n\t\t\t\t\t\t\tend = false;\n\t\t\t\t\t\tword += chr;\n\n\t\t\t\t\t\t// match for whole word from current node down (including the current node)\n\t\t\t\t\t\tif(word.length > 1) {\n\t\t\t\t\t\t\tcol.slice(ind).each(function (i, v) {\n\t\t\t\t\t\t\t\tif($(v).text().toLowerCase().indexOf(word) === 0) {\n\t\t\t\t\t\t\t\t\t$(v).trigger('focus');\n\t\t\t\t\t\t\t\t\tend = true;\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\t\t\tif(end) { return; }\n\n\t\t\t\t\t\t\t// match for whole word from the beginning of the tree\n\t\t\t\t\t\t\tcol.slice(0, ind).each(function (i, v) {\n\t\t\t\t\t\t\t\tif($(v).text().toLowerCase().indexOf(word) === 0) {\n\t\t\t\t\t\t\t\t\t$(v).trigger('focus');\n\t\t\t\t\t\t\t\t\tend = true;\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\t\t\tif(end) { return; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// list nodes that start with that letter (only if word consists of a single char)\n\t\t\t\t\t\tif(new RegExp('^' + chr.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&') + '+$').test(word)) {\n\t\t\t\t\t\t\t// search for the next node starting with that letter\n\t\t\t\t\t\t\tcol.slice(ind + 1).each(function (i, v) {\n\t\t\t\t\t\t\t\tif($(v).text().toLowerCase().charAt(0) === chr) {\n\t\t\t\t\t\t\t\t\t$(v).trigger('focus');\n\t\t\t\t\t\t\t\t\tend = true;\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\t\t\tif(end) { return; }\n\n\t\t\t\t\t\t\t// search from the beginning\n\t\t\t\t\t\t\tcol.slice(0, ind + 1).each(function (i, v) {\n\t\t\t\t\t\t\t\tif($(v).text().toLowerCase().charAt(0) === chr) {\n\t\t\t\t\t\t\t\t\t$(v).trigger('focus');\n\t\t\t\t\t\t\t\t\tend = true;\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\t\t\tif(end) { return; }\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t// THEME RELATED\n\t\t\t\t.on(\"init.jstree\", function () {\n\t\t\t\t\t\tvar s = this.settings.core.themes;\n\t\t\t\t\t\tthis._data.core.themes.dots\t\t\t= s.dots;\n\t\t\t\t\t\tthis._data.core.themes.stripes\t\t= s.stripes;\n\t\t\t\t\t\tthis._data.core.themes.icons\t\t= s.icons;\n\t\t\t\t\t\tthis._data.core.themes.ellipsis\t\t= s.ellipsis;\n\t\t\t\t\t\tthis.set_theme(s.name || \"default\", s.url);\n\t\t\t\t\t\tthis.set_theme_variant(s.variant);\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"loading.jstree\", function () {\n\t\t\t\t\t\tthis[ this._data.core.themes.dots ? \"show_dots\" : \"hide_dots\" ]();\n\t\t\t\t\t\tthis[ this._data.core.themes.icons ? \"show_icons\" : \"hide_icons\" ]();\n\t\t\t\t\t\tthis[ this._data.core.themes.stripes ? \"show_stripes\" : \"hide_stripes\" ]();\n\t\t\t\t\t\tthis[ this._data.core.themes.ellipsis ? \"show_ellipsis\" : \"hide_ellipsis\" ]();\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on('blur.jstree', '.jstree-anchor', function (e) {\n\t\t\t\t\t\tthis._data.core.focused = null;\n\t\t\t\t\t\t$(e.currentTarget).filter('.jstree-hovered').trigger('mouseleave');\n\t\t\t\t\t\tthis.element.attr('tabindex', '0');\n\t\t\t\t\t\t$(e.currentTarget).attr('tabindex', '-1');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on('focus.jstree', '.jstree-anchor', function (e) {\n\t\t\t\t\t\tvar tmp = this.get_node(e.currentTarget);\n\t\t\t\t\t\tif(tmp && (tmp.id || tmp.id === 0)) {\n\t\t\t\t\t\t\tthis._data.core.focused = tmp.id;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.element.find('.jstree-hovered').not(e.currentTarget).trigger('mouseleave');\n\t\t\t\t\t\t$(e.currentTarget).trigger('mouseenter');\n\t\t\t\t\t\tthis.element.attr('tabindex', '-1');\n\t\t\t\t\t\t$(e.currentTarget).attr('tabindex', '0');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on('focus.jstree', function () {\n\t\t\t\t\t\tif(+(new Date()) - was_click > 500 && !this._data.core.focused && this.settings.core.restore_focus) {\n\t\t\t\t\t\t\twas_click = 0;\n\t\t\t\t\t\t\tvar act = this.get_node(this.element.attr('aria-activedescendant'), true);\n\t\t\t\t\t\t\tif(act) {\n\t\t\t\t\t\t\t\tact.find('> .jstree-anchor').trigger('focus');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on('mouseenter.jstree', '.jstree-anchor', function (e) {\n\t\t\t\t\t\tthis.hover_node(e.currentTarget);\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on('mouseleave.jstree', '.jstree-anchor', function (e) {\n\t\t\t\t\t\tthis.dehover_node(e.currentTarget);\n\t\t\t\t\t}.bind(this));\n\t\t},\n\t\t/**\n\t\t * part of the destroying of an instance. Used internally.\n\t\t * @private\n\t\t * @name unbind()\n\t\t */\n\t\tunbind : function () {\n\t\t\tthis.element.off('.jstree');\n\t\t\t$(document).off('.jstree-' + this._id);\n\t\t},\n\t\t/**\n\t\t * trigger an event. Used internally.\n\t\t * @private\n\t\t * @name trigger(ev [, data])\n\t\t * @param  {String} ev the name of the event to trigger\n\t\t * @param  {Object} data additional data to pass with the event\n\t\t */\n\t\ttrigger : function (ev, data) {\n\t\t\tif(!data) {\n\t\t\t\tdata = {};\n\t\t\t}\n\t\t\tdata.instance = this;\n\t\t\tthis.element.triggerHandler(ev.replace('.jstree','') + '.jstree', data);\n\t\t},\n\t\t/**\n\t\t * returns the jQuery extended instance container\n\t\t * @name get_container()\n\t\t * @return {jQuery}\n\t\t */\n\t\tget_container : function () {\n\t\t\treturn this.element;\n\t\t},\n\t\t/**\n\t\t * returns the jQuery extended main UL node inside the instance container. Used internally.\n\t\t * @private\n\t\t * @name get_container_ul()\n\t\t * @return {jQuery}\n\t\t */\n\t\tget_container_ul : function () {\n\t\t\treturn this.element.children(\".jstree-children\").first();\n\t\t},\n\t\t/**\n\t\t * gets string replacements (localization). Used internally.\n\t\t * @private\n\t\t * @name get_string(key)\n\t\t * @param  {String} key\n\t\t * @return {String}\n\t\t */\n\t\tget_string : function (key) {\n\t\t\tvar a = this.settings.core.strings;\n\t\t\tif($.vakata.is_function(a)) { return a.call(this, key); }\n\t\t\tif(a && a[key]) { return a[key]; }\n\t\t\treturn key;\n\t\t},\n\t\t/**\n\t\t * gets the first child of a DOM node. Used internally.\n\t\t * @private\n\t\t * @name _firstChild(dom)\n\t\t * @param  {DOMElement} dom\n\t\t * @return {DOMElement}\n\t\t */\n\t\t_firstChild : function (dom) {\n\t\t\tdom = dom ? dom.firstChild : null;\n\t\t\twhile(dom !== null && dom.nodeType !== 1) {\n\t\t\t\tdom = dom.nextSibling;\n\t\t\t}\n\t\t\treturn dom;\n\t\t},\n\t\t/**\n\t\t * gets the next sibling of a DOM node. Used internally.\n\t\t * @private\n\t\t * @name _nextSibling(dom)\n\t\t * @param  {DOMElement} dom\n\t\t * @return {DOMElement}\n\t\t */\n\t\t_nextSibling : function (dom) {\n\t\t\tdom = dom ? dom.nextSibling : null;\n\t\t\twhile(dom !== null && dom.nodeType !== 1) {\n\t\t\t\tdom = dom.nextSibling;\n\t\t\t}\n\t\t\treturn dom;\n\t\t},\n\t\t/**\n\t\t * gets the previous sibling of a DOM node. Used internally.\n\t\t * @private\n\t\t * @name _previousSibling(dom)\n\t\t * @param  {DOMElement} dom\n\t\t * @return {DOMElement}\n\t\t */\n\t\t_previousSibling : function (dom) {\n\t\t\tdom = dom ? dom.previousSibling : null;\n\t\t\twhile(dom !== null && dom.nodeType !== 1) {\n\t\t\t\tdom = dom.previousSibling;\n\t\t\t}\n\t\t\treturn dom;\n\t\t},\n\t\t/**\n\t\t * get the JSON representation of a node (or the actual jQuery extended DOM node) by using any input (child DOM element, ID string, selector, etc)\n\t\t * @name get_node(obj [, as_dom])\n\t\t * @param  {mixed} obj\n\t\t * @param  {Boolean} as_dom\n\t\t * @return {Object|jQuery}\n\t\t */\n\t\tget_node : function (obj, as_dom) {\n\t\t\tif(obj && (obj.id || obj.id === 0)) {\n\t\t\t\tobj = obj.id;\n\t\t\t}\n\t\t\tif (obj instanceof $ && obj.length && obj[0].id) {\n\t\t\t\tobj = obj[0].id;\n\t\t\t}\n\t\t\tvar dom;\n\t\t\ttry {\n\t\t\t\tif(this._model.data[obj]) {\n\t\t\t\t\tobj = this._model.data[obj];\n\t\t\t\t}\n\t\t\t\telse if(typeof obj === \"string\" && this._model.data[obj.replace(/^#/, '')]) {\n\t\t\t\t\tobj = this._model.data[obj.replace(/^#/, '')];\n\t\t\t\t}\n\t\t\t\telse if(typeof obj === \"string\" && (dom = $('#' + obj.replace($.jstree.idregex,'\\\\$&'), this.element)).length && this._model.data[dom.closest('.jstree-node').attr('id')]) {\n\t\t\t\t\tobj = this._model.data[dom.closest('.jstree-node').attr('id')];\n\t\t\t\t}\n\t\t\t\telse if((dom = this.element.find(obj)).length && this._model.data[dom.closest('.jstree-node').attr('id')]) {\n\t\t\t\t\tobj = this._model.data[dom.closest('.jstree-node').attr('id')];\n\t\t\t\t}\n\t\t\t\telse if((dom = this.element.find(obj)).length && dom.hasClass('jstree')) {\n\t\t\t\t\tobj = this._model.data[$.jstree.root];\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tif(as_dom) {\n\t\t\t\t\tobj = obj.id === $.jstree.root ? this.element : $('#' + obj.id.replace($.jstree.idregex,'\\\\$&'), this.element);\n\t\t\t\t}\n\t\t\t\treturn obj;\n\t\t\t} catch (ex) { return false; }\n\t\t},\n\t\t/**\n\t\t * get the path to a node, either consisting of node texts, or of node IDs, optionally glued together (otherwise an array)\n\t\t * @name get_path(obj [, glue, ids])\n\t\t * @param  {mixed} obj the node\n\t\t * @param  {String} glue if you want the path as a string - pass the glue here (for example '/'), if a falsy value is supplied here, an array is returned\n\t\t * @param  {Boolean} ids if set to true build the path using ID, otherwise node text is used\n\t\t * @return {mixed}\n\t\t */\n\t\tget_path : function (obj, glue, ids) {\n\t\t\tobj = obj.parents ? obj : this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root || !obj.parents) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tvar i, j, p = [];\n\t\t\tp.push(ids ? obj.id : obj.text);\n\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\tp.push(ids ? obj.parents[i] : this.get_text(obj.parents[i]));\n\t\t\t}\n\t\t\tp = p.reverse().slice(1);\n\t\t\treturn glue ? p.join(glue) : p;\n\t\t},\n\t\t/**\n\t\t * get the next visible node that is below the `obj` node. If `strict` is set to `true` only sibling nodes are returned.\n\t\t * @name get_next_dom(obj [, strict])\n\t\t * @param  {mixed} obj\n\t\t * @param  {Boolean} strict\n\t\t * @return {jQuery}\n\t\t */\n\t\tget_next_dom : function (obj, strict) {\n\t\t\tvar tmp;\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(obj[0] === this.element[0]) {\n\t\t\t\ttmp = this._firstChild(this.get_container_ul()[0]);\n\t\t\t\twhile (tmp && tmp.offsetHeight === 0) {\n\t\t\t\t\ttmp = this._nextSibling(tmp);\n\t\t\t\t}\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(strict) {\n\t\t\t\ttmp = obj[0];\n\t\t\t\tdo {\n\t\t\t\t\ttmp = this._nextSibling(tmp);\n\t\t\t\t} while (tmp && tmp.offsetHeight === 0);\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif(obj.hasClass(\"jstree-open\")) {\n\t\t\t\ttmp = this._firstChild(obj.children('.jstree-children')[0]);\n\t\t\t\twhile (tmp && tmp.offsetHeight === 0) {\n\t\t\t\t\ttmp = this._nextSibling(tmp);\n\t\t\t\t}\n\t\t\t\tif(tmp !== null) {\n\t\t\t\t\treturn $(tmp);\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = obj[0];\n\t\t\tdo {\n\t\t\t\ttmp = this._nextSibling(tmp);\n\t\t\t} while (tmp && tmp.offsetHeight === 0);\n\t\t\tif(tmp !== null) {\n\t\t\t\treturn $(tmp);\n\t\t\t}\n\t\t\treturn obj.parentsUntil(\".jstree\",\".jstree-node\").nextAll(\".jstree-node:visible\").first();\n\t\t},\n\t\t/**\n\t\t * get the previous visible node that is above the `obj` node. If `strict` is set to `true` only sibling nodes are returned.\n\t\t * @name get_prev_dom(obj [, strict])\n\t\t * @param  {mixed} obj\n\t\t * @param  {Boolean} strict\n\t\t * @return {jQuery}\n\t\t */\n\t\tget_prev_dom : function (obj, strict) {\n\t\t\tvar tmp;\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(obj[0] === this.element[0]) {\n\t\t\t\ttmp = this.get_container_ul()[0].lastChild;\n\t\t\t\twhile (tmp && tmp.offsetHeight === 0) {\n\t\t\t\t\ttmp = this._previousSibling(tmp);\n\t\t\t\t}\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(strict) {\n\t\t\t\ttmp = obj[0];\n\t\t\t\tdo {\n\t\t\t\t\ttmp = this._previousSibling(tmp);\n\t\t\t\t} while (tmp && tmp.offsetHeight === 0);\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\ttmp = obj[0];\n\t\t\tdo {\n\t\t\t\ttmp = this._previousSibling(tmp);\n\t\t\t} while (tmp && tmp.offsetHeight === 0);\n\t\t\tif(tmp !== null) {\n\t\t\t\tobj = $(tmp);\n\t\t\t\twhile(obj.hasClass(\"jstree-open\")) {\n\t\t\t\t\tobj = obj.children(\".jstree-children\").first().children(\".jstree-node:visible:last\");\n\t\t\t\t}\n\t\t\t\treturn obj;\n\t\t\t}\n\t\t\ttmp = obj[0].parentNode.parentNode;\n\t\t\treturn tmp && tmp.className && tmp.className.indexOf('jstree-node') !== -1 ? $(tmp) : false;\n\t\t},\n\t\t/**\n\t\t * get the parent ID of a node\n\t\t * @name get_parent(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {String}\n\t\t */\n\t\tget_parent : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn obj.parent;\n\t\t},\n\t\t/**\n\t\t * get a jQuery collection of all the children of a node (node must be rendered), returns false on error\n\t\t * @name get_children_dom(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {jQuery}\n\t\t */\n\t\tget_children_dom : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(obj[0] === this.element[0]) {\n\t\t\t\treturn this.get_container_ul().children(\".jstree-node\");\n\t\t\t}\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn obj.children(\".jstree-children\").children(\".jstree-node\");\n\t\t},\n\t\t/**\n\t\t * checks if a node has children\n\t\t * @name is_parent(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_parent : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && (obj.state.loaded === false || obj.children.length > 0);\n\t\t},\n\t\t/**\n\t\t * checks if a node is loaded (its children are available)\n\t\t * @name is_loaded(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_loaded : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state.loaded;\n\t\t},\n\t\t/**\n\t\t * check if a node is currently loading (fetching children)\n\t\t * @name is_loading(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_loading : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state && obj.state.loading;\n\t\t},\n\t\t/**\n\t\t * check if a node is opened\n\t\t * @name is_open(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_open : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state.opened;\n\t\t},\n\t\t/**\n\t\t * check if a node is in a closed state\n\t\t * @name is_closed(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_closed : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && this.is_parent(obj) && !obj.state.opened;\n\t\t},\n\t\t/**\n\t\t * check if a node has no children\n\t\t * @name is_leaf(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_leaf : function (obj) {\n\t\t\treturn !this.is_parent(obj);\n\t\t},\n\t\t/**\n\t\t * loads a node (fetches its children using the `core.data` setting). Multiple nodes can be passed to by using an array.\n\t\t * @name load_node(obj [, callback])\n\t\t * @param  {mixed} obj\n\t\t * @param  {function} callback a function to be executed once loading is complete, the function is executed in the instance's scope and receives two arguments - the node and a boolean status\n\t\t * @return {Boolean}\n\t\t * @trigger load_node.jstree\n\t\t */\n\t\tload_node : function (obj, callback) {\n\t\t\tvar dom = this.get_node(obj, true), k, l, i, j, c;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tthis._load_nodes(obj.slice(), callback);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) {\n\t\t\t\tif(callback) { callback.call(this, obj, false); }\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t// if(obj.state.loading) { } // the node is already loading - just wait for it to load and invoke callback? but if called implicitly it should be loaded again?\n\t\t\tif(obj.state.loaded) {\n\t\t\t\tobj.state.loaded = false;\n\t\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\t\tthis._model.data[obj.parents[i]].children_d = $.vakata.array_filter(this._model.data[obj.parents[i]].children_d, function (v) {\n\t\t\t\t\t\treturn $.inArray(v, obj.children_d) === -1;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tfor(k = 0, l = obj.children_d.length; k < l; k++) {\n\t\t\t\t\tif(this._model.data[obj.children_d[k]].state.selected) {\n\t\t\t\t\t\tc = true;\n\t\t\t\t\t}\n\t\t\t\t\tdelete this._model.data[obj.children_d[k]];\n\t\t\t\t}\n\t\t\t\tif (c) {\n\t\t\t\t\tthis._data.core.selected = $.vakata.array_filter(this._data.core.selected, function (v) {\n\t\t\t\t\t\treturn $.inArray(v, obj.children_d) === -1;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tobj.children = [];\n\t\t\t\tobj.children_d = [];\n\t\t\t\tif(c) {\n\t\t\t\t\tthis.trigger('changed', { 'action' : 'load_node', 'node' : obj, 'selected' : this._data.core.selected });\n\t\t\t\t}\n\t\t\t}\n\t\t\tobj.state.failed = false;\n\t\t\tobj.state.loading = true;\n\t\t\tif (obj.id !== $.jstree.root) {\n\t\t\t\tdom.children(\".jstree-anchor\").attr('aria-busy', true);\n\t\t\t} else {\n\t\t\t\tdom.attr('aria-busy', true);\n\t\t\t}\n\t\t\tdom.addClass(\"jstree-loading\");\n\t\t\tthis._load_node(obj, function (status) {\n\t\t\t\tobj = this._model.data[obj.id];\n\t\t\t\tobj.state.loading = false;\n\t\t\t\tobj.state.loaded = status;\n\t\t\t\tobj.state.failed = !obj.state.loaded;\n\t\t\t\tvar dom = this.get_node(obj, true), i = 0, j = 0, m = this._model.data, has_children = false;\n\t\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\t\tif(m[obj.children[i]] && !m[obj.children[i]].state.hidden) {\n\t\t\t\t\t\thas_children = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(obj.state.loaded && dom && dom.length) {\n\t\t\t\t\tdom.removeClass('jstree-closed jstree-open jstree-leaf');\n\t\t\t\t\tif (!has_children) {\n\t\t\t\t\t\tdom.addClass('jstree-leaf');\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (obj.id !== '#') {\n\t\t\t\t\t\t\tdom.addClass(obj.state.opened ? 'jstree-open' : 'jstree-closed');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (obj.id !== $.jstree.root) {\n\t\t\t\t\tdom.children(\".jstree-anchor\").attr('aria-busy', false);\n\t\t\t\t} else {\n\t\t\t\t\tdom.attr('aria-busy', false);\n\t\t\t\t}\n\t\t\t\tdom.removeClass(\"jstree-loading\");\n\t\t\t\t/**\n\t\t\t\t * triggered after a node is loaded\n\t\t\t\t * @event\n\t\t\t\t * @name load_node.jstree\n\t\t\t\t * @param {Object} node the node that was loading\n\t\t\t\t * @param {Boolean} status was the node loaded successfully\n\t\t\t\t */\n\t\t\t\tthis.trigger('load_node', { \"node\" : obj, \"status\" : status });\n\t\t\t\tif(callback) {\n\t\t\t\t\tcallback.call(this, obj, status);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * load an array of nodes (will also load unavailable nodes as soon as they appear in the structure). Used internally.\n\t\t * @private\n\t\t * @name _load_nodes(nodes [, callback])\n\t\t * @param  {array} nodes\n\t\t * @param  {function} callback a function to be executed once loading is complete, the function is executed in the instance's scope and receives one argument - the array passed to _load_nodes\n\t\t */\n\t\t_load_nodes : function (nodes, callback, is_callback, force_reload) {\n\t\t\tvar r = true,\n\t\t\t\tc = function () { this._load_nodes(nodes, callback, true); },\n\t\t\t\tm = this._model.data, i, j, tmp = [];\n\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\tif(m[nodes[i]] && ( (!m[nodes[i]].state.loaded && !m[nodes[i]].state.failed) || (!is_callback && force_reload) )) {\n\t\t\t\t\tif(!this.is_loading(nodes[i])) {\n\t\t\t\t\t\tthis.load_node(nodes[i], c);\n\t\t\t\t\t}\n\t\t\t\t\tr = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(r) {\n\t\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\t\tif(m[nodes[i]] && m[nodes[i]].state.loaded) {\n\t\t\t\t\t\ttmp.push(nodes[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(callback && !callback.done) {\n\t\t\t\t\tcallback.call(this, tmp);\n\t\t\t\t\tcallback.done = true;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * loads all unloaded nodes\n\t\t * @name load_all([obj, callback])\n\t\t * @param {mixed} obj the node to load recursively, omit to load all nodes in the tree\n\t\t * @param {function} callback a function to be executed once loading all the nodes is complete,\n\t\t * @trigger load_all.jstree\n\t\t */\n\t\tload_all : function (obj, callback) {\n\t\t\tif(!obj) { obj = $.jstree.root; }\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar to_load = [],\n\t\t\t\tm = this._model.data,\n\t\t\t\tc = m[obj.id].children_d,\n\t\t\t\ti, j;\n\t\t\tif(obj.state && !obj.state.loaded) {\n\t\t\t\tto_load.push(obj.id);\n\t\t\t}\n\t\t\tfor(i = 0, j = c.length; i < j; i++) {\n\t\t\t\tif(m[c[i]] && m[c[i]].state && !m[c[i]].state.loaded) {\n\t\t\t\t\tto_load.push(c[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(to_load.length) {\n\t\t\t\tthis._load_nodes(to_load, function () {\n\t\t\t\t\tthis.load_all(obj, callback);\n\t\t\t\t});\n\t\t\t}\n\t\t\telse {\n\t\t\t\t/**\n\t\t\t\t * triggered after a load_all call completes\n\t\t\t\t * @event\n\t\t\t\t * @name load_all.jstree\n\t\t\t\t * @param {Object} node the recursively loaded node\n\t\t\t\t */\n\t\t\t\tif(callback) { callback.call(this, obj); }\n\t\t\t\tthis.trigger('load_all', { \"node\" : obj });\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * handles the actual loading of a node. Used only internally.\n\t\t * @private\n\t\t * @name _load_node(obj [, callback])\n\t\t * @param  {mixed} obj\n\t\t * @param  {function} callback a function to be executed once loading is complete, the function is executed in the instance's scope and receives one argument - a boolean status\n\t\t * @return {Boolean}\n\t\t */\n\t\t_load_node : function (obj, callback) {\n\t\t\tvar s = this.settings.core.data, t;\n\t\t\tvar notTextOrCommentNode = function notTextOrCommentNode () {\n\t\t\t\treturn this.nodeType !== 3 && this.nodeType !== 8;\n\t\t\t};\n\t\t\t// use original HTML\n\t\t\tif(!s) {\n\t\t\t\tif(obj.id === $.jstree.root) {\n\t\t\t\t\treturn this._append_html_data(obj, this._data.core.original_container_html.clone(true), function (status) {\n\t\t\t\t\t\tcallback.call(this, status);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn callback.call(this, false);\n\t\t\t\t}\n\t\t\t\t// return callback.call(this, obj.id === $.jstree.root ? this._append_html_data(obj, this._data.core.original_container_html.clone(true)) : false);\n\t\t\t}\n\t\t\tif($.vakata.is_function(s)) {\n\t\t\t\treturn s.call(this, obj, function (d) {\n\t\t\t\t\tif(d === false) {\n\t\t\t\t\t\tcallback.call(this, false);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis[typeof d === 'string' ? '_append_html_data' : '_append_json_data'](obj, typeof d === 'string' ? $($.parseHTML(d)).filter(notTextOrCommentNode) : d, function (status) {\n\t\t\t\t\t\t\tcallback.call(this, status);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t// return d === false ? callback.call(this, false) : callback.call(this, this[typeof d === 'string' ? '_append_html_data' : '_append_json_data'](obj, typeof d === 'string' ? $(d) : d));\n\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t\tif(typeof s === 'object') {\n\t\t\t\tif(s.url) {\n\t\t\t\t\ts = $.extend(true, {}, s);\n\t\t\t\t\tif($.vakata.is_function(s.url)) {\n\t\t\t\t\t\ts.url = s.url.call(this, obj);\n\t\t\t\t\t}\n\t\t\t\t\tif($.vakata.is_function(s.data)) {\n\t\t\t\t\t\ts.data = s.data.call(this, obj);\n\t\t\t\t\t}\n\t\t\t\t\treturn $.ajax(s)\n\t\t\t\t\t\t.done(function (d,t,x) {\n\t\t\t\t\t\t\t\tvar type = x.getResponseHeader('Content-Type');\n\t\t\t\t\t\t\t\tif((type && type.indexOf('json') !== -1) || typeof d === \"object\") {\n\t\t\t\t\t\t\t\t\treturn this._append_json_data(obj, d, function (status) { callback.call(this, status); });\n\t\t\t\t\t\t\t\t\t//return callback.call(this, this._append_json_data(obj, d));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif((type && type.indexOf('html') !== -1) || typeof d === \"string\") {\n\t\t\t\t\t\t\t\t\treturn this._append_html_data(obj, $($.parseHTML(d)).filter(notTextOrCommentNode), function (status) { callback.call(this, status); });\n\t\t\t\t\t\t\t\t\t// return callback.call(this, this._append_html_data(obj, $(d)));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'ajax', 'plugin' : 'core', 'id' : 'core_04', 'reason' : 'Could not load node', 'data' : JSON.stringify({ 'id' : obj.id, 'xhr' : x }) };\n\t\t\t\t\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\t\t\t\t\treturn callback.call(this, false);\n\t\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t\t.fail(function (f) {\n\t\t\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'ajax', 'plugin' : 'core', 'id' : 'core_04', 'reason' : 'Could not load node', 'data' : JSON.stringify({ 'id' : obj.id, 'xhr' : f }) };\n\t\t\t\t\t\t\t\tcallback.call(this, false);\n\t\t\t\t\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\t\t\t\t}.bind(this));\n\t\t\t\t}\n\t\t\t\tif ($.vakata.is_array(s)) {\n\t\t\t\t\tt = $.extend(true, [], s);\n\t\t\t\t} else if ($.isPlainObject(s)) {\n\t\t\t\t\tt = $.extend(true, {}, s);\n\t\t\t\t} else {\n\t\t\t\t\tt = s;\n\t\t\t\t}\n\t\t\t\tif(obj.id === $.jstree.root) {\n\t\t\t\t\treturn this._append_json_data(obj, t, function (status) {\n\t\t\t\t\t\tcallback.call(this, status);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis._data.core.last_error = { 'error' : 'nodata', 'plugin' : 'core', 'id' : 'core_05', 'reason' : 'Could not load node', 'data' : JSON.stringify({ 'id' : obj.id }) };\n\t\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\t\treturn callback.call(this, false);\n\t\t\t\t}\n\t\t\t\t//return callback.call(this, (obj.id === $.jstree.root ? this._append_json_data(obj, t) : false) );\n\t\t\t}\n\t\t\tif(typeof s === 'string') {\n\t\t\t\tif(obj.id === $.jstree.root) {\n\t\t\t\t\treturn this._append_html_data(obj, $($.parseHTML(s)).filter(notTextOrCommentNode), function (status) {\n\t\t\t\t\t\tcallback.call(this, status);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis._data.core.last_error = { 'error' : 'nodata', 'plugin' : 'core', 'id' : 'core_06', 'reason' : 'Could not load node', 'data' : JSON.stringify({ 'id' : obj.id }) };\n\t\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\t\treturn callback.call(this, false);\n\t\t\t\t}\n\t\t\t\t//return callback.call(this, (obj.id === $.jstree.root ? this._append_html_data(obj, $(s)) : false) );\n\t\t\t}\n\t\t\treturn callback.call(this, false);\n\t\t},\n\t\t/**\n\t\t * adds a node to the list of nodes to redraw. Used only internally.\n\t\t * @private\n\t\t * @name _node_changed(obj [, callback])\n\t\t * @param  {mixed} obj\n\t\t */\n\t\t_node_changed : function (obj) {\n\t\t\tobj = this.get_node(obj);\n      if (obj && $.inArray(obj.id, this._model.changed) === -1) {\n\t\t\t\tthis._model.changed.push(obj.id);\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * appends HTML content to the tree. Used internally.\n\t\t * @private\n\t\t * @name _append_html_data(obj, data)\n\t\t * @param  {mixed} obj the node to append to\n\t\t * @param  {String} data the HTML string to parse and append\n\t\t * @trigger model.jstree, changed.jstree\n\t\t */\n\t\t_append_html_data : function (dom, data, cb) {\n\t\t\tdom = this.get_node(dom);\n\t\t\tdom.children = [];\n\t\t\tdom.children_d = [];\n\t\t\tvar dat = data.is('ul') ? data.children() : data,\n\t\t\t\tpar = dom.id,\n\t\t\t\tchd = [],\n\t\t\t\tdpc = [],\n\t\t\t\tm = this._model.data,\n\t\t\t\tp = m[par],\n\t\t\t\ts = this._data.core.selected.length,\n\t\t\t\ttmp, i, j;\n\t\t\tdat.each(function (i, v) {\n\t\t\t\ttmp = this._parse_model_from_html($(v), par, p.parents.concat());\n\t\t\t\tif(tmp) {\n\t\t\t\t\tchd.push(tmp);\n\t\t\t\t\tdpc.push(tmp);\n\t\t\t\t\tif(m[tmp].children_d.length) {\n\t\t\t\t\t\tdpc = dpc.concat(m[tmp].children_d);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t\tp.children = chd;\n\t\t\tp.children_d = dpc;\n\t\t\tfor(i = 0, j = p.parents.length; i < j; i++) {\n\t\t\t\tm[p.parents[i]].children_d = m[p.parents[i]].children_d.concat(dpc);\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when new data is inserted to the tree model\n\t\t\t * @event\n\t\t\t * @name model.jstree\n\t\t\t * @param {Array} nodes an array of node IDs\n\t\t\t * @param {String} parent the parent ID of the nodes\n\t\t\t */\n\t\t\tthis.trigger('model', { \"nodes\" : dpc, 'parent' : par });\n\t\t\tif(par !== $.jstree.root) {\n\t\t\t\tthis._node_changed(par);\n\t\t\t\tthis.redraw();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.get_container_ul().children('.jstree-initial-node').remove();\n\t\t\t\tthis.redraw(true);\n\t\t\t}\n\t\t\tif(this._data.core.selected.length !== s) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'model', 'selected' : this._data.core.selected });\n\t\t\t}\n\t\t\tcb.call(this, true);\n\t\t},\n\t\t/**\n\t\t * appends JSON content to the tree. Used internally.\n\t\t * @private\n\t\t * @name _append_json_data(obj, data)\n\t\t * @param  {mixed} obj the node to append to\n\t\t * @param  {String} data the JSON object to parse and append\n\t\t * @param  {Boolean} force_processing internal param - do not set\n\t\t * @trigger model.jstree, changed.jstree\n\t\t */\n\t\t_append_json_data : function (dom, data, cb, force_processing) {\n\t\t\tif(this.element === null) { return; }\n\t\t\tdom = this.get_node(dom);\n\t\t\tdom.children = [];\n\t\t\tdom.children_d = [];\n\t\t\t// *%$@!!!\n\t\t\tif(data.d) {\n\t\t\t\tdata = data.d;\n\t\t\t\tif(typeof data === \"string\") {\n\t\t\t\t\tdata = JSON.parse(data);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!$.vakata.is_array(data)) { data = [data]; }\n\t\t\tvar w = null,\n\t\t\t\targs = {\n\t\t\t\t\t'df'\t: this._model.default_state,\n\t\t\t\t\t'dat'\t: data,\n\t\t\t\t\t'par'\t: dom.id,\n\t\t\t\t\t'm'\t\t: this._model.data,\n\t\t\t\t\t't_id'\t: this._id,\n\t\t\t\t\t't_cnt'\t: this._cnt,\n\t\t\t\t\t'sel'\t: this._data.core.selected\n\t\t\t\t},\n\t\t\t\tinst = this,\n\t\t\t\tfunc = function (data, undefined) {\n\t\t\t\t\tif(data.data) { data = data.data; }\n\t\t\t\t\tvar dat = data.dat,\n\t\t\t\t\t\tpar = data.par,\n\t\t\t\t\t\tchd = [],\n\t\t\t\t\t\tdpc = [],\n\t\t\t\t\t\tadd = [],\n\t\t\t\t\t\tdf = data.df,\n\t\t\t\t\t\tt_id = data.t_id,\n\t\t\t\t\t\tt_cnt = data.t_cnt,\n\t\t\t\t\t\tm = data.m,\n\t\t\t\t\t\tp = m[par],\n\t\t\t\t\t\tsel = data.sel,\n\t\t\t\t\t\ttmp, i, j, rslt,\n\t\t\t\t\t\tparse_flat = function (d, p, ps) {\n\t\t\t\t\t\t\tif(!ps) { ps = []; }\n\t\t\t\t\t\t\telse { ps = ps.concat(); }\n\t\t\t\t\t\t\tif(p) { ps.unshift(p); }\n\t\t\t\t\t\t\tvar tid = d.id.toString(),\n\t\t\t\t\t\t\t\ti, j, c, e,\n\t\t\t\t\t\t\t\ttmp = {\n\t\t\t\t\t\t\t\t\tid\t\t\t: tid,\n\t\t\t\t\t\t\t\t\ttext\t\t: d.text || '',\n\t\t\t\t\t\t\t\t\ticon\t\t: d.icon !== undefined ? d.icon : true,\n\t\t\t\t\t\t\t\t\tparent\t\t: p,\n\t\t\t\t\t\t\t\t\tparents\t\t: ps,\n\t\t\t\t\t\t\t\t\tchildren\t: d.children || [],\n\t\t\t\t\t\t\t\t\tchildren_d\t: d.children_d || [],\n\t\t\t\t\t\t\t\t\tdata\t\t: d.data,\n\t\t\t\t\t\t\t\t\tstate\t\t: { },\n\t\t\t\t\t\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\t\t\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\t\t\t\t\t\toriginal\t: false\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tfor(i in df) {\n\t\t\t\t\t\t\t\tif(df.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\ttmp.state[i] = df[i];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && d.data && d.data.jstree && d.data.jstree.icon) {\n\t\t\t\t\t\t\t\ttmp.icon = d.data.jstree.icon;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(tmp.icon === undefined || tmp.icon === null || tmp.icon === \"\") {\n\t\t\t\t\t\t\t\ttmp.icon = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && d.data) {\n\t\t\t\t\t\t\t\ttmp.data = d.data;\n\t\t\t\t\t\t\t\tif(d.data.jstree) {\n\t\t\t\t\t\t\t\t\tfor(i in d.data.jstree) {\n\t\t\t\t\t\t\t\t\t\tif(d.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.state[i] = d.data.jstree[i];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && typeof d.state === 'object') {\n\t\t\t\t\t\t\t\tfor (i in d.state) {\n\t\t\t\t\t\t\t\t\tif(d.state.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\ttmp.state[i] = d.state[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && typeof d.li_attr === 'object') {\n\t\t\t\t\t\t\t\tfor (i in d.li_attr) {\n\t\t\t\t\t\t\t\t\tif(d.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\ttmp.li_attr[i] = d.li_attr[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!tmp.li_attr.id) {\n\t\t\t\t\t\t\t\ttmp.li_attr.id = tid;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && typeof d.a_attr === 'object') {\n\t\t\t\t\t\t\t\tfor (i in d.a_attr) {\n\t\t\t\t\t\t\t\t\tif(d.a_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\ttmp.a_attr[i] = d.a_attr[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && d.children && d.children === true) {\n\t\t\t\t\t\t\t\ttmp.state.loaded = false;\n\t\t\t\t\t\t\t\ttmp.children = [];\n\t\t\t\t\t\t\t\ttmp.children_d = [];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tm[tmp.id] = tmp;\n\t\t\t\t\t\t\tfor(i = 0, j = tmp.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\tc = parse_flat(m[tmp.children[i]], tmp.id, ps);\n\t\t\t\t\t\t\t\te = m[c];\n\t\t\t\t\t\t\t\ttmp.children_d.push(c);\n\t\t\t\t\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\t\t\t\t\ttmp.children_d = tmp.children_d.concat(e.children_d);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdelete d.data;\n\t\t\t\t\t\t\tdelete d.children;\n\t\t\t\t\t\t\tm[tmp.id].original = d;\n\t\t\t\t\t\t\tif(tmp.state.selected) {\n\t\t\t\t\t\t\t\tadd.push(tmp.id);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn tmp.id;\n\t\t\t\t\t\t},\n\t\t\t\t\t\tparse_nest = function (d, p, ps) {\n\t\t\t\t\t\t\tif(!ps) { ps = []; }\n\t\t\t\t\t\t\telse { ps = ps.concat(); }\n\t\t\t\t\t\t\tif(p) { ps.unshift(p); }\n\t\t\t\t\t\t\tvar tid = false, i, j, c, e, tmp;\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\ttid = 'j' + t_id + '_' + (++t_cnt);\n\t\t\t\t\t\t\t} while(m[tid]);\n\n\t\t\t\t\t\t\ttmp = {\n\t\t\t\t\t\t\t\tid\t\t\t: false,\n\t\t\t\t\t\t\t\ttext\t\t: typeof d === 'string' ? d : '',\n\t\t\t\t\t\t\t\ticon\t\t: typeof d === 'object' && d.icon !== undefined ? d.icon : true,\n\t\t\t\t\t\t\t\tparent\t\t: p,\n\t\t\t\t\t\t\t\tparents\t\t: ps,\n\t\t\t\t\t\t\t\tchildren\t: [],\n\t\t\t\t\t\t\t\tchildren_d\t: [],\n\t\t\t\t\t\t\t\tdata\t\t: null,\n\t\t\t\t\t\t\t\tstate\t\t: { },\n\t\t\t\t\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\t\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\t\t\t\t\toriginal\t: false\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tfor(i in df) {\n\t\t\t\t\t\t\t\tif(df.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\ttmp.state[i] = df[i];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && (d.id || d.id === 0)) { tmp.id = d.id.toString(); }\n\t\t\t\t\t\t\tif(d && d.text) { tmp.text = d.text; }\n\t\t\t\t\t\t\tif(d && d.data && d.data.jstree && d.data.jstree.icon) {\n\t\t\t\t\t\t\t\ttmp.icon = d.data.jstree.icon;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(tmp.icon === undefined || tmp.icon === null || tmp.icon === \"\") {\n\t\t\t\t\t\t\t\ttmp.icon = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && d.data) {\n\t\t\t\t\t\t\t\ttmp.data = d.data;\n\t\t\t\t\t\t\t\tif(d.data.jstree) {\n\t\t\t\t\t\t\t\t\tfor(i in d.data.jstree) {\n\t\t\t\t\t\t\t\t\t\tif(d.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.state[i] = d.data.jstree[i];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && typeof d.state === 'object') {\n\t\t\t\t\t\t\t\tfor (i in d.state) {\n\t\t\t\t\t\t\t\t\tif(d.state.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\ttmp.state[i] = d.state[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && typeof d.li_attr === 'object') {\n\t\t\t\t\t\t\t\tfor (i in d.li_attr) {\n\t\t\t\t\t\t\t\t\tif(d.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\ttmp.li_attr[i] = d.li_attr[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(tmp.li_attr.id && !(tmp.id || tmp.id === 0)) {\n\t\t\t\t\t\t\t\ttmp.id = tmp.li_attr.id.toString();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!(tmp.id || tmp.id === 0)) {\n\t\t\t\t\t\t\t\ttmp.id = tid;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!tmp.li_attr.id) {\n\t\t\t\t\t\t\t\ttmp.li_attr.id = tmp.id;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && typeof d.a_attr === 'object') {\n\t\t\t\t\t\t\t\tfor (i in d.a_attr) {\n\t\t\t\t\t\t\t\t\tif(d.a_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\ttmp.a_attr[i] = d.a_attr[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && d.children && d.children.length) {\n\t\t\t\t\t\t\t\tfor(i = 0, j = d.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tc = parse_nest(d.children[i], tmp.id, ps);\n\t\t\t\t\t\t\t\t\te = m[c];\n\t\t\t\t\t\t\t\t\ttmp.children.push(c);\n\t\t\t\t\t\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\t\t\t\t\t\ttmp.children_d = tmp.children_d.concat(e.children_d);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\ttmp.children_d = tmp.children_d.concat(tmp.children);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(d && d.children && d.children === true) {\n\t\t\t\t\t\t\t\ttmp.state.loaded = false;\n\t\t\t\t\t\t\t\ttmp.children = [];\n\t\t\t\t\t\t\t\ttmp.children_d = [];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdelete d.data;\n\t\t\t\t\t\t\tdelete d.children;\n\t\t\t\t\t\t\ttmp.original = d;\n\t\t\t\t\t\t\tm[tmp.id] = tmp;\n\t\t\t\t\t\t\tif(tmp.state.selected) {\n\t\t\t\t\t\t\t\tadd.push(tmp.id);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn tmp.id;\n\t\t\t\t\t\t};\n\n\t\t\t\t\tif(dat.length && dat[0].id !== undefined && dat[0].parent !== undefined) {\n\t\t\t\t\t\t// Flat JSON support (for easy import from DB):\n\t\t\t\t\t\t// 1) convert to object (foreach)\n\t\t\t\t\t\tfor(i = 0, j = dat.length; i < j; i++) {\n\t\t\t\t\t\t\tif(!dat[i].children) {\n\t\t\t\t\t\t\t\tdat[i].children = [];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!dat[i].state) {\n\t\t\t\t\t\t\t\tdat[i].state = {};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tm[dat[i].id.toString()] = dat[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 2) populate children (foreach)\n\t\t\t\t\t\tfor(i = 0, j = dat.length; i < j; i++) {\n\t\t\t\t\t\t\tif (!m[dat[i].parent.toString()]) {\n\t\t\t\t\t\t\t\tif (typeof inst !== \"undefined\") {\n\t\t\t\t\t\t\t\t\tinst._data.core.last_error = { 'error' : 'parse', 'plugin' : 'core', 'id' : 'core_07', 'reason' : 'Node with invalid parent', 'data' : JSON.stringify({ 'id' : dat[i].id.toString(), 'parent' : dat[i].parent.toString() }) };\n\t\t\t\t\t\t\t\t\tinst.settings.core.error.call(inst, inst._data.core.last_error);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tm[dat[i].parent.toString()].children.push(dat[i].id.toString());\n\t\t\t\t\t\t\t// populate parent.children_d\n\t\t\t\t\t\t\tp.children_d.push(dat[i].id.toString());\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 3) normalize && populate parents and children_d with recursion\n\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\ttmp = parse_flat(m[p.children[i]], par, p.parents.concat());\n\t\t\t\t\t\t\tdpc.push(tmp);\n\t\t\t\t\t\t\tif(m[tmp].children_d.length) {\n\t\t\t\t\t\t\t\tdpc = dpc.concat(m[tmp].children_d);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tfor(i = 0, j = p.parents.length; i < j; i++) {\n\t\t\t\t\t\t\tm[p.parents[i]].children_d = m[p.parents[i]].children_d.concat(dpc);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// ?) three_state selection - p.state.selected && t - (if three_state foreach(dat => ch) -> foreach(parents) if(parent.selected) child.selected = true;\n\t\t\t\t\t\trslt = {\n\t\t\t\t\t\t\t'cnt' : t_cnt,\n\t\t\t\t\t\t\t'mod' : m,\n\t\t\t\t\t\t\t'sel' : sel,\n\t\t\t\t\t\t\t'par' : par,\n\t\t\t\t\t\t\t'dpc' : dpc,\n\t\t\t\t\t\t\t'add' : add\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tfor(i = 0, j = dat.length; i < j; i++) {\n\t\t\t\t\t\t\ttmp = parse_nest(dat[i], par, p.parents.concat());\n\t\t\t\t\t\t\tif(tmp) {\n\t\t\t\t\t\t\t\tchd.push(tmp);\n\t\t\t\t\t\t\t\tdpc.push(tmp);\n\t\t\t\t\t\t\t\tif(m[tmp].children_d.length) {\n\t\t\t\t\t\t\t\t\tdpc = dpc.concat(m[tmp].children_d);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tp.children = chd;\n\t\t\t\t\t\tp.children_d = dpc;\n\t\t\t\t\t\tfor(i = 0, j = p.parents.length; i < j; i++) {\n\t\t\t\t\t\t\tm[p.parents[i]].children_d = m[p.parents[i]].children_d.concat(dpc);\n\t\t\t\t\t\t}\n\t\t\t\t\t\trslt = {\n\t\t\t\t\t\t\t'cnt' : t_cnt,\n\t\t\t\t\t\t\t'mod' : m,\n\t\t\t\t\t\t\t'sel' : sel,\n\t\t\t\t\t\t\t'par' : par,\n\t\t\t\t\t\t\t'dpc' : dpc,\n\t\t\t\t\t\t\t'add' : add\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\tif(typeof window === 'undefined' || typeof window.document === 'undefined') {\n\t\t\t\t\t\tpostMessage(rslt);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\treturn rslt;\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\trslt = function (rslt, worker) {\n\t\t\t\t\tif(this.element === null) { return; }\n\t\t\t\t\tthis._cnt = rslt.cnt;\n\t\t\t\t\tvar i, m = this._model.data;\n\t\t\t\t\tfor (i in m) {\n\t\t\t\t\t\tif (m.hasOwnProperty(i) && m[i].state && m[i].state.loading && rslt.mod[i]) {\n\t\t\t\t\t\t\trslt.mod[i].state.loading = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis._model.data = rslt.mod; // breaks the reference in load_node - careful\n\n\t\t\t\t\tif(worker) {\n\t\t\t\t\t\tvar j, a = rslt.add, r = rslt.sel, s = this._data.core.selected.slice();\n\t\t\t\t\t\tm = this._model.data;\n\t\t\t\t\t\t// if selection was changed while calculating in worker\n\t\t\t\t\t\tif(r.length !== s.length || $.vakata.array_unique(r.concat(s)).length !== r.length) {\n\t\t\t\t\t\t\t// deselect nodes that are no longer selected\n\t\t\t\t\t\t\tfor(i = 0, j = r.length; i < j; i++) {\n\t\t\t\t\t\t\t\tif($.inArray(r[i], a) === -1 && $.inArray(r[i], s) === -1) {\n\t\t\t\t\t\t\t\t\tm[r[i]].state.selected = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// select nodes that were selected in the mean time\n\t\t\t\t\t\t\tfor(i = 0, j = s.length; i < j; i++) {\n\t\t\t\t\t\t\t\tif($.inArray(s[i], r) === -1) {\n\t\t\t\t\t\t\t\t\tm[s[i]].state.selected = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(rslt.add.length) {\n\t\t\t\t\t\tthis._data.core.selected = this._data.core.selected.concat(rslt.add);\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.trigger('model', { \"nodes\" : rslt.dpc, 'parent' : rslt.par });\n\n\t\t\t\t\tif(rslt.par !== $.jstree.root) {\n\t\t\t\t\t\tthis._node_changed(rslt.par);\n\t\t\t\t\t\tthis.redraw();\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\t// this.get_container_ul().children('.jstree-initial-node').remove();\n\t\t\t\t\t\tthis.redraw(true);\n\t\t\t\t\t}\n\t\t\t\t\tif(rslt.add.length) {\n\t\t\t\t\t\tthis.trigger('changed', { 'action' : 'model', 'selected' : this._data.core.selected });\n\t\t\t\t\t}\n\n\t\t\t\t\t// If no worker, try to mimic worker behavioour, by invoking cb asynchronously\n\t\t\t\t\tif (!worker && setImmediate) {\n\t\t\t\t\t\tsetImmediate(function(){\n\t\t\t\t\t\t\tcb.call(inst, true);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tcb.call(inst, true);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\tif(this.settings.core.worker && window.Blob && window.URL && window.Worker) {\n\t\t\t\ttry {\n\t\t\t\t\tif(this._wrk === null) {\n\t\t\t\t\t\tthis._wrk = window.URL.createObjectURL(\n\t\t\t\t\t\t\tnew window.Blob(\n\t\t\t\t\t\t\t\t['self.onmessage = ' + func.toString()],\n\t\t\t\t\t\t\t\t{type:\"text/javascript\"}\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\tif(!this._data.core.working || force_processing) {\n\t\t\t\t\t\tthis._data.core.working = true;\n\t\t\t\t\t\tw = new window.Worker(this._wrk);\n\t\t\t\t\t\tw.onmessage = function (e) {\n\t\t\t\t\t\t\trslt.call(this, e.data, true);\n\t\t\t\t\t\t\ttry { w.terminate(); w = null; } catch(ignore) { }\n\t\t\t\t\t\t\tif(this._data.core.worker_queue.length) {\n\t\t\t\t\t\t\t\tthis._append_json_data.apply(this, this._data.core.worker_queue.shift());\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tthis._data.core.working = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}.bind(this);\n\t\t\t\t\t\tw.onerror = function (e) {\n\t\t\t\t\t\t\trslt.call(this, func(args), false);\n\t\t\t\t\t\t\tif(this._data.core.worker_queue.length) {\n\t\t\t\t\t\t\t\tthis._append_json_data.apply(this, this._data.core.worker_queue.shift());\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tthis._data.core.working = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}.bind(this);\n\t\t\t\t\t\tif(!args.par) {\n\t\t\t\t\t\t\tif(this._data.core.worker_queue.length) {\n\t\t\t\t\t\t\t\tthis._append_json_data.apply(this, this._data.core.worker_queue.shift());\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tthis._data.core.working = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tw.postMessage(args);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis._data.core.worker_queue.push([dom, data, cb, true]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tcatch(e) {\n\t\t\t\t\trslt.call(this, func(args), false);\n\t\t\t\t\tif(this._data.core.worker_queue.length) {\n\t\t\t\t\t\tthis._append_json_data.apply(this, this._data.core.worker_queue.shift());\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis._data.core.working = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\trslt.call(this, func(args), false);\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * parses a node from a jQuery object and appends them to the in memory tree model. Used internally.\n\t\t * @private\n\t\t * @name _parse_model_from_html(d [, p, ps])\n\t\t * @param  {jQuery} d the jQuery object to parse\n\t\t * @param  {String} p the parent ID\n\t\t * @param  {Array} ps list of all parents\n\t\t * @return {String} the ID of the object added to the model\n\t\t */\n\t\t_parse_model_from_html : function (d, p, ps) {\n\t\t\tif(!ps) { ps = []; }\n\t\t\telse { ps = [].concat(ps); }\n\t\t\tif(p) { ps.unshift(p); }\n\t\t\tvar c, e, m = this._model.data,\n\t\t\t\tdata = {\n\t\t\t\t\tid\t\t\t: false,\n\t\t\t\t\ttext\t\t: false,\n\t\t\t\t\ticon\t\t: true,\n\t\t\t\t\tparent\t\t: p,\n\t\t\t\t\tparents\t\t: ps,\n\t\t\t\t\tchildren\t: [],\n\t\t\t\t\tchildren_d\t: [],\n\t\t\t\t\tdata\t\t: null,\n\t\t\t\t\tstate\t\t: { },\n\t\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\t\toriginal\t: false\n\t\t\t\t}, i, tmp, tid;\n\t\t\tfor(i in this._model.default_state) {\n\t\t\t\tif(this._model.default_state.hasOwnProperty(i)) {\n\t\t\t\t\tdata.state[i] = this._model.default_state[i];\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = $.vakata.attributes(d, true);\n\t\t\t$.each(tmp, function (i, v) {\n\t\t\t\tv = $.vakata.trim(v);\n\t\t\t\tif(!v.length) { return true; }\n\t\t\t\tdata.li_attr[i] = v;\n\t\t\t\tif(i === 'id') {\n\t\t\t\t\tdata.id = v.toString();\n\t\t\t\t}\n\t\t\t});\n\t\t\ttmp = d.children('a').first();\n\t\t\tif(tmp.length) {\n\t\t\t\ttmp = $.vakata.attributes(tmp, true);\n\t\t\t\t$.each(tmp, function (i, v) {\n\t\t\t\t\tv = $.vakata.trim(v);\n\t\t\t\t\tif(v.length) {\n\t\t\t\t\t\tdata.a_attr[i] = v;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\ttmp = d.children(\"a\").first().length ? d.children(\"a\").first().clone() : d.clone();\n\t\t\ttmp.children(\"ins, i, ul\").remove();\n\t\t\ttmp = tmp.html();\n\t\t\ttmp = $('<div></div>').html(tmp);\n\t\t\tdata.text = this.settings.core.force_text ? tmp.text() : tmp.html();\n\t\t\ttmp = d.data();\n\t\t\tdata.data = tmp ? $.extend(true, {}, tmp) : null;\n\t\t\tdata.state.opened = d.hasClass('jstree-open');\n\t\t\tdata.state.selected = d.children('a').hasClass('jstree-clicked');\n\t\t\tdata.state.disabled = d.children('a').hasClass('jstree-disabled');\n\t\t\tif(data.data && data.data.jstree) {\n\t\t\t\tfor(i in data.data.jstree) {\n\t\t\t\t\tif(data.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\tdata.state[i] = data.data.jstree[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = d.children(\"a\").children(\".jstree-themeicon\");\n\t\t\tif(tmp.length) {\n\t\t\t\tdata.icon = tmp.hasClass('jstree-themeicon-hidden') ? false : tmp.attr('rel');\n\t\t\t}\n\t\t\tif(data.state.icon !== undefined) {\n\t\t\t\tdata.icon = data.state.icon;\n\t\t\t}\n\t\t\tif(data.icon === undefined || data.icon === null || data.icon === \"\") {\n\t\t\t\tdata.icon = true;\n\t\t\t}\n\t\t\ttmp = d.children(\"ul\").children(\"li\");\n\t\t\tdo {\n\t\t\t\ttid = 'j' + this._id + '_' + (++this._cnt);\n\t\t\t} while(m[tid]);\n\t\t\tdata.id = data.li_attr.id ? data.li_attr.id.toString() : tid;\n\t\t\tif(tmp.length) {\n\t\t\t\ttmp.each(function (i, v) {\n\t\t\t\t\tc = this._parse_model_from_html($(v), data.id, ps);\n\t\t\t\t\te = this._model.data[c];\n\t\t\t\t\tdata.children.push(c);\n\t\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\t\tdata.children_d = data.children_d.concat(e.children_d);\n\t\t\t\t\t}\n\t\t\t\t}.bind(this));\n\t\t\t\tdata.children_d = data.children_d.concat(data.children);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif(d.hasClass('jstree-closed')) {\n\t\t\t\t\tdata.state.loaded = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(data.li_attr['class']) {\n\t\t\t\tdata.li_attr['class'] = data.li_attr['class'].replace('jstree-closed','').replace('jstree-open','');\n\t\t\t}\n\t\t\tif(data.a_attr['class']) {\n\t\t\t\tdata.a_attr['class'] = data.a_attr['class'].replace('jstree-clicked','').replace('jstree-disabled','');\n\t\t\t}\n\t\t\tm[data.id] = data;\n\t\t\tif(data.state.selected) {\n\t\t\t\tthis._data.core.selected.push(data.id);\n\t\t\t}\n\t\t\treturn data.id;\n\t\t},\n\t\t/**\n\t\t * parses a node from a JSON object (used when dealing with flat data, which has no nesting of children, but has id and parent properties) and appends it to the in memory tree model. Used internally.\n\t\t * @private\n\t\t * @name _parse_model_from_flat_json(d [, p, ps])\n\t\t * @param  {Object} d the JSON object to parse\n\t\t * @param  {String} p the parent ID\n\t\t * @param  {Array} ps list of all parents\n\t\t * @return {String} the ID of the object added to the model\n\t\t */\n\t\t_parse_model_from_flat_json : function (d, p, ps) {\n\t\t\tif(!ps) { ps = []; }\n\t\t\telse { ps = ps.concat(); }\n\t\t\tif(p) { ps.unshift(p); }\n\t\t\tvar tid = d.id.toString(),\n\t\t\t\tm = this._model.data,\n\t\t\t\tdf = this._model.default_state,\n\t\t\t\ti, j, c, e,\n\t\t\t\ttmp = {\n\t\t\t\t\tid\t\t\t: tid,\n\t\t\t\t\ttext\t\t: d.text || '',\n\t\t\t\t\ticon\t\t: d.icon !== undefined ? d.icon : true,\n\t\t\t\t\tparent\t\t: p,\n\t\t\t\t\tparents\t\t: ps,\n\t\t\t\t\tchildren\t: d.children || [],\n\t\t\t\t\tchildren_d\t: d.children_d || [],\n\t\t\t\t\tdata\t\t: d.data,\n\t\t\t\t\tstate\t\t: { },\n\t\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\t\toriginal\t: false\n\t\t\t\t};\n\t\t\tfor(i in df) {\n\t\t\t\tif(df.hasOwnProperty(i)) {\n\t\t\t\t\ttmp.state[i] = df[i];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.data && d.data.jstree && d.data.jstree.icon) {\n\t\t\t\ttmp.icon = d.data.jstree.icon;\n\t\t\t}\n\t\t\tif(tmp.icon === undefined || tmp.icon === null || tmp.icon === \"\") {\n\t\t\t\ttmp.icon = true;\n\t\t\t}\n\t\t\tif(d && d.data) {\n\t\t\t\ttmp.data = d.data;\n\t\t\t\tif(d.data.jstree) {\n\t\t\t\t\tfor(i in d.data.jstree) {\n\t\t\t\t\t\tif(d.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\t\ttmp.state[i] = d.data.jstree[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.state === 'object') {\n\t\t\t\tfor (i in d.state) {\n\t\t\t\t\tif(d.state.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.state[i] = d.state[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.li_attr === 'object') {\n\t\t\t\tfor (i in d.li_attr) {\n\t\t\t\t\tif(d.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.li_attr[i] = d.li_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!tmp.li_attr.id) {\n\t\t\t\ttmp.li_attr.id = tid;\n\t\t\t}\n\t\t\tif(d && typeof d.a_attr === 'object') {\n\t\t\t\tfor (i in d.a_attr) {\n\t\t\t\t\tif(d.a_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.a_attr[i] = d.a_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.children && d.children === true) {\n\t\t\t\ttmp.state.loaded = false;\n\t\t\t\ttmp.children = [];\n\t\t\t\ttmp.children_d = [];\n\t\t\t}\n\t\t\tm[tmp.id] = tmp;\n\t\t\tfor(i = 0, j = tmp.children.length; i < j; i++) {\n\t\t\t\tc = this._parse_model_from_flat_json(m[tmp.children[i]], tmp.id, ps);\n\t\t\t\te = m[c];\n\t\t\t\ttmp.children_d.push(c);\n\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\ttmp.children_d = tmp.children_d.concat(e.children_d);\n\t\t\t\t}\n\t\t\t}\n\t\t\tdelete d.data;\n\t\t\tdelete d.children;\n\t\t\tm[tmp.id].original = d;\n\t\t\tif(tmp.state.selected) {\n\t\t\t\tthis._data.core.selected.push(tmp.id);\n\t\t\t}\n\t\t\treturn tmp.id;\n\t\t},\n\t\t/**\n\t\t * parses a node from a JSON object and appends it to the in memory tree model. Used internally.\n\t\t * @private\n\t\t * @name _parse_model_from_json(d [, p, ps])\n\t\t * @param  {Object} d the JSON object to parse\n\t\t * @param  {String} p the parent ID\n\t\t * @param  {Array} ps list of all parents\n\t\t * @return {String} the ID of the object added to the model\n\t\t */\n\t\t_parse_model_from_json : function (d, p, ps) {\n\t\t\tif(!ps) { ps = []; }\n\t\t\telse { ps = ps.concat(); }\n\t\t\tif(p) { ps.unshift(p); }\n\t\t\tvar tid = false, i, j, c, e, m = this._model.data, df = this._model.default_state, tmp;\n\t\t\tdo {\n\t\t\t\ttid = 'j' + this._id + '_' + (++this._cnt);\n\t\t\t} while(m[tid]);\n\n\t\t\ttmp = {\n\t\t\t\tid\t\t\t: false,\n\t\t\t\ttext\t\t: typeof d === 'string' ? d : '',\n\t\t\t\ticon\t\t: typeof d === 'object' && d.icon !== undefined ? d.icon : true,\n\t\t\t\tparent\t\t: p,\n\t\t\t\tparents\t\t: ps,\n\t\t\t\tchildren\t: [],\n\t\t\t\tchildren_d\t: [],\n\t\t\t\tdata\t\t: null,\n\t\t\t\tstate\t\t: { },\n\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\toriginal\t: false\n\t\t\t};\n\t\t\tfor(i in df) {\n\t\t\t\tif(df.hasOwnProperty(i)) {\n\t\t\t\t\ttmp.state[i] = df[i];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && (d.id || d.id === 0)) { tmp.id = d.id.toString(); }\n\t\t\tif(d && d.text) { tmp.text = d.text; }\n\t\t\tif(d && d.data && d.data.jstree && d.data.jstree.icon) {\n\t\t\t\ttmp.icon = d.data.jstree.icon;\n\t\t\t}\n\t\t\tif(tmp.icon === undefined || tmp.icon === null || tmp.icon === \"\") {\n\t\t\t\ttmp.icon = true;\n\t\t\t}\n\t\t\tif(d && d.data) {\n\t\t\t\ttmp.data = d.data;\n\t\t\t\tif(d.data.jstree) {\n\t\t\t\t\tfor(i in d.data.jstree) {\n\t\t\t\t\t\tif(d.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\t\ttmp.state[i] = d.data.jstree[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.state === 'object') {\n\t\t\t\tfor (i in d.state) {\n\t\t\t\t\tif(d.state.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.state[i] = d.state[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.li_attr === 'object') {\n\t\t\t\tfor (i in d.li_attr) {\n\t\t\t\t\tif(d.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.li_attr[i] = d.li_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(tmp.li_attr.id && !(tmp.id || tmp.id === 0)) {\n\t\t\t\ttmp.id = tmp.li_attr.id.toString();\n\t\t\t}\n\t\t\tif(!(tmp.id || tmp.id === 0)) {\n\t\t\t\ttmp.id = tid;\n\t\t\t}\n\t\t\tif(!tmp.li_attr.id) {\n\t\t\t\ttmp.li_attr.id = tmp.id;\n\t\t\t}\n\t\t\tif(d && typeof d.a_attr === 'object') {\n\t\t\t\tfor (i in d.a_attr) {\n\t\t\t\t\tif(d.a_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.a_attr[i] = d.a_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.children && d.children.length) {\n\t\t\t\tfor(i = 0, j = d.children.length; i < j; i++) {\n\t\t\t\t\tc = this._parse_model_from_json(d.children[i], tmp.id, ps);\n\t\t\t\t\te = m[c];\n\t\t\t\t\ttmp.children.push(c);\n\t\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\t\ttmp.children_d = tmp.children_d.concat(e.children_d);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ttmp.children_d = tmp.children.concat(tmp.children_d);\n\t\t\t}\n\t\t\tif(d && d.children && d.children === true) {\n\t\t\t\ttmp.state.loaded = false;\n\t\t\t\ttmp.children = [];\n\t\t\t\ttmp.children_d = [];\n\t\t\t}\n\t\t\tdelete d.data;\n\t\t\tdelete d.children;\n\t\t\ttmp.original = d;\n\t\t\tm[tmp.id] = tmp;\n\t\t\tif(tmp.state.selected) {\n\t\t\t\tthis._data.core.selected.push(tmp.id);\n\t\t\t}\n\t\t\treturn tmp.id;\n\t\t},\n\t\t/**\n\t\t * redraws all nodes that need to be redrawn. Used internally.\n\t\t * @private\n\t\t * @name _redraw()\n\t\t * @trigger redraw.jstree\n\t\t */\n\t\t_redraw : function () {\n\t\t\tvar nodes = this._model.force_full_redraw ? this._model.data[$.jstree.root].children.concat([]) : this._model.changed.concat([]),\n\t\t\t\tf = document.createElement('UL'), tmp, i, j, fe = this._data.core.focused;\n\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\ttmp = this.redraw_node(nodes[i], true, this._model.force_full_redraw);\n\t\t\t\tif(tmp && this._model.force_full_redraw) {\n\t\t\t\t\tf.appendChild(tmp);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(this._model.force_full_redraw) {\n\t\t\t\tf.className = this.get_container_ul()[0].className;\n\t\t\t\tf.setAttribute('role','presentation');\n\t\t\t\tthis.element.empty().append(f);\n\t\t\t\t//this.get_container_ul()[0].appendChild(f);\n\t\t\t}\n\t\t\tif(fe !== null && this.settings.core.restore_focus) {\n\t\t\t\ttmp = this.get_node(fe, true);\n\t\t\t\tif(tmp && tmp.length && tmp.children('.jstree-anchor')[0] !== document.activeElement) {\n\t\t\t\t\ttmp.children('.jstree-anchor').trigger('focus');\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis._data.core.focused = null;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._model.force_full_redraw = false;\n\t\t\tthis._model.changed = [];\n\t\t\t/**\n\t\t\t * triggered after nodes are redrawn\n\t\t\t * @event\n\t\t\t * @name redraw.jstree\n\t\t\t * @param {array} nodes the redrawn nodes\n\t\t\t */\n\t\t\tthis.trigger('redraw', { \"nodes\" : nodes });\n\t\t},\n\t\t/**\n\t\t * redraws all nodes that need to be redrawn or optionally - the whole tree\n\t\t * @name redraw([full])\n\t\t * @param {Boolean} full if set to `true` all nodes are redrawn.\n\t\t */\n\t\tredraw : function (full) {\n\t\t\tif(full) {\n\t\t\t\tthis._model.force_full_redraw = true;\n\t\t\t}\n\t\t\t//if(this._model.redraw_timeout) {\n\t\t\t//\tclearTimeout(this._model.redraw_timeout);\n\t\t\t//}\n\t\t\t//this._model.redraw_timeout = setTimeout($.proxy(this._redraw, this),0);\n\t\t\tthis._redraw();\n\t\t},\n\t\t/**\n\t\t * redraws a single node's children. Used internally.\n\t\t * @private\n\t\t * @name draw_children(node)\n\t\t * @param {mixed} node the node whose children will be redrawn\n\t\t */\n\t\tdraw_children : function (node) {\n\t\t\tvar obj = this.get_node(node),\n\t\t\t\ti = false,\n\t\t\t\tj = false,\n\t\t\t\tk = false,\n\t\t\t\td = document;\n\t\t\tif(!obj) { return false; }\n\t\t\tif(obj.id === $.jstree.root) { return this.redraw(true); }\n\t\t\tnode = this.get_node(node, true);\n\t\t\tif(!node || !node.length) { return false; } // TODO: quick toggle\n\n\t\t\tnode.children('.jstree-children').remove();\n\t\t\tnode = node[0];\n\t\t\tif(obj.children.length && obj.state.loaded) {\n\t\t\t\tk = d.createElement('UL');\n\t\t\t\tk.setAttribute('role', 'group');\n\t\t\t\tk.className = 'jstree-children';\n\t\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\t\tk.appendChild(this.redraw_node(obj.children[i], true, true));\n\t\t\t\t}\n\t\t\t\tnode.appendChild(k);\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * redraws a single node. Used internally.\n\t\t * @private\n\t\t * @name redraw_node(node, deep, is_callback, force_render)\n\t\t * @param {mixed} node the node to redraw\n\t\t * @param {Boolean} deep should child nodes be redrawn too\n\t\t * @param {Boolean} is_callback is this a recursion call\n\t\t * @param {Boolean} force_render should children of closed parents be drawn anyway\n\t\t */\n\t\tredraw_node : function (node, deep, is_callback, force_render) {\n\t\t\tvar obj = this.get_node(node),\n\t\t\t\tpar = false,\n\t\t\t\tind = false,\n\t\t\t\told = false,\n\t\t\t\ti = false,\n\t\t\t\tj = false,\n\t\t\t\tk = false,\n\t\t\t\tc = '',\n\t\t\t\td = document,\n\t\t\t\tm = this._model.data,\n\t\t\t\tf = false,\n\t\t\t\ts = false,\n\t\t\t\ttmp = null,\n\t\t\t\tt = 0,\n\t\t\t\tl = 0,\n\t\t\t\thas_children = false,\n\t\t\t\tlast_sibling = false;\n\t\t\tif(!obj) { return false; }\n\t\t\tif(obj.id === $.jstree.root) {  return this.redraw(true); }\n\t\t\tdeep = deep || obj.children.length === 0;\n\t\t\tnode = !document.querySelector ? document.getElementById(obj.id) : this.element[0].querySelector('#' + (\"0123456789\".indexOf(obj.id[0]) !== -1 ? '\\\\3' + obj.id[0] + ' ' + obj.id.substr(1).replace($.jstree.idregex,'\\\\$&') : obj.id.replace($.jstree.idregex,'\\\\$&')) ); //, this.element);\n\t\t\tif(!node) {\n\t\t\t\tdeep = true;\n\t\t\t\t//node = d.createElement('LI');\n\t\t\t\tif(!is_callback) {\n\t\t\t\t\tpar = obj.parent !== $.jstree.root ? $('#' + obj.parent.replace($.jstree.idregex,'\\\\$&'), this.element)[0] : null;\n\t\t\t\t\tif(par !== null && (!par || !m[obj.parent].state.opened)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tind = $.inArray(obj.id, par === null ? m[$.jstree.root].children : m[obj.parent].children);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tnode = $(node);\n\t\t\t\tif(!is_callback) {\n\t\t\t\t\tpar = node.parent().parent()[0];\n\t\t\t\t\tif(par === this.element[0]) {\n\t\t\t\t\t\tpar = null;\n\t\t\t\t\t}\n\t\t\t\t\tind = node.index();\n\t\t\t\t}\n\t\t\t\t// m[obj.id].data = node.data(); // use only node's data, no need to touch jquery storage\n\t\t\t\tif(!deep && obj.children.length && !node.children('.jstree-children').length) {\n\t\t\t\t\tdeep = true;\n\t\t\t\t}\n\t\t\t\tif(!deep) {\n\t\t\t\t\told = node.children('.jstree-children')[0];\n\t\t\t\t}\n\t\t\t\tf = node.children('.jstree-anchor')[0] === document.activeElement;\n\t\t\t\tnode.remove();\n\t\t\t\t//node = d.createElement('LI');\n\t\t\t\t//node = node[0];\n\t\t\t}\n\t\t\tnode = this._data.core.node.cloneNode(true);\n\t\t\t// node is DOM, deep is boolean\n\n\t\t\tc = 'jstree-node ';\n\t\t\tfor(i in obj.li_attr) {\n\t\t\t\tif(obj.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\tif(i === 'id') { continue; }\n\t\t\t\t\tif(i !== 'class') {\n\t\t\t\t\t\tnode.setAttribute(i, obj.li_attr[i]);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tc += obj.li_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!obj.a_attr.id) {\n\t\t\t\tobj.a_attr.id = obj.id + '_anchor';\n\t\t\t}\n\t\t\tnode.childNodes[1].setAttribute('aria-selected', !!obj.state.selected);\n\t\t\tnode.childNodes[1].setAttribute('aria-level', obj.parents.length);\n\t\t\tif(this.settings.core.compute_elements_positions) {\n\t\t\t\tnode.childNodes[1].setAttribute('aria-setsize', m[obj.parent].children.length);\n\t\t\t\tnode.childNodes[1].setAttribute('aria-posinset', m[obj.parent].children.indexOf(obj.id) + 1);\n\t\t\t}\n\t\t\tif(obj.state.disabled) {\n\t\t\t\tnode.childNodes[1].setAttribute('aria-disabled', true);\n\t\t\t}\n\n\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\tif(!m[obj.children[i]].state.hidden) {\n\t\t\t\t\thas_children = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(obj.parent !== null && m[obj.parent] && !obj.state.hidden) {\n\t\t\t\ti = $.inArray(obj.id, m[obj.parent].children);\n\t\t\t\tlast_sibling = obj.id;\n\t\t\t\tif(i !== -1) {\n\t\t\t\t\ti++;\n\t\t\t\t\tfor(j = m[obj.parent].children.length; i < j; i++) {\n\t\t\t\t\t\tif(!m[m[obj.parent].children[i]].state.hidden) {\n\t\t\t\t\t\t\tlast_sibling = m[obj.parent].children[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(last_sibling !== obj.id) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif(obj.state.hidden) {\n\t\t\t\tc += ' jstree-hidden';\n\t\t\t}\n\t\t\tif (obj.state.loading) {\n\t\t\t\tc += ' jstree-loading';\n\t\t\t}\n\t\t\tif(obj.state.loaded && !has_children) {\n\t\t\t\tc += ' jstree-leaf';\n\t\t\t}\n\t\t\telse {\n\t\t\t\tc += obj.state.opened && obj.state.loaded ? ' jstree-open' : ' jstree-closed';\n\t\t\t\tnode.childNodes[1].setAttribute('aria-expanded', (obj.state.opened && obj.state.loaded) );\n\t\t\t}\n\t\t\tif(last_sibling === obj.id) {\n\t\t\t\tc += ' jstree-last';\n\t\t\t}\n\t\t\tnode.id = obj.id;\n\t\t\tnode.className = c;\n\t\t\tc = ( obj.state.selected ? ' jstree-clicked' : '') + ( obj.state.disabled ? ' jstree-disabled' : '');\n\t\t\tfor(j in obj.a_attr) {\n\t\t\t\tif(obj.a_attr.hasOwnProperty(j)) {\n\t\t\t\t\tif(j === 'href' && obj.a_attr[j] === '#') { continue; }\n\t\t\t\t\tif(j !== 'class') {\n\t\t\t\t\t\tnode.childNodes[1].setAttribute(j, obj.a_attr[j]);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tc += ' ' + obj.a_attr[j];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(c.length) {\n\t\t\t\tnode.childNodes[1].className = 'jstree-anchor ' + c;\n\t\t\t}\n\t\t\tif((obj.icon && obj.icon !== true) || obj.icon === false) {\n\t\t\t\tif(obj.icon === false) {\n\t\t\t\t\tnode.childNodes[1].childNodes[0].className += ' jstree-themeicon-hidden';\n\t\t\t\t}\n\t\t\t\telse if(obj.icon.indexOf('/') === -1 && obj.icon.indexOf('.') === -1) {\n\t\t\t\t\tnode.childNodes[1].childNodes[0].className += ' ' + obj.icon + ' jstree-themeicon-custom';\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tnode.childNodes[1].childNodes[0].style.backgroundImage = 'url(\"'+obj.icon+'\")';\n\t\t\t\t\tnode.childNodes[1].childNodes[0].style.backgroundPosition = 'center center';\n\t\t\t\t\tnode.childNodes[1].childNodes[0].style.backgroundSize = 'auto';\n\t\t\t\t\tnode.childNodes[1].childNodes[0].className += ' jstree-themeicon-custom';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif(this.settings.core.force_text) {\n\t\t\t\tnode.childNodes[1].appendChild(d.createTextNode(obj.text));\n\t\t\t}\n\t\t\telse {\n\t\t\t\tnode.childNodes[1].innerHTML += obj.text;\n\t\t\t}\n\n\n\t\t\tif(deep && obj.children.length && (obj.state.opened || force_render) && obj.state.loaded) {\n\t\t\t\tk = d.createElement('UL');\n\t\t\t\tk.setAttribute('role', 'group');\n\t\t\t\tk.className = 'jstree-children';\n\t\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\t\tk.appendChild(this.redraw_node(obj.children[i], deep, true));\n\t\t\t\t}\n\t\t\t\tnode.appendChild(k);\n\t\t\t}\n\t\t\tif(old) {\n\t\t\t\tnode.appendChild(old);\n\t\t\t}\n\t\t\tif(!is_callback) {\n\t\t\t\t// append back using par / ind\n\t\t\t\tif(!par) {\n\t\t\t\t\tpar = this.element[0];\n\t\t\t\t}\n\t\t\t\tfor(i = 0, j = par.childNodes.length; i < j; i++) {\n\t\t\t\t\tif(par.childNodes[i] && par.childNodes[i].className && par.childNodes[i].className.indexOf('jstree-children') !== -1) {\n\t\t\t\t\t\ttmp = par.childNodes[i];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(!tmp) {\n\t\t\t\t\ttmp = d.createElement('UL');\n\t\t\t\t\ttmp.setAttribute('role', 'group');\n\t\t\t\t\ttmp.className = 'jstree-children';\n\t\t\t\t\tpar.appendChild(tmp);\n\t\t\t\t}\n\t\t\t\tpar = tmp;\n\n\t\t\t\tif(ind < par.childNodes.length) {\n\t\t\t\t\tpar.insertBefore(node, par.childNodes[ind]);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tpar.appendChild(node);\n\t\t\t\t}\n\t\t\t\tif(f) {\n\t\t\t\t\tt = this.element[0].scrollTop;\n\t\t\t\t\tl = this.element[0].scrollLeft;\n\t\t\t\t\tnode.childNodes[1].focus();\n\t\t\t\t\tthis.element[0].scrollTop = t;\n\t\t\t\t\tthis.element[0].scrollLeft = l;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(obj.state.opened && !obj.state.loaded) {\n\t\t\t\tobj.state.opened = false;\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthis.open_node(obj.id, false, 0);\n\t\t\t\t}.bind(this), 0);\n\t\t\t}\n\t\t\treturn node;\n\t\t},\n\t\t/**\n\t\t * opens a node, revealing its children. If the node is not loaded it will be loaded and opened once ready.\n\t\t * @name open_node(obj [, callback, animation])\n\t\t * @param {mixed} obj the node to open\n\t\t * @param {Function} callback a function to execute once the node is opened\n\t\t * @param {Number} animation the animation duration in milliseconds when opening the node (overrides the `core.animation` setting). Use `false` for no animation.\n\t\t * @trigger open_node.jstree, after_open.jstree, before_open.jstree\n\t\t */\n\t\topen_node : function (obj, callback, animation) {\n\t\t\tvar t1, t2, d, t;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.open_node(obj[t1], callback, animation);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tanimation = animation === undefined ? this.settings.core.animation : animation;\n\t\t\tif(!this.is_closed(obj)) {\n\t\t\t\tif(callback) {\n\t\t\t\t\tcallback.call(this, obj, false);\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(!this.is_loaded(obj)) {\n\t\t\t\tif(this.is_loading(obj)) {\n\t\t\t\t\treturn setTimeout(function () {\n\t\t\t\t\t\tthis.open_node(obj, callback, animation);\n\t\t\t\t\t}.bind(this), 500);\n\t\t\t\t}\n\t\t\t\tthis.load_node(obj, function (o, ok) {\n\t\t\t\t\treturn ok ? this.open_node(o, callback, animation) : (callback ? callback.call(this, o, false) : false);\n\t\t\t\t});\n\t\t\t}\n\t\t\telse {\n\t\t\t\td = this.get_node(obj, true);\n\t\t\t\tt = this;\n\t\t\t\tif(d.length) {\n\t\t\t\t\tif(animation && d.children(\".jstree-children\").length) {\n\t\t\t\t\t\td.children(\".jstree-children\").stop(true, true);\n\t\t\t\t\t}\n\t\t\t\t\tif(obj.children.length && !this._firstChild(d.children('.jstree-children')[0])) {\n\t\t\t\t\t\tthis.draw_children(obj);\n\t\t\t\t\t\t//d = this.get_node(obj, true);\n\t\t\t\t\t}\n\t\t\t\t\tif(!animation) {\n\t\t\t\t\t\tthis.trigger('before_open', { \"node\" : obj });\n\t\t\t\t\t\td[0].className = d[0].className.replace('jstree-closed', 'jstree-open');\n\t\t\t\t\t\td[0].childNodes[1].setAttribute(\"aria-expanded\", true);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.trigger('before_open', { \"node\" : obj });\n\t\t\t\t\t\td\n\t\t\t\t\t\t\t.children(\".jstree-children\").css(\"display\",\"none\").end()\n\t\t\t\t\t\t\t.removeClass(\"jstree-closed\").addClass(\"jstree-open\")\n\t\t\t\t\t\t\t\t.children('.jstree-anchor').attr(\"aria-expanded\", true).end()\n\t\t\t\t\t\t\t.children(\".jstree-children\").stop(true, true)\n\t\t\t\t\t\t\t\t.slideDown(animation, function () {\n\t\t\t\t\t\t\t\t\tthis.style.display = \"\";\n\t\t\t\t\t\t\t\t\tif (t.element) {\n\t\t\t\t\t\t\t\t\t\tt.trigger(\"after_open\", { \"node\" : obj });\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tobj.state.opened = true;\n\t\t\t\tif(callback) {\n\t\t\t\t\tcallback.call(this, obj, true);\n\t\t\t\t}\n\t\t\t\tif(!d.length) {\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered when a node is about to be opened (if the node is supposed to be in the DOM, it will be, but it won't be visible yet)\n\t\t\t\t\t * @event\n\t\t\t\t\t * @name before_open.jstree\n\t\t\t\t\t * @param {Object} node the opened node\n\t\t\t\t\t */\n\t\t\t\t\tthis.trigger('before_open', { \"node\" : obj });\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when a node is opened (if there is an animation it will not be completed yet)\n\t\t\t\t * @event\n\t\t\t\t * @name open_node.jstree\n\t\t\t\t * @param {Object} node the opened node\n\t\t\t\t */\n\t\t\t\tthis.trigger('open_node', { \"node\" : obj });\n\t\t\t\tif(!animation || !d.length) {\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered when a node is opened and the animation is complete\n\t\t\t\t\t * @event\n\t\t\t\t\t * @name after_open.jstree\n\t\t\t\t\t * @param {Object} node the opened node\n\t\t\t\t\t */\n\t\t\t\t\tthis.trigger(\"after_open\", { \"node\" : obj });\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * opens every parent of a node (node should be loaded)\n\t\t * @name _open_to(obj)\n\t\t * @param {mixed} obj the node to reveal\n\t\t * @private\n\t\t */\n\t\t_open_to : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tvar i, j, p = obj.parents;\n\t\t\tfor(i = 0, j = p.length; i < j; i+=1) {\n\t\t\t\tif(i !== $.jstree.root) {\n\t\t\t\t\tthis.open_node(p[i], false, 0);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn $('#' + obj.id.replace($.jstree.idregex,'\\\\$&'), this.element);\n\t\t},\n\t\t/**\n\t\t * closes a node, hiding its children\n\t\t * @name close_node(obj [, animation])\n\t\t * @param {mixed} obj the node to close\n\t\t * @param {Number} animation the animation duration in milliseconds when closing the node (overrides the `core.animation` setting). Use `false` for no animation.\n\t\t * @trigger close_node.jstree, after_close.jstree\n\t\t */\n\t\tclose_node : function (obj, animation) {\n\t\t\tvar t1, t2, t, d;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.close_node(obj[t1], animation);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(this.is_closed(obj)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tanimation = animation === undefined ? this.settings.core.animation : animation;\n\t\t\tt = this;\n\t\t\td = this.get_node(obj, true);\n\n\t\t\tobj.state.opened = false;\n\t\t\t/**\n\t\t\t * triggered when a node is closed (if there is an animation it will not be complete yet)\n\t\t\t * @event\n\t\t\t * @name close_node.jstree\n\t\t\t * @param {Object} node the closed node\n\t\t\t */\n\t\t\tthis.trigger('close_node',{ \"node\" : obj });\n\t\t\tif(!d.length) {\n\t\t\t\t/**\n\t\t\t\t * triggered when a node is closed and the animation is complete\n\t\t\t\t * @event\n\t\t\t\t * @name after_close.jstree\n\t\t\t\t * @param {Object} node the closed node\n\t\t\t\t */\n\t\t\t\tthis.trigger(\"after_close\", { \"node\" : obj });\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif(!animation) {\n\t\t\t\t\td[0].className = d[0].className.replace('jstree-open', 'jstree-closed');\n\t\t\t\t\td.children('.jstree-anchor').attr(\"aria-expanded\", false);\n\t\t\t\t\td.children('.jstree-children').remove();\n\t\t\t\t\tthis.trigger(\"after_close\", { \"node\" : obj });\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\td\n\t\t\t\t\t\t.children(\".jstree-children\").attr(\"style\",\"display:block !important\").end()\n\t\t\t\t\t\t.removeClass(\"jstree-open\").addClass(\"jstree-closed\")\n\t\t\t\t\t\t\t.children('.jstree-anchor').attr(\"aria-expanded\", false).end()\n\t\t\t\t\t\t.children(\".jstree-children\").stop(true, true).slideUp(animation, function () {\n\t\t\t\t\t\t\tthis.style.display = \"\";\n\t\t\t\t\t\t\td.children('.jstree-children').remove();\n\t\t\t\t\t\t\tif (t.element) {\n\t\t\t\t\t\t\t\tt.trigger(\"after_close\", { \"node\" : obj });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * toggles a node - closing it if it is open, opening it if it is closed\n\t\t * @name toggle_node(obj)\n\t\t * @param {mixed} obj the node to toggle\n\t\t */\n\t\ttoggle_node : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.toggle_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif(this.is_closed(obj)) {\n\t\t\t\treturn this.open_node(obj);\n\t\t\t}\n\t\t\tif(this.is_open(obj)) {\n\t\t\t\treturn this.close_node(obj);\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * opens all nodes within a node (or the tree), revealing their children. If the node is not loaded it will be loaded and opened once ready.\n\t\t * @name open_all([obj, animation, original_obj])\n\t\t * @param {mixed} obj the node to open recursively, omit to open all nodes in the tree\n\t\t * @param {Number} animation the animation duration in milliseconds when opening the nodes, the default is no animation\n\t\t * @param {jQuery} reference to the node that started the process (internal use)\n\t\t * @trigger open_all.jstree\n\t\t */\n\t\topen_all : function (obj, animation, original_obj) {\n\t\t\tif(!obj) { obj = $.jstree.root; }\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar dom = obj.id === $.jstree.root ? this.get_container_ul() : this.get_node(obj, true), i, j, _this;\n\t\t\tif(!dom.length) {\n\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\tif(this.is_closed(this._model.data[obj.children_d[i]])) {\n\t\t\t\t\t\tthis._model.data[obj.children_d[i]].state.opened = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this.trigger('open_all', { \"node\" : obj });\n\t\t\t}\n\t\t\toriginal_obj = original_obj || dom;\n\t\t\t_this = this;\n\t\t\tdom = this.is_closed(obj) ? dom.find('.jstree-closed').addBack() : dom.find('.jstree-closed');\n\t\t\tdom.each(function () {\n\t\t\t\t_this.open_node(\n\t\t\t\t\tthis,\n\t\t\t\t\tfunction(node, status) { if(status && this.is_parent(node)) { this.open_all(node, animation, original_obj); } },\n\t\t\t\t\tanimation || 0\n\t\t\t\t);\n\t\t\t});\n\t\t\tif(original_obj.find('.jstree-closed').length === 0) {\n\t\t\t\t/**\n\t\t\t\t * triggered when an `open_all` call completes\n\t\t\t\t * @event\n\t\t\t\t * @name open_all.jstree\n\t\t\t\t * @param {Object} node the opened node\n\t\t\t\t */\n\t\t\t\tthis.trigger('open_all', { \"node\" : this.get_node(original_obj) });\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * closes all nodes within a node (or the tree), revealing their children\n\t\t * @name close_all([obj, animation])\n\t\t * @param {mixed} obj the node to close recursively, omit to close all nodes in the tree\n\t\t * @param {Number} animation the animation duration in milliseconds when closing the nodes, the default is no animation\n\t\t * @trigger close_all.jstree\n\t\t */\n\t\tclose_all : function (obj, animation) {\n\t\t\tif(!obj) { obj = $.jstree.root; }\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar dom = obj.id === $.jstree.root ? this.get_container_ul() : this.get_node(obj, true),\n\t\t\t\t_this = this, i, j;\n\t\t\tif(dom.length) {\n\t\t\t\tdom = this.is_open(obj) ? dom.find('.jstree-open').addBack() : dom.find('.jstree-open');\n\t\t\t\t$(dom.get().reverse()).each(function () { _this.close_node(this, animation || 0); });\n\t\t\t}\n\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\tthis._model.data[obj.children_d[i]].state.opened = false;\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when an `close_all` call completes\n\t\t\t * @event\n\t\t\t * @name close_all.jstree\n\t\t\t * @param {Object} node the closed node\n\t\t\t */\n\t\t\tthis.trigger('close_all', { \"node\" : obj });\n\t\t},\n\t\t/**\n\t\t * checks if a node is disabled (not selectable)\n\t\t * @name is_disabled(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_disabled : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state && obj.state.disabled;\n\t\t},\n\t\t/**\n\t\t * enables a node - so that it can be selected\n\t\t * @name enable_node(obj)\n\t\t * @param {mixed} obj the node to enable\n\t\t * @trigger enable_node.jstree\n\t\t */\n\t\tenable_node : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.enable_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.state.disabled = false;\n\t\t\tthis.get_node(obj,true).children('.jstree-anchor').removeClass('jstree-disabled').attr('aria-disabled', false);\n\t\t\t/**\n\t\t\t * triggered when an node is enabled\n\t\t\t * @event\n\t\t\t * @name enable_node.jstree\n\t\t\t * @param {Object} node the enabled node\n\t\t\t */\n\t\t\tthis.trigger('enable_node', { 'node' : obj });\n\t\t},\n\t\t/**\n\t\t * disables a node - so that it can not be selected\n\t\t * @name disable_node(obj)\n\t\t * @param {mixed} obj the node to disable\n\t\t * @trigger disable_node.jstree\n\t\t */\n\t\tdisable_node : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.disable_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.state.disabled = true;\n\t\t\tthis.get_node(obj,true).children('.jstree-anchor').addClass('jstree-disabled').attr('aria-disabled', true);\n\t\t\t/**\n\t\t\t * triggered when an node is disabled\n\t\t\t * @event\n\t\t\t * @name disable_node.jstree\n\t\t\t * @param {Object} node the disabled node\n\t\t\t */\n\t\t\tthis.trigger('disable_node', { 'node' : obj });\n\t\t},\n\t\t/**\n\t\t * determines if a node is hidden\n\t\t * @name is_hidden(obj)\n\t\t * @param {mixed} obj the node\n\t\t */\n\t\tis_hidden : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj.state.hidden === true;\n\t\t},\n\t\t/**\n\t\t * hides a node - it is still in the structure but will not be visible\n\t\t * @name hide_node(obj)\n\t\t * @param {mixed} obj the node to hide\n\t\t * @param {Boolean} skip_redraw internal parameter controlling if redraw is called\n\t\t * @trigger hide_node.jstree\n\t\t */\n\t\thide_node : function (obj, skip_redraw) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.hide_node(obj[t1], true);\n\t\t\t\t}\n\t\t\t\tif (!skip_redraw) {\n\t\t\t\t\tthis.redraw();\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(!obj.state.hidden) {\n\t\t\t\tobj.state.hidden = true;\n\t\t\t\tthis._node_changed(obj.parent);\n\t\t\t\tif(!skip_redraw) {\n\t\t\t\t\tthis.redraw();\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node is hidden\n\t\t\t\t * @event\n\t\t\t\t * @name hide_node.jstree\n\t\t\t\t * @param {Object} node the hidden node\n\t\t\t\t */\n\t\t\t\tthis.trigger('hide_node', { 'node' : obj });\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * shows a node\n\t\t * @name show_node(obj)\n\t\t * @param {mixed} obj the node to show\n\t\t * @param {Boolean} skip_redraw internal parameter controlling if redraw is called\n\t\t * @trigger show_node.jstree\n\t\t */\n\t\tshow_node : function (obj, skip_redraw) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.show_node(obj[t1], true);\n\t\t\t\t}\n\t\t\t\tif (!skip_redraw) {\n\t\t\t\t\tthis.redraw();\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(obj.state.hidden) {\n\t\t\t\tobj.state.hidden = false;\n\t\t\t\tthis._node_changed(obj.parent);\n\t\t\t\tif(!skip_redraw) {\n\t\t\t\t\tthis.redraw();\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node is shown\n\t\t\t\t * @event\n\t\t\t\t * @name show_node.jstree\n\t\t\t\t * @param {Object} node the shown node\n\t\t\t\t */\n\t\t\t\tthis.trigger('show_node', { 'node' : obj });\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * hides all nodes\n\t\t * @name hide_all()\n\t\t * @trigger hide_all.jstree\n\t\t */\n\t\thide_all : function (skip_redraw) {\n\t\t\tvar i, m = this._model.data, ids = [];\n\t\t\tfor(i in m) {\n\t\t\t\tif(m.hasOwnProperty(i) && i !== $.jstree.root && !m[i].state.hidden) {\n\t\t\t\t\tm[i].state.hidden = true;\n\t\t\t\t\tids.push(i);\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._model.force_full_redraw = true;\n\t\t\tif(!skip_redraw) {\n\t\t\t\tthis.redraw();\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when all nodes are hidden\n\t\t\t * @event\n\t\t\t * @name hide_all.jstree\n\t\t\t * @param {Array} nodes the IDs of all hidden nodes\n\t\t\t */\n\t\t\tthis.trigger('hide_all', { 'nodes' : ids });\n\t\t\treturn ids;\n\t\t},\n\t\t/**\n\t\t * shows all nodes\n\t\t * @name show_all()\n\t\t * @trigger show_all.jstree\n\t\t */\n\t\tshow_all : function (skip_redraw) {\n\t\t\tvar i, m = this._model.data, ids = [];\n\t\t\tfor(i in m) {\n\t\t\t\tif(m.hasOwnProperty(i) && i !== $.jstree.root && m[i].state.hidden) {\n\t\t\t\t\tm[i].state.hidden = false;\n\t\t\t\t\tids.push(i);\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._model.force_full_redraw = true;\n\t\t\tif(!skip_redraw) {\n\t\t\t\tthis.redraw();\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when all nodes are shown\n\t\t\t * @event\n\t\t\t * @name show_all.jstree\n\t\t\t * @param {Array} nodes the IDs of all shown nodes\n\t\t\t */\n\t\t\tthis.trigger('show_all', { 'nodes' : ids });\n\t\t\treturn ids;\n\t\t},\n\t\t/**\n\t\t * called when a node is selected by the user. Used internally.\n\t\t * @private\n\t\t * @name activate_node(obj, e)\n\t\t * @param {mixed} obj the node\n\t\t * @param {Object} e the related event\n\t\t * @trigger activate_node.jstree, changed.jstree\n\t\t */\n\t\tactivate_node : function (obj, e) {\n\t\t\tif(this.is_disabled(obj)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(!e || typeof e !== 'object') {\n\t\t\t\te = {};\n\t\t\t}\n\n\t\t\t// ensure last_clicked is still in the DOM, make it fresh (maybe it was moved?) and make sure it is still selected, if not - make last_clicked the last selected node\n\t\t\tthis._data.core.last_clicked = this._data.core.last_clicked && this._data.core.last_clicked.id !== undefined ? this.get_node(this._data.core.last_clicked.id) : null;\n\t\t\tif(this._data.core.last_clicked && !this._data.core.last_clicked.state.selected) { this._data.core.last_clicked = null; }\n\t\t\tif(!this._data.core.last_clicked && this._data.core.selected.length) { this._data.core.last_clicked = this.get_node(this._data.core.selected[this._data.core.selected.length - 1]); }\n\n\t\t\tif(!this.settings.core.multiple || (!e.metaKey && !e.ctrlKey && !e.shiftKey) || (e.shiftKey && (!this._data.core.last_clicked || !this.get_parent(obj) || this.get_parent(obj) !== this._data.core.last_clicked.parent ) )) {\n\t\t\t\tif(!this.settings.core.multiple && (e.metaKey || e.ctrlKey || e.shiftKey) && this.is_selected(obj)) {\n\t\t\t\t\tthis.deselect_node(obj, false, e);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tif (this.settings.core.allow_reselect || !this.is_selected(obj) || this._data.core.selected.length !== 1) {\n\t\t\t\t\t\tthis.deselect_all(true);\n\t\t\t\t\t\tthis.select_node(obj, false, false, e);\n\t\t\t\t\t}\n\t\t\t\t\tthis._data.core.last_clicked = this.get_node(obj);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif(e.shiftKey) {\n\t\t\t\t\tvar o = this.get_node(obj).id,\n\t\t\t\t\t\tl = this._data.core.last_clicked.id,\n\t\t\t\t\t\tp = this.get_node(this._data.core.last_clicked.parent).children,\n\t\t\t\t\t\tc = false,\n\t\t\t\t\t\ti, j;\n\t\t\t\t\tfor(i = 0, j = p.length; i < j; i += 1) {\n\t\t\t\t\t\t// separate IFs work whem o and l are the same\n\t\t\t\t\t\tif(p[i] === o) {\n\t\t\t\t\t\t\tc = !c;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(p[i] === l) {\n\t\t\t\t\t\t\tc = !c;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(!this.is_disabled(p[i]) && (c || p[i] === o || p[i] === l)) {\n\t\t\t\t\t\t\tif (!this.is_hidden(p[i])) {\n\t\t\t\t\t\t\t\tthis.select_node(p[i], true, false, e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif (!e.ctrlKey) {\n\t\t\t\t\t\t\t\tthis.deselect_node(p[i], true, e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.trigger('changed', { 'action' : 'select_node', 'node' : this.get_node(obj), 'selected' : this._data.core.selected, 'event' : e });\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tif(!this.is_selected(obj)) {\n\t\t\t\t\t\tif (e.ctrlKey) {\n\t\t\t\t\t\t\tthis._data.core.last_clicked = this.get_node(obj);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.select_node(obj, false, false, e);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.deselect_node(obj, false, e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when an node is clicked or intercated with by the user\n\t\t\t * @event\n\t\t\t * @name activate_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {Object} event the ooriginal event (if any) which triggered the call (may be an empty object)\n\t\t\t */\n\t\t\tthis.trigger('activate_node', { 'node' : this.get_node(obj), 'event' : e });\n\t\t},\n\t\t/**\n\t\t * applies the hover state on a node, called when a node is hovered by the user. Used internally.\n\t\t * @private\n\t\t * @name hover_node(obj)\n\t\t * @param {mixed} obj\n\t\t * @trigger hover_node.jstree\n\t\t */\n\t\thover_node : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(!obj || !obj.length || obj.children('.jstree-hovered').length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tvar o = this.element.find('.jstree-hovered'), t = this.element;\n\t\t\tif(o && o.length) { this.dehover_node(o); }\n\n\t\t\tobj.children('.jstree-anchor').addClass('jstree-hovered');\n\t\t\t/**\n\t\t\t * triggered when an node is hovered\n\t\t\t * @event\n\t\t\t * @name hover_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t */\n\t\t\tthis.trigger('hover_node', { 'node' : this.get_node(obj) });\n\t\t\tsetTimeout(function () { t.attr('aria-activedescendant', obj[0].id); }, 0);\n\t\t},\n\t\t/**\n\t\t * removes the hover state from a nodecalled when a node is no longer hovered by the user. Used internally.\n\t\t * @private\n\t\t * @name dehover_node(obj)\n\t\t * @param {mixed} obj\n\t\t * @trigger dehover_node.jstree\n\t\t */\n\t\tdehover_node : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(!obj || !obj.length || !obj.children('.jstree-hovered').length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.children('.jstree-anchor').removeClass('jstree-hovered');\n\t\t\t/**\n\t\t\t * triggered when an node is no longer hovered\n\t\t\t * @event\n\t\t\t * @name dehover_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t */\n\t\t\tthis.trigger('dehover_node', { 'node' : this.get_node(obj) });\n\t\t},\n\t\t/**\n\t\t * select a node\n\t\t * @name select_node(obj [, supress_event, prevent_open])\n\t\t * @param {mixed} obj an array can be used to select multiple nodes\n\t\t * @param {Boolean} supress_event if set to `true` the `changed.jstree` event won't be triggered\n\t\t * @param {Boolean} prevent_open if set to `true` parents of the selected node won't be opened\n\t\t * @trigger select_node.jstree, changed.jstree\n\t\t */\n\t\tselect_node : function (obj, supress_event, prevent_open, e) {\n\t\t\tvar dom, t1, t2, th;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.select_node(obj[t1], supress_event, prevent_open, e);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(!obj.state.selected) {\n\t\t\t\tobj.state.selected = true;\n\t\t\t\tthis._data.core.selected.push(obj.id);\n\t\t\t\tif(!prevent_open) {\n\t\t\t\t\tdom = this._open_to(obj);\n\t\t\t\t}\n\t\t\t\tif(dom && dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').addClass('jstree-clicked').attr('aria-selected', true);\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node is selected\n\t\t\t\t * @event\n\t\t\t\t * @name select_node.jstree\n\t\t\t\t * @param {Object} node\n\t\t\t\t * @param {Array} selected the current selection\n\t\t\t\t * @param {Object} event the event (if any) that triggered this select_node\n\t\t\t\t */\n\t\t\t\tthis.trigger('select_node', { 'node' : obj, 'selected' : this._data.core.selected, 'event' : e });\n\t\t\t\tif(!supress_event) {\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered when selection changes\n\t\t\t\t\t * @event\n\t\t\t\t\t * @name changed.jstree\n\t\t\t\t\t * @param {Object} node\n\t\t\t\t\t * @param {Object} action the action that caused the selection to change\n\t\t\t\t\t * @param {Array} selected the current selection\n\t\t\t\t\t * @param {Object} event the event (if any) that triggered this changed event\n\t\t\t\t\t */\n\t\t\t\t\tthis.trigger('changed', { 'action' : 'select_node', 'node' : obj, 'selected' : this._data.core.selected, 'event' : e });\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * deselect a node\n\t\t * @name deselect_node(obj [, supress_event])\n\t\t * @param {mixed} obj an array can be used to deselect multiple nodes\n\t\t * @param {Boolean} supress_event if set to `true` the `changed.jstree` event won't be triggered\n\t\t * @trigger deselect_node.jstree, changed.jstree\n\t\t */\n\t\tdeselect_node : function (obj, supress_event, e) {\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.deselect_node(obj[t1], supress_event, e);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(obj.state.selected) {\n\t\t\t\tobj.state.selected = false;\n\t\t\t\tthis._data.core.selected = $.vakata.array_remove_item(this._data.core.selected, obj.id);\n\t\t\t\tif(dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').removeClass('jstree-clicked').attr('aria-selected', false);\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node is deselected\n\t\t\t\t * @event\n\t\t\t\t * @name deselect_node.jstree\n\t\t\t\t * @param {Object} node\n\t\t\t\t * @param {Array} selected the current selection\n\t\t\t\t * @param {Object} event the event (if any) that triggered this deselect_node\n\t\t\t\t */\n\t\t\t\tthis.trigger('deselect_node', { 'node' : obj, 'selected' : this._data.core.selected, 'event' : e });\n\t\t\t\tif(!supress_event) {\n\t\t\t\t\tthis.trigger('changed', { 'action' : 'deselect_node', 'node' : obj, 'selected' : this._data.core.selected, 'event' : e });\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * select all nodes in the tree\n\t\t * @name select_all([supress_event])\n\t\t * @param {Boolean} supress_event if set to `true` the `changed.jstree` event won't be triggered\n\t\t * @trigger select_all.jstree, changed.jstree\n\t\t */\n\t\tselect_all : function (supress_event) {\n\t\t\tvar tmp = this._data.core.selected.concat([]), i, j;\n\t\t\tthis._data.core.selected = this._model.data[$.jstree.root].children_d.concat();\n\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\tif(this._model.data[this._data.core.selected[i]]) {\n\t\t\t\t\tthis._model.data[this._data.core.selected[i]].state.selected = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.redraw(true);\n\t\t\t/**\n\t\t\t * triggered when all nodes are selected\n\t\t\t * @event\n\t\t\t * @name select_all.jstree\n\t\t\t * @param {Array} selected the current selection\n\t\t\t */\n\t\t\tthis.trigger('select_all', { 'selected' : this._data.core.selected });\n\t\t\tif(!supress_event) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'select_all', 'selected' : this._data.core.selected, 'old_selection' : tmp });\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * deselect all selected nodes\n\t\t * @name deselect_all([supress_event])\n\t\t * @param {Boolean} supress_event if set to `true` the `changed.jstree` event won't be triggered\n\t\t * @trigger deselect_all.jstree, changed.jstree\n\t\t */\n\t\tdeselect_all : function (supress_event) {\n\t\t\tvar tmp = this._data.core.selected.concat([]), i, j;\n\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\tif(this._model.data[this._data.core.selected[i]]) {\n\t\t\t\t\tthis._model.data[this._data.core.selected[i]].state.selected = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._data.core.selected = [];\n\t\t\tthis.element.find('.jstree-clicked').removeClass('jstree-clicked').attr('aria-selected', false);\n\t\t\t/**\n\t\t\t * triggered when all nodes are deselected\n\t\t\t * @event\n\t\t\t * @name deselect_all.jstree\n\t\t\t * @param {Object} node the previous selection\n\t\t\t * @param {Array} selected the current selection\n\t\t\t */\n\t\t\tthis.trigger('deselect_all', { 'selected' : this._data.core.selected, 'node' : tmp });\n\t\t\tif(!supress_event) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'deselect_all', 'selected' : this._data.core.selected, 'old_selection' : tmp });\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * checks if a node is selected\n\t\t * @name is_selected(obj)\n\t\t * @param  {mixed}  obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tis_selected : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn obj.state.selected;\n\t\t},\n\t\t/**\n\t\t * get an array of all selected nodes\n\t\t * @name get_selected([full])\n\t\t * @param  {mixed}  full if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t */\n\t\tget_selected : function (full) {\n\t\t\treturn full ? $.map(this._data.core.selected, function (i) { return this.get_node(i); }.bind(this)) : this._data.core.selected.slice();\n\t\t},\n\t\t/**\n\t\t * get an array of all top level selected nodes (ignoring children of selected nodes)\n\t\t * @name get_top_selected([full])\n\t\t * @param  {mixed}  full if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t */\n\t\tget_top_selected : function (full) {\n\t\t\tvar tmp = this.get_selected(true),\n\t\t\t\tobj = {}, i, j, k, l;\n\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\tobj[tmp[i].id] = tmp[i];\n\t\t\t}\n\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\tfor(k = 0, l = tmp[i].children_d.length; k < l; k++) {\n\t\t\t\t\tif(obj[tmp[i].children_d[k]]) {\n\t\t\t\t\t\tdelete obj[tmp[i].children_d[k]];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = [];\n\t\t\tfor(i in obj) {\n\t\t\t\tif(obj.hasOwnProperty(i)) {\n\t\t\t\t\ttmp.push(i);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn full ? $.map(tmp, function (i) { return this.get_node(i); }.bind(this)) : tmp;\n\t\t},\n\t\t/**\n\t\t * get an array of all bottom level selected nodes (ignoring selected parents)\n\t\t * @name get_bottom_selected([full])\n\t\t * @param  {mixed}  full if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t */\n\t\tget_bottom_selected : function (full) {\n\t\t\tvar tmp = this.get_selected(true),\n\t\t\t\tobj = [], i, j;\n\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\tif(!tmp[i].children.length) {\n\t\t\t\t\tobj.push(tmp[i].id);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn full ? $.map(obj, function (i) { return this.get_node(i); }.bind(this)) : obj;\n\t\t},\n\t\t/**\n\t\t * gets the current state of the tree so that it can be restored later with `set_state(state)`. Used internally.\n\t\t * @name get_state()\n\t\t * @private\n\t\t * @return {Object}\n\t\t */\n\t\tget_state : function () {\n\t\t\tvar state\t= {\n\t\t\t\t'core' : {\n\t\t\t\t\t'open' : [],\n\t\t\t\t\t'loaded' : [],\n\t\t\t\t\t'scroll' : {\n\t\t\t\t\t\t'left' : this.element.scrollLeft(),\n\t\t\t\t\t\t'top' : this.element.scrollTop()\n\t\t\t\t\t},\n\t\t\t\t\t/*!\n\t\t\t\t\t'themes' : {\n\t\t\t\t\t\t'name' : this.get_theme(),\n\t\t\t\t\t\t'icons' : this._data.core.themes.icons,\n\t\t\t\t\t\t'dots' : this._data.core.themes.dots\n\t\t\t\t\t},\n\t\t\t\t\t*/\n\t\t\t\t\t'selected' : []\n\t\t\t\t}\n\t\t\t}, i;\n\t\t\tfor(i in this._model.data) {\n\t\t\t\tif(this._model.data.hasOwnProperty(i)) {\n\t\t\t\t\tif(i !== $.jstree.root) {\n\t\t\t\t\t\tif(this._model.data[i].state.loaded && this.settings.core.loaded_state) {\n\t\t\t\t\t\t\tstate.core.loaded.push(i);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(this._model.data[i].state.opened) {\n\t\t\t\t\t\t\tstate.core.open.push(i);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(this._model.data[i].state.selected) {\n\t\t\t\t\t\t\tstate.core.selected.push(i);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn state;\n\t\t},\n\t\t/**\n\t\t * sets the state of the tree. Used internally.\n\t\t * @name set_state(state [, callback])\n\t\t * @private\n\t\t * @param {Object} state the state to restore. Keep in mind this object is passed by reference and jstree will modify it.\n\t\t * @param {Function} callback an optional function to execute once the state is restored.\n\t\t * @trigger set_state.jstree\n\t\t */\n\t\tset_state : function (state, callback) {\n\t\t\tif(state) {\n\t\t\t\tif(state.core && state.core.selected && state.core.initial_selection === undefined) {\n\t\t\t\t\tstate.core.initial_selection = this._data.core.selected.concat([]).sort().join(',');\n\t\t\t\t}\n\t\t\t\tif(state.core) {\n\t\t\t\t\tvar res, n, t, _this, i;\n\t\t\t\t\tif(state.core.loaded) {\n\t\t\t\t\t\tif(!this.settings.core.loaded_state || !$.vakata.is_array(state.core.loaded) || !state.core.loaded.length) {\n\t\t\t\t\t\t\tdelete state.core.loaded;\n\t\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis._load_nodes(state.core.loaded, function (nodes) {\n\t\t\t\t\t\t\t\tdelete state.core.loaded;\n\t\t\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif(state.core.open) {\n\t\t\t\t\t\tif(!$.vakata.is_array(state.core.open) || !state.core.open.length) {\n\t\t\t\t\t\t\tdelete state.core.open;\n\t\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis._load_nodes(state.core.open, function (nodes) {\n\t\t\t\t\t\t\t\tthis.open_node(nodes, false, 0);\n\t\t\t\t\t\t\t\tdelete state.core.open;\n\t\t\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif(state.core.scroll) {\n\t\t\t\t\t\tif(state.core.scroll && state.core.scroll.left !== undefined) {\n\t\t\t\t\t\t\tthis.element.scrollLeft(state.core.scroll.left);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(state.core.scroll && state.core.scroll.top !== undefined) {\n\t\t\t\t\t\t\tthis.element.scrollTop(state.core.scroll.top);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdelete state.core.scroll;\n\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif(state.core.selected) {\n\t\t\t\t\t\t_this = this;\n\t\t\t\t\t\tif (state.core.initial_selection === undefined ||\n\t\t\t\t\t\t\tstate.core.initial_selection === this._data.core.selected.concat([]).sort().join(',')\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tthis.deselect_all();\n\t\t\t\t\t\t\t$.each(state.core.selected, function (i, v) {\n\t\t\t\t\t\t\t\t_this.select_node(v, false, true);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdelete state.core.initial_selection;\n\t\t\t\t\t\tdelete state.core.selected;\n\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tfor(i in state) {\n\t\t\t\t\t\tif(state.hasOwnProperty(i) && i !== \"core\" && $.inArray(i, this.settings.plugins) === -1) {\n\t\t\t\t\t\t\tdelete state[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif($.isEmptyObject(state.core)) {\n\t\t\t\t\t\tdelete state.core;\n\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif($.isEmptyObject(state)) {\n\t\t\t\t\tstate = null;\n\t\t\t\t\tif(callback) { callback.call(this); }\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered when a `set_state` call completes\n\t\t\t\t\t * @event\n\t\t\t\t\t * @name set_state.jstree\n\t\t\t\t\t */\n\t\t\t\t\tthis.trigger('set_state');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\t/**\n\t\t * refreshes the tree - all nodes are reloaded with calls to `load_node`.\n\t\t * @name refresh()\n\t\t * @param {Boolean} skip_loading an option to skip showing the loading indicator\n\t\t * @param {Mixed} forget_state if set to `true` state will not be reapplied, if set to a function (receiving the current state as argument) the result of that function will be used as state\n\t\t * @trigger refresh.jstree\n\t\t */\n\t\trefresh : function (skip_loading, forget_state) {\n\t\t\tthis._data.core.state = forget_state === true ? {} : this.get_state();\n\t\t\tif(forget_state && $.vakata.is_function(forget_state)) { this._data.core.state = forget_state.call(this, this._data.core.state); }\n\t\t\tthis._cnt = 0;\n\t\t\tthis._model.data = {};\n\t\t\tthis._model.data[$.jstree.root] = {\n\t\t\t\tid : $.jstree.root,\n\t\t\t\tparent : null,\n\t\t\t\tparents : [],\n\t\t\t\tchildren : [],\n\t\t\t\tchildren_d : [],\n\t\t\t\tstate : { loaded : false }\n\t\t\t};\n\t\t\tthis._data.core.selected = [];\n\t\t\tthis._data.core.last_clicked = null;\n\t\t\tthis._data.core.focused = null;\n\n\t\t\tvar c = this.get_container_ul()[0].className;\n\t\t\tif(!skip_loading) {\n\t\t\t\tthis.element.html(\"<\"+\"ul class='\"+c+\"' role='group'><\"+\"li class='jstree-initial-node jstree-loading jstree-leaf jstree-last' role='none' id='j\"+this._id+\"_loading'><i class='jstree-icon jstree-ocl'></i><\"+\"a class='jstree-anchor' role='treeitem' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>\" + this.get_string(\"Loading ...\") + \"</a></li></ul>\");\n\t\t\t\tthis.element.attr('aria-activedescendant','j'+this._id+'_loading');\n\t\t\t}\n\t\t\tthis.load_node($.jstree.root, function (o, s) {\n\t\t\t\tif(s) {\n\t\t\t\t\tthis.get_container_ul()[0].className = c;\n\t\t\t\t\tif(this._firstChild(this.get_container_ul()[0])) {\n\t\t\t\t\t\tthis.element.attr('aria-activedescendant',this._firstChild(this.get_container_ul()[0]).id);\n\t\t\t\t\t}\n\t\t\t\t\tthis.set_state($.extend(true, {}, this._data.core.state), function () {\n\t\t\t\t\t\t/**\n\t\t\t\t\t\t * triggered when a `refresh` call completes\n\t\t\t\t\t\t * @event\n\t\t\t\t\t\t * @name refresh.jstree\n\t\t\t\t\t\t */\n\t\t\t\t\t\tthis.trigger('refresh');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthis._data.core.state = null;\n\t\t\t});\n\t\t},\n\t\t/**\n\t\t * refreshes a node in the tree (reload its children) all opened nodes inside that node are reloaded with calls to `load_node`.\n\t\t * @name refresh_node(obj)\n\t\t * @param  {mixed} obj the node\n\t\t * @trigger refresh_node.jstree\n\t\t */\n\t\trefresh_node : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\tvar opened = [], to_load = [], s = this._data.core.selected.concat([]);\n\t\t\tto_load.push(obj.id);\n\t\t\tif(obj.state.opened === true) { opened.push(obj.id); }\n\t\t\tthis.get_node(obj, true).find('.jstree-open').each(function() { to_load.push(this.id); opened.push(this.id); });\n\t\t\tthis._load_nodes(to_load, function (nodes) {\n\t\t\t\tthis.open_node(opened, false, 0);\n\t\t\t\tthis.select_node(s);\n\t\t\t\t/**\n\t\t\t\t * triggered when a node is refreshed\n\t\t\t\t * @event\n\t\t\t\t * @name refresh_node.jstree\n\t\t\t\t * @param {Object} node - the refreshed node\n\t\t\t\t * @param {Array} nodes - an array of the IDs of the nodes that were reloaded\n\t\t\t\t */\n\t\t\t\tthis.trigger('refresh_node', { 'node' : obj, 'nodes' : nodes });\n\t\t\t}.bind(this), false, true);\n\t\t},\n\t\t/**\n\t\t * set (change) the ID of a node\n\t\t * @name set_id(obj, id)\n\t\t * @param  {mixed} obj the node\n\t\t * @param  {String} id the new ID\n\t\t * @return {Boolean}\n\t\t * @trigger set_id.jstree\n\t\t */\n\t\tset_id : function (obj, id) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\tvar i, j, m = this._model.data, old = obj.id;\n\t\t\tid = id.toString();\n\t\t\t// update parents (replace current ID with new one in children and children_d)\n\t\t\tm[obj.parent].children[$.inArray(obj.id, m[obj.parent].children)] = id;\n\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\tm[obj.parents[i]].children_d[$.inArray(obj.id, m[obj.parents[i]].children_d)] = id;\n\t\t\t}\n\t\t\t// update children (replace current ID with new one in parent and parents)\n\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\tm[obj.children[i]].parent = id;\n\t\t\t}\n\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\tm[obj.children_d[i]].parents[$.inArray(obj.id, m[obj.children_d[i]].parents)] = id;\n\t\t\t}\n\t\t\ti = $.inArray(obj.id, this._data.core.selected);\n\t\t\tif(i !== -1) { this._data.core.selected[i] = id; }\n\t\t\t// update model and obj itself (obj.id, this._model.data[KEY])\n\t\t\ti = this.get_node(obj.id, true);\n\t\t\tif(i) {\n\t\t\t\ti.attr('id', id); //.children('.jstree-anchor').attr('id', id + '_anchor').end().attr('aria-labelledby', id + '_anchor');\n\t\t\t\tif(this.element.attr('aria-activedescendant') === obj.id) {\n\t\t\t\t\tthis.element.attr('aria-activedescendant', id);\n\t\t\t\t}\n\t\t\t}\n\t\t\tdelete m[obj.id];\n\t\t\tobj.id = id;\n\t\t\tobj.li_attr.id = id;\n\t\t\tm[id] = obj;\n\t\t\t/**\n\t\t\t * triggered when a node id value is changed\n\t\t\t * @event\n\t\t\t * @name set_id.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {String} old the old id\n\t\t\t */\n\t\t\tthis.trigger('set_id',{ \"node\" : obj, \"new\" : obj.id, \"old\" : old });\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * get the text value of a node\n\t\t * @name get_text(obj)\n\t\t * @param  {mixed} obj the node\n\t\t * @return {String}\n\t\t */\n\t\tget_text : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn (!obj || obj.id === $.jstree.root) ? false : obj.text;\n\t\t},\n\t\t/**\n\t\t * set the text value of a node. Used internally, please use `rename_node(obj, val)`.\n\t\t * @private\n\t\t * @name set_text(obj, val)\n\t\t * @param  {mixed} obj the node, you can pass an array to set the text on multiple nodes\n\t\t * @param  {String} val the new text value\n\t\t * @return {Boolean}\n\t\t * @trigger set_text.jstree\n\t\t */\n\t\tset_text : function (obj, val) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.set_text(obj[t1], val);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\tobj.text = val;\n\t\t\tif(this.get_node(obj, true).length) {\n\t\t\t\tthis.redraw_node(obj.id);\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when a node text value is changed\n\t\t\t * @event\n\t\t\t * @name set_text.jstree\n\t\t\t * @param {Object} obj\n\t\t\t * @param {String} text the new value\n\t\t\t */\n\t\t\tthis.trigger('set_text',{ \"obj\" : obj, \"text\" : val });\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * gets a JSON representation of a node (or the whole tree)\n\t\t * @name get_json([obj, options])\n\t\t * @param  {mixed} obj\n\t\t * @param  {Object} options\n\t\t * @param  {Boolean} options.no_state do not return state information\n\t\t * @param  {Boolean} options.no_id do not return ID\n\t\t * @param  {Boolean} options.no_children do not include children\n\t\t * @param  {Boolean} options.no_data do not include node data\n\t\t * @param  {Boolean} options.no_li_attr do not include LI attributes\n\t\t * @param  {Boolean} options.no_a_attr do not include A attributes\n\t\t * @param  {Boolean} options.flat return flat JSON instead of nested\n\t\t * @return {Object}\n\t\t */\n\t\tget_json : function (obj, options, flat) {\n\t\t\tobj = this.get_node(obj || $.jstree.root);\n\t\t\tif(!obj) { return false; }\n\t\t\tif(options && options.flat && !flat) { flat = []; }\n\t\t\tvar tmp = {\n\t\t\t\t'id' : obj.id,\n\t\t\t\t'text' : obj.text,\n\t\t\t\t'icon' : this.get_icon(obj),\n\t\t\t\t'li_attr' : $.extend(true, {}, obj.li_attr),\n\t\t\t\t'a_attr' : $.extend(true, {}, obj.a_attr),\n\t\t\t\t'state' : {},\n\t\t\t\t'data' : options && options.no_data ? false : $.extend(true, $.vakata.is_array(obj.data)?[]:{}, obj.data)\n\t\t\t\t//( this.get_node(obj, true).length ? this.get_node(obj, true).data() : obj.data ),\n\t\t\t}, i, j;\n\t\t\tif(options && options.flat) {\n\t\t\t\ttmp.parent = obj.parent;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ttmp.children = [];\n\t\t\t}\n\t\t\tif(!options || !options.no_state) {\n\t\t\t\tfor(i in obj.state) {\n\t\t\t\t\tif(obj.state.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.state[i] = obj.state[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdelete tmp.state;\n\t\t\t}\n\t\t\tif(options && options.no_li_attr) {\n\t\t\t\tdelete tmp.li_attr;\n\t\t\t}\n\t\t\tif(options && options.no_a_attr) {\n\t\t\t\tdelete tmp.a_attr;\n\t\t\t}\n\t\t\tif(options && options.no_id) {\n\t\t\t\tdelete tmp.id;\n\t\t\t\tif(tmp.li_attr && tmp.li_attr.id) {\n\t\t\t\t\tdelete tmp.li_attr.id;\n\t\t\t\t}\n\t\t\t\tif(tmp.a_attr && tmp.a_attr.id) {\n\t\t\t\t\tdelete tmp.a_attr.id;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(options && options.flat && obj.id !== $.jstree.root) {\n\t\t\t\tflat.push(tmp);\n\t\t\t}\n\t\t\tif(!options || !options.no_children) {\n\t\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\t\tif(options && options.flat) {\n\t\t\t\t\t\tthis.get_json(obj.children[i], options, flat);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttmp.children.push(this.get_json(obj.children[i], options));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn options && options.flat ? flat : (obj.id === $.jstree.root ? tmp.children : tmp);\n\t\t},\n\t\t/**\n\t\t * create a new node (do not confuse with load_node)\n\t\t * @name create_node([par, node, pos, callback, is_loaded])\n\t\t * @param  {mixed}   par       the parent node (to create a root node use either \"#\" (string) or `null`)\n\t\t * @param  {mixed}   node      the data for the new node (a valid JSON object, or a simple string with the name)\n\t\t * @param  {mixed}   pos       the index at which to insert the node, \"first\" and \"last\" are also supported, default is \"last\"\n\t\t * @param  {Function} callback a function to be called once the node is created\n\t\t * @param  {Boolean} is_loaded internal argument indicating if the parent node was succesfully loaded\n\t\t * @return {String}            the ID of the newly create node\n\t\t * @trigger model.jstree, create_node.jstree\n\t\t */\n\t\tcreate_node : function (par, node, pos, callback, is_loaded) {\n\t\t\tif(par === null) { par = $.jstree.root; }\n\t\t\tpar = this.get_node(par);\n\t\t\tif(!par) { return false; }\n\t\t\tpos = pos === undefined ? \"last\" : pos;\n\t\t\tif(!pos.toString().match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\treturn this.load_node(par, function () { this.create_node(par, node, pos, callback, true); });\n\t\t\t}\n\t\t\tif(!node) { node = { \"text\" : this.get_string('New node') }; }\n\t\t\tif(typeof node === \"string\") {\n\t\t\t\tnode = { \"text\" : node };\n\t\t\t} else {\n\t\t\t\tnode = $.extend(true, {}, node);\n\t\t\t}\n\t\t\tif(node.text === undefined) { node.text = this.get_string('New node'); }\n\t\t\tvar tmp, dpc, i, j;\n\n\t\t\tif(par.id === $.jstree.root) {\n\t\t\t\tif(pos === \"before\") { pos = \"first\"; }\n\t\t\t\tif(pos === \"after\") { pos = \"last\"; }\n\t\t\t}\n\t\t\tswitch(pos) {\n\t\t\t\tcase \"before\":\n\t\t\t\t\ttmp = this.get_node(par.parent);\n\t\t\t\t\tpos = $.inArray(par.id, tmp.children);\n\t\t\t\t\tpar = tmp;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"after\" :\n\t\t\t\t\ttmp = this.get_node(par.parent);\n\t\t\t\t\tpos = $.inArray(par.id, tmp.children) + 1;\n\t\t\t\t\tpar = tmp;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"inside\":\n\t\t\t\tcase \"first\":\n\t\t\t\t\tpos = 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"last\":\n\t\t\t\t\tpos = par.children.length;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tif(!pos) { pos = 0; }\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif(pos > par.children.length) { pos = par.children.length; }\n\t\t\tif(node.id === undefined) { node.id = true; }\n\t\t\tif(!this.check(\"create_node\", node, par, pos)) {\n\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(node.id === true) { delete node.id; }\n\t\t\tnode = this._parse_model_from_json(node, par.id, par.parents.concat());\n\t\t\tif(!node) { return false; }\n\t\t\ttmp = this.get_node(node);\n\t\t\tdpc = [];\n\t\t\tdpc.push(node);\n\t\t\tdpc = dpc.concat(tmp.children_d);\n\t\t\tthis.trigger('model', { \"nodes\" : dpc, \"parent\" : par.id });\n\n\t\t\tpar.children_d = par.children_d.concat(dpc);\n\t\t\tfor(i = 0, j = par.parents.length; i < j; i++) {\n\t\t\t\tthis._model.data[par.parents[i]].children_d = this._model.data[par.parents[i]].children_d.concat(dpc);\n\t\t\t}\n\t\t\tnode = tmp;\n\t\t\ttmp = [];\n\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\ttmp[i >= pos ? i+1 : i] = par.children[i];\n\t\t\t}\n\t\t\ttmp[pos] = node.id;\n\t\t\tpar.children = tmp;\n\n\t\t\tthis.redraw_node(par, true);\n\t\t\t/**\n\t\t\t * triggered when a node is created\n\t\t\t * @event\n\t\t\t * @name create_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {String} parent the parent's ID\n\t\t\t * @param {Number} position the position of the new node among the parent's children\n\t\t\t */\n\t\t\tthis.trigger('create_node', { \"node\" : this.get_node(node), \"parent\" : par.id, \"position\" : pos });\n\t\t\tif(callback) { callback.call(this, this.get_node(node)); }\n\t\t\treturn node.id;\n\t\t},\n\t\t/**\n\t\t * set the text value of a node\n\t\t * @name rename_node(obj, val)\n\t\t * @param  {mixed} obj the node, you can pass an array to rename multiple nodes to the same name\n\t\t * @param  {String} val the new text value\n\t\t * @return {Boolean}\n\t\t * @trigger rename_node.jstree\n\t\t */\n\t\trename_node : function (obj, val) {\n\t\t\tvar t1, t2, old;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.rename_node(obj[t1], val);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\told = obj.text;\n\t\t\tif(!this.check(\"rename_node\", obj, this.get_parent(obj), val)) {\n\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tthis.set_text(obj, val); // .apply(this, Array.prototype.slice.call(arguments))\n\t\t\t/**\n\t\t\t * triggered when a node is renamed\n\t\t\t * @event\n\t\t\t * @name rename_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {String} text the new value\n\t\t\t * @param {String} old the old value\n\t\t\t */\n\t\t\tthis.trigger('rename_node', { \"node\" : obj, \"text\" : val, \"old\" : old });\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * remove a node\n\t\t * @name delete_node(obj)\n\t\t * @param  {mixed} obj the node, you can pass an array to delete multiple nodes\n\t\t * @return {Boolean}\n\t\t * @trigger delete_node.jstree, changed.jstree\n\t\t */\n\t\tdelete_node : function (obj) {\n\t\t\tvar t1, t2, par, pos, tmp, i, j, k, l, c, top, lft;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.delete_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\tpar = this.get_node(obj.parent);\n\t\t\tpos = $.inArray(obj.id, par.children);\n\t\t\tc = false;\n\t\t\tif(!this.check(\"delete_node\", obj, par, pos)) {\n\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(pos !== -1) {\n\t\t\t\tpar.children = $.vakata.array_remove(par.children, pos);\n\t\t\t}\n\t\t\ttmp = obj.children_d.concat([]);\n\t\t\ttmp.push(obj.id);\n\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\tthis._model.data[obj.parents[i]].children_d = $.vakata.array_filter(this._model.data[obj.parents[i]].children_d, function (v) {\n\t\t\t\t\treturn $.inArray(v, tmp) === -1;\n\t\t\t\t});\n\t\t\t}\n\t\t\tfor(k = 0, l = tmp.length; k < l; k++) {\n\t\t\t\tif(this._model.data[tmp[k]].state.selected) {\n\t\t\t\t\tc = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (c) {\n\t\t\t\tthis._data.core.selected = $.vakata.array_filter(this._data.core.selected, function (v) {\n\t\t\t\t\treturn $.inArray(v, tmp) === -1;\n\t\t\t\t});\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when a node is deleted\n\t\t\t * @event\n\t\t\t * @name delete_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {String} parent the parent's ID\n\t\t\t */\n\t\t\tthis.trigger('delete_node', { \"node\" : obj, \"parent\" : par.id });\n\t\t\tif(c) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'delete_node', 'node' : obj, 'selected' : this._data.core.selected, 'parent' : par.id });\n\t\t\t}\n\t\t\tfor(k = 0, l = tmp.length; k < l; k++) {\n\t\t\t\tdelete this._model.data[tmp[k]];\n\t\t\t}\n\t\t\tif($.inArray(this._data.core.focused, tmp) !== -1) {\n\t\t\t\tthis._data.core.focused = null;\n\t\t\t\ttop = this.element[0].scrollTop;\n\t\t\t\tlft = this.element[0].scrollLeft;\n\t\t\t\tif(par.id === $.jstree.root) {\n\t\t\t\t\tif (this._model.data[$.jstree.root].children[0]) {\n\t\t\t\t\t\tthis.get_node(this._model.data[$.jstree.root].children[0], true).children('.jstree-anchor').trigger('focus');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.get_node(par, true).children('.jstree-anchor').trigger('focus');\n\t\t\t\t}\n\t\t\t\tthis.element[0].scrollTop  = top;\n\t\t\t\tthis.element[0].scrollLeft = lft;\n\t\t\t}\n\t\t\tthis.redraw_node(par, true);\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * check if an operation is premitted on the tree. Used internally.\n\t\t * @private\n\t\t * @name check(chk, obj, par, pos)\n\t\t * @param  {String} chk the operation to check, can be \"create_node\", \"rename_node\", \"delete_node\", \"copy_node\" or \"move_node\"\n\t\t * @param  {mixed} obj the node\n\t\t * @param  {mixed} par the parent\n\t\t * @param  {mixed} pos the position to insert at, or if \"rename_node\" - the new name\n\t\t * @param  {mixed} more some various additional information, for example if a \"move_node\" operations is triggered by DND this will be the hovered node\n\t\t * @return {Boolean}\n\t\t */\n\t\tcheck : function (chk, obj, par, pos, more) {\n\t\t\tobj = obj && obj.id ? obj : this.get_node(obj);\n\t\t\tpar = par && par.id ? par : this.get_node(par);\n\t\t\tvar tmp = chk.match(/^(move_node|copy_node|create_node)$/i) ? par : obj,\n\t\t\t\tchc = this.settings.core.check_callback;\n\t\t\tif(chk === \"move_node\" || chk === \"copy_node\") {\n\t\t\t\tif((!more || !more.is_multi) && (chk === \"move_node\" && $.inArray(obj.id, par.children) === pos)) {\n\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'core', 'id' : 'core_08', 'reason' : 'Moving node to its current position', 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && obj.id ? obj.id : false, 'par' : par && par.id ? par.id : false }) };\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif((!more || !more.is_multi) && (obj.id === par.id || (chk === \"move_node\" && $.inArray(obj.id, par.children) === pos) || $.inArray(par.id, obj.children_d) !== -1)) {\n\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'core', 'id' : 'core_01', 'reason' : 'Moving parent inside child', 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && obj.id ? obj.id : false, 'par' : par && par.id ? par.id : false }) };\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(tmp && tmp.data) { tmp = tmp.data; }\n\t\t\tif(tmp && tmp.functions && (tmp.functions[chk] === false || tmp.functions[chk] === true)) {\n\t\t\t\tif(tmp.functions[chk] === false) {\n\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'core', 'id' : 'core_02', 'reason' : 'Node data prevents function: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && obj.id ? obj.id : false, 'par' : par && par.id ? par.id : false }) };\n\t\t\t\t}\n\t\t\t\treturn tmp.functions[chk];\n\t\t\t}\n\t\t\tif(chc === false || ($.vakata.is_function(chc) && chc.call(this, chk, obj, par, pos, more) === false) || (chc && chc[chk] === false)) {\n\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'core', 'id' : 'core_03', 'reason' : 'User config for core.check_callback prevents function: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && obj.id ? obj.id : false, 'par' : par && par.id ? par.id : false }) };\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * get the last error\n\t\t * @name last_error()\n\t\t * @return {Object}\n\t\t */\n\t\tlast_error : function () {\n\t\t\treturn this._data.core.last_error;\n\t\t},\n\t\t/**\n\t\t * move a node to a new parent\n\t\t * @name move_node(obj, par [, pos, callback, is_loaded])\n\t\t * @param  {mixed} obj the node to move, pass an array to move multiple nodes\n\t\t * @param  {mixed} par the new parent\n\t\t * @param  {mixed} pos the position to insert at (besides integer values, \"first\" and \"last\" are supported, as well as \"before\" and \"after\"), defaults to integer `0`\n\t\t * @param  {function} callback a function to call once the move is completed, receives 3 arguments - the node, the new parent and the position\n\t\t * @param  {Boolean} is_loaded internal parameter indicating if the parent node has been loaded\n\t\t * @param  {Boolean} skip_redraw internal parameter indicating if the tree should be redrawn\n\t\t * @param  {Boolean} instance internal parameter indicating if the node comes from another instance\n\t\t * @trigger move_node.jstree\n\t\t */\n\t\tmove_node : function (obj, par, pos, callback, is_loaded, skip_redraw, origin) {\n\t\t\tvar t1, t2, old_par, old_pos, new_par, old_ins, is_multi, dpc, tmp, i, j, k, l, p;\n\n\t\t\tpar = this.get_node(par);\n\t\t\tpos = pos === undefined ? 0 : pos;\n\t\t\tif(!par) { return false; }\n\t\t\tif(!pos.toString().match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\treturn this.load_node(par, function () { this.move_node(obj, par, pos, callback, true, false, origin); });\n\t\t\t}\n\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tif(obj.length === 1) {\n\t\t\t\t\tobj = obj[0];\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t//obj = obj.slice();\n\t\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\t\tif((tmp = this.move_node(obj[t1], par, pos, callback, is_loaded, false, origin))) {\n\t\t\t\t\t\t\tpar = tmp;\n\t\t\t\t\t\t\tpos = \"after\";\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.redraw();\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tobj = obj && (obj.id !== undefined) ? obj : this.get_node(obj);\n\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\n\t\t\told_par = (obj.parent || $.jstree.root).toString();\n\t\t\tnew_par = (!pos.toString().match(/^(before|after)$/) || par.id === $.jstree.root) ? par : this.get_node(par.parent);\n\t\t\told_ins = origin ? origin : (this._model.data[obj.id] ? this : $.jstree.reference(obj.id));\n\t\t\tis_multi = !old_ins || !old_ins._id || (this._id !== old_ins._id);\n\t\t\told_pos = old_ins && old_ins._id && old_par && old_ins._model.data[old_par] && old_ins._model.data[old_par].children ? $.inArray(obj.id, old_ins._model.data[old_par].children) : -1;\n\t\t\tif(old_ins && old_ins._id) {\n\t\t\t\tobj = old_ins._model.data[obj.id];\n\t\t\t}\n\n\t\t\tif(is_multi) {\n\t\t\t\tif((tmp = this.copy_node(obj, par, pos, callback, is_loaded, false, origin))) {\n\t\t\t\t\tif(old_ins) { old_ins.delete_node(obj); }\n\t\t\t\t\treturn tmp;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t//var m = this._model.data;\n\t\t\tif(par.id === $.jstree.root) {\n\t\t\t\tif(pos === \"before\") { pos = \"first\"; }\n\t\t\t\tif(pos === \"after\") { pos = \"last\"; }\n\t\t\t}\n\t\t\tswitch(pos) {\n\t\t\t\tcase \"before\":\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children);\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"after\" :\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children) + 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"inside\":\n\t\t\t\tcase \"first\":\n\t\t\t\t\tpos = 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"last\":\n\t\t\t\t\tpos = new_par.children.length;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tif(!pos) { pos = 0; }\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif(pos > new_par.children.length) { pos = new_par.children.length; }\n\t\t\tif(!this.check(\"move_node\", obj, new_par, pos, { 'core' : true, 'origin' : origin, 'is_multi' : (old_ins && old_ins._id && old_ins._id !== this._id), 'is_foreign' : (!old_ins || !old_ins._id) })) {\n\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(obj.parent === new_par.id) {\n\t\t\t\tdpc = new_par.children.concat();\n\t\t\t\ttmp = $.inArray(obj.id, dpc);\n\t\t\t\tif(tmp !== -1) {\n\t\t\t\t\tdpc = $.vakata.array_remove(dpc, tmp);\n\t\t\t\t\tif(pos > tmp) { pos--; }\n\t\t\t\t}\n\t\t\t\ttmp = [];\n\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\ttmp[i >= pos ? i+1 : i] = dpc[i];\n\t\t\t\t}\n\t\t\t\ttmp[pos] = obj.id;\n\t\t\t\tnew_par.children = tmp;\n\t\t\t\tthis._node_changed(new_par.id);\n\t\t\t\tthis.redraw(new_par.id === $.jstree.root);\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// clean old parent and up\n\t\t\t\ttmp = obj.children_d.concat();\n\t\t\t\ttmp.push(obj.id);\n\t\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\t\tdpc = [];\n\t\t\t\t\tp = old_ins._model.data[obj.parents[i]].children_d;\n\t\t\t\t\tfor(k = 0, l = p.length; k < l; k++) {\n\t\t\t\t\t\tif($.inArray(p[k], tmp) === -1) {\n\t\t\t\t\t\t\tdpc.push(p[k]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\told_ins._model.data[obj.parents[i]].children_d = dpc;\n\t\t\t\t}\n\t\t\t\told_ins._model.data[old_par].children = $.vakata.array_remove_item(old_ins._model.data[old_par].children, obj.id);\n\n\t\t\t\t// insert into new parent and up\n\t\t\t\tfor(i = 0, j = new_par.parents.length; i < j; i++) {\n\t\t\t\t\tthis._model.data[new_par.parents[i]].children_d = this._model.data[new_par.parents[i]].children_d.concat(tmp);\n\t\t\t\t}\n\t\t\t\tdpc = [];\n\t\t\t\tfor(i = 0, j = new_par.children.length; i < j; i++) {\n\t\t\t\t\tdpc[i >= pos ? i+1 : i] = new_par.children[i];\n\t\t\t\t}\n\t\t\t\tdpc[pos] = obj.id;\n\t\t\t\tnew_par.children = dpc;\n\t\t\t\tnew_par.children_d.push(obj.id);\n\t\t\t\tnew_par.children_d = new_par.children_d.concat(obj.children_d);\n\n\t\t\t\t// update object\n\t\t\t\tobj.parent = new_par.id;\n\t\t\t\ttmp = new_par.parents.concat();\n\t\t\t\ttmp.unshift(new_par.id);\n\t\t\t\tp = obj.parents.length;\n\t\t\t\tobj.parents = tmp;\n\n\t\t\t\t// update object children\n\t\t\t\ttmp = tmp.concat();\n\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\tthis._model.data[obj.children_d[i]].parents = this._model.data[obj.children_d[i]].parents.slice(0,p*-1);\n\t\t\t\t\tArray.prototype.push.apply(this._model.data[obj.children_d[i]].parents, tmp);\n\t\t\t\t}\n\n\t\t\t\tif(old_par === $.jstree.root || new_par.id === $.jstree.root) {\n\t\t\t\t\tthis._model.force_full_redraw = true;\n\t\t\t\t}\n\t\t\t\tif(!this._model.force_full_redraw) {\n\t\t\t\t\tthis._node_changed(old_par);\n\t\t\t\t\tthis._node_changed(new_par.id);\n\t\t\t\t}\n\t\t\t\tif(!skip_redraw) {\n\t\t\t\t\tthis.redraw();\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(callback) { callback.call(this, obj, new_par, pos); }\n\t\t\t/**\n\t\t\t * triggered when a node is moved\n\t\t\t * @event\n\t\t\t * @name move_node.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {String} parent the parent's ID\n\t\t\t * @param {Number} position the position of the node among the parent's children\n\t\t\t * @param {String} old_parent the old parent of the node\n\t\t\t * @param {Number} old_position the old position of the node\n\t\t\t * @param {Boolean} is_multi do the node and new parent belong to different instances\n\t\t\t * @param {jsTree} old_instance the instance the node came from\n\t\t\t * @param {jsTree} new_instance the instance of the new parent\n\t\t\t */\n\t\t\tthis.trigger('move_node', { \"node\" : obj, \"parent\" : new_par.id, \"position\" : pos, \"old_parent\" : old_par, \"old_position\" : old_pos, 'is_multi' : (old_ins && old_ins._id && old_ins._id !== this._id), 'is_foreign' : (!old_ins || !old_ins._id), 'old_instance' : old_ins, 'new_instance' : this });\n\t\t\treturn obj.id;\n\t\t},\n\t\t/**\n\t\t * copy a node to a new parent\n\t\t * @name copy_node(obj, par [, pos, callback, is_loaded])\n\t\t * @param  {mixed} obj the node to copy, pass an array to copy multiple nodes\n\t\t * @param  {mixed} par the new parent\n\t\t * @param  {mixed} pos the position to insert at (besides integer values, \"first\" and \"last\" are supported, as well as \"before\" and \"after\"), defaults to integer `0`\n\t\t * @param  {function} callback a function to call once the move is completed, receives 3 arguments - the node, the new parent and the position\n\t\t * @param  {Boolean} is_loaded internal parameter indicating if the parent node has been loaded\n\t\t * @param  {Boolean} skip_redraw internal parameter indicating if the tree should be redrawn\n\t\t * @param  {Boolean} instance internal parameter indicating if the node comes from another instance\n\t\t * @trigger model.jstree copy_node.jstree\n\t\t */\n\t\tcopy_node : function (obj, par, pos, callback, is_loaded, skip_redraw, origin) {\n\t\t\tvar t1, t2, dpc, tmp, i, j, node, old_par, new_par, old_ins, is_multi;\n\n\t\t\tpar = this.get_node(par);\n\t\t\tpos = pos === undefined ? 0 : pos;\n\t\t\tif(!par) { return false; }\n\t\t\tif(!pos.toString().match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\treturn this.load_node(par, function () { this.copy_node(obj, par, pos, callback, true, false, origin); });\n\t\t\t}\n\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tif(obj.length === 1) {\n\t\t\t\t\tobj = obj[0];\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t//obj = obj.slice();\n\t\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\t\tif((tmp = this.copy_node(obj[t1], par, pos, callback, is_loaded, true, origin))) {\n\t\t\t\t\t\t\tpar = tmp;\n\t\t\t\t\t\t\tpos = \"after\";\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.redraw();\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tobj = obj && (obj.id !== undefined) ? obj : this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\n\t\t\told_par = (obj.parent || $.jstree.root).toString();\n\t\t\tnew_par = (!pos.toString().match(/^(before|after)$/) || par.id === $.jstree.root) ? par : this.get_node(par.parent);\n\t\t\told_ins = origin ? origin : (this._model.data[obj.id] ? this : $.jstree.reference(obj.id));\n\t\t\tis_multi = !old_ins || !old_ins._id || (this._id !== old_ins._id);\n\n\t\t\tif(old_ins && old_ins._id) {\n\t\t\t\tobj = old_ins._model.data[obj.id];\n\t\t\t}\n\n\t\t\tif(par.id === $.jstree.root) {\n\t\t\t\tif(pos === \"before\") { pos = \"first\"; }\n\t\t\t\tif(pos === \"after\") { pos = \"last\"; }\n\t\t\t}\n\t\t\tswitch(pos) {\n\t\t\t\tcase \"before\":\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children);\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"after\" :\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children) + 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"inside\":\n\t\t\t\tcase \"first\":\n\t\t\t\t\tpos = 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"last\":\n\t\t\t\t\tpos = new_par.children.length;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tif(!pos) { pos = 0; }\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif(pos > new_par.children.length) { pos = new_par.children.length; }\n\t\t\tif(!this.check(\"copy_node\", obj, new_par, pos, { 'core' : true, 'origin' : origin, 'is_multi' : (old_ins && old_ins._id && old_ins._id !== this._id), 'is_foreign' : (!old_ins || !old_ins._id) })) {\n\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tnode = old_ins ? old_ins.get_json(obj, { no_id : true, no_data : true, no_state : true }) : obj;\n\t\t\tif(!node) { return false; }\n\t\t\tif(node.id === true) { delete node.id; }\n\t\t\tnode = this._parse_model_from_json(node, new_par.id, new_par.parents.concat());\n\t\t\tif(!node) { return false; }\n\t\t\ttmp = this.get_node(node);\n\t\t\tif(obj && obj.state && obj.state.loaded === false) { tmp.state.loaded = false; }\n\t\t\tdpc = [];\n\t\t\tdpc.push(node);\n\t\t\tdpc = dpc.concat(tmp.children_d);\n\t\t\tthis.trigger('model', { \"nodes\" : dpc, \"parent\" : new_par.id });\n\n\t\t\t// insert into new parent and up\n\t\t\tfor(i = 0, j = new_par.parents.length; i < j; i++) {\n\t\t\t\tthis._model.data[new_par.parents[i]].children_d = this._model.data[new_par.parents[i]].children_d.concat(dpc);\n\t\t\t}\n\t\t\tdpc = [];\n\t\t\tfor(i = 0, j = new_par.children.length; i < j; i++) {\n\t\t\t\tdpc[i >= pos ? i+1 : i] = new_par.children[i];\n\t\t\t}\n\t\t\tdpc[pos] = tmp.id;\n\t\t\tnew_par.children = dpc;\n\t\t\tnew_par.children_d.push(tmp.id);\n\t\t\tnew_par.children_d = new_par.children_d.concat(tmp.children_d);\n\n\t\t\tif(new_par.id === $.jstree.root) {\n\t\t\t\tthis._model.force_full_redraw = true;\n\t\t\t}\n\t\t\tif(!this._model.force_full_redraw) {\n\t\t\t\tthis._node_changed(new_par.id);\n\t\t\t}\n\t\t\tif(!skip_redraw) {\n\t\t\t\tthis.redraw(new_par.id === $.jstree.root);\n\t\t\t}\n\t\t\tif(callback) { callback.call(this, tmp, new_par, pos); }\n\t\t\t/**\n\t\t\t * triggered when a node is copied\n\t\t\t * @event\n\t\t\t * @name copy_node.jstree\n\t\t\t * @param {Object} node the copied node\n\t\t\t * @param {Object} original the original node\n\t\t\t * @param {String} parent the parent's ID\n\t\t\t * @param {Number} position the position of the node among the parent's children\n\t\t\t * @param {String} old_parent the old parent of the node\n\t\t\t * @param {Number} old_position the position of the original node\n\t\t\t * @param {Boolean} is_multi do the node and new parent belong to different instances\n\t\t\t * @param {jsTree} old_instance the instance the node came from\n\t\t\t * @param {jsTree} new_instance the instance of the new parent\n\t\t\t */\n\t\t\tthis.trigger('copy_node', { \"node\" : tmp, \"original\" : obj, \"parent\" : new_par.id, \"position\" : pos, \"old_parent\" : old_par, \"old_position\" : old_ins && old_ins._id && old_par && old_ins._model.data[old_par] && old_ins._model.data[old_par].children ? $.inArray(obj.id, old_ins._model.data[old_par].children) : -1,'is_multi' : (old_ins && old_ins._id && old_ins._id !== this._id), 'is_foreign' : (!old_ins || !old_ins._id), 'old_instance' : old_ins, 'new_instance' : this });\n\t\t\treturn tmp.id;\n\t\t},\n\t\t/**\n\t\t * cut a node (a later call to `paste(obj)` would move the node)\n\t\t * @name cut(obj)\n\t\t * @param  {mixed} obj multiple objects can be passed using an array\n\t\t * @trigger cut.jstree\n\t\t */\n\t\tcut : function (obj) {\n\t\t\tif(!obj) { obj = this._data.core.selected.concat(); }\n\t\t\tif(!$.vakata.is_array(obj)) { obj = [obj]; }\n\t\t\tif(!obj.length) { return false; }\n\t\t\tvar tmp = [], o, t1, t2;\n\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\to = this.get_node(obj[t1]);\n\t\t\t\tif(o && (o.id || o.id === 0) && o.id !== $.jstree.root) { tmp.push(o); }\n\t\t\t}\n\t\t\tif(!tmp.length) { return false; }\n\t\t\tccp_node = tmp;\n\t\t\tccp_inst = this;\n\t\t\tccp_mode = 'move_node';\n\t\t\t/**\n\t\t\t * triggered when nodes are added to the buffer for moving\n\t\t\t * @event\n\t\t\t * @name cut.jstree\n\t\t\t * @param {Array} node\n\t\t\t */\n\t\t\tthis.trigger('cut', { \"node\" : obj });\n\t\t},\n\t\t/**\n\t\t * copy a node (a later call to `paste(obj)` would copy the node)\n\t\t * @name copy(obj)\n\t\t * @param  {mixed} obj multiple objects can be passed using an array\n\t\t * @trigger copy.jstree\n\t\t */\n\t\tcopy : function (obj) {\n\t\t\tif(!obj) { obj = this._data.core.selected.concat(); }\n\t\t\tif(!$.vakata.is_array(obj)) { obj = [obj]; }\n\t\t\tif(!obj.length) { return false; }\n\t\t\tvar tmp = [], o, t1, t2;\n\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\to = this.get_node(obj[t1]);\n\t\t\t\tif(o && (o.id !== undefined) && o.id !== $.jstree.root) { tmp.push(o); }\n\t\t\t}\n\t\t\tif(!tmp.length) { return false; }\n\t\t\tccp_node = tmp;\n\t\t\tccp_inst = this;\n\t\t\tccp_mode = 'copy_node';\n\t\t\t/**\n\t\t\t * triggered when nodes are added to the buffer for copying\n\t\t\t * @event\n\t\t\t * @name copy.jstree\n\t\t\t * @param {Array} node\n\t\t\t */\n\t\t\tthis.trigger('copy', { \"node\" : obj });\n\t\t},\n\t\t/**\n\t\t * get the current buffer (any nodes that are waiting for a paste operation)\n\t\t * @name get_buffer()\n\t\t * @return {Object} an object consisting of `mode` (\"copy_node\" or \"move_node\"), `node` (an array of objects) and `inst` (the instance)\n\t\t */\n\t\tget_buffer : function () {\n\t\t\treturn { 'mode' : ccp_mode, 'node' : ccp_node, 'inst' : ccp_inst };\n\t\t},\n\t\t/**\n\t\t * check if there is something in the buffer to paste\n\t\t * @name can_paste()\n\t\t * @return {Boolean}\n\t\t */\n\t\tcan_paste : function () {\n\t\t\treturn ccp_mode !== false && ccp_node !== false; // && ccp_inst._model.data[ccp_node];\n\t\t},\n\t\t/**\n\t\t * copy or move the previously cut or copied nodes to a new parent\n\t\t * @name paste(obj [, pos])\n\t\t * @param  {mixed} obj the new parent\n\t\t * @param  {mixed} pos the position to insert at (besides integer, \"first\" and \"last\" are supported), defaults to integer `0`\n\t\t * @trigger paste.jstree\n\t\t */\n\t\tpaste : function (obj, pos) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || !ccp_mode || !ccp_mode.match(/^(copy_node|move_node)$/) || !ccp_node) { return false; }\n\t\t\tif(this[ccp_mode](ccp_node, obj, pos, false, false, false, ccp_inst)) {\n\t\t\t\t/**\n\t\t\t\t * triggered when paste is invoked\n\t\t\t\t * @event\n\t\t\t\t * @name paste.jstree\n\t\t\t\t * @param {String} parent the ID of the receiving node\n\t\t\t\t * @param {Array} node the nodes in the buffer\n\t\t\t\t * @param {String} mode the performed operation - \"copy_node\" or \"move_node\"\n\t\t\t\t */\n\t\t\t\tthis.trigger('paste', { \"parent\" : obj.id, \"node\" : ccp_node, \"mode\" : ccp_mode });\n\t\t\t}\n\t\t\tccp_node = false;\n\t\t\tccp_mode = false;\n\t\t\tccp_inst = false;\n\t\t},\n\t\t/**\n\t\t * clear the buffer of previously copied or cut nodes\n\t\t * @name clear_buffer()\n\t\t * @trigger clear_buffer.jstree\n\t\t */\n\t\tclear_buffer : function () {\n\t\t\tccp_node = false;\n\t\t\tccp_mode = false;\n\t\t\tccp_inst = false;\n\t\t\t/**\n\t\t\t * triggered when the copy / cut buffer is cleared\n\t\t\t * @event\n\t\t\t * @name clear_buffer.jstree\n\t\t\t */\n\t\t\tthis.trigger('clear_buffer');\n\t\t},\n\t\t/**\n\t\t * put a node in edit mode (input field to rename the node)\n\t\t * @name edit(obj [, default_text, callback])\n\t\t * @param  {mixed} obj\n\t\t * @param  {String} default_text the text to populate the input with (if omitted or set to a non-string value the node's text value is used)\n\t\t * @param  {Function} callback a function to be called once the text box is blurred, it is called in the instance's scope and receives the node, a status parameter (true if the rename is successful, false otherwise), a boolean indicating if the user cancelled the edit and the original unescaped value provided by the user. You can also access the node's title using .text\n\t\t */\n\t\tedit : function (obj, default_text, callback) {\n\t\t\tvar rtl, w, a, s, t, h1, h2, fn, tmp, cancel = false;\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tif(!this.check(\"edit\", obj, this.get_parent(obj))) {\n\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\ttmp = obj;\n\t\t\tdefault_text = typeof default_text === 'string' ? default_text : obj.text;\n\t\t\tthis.set_text(obj, \"\");\n\t\t\tobj = this._open_to(obj);\n\t\t\ttmp.text = default_text;\n\n\t\t\trtl = this._data.core.rtl;\n\t\t\tw  = this.element.width();\n\t\t\tthis._data.core.focused = tmp.id;\n\t\t\ta  = obj.children('.jstree-anchor').trigger('focus');\n\t\t\ts  = $('<span></span>');\n\t\t\t/*!\n\t\t\toi = obj.children(\"i:visible\"),\n\t\t\tai = a.children(\"i:visible\"),\n\t\t\tw1 = oi.width() * oi.length,\n\t\t\tw2 = ai.width() * ai.length,\n\t\t\t*/\n\t\t\tt  = default_text;\n\t\t\th1 = $(\"<\"+\"div></div>\", { css : { \"position\" : \"absolute\", \"top\" : \"-200px\", \"left\" : (rtl ? \"0px\" : \"-1000px\"), \"visibility\" : \"hidden\" } }).appendTo(document.body);\n\t\t\th2 = $(\"<\"+\"input />\", {\n\t\t\t\t\t\t\"value\" : t,\n\t\t\t\t\t\t\"class\" : \"jstree-rename-input\",\n\t\t\t\t\t\t// \"size\" : t.length,\n\t\t\t\t\t\t\"css\" : {\n\t\t\t\t\t\t\t\"padding\" : \"0\",\n\t\t\t\t\t\t\t\"border\" : \"1px solid silver\",\n\t\t\t\t\t\t\t\"box-sizing\" : \"border-box\",\n\t\t\t\t\t\t\t\"display\" : \"inline-block\",\n\t\t\t\t\t\t\t\"height\" : (this._data.core.li_height) + \"px\",\n\t\t\t\t\t\t\t\"lineHeight\" : (this._data.core.li_height) + \"px\",\n\t\t\t\t\t\t\t\"width\" : \"150px\" // will be set a bit further down\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"on\" : {\n\t\t\t\t\t\t\t\"blur\" : function (e) {\n\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\tvar i = s.children(\".jstree-rename-input\"),\n\t\t\t\t\t\t\t\t\tv = i.val(),\n\t\t\t\t\t\t\t\t\tf = this.settings.core.force_text,\n\t\t\t\t\t\t\t\t\tnv;\n\t\t\t\t\t\t\t\tif(v === \"\") { v = t; }\n\t\t\t\t\t\t\t\th1.remove();\n\t\t\t\t\t\t\t\ts.replaceWith(a);\n\t\t\t\t\t\t\t\ts.remove();\n\t\t\t\t\t\t\t\tt = f ? t : $('<div></div>').append($.parseHTML(t)).html();\n\t\t\t\t\t\t\t\tobj = this.get_node(obj);\n\t\t\t\t\t\t\t\tthis.set_text(obj, t);\n\t\t\t\t\t\t\t\tnv = !!this.rename_node(obj, f ? $('<div></div>').text(v).text() : $('<div></div>').append($.parseHTML(v)).html());\n\t\t\t\t\t\t\t\tif(!nv) {\n\t\t\t\t\t\t\t\t\tthis.set_text(obj, t); // move this up? and fix #483\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis._data.core.focused = tmp.id;\n\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\tvar node = this.get_node(tmp.id, true);\n\t\t\t\t\t\t\t\t\tif(node.length) {\n\t\t\t\t\t\t\t\t\t\tthis._data.core.focused = tmp.id;\n\t\t\t\t\t\t\t\t\t\tnode.children('.jstree-anchor').trigger('focus');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}.bind(this), 0);\n\t\t\t\t\t\t\t\tif(callback) {\n\t\t\t\t\t\t\t\t\tcallback.call(this, tmp, nv, cancel, v);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\th2 = null;\n\t\t\t\t\t\t\t}.bind(this),\n\t\t\t\t\t\t\t\"keydown\" : function (e) {\n\t\t\t\t\t\t\t\tvar key = e.which;\n\t\t\t\t\t\t\t\tif(key === 27) {\n\t\t\t\t\t\t\t\t\tcancel = true;\n\t\t\t\t\t\t\t\t\tthis.value = t;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(key === 27 || key === 13 || key === 37 || key === 38 || key === 39 || key === 40 || key === 32) {\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(key === 27 || key === 13) {\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t\tthis.blur();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"click\" : function (e) { e.stopImmediatePropagation(); },\n\t\t\t\t\t\t\t\"mousedown\" : function (e) { e.stopImmediatePropagation(); },\n\t\t\t\t\t\t\t\"keyup\" : function (e) {\n\t\t\t\t\t\t\t\th2.width(Math.min(h1.text(\"pW\" + this.value).width(),w));\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"keypress\" : function(e) {\n\t\t\t\t\t\t\t\tif(e.which === 13) { return false; }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\tfn = {\n\t\t\t\t\t\tfontFamily\t\t: a.css('fontFamily')\t\t|| '',\n\t\t\t\t\t\tfontSize\t\t: a.css('fontSize')\t\t\t|| '',\n\t\t\t\t\t\tfontWeight\t\t: a.css('fontWeight')\t\t|| '',\n\t\t\t\t\t\tfontStyle\t\t: a.css('fontStyle')\t\t|| '',\n\t\t\t\t\t\tfontStretch\t\t: a.css('fontStretch')\t\t|| '',\n\t\t\t\t\t\tfontVariant\t\t: a.css('fontVariant')\t\t|| '',\n\t\t\t\t\t\tletterSpacing\t: a.css('letterSpacing')\t|| '',\n\t\t\t\t\t\twordSpacing\t\t: a.css('wordSpacing')\t\t|| ''\n\t\t\t\t};\n\t\t\ts.attr('class', a.attr('class')).append(a.contents().clone()).append(h2);\n\t\t\ta.replaceWith(s);\n\t\t\th1.css(fn);\n\t\t\th2.css(fn).width(Math.min(h1.text(\"pW\" + h2[0].value).width(),w))[0].select();\n\t\t\t$(document).one('mousedown.jstree touchstart.jstree dnd_start.vakata', function (e) {\n\t\t\t\tif (h2 && e.target !== h2) {\n\t\t\t\t\t$(h2).trigger('blur');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\n\t\t/**\n\t\t * changes the theme\n\t\t * @name set_theme(theme_name [, theme_url])\n\t\t * @param {String} theme_name the name of the new theme to apply\n\t\t * @param {mixed} theme_url  the location of the CSS file for this theme. Omit or set to `false` if you manually included the file. Set to `true` to autoload from the `core.themes.dir` directory.\n\t\t * @trigger set_theme.jstree\n\t\t */\n\t\tset_theme : function (theme_name, theme_url) {\n\t\t\tif(!theme_name) { return false; }\n\t\t\tif(theme_url === true) {\n\t\t\t\tvar dir = this.settings.core.themes.dir;\n\t\t\t\tif(!dir) { dir = $.jstree.path + '/themes'; }\n\t\t\t\ttheme_url = dir + '/' + theme_name + '/style.css';\n\t\t\t}\n\t\t\tif(theme_url && $.inArray(theme_url, themes_loaded) === -1) {\n\t\t\t\t$('head').append('<'+'link rel=\"stylesheet\" href=\"' + theme_url + '\" type=\"text/css\" />');\n\t\t\t\tthemes_loaded.push(theme_url);\n\t\t\t}\n\t\t\tif(this._data.core.themes.name) {\n\t\t\t\tthis.element.removeClass('jstree-' + this._data.core.themes.name);\n\t\t\t}\n\t\t\tthis._data.core.themes.name = theme_name;\n\t\t\tthis.element.addClass('jstree-' + theme_name);\n\t\t\tthis.element[this.settings.core.themes.responsive ? 'addClass' : 'removeClass' ]('jstree-' + theme_name + '-responsive');\n\t\t\t/**\n\t\t\t * triggered when a theme is set\n\t\t\t * @event\n\t\t\t * @name set_theme.jstree\n\t\t\t * @param {String} theme the new theme\n\t\t\t */\n\t\t\tthis.trigger('set_theme', { 'theme' : theme_name });\n\t\t},\n\t\t/**\n\t\t * gets the name of the currently applied theme name\n\t\t * @name get_theme()\n\t\t * @return {String}\n\t\t */\n\t\tget_theme : function () { return this._data.core.themes.name; },\n\t\t/**\n\t\t * changes the theme variant (if the theme has variants)\n\t\t * @name set_theme_variant(variant_name)\n\t\t * @param {String|Boolean} variant_name the variant to apply (if `false` is used the current variant is removed)\n\t\t */\n\t\tset_theme_variant : function (variant_name) {\n\t\t\tif(this._data.core.themes.variant) {\n\t\t\t\tthis.element.removeClass('jstree-' + this._data.core.themes.name + '-' + this._data.core.themes.variant);\n\t\t\t}\n\t\t\tthis._data.core.themes.variant = variant_name;\n\t\t\tif(variant_name) {\n\t\t\t\tthis.element.addClass('jstree-' + this._data.core.themes.name + '-' + this._data.core.themes.variant);\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * gets the name of the currently applied theme variant\n\t\t * @name get_theme()\n\t\t * @return {String}\n\t\t */\n\t\tget_theme_variant : function () { return this._data.core.themes.variant; },\n\t\t/**\n\t\t * shows a striped background on the container (if the theme supports it)\n\t\t * @name show_stripes()\n\t\t */\n\t\tshow_stripes : function () {\n\t\t\tthis._data.core.themes.stripes = true;\n\t\t\tthis.get_container_ul().addClass(\"jstree-striped\");\n\t\t\t/**\n\t\t\t * triggered when stripes are shown\n\t\t\t * @event\n\t\t\t * @name show_stripes.jstree\n\t\t\t */\n\t\t\tthis.trigger('show_stripes');\n\t\t},\n\t\t/**\n\t\t * hides the striped background on the container\n\t\t * @name hide_stripes()\n\t\t */\n\t\thide_stripes : function () {\n\t\t\tthis._data.core.themes.stripes = false;\n\t\t\tthis.get_container_ul().removeClass(\"jstree-striped\");\n\t\t\t/**\n\t\t\t * triggered when stripes are hidden\n\t\t\t * @event\n\t\t\t * @name hide_stripes.jstree\n\t\t\t */\n\t\t\tthis.trigger('hide_stripes');\n\t\t},\n\t\t/**\n\t\t * toggles the striped background on the container\n\t\t * @name toggle_stripes()\n\t\t */\n\t\ttoggle_stripes : function () { if(this._data.core.themes.stripes) { this.hide_stripes(); } else { this.show_stripes(); } },\n\t\t/**\n\t\t * shows the connecting dots (if the theme supports it)\n\t\t * @name show_dots()\n\t\t */\n\t\tshow_dots : function () {\n\t\t\tthis._data.core.themes.dots = true;\n\t\t\tthis.get_container_ul().removeClass(\"jstree-no-dots\");\n\t\t\t/**\n\t\t\t * triggered when dots are shown\n\t\t\t * @event\n\t\t\t * @name show_dots.jstree\n\t\t\t */\n\t\t\tthis.trigger('show_dots');\n\t\t},\n\t\t/**\n\t\t * hides the connecting dots\n\t\t * @name hide_dots()\n\t\t */\n\t\thide_dots : function () {\n\t\t\tthis._data.core.themes.dots = false;\n\t\t\tthis.get_container_ul().addClass(\"jstree-no-dots\");\n\t\t\t/**\n\t\t\t * triggered when dots are hidden\n\t\t\t * @event\n\t\t\t * @name hide_dots.jstree\n\t\t\t */\n\t\t\tthis.trigger('hide_dots');\n\t\t},\n\t\t/**\n\t\t * toggles the connecting dots\n\t\t * @name toggle_dots()\n\t\t */\n\t\ttoggle_dots : function () { if(this._data.core.themes.dots) { this.hide_dots(); } else { this.show_dots(); } },\n\t\t/**\n\t\t * show the node icons\n\t\t * @name show_icons()\n\t\t */\n\t\tshow_icons : function () {\n\t\t\tthis._data.core.themes.icons = true;\n\t\t\tthis.get_container_ul().removeClass(\"jstree-no-icons\");\n\t\t\t/**\n\t\t\t * triggered when icons are shown\n\t\t\t * @event\n\t\t\t * @name show_icons.jstree\n\t\t\t */\n\t\t\tthis.trigger('show_icons');\n\t\t},\n\t\t/**\n\t\t * hide the node icons\n\t\t * @name hide_icons()\n\t\t */\n\t\thide_icons : function () {\n\t\t\tthis._data.core.themes.icons = false;\n\t\t\tthis.get_container_ul().addClass(\"jstree-no-icons\");\n\t\t\t/**\n\t\t\t * triggered when icons are hidden\n\t\t\t * @event\n\t\t\t * @name hide_icons.jstree\n\t\t\t */\n\t\t\tthis.trigger('hide_icons');\n\t\t},\n\t\t/**\n\t\t * toggle the node icons\n\t\t * @name toggle_icons()\n\t\t */\n\t\ttoggle_icons : function () { if(this._data.core.themes.icons) { this.hide_icons(); } else { this.show_icons(); } },\n\t\t/**\n\t\t * show the node ellipsis\n\t\t * @name show_icons()\n\t\t */\n\t\tshow_ellipsis : function () {\n\t\t\tthis._data.core.themes.ellipsis = true;\n\t\t\tthis.get_container_ul().addClass(\"jstree-ellipsis\");\n\t\t\t/**\n\t\t\t * triggered when ellisis is shown\n\t\t\t * @event\n\t\t\t * @name show_ellipsis.jstree\n\t\t\t */\n\t\t\tthis.trigger('show_ellipsis');\n\t\t},\n\t\t/**\n\t\t * hide the node ellipsis\n\t\t * @name hide_ellipsis()\n\t\t */\n\t\thide_ellipsis : function () {\n\t\t\tthis._data.core.themes.ellipsis = false;\n\t\t\tthis.get_container_ul().removeClass(\"jstree-ellipsis\");\n\t\t\t/**\n\t\t\t * triggered when ellisis is hidden\n\t\t\t * @event\n\t\t\t * @name hide_ellipsis.jstree\n\t\t\t */\n\t\t\tthis.trigger('hide_ellipsis');\n\t\t},\n\t\t/**\n\t\t * toggle the node ellipsis\n\t\t * @name toggle_icons()\n\t\t */\n\t\ttoggle_ellipsis : function () { if(this._data.core.themes.ellipsis) { this.hide_ellipsis(); } else { this.show_ellipsis(); } },\n\t\t/**\n\t\t * set the node icon for a node\n\t\t * @name set_icon(obj, icon)\n\t\t * @param {mixed} obj\n\t\t * @param {String} icon the new icon - can be a path to an icon or a className, if using an image that is in the current directory use a `./` prefix, otherwise it will be detected as a class\n\t\t */\n\t\tset_icon : function (obj, icon) {\n\t\t\tvar t1, t2, dom, old;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.set_icon(obj[t1], icon);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\told = obj.icon;\n\t\t\tobj.icon = icon === true || icon === null || icon === undefined || icon === '' ? true : icon;\n\t\t\tdom = this.get_node(obj, true).children(\".jstree-anchor\").children(\".jstree-themeicon\");\n\t\t\tif(icon === false) {\n\t\t\t\tdom.removeClass('jstree-themeicon-custom ' + old).css(\"background\",\"\").removeAttr(\"rel\");\n\t\t\t\tthis.hide_icon(obj);\n\t\t\t}\n\t\t\telse if(icon === true || icon === null || icon === undefined || icon === '') {\n\t\t\t\tdom.removeClass('jstree-themeicon-custom ' + old).css(\"background\",\"\").removeAttr(\"rel\");\n\t\t\t\tif(old === false) { this.show_icon(obj); }\n\t\t\t}\n\t\t\telse if(icon.indexOf(\"/\") === -1 && icon.indexOf(\".\") === -1) {\n\t\t\t\tdom.removeClass(old).css(\"background\",\"\");\n\t\t\t\tdom.addClass(icon + ' jstree-themeicon-custom').attr(\"rel\",icon);\n\t\t\t\tif(old === false) { this.show_icon(obj); }\n\t\t\t}\n\t\t\telse {\n\t\t\t\tdom.removeClass(old).css(\"background\",\"\");\n\t\t\t\tdom.addClass('jstree-themeicon-custom').css(\"background\", \"url('\" + icon + \"') center center no-repeat\").attr(\"rel\",icon);\n\t\t\t\tif(old === false) { this.show_icon(obj); }\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * get the node icon for a node\n\t\t * @name get_icon(obj)\n\t\t * @param {mixed} obj\n\t\t * @return {String}\n\t\t */\n\t\tget_icon : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn (!obj || obj.id === $.jstree.root) ? false : obj.icon;\n\t\t},\n\t\t/**\n\t\t * hide the icon on an individual node\n\t\t * @name hide_icon(obj)\n\t\t * @param {mixed} obj\n\t\t */\n\t\thide_icon : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.hide_icon(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj === $.jstree.root) { return false; }\n\t\t\tobj.icon = false;\n\t\t\tthis.get_node(obj, true).children(\".jstree-anchor\").children(\".jstree-themeicon\").addClass('jstree-themeicon-hidden');\n\t\t\treturn true;\n\t\t},\n\t\t/**\n\t\t * show the icon on an individual node\n\t\t * @name show_icon(obj)\n\t\t * @param {mixed} obj\n\t\t */\n\t\tshow_icon : function (obj) {\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.show_icon(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj === $.jstree.root) { return false; }\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tobj.icon = dom.length ? dom.children(\".jstree-anchor\").children(\".jstree-themeicon\").attr('rel') : true;\n\t\t\tif(!obj.icon) { obj.icon = true; }\n\t\t\tdom.children(\".jstree-anchor\").children(\".jstree-themeicon\").removeClass('jstree-themeicon-hidden');\n\t\t\treturn true;\n\t\t}\n\t};\n\n\t// helpers\n\t$.vakata = {};\n\t// collect attributes\n\t$.vakata.attributes = function(node, with_values) {\n\t\tnode = $(node)[0];\n\t\tvar attr = with_values ? {} : [];\n\t\tif(node && node.attributes) {\n\t\t\t$.each(node.attributes, function (i, v) {\n\t\t\t\tif($.inArray(v.name.toLowerCase(),['style','contenteditable','hasfocus','tabindex']) !== -1) { return; }\n\t\t\t\tif(v.value !== null && $.vakata.trim(v.value) !== '') {\n\t\t\t\t\tif(with_values) { attr[v.name] = v.value; }\n\t\t\t\t\telse { attr.push(v.name); }\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn attr;\n\t};\n\t$.vakata.array_unique = function(array) {\n\t\tvar a = [], i, j, l, o = {};\n\t\tfor(i = 0, l = array.length; i < l; i++) {\n\t\t\tif(o[array[i]] === undefined) {\n\t\t\t\ta.push(array[i]);\n\t\t\t\to[array[i]] = true;\n\t\t\t}\n\t\t}\n\t\treturn a;\n\t};\n\t// remove item from array\n\t$.vakata.array_remove = function(array, from) {\n\t\tarray.splice(from, 1);\n\t\treturn array;\n\t\t//var rest = array.slice((to || from) + 1 || array.length);\n\t\t//array.length = from < 0 ? array.length + from : from;\n\t\t//array.push.apply(array, rest);\n\t\t//return array;\n\t};\n\t// remove item from array\n\t$.vakata.array_remove_item = function(array, item) {\n\t\tvar tmp = $.inArray(item, array);\n\t\treturn tmp !== -1 ? $.vakata.array_remove(array, tmp) : array;\n\t};\n\t$.vakata.array_filter = function(c,a,b,d,e) {\n\t\tif (c.filter) {\n\t\t\treturn c.filter(a, b);\n\t\t}\n\t\td=[];\n\t\tfor (e in c) {\n\t\t\tif (~~e+''===e+'' && e>=0 && a.call(b,c[e],+e,c)) {\n\t\t\t\td.push(c[e]);\n\t\t\t}\n\t\t}\n\t\treturn d;\n\t};\n\t$.vakata.trim = function (text) {\n\t\treturn String.prototype.trim ? \n\t\t\tString.prototype.trim.call(text.toString()) :\n\t\t\ttext.toString().replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\t};\n\t$.vakata.is_function = function(obj) {\n\t\treturn typeof obj === \"function\" && typeof obj.nodeType !== \"number\";\n\t};\n\t$.vakata.is_array = Array.isArray || function (obj) {\n\t\treturn Object.prototype.toString.call(obj) === \"[object Array]\";\n\t};\n\n\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_objects/Function/bind#polyfill\n\tif (!Function.prototype.bind) {\n\t\tFunction.prototype.bind = function () {\n\t\t\tvar thatFunc = this, thatArg = arguments[0];\n\t\t\tvar args = Array.prototype.slice.call(arguments, 1);\n\t\t\tif (typeof thatFunc !== 'function') {\n\t\t\t\t// closest thing possible to the ECMAScript 5\n\t\t\t\t// internal IsCallable function\n\t\t\t\tthrow new TypeError('Function.prototype.bind - what is trying to be bound is not callable');\n\t\t\t}\n\t\t\treturn function(){\n\t\t\t\tvar funcArgs = args.concat(Array.prototype.slice.call(arguments));\n\t\t\t\treturn thatFunc.apply(thatArg, funcArgs);\n\t\t\t};\n\t\t};\n\t}\n\n\n/**\n * ### Changed plugin\n *\n * This plugin adds more information to the `changed.jstree` event. The new data is contained in the `changed` event data property, and contains a lists of `selected` and `deselected` nodes.\n */\n\n\t$.jstree.plugins.changed = function (options, parent) {\n\t\tvar last = [];\n\t\tthis.trigger = function (ev, data) {\n\t\t\tvar i, j;\n\t\t\tif(!data) {\n\t\t\t\tdata = {};\n\t\t\t}\n\t\t\tif(ev.replace('.jstree','') === 'changed') {\n\t\t\t\tdata.changed = { selected : [], deselected : [] };\n\t\t\t\tvar tmp = {};\n\t\t\t\tfor(i = 0, j = last.length; i < j; i++) {\n\t\t\t\t\ttmp[last[i]] = 1;\n\t\t\t\t}\n\t\t\t\tfor(i = 0, j = data.selected.length; i < j; i++) {\n\t\t\t\t\tif(!tmp[data.selected[i]]) {\n\t\t\t\t\t\tdata.changed.selected.push(data.selected[i]);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttmp[data.selected[i]] = 2;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor(i = 0, j = last.length; i < j; i++) {\n\t\t\t\t\tif(tmp[last[i]] === 1) {\n\t\t\t\t\t\tdata.changed.deselected.push(last[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlast = data.selected.slice();\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered when selection changes (the \"changed\" plugin enhances the original event with more data)\n\t\t\t * @event\n\t\t\t * @name changed.jstree\n\t\t\t * @param {Object} node\n\t\t\t * @param {Object} action the action that caused the selection to change\n\t\t\t * @param {Array} selected the current selection\n\t\t\t * @param {Object} changed an object containing two properties `selected` and `deselected` - both arrays of node IDs, which were selected or deselected since the last changed event\n\t\t\t * @param {Object} event the event (if any) that triggered this changed event\n\t\t\t * @plugin changed\n\t\t\t */\n\t\t\tparent.trigger.call(this, ev, data);\n\t\t};\n\t\tthis.refresh = function (skip_loading, forget_state) {\n\t\t\tlast = [];\n\t\t\treturn parent.refresh.apply(this, arguments);\n\t\t};\n\t};\n\n/**\n * ### Checkbox plugin\n *\n * This plugin renders checkbox icons in front of each node, making multiple selection much easier.\n * It also supports tri-state behavior, meaning that if a node has a few of its children checked it will be rendered as undetermined, and state will be propagated up.\n */\n\n\tvar _i = document.createElement('I');\n\t_i.className = 'jstree-icon jstree-checkbox';\n\t_i.setAttribute('role', 'presentation');\n\t/**\n\t * stores all defaults for the checkbox plugin\n\t * @name $.jstree.defaults.checkbox\n\t * @plugin checkbox\n\t */\n\t$.jstree.defaults.checkbox = {\n\t\t/**\n\t\t * a boolean indicating if checkboxes should be visible (can be changed at a later time using `show_checkboxes()` and `hide_checkboxes`). Defaults to `true`.\n\t\t * @name $.jstree.defaults.checkbox.visible\n\t\t * @plugin checkbox\n\t\t */\n\t\tvisible\t\t\t\t: true,\n\t\t/**\n\t\t * a boolean indicating if checkboxes should cascade down and have an undetermined state. Defaults to `true`.\n\t\t * @name $.jstree.defaults.checkbox.three_state\n\t\t * @plugin checkbox\n\t\t */\n\t\tthree_state\t\t\t: true,\n\t\t/**\n\t\t * a boolean indicating if clicking anywhere on the node should act as clicking on the checkbox. Defaults to `true`.\n\t\t * @name $.jstree.defaults.checkbox.whole_node\n\t\t * @plugin checkbox\n\t\t */\n\t\twhole_node\t\t\t: true,\n\t\t/**\n\t\t * a boolean indicating if the selected style of a node should be kept, or removed. Defaults to `true`.\n\t\t * @name $.jstree.defaults.checkbox.keep_selected_style\n\t\t * @plugin checkbox\n\t\t */\n\t\tkeep_selected_style\t: true,\n\t\t/**\n\t\t * This setting controls how cascading and undetermined nodes are applied.\n\t\t * If 'up' is in the string - cascading up is enabled, if 'down' is in the string - cascading down is enabled, if 'undetermined' is in the string - undetermined nodes will be used.\n\t\t * If `three_state` is set to `true` this setting is automatically set to 'up+down+undetermined'. Defaults to ''.\n\t\t * @name $.jstree.defaults.checkbox.cascade\n\t\t * @plugin checkbox\n\t\t */\n\t\tcascade\t\t\t\t: '',\n\t\t/**\n\t\t * This setting controls if checkbox are bound to the general tree selection or to an internal array maintained by the checkbox plugin. Defaults to `true`, only set to `false` if you know exactly what you are doing.\n\t\t * @name $.jstree.defaults.checkbox.tie_selection\n\t\t * @plugin checkbox\n\t\t */\n\t\ttie_selection\t\t: true,\n\n\t\t/**\n\t\t * This setting controls if cascading down affects disabled checkboxes\n\t\t * @name $.jstree.defaults.checkbox.cascade_to_disabled\n\t\t * @plugin checkbox\n\t\t */\n\t\tcascade_to_disabled : true,\n\n\t\t/**\n\t\t * This setting controls if cascading down affects hidden checkboxes\n\t\t * @name $.jstree.defaults.checkbox.cascade_to_hidden\n\t\t * @plugin checkbox\n\t\t */\n\t\tcascade_to_hidden : true\n\t};\n\t$.jstree.plugins.checkbox = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\t\t\tthis._data.checkbox.uto = false;\n\t\t\tthis._data.checkbox.selected = [];\n\t\t\tif(this.settings.checkbox.three_state) {\n\t\t\t\tthis.settings.checkbox.cascade = 'up+down+undetermined';\n\t\t\t}\n\t\t\tthis.element\n\t\t\t\t.on(\"init.jstree\", function () {\n\t\t\t\t\t\tthis._data.checkbox.visible = this.settings.checkbox.visible;\n\t\t\t\t\t\tif(!this.settings.checkbox.keep_selected_style) {\n\t\t\t\t\t\t\tthis.element.addClass('jstree-checkbox-no-clicked');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(this.settings.checkbox.tie_selection) {\n\t\t\t\t\t\t\tthis.element.addClass('jstree-checkbox-selection');\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"loading.jstree\", function () {\n\t\t\t\t\t\tthis[ this._data.checkbox.visible ? 'show_checkboxes' : 'hide_checkboxes' ]();\n\t\t\t\t\t}.bind(this));\n\t\t\tif(this.settings.checkbox.cascade.indexOf('undetermined') !== -1) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on('changed.jstree uncheck_node.jstree check_node.jstree uncheck_all.jstree check_all.jstree move_node.jstree copy_node.jstree redraw.jstree open_node.jstree', function () {\n\t\t\t\t\t\t\t// only if undetermined is in setting\n\t\t\t\t\t\t\tif(this._data.checkbox.uto) { clearTimeout(this._data.checkbox.uto); }\n\t\t\t\t\t\t\tthis._data.checkbox.uto = setTimeout(this._undetermined.bind(this), 50);\n\t\t\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t\tif(!this.settings.checkbox.tie_selection) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on('model.jstree', function (e, data) {\n\t\t\t\t\t\tvar m = this._model.data,\n\t\t\t\t\t\t\tp = m[data.parent],\n\t\t\t\t\t\t\tdpc = data.nodes,\n\t\t\t\t\t\t\ti, j;\n\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\tm[dpc[i]].state.checked = m[dpc[i]].state.checked || (m[dpc[i]].original && m[dpc[i]].original.state && m[dpc[i]].original.state.checked);\n\t\t\t\t\t\t\tif(m[dpc[i]].state.checked) {\n\t\t\t\t\t\t\t\tthis._data.checkbox.selected.push(dpc[i]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t\tif(this.settings.checkbox.cascade.indexOf('up') !== -1 || this.settings.checkbox.cascade.indexOf('down') !== -1) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on('model.jstree', function (e, data) {\n\t\t\t\t\t\t\tvar m = this._model.data,\n\t\t\t\t\t\t\t\tp = m[data.parent],\n\t\t\t\t\t\t\t\tdpc = data.nodes,\n\t\t\t\t\t\t\t\tchd = [],\n\t\t\t\t\t\t\t\tc, i, j, k, l, tmp, s = this.settings.checkbox.cascade, t = this.settings.checkbox.tie_selection;\n\n\t\t\t\t\t\t\tif(s.indexOf('down') !== -1) {\n\t\t\t\t\t\t\t\t// apply down\n\t\t\t\t\t\t\t\tif(p.state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tm[dpc[i]].state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected = this._data[ t ? 'core' : 'checkbox' ].selected.concat(dpc);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tif(m[dpc[i]].state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\t\t\t\t\t\t\tfor(k = 0, l = m[dpc[i]].children_d.length; k < l; k++) {\n\t\t\t\t\t\t\t\t\t\t\t\tm[m[dpc[i]].children_d[k]].state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected = this._data[ t ? 'core' : 'checkbox' ].selected.concat(m[dpc[i]].children_d);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif(s.indexOf('up') !== -1) {\n\t\t\t\t\t\t\t\t// apply up\n\t\t\t\t\t\t\t\tfor(i = 0, j = p.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tif(!m[p.children_d[i]].children.length) {\n\t\t\t\t\t\t\t\t\t\tchd.push(m[p.children_d[i]].parent);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tchd = $.vakata.array_unique(chd);\n\t\t\t\t\t\t\t\tfor(k = 0, l = chd.length; k < l; k++) {\n\t\t\t\t\t\t\t\t\tp = m[chd[k]];\n\t\t\t\t\t\t\t\t\twhile(p && p.id !== $.jstree.root) {\n\t\t\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state[ t ? 'selected' : 'checked' ];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\t\t\tp.state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected.push(p.id);\n\t\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', true).addClass( t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected = $.vakata.array_unique(this._data[ t ? 'core' : 'checkbox' ].selected);\n\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t.on(this.settings.checkbox.tie_selection ? 'select_node.jstree' : 'check_node.jstree', function (e, data) {\n\t\t\t\t\t\t\tvar self = this,\n\t\t\t\t\t\t\t\tobj = data.node,\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\tpar = this.get_node(obj.parent),\n\t\t\t\t\t\t\t\ti, j, c, tmp, s = this.settings.checkbox.cascade, t = this.settings.checkbox.tie_selection,\n\t\t\t\t\t\t\t\tsel = {}, cur = this._data[ t ? 'core' : 'checkbox' ].selected;\n\n\t\t\t\t\t\t\tfor (i = 0, j = cur.length; i < j; i++) {\n\t\t\t\t\t\t\t\tsel[cur[i]] = true;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// apply down\n\t\t\t\t\t\t\tif(s.indexOf('down') !== -1) {\n\t\t\t\t\t\t\t\t//this._data[ t ? 'core' : 'checkbox' ].selected = $.vakata.array_unique(this._data[ t ? 'core' : 'checkbox' ].selected.concat(obj.children_d));\n\t\t\t\t\t\t\t\tvar selectedIds = this._cascade_new_checked_state(obj.id, true);\n\t\t\t\t\t\t\t\tvar temp = obj.children_d.concat(obj.id);\n\t\t\t\t\t\t\t\tfor (i = 0, j = temp.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tif (selectedIds.indexOf(temp[i]) > -1) {\n\t\t\t\t\t\t\t\t\t\tsel[temp[i]] = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tdelete sel[temp[i]];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// apply up\n\t\t\t\t\t\t\tif(s.indexOf('up') !== -1) {\n\t\t\t\t\t\t\t\twhile(par && par.id !== $.jstree.root) {\n\t\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tc += m[par.children[i]].state[ t ? 'selected' : 'checked' ];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\t\tpar.state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\t\tsel[par.id] = true;\n\t\t\t\t\t\t\t\t\t\t//this._data[ t ? 'core' : 'checkbox' ].selected.push(par.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(par, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', true).addClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tpar = this.get_node(par.parent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tcur = [];\n\t\t\t\t\t\t\tfor (i in sel) {\n\t\t\t\t\t\t\t\tif (sel.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\tcur.push(i);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected = cur;\n\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t.on(this.settings.checkbox.tie_selection ? 'deselect_all.jstree' : 'uncheck_all.jstree', function (e, data) {\n\t\t\t\t\t\t\tvar obj = this.get_node($.jstree.root),\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\ti, j, tmp;\n\t\t\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\ttmp = m[obj.children_d[i]];\n\t\t\t\t\t\t\t\tif(tmp && tmp.original && tmp.original.state && tmp.original.state.undetermined) {\n\t\t\t\t\t\t\t\t\ttmp.original.state.undetermined = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t.on(this.settings.checkbox.tie_selection ? 'deselect_node.jstree' : 'uncheck_node.jstree', function (e, data) {\n\t\t\t\t\t\t\tvar self = this,\n\t\t\t\t\t\t\t\tobj = data.node,\n\t\t\t\t\t\t\t\tdom = this.get_node(obj, true),\n\t\t\t\t\t\t\t\ti, j, tmp, s = this.settings.checkbox.cascade, t = this.settings.checkbox.tie_selection,\n\t\t\t\t\t\t\t\tcur = this._data[ t ? 'core' : 'checkbox' ].selected, sel = {},\n\t\t\t\t\t\t\t\tstillSelectedIds = [],\n\t\t\t\t\t\t\t\tallIds = obj.children_d.concat(obj.id);\n\n\t\t\t\t\t\t\t// apply down\n\t\t\t\t\t\t\tif(s.indexOf('down') !== -1) {\n\t\t\t\t\t\t\t\tvar selectedIds = this._cascade_new_checked_state(obj.id, false);\n\n\t\t\t\t\t\t\t\tcur = $.vakata.array_filter(cur, function(id) {\n\t\t\t\t\t\t\t\t\treturn allIds.indexOf(id) === -1 || selectedIds.indexOf(id) > -1;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// only apply up if cascade up is enabled and if this node is not selected\n\t\t\t\t\t\t\t// (if all child nodes are disabled and cascade_to_disabled === false then this node will till be selected).\n\t\t\t\t\t\t\tif(s.indexOf('up') !== -1 && cur.indexOf(obj.id) === -1) {\n\t\t\t\t\t\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\ttmp = this._model.data[obj.parents[i]];\n\t\t\t\t\t\t\t\t\ttmp.state[ t ? 'selected' : 'checked' ] = false;\n\t\t\t\t\t\t\t\t\tif(tmp && tmp.original && tmp.original.state && tmp.original.state.undetermined) {\n\t\t\t\t\t\t\t\t\t\ttmp.original.state.undetermined = false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\ttmp = this.get_node(obj.parents[i], true);\n\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', false).removeClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tcur = $.vakata.array_filter(cur, function(id) {\n\t\t\t\t\t\t\t\t\treturn obj.parents.indexOf(id) === -1;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected = cur;\n\t\t\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t\tif(this.settings.checkbox.cascade.indexOf('up') !== -1) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on('delete_node.jstree', function (e, data) {\n\t\t\t\t\t\t\t// apply up (whole handler)\n\t\t\t\t\t\t\tvar p = this.get_node(data.parent),\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\ti, j, c, tmp, t = this.settings.checkbox.tie_selection;\n\t\t\t\t\t\t\twhile(p && p.id !== $.jstree.root && !p.state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state[ t ? 'selected' : 'checked' ];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(j > 0 && c === j) {\n\t\t\t\t\t\t\t\t\tp.state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected.push(p.id);\n\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', true).addClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t.on('move_node.jstree', function (e, data) {\n\t\t\t\t\t\t\t// apply up (whole handler)\n\t\t\t\t\t\t\tvar is_multi = data.is_multi,\n\t\t\t\t\t\t\t\told_par = data.old_parent,\n\t\t\t\t\t\t\t\tnew_par = this.get_node(data.parent),\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\tp, c, i, j, tmp, t = this.settings.checkbox.tie_selection;\n\t\t\t\t\t\t\tif(!is_multi) {\n\t\t\t\t\t\t\t\tp = this.get_node(old_par);\n\t\t\t\t\t\t\t\twhile(p && p.id !== $.jstree.root && !p.state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state[ t ? 'selected' : 'checked' ];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif(j > 0 && c === j) {\n\t\t\t\t\t\t\t\t\t\tp.state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected.push(p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', true).addClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tp = new_par;\n\t\t\t\t\t\t\twhile(p && p.id !== $.jstree.root) {\n\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state[ t ? 'selected' : 'checked' ];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\tif(!p.state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\t\t\t\t\t\tp.state[ t ? 'selected' : 'checked' ] = true;\n\t\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected.push(p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', true).addClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tif(p.state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\t\t\t\t\t\tp.state[ t ? 'selected' : 'checked' ] = false;\n\t\t\t\t\t\t\t\t\t\tthis._data[ t ? 'core' : 'checkbox' ].selected = $.vakata.array_remove_item(this._data[ t ? 'core' : 'checkbox' ].selected, p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').attr('aria-selected', false).removeClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t};\n\t\t/**\n\t\t * get an array of all nodes whose state is \"undetermined\"\n\t\t * @name get_undetermined([full])\n\t\t * @param  {boolean} full: if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.get_undetermined = function (full) {\n\t\t\tif (this.settings.checkbox.cascade.indexOf('undetermined') === -1) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\tvar i, j, k, l, o = {}, m = this._model.data, t = this.settings.checkbox.tie_selection, s = this._data[ t ? 'core' : 'checkbox' ].selected, p = [], tt = this, r = [];\n\t\t\tfor(i = 0, j = s.length; i < j; i++) {\n\t\t\t\tif(m[s[i]] && m[s[i]].parents) {\n\t\t\t\t\tfor(k = 0, l = m[s[i]].parents.length; k < l; k++) {\n\t\t\t\t\t\tif(o[m[s[i]].parents[k]] !== undefined) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(m[s[i]].parents[k] !== $.jstree.root) {\n\t\t\t\t\t\t\to[m[s[i]].parents[k]] = true;\n\t\t\t\t\t\t\tp.push(m[s[i]].parents[k]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t// attempt for server side undetermined state\n\t\t\tthis.element.find('.jstree-closed').not(':has(.jstree-children)')\n\t\t\t\t.each(function () {\n\t\t\t\t\tvar tmp = tt.get_node(this), tmp2;\n\t\t\t\t\t\n\t\t\t\t\tif(!tmp) { return; }\n\t\t\t\t\t\n\t\t\t\t\tif(!tmp.state.loaded) {\n\t\t\t\t\t\tif(tmp.original && tmp.original.state && tmp.original.state.undetermined && tmp.original.state.undetermined === true) {\n\t\t\t\t\t\t\tif(o[tmp.id] === undefined && tmp.id !== $.jstree.root) {\n\t\t\t\t\t\t\t\to[tmp.id] = true;\n\t\t\t\t\t\t\t\tp.push(tmp.id);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor(k = 0, l = tmp.parents.length; k < l; k++) {\n\t\t\t\t\t\t\t\tif(o[tmp.parents[k]] === undefined && tmp.parents[k] !== $.jstree.root) {\n\t\t\t\t\t\t\t\t\to[tmp.parents[k]] = true;\n\t\t\t\t\t\t\t\t\tp.push(tmp.parents[k]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tfor(i = 0, j = tmp.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\ttmp2 = m[tmp.children_d[i]];\n\t\t\t\t\t\t\tif(!tmp2.state.loaded && tmp2.original && tmp2.original.state && tmp2.original.state.undetermined && tmp2.original.state.undetermined === true) {\n\t\t\t\t\t\t\t\tif(o[tmp2.id] === undefined && tmp2.id !== $.jstree.root) {\n\t\t\t\t\t\t\t\t\to[tmp2.id] = true;\n\t\t\t\t\t\t\t\t\tp.push(tmp2.id);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tfor(k = 0, l = tmp2.parents.length; k < l; k++) {\n\t\t\t\t\t\t\t\t\tif(o[tmp2.parents[k]] === undefined && tmp2.parents[k] !== $.jstree.root) {\n\t\t\t\t\t\t\t\t\t\to[tmp2.parents[k]] = true;\n\t\t\t\t\t\t\t\t\t\tp.push(tmp2.parents[k]);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\tfor (i = 0, j = p.length; i < j; i++) {\n\t\t\t\tif(!m[p[i]].state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\tr.push(full ? m[p[i]] : p[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn r;\n\t\t};\n\t\t/**\n\t\t * set the undetermined state where and if necessary. Used internally.\n\t\t * @private\n\t\t * @name _undetermined()\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis._undetermined = function () {\n\t\t\tif(this.element === null) { return; }\n\t\t\tvar p = this.get_undetermined(false), i, j, s;\n\n\t\t\tthis.element.find('.jstree-undetermined').removeClass('jstree-undetermined');\n\t\t\tfor (i = 0, j = p.length; i < j; i++) {\n\t\t\t\ts = this.get_node(p[i], true);\n\t\t\t\tif(s && s.length) {\n\t\t\t\t\ts.children('.jstree-anchor').children('.jstree-checkbox').addClass('jstree-undetermined');\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tthis.redraw_node = function(obj, deep, is_callback, force_render) {\n\t\t\tobj = parent.redraw_node.apply(this, arguments);\n\t\t\tif(obj) {\n\t\t\t\tvar i, j, tmp = null, icon = null;\n\t\t\t\tfor(i = 0, j = obj.childNodes.length; i < j; i++) {\n\t\t\t\t\tif(obj.childNodes[i] && obj.childNodes[i].className && obj.childNodes[i].className.indexOf(\"jstree-anchor\") !== -1) {\n\t\t\t\t\t\ttmp = obj.childNodes[i];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(tmp) {\n\t\t\t\t\tif(!this.settings.checkbox.tie_selection && this._model.data[obj.id].state.checked) { tmp.className += ' jstree-checked'; }\n\t\t\t\t\ticon = _i.cloneNode(false);\n\t\t\t\t\tif(this._model.data[obj.id].state.checkbox_disabled) { icon.className += ' jstree-checkbox-disabled'; }\n\t\t\t\t\ttmp.insertBefore(icon, tmp.childNodes[0]);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!is_callback && this.settings.checkbox.cascade.indexOf('undetermined') !== -1) {\n\t\t\t\tif(this._data.checkbox.uto) { clearTimeout(this._data.checkbox.uto); }\n\t\t\t\tthis._data.checkbox.uto = setTimeout(this._undetermined.bind(this), 50);\n\t\t\t}\n\t\t\treturn obj;\n\t\t};\n\t\t/**\n\t\t * show the node checkbox icons\n\t\t * @name show_checkboxes()\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.show_checkboxes = function () { this._data.core.themes.checkboxes = true; this.get_container_ul().removeClass(\"jstree-no-checkboxes\"); };\n\t\t/**\n\t\t * hide the node checkbox icons\n\t\t * @name hide_checkboxes()\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.hide_checkboxes = function () { this._data.core.themes.checkboxes = false; this.get_container_ul().addClass(\"jstree-no-checkboxes\"); };\n\t\t/**\n\t\t * toggle the node icons\n\t\t * @name toggle_checkboxes()\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.toggle_checkboxes = function () { if(this._data.core.themes.checkboxes) { this.hide_checkboxes(); } else { this.show_checkboxes(); } };\n\t\t/**\n\t\t * checks if a node is in an undetermined state\n\t\t * @name is_undetermined(obj)\n\t\t * @param  {mixed} obj\n\t\t * @return {Boolean}\n\t\t */\n\t\tthis.is_undetermined = function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tvar s = this.settings.checkbox.cascade, i, j, t = this.settings.checkbox.tie_selection, d = this._data[ t ? 'core' : 'checkbox' ].selected, m = this._model.data;\n\t\t\tif(!obj || obj.state[ t ? 'selected' : 'checked' ] === true || s.indexOf('undetermined') === -1 || (s.indexOf('down') === -1 && s.indexOf('up') === -1)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(!obj.state.loaded && obj.original.state.undetermined === true) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\tif($.inArray(obj.children_d[i], d) !== -1 || (!m[obj.children_d[i]].state.loaded && m[obj.children_d[i]].original.state.undetermined)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n\t\t/**\n\t\t * disable a node's checkbox\n\t\t * @name disable_checkbox(obj)\n\t\t * @param {mixed} obj an array can be used too\n\t\t * @trigger disable_checkbox.jstree\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.disable_checkbox = function (obj) {\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.disable_checkbox(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(!obj.state.checkbox_disabled) {\n\t\t\t\tobj.state.checkbox_disabled = true;\n\t\t\t\tif(dom && dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').children('.jstree-checkbox').addClass('jstree-checkbox-disabled');\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node's checkbox is disabled\n\t\t\t\t * @event\n\t\t\t\t * @name disable_checkbox.jstree\n\t\t\t\t * @param {Object} node\n\t\t\t\t * @plugin checkbox\n\t\t\t\t */\n\t\t\t\tthis.trigger('disable_checkbox', { 'node' : obj });\n\t\t\t}\n\t\t};\n\t\t/**\n\t\t * enable a node's checkbox\n\t\t * @name enable_checkbox(obj)\n\t\t * @param {mixed} obj an array can be used too\n\t\t * @trigger enable_checkbox.jstree\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.enable_checkbox = function (obj) {\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.enable_checkbox(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(obj.state.checkbox_disabled) {\n\t\t\t\tobj.state.checkbox_disabled = false;\n\t\t\t\tif(dom && dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').children('.jstree-checkbox').removeClass('jstree-checkbox-disabled');\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node's checkbox is enabled\n\t\t\t\t * @event\n\t\t\t\t * @name enable_checkbox.jstree\n\t\t\t\t * @param {Object} node\n\t\t\t\t * @plugin checkbox\n\t\t\t\t */\n\t\t\t\tthis.trigger('enable_checkbox', { 'node' : obj });\n\t\t\t}\n\t\t};\n\n\t\tthis.activate_node = function (obj, e) {\n\t\t\tif($(e.target).hasClass('jstree-checkbox-disabled')) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(this.settings.checkbox.tie_selection && (this.settings.checkbox.whole_node || $(e.target).hasClass('jstree-checkbox'))) {\n\t\t\t\te.ctrlKey = true;\n\t\t\t}\n\t\t\tif(this.settings.checkbox.tie_selection || (!this.settings.checkbox.whole_node && !$(e.target).hasClass('jstree-checkbox'))) {\n\t\t\t\treturn parent.activate_node.call(this, obj, e);\n\t\t\t}\n\t\t\tif(this.is_disabled(obj)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(this.is_checked(obj)) {\n\t\t\t\tthis.uncheck_node(obj, e);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.check_node(obj, e);\n\t\t\t}\n\t\t\tthis.trigger('activate_node', { 'node' : this.get_node(obj) });\n\t\t};\n\t\tthis.delete_node = function (obj) {\n\t\t\tif(this.settings.checkbox.tie_selection || $.vakata.is_array(obj)) {\n\t\t\t\treturn parent.delete_node.call(this, obj);\n\t\t\t}\n\t\t\tvar tmp, k, l, c = false;\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\ttmp = obj.children_d.concat([]);\n\t\t\ttmp.push(obj.id);\n\t\t\tfor(k = 0, l = tmp.length; k < l; k++) {\n\t\t\t\tif(this._model.data[tmp[k]].state.checked) {\n\t\t\t\t\tc = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (c) {\n\t\t\t\tthis._data.checkbox.selected = $.vakata.array_filter(this._data.checkbox.selected, function (v) {\n\t\t\t\t\treturn $.inArray(v, tmp) === -1;\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn parent.delete_node.call(this, obj);\n\t\t};\n\n\t\t/**\n\t\t * Cascades checked state to a node and all its descendants. This function does NOT affect hidden and disabled nodes (or their descendants).\n\t\t * However if these unaffected nodes are already selected their ids will be included in the returned array.\n\t\t * @private\n\t\t * @name _cascade_new_checked_state(id, checkedState)\n\t\t * @param {string} id the node ID\n\t\t * @param {bool} checkedState should the nodes be checked or not\n\t\t * @returns {Array} Array of all node id's (in this tree branch) that are checked.\n\t\t */\n\t\tthis._cascade_new_checked_state = function (id, checkedState) {\n\t\t\tvar self = this;\n\t\t\tvar t = this.settings.checkbox.tie_selection;\n\t\t\tvar node = this._model.data[id];\n\t\t\tvar selectedNodeIds = [];\n\t\t\tvar selectedChildrenIds = [], i, j, selectedChildIds;\n\n\t\t\tif (\n\t\t\t\t(this.settings.checkbox.cascade_to_disabled || !node.state.disabled) &&\n\t\t\t\t(this.settings.checkbox.cascade_to_hidden || !node.state.hidden)\n\t\t\t) {\n\t\t\t\t//First try and check/uncheck the children\n\t\t\t\tif (node.children) {\n\t\t\t\t\tfor (i = 0, j = node.children.length; i < j; i++) {\n\t\t\t\t\t\tvar childId = node.children[i];\n\t\t\t\t\t\tselectedChildIds = self._cascade_new_checked_state(childId, checkedState);\n\t\t\t\t\t\tselectedNodeIds = selectedNodeIds.concat(selectedChildIds);\n\t\t\t\t\t\tif (selectedChildIds.indexOf(childId) > -1) {\n\t\t\t\t\t\t\tselectedChildrenIds.push(childId);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar dom = self.get_node(node, true);\n\n\t\t\t\t//A node's state is undetermined if some but not all of it's children are checked/selected .\n\t\t\t\tvar undetermined = selectedChildrenIds.length > 0 && selectedChildrenIds.length < node.children.length;\n\n\t\t\t\tif(node.original && node.original.state && node.original.state.undetermined) {\n\t\t\t\t\tnode.original.state.undetermined = undetermined;\n\t\t\t\t}\n\n\t\t\t\t//If a node is undetermined then remove selected class\n\t\t\t\tif (undetermined) {\n\t\t\t\t\tnode.state[ t ? 'selected' : 'checked' ] = false;\n\t\t\t\t\tdom.children('.jstree-anchor').attr('aria-selected', false).removeClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t}\n\t\t\t\t//Otherwise, if the checkedState === true (i.e. the node is being checked now) and all of the node's children are checked (if it has any children),\n\t\t\t\t//check the node and style it correctly.\n\t\t\t\telse if (checkedState && selectedChildrenIds.length === node.children.length) {\n\t\t\t\t\tnode.state[ t ? 'selected' : 'checked' ] = checkedState;\n\t\t\t\t\tselectedNodeIds.push(node.id);\n\n\t\t\t\t\tdom.children('.jstree-anchor').attr('aria-selected', true).addClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tnode.state[ t ? 'selected' : 'checked' ] = false;\n\t\t\t\t\tdom.children('.jstree-anchor').attr('aria-selected', false).removeClass(t ? 'jstree-clicked' : 'jstree-checked');\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tselectedChildIds = this.get_checked_descendants(id);\n\n\t\t\t\tif (node.state[ t ? 'selected' : 'checked' ]) {\n\t\t\t\t\tselectedChildIds.push(node.id);\n\t\t\t\t}\n\n\t\t\t\tselectedNodeIds = selectedNodeIds.concat(selectedChildIds);\n\t\t\t}\n\n\t\t\treturn selectedNodeIds;\n\t\t};\n\n\t\t/**\n\t\t * Gets ids of nodes selected in branch (of tree) specified by id (does not include the node specified by id)\n\t\t * @name get_checked_descendants(obj)\n\t\t * @param {string} id the node ID\n\t\t * @return {Array} array of IDs\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.get_checked_descendants = function (id) {\n\t\t\tvar self = this;\n\t\t\tvar t = self.settings.checkbox.tie_selection;\n\t\t\tvar node = self._model.data[id];\n\n\t\t\treturn $.vakata.array_filter(node.children_d, function(_id) {\n\t\t\t\treturn self._model.data[_id].state[ t ? 'selected' : 'checked' ];\n\t\t\t});\n\t\t};\n\n\t\t/**\n\t\t * check a node (only if tie_selection in checkbox settings is false, otherwise select_node will be called internally)\n\t\t * @name check_node(obj)\n\t\t * @param {mixed} obj an array can be used to check multiple nodes\n\t\t * @trigger check_node.jstree\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.check_node = function (obj, e) {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.select_node(obj, false, true, e); }\n\t\t\tvar dom, t1, t2, th;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.check_node(obj[t1], e);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(!obj.state.checked) {\n\t\t\t\tobj.state.checked = true;\n\t\t\t\tthis._data.checkbox.selected.push(obj.id);\n\t\t\t\tif(dom && dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').addClass('jstree-checked');\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node is checked (only if tie_selection in checkbox settings is false)\n\t\t\t\t * @event\n\t\t\t\t * @name check_node.jstree\n\t\t\t\t * @param {Object} node\n\t\t\t\t * @param {Array} selected the current selection\n\t\t\t\t * @param {Object} event the event (if any) that triggered this check_node\n\t\t\t\t * @plugin checkbox\n\t\t\t\t */\n\t\t\t\tthis.trigger('check_node', { 'node' : obj, 'selected' : this._data.checkbox.selected, 'event' : e });\n\t\t\t}\n\t\t};\n\t\t/**\n\t\t * uncheck a node (only if tie_selection in checkbox settings is false, otherwise deselect_node will be called internally)\n\t\t * @name uncheck_node(obj)\n\t\t * @param {mixed} obj an array can be used to uncheck multiple nodes\n\t\t * @trigger uncheck_node.jstree\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.uncheck_node = function (obj, e) {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.deselect_node(obj, false, e); }\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.uncheck_node(obj[t1], e);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(obj.state.checked) {\n\t\t\t\tobj.state.checked = false;\n\t\t\t\tthis._data.checkbox.selected = $.vakata.array_remove_item(this._data.checkbox.selected, obj.id);\n\t\t\t\tif(dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').removeClass('jstree-checked');\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered when an node is unchecked (only if tie_selection in checkbox settings is false)\n\t\t\t\t * @event\n\t\t\t\t * @name uncheck_node.jstree\n\t\t\t\t * @param {Object} node\n\t\t\t\t * @param {Array} selected the current selection\n\t\t\t\t * @param {Object} event the event (if any) that triggered this uncheck_node\n\t\t\t\t * @plugin checkbox\n\t\t\t\t */\n\t\t\t\tthis.trigger('uncheck_node', { 'node' : obj, 'selected' : this._data.checkbox.selected, 'event' : e });\n\t\t\t}\n\t\t};\n\t\t\n\t\t/**\n\t\t * checks all nodes in the tree (only if tie_selection in checkbox settings is false, otherwise select_all will be called internally)\n\t\t * @name check_all()\n\t\t * @trigger check_all.jstree, changed.jstree\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.check_all = function () {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.select_all(); }\n\t\t\tvar tmp = this._data.checkbox.selected.concat([]), i, j;\n\t\t\tthis._data.checkbox.selected = this._model.data[$.jstree.root].children_d.concat();\n\t\t\tfor(i = 0, j = this._data.checkbox.selected.length; i < j; i++) {\n\t\t\t\tif(this._model.data[this._data.checkbox.selected[i]]) {\n\t\t\t\t\tthis._model.data[this._data.checkbox.selected[i]].state.checked = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.redraw(true);\n\t\t\t/**\n\t\t\t * triggered when all nodes are checked (only if tie_selection in checkbox settings is false)\n\t\t\t * @event\n\t\t\t * @name check_all.jstree\n\t\t\t * @param {Array} selected the current selection\n\t\t\t * @plugin checkbox\n\t\t\t */\n\t\t\tthis.trigger('check_all', { 'selected' : this._data.checkbox.selected });\n\t\t};\n\t\t/**\n\t\t * uncheck all checked nodes (only if tie_selection in checkbox settings is false, otherwise deselect_all will be called internally)\n\t\t * @name uncheck_all()\n\t\t * @trigger uncheck_all.jstree\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.uncheck_all = function () {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.deselect_all(); }\n\t\t\tvar tmp = this._data.checkbox.selected.concat([]), i, j;\n\t\t\tfor(i = 0, j = this._data.checkbox.selected.length; i < j; i++) {\n\t\t\t\tif(this._model.data[this._data.checkbox.selected[i]]) {\n\t\t\t\t\tthis._model.data[this._data.checkbox.selected[i]].state.checked = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._data.checkbox.selected = [];\n\t\t\tthis.element.find('.jstree-checked').removeClass('jstree-checked');\n\t\t\t/**\n\t\t\t * triggered when all nodes are unchecked (only if tie_selection in checkbox settings is false)\n\t\t\t * @event\n\t\t\t * @name uncheck_all.jstree\n\t\t\t * @param {Object} node the previous selection\n\t\t\t * @param {Array} selected the current selection\n\t\t\t * @plugin checkbox\n\t\t\t */\n\t\t\tthis.trigger('uncheck_all', { 'selected' : this._data.checkbox.selected, 'node' : tmp });\n\t\t};\n\t\t/**\n\t\t * checks if a node is checked (if tie_selection is on in the settings this function will return the same as is_selected)\n\t\t * @name is_checked(obj)\n\t\t * @param  {mixed}  obj\n\t\t * @return {Boolean}\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.is_checked = function (obj) {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.is_selected(obj); }\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\treturn obj.state.checked;\n\t\t};\n\t\t/**\n\t\t * get an array of all checked nodes (if tie_selection is on in the settings this function will return the same as get_selected)\n\t\t * @name get_checked([full])\n\t\t * @param  {mixed}  full if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.get_checked = function (full) {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.get_selected(full); }\n\t\t\treturn full ? $.map(this._data.checkbox.selected, function (i) { return this.get_node(i); }.bind(this)) : this._data.checkbox.selected.slice();\n\t\t};\n\t\t/**\n\t\t * get an array of all top level checked nodes (ignoring children of checked nodes) (if tie_selection is on in the settings this function will return the same as get_top_selected)\n\t\t * @name get_top_checked([full])\n\t\t * @param  {mixed}  full if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.get_top_checked = function (full) {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.get_top_selected(full); }\n\t\t\tvar tmp = this.get_checked(true),\n\t\t\t\tobj = {}, i, j, k, l;\n\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\tobj[tmp[i].id] = tmp[i];\n\t\t\t}\n\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\tfor(k = 0, l = tmp[i].children_d.length; k < l; k++) {\n\t\t\t\t\tif(obj[tmp[i].children_d[k]]) {\n\t\t\t\t\t\tdelete obj[tmp[i].children_d[k]];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = [];\n\t\t\tfor(i in obj) {\n\t\t\t\tif(obj.hasOwnProperty(i)) {\n\t\t\t\t\ttmp.push(i);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn full ? $.map(tmp, function (i) { return this.get_node(i); }.bind(this)) : tmp;\n\t\t};\n\t\t/**\n\t\t * get an array of all bottom level checked nodes (ignoring selected parents) (if tie_selection is on in the settings this function will return the same as get_bottom_selected)\n\t\t * @name get_bottom_checked([full])\n\t\t * @param  {mixed}  full if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned\n\t\t * @return {Array}\n\t\t * @plugin checkbox\n\t\t */\n\t\tthis.get_bottom_checked = function (full) {\n\t\t\tif(this.settings.checkbox.tie_selection) { return this.get_bottom_selected(full); }\n\t\t\tvar tmp = this.get_checked(true),\n\t\t\t\tobj = [], i, j;\n\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\tif(!tmp[i].children.length) {\n\t\t\t\t\tobj.push(tmp[i].id);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn full ? $.map(obj, function (i) { return this.get_node(i); }.bind(this)) : obj;\n\t\t};\n\t\tthis.load_node = function (obj, callback) {\n\t\t\tvar k, l, i, j, c, tmp;\n\t\t\tif(!$.vakata.is_array(obj) && !this.settings.checkbox.tie_selection) {\n\t\t\t\ttmp = this.get_node(obj);\n\t\t\t\tif(tmp && tmp.state.loaded) {\n\t\t\t\t\tfor(k = 0, l = tmp.children_d.length; k < l; k++) {\n\t\t\t\t\t\tif(this._model.data[tmp.children_d[k]].state.checked) {\n\t\t\t\t\t\t\tc = true;\n\t\t\t\t\t\t\tthis._data.checkbox.selected = $.vakata.array_remove_item(this._data.checkbox.selected, tmp.children_d[k]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn parent.load_node.apply(this, arguments);\n\t\t};\n\t\tthis.get_state = function () {\n\t\t\tvar state = parent.get_state.apply(this, arguments);\n\t\t\tif(this.settings.checkbox.tie_selection) { return state; }\n\t\t\tstate.checkbox = this._data.checkbox.selected.slice();\n\t\t\treturn state;\n\t\t};\n\t\tthis.set_state = function (state, callback) {\n\t\t\tvar res = parent.set_state.apply(this, arguments);\n\t\t\tif(res && state.checkbox) {\n\t\t\t\tif(!this.settings.checkbox.tie_selection) {\n\t\t\t\t\tthis.uncheck_all();\n\t\t\t\t\tvar _this = this;\n\t\t\t\t\t$.each(state.checkbox, function (i, v) {\n\t\t\t\t\t\t_this.check_node(v);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tdelete state.checkbox;\n\t\t\t\tthis.set_state(state, callback);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn res;\n\t\t};\n\t\tthis.refresh = function (skip_loading, forget_state) {\n\t\t\tif(this.settings.checkbox.tie_selection) {\n\t\t\t\tthis._data.checkbox.selected = [];\n\t\t\t}\n\t\t\treturn parent.refresh.apply(this, arguments);\n\t\t};\n\t};\n\n\t// include the checkbox plugin by default\n\t// $.jstree.defaults.plugins.push(\"checkbox\");\n\n\n/**\n * ### Conditionalselect plugin\n *\n * This plugin allows defining a callback to allow or deny node selection by user input (activate node method).\n */\n\n\t/**\n\t * a callback (function) which is invoked in the instance's scope and receives two arguments - the node and the event that triggered the `activate_node` call. Returning false prevents working with the node, returning true allows invoking activate_node. Defaults to returning `true`.\n\t * @name $.jstree.defaults.checkbox.visible\n\t * @plugin checkbox\n\t */\n\t$.jstree.defaults.conditionalselect = function () { return true; };\n\t$.jstree.plugins.conditionalselect = function (options, parent) {\n\t\t// own function\n\t\tthis.activate_node = function (obj, e) {\n\t\t\tif(this.settings.conditionalselect.call(this, this.get_node(obj), e)) {\n\t\t\t\treturn parent.activate_node.call(this, obj, e);\n\t\t\t}\n\t\t};\n\t};\n\n\n/**\n * ### Contextmenu plugin\n *\n * Shows a context menu when a node is right-clicked.\n */\n\n\t/**\n\t * stores all defaults for the contextmenu plugin\n\t * @name $.jstree.defaults.contextmenu\n\t * @plugin contextmenu\n\t */\n\t$.jstree.defaults.contextmenu = {\n\t\t/**\n\t\t * a boolean indicating if the node should be selected when the context menu is invoked on it. Defaults to `true`.\n\t\t * @name $.jstree.defaults.contextmenu.select_node\n\t\t * @plugin contextmenu\n\t\t */\n\t\tselect_node : true,\n\t\t/**\n\t\t * a boolean indicating if the menu should be shown aligned with the node. Defaults to `true`, otherwise the mouse coordinates are used.\n\t\t * @name $.jstree.defaults.contextmenu.show_at_node\n\t\t * @plugin contextmenu\n\t\t */\n\t\tshow_at_node : true,\n\t\t/**\n\t\t * an object of actions, or a function that accepts a node and a callback function and calls the callback function with an object of actions available for that node (you can also return the items too).\n\t\t *\n\t\t * Each action consists of a key (a unique name) and a value which is an object with the following properties (only label and action are required). Once a menu item is activated the `action` function will be invoked with an object containing the following keys: item - the contextmenu item definition as seen below, reference - the DOM node that was used (the tree node), element - the contextmenu DOM element, position - an object with x/y properties indicating the position of the menu.\n\t\t *\n\t\t * * `separator_before` - a boolean indicating if there should be a separator before this item\n\t\t * * `separator_after` - a boolean indicating if there should be a separator after this item\n\t\t * * `_disabled` - a boolean indicating if this action should be disabled\n\t\t * * `label` - a string - the name of the action (could be a function returning a string)\n\t\t * * `title` - a string - an optional tooltip for the item\n\t\t * * `action` - a function to be executed if this item is chosen, the function will receive \n\t\t * * `icon` - a string, can be a path to an icon or a className, if using an image that is in the current directory use a `./` prefix, otherwise it will be detected as a class\n\t\t * * `shortcut` - keyCode which will trigger the action if the menu is open (for example `113` for rename, which equals F2)\n\t\t * * `shortcut_label` - shortcut label (like for example `F2` for rename)\n\t\t * * `submenu` - an object with the same structure as $.jstree.defaults.contextmenu.items which can be used to create a submenu - each key will be rendered as a separate option in a submenu that will appear once the current item is hovered\n\t\t *\n\t\t * @name $.jstree.defaults.contextmenu.items\n\t\t * @plugin contextmenu\n\t\t */\n\t\titems : function (o, cb) { // Could be an object directly\n\t\t\treturn {\n\t\t\t\t\"create\" : {\n\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\"separator_after\"\t: true,\n\t\t\t\t\t\"_disabled\"\t\t\t: false, //(this.check(\"create_node\", data.reference, {}, \"last\")),\n\t\t\t\t\t\"label\"\t\t\t\t: \"Create\",\n\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\tinst.create_node(obj, {}, \"last\", function (new_node) {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tinst.edit(new_node);\n\t\t\t\t\t\t\t} catch (ex) {\n\t\t\t\t\t\t\t\tsetTimeout(function () { inst.edit(new_node); },0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"rename\" : {\n\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\"_disabled\"\t\t\t: false, //(this.check(\"rename_node\", data.reference, this.get_parent(data.reference), \"\")),\n\t\t\t\t\t\"label\"\t\t\t\t: \"Rename\",\n\t\t\t\t\t/*!\n\t\t\t\t\t\"shortcut\"\t\t\t: 113,\n\t\t\t\t\t\"shortcut_label\"\t: 'F2',\n\t\t\t\t\t\"icon\"\t\t\t\t: \"glyphicon glyphicon-leaf\",\n\t\t\t\t\t*/\n\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\tinst.edit(obj);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"remove\" : {\n\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\"_disabled\"\t\t\t: false, //(this.check(\"delete_node\", data.reference, this.get_parent(data.reference), \"\")),\n\t\t\t\t\t\"label\"\t\t\t\t: \"Delete\",\n\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\tif(inst.is_selected(obj)) {\n\t\t\t\t\t\t\tinst.delete_node(inst.get_selected());\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tinst.delete_node(obj);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"ccp\" : {\n\t\t\t\t\t\"separator_before\"\t: true,\n\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\"label\"\t\t\t\t: \"Edit\",\n\t\t\t\t\t\"action\"\t\t\t: false,\n\t\t\t\t\t\"submenu\" : {\n\t\t\t\t\t\t\"cut\" : {\n\t\t\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\t\t\"label\"\t\t\t\t: \"Cut\",\n\t\t\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\t\t\tif(inst.is_selected(obj)) {\n\t\t\t\t\t\t\t\t\tinst.cut(inst.get_top_selected());\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tinst.cut(obj);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"copy\" : {\n\t\t\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\t\t\"label\"\t\t\t\t: \"Copy\",\n\t\t\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\t\t\tif(inst.is_selected(obj)) {\n\t\t\t\t\t\t\t\t\tinst.copy(inst.get_top_selected());\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tinst.copy(obj);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"paste\" : {\n\t\t\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\t\t\"_disabled\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\treturn !$.jstree.reference(data.reference).can_paste();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\t\t\"label\"\t\t\t\t: \"Paste\",\n\t\t\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\t\t\tinst.paste(obj);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t};\n\n\t$.jstree.plugins.contextmenu = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tvar last_ts = 0, cto = null, ex, ey;\n\t\t\tthis.element\n\t\t\t\t.on(\"init.jstree loading.jstree ready.jstree\", function () {\n\t\t\t\t\t\tthis.get_container_ul().addClass('jstree-contextmenu');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"contextmenu.jstree\", \".jstree-anchor\", function (e, data) {\n\t\t\t\t\t\tif (e.target.tagName.toLowerCase() === 'input') {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tlast_ts = e.ctrlKey ? +new Date() : 0;\n\t\t\t\t\t\tif(data || cto) {\n\t\t\t\t\t\t\tlast_ts = (+new Date()) + 10000;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(cto) {\n\t\t\t\t\t\t\tclearTimeout(cto);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(!this.is_loading(e.currentTarget)) {\n\t\t\t\t\t\t\tthis.show_contextmenu(e.currentTarget, e.pageX, e.pageY, e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"click.jstree\", \".jstree-anchor\", function (e) {\n\t\t\t\t\t\tif(this._data.contextmenu.visible && (!last_ts || (+new Date()) - last_ts > 250)) { // work around safari & macOS ctrl+click\n\t\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlast_ts = 0;\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"touchstart.jstree\", \".jstree-anchor\", function (e) {\n\t\t\t\t\t\tif(!e.originalEvent || !e.originalEvent.changedTouches || !e.originalEvent.changedTouches[0]) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tex = e.originalEvent.changedTouches[0].clientX;\n\t\t\t\t\t\tey = e.originalEvent.changedTouches[0].clientY;\n\t\t\t\t\t\tcto = setTimeout(function () {\n\t\t\t\t\t\t\t$(e.currentTarget).trigger('contextmenu', true);\n\t\t\t\t\t\t}, 750);\n\t\t\t\t\t})\n\t\t\t\t.on('touchmove.vakata.jstree', function (e) {\n\t\t\t\t\t\tif(cto && e.originalEvent && e.originalEvent.changedTouches && e.originalEvent.changedTouches[0] && (Math.abs(ex - e.originalEvent.changedTouches[0].clientX) > 10 || Math.abs(ey - e.originalEvent.changedTouches[0].clientY) > 10)) {\n\t\t\t\t\t\t\tclearTimeout(cto);\n\t\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t.on('touchend.vakata.jstree', function (e) {\n\t\t\t\t\t\tif(cto) {\n\t\t\t\t\t\t\tclearTimeout(cto);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t/*!\n\t\t\tif(!('oncontextmenu' in document.body) && ('ontouchstart' in document.body)) {\n\t\t\t\tvar el = null, tm = null;\n\t\t\t\tthis.element\n\t\t\t\t\t.on(\"touchstart\", \".jstree-anchor\", function (e) {\n\t\t\t\t\t\tel = e.currentTarget;\n\t\t\t\t\t\ttm = +new Date();\n\t\t\t\t\t\t$(document).one(\"touchend\", function (e) {\n\t\t\t\t\t\t\te.target = document.elementFromPoint(e.originalEvent.targetTouches[0].pageX - window.pageXOffset, e.originalEvent.targetTouches[0].pageY - window.pageYOffset);\n\t\t\t\t\t\t\te.currentTarget = e.target;\n\t\t\t\t\t\t\ttm = ((+(new Date())) - tm);\n\t\t\t\t\t\t\tif(e.target === el && tm > 600 && tm < 1000) {\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t$(el).trigger('contextmenu', e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tel = null;\n\t\t\t\t\t\t\ttm = null;\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t}\n\t\t\t*/\n\t\t\t$(document).on(\"context_hide.vakata.jstree\", function (e, data) {\n\t\t\t\tthis._data.contextmenu.visible = false;\n\t\t\t\t$(data.reference).removeClass('jstree-context');\n\t\t\t}.bind(this));\n\t\t};\n\t\tthis.teardown = function () {\n\t\t\tif(this._data.contextmenu.visible) {\n\t\t\t\t$.vakata.context.hide();\n\t\t\t}\n\t\t\t$(document).off(\"context_hide.vakata.jstree\");\n\t\t\tparent.teardown.call(this);\n\t\t};\n\n\t\t/**\n\t\t * prepare and show the context menu for a node\n\t\t * @name show_contextmenu(obj [, x, y])\n\t\t * @param {mixed} obj the node\n\t\t * @param {Number} x the x-coordinate relative to the document to show the menu at\n\t\t * @param {Number} y the y-coordinate relative to the document to show the menu at\n\t\t * @param {Object} e the event if available that triggered the contextmenu\n\t\t * @plugin contextmenu\n\t\t * @trigger show_contextmenu.jstree\n\t\t */\n\t\tthis.show_contextmenu = function (obj, x, y, e) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === $.jstree.root) { return false; }\n\t\t\tvar s = this.settings.contextmenu,\n\t\t\t\td = this.get_node(obj, true),\n\t\t\t\ta = d.children(\".jstree-anchor\"),\n\t\t\t\to = false,\n\t\t\t\ti = false;\n\t\t\tif(s.show_at_node || x === undefined || y === undefined) {\n\t\t\t\to = a.offset();\n\t\t\t\tx = o.left;\n\t\t\t\ty = o.top + this._data.core.li_height;\n\t\t\t}\n\t\t\tif(this.settings.contextmenu.select_node && !this.is_selected(obj)) {\n\t\t\t\tthis.activate_node(obj, e);\n\t\t\t}\n\n\t\t\ti = s.items;\n\t\t\tif($.vakata.is_function(i)) {\n\t\t\t\ti = i.call(this, obj, function (i) {\n\t\t\t\t\tthis._show_contextmenu(obj, x, y, i);\n\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t\tif($.isPlainObject(i)) {\n\t\t\t\tthis._show_contextmenu(obj, x, y, i);\n\t\t\t}\n\t\t};\n\t\t/**\n\t\t * show the prepared context menu for a node\n\t\t * @name _show_contextmenu(obj, x, y, i)\n\t\t * @param {mixed} obj the node\n\t\t * @param {Number} x the x-coordinate relative to the document to show the menu at\n\t\t * @param {Number} y the y-coordinate relative to the document to show the menu at\n\t\t * @param {Number} i the object of items to show\n\t\t * @plugin contextmenu\n\t\t * @trigger show_contextmenu.jstree\n\t\t * @private\n\t\t */\n\t\tthis._show_contextmenu = function (obj, x, y, i) {\n\t\t\tvar d = this.get_node(obj, true),\n\t\t\t\ta = d.children(\".jstree-anchor\");\n\t\t\t$(document).one(\"context_show.vakata.jstree\", function (e, data) {\n\t\t\t\tvar cls = 'jstree-contextmenu jstree-' + this.get_theme() + '-contextmenu';\n\t\t\t\t$(data.element).addClass(cls);\n\t\t\t\ta.addClass('jstree-context');\n\t\t\t}.bind(this));\n\t\t\tthis._data.contextmenu.visible = true;\n\t\t\t$.vakata.context.show(a, { 'x' : x, 'y' : y }, i);\n\t\t\t/**\n\t\t\t * triggered when the contextmenu is shown for a node\n\t\t\t * @event\n\t\t\t * @name show_contextmenu.jstree\n\t\t\t * @param {Object} node the node\n\t\t\t * @param {Number} x the x-coordinate of the menu relative to the document\n\t\t\t * @param {Number} y the y-coordinate of the menu relative to the document\n\t\t\t * @plugin contextmenu\n\t\t\t */\n\t\t\tthis.trigger('show_contextmenu', { \"node\" : obj, \"x\" : x, \"y\" : y });\n\t\t};\n\t};\n\n\t// contextmenu helper\n\t(function ($) {\n\t\tvar right_to_left = false,\n\t\t\tvakata_context = {\n\t\t\t\telement\t\t: false,\n\t\t\t\treference\t: false,\n\t\t\t\tposition_x\t: 0,\n\t\t\t\tposition_y\t: 0,\n\t\t\t\titems\t\t: [],\n\t\t\t\thtml\t\t: \"\",\n\t\t\t\tis_visible\t: false\n\t\t\t};\n\n\t\t$.vakata.context = {\n\t\t\tsettings : {\n\t\t\t\thide_onmouseleave\t: 0,\n\t\t\t\ticons\t\t\t\t: true\n\t\t\t},\n\t\t\t_trigger : function (event_name) {\n\t\t\t\t$(document).triggerHandler(\"context_\" + event_name + \".vakata\", {\n\t\t\t\t\t\"reference\"\t: vakata_context.reference,\n\t\t\t\t\t\"element\"\t: vakata_context.element,\n\t\t\t\t\t\"position\"\t: {\n\t\t\t\t\t\t\"x\" : vakata_context.position_x,\n\t\t\t\t\t\t\"y\" : vakata_context.position_y\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t_execute : function (i) {\n\t\t\t\ti = vakata_context.items[i];\n\t\t\t\treturn i && (!i._disabled || ($.vakata.is_function(i._disabled) && !i._disabled({ \"item\" : i, \"reference\" : vakata_context.reference, \"element\" : vakata_context.element }))) && i.action ? i.action.call(null, {\n\t\t\t\t\t\t\t\"item\"\t\t: i,\n\t\t\t\t\t\t\t\"reference\"\t: vakata_context.reference,\n\t\t\t\t\t\t\t\"element\"\t: vakata_context.element,\n\t\t\t\t\t\t\t\"position\"\t: {\n\t\t\t\t\t\t\t\t\"x\" : vakata_context.position_x,\n\t\t\t\t\t\t\t\t\"y\" : vakata_context.position_y\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}) : false;\n\t\t\t},\n\t\t\t_parse : function (o, is_callback) {\n\t\t\t\tif(!o) { return false; }\n\t\t\t\tif(!is_callback) {\n\t\t\t\t\tvakata_context.html\t\t= \"\";\n\t\t\t\t\tvakata_context.items\t= [];\n\t\t\t\t}\n\t\t\t\tvar str = \"\",\n\t\t\t\t\tsep = false,\n\t\t\t\t\ttmp;\n\n\t\t\t\tif(is_callback) { str += \"<\"+\"ul>\"; }\n\t\t\t\t$.each(o, function (i, val) {\n\t\t\t\t\tif(!val) { return true; }\n\t\t\t\t\tvakata_context.items.push(val);\n\t\t\t\t\tif(!sep && val.separator_before) {\n\t\t\t\t\t\tstr += \"<\"+\"li class='vakata-context-separator'><\"+\"a href='#' \" + ($.vakata.context.settings.icons ? '' : 'class=\"vakata-context-no-icons\"') + \">&#160;<\"+\"/a><\"+\"/li>\";\n\t\t\t\t\t}\n\t\t\t\t\tsep = false;\n\t\t\t\t\tstr += \"<\"+\"li class='\" + (val._class || \"\") + (val._disabled === true || ($.vakata.is_function(val._disabled) && val._disabled({ \"item\" : val, \"reference\" : vakata_context.reference, \"element\" : vakata_context.element })) ? \" vakata-contextmenu-disabled \" : \"\") + \"' \"+(val.shortcut?\" data-shortcut='\"+val.shortcut+\"' \":'')+\">\";\n\t\t\t\t\tstr += \"<\"+\"a href='#' rel='\" + (vakata_context.items.length - 1) + \"' \" + (val.title ? \"title='\" + val.title + \"'\" : \"\") + \">\";\n\t\t\t\t\tif($.vakata.context.settings.icons) {\n\t\t\t\t\t\tstr += \"<\"+\"i \";\n\t\t\t\t\t\tif(val.icon) {\n\t\t\t\t\t\t\tif(val.icon.indexOf(\"/\") !== -1 || val.icon.indexOf(\".\") !== -1) { str += \" style='background:url(\\\"\" + val.icon + \"\\\") center center no-repeat' \"; }\n\t\t\t\t\t\t\telse { str += \" class='\" + val.icon + \"' \"; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstr += \"><\"+\"/i><\"+\"span class='vakata-contextmenu-sep'>&#160;<\"+\"/span>\";\n\t\t\t\t\t}\n\t\t\t\t\tstr += ($.vakata.is_function(val.label) ? val.label({ \"item\" : i, \"reference\" : vakata_context.reference, \"element\" : vakata_context.element }) : val.label) + (val.shortcut?' <span class=\"vakata-contextmenu-shortcut vakata-contextmenu-shortcut-'+val.shortcut+'\">'+ (val.shortcut_label || '') +'</span>':'') + \"<\"+\"/a>\";\n\t\t\t\t\tif(val.submenu) {\n\t\t\t\t\t\ttmp = $.vakata.context._parse(val.submenu, true);\n\t\t\t\t\t\tif(tmp) { str += tmp; }\n\t\t\t\t\t}\n\t\t\t\t\tstr += \"<\"+\"/li>\";\n\t\t\t\t\tif(val.separator_after) {\n\t\t\t\t\t\tstr += \"<\"+\"li class='vakata-context-separator'><\"+\"a href='#' \" + ($.vakata.context.settings.icons ? '' : 'class=\"vakata-context-no-icons\"') + \">&#160;<\"+\"/a><\"+\"/li>\";\n\t\t\t\t\t\tsep = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tstr  = str.replace(/<li class\\='vakata-context-separator'\\><\\/li\\>$/,\"\");\n\t\t\t\tif(is_callback) { str += \"</ul>\"; }\n\t\t\t\t/**\n\t\t\t\t * triggered on the document when the contextmenu is parsed (HTML is built)\n\t\t\t\t * @event\n\t\t\t\t * @plugin contextmenu\n\t\t\t\t * @name context_parse.vakata\n\t\t\t\t * @param {jQuery} reference the element that was right clicked\n\t\t\t\t * @param {jQuery} element the DOM element of the menu itself\n\t\t\t\t * @param {Object} position the x & y coordinates of the menu\n\t\t\t\t */\n\t\t\t\tif(!is_callback) { vakata_context.html = str; $.vakata.context._trigger(\"parse\"); }\n\t\t\t\treturn str.length > 10 ? str : false;\n\t\t\t},\n\t\t\t_show_submenu : function (o) {\n\t\t\t\to = $(o);\n\t\t\t\tif(!o.length || !o.children(\"ul\").length) { return; }\n\t\t\t\tvar e = o.children(\"ul\"),\n\t\t\t\t\txl = o.offset().left,\n\t\t\t\t\tx = xl + o.outerWidth(),\n\t\t\t\t\ty = o.offset().top,\n\t\t\t\t\tw = e.width(),\n\t\t\t\t\th = e.height(),\n\t\t\t\t\tdw = $(window).width() + $(window).scrollLeft(),\n\t\t\t\t\tdh = $(window).height() + $(window).scrollTop();\n\t\t\t\t// може да се спести е една проверка - дали няма някой от класовете вече нагоре\n\t\t\t\tif(right_to_left) {\n\t\t\t\t\to[x - (w + 10 + o.outerWidth()) < 0 ? \"addClass\" : \"removeClass\"](\"vakata-context-left\");\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\to[x + w > dw  && xl > dw - x ? \"addClass\" : \"removeClass\"](\"vakata-context-right\");\n\t\t\t\t}\n\t\t\t\tif(y + h + 10 > dh) {\n\t\t\t\t\te.css(\"bottom\",\"-1px\");\n\t\t\t\t}\n\n\t\t\t\t//if does not fit - stick it to the side\n\t\t\t\tif (o.hasClass('vakata-context-right')) {\n\t\t\t\t\tif (xl < w) {\n\t\t\t\t\t\te.css(\"margin-right\", xl - w);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (dw - x < w) {\n\t\t\t\t\t\te.css(\"margin-left\", dw - x - w);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\te.show();\n\t\t\t},\n\t\t\tshow : function (reference, position, data) {\n\t\t\t\tvar o, e, x, y, w, h, dw, dh, cond = true;\n\t\t\t\tif(vakata_context.element && vakata_context.element.length) {\n\t\t\t\t\tvakata_context.element.width('');\n\t\t\t\t}\n\t\t\t\tswitch(cond) {\n\t\t\t\t\tcase (!position && !reference):\n\t\t\t\t\t\treturn false;\n\t\t\t\t\tcase (!!position && !!reference):\n\t\t\t\t\t\tvakata_context.reference\t= reference;\n\t\t\t\t\t\tvakata_context.position_x\t= position.x;\n\t\t\t\t\t\tvakata_context.position_y\t= position.y;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase (!position && !!reference):\n\t\t\t\t\t\tvakata_context.reference\t= reference;\n\t\t\t\t\t\to = reference.offset();\n\t\t\t\t\t\tvakata_context.position_x\t= o.left + reference.outerHeight();\n\t\t\t\t\t\tvakata_context.position_y\t= o.top;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase (!!position && !reference):\n\t\t\t\t\t\tvakata_context.position_x\t= position.x;\n\t\t\t\t\t\tvakata_context.position_y\t= position.y;\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tif(!!reference && !data && $(reference).data('vakata_contextmenu')) {\n\t\t\t\t\tdata = $(reference).data('vakata_contextmenu');\n\t\t\t\t}\n\t\t\t\tif($.vakata.context._parse(data)) {\n\t\t\t\t\tvakata_context.element.html(vakata_context.html);\n\t\t\t\t}\n\t\t\t\tif(vakata_context.items.length) {\n\t\t\t\t\tvakata_context.element.appendTo(document.body);\n\t\t\t\t\te = vakata_context.element;\n\t\t\t\t\tx = vakata_context.position_x;\n\t\t\t\t\ty = vakata_context.position_y;\n\t\t\t\t\tw = e.width();\n\t\t\t\t\th = e.height();\n\t\t\t\t\tdw = $(window).width() + $(window).scrollLeft();\n\t\t\t\t\tdh = $(window).height() + $(window).scrollTop();\n\t\t\t\t\tif(right_to_left) {\n\t\t\t\t\t\tx -= (e.outerWidth() - $(reference).outerWidth());\n\t\t\t\t\t\tif(x < $(window).scrollLeft() + 20) {\n\t\t\t\t\t\t\tx = $(window).scrollLeft() + 20;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(x + w + 20 > dw) {\n\t\t\t\t\t\tx = dw - (w + 20);\n\t\t\t\t\t}\n\t\t\t\t\tif(y + h + 20 > dh) {\n\t\t\t\t\t\ty = dh - (h + 20);\n\t\t\t\t\t}\n\n\t\t\t\t\tvakata_context.element\n\t\t\t\t\t\t.css({ \"left\" : x, \"top\" : y })\n\t\t\t\t\t\t.show()\n\t\t\t\t\t\t.find('a').first().trigger('focus').parent().addClass(\"vakata-context-hover\");\n\t\t\t\t\tvakata_context.is_visible = true;\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered on the document when the contextmenu is shown\n\t\t\t\t\t * @event\n\t\t\t\t\t * @plugin contextmenu\n\t\t\t\t\t * @name context_show.vakata\n\t\t\t\t\t * @param {jQuery} reference the element that was right clicked\n\t\t\t\t\t * @param {jQuery} element the DOM element of the menu itself\n\t\t\t\t\t * @param {Object} position the x & y coordinates of the menu\n\t\t\t\t\t */\n\t\t\t\t\t$.vakata.context._trigger(\"show\");\n\t\t\t\t}\n\t\t\t},\n\t\t\thide : function () {\n\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\tvakata_context.element.hide().find(\"ul\").hide().end().find(':focus').trigger('blur').end().detach();\n\t\t\t\t\tvakata_context.is_visible = false;\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered on the document when the contextmenu is hidden\n\t\t\t\t\t * @event\n\t\t\t\t\t * @plugin contextmenu\n\t\t\t\t\t * @name context_hide.vakata\n\t\t\t\t\t * @param {jQuery} reference the element that was right clicked\n\t\t\t\t\t * @param {jQuery} element the DOM element of the menu itself\n\t\t\t\t\t * @param {Object} position the x & y coordinates of the menu\n\t\t\t\t\t */\n\t\t\t\t\t$.vakata.context._trigger(\"hide\");\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\t$(function () {\n\t\t\tright_to_left = $(document.body).css(\"direction\") === \"rtl\";\n\t\t\tvar to = false;\n\n\t\t\tvakata_context.element = $(\"<ul class='vakata-context'></ul>\");\n\t\t\tvakata_context.element\n\t\t\t\t.on(\"mouseenter\", \"li\", function (e) {\n\t\t\t\t\te.stopImmediatePropagation();\n\n\t\t\t\t\tif($.contains(this, e.relatedTarget)) {\n\t\t\t\t\t\t// премахнато заради delegate mouseleave по-долу\n\t\t\t\t\t\t// $(this).find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\");\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif(to) { clearTimeout(to); }\n\t\t\t\t\tvakata_context.element.find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").end();\n\n\t\t\t\t\t$(this)\n\t\t\t\t\t\t.siblings().find(\"ul\").hide().end().end()\n\t\t\t\t\t\t.parentsUntil(\".vakata-context\", \"li\").addBack().addClass(\"vakata-context-hover\");\n\t\t\t\t\t$.vakata.context._show_submenu(this);\n\t\t\t\t})\n\t\t\t\t// тестово - дали не натоварва?\n\t\t\t\t.on(\"mouseleave\", \"li\", function (e) {\n\t\t\t\t\tif($.contains(this, e.relatedTarget)) { return; }\n\t\t\t\t\t$(this).find(\".vakata-context-hover\").addBack().removeClass(\"vakata-context-hover\");\n\t\t\t\t})\n\t\t\t\t.on(\"mouseleave\", function (e) {\n\t\t\t\t\t$(this).find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\");\n\t\t\t\t\tif($.vakata.context.settings.hide_onmouseleave) {\n\t\t\t\t\t\tto = setTimeout(\n\t\t\t\t\t\t\t(function (t) {\n\t\t\t\t\t\t\t\treturn function () { $.vakata.context.hide(); };\n\t\t\t\t\t\t\t}(this)), $.vakata.context.settings.hide_onmouseleave);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.on(\"click\", \"a\", function (e) {\n\t\t\t\t\te.preventDefault();\n\t\t\t\t//})\n\t\t\t\t//.on(\"mouseup\", \"a\", function (e) {\n\t\t\t\t\tif(!$(this).trigger('blur').parent().hasClass(\"vakata-context-disabled\") && $.vakata.context._execute($(this).attr(\"rel\")) !== false) {\n\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.on('keydown', 'a', function (e) {\n\t\t\t\t\t\tvar o = null;\n\t\t\t\t\t\tswitch(e.which) {\n\t\t\t\t\t\t\tcase 13:\n\t\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\t\te.type = \"click\";\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t$(e.currentTarget).trigger(e);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 37:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\tvakata_context.element.find(\".vakata-context-hover\").last().closest(\"li\").first().find(\"ul\").hide().find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").end().end().children('a').trigger('focus');\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 38:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\to = vakata_context.element.find(\"ul:visible\").addBack().last().children(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").prevAll(\"li:not(.vakata-context-separator)\").first();\n\t\t\t\t\t\t\t\t\tif(!o.length) { o = vakata_context.element.find(\"ul:visible\").addBack().last().children(\"li:not(.vakata-context-separator)\").last(); }\n\t\t\t\t\t\t\t\t\to.addClass(\"vakata-context-hover\").children('a').trigger('focus');\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 39:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\tvakata_context.element.find(\".vakata-context-hover\").last().children(\"ul\").show().children(\"li:not(.vakata-context-separator)\").removeClass(\"vakata-context-hover\").first().addClass(\"vakata-context-hover\").children('a').trigger('focus');\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 40:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\to = vakata_context.element.find(\"ul:visible\").addBack().last().children(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").nextAll(\"li:not(.vakata-context-separator)\").first();\n\t\t\t\t\t\t\t\t\tif(!o.length) { o = vakata_context.element.find(\"ul:visible\").addBack().last().children(\"li:not(.vakata-context-separator)\").first(); }\n\t\t\t\t\t\t\t\t\to.addClass(\"vakata-context-hover\").children('a').trigger('focus');\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 27:\n\t\t\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t//console.log(e.which);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t.on('keydown', function (e) {\n\t\t\t\t\te.preventDefault();\n\t\t\t\t\tvar a = vakata_context.element.find('.vakata-contextmenu-shortcut-' + e.which).parent();\n\t\t\t\t\tif(a.parent().not('.vakata-context-disabled')) {\n\t\t\t\t\t\ta.trigger('click');\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t$(document)\n\t\t\t\t.on(\"mousedown.vakata.jstree\", function (e) {\n\t\t\t\t\tif(vakata_context.is_visible && vakata_context.element[0] !== e.target  && !$.contains(vakata_context.element[0], e.target)) {\n\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.on(\"context_show.vakata.jstree\", function (e, data) {\n\t\t\t\t\tvakata_context.element.find(\"li:has(ul)\").children(\"a\").addClass(\"vakata-context-parent\");\n\t\t\t\t\tif(right_to_left) {\n\t\t\t\t\t\tvakata_context.element.addClass(\"vakata-context-rtl\").css(\"direction\", \"rtl\");\n\t\t\t\t\t}\n\t\t\t\t\t// also apply a RTL class?\n\t\t\t\t\tvakata_context.element.find(\"ul\").hide().end();\n\t\t\t\t});\n\t\t});\n\t}($));\n\t// $.jstree.defaults.plugins.push(\"contextmenu\");\n\n\n/**\n * ### Drag'n'drop plugin\n *\n * Enables dragging and dropping of nodes in the tree, resulting in a move or copy operations.\n */\n\n\t/**\n\t * stores all defaults for the drag'n'drop plugin\n\t * @name $.jstree.defaults.dnd\n\t * @plugin dnd\n\t */\n\t$.jstree.defaults.dnd = {\n\t\t/**\n\t\t * a boolean indicating if a copy should be possible while dragging (by pressint the meta key or Ctrl). Defaults to `true`.\n\t\t * @name $.jstree.defaults.dnd.copy\n\t\t * @plugin dnd\n\t\t */\n\t\tcopy : true,\n\t\t/**\n\t\t * a number indicating how long a node should remain hovered while dragging to be opened. Defaults to `500`.\n\t\t * @name $.jstree.defaults.dnd.open_timeout\n\t\t * @plugin dnd\n\t\t */\n\t\topen_timeout : 500,\n\t\t/**\n\t\t * a function invoked each time a node is about to be dragged, invoked in the tree's scope and receives the nodes about to be dragged as an argument (array) and the event that started the drag - return `false` to prevent dragging\n\t\t * @name $.jstree.defaults.dnd.is_draggable\n\t\t * @plugin dnd\n\t\t */\n\t\tis_draggable : true,\n\t\t/**\n\t\t * a boolean indicating if checks should constantly be made while the user is dragging the node (as opposed to checking only on drop), default is `true`\n\t\t * @name $.jstree.defaults.dnd.check_while_dragging\n\t\t * @plugin dnd\n\t\t */\n\t\tcheck_while_dragging : true,\n\t\t/**\n\t\t * a boolean indicating if nodes from this tree should only be copied with dnd (as opposed to moved), default is `false`\n\t\t * @name $.jstree.defaults.dnd.always_copy\n\t\t * @plugin dnd\n\t\t */\n\t\talways_copy : false,\n\t\t/**\n\t\t * when dropping a node \"inside\", this setting indicates the position the node should go to - it can be an integer or a string: \"first\" (same as 0) or \"last\", default is `0`\n\t\t * @name $.jstree.defaults.dnd.inside_pos\n\t\t * @plugin dnd\n\t\t */\n\t\tinside_pos : 0,\n\t\t/**\n\t\t * when starting the drag on a node that is selected this setting controls if all selected nodes are dragged or only the single node, default is `true`, which means all selected nodes are dragged when the drag is started on a selected node\n\t\t * @name $.jstree.defaults.dnd.drag_selection\n\t\t * @plugin dnd\n\t\t */\n\t\tdrag_selection : true,\n\t\t/**\n\t\t * controls whether dnd works on touch devices. If left as boolean true dnd will work the same as in desktop browsers, which in some cases may impair scrolling. If set to boolean false dnd will not work on touch devices. There is a special third option - string \"selected\" which means only selected nodes can be dragged on touch devices.\n\t\t * @name $.jstree.defaults.dnd.touch\n\t\t * @plugin dnd\n\t\t */\n\t\ttouch : true,\n\t\t/**\n\t\t * controls whether items can be dropped anywhere on the node, not just on the anchor, by default only the node anchor is a valid drop target. Works best with the wholerow plugin. If enabled on mobile depending on the interface it might be hard for the user to cancel the drop, since the whole tree container will be a valid drop target.\n\t\t * @name $.jstree.defaults.dnd.large_drop_target\n\t\t * @plugin dnd\n\t\t */\n\t\tlarge_drop_target : false,\n\t\t/**\n\t\t * controls whether a drag can be initiated from any part of the node and not just the text/icon part, works best with the wholerow plugin. Keep in mind it can cause problems with tree scrolling on mobile depending on the interface - in that case set the touch option to \"selected\".\n\t\t * @name $.jstree.defaults.dnd.large_drag_target\n\t\t * @plugin dnd\n\t\t */\n\t\tlarge_drag_target : false,\n\t\t/**\n\t\t * controls whether use HTML5 dnd api instead of classical. That will allow better integration of dnd events with other HTML5 controls.\n\t\t * @reference http://caniuse.com/#feat=dragndrop\n\t\t * @name $.jstree.defaults.dnd.use_html5\n\t\t * @plugin dnd\n\t\t */\n\t\tuse_html5: false,\n\t\t/**\n\t\t * controls whether items can be dropped anywhere on the tree.\n\t\t * @name $.jstree.defaults.dnd.blank_space_drop\n\t\t * @plugin dnd\n\t\t */\n\t\tblank_space_drop: false\n\t};\n\tvar drg, elm;\n\t// TODO: now check works by checking for each node individually, how about max_children, unique, etc?\n\t$.jstree.plugins.dnd = function (options, parent) {\n\t\tthis.init = function (el, options) {\n\t\t\tparent.init.call(this, el, options);\n\t\t\tthis.settings.dnd.use_html5 = this.settings.dnd.use_html5 && ('draggable' in document.createElement('span'));\n\t\t};\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis.element\n\t\t\t\t.on(this.settings.dnd.use_html5 ? 'dragstart.jstree' : 'mousedown.jstree touchstart.jstree', this.settings.dnd.large_drag_target ? '.jstree-node' : '.jstree-anchor', function (e) {\n\t\t\t\t\t\tif(this.settings.dnd.large_drag_target && $(e.target).closest('.jstree-node')[0] !== e.currentTarget) {\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(e.type === \"touchstart\" && (!this.settings.dnd.touch || (this.settings.dnd.touch === 'selected' && !$(e.currentTarget).closest('.jstree-node').children('.jstree-anchor').hasClass('jstree-clicked')))) {\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar obj = this.get_node(e.target),\n\t\t\t\t\t\t\tmlt = this.is_selected(obj) && this.settings.dnd.drag_selection ? this.get_top_selected().length : 1,\n\t\t\t\t\t\t\ttxt = (mlt > 1 ? mlt + ' ' + this.get_string('nodes') : this.get_text(e.currentTarget));\n\t\t\t\t\t\tif(this.settings.core.force_text) {\n\t\t\t\t\t\t\ttxt = $.vakata.html.escape(txt);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(obj && (obj.id || obj.id === 0) && obj.id !== $.jstree.root && (e.which === 1 || e.type === \"touchstart\" || e.type === \"dragstart\") &&\n\t\t\t\t\t\t\t(this.settings.dnd.is_draggable === true || ($.vakata.is_function(this.settings.dnd.is_draggable) && this.settings.dnd.is_draggable.call(this, (mlt > 1 ? this.get_top_selected(true) : [obj]), e)))\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tdrg = { 'jstree' : true, 'origin' : this, 'obj' : this.get_node(obj,true), 'nodes' : mlt > 1 ? this.get_top_selected() : [obj.id] };\n\t\t\t\t\t\t\telm = e.currentTarget;\n\t\t\t\t\t\t\tif (this.settings.dnd.use_html5) {\n\t\t\t\t\t\t\t\t$.vakata.dnd._trigger('start', e, { 'helper': $(), 'element': elm, 'data': drg });\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.element.trigger('mousedown.jstree');\n\t\t\t\t\t\t\t\treturn $.vakata.dnd.start(e, drg, '<div id=\"jstree-dnd\" class=\"jstree-' + this.get_theme() + ' jstree-' + this.get_theme() + '-' + this.get_theme_variant() + ' ' + ( this.settings.core.themes.responsive ? ' jstree-dnd-responsive' : '' ) + '\"><i class=\"jstree-icon jstree-er\"></i>' + txt + '<ins class=\"jstree-copy\">+</ins></div>');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this));\n\t\t\tif (this.settings.dnd.use_html5) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on('dragover.jstree', function (e) {\n\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t$.vakata.dnd._trigger('move', e, { 'helper': $(), 'element': elm, 'data': drg });\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t})\n\t\t\t\t\t//.on('dragenter.jstree', this.settings.dnd.large_drop_target ? '.jstree-node' : '.jstree-anchor', $.proxy(function (e) {\n\t\t\t\t\t//\t\te.preventDefault();\n\t\t\t\t\t//\t\t$.vakata.dnd._trigger('move', e, { 'helper': $(), 'element': elm, 'data': drg });\n\t\t\t\t\t//\t\treturn false;\n\t\t\t\t\t//\t}, this))\n\t\t\t\t\t.on('drop.jstree', function (e) {\n\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t$.vakata.dnd._trigger('stop', e, { 'helper': $(), 'element': elm, 'data': drg });\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}.bind(this));\n\t\t\t}\n\t\t};\n\t\tthis.redraw_node = function(obj, deep, callback, force_render) {\n\t\t\tobj = parent.redraw_node.apply(this, arguments);\n\t\t\tif (obj && this.settings.dnd.use_html5) {\n\t\t\t\tif (this.settings.dnd.large_drag_target) {\n\t\t\t\t\tobj.setAttribute('draggable', true);\n\t\t\t\t} else {\n\t\t\t\t\tvar i, j, tmp = null;\n\t\t\t\t\tfor(i = 0, j = obj.childNodes.length; i < j; i++) {\n\t\t\t\t\t\tif(obj.childNodes[i] && obj.childNodes[i].className && obj.childNodes[i].className.indexOf(\"jstree-anchor\") !== -1) {\n\t\t\t\t\t\t\ttmp = obj.childNodes[i];\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(tmp) {\n\t\t\t\t\t\ttmp.setAttribute('draggable', true);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn obj;\n\t\t};\n\t};\n\n\t$(function() {\n\t\t// bind only once for all instances\n\t\tvar lastmv = false,\n\t\t\tlaster = false,\n\t\t\tlastev = false,\n\t\t\topento = false,\n\t\t\tmarker = $('<div id=\"jstree-marker\">&#160;</div>').hide(); //.appendTo('body');\n\n\t\t$(document)\n\t\t\t.on('dragover.vakata.jstree', function (e) {\n\t\t\t\tif (elm) {\n\t\t\t\t\t$.vakata.dnd._trigger('move', e, { 'helper': $(), 'element': elm, 'data': drg });\n\t\t\t\t}\n\t\t\t})\n\t\t\t.on('drop.vakata.jstree', function (e) {\n\t\t\t\tif (elm) {\n\t\t\t\t\t$.vakata.dnd._trigger('stop', e, { 'helper': $(), 'element': elm, 'data': drg });\n\t\t\t\t\telm = null;\n\t\t\t\t\tdrg = null;\n\t\t\t\t}\n\t\t\t})\n\t\t\t.on('dnd_start.vakata.jstree', function (e, data) {\n\t\t\t\tlastmv = false;\n\t\t\t\tlastev = false;\n\t\t\t\tif(!data || !data.data || !data.data.jstree) { return; }\n\t\t\t\tmarker.appendTo(document.body); //.show();\n\t\t\t})\n\t\t\t.on('dnd_move.vakata.jstree', function (e, data) {\n\t\t\t\tvar isDifferentNode = data.event.target !== lastev.target;\n\t\t\t\tif(opento) {\n\t\t\t\t\tif (!data.event || data.event.type !== 'dragover' || isDifferentNode) {\n\t\t\t\t\t\tclearTimeout(opento);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(!data || !data.data || !data.data.jstree) { return; }\n\n\t\t\t\t// if we are hovering the marker image do nothing (can happen on \"inside\" drags)\n\t\t\t\tif(data.event.target.id && data.event.target.id === 'jstree-marker') {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlastev = data.event;\n\n\t\t\t\tvar ins = $.jstree.reference(data.event.target),\n\t\t\t\t\tref = false,\n\t\t\t\t\toff = false,\n\t\t\t\t\trel = false,\n\t\t\t\t\ttmp, l, t, h, p, i, o, ok, t1, t2, op, ps, pr, ip, tm, is_copy, pn, c;\n\t\t\t\t// if we are over an instance\n\t\t\t\tif(ins && ins._data && ins._data.dnd) {\n\t\t\t\t\tmarker.attr('class', 'jstree-' + ins.get_theme() + ( ins.settings.core.themes.responsive ? ' jstree-dnd-responsive' : '' ));\n\t\t\t\t\tis_copy = data.data.origin && (data.data.origin.settings.dnd.always_copy || (data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey)));\n\t\t\t\t\tdata.helper\n\t\t\t\t\t\t.children().attr('class', 'jstree-' + ins.get_theme() + ' jstree-' + ins.get_theme() + '-' + ins.get_theme_variant() + ' ' + ( ins.settings.core.themes.responsive ? ' jstree-dnd-responsive' : '' ))\n\t\t\t\t\t\t.find('.jstree-copy').first()[ is_copy ? 'show' : 'hide' ]();\n\n\t\t\t\t\t// if are hovering the container itself add a new root node\n\t\t\t\t\t//console.log(data.event);\n\t\t\t\t\tif( (data.event.target === ins.element[0] || data.event.target === ins.get_container_ul()[0]) && (ins.get_container_ul().children().length === 0 || ins.settings.dnd.blank_space_drop)) {\n\t\t\t\t\t\tok = true;\n\t\t\t\t\t\tfor(t1 = 0, t2 = data.data.nodes.length; t1 < t2; t1++) {\n\t\t\t\t\t\t\tok = ok && ins.check( (data.data.origin && (data.data.origin.settings.dnd.always_copy || (data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey)) ) ? \"copy_node\" : \"move_node\"), (data.data.origin && data.data.origin !== ins ? data.data.origin.get_node(data.data.nodes[t1]) : data.data.nodes[t1]), $.jstree.root, 'last', { 'dnd' : true, 'ref' : ins.get_node($.jstree.root), 'pos' : 'i', 'origin' : data.data.origin, 'is_multi' : (data.data.origin && data.data.origin !== ins), 'is_foreign' : (!data.data.origin) });\n\t\t\t\t\t\t\tif(!ok) { break; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(ok) {\n\t\t\t\t\t\t\tlastmv = { 'ins' : ins, 'par' : $.jstree.root, 'pos' : 'last' };\n\t\t\t\t\t\t\tmarker.hide();\n\t\t\t\t\t\t\tdata.helper.find('.jstree-icon').first().removeClass('jstree-er').addClass('jstree-ok');\n\t\t\t\t\t\t\tif (data.event.originalEvent && data.event.originalEvent.dataTransfer) {\n\t\t\t\t\t\t\t\tdata.event.originalEvent.dataTransfer.dropEffect = is_copy ? 'copy' : 'move';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\t// if we are hovering a tree node\n\t\t\t\t\t\tref = ins.settings.dnd.large_drop_target ? $(data.event.target).closest('.jstree-node').children('.jstree-anchor') : $(data.event.target).closest('.jstree-anchor');\n\t\t\t\t\t\t\n\t\t\t\t\t\tif(ref && ref.length && ref.parent().is('.jstree-closed, .jstree-open, .jstree-leaf')) {\n\t\t\t\t\t\t\toff = ref.offset();\n\t\t\t\t\t\t\trel = (data.event.pageY !== undefined ? data.event.pageY : data.event.originalEvent.pageY) - off.top;\n\t\t\t\t\t\t\th = ref.outerHeight();\n\t\t\t\t\t\t\tif(rel < h / 3) {\n\t\t\t\t\t\t\t\to = ['b', 'i', 'a'];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse if(rel > h - h / 3) {\n\t\t\t\t\t\t\t\to = ['a', 'i', 'b'];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\to = rel > h / 2 ? ['i', 'a', 'b'] : ['i', 'b', 'a'];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t$.each(o, function (j, v) {\n\t\t\t\t\t\t\t\tswitch(v) {\n\t\t\t\t\t\t\t\t\tcase 'b':\n\t\t\t\t\t\t\t\t\t\tl = off.left - 6;\n\t\t\t\t\t\t\t\t\t\tt = off.top;\n\t\t\t\t\t\t\t\t\t\tp = ins.get_parent(ref);\n\t\t\t\t\t\t\t\t\t\ti = ref.parent().index();\n\t\t\t\t\t\t\t\t\t\tc = 'jstree-below';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 'i':\n\t\t\t\t\t\t\t\t\t\tip = ins.settings.dnd.inside_pos;\n\t\t\t\t\t\t\t\t\t\ttm = ins.get_node(ref.parent());\n\t\t\t\t\t\t\t\t\t\tl = off.left - 2;\n\t\t\t\t\t\t\t\t\t\tt = off.top + h / 2 + 1;\n\t\t\t\t\t\t\t\t\t\tp = tm.id;\n\t\t\t\t\t\t\t\t\t\ti = ip === 'first' ? 0 : (ip === 'last' ? tm.children.length : Math.min(ip, tm.children.length));\n\t\t\t\t\t\t\t\t\t\tc = 'jstree-inside';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 'a':\n\t\t\t\t\t\t\t\t\t\tl = off.left - 6;\n\t\t\t\t\t\t\t\t\t\tt = off.top + h;\n\t\t\t\t\t\t\t\t\t\tp = ins.get_parent(ref);\n\t\t\t\t\t\t\t\t\t\ti = ref.parent().index() + 1;\n\t\t\t\t\t\t\t\t\t\tc = 'jstree-above';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tok = true;\n\t\t\t\t\t\t\t\tfor(t1 = 0, t2 = data.data.nodes.length; t1 < t2; t1++) {\n\t\t\t\t\t\t\t\t\top = data.data.origin && (data.data.origin.settings.dnd.always_copy || (data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey))) ? \"copy_node\" : \"move_node\";\n\t\t\t\t\t\t\t\t\tps = i;\n\t\t\t\t\t\t\t\t\tif(op === \"move_node\" && v === 'a' && (data.data.origin && data.data.origin === ins) && p === ins.get_parent(data.data.nodes[t1])) {\n\t\t\t\t\t\t\t\t\t\tpr = ins.get_node(p);\n\t\t\t\t\t\t\t\t\t\tif(ps > $.inArray(data.data.nodes[t1], pr.children)) {\n\t\t\t\t\t\t\t\t\t\t\tps -= 1;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tok = ok && ( (ins && ins.settings && ins.settings.dnd && ins.settings.dnd.check_while_dragging === false) || ins.check(op, (data.data.origin && data.data.origin !== ins ? data.data.origin.get_node(data.data.nodes[t1]) : data.data.nodes[t1]), p, ps, { 'dnd' : true, 'ref' : ins.get_node(ref.parent()), 'pos' : v, 'origin' : data.data.origin, 'is_multi' : (data.data.origin && data.data.origin !== ins), 'is_foreign' : (!data.data.origin) }) );\n\t\t\t\t\t\t\t\t\tif(!ok) {\n\t\t\t\t\t\t\t\t\t\tif(ins && ins.last_error) { laster = ins.last_error(); }\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(v === 'i' && ref.parent().is('.jstree-closed') && ins.settings.dnd.open_timeout) {\n\t\t\t\t\t\t\t\t\tif (!data.event || data.event.type !== 'dragover' || isDifferentNode) {\n\t\t\t\t\t\t\t\t\t\tif (opento) { clearTimeout(opento); }\n\t\t\t\t\t\t\t\t\t\topento = setTimeout((function (x, z) { return function () { x.open_node(z); }; }(ins, ref)), ins.settings.dnd.open_timeout);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(ok) {\n\t\t\t\t\t\t\t\t\tpn = ins.get_node(p, true);\n\t\t\t\t\t\t\t\t\tif (!pn.hasClass('.jstree-dnd-parent')) {\n\t\t\t\t\t\t\t\t\t\t$('.jstree-dnd-parent').removeClass('jstree-dnd-parent');\n\t\t\t\t\t\t\t\t\t\tpn.addClass('jstree-dnd-parent');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlastmv = { 'ins' : ins, 'par' : p, 'pos' : v === 'i' && ip === 'last' && i === 0 && !ins.is_loaded(tm) ? 'last' : i };\n\t\t\t\t\t\t\t\t\tmarker.css({ 'left' : l + 'px', 'top' : t + 'px' }).show();\n\t\t\t\t\t\t\t\t\tmarker.removeClass('jstree-above jstree-inside jstree-below').addClass(c);\n\t\t\t\t\t\t\t\t\tdata.helper.find('.jstree-icon').first().removeClass('jstree-er').addClass('jstree-ok');\n\t\t\t\t\t\t\t\t\tif (data.event.originalEvent && data.event.originalEvent.dataTransfer) {\n\t\t\t\t\t\t\t\t\t\tdata.event.originalEvent.dataTransfer.dropEffect = is_copy ? 'copy' : 'move';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlaster = {};\n\t\t\t\t\t\t\t\t\to = true;\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tif(o === true) { return; }\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t$('.jstree-dnd-parent').removeClass('jstree-dnd-parent');\n\t\t\t\tlastmv = false;\n\t\t\t\tdata.helper.find('.jstree-icon').removeClass('jstree-ok').addClass('jstree-er');\n\t\t\t\tif (data.event.originalEvent && data.event.originalEvent.dataTransfer) {\n\t\t\t\t\t//data.event.originalEvent.dataTransfer.dropEffect = 'none';\n\t\t\t\t}\n\t\t\t\tmarker.hide();\n\t\t\t})\n\t\t\t.on('dnd_scroll.vakata.jstree', function (e, data) {\n\t\t\t\tif(!data || !data.data || !data.data.jstree) { return; }\n\t\t\t\tmarker.hide();\n\t\t\t\tlastmv = false;\n\t\t\t\tlastev = false;\n\t\t\t\tdata.helper.find('.jstree-icon').first().removeClass('jstree-ok').addClass('jstree-er');\n\t\t\t})\n\t\t\t.on('dnd_stop.vakata.jstree', function (e, data) {\n\t\t\t\t$('.jstree-dnd-parent').removeClass('jstree-dnd-parent');\n\t\t\t\tif(opento) { clearTimeout(opento); }\n\t\t\t\tif(!data || !data.data || !data.data.jstree) { return; }\n\t\t\t\tmarker.hide().detach();\n\t\t\t\tvar i, j, nodes = [];\n\t\t\t\tif(lastmv) {\n\t\t\t\t\tfor(i = 0, j = data.data.nodes.length; i < j; i++) {\n\t\t\t\t\t\tnodes[i] = data.data.origin ? data.data.origin.get_node(data.data.nodes[i]) : data.data.nodes[i];\n\t\t\t\t\t}\n\t\t\t\t\tlastmv.ins[ data.data.origin && (data.data.origin.settings.dnd.always_copy || (data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey))) ? 'copy_node' : 'move_node' ](nodes, lastmv.par, lastmv.pos, false, false, false, data.data.origin);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\ti = $(data.event.target).closest('.jstree');\n\t\t\t\t\tif(i.length && laster && laster.error && laster.error === 'check') {\n\t\t\t\t\t\ti = i.jstree(true);\n\t\t\t\t\t\tif(i) {\n\t\t\t\t\t\t\ti.settings.core.error.call(this, laster);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlastev = false;\n\t\t\t\tlastmv = false;\n\t\t\t})\n\t\t\t.on('keyup.jstree keydown.jstree', function (e, data) {\n\t\t\t\tdata = $.vakata.dnd._get();\n\t\t\t\tif(data && data.data && data.data.jstree) {\n\t\t\t\t\tif (e.type === \"keyup\" && e.which === 27) {\n\t\t\t\t\t\tif (opento) { clearTimeout(opento); }\n\t\t\t\t\t\tlastmv = false;\n\t\t\t\t\t\tlaster = false;\n\t\t\t\t\t\tlastev = false;\n\t\t\t\t\t\topento = false;\n\t\t\t\t\t\tmarker.hide().detach();\n\t\t\t\t\t\t$.vakata.dnd._clean();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdata.helper.find('.jstree-copy').first()[ data.data.origin && (data.data.origin.settings.dnd.always_copy || (data.data.origin.settings.dnd.copy && (e.metaKey || e.ctrlKey))) ? 'show' : 'hide' ]();\n\t\t\t\t\t\tif(lastev) {\n\t\t\t\t\t\t\tlastev.metaKey = e.metaKey;\n\t\t\t\t\t\t\tlastev.ctrlKey = e.ctrlKey;\n\t\t\t\t\t\t\t$.vakata.dnd._trigger('move', lastev);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t});\n\n\t// helpers\n\t(function ($) {\n\t\t$.vakata.html = {\n\t\t\tdiv : $('<div></div>'),\n\t\t\tescape : function (str) {\n\t\t\t\treturn $.vakata.html.div.text(str).html();\n\t\t\t},\n\t\t\tstrip : function (str) {\n\t\t\t\treturn $.vakata.html.div.empty().append($.parseHTML(str)).text();\n\t\t\t}\n\t\t};\n\t\t// private variable\n\t\tvar vakata_dnd = {\n\t\t\telement\t: false,\n\t\t\ttarget\t: false,\n\t\t\tis_down\t: false,\n\t\t\tis_drag\t: false,\n\t\t\thelper\t: false,\n\t\t\thelper_w: 0,\n\t\t\tdata\t: false,\n\t\t\tinit_x\t: 0,\n\t\t\tinit_y\t: 0,\n\t\t\tscroll_l: 0,\n\t\t\tscroll_t: 0,\n\t\t\tscroll_e: false,\n\t\t\tscroll_i: false,\n\t\t\tis_touch: false\n\t\t};\n\t\t$.vakata.dnd = {\n\t\t\tsettings : {\n\t\t\t\tscroll_speed\t\t: 10,\n\t\t\t\tscroll_proximity\t: 20,\n\t\t\t\thelper_left\t\t\t: 5,\n\t\t\t\thelper_top\t\t\t: 10,\n\t\t\t\tthreshold\t\t\t: 5,\n\t\t\t\tthreshold_touch\t\t: 10\n\t\t\t},\n\t\t\t_trigger : function (event_name, e, data) {\n\t\t\t\tif (data === undefined) {\n\t\t\t\t\tdata = $.vakata.dnd._get();\n\t\t\t\t}\n\t\t\t\tdata.event = e;\n\t\t\t\t$(document).triggerHandler(\"dnd_\" + event_name + \".vakata\", data);\n\t\t\t},\n\t\t\t_get : function () {\n\t\t\t\treturn {\n\t\t\t\t\t\"data\"\t\t: vakata_dnd.data,\n\t\t\t\t\t\"element\"\t: vakata_dnd.element,\n\t\t\t\t\t\"helper\"\t: vakata_dnd.helper\n\t\t\t\t};\n\t\t\t},\n\t\t\t_clean : function () {\n\t\t\t\tif(vakata_dnd.helper) { vakata_dnd.helper.remove(); }\n\t\t\t\tif(vakata_dnd.scroll_i) { clearInterval(vakata_dnd.scroll_i); vakata_dnd.scroll_i = false; }\n\t\t\t\tvakata_dnd = {\n\t\t\t\t\telement\t: false,\n\t\t\t\t\ttarget\t: false,\n\t\t\t\t\tis_down\t: false,\n\t\t\t\t\tis_drag\t: false,\n\t\t\t\t\thelper\t: false,\n\t\t\t\t\thelper_w: 0,\n\t\t\t\t\tdata\t: false,\n\t\t\t\t\tinit_x\t: 0,\n\t\t\t\t\tinit_y\t: 0,\n\t\t\t\t\tscroll_l: 0,\n\t\t\t\t\tscroll_t: 0,\n\t\t\t\t\tscroll_e: false,\n\t\t\t\t\tscroll_i: false,\n\t\t\t\t\tis_touch: false\n\t\t\t\t};\n\t\t\t\telm = null;\n\t\t\t\t$(document).off(\"mousemove.vakata.jstree touchmove.vakata.jstree\", $.vakata.dnd.drag);\n\t\t\t\t$(document).off(\"mouseup.vakata.jstree touchend.vakata.jstree\", $.vakata.dnd.stop);\n\t\t\t},\n\t\t\t_scroll : function (init_only) {\n\t\t\t\tif(!vakata_dnd.scroll_e || (!vakata_dnd.scroll_l && !vakata_dnd.scroll_t)) {\n\t\t\t\t\tif(vakata_dnd.scroll_i) { clearInterval(vakata_dnd.scroll_i); vakata_dnd.scroll_i = false; }\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif(!vakata_dnd.scroll_i) {\n\t\t\t\t\tvakata_dnd.scroll_i = setInterval($.vakata.dnd._scroll, 100);\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif(init_only === true) { return false; }\n\n\t\t\t\tvar i = vakata_dnd.scroll_e.scrollTop(),\n\t\t\t\t\tj = vakata_dnd.scroll_e.scrollLeft();\n\t\t\t\tvakata_dnd.scroll_e.scrollTop(i + vakata_dnd.scroll_t * $.vakata.dnd.settings.scroll_speed);\n\t\t\t\tvakata_dnd.scroll_e.scrollLeft(j + vakata_dnd.scroll_l * $.vakata.dnd.settings.scroll_speed);\n\t\t\t\tif(i !== vakata_dnd.scroll_e.scrollTop() || j !== vakata_dnd.scroll_e.scrollLeft()) {\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered on the document when a drag causes an element to scroll\n\t\t\t\t\t * @event\n\t\t\t\t\t * @plugin dnd\n\t\t\t\t\t * @name dnd_scroll.vakata\n\t\t\t\t\t * @param {Mixed} data any data supplied with the call to $.vakata.dnd.start\n\t\t\t\t\t * @param {DOM} element the DOM element being dragged\n\t\t\t\t\t * @param {jQuery} helper the helper shown next to the mouse\n\t\t\t\t\t * @param {jQuery} event the element that is scrolling\n\t\t\t\t\t */\n\t\t\t\t\t$.vakata.dnd._trigger(\"scroll\", vakata_dnd.scroll_e);\n\t\t\t\t}\n\t\t\t},\n\t\t\tstart : function (e, data, html) {\n\t\t\t\tif(e.type === \"touchstart\" && e.originalEvent && e.originalEvent.changedTouches && e.originalEvent.changedTouches[0]) {\n\t\t\t\t\te.pageX = e.originalEvent.changedTouches[0].pageX;\n\t\t\t\t\te.pageY = e.originalEvent.changedTouches[0].pageY;\n\t\t\t\t\te.target = document.elementFromPoint(e.originalEvent.changedTouches[0].pageX - window.pageXOffset, e.originalEvent.changedTouches[0].pageY - window.pageYOffset);\n\t\t\t\t}\n\t\t\t\tif(vakata_dnd.is_drag) { $.vakata.dnd.stop({}); }\n\t\t\t\ttry {\n\t\t\t\t\te.currentTarget.unselectable = \"on\";\n\t\t\t\t\te.currentTarget.onselectstart = function() { return false; };\n\t\t\t\t\tif(e.currentTarget.style) {\n\t\t\t\t\t\te.currentTarget.style.touchAction = \"none\";\n\t\t\t\t\t\te.currentTarget.style.msTouchAction = \"none\";\n\t\t\t\t\t\te.currentTarget.style.MozUserSelect = \"none\";\n\t\t\t\t\t}\n\t\t\t\t} catch(ignore) { }\n\t\t\t\tvakata_dnd.init_x\t= e.pageX;\n\t\t\t\tvakata_dnd.init_y\t= e.pageY;\n\t\t\t\tvakata_dnd.data\t\t= data;\n\t\t\t\tvakata_dnd.is_down\t= true;\n\t\t\t\tvakata_dnd.element\t= e.currentTarget;\n\t\t\t\tvakata_dnd.target\t= e.target;\n\t\t\t\tvakata_dnd.is_touch\t= e.type === \"touchstart\";\n\t\t\t\tif(html !== false) {\n\t\t\t\t\tvakata_dnd.helper = $(\"<div id='vakata-dnd'></div>\").html(html).css({\n\t\t\t\t\t\t\"display\"\t\t: \"block\",\n\t\t\t\t\t\t\"margin\"\t\t: \"0\",\n\t\t\t\t\t\t\"padding\"\t\t: \"0\",\n\t\t\t\t\t\t\"position\"\t\t: \"absolute\",\n\t\t\t\t\t\t\"top\"\t\t\t: \"-2000px\",\n\t\t\t\t\t\t\"lineHeight\"\t: \"16px\",\n\t\t\t\t\t\t\"zIndex\"\t\t: \"10000\"\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t$(document).on(\"mousemove.vakata.jstree touchmove.vakata.jstree\", $.vakata.dnd.drag);\n\t\t\t\t$(document).on(\"mouseup.vakata.jstree touchend.vakata.jstree\", $.vakata.dnd.stop);\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tdrag : function (e) {\n\t\t\t\tif(e.type === \"touchmove\" && e.originalEvent && e.originalEvent.changedTouches && e.originalEvent.changedTouches[0]) {\n\t\t\t\t\te.pageX = e.originalEvent.changedTouches[0].pageX;\n\t\t\t\t\te.pageY = e.originalEvent.changedTouches[0].pageY;\n\t\t\t\t\te.target = document.elementFromPoint(e.originalEvent.changedTouches[0].pageX - window.pageXOffset, e.originalEvent.changedTouches[0].pageY - window.pageYOffset);\n\t\t\t\t}\n\t\t\t\tif(!vakata_dnd.is_down) { return; }\n\t\t\t\tif(!vakata_dnd.is_drag) {\n\t\t\t\t\tif(\n\t\t\t\t\t\tMath.abs(e.pageX - vakata_dnd.init_x) > (vakata_dnd.is_touch ? $.vakata.dnd.settings.threshold_touch : $.vakata.dnd.settings.threshold) ||\n\t\t\t\t\t\tMath.abs(e.pageY - vakata_dnd.init_y) > (vakata_dnd.is_touch ? $.vakata.dnd.settings.threshold_touch : $.vakata.dnd.settings.threshold)\n\t\t\t\t\t) {\n\t\t\t\t\t\tif(vakata_dnd.helper) {\n\t\t\t\t\t\t\tvakata_dnd.helper.appendTo(document.body);\n\t\t\t\t\t\t\tvakata_dnd.helper_w = vakata_dnd.helper.outerWidth();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvakata_dnd.is_drag = true;\n\t\t\t\t\t\t$(vakata_dnd.target).one('click.vakata', false);\n\t\t\t\t\t\t/**\n\t\t\t\t\t\t * triggered on the document when a drag starts\n\t\t\t\t\t\t * @event\n\t\t\t\t\t\t * @plugin dnd\n\t\t\t\t\t\t * @name dnd_start.vakata\n\t\t\t\t\t\t * @param {Mixed} data any data supplied with the call to $.vakata.dnd.start\n\t\t\t\t\t\t * @param {DOM} element the DOM element being dragged\n\t\t\t\t\t\t * @param {jQuery} helper the helper shown next to the mouse\n\t\t\t\t\t\t * @param {Object} event the event that caused the start (probably mousemove)\n\t\t\t\t\t\t */\n\t\t\t\t\t\t$.vakata.dnd._trigger(\"start\", e);\n\t\t\t\t\t}\n\t\t\t\t\telse { return; }\n\t\t\t\t}\n\n\t\t\t\tvar d  = false, w  = false,\n\t\t\t\t\tdh = false, wh = false,\n\t\t\t\t\tdw = false, ww = false,\n\t\t\t\t\tdt = false, dl = false,\n\t\t\t\t\tht = false, hl = false;\n\n\t\t\t\tvakata_dnd.scroll_t = 0;\n\t\t\t\tvakata_dnd.scroll_l = 0;\n\t\t\t\tvakata_dnd.scroll_e = false;\n\t\t\t\t$($(e.target).parentsUntil(\"body\").addBack().get().reverse())\n\t\t\t\t\t.filter(function () {\n\t\t\t\t\t\treturn\tthis.ownerDocument && (/^auto|scroll$/).test($(this).css(\"overflow\")) &&\n\t\t\t\t\t\t\t\t(this.scrollHeight > this.offsetHeight || this.scrollWidth > this.offsetWidth);\n\t\t\t\t\t})\n\t\t\t\t\t.each(function () {\n\t\t\t\t\t\tvar t = $(this), o = t.offset();\n\t\t\t\t\t\tif(this.scrollHeight > this.offsetHeight) {\n\t\t\t\t\t\t\tif(o.top + t.height() - e.pageY < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_t = 1; }\n\t\t\t\t\t\t\tif(e.pageY - o.top < $.vakata.dnd.settings.scroll_proximity)\t\t\t\t{ vakata_dnd.scroll_t = -1; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(this.scrollWidth > this.offsetWidth) {\n\t\t\t\t\t\t\tif(o.left + t.width() - e.pageX < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_l = 1; }\n\t\t\t\t\t\t\tif(e.pageX - o.left < $.vakata.dnd.settings.scroll_proximity)\t\t\t\t{ vakata_dnd.scroll_l = -1; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(vakata_dnd.scroll_t || vakata_dnd.scroll_l) {\n\t\t\t\t\t\t\tvakata_dnd.scroll_e = $(this);\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\tif(!vakata_dnd.scroll_e) {\n\t\t\t\t\td  = $(document); w = $(window);\n\t\t\t\t\tdh = d.height(); wh = w.height();\n\t\t\t\t\tdw = d.width(); ww = w.width();\n\t\t\t\t\tdt = d.scrollTop(); dl = d.scrollLeft();\n\t\t\t\t\tif(dh > wh && e.pageY - dt < $.vakata.dnd.settings.scroll_proximity)\t\t{ vakata_dnd.scroll_t = -1;  }\n\t\t\t\t\tif(dh > wh && wh - (e.pageY - dt) < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_t = 1; }\n\t\t\t\t\tif(dw > ww && e.pageX - dl < $.vakata.dnd.settings.scroll_proximity)\t\t{ vakata_dnd.scroll_l = -1; }\n\t\t\t\t\tif(dw > ww && ww - (e.pageX - dl) < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_l = 1; }\n\t\t\t\t\tif(vakata_dnd.scroll_t || vakata_dnd.scroll_l) {\n\t\t\t\t\t\tvakata_dnd.scroll_e = d;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(vakata_dnd.scroll_e) { $.vakata.dnd._scroll(true); }\n\n\t\t\t\tif(vakata_dnd.helper) {\n\t\t\t\t\tht = parseInt(e.pageY + $.vakata.dnd.settings.helper_top, 10);\n\t\t\t\t\thl = parseInt(e.pageX + $.vakata.dnd.settings.helper_left, 10);\n\t\t\t\t\tif(dh && ht + 25 > dh) { ht = dh - 50; }\n\t\t\t\t\tif(dw && hl + vakata_dnd.helper_w > dw) { hl = dw - (vakata_dnd.helper_w + 2); }\n\t\t\t\t\tvakata_dnd.helper.css({\n\t\t\t\t\t\tleft\t: hl + \"px\",\n\t\t\t\t\t\ttop\t\t: ht + \"px\"\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t/**\n\t\t\t\t * triggered on the document when a drag is in progress\n\t\t\t\t * @event\n\t\t\t\t * @plugin dnd\n\t\t\t\t * @name dnd_move.vakata\n\t\t\t\t * @param {Mixed} data any data supplied with the call to $.vakata.dnd.start\n\t\t\t\t * @param {DOM} element the DOM element being dragged\n\t\t\t\t * @param {jQuery} helper the helper shown next to the mouse\n\t\t\t\t * @param {Object} event the event that caused this to trigger (most likely mousemove)\n\t\t\t\t */\n\t\t\t\t$.vakata.dnd._trigger(\"move\", e);\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tstop : function (e) {\n\t\t\t\tif(e.type === \"touchend\" && e.originalEvent && e.originalEvent.changedTouches && e.originalEvent.changedTouches[0]) {\n\t\t\t\t\te.pageX = e.originalEvent.changedTouches[0].pageX;\n\t\t\t\t\te.pageY = e.originalEvent.changedTouches[0].pageY;\n\t\t\t\t\te.target = document.elementFromPoint(e.originalEvent.changedTouches[0].pageX - window.pageXOffset, e.originalEvent.changedTouches[0].pageY - window.pageYOffset);\n\t\t\t\t}\n\t\t\t\tif(vakata_dnd.is_drag) {\n\t\t\t\t\t/**\n\t\t\t\t\t * triggered on the document when a drag stops (the dragged element is dropped)\n\t\t\t\t\t * @event\n\t\t\t\t\t * @plugin dnd\n\t\t\t\t\t * @name dnd_stop.vakata\n\t\t\t\t\t * @param {Mixed} data any data supplied with the call to $.vakata.dnd.start\n\t\t\t\t\t * @param {DOM} element the DOM element being dragged\n\t\t\t\t\t * @param {jQuery} helper the helper shown next to the mouse\n\t\t\t\t\t * @param {Object} event the event that caused the stop\n\t\t\t\t\t */\n\t\t\t\t\tif (e.target !== vakata_dnd.target) {\n\t\t\t\t\t\t$(vakata_dnd.target).off('click.vakata');\n\t\t\t\t\t}\n\t\t\t\t\t$.vakata.dnd._trigger(\"stop\", e);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tif(e.type === \"touchend\" && e.target === vakata_dnd.target) {\n\t\t\t\t\t\tvar to = setTimeout(function () { $(e.target).trigger('click'); }, 100);\n\t\t\t\t\t\t$(e.target).one('click', function() { if(to) { clearTimeout(to); } });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t$.vakata.dnd._clean();\n\t\t\t\treturn false;\n\t\t\t}\n\t\t};\n\t}($));\n\n\t// include the dnd plugin by default\n\t// $.jstree.defaults.plugins.push(\"dnd\");\n\n\n/**\n * ### Massload plugin\n *\n * Adds massload functionality to jsTree, so that multiple nodes can be loaded in a single request (only useful with lazy loading).\n */\n\n\t/**\n\t * massload configuration\n\t *\n\t * It is possible to set this to a standard jQuery-like AJAX config.\n\t * In addition to the standard jQuery ajax options here you can supply functions for `data` and `url`, the functions will be run in the current instance's scope and a param will be passed indicating which node IDs need to be loaded, the return value of those functions will be used.\n\t *\n\t * You can also set this to a function, that function will receive the node IDs being loaded as argument and a second param which is a function (callback) which should be called with the result.\n\t *\n\t * Both the AJAX and the function approach rely on the same return value - an object where the keys are the node IDs, and the value is the children of that node as an array.\n\t *\n\t *\t{\n\t *\t\t\"id1\" : [{ \"text\" : \"Child of ID1\", \"id\" : \"c1\" }, { \"text\" : \"Another child of ID1\", \"id\" : \"c2\" }],\n\t *\t\t\"id2\" : [{ \"text\" : \"Child of ID2\", \"id\" : \"c3\" }]\n\t *\t}\n\t * \n\t * @name $.jstree.defaults.massload\n\t * @plugin massload\n\t */\n\t$.jstree.defaults.massload = null;\n\t$.jstree.plugins.massload = function (options, parent) {\n\t\tthis.init = function (el, options) {\n\t\t\tthis._data.massload = {};\n\t\t\tparent.init.call(this, el, options);\n\t\t};\n\t\tthis._load_nodes = function (nodes, callback, is_callback, force_reload) {\n\t\t\tvar s = this.settings.massload,\t\t\t\t\n\t\t\t\ttoLoad = [],\n\t\t\t\tm = this._model.data,\n\t\t\t\ti, j, dom;\n\t\t\tif (!is_callback) {\n\t\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\t\tif(!m[nodes[i]] || ( (!m[nodes[i]].state.loaded && !m[nodes[i]].state.failed) || force_reload) ) {\n\t\t\t\t\t\ttoLoad.push(nodes[i]);\n\t\t\t\t\t\tdom = this.get_node(nodes[i], true);\n\t\t\t\t\t\tif (dom && dom.length) {\n\t\t\t\t\t\t\tdom.addClass(\"jstree-loading\").attr('aria-busy',true);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis._data.massload = {};\n\t\t\t\tif (toLoad.length) {\n\t\t\t\t\tif($.vakata.is_function(s)) {\n\t\t\t\t\t\treturn s.call(this, toLoad, function (data) {\n\t\t\t\t\t\t\tvar i, j;\n\t\t\t\t\t\t\tif(data) {\n\t\t\t\t\t\t\t\tfor(i in data) {\n\t\t\t\t\t\t\t\t\tif(data.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\tthis._data.massload[i] = data[i];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\t\t\t\t\tdom = this.get_node(nodes[i], true);\n\t\t\t\t\t\t\t\tif (dom && dom.length) {\n\t\t\t\t\t\t\t\t\tdom.removeClass(\"jstree-loading\").attr('aria-busy',false);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tparent._load_nodes.call(this, nodes, callback, is_callback, force_reload);\n\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\t}\n\t\t\t\t\tif(typeof s === 'object' && s && s.url) {\n\t\t\t\t\t\ts = $.extend(true, {}, s);\n\t\t\t\t\t\tif($.vakata.is_function(s.url)) {\n\t\t\t\t\t\t\ts.url = s.url.call(this, toLoad);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif($.vakata.is_function(s.data)) {\n\t\t\t\t\t\t\ts.data = s.data.call(this, toLoad);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn $.ajax(s)\n\t\t\t\t\t\t\t.done(function (data,t,x) {\n\t\t\t\t\t\t\t\t\tvar i, j;\n\t\t\t\t\t\t\t\t\tif(data) {\n\t\t\t\t\t\t\t\t\t\tfor(i in data) {\n\t\t\t\t\t\t\t\t\t\t\tif(data.hasOwnProperty(i)) {\n\t\t\t\t\t\t\t\t\t\t\t\tthis._data.massload[i] = data[i];\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tdom = this.get_node(nodes[i], true);\n\t\t\t\t\t\t\t\t\t\tif (dom && dom.length) {\n\t\t\t\t\t\t\t\t\t\t\tdom.removeClass(\"jstree-loading\").attr('aria-busy',false);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tparent._load_nodes.call(this, nodes, callback, is_callback, force_reload);\n\t\t\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t\t\t.fail(function (f) {\n\t\t\t\t\t\t\t\t\tparent._load_nodes.call(this, nodes, callback, is_callback, force_reload);\n\t\t\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn parent._load_nodes.call(this, nodes, callback, is_callback, force_reload);\n\t\t};\n\t\tthis._load_node = function (obj, callback) {\n\t\t\tvar data = this._data.massload[obj.id],\n\t\t\t\trslt = null, dom;\n\t\t\tif(data) {\n\t\t\t\trslt = this[typeof data === 'string' ? '_append_html_data' : '_append_json_data'](\n\t\t\t\t\tobj,\n\t\t\t\t\ttypeof data === 'string' ? $($.parseHTML(data)).filter(function () { return this.nodeType !== 3; }) : data,\n\t\t\t\t\tfunction (status) { callback.call(this, status); }\n\t\t\t\t);\n\t\t\t\tdom = this.get_node(obj.id, true);\n\t\t\t\tif (dom && dom.length) {\n\t\t\t\t\tdom.removeClass(\"jstree-loading\").attr('aria-busy',false);\n\t\t\t\t}\n\t\t\t\tdelete this._data.massload[obj.id];\n\t\t\t\treturn rslt;\n\t\t\t}\n\t\t\treturn parent._load_node.call(this, obj, callback);\n\t\t};\n\t};\n\n\n/**\n * ### Search plugin\n *\n * Adds search functionality to jsTree.\n */\n\n\t/**\n\t * stores all defaults for the search plugin\n\t * @name $.jstree.defaults.search\n\t * @plugin search\n\t */\n\t$.jstree.defaults.search = {\n\t\t/**\n\t\t * a jQuery-like AJAX config, which jstree uses if a server should be queried for results.\n\t\t *\n\t\t * A `str` (which is the search string) parameter will be added with the request, an optional `inside` parameter will be added if the search is limited to a node id. The expected result is a JSON array with nodes that need to be opened so that matching nodes will be revealed.\n\t\t * Leave this setting as `false` to not query the server. You can also set this to a function, which will be invoked in the instance's scope and receive 3 parameters - the search string, the callback to call with the array of nodes to load, and the optional node ID to limit the search to\n\t\t * @name $.jstree.defaults.search.ajax\n\t\t * @plugin search\n\t\t */\n\t\tajax : false,\n\t\t/**\n\t\t * Indicates if the search should be fuzzy or not (should `chnd3` match `child node 3`). Default is `false`.\n\t\t * @name $.jstree.defaults.search.fuzzy\n\t\t * @plugin search\n\t\t */\n\t\tfuzzy : false,\n\t\t/**\n\t\t * Indicates if the search should be case sensitive. Default is `false`.\n\t\t * @name $.jstree.defaults.search.case_sensitive\n\t\t * @plugin search\n\t\t */\n\t\tcase_sensitive : false,\n\t\t/**\n\t\t * Indicates if the tree should be filtered (by default) to show only matching nodes (keep in mind this can be a heavy on large trees in old browsers).\n\t\t * This setting can be changed at runtime when calling the search method. Default is `false`.\n\t\t * @name $.jstree.defaults.search.show_only_matches\n\t\t * @plugin search\n\t\t */\n\t\tshow_only_matches : false,\n\t\t/**\n\t\t * Indicates if the children of matched element are shown (when show_only_matches is true)\n\t\t * This setting can be changed at runtime when calling the search method. Default is `false`.\n\t\t * @name $.jstree.defaults.search.show_only_matches_children\n\t\t * @plugin search\n\t\t */\n\t\tshow_only_matches_children : false,\n\t\t/**\n\t\t * Indicates if all nodes opened to reveal the search result, should be closed when the search is cleared or a new search is performed. Default is `true`.\n\t\t * @name $.jstree.defaults.search.close_opened_onclear\n\t\t * @plugin search\n\t\t */\n\t\tclose_opened_onclear : true,\n\t\t/**\n\t\t * Indicates if only leaf nodes should be included in search results. Default is `false`.\n\t\t * @name $.jstree.defaults.search.search_leaves_only\n\t\t * @plugin search\n\t\t */\n\t\tsearch_leaves_only : false,\n\t\t/**\n\t\t * If set to a function it wil be called in the instance's scope with two arguments - search string and node (where node will be every node in the structure, so use with caution).\n\t\t * If the function returns a truthy value the node will be considered a match (it might not be displayed if search_only_leaves is set to true and the node is not a leaf). Default is `false`.\n\t\t * @name $.jstree.defaults.search.search_callback\n\t\t * @plugin search\n\t\t */\n\t\tsearch_callback : false\n\t};\n\n\t$.jstree.plugins.search = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis._data.search.str = \"\";\n\t\t\tthis._data.search.dom = $();\n\t\t\tthis._data.search.res = [];\n\t\t\tthis._data.search.opn = [];\n\t\t\tthis._data.search.som = false;\n\t\t\tthis._data.search.smc = false;\n\t\t\tthis._data.search.hdn = [];\n\n\t\t\tthis.element\n\t\t\t\t.on(\"search.jstree\", function (e, data) {\n\t\t\t\t\t\tif(this._data.search.som && data.res.length) {\n\t\t\t\t\t\t\tvar m = this._model.data, i, j, p = [], k, l;\n\t\t\t\t\t\t\tfor(i = 0, j = data.res.length; i < j; i++) {\n\t\t\t\t\t\t\t\tif(m[data.res[i]] && !m[data.res[i]].state.hidden) {\n\t\t\t\t\t\t\t\t\tp.push(data.res[i]);\n\t\t\t\t\t\t\t\t\tp = p.concat(m[data.res[i]].parents);\n\t\t\t\t\t\t\t\t\tif(this._data.search.smc) {\n\t\t\t\t\t\t\t\t\t\tfor (k = 0, l = m[data.res[i]].children_d.length; k < l; k++) {\n\t\t\t\t\t\t\t\t\t\t\tif (m[m[data.res[i]].children_d[k]] && !m[m[data.res[i]].children_d[k]].state.hidden) {\n\t\t\t\t\t\t\t\t\t\t\t\tp.push(m[data.res[i]].children_d[k]);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tp = $.vakata.array_remove_item($.vakata.array_unique(p), $.jstree.root);\n\t\t\t\t\t\t\tthis._data.search.hdn = this.hide_all(true);\n\t\t\t\t\t\t\tthis.show_node(p, true);\n\t\t\t\t\t\t\tthis.redraw(true);\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"clear_search.jstree\", function (e, data) {\n\t\t\t\t\t\tif(this._data.search.som && data.res.length) {\n\t\t\t\t\t\t\tthis.show_node(this._data.search.hdn, true);\n\t\t\t\t\t\t\tthis.redraw(true);\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this));\n\t\t};\n\t\t/**\n\t\t * used to search the tree nodes for a given string\n\t\t * @name search(str [, skip_async])\n\t\t * @param {String} str the search string\n\t\t * @param {Boolean} skip_async if set to true server will not be queried even if configured\n\t\t * @param {Boolean} show_only_matches if set to true only matching nodes will be shown (keep in mind this can be very slow on large trees or old browsers)\n\t\t * @param {mixed} inside an optional node to whose children to limit the search\n\t\t * @param {Boolean} append if set to true the results of this search are appended to the previous search\n\t\t * @plugin search\n\t\t * @trigger search.jstree\n\t\t */\n\t\tthis.search = function (str, skip_async, show_only_matches, inside, append, show_only_matches_children) {\n\t\t\tif(str === false || $.vakata.trim(str.toString()) === \"\") {\n\t\t\t\treturn this.clear_search();\n\t\t\t}\n\t\t\tinside = this.get_node(inside);\n\t\t\tinside = inside && (inside.id || inside.id === 0) ? inside.id : null;\n\t\t\tstr = str.toString();\n\t\t\tvar s = this.settings.search,\n\t\t\t\ta = s.ajax ? s.ajax : false,\n\t\t\t\tm = this._model.data,\n\t\t\t\tf = null,\n\t\t\t\tr = [],\n\t\t\t\tp = [], i, j;\n\t\t\tif(this._data.search.res.length && !append) {\n\t\t\t\tthis.clear_search();\n\t\t\t}\n\t\t\tif(show_only_matches === undefined) {\n\t\t\t\tshow_only_matches = s.show_only_matches;\n\t\t\t}\n\t\t\tif(show_only_matches_children === undefined) {\n\t\t\t\tshow_only_matches_children = s.show_only_matches_children;\n\t\t\t}\n\t\t\tif(!skip_async && a !== false) {\n\t\t\t\tif($.vakata.is_function(a)) {\n\t\t\t\t\treturn a.call(this, str, function (d) {\n\t\t\t\t\t\t\tif(d && d.d) { d = d.d; }\n\t\t\t\t\t\t\tthis._load_nodes(!$.vakata.is_array(d) ? [] : $.vakata.array_unique(d), function () {\n\t\t\t\t\t\t\t\tthis.search(str, true, show_only_matches, inside, append, show_only_matches_children);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}.bind(this), inside);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\ta = $.extend({}, a);\n\t\t\t\t\tif(!a.data) { a.data = {}; }\n\t\t\t\t\ta.data.str = str;\n\t\t\t\t\tif(inside) {\n\t\t\t\t\t\ta.data.inside = inside;\n\t\t\t\t\t}\n\t\t\t\t\tif (this._data.search.lastRequest) {\n\t\t\t\t\t\tthis._data.search.lastRequest.abort();\n\t\t\t\t\t}\n\t\t\t\t\tthis._data.search.lastRequest = $.ajax(a)\n\t\t\t\t\t\t.fail(function () {\n\t\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'ajax', 'plugin' : 'search', 'id' : 'search_01', 'reason' : 'Could not load search parents', 'data' : JSON.stringify(a) };\n\t\t\t\t\t\t\tthis.settings.core.error.call(this, this._data.core.last_error);\n\t\t\t\t\t\t}.bind(this))\n\t\t\t\t\t\t.done(function (d) {\n\t\t\t\t\t\t\tif(d && d.d) { d = d.d; }\n\t\t\t\t\t\t\tthis._load_nodes(!$.vakata.is_array(d) ? [] : $.vakata.array_unique(d), function () {\n\t\t\t\t\t\t\t\tthis.search(str, true, show_only_matches, inside, append, show_only_matches_children);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}.bind(this));\n\t\t\t\t\treturn this._data.search.lastRequest;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!append) {\n\t\t\t\tthis._data.search.str = str;\n\t\t\t\tthis._data.search.dom = $();\n\t\t\t\tthis._data.search.res = [];\n\t\t\t\tthis._data.search.opn = [];\n\t\t\t\tthis._data.search.som = show_only_matches;\n\t\t\t\tthis._data.search.smc = show_only_matches_children;\n\t\t\t}\n\n\t\t\tf = new $.vakata.search(str, true, { caseSensitive : s.case_sensitive, fuzzy : s.fuzzy });\n\t\t\t$.each(m[inside ? inside : $.jstree.root].children_d, function (ii, i) {\n\t\t\t\tvar v = m[i];\n\t\t\t\tif(v.text && !v.state.hidden && (!s.search_leaves_only || (v.state.loaded && v.children.length === 0)) && ( (s.search_callback && s.search_callback.call(this, str, v)) || (!s.search_callback && f.search(v.text).isMatch) ) ) {\n\t\t\t\t\tr.push(i);\n\t\t\t\t\tp = p.concat(v.parents);\n\t\t\t\t}\n\t\t\t});\n\t\t\tif(r.length) {\n\t\t\t\tp = $.vakata.array_unique(p);\n\t\t\t\tfor(i = 0, j = p.length; i < j; i++) {\n\t\t\t\t\tif(p[i] !== $.jstree.root && m[p[i]] && this.open_node(p[i], null, 0) === true) {\n\t\t\t\t\t\tthis._data.search.opn.push(p[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(!append) {\n\t\t\t\t\tthis._data.search.dom = $(this.element[0].querySelectorAll('#' + $.map(r, function (v) { return \"0123456789\".indexOf(v[0]) !== -1 ? '\\\\3' + v[0] + ' ' + v.substr(1).replace($.jstree.idregex,'\\\\$&') : v.replace($.jstree.idregex,'\\\\$&'); }).join(', #')));\n\t\t\t\t\tthis._data.search.res = r;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis._data.search.dom = this._data.search.dom.add($(this.element[0].querySelectorAll('#' + $.map(r, function (v) { return \"0123456789\".indexOf(v[0]) !== -1 ? '\\\\3' + v[0] + ' ' + v.substr(1).replace($.jstree.idregex,'\\\\$&') : v.replace($.jstree.idregex,'\\\\$&'); }).join(', #'))));\n\t\t\t\t\tthis._data.search.res = $.vakata.array_unique(this._data.search.res.concat(r));\n\t\t\t\t}\n\t\t\t\tthis._data.search.dom.children(\".jstree-anchor\").addClass('jstree-search');\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered after search is complete\n\t\t\t * @event\n\t\t\t * @name search.jstree\n\t\t\t * @param {jQuery} nodes a jQuery collection of matching nodes\n\t\t\t * @param {String} str the search string\n\t\t\t * @param {Array} res a collection of objects represeing the matching nodes\n\t\t\t * @plugin search\n\t\t\t */\n\t\t\tthis.trigger('search', { nodes : this._data.search.dom, str : str, res : this._data.search.res, show_only_matches : show_only_matches });\n\t\t};\n\t\t/**\n\t\t * used to clear the last search (removes classes and shows all nodes if filtering is on)\n\t\t * @name clear_search()\n\t\t * @plugin search\n\t\t * @trigger clear_search.jstree\n\t\t */\n\t\tthis.clear_search = function () {\n\t\t\tif(this.settings.search.close_opened_onclear) {\n\t\t\t\tthis.close_node(this._data.search.opn, 0);\n\t\t\t}\n\t\t\t/**\n\t\t\t * triggered after search is complete\n\t\t\t * @event\n\t\t\t * @name clear_search.jstree\n\t\t\t * @param {jQuery} nodes a jQuery collection of matching nodes (the result from the last search)\n\t\t\t * @param {String} str the search string (the last search string)\n\t\t\t * @param {Array} res a collection of objects represeing the matching nodes (the result from the last search)\n\t\t\t * @plugin search\n\t\t\t */\n\t\t\tthis.trigger('clear_search', { 'nodes' : this._data.search.dom, str : this._data.search.str, res : this._data.search.res });\n\t\t\tif(this._data.search.res.length) {\n\t\t\t\tthis._data.search.dom = $(this.element[0].querySelectorAll('#' + $.map(this._data.search.res, function (v) {\n\t\t\t\t\treturn \"0123456789\".indexOf(v[0]) !== -1 ? '\\\\3' + v[0] + ' ' + v.substr(1).replace($.jstree.idregex,'\\\\$&') : v.replace($.jstree.idregex,'\\\\$&');\n\t\t\t\t}).join(', #')));\n\t\t\t\tthis._data.search.dom.children(\".jstree-anchor\").removeClass(\"jstree-search\");\n\t\t\t}\n\t\t\tthis._data.search.str = \"\";\n\t\t\tthis._data.search.res = [];\n\t\t\tthis._data.search.opn = [];\n\t\t\tthis._data.search.dom = $();\n\t\t};\n\n\t\tthis.redraw_node = function(obj, deep, callback, force_render) {\n\t\t\tobj = parent.redraw_node.apply(this, arguments);\n\t\t\tif(obj) {\n\t\t\t\tif($.inArray(obj.id, this._data.search.res) !== -1) {\n\t\t\t\t\tvar i, j, tmp = null;\n\t\t\t\t\tfor(i = 0, j = obj.childNodes.length; i < j; i++) {\n\t\t\t\t\t\tif(obj.childNodes[i] && obj.childNodes[i].className && obj.childNodes[i].className.indexOf(\"jstree-anchor\") !== -1) {\n\t\t\t\t\t\t\ttmp = obj.childNodes[i];\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(tmp) {\n\t\t\t\t\t\ttmp.className += ' jstree-search';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn obj;\n\t\t};\n\t};\n\n\t// helpers\n\t(function ($) {\n\t\t// from http://kiro.me/projects/fuse.html\n\t\t$.vakata.search = function(pattern, txt, options) {\n\t\t\toptions = options || {};\n\t\t\toptions = $.extend({}, $.vakata.search.defaults, options);\n\t\t\tif(options.fuzzy !== false) {\n\t\t\t\toptions.fuzzy = true;\n\t\t\t}\n\t\t\tpattern = options.caseSensitive ? pattern : pattern.toLowerCase();\n\t\t\tvar MATCH_LOCATION\t= options.location,\n\t\t\t\tMATCH_DISTANCE\t= options.distance,\n\t\t\t\tMATCH_THRESHOLD\t= options.threshold,\n\t\t\t\tpatternLen = pattern.length,\n\t\t\t\tmatchmask, pattern_alphabet, match_bitapScore, search;\n\t\t\tif(patternLen > 32) {\n\t\t\t\toptions.fuzzy = false;\n\t\t\t}\n\t\t\tif(options.fuzzy) {\n\t\t\t\tmatchmask = 1 << (patternLen - 1);\n\t\t\t\tpattern_alphabet = (function () {\n\t\t\t\t\tvar mask = {},\n\t\t\t\t\t\ti = 0;\n\t\t\t\t\tfor (i = 0; i < patternLen; i++) {\n\t\t\t\t\t\tmask[pattern.charAt(i)] = 0;\n\t\t\t\t\t}\n\t\t\t\t\tfor (i = 0; i < patternLen; i++) {\n\t\t\t\t\t\tmask[pattern.charAt(i)] |= 1 << (patternLen - i - 1);\n\t\t\t\t\t}\n\t\t\t\t\treturn mask;\n\t\t\t\t}());\n\t\t\t\tmatch_bitapScore = function (e, x) {\n\t\t\t\t\tvar accuracy = e / patternLen,\n\t\t\t\t\t\tproximity = Math.abs(MATCH_LOCATION - x);\n\t\t\t\t\tif(!MATCH_DISTANCE) {\n\t\t\t\t\t\treturn proximity ? 1.0 : accuracy;\n\t\t\t\t\t}\n\t\t\t\t\treturn accuracy + (proximity / MATCH_DISTANCE);\n\t\t\t\t};\n\t\t\t}\n\t\t\tsearch = function (text) {\n\t\t\t\ttext = options.caseSensitive ? text.toString() : text.toString().toLowerCase();\n\t\t\t\tif(pattern === text || text.indexOf(pattern) !== -1) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tisMatch: true,\n\t\t\t\t\t\tscore: 0\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif(!options.fuzzy) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tisMatch: false,\n\t\t\t\t\t\tscore: 1\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tvar i, j,\n\t\t\t\t\ttextLen = text.length,\n\t\t\t\t\tscoreThreshold = MATCH_THRESHOLD,\n\t\t\t\t\tbestLoc = text.indexOf(pattern, MATCH_LOCATION),\n\t\t\t\t\tbinMin, binMid,\n\t\t\t\t\tbinMax = patternLen + textLen,\n\t\t\t\t\tlastRd, start, finish, rd, charMatch,\n\t\t\t\t\tscore = 1,\n\t\t\t\t\tlocations = [];\n\t\t\t\tif (bestLoc !== -1) {\n\t\t\t\t\tscoreThreshold = Math.min(match_bitapScore(0, bestLoc), scoreThreshold);\n\t\t\t\t\tbestLoc = text.lastIndexOf(pattern, MATCH_LOCATION + patternLen);\n\t\t\t\t\tif (bestLoc !== -1) {\n\t\t\t\t\t\tscoreThreshold = Math.min(match_bitapScore(0, bestLoc), scoreThreshold);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbestLoc = -1;\n\t\t\t\tfor (i = 0; i < patternLen; i++) {\n\t\t\t\t\tbinMin = 0;\n\t\t\t\t\tbinMid = binMax;\n\t\t\t\t\twhile (binMin < binMid) {\n\t\t\t\t\t\tif (match_bitapScore(i, MATCH_LOCATION + binMid) <= scoreThreshold) {\n\t\t\t\t\t\t\tbinMin = binMid;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tbinMax = binMid;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbinMid = Math.floor((binMax - binMin) / 2 + binMin);\n\t\t\t\t\t}\n\t\t\t\t\tbinMax = binMid;\n\t\t\t\t\tstart = Math.max(1, MATCH_LOCATION - binMid + 1);\n\t\t\t\t\tfinish = Math.min(MATCH_LOCATION + binMid, textLen) + patternLen;\n\t\t\t\t\trd = new Array(finish + 2);\n\t\t\t\t\trd[finish + 1] = (1 << i) - 1;\n\t\t\t\t\tfor (j = finish; j >= start; j--) {\n\t\t\t\t\t\tcharMatch = pattern_alphabet[text.charAt(j - 1)];\n\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\trd[j] = ((rd[j + 1] << 1) | 1) & charMatch;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\trd[j] = ((rd[j + 1] << 1) | 1) & charMatch | (((lastRd[j + 1] | lastRd[j]) << 1) | 1) | lastRd[j + 1];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (rd[j] & matchmask) {\n\t\t\t\t\t\t\tscore = match_bitapScore(i, j - 1);\n\t\t\t\t\t\t\tif (score <= scoreThreshold) {\n\t\t\t\t\t\t\t\tscoreThreshold = score;\n\t\t\t\t\t\t\t\tbestLoc = j - 1;\n\t\t\t\t\t\t\t\tlocations.push(bestLoc);\n\t\t\t\t\t\t\t\tif (bestLoc > MATCH_LOCATION) {\n\t\t\t\t\t\t\t\t\tstart = Math.max(1, 2 * MATCH_LOCATION - bestLoc);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (match_bitapScore(i + 1, MATCH_LOCATION) > scoreThreshold) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tlastRd = rd;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\tisMatch: bestLoc >= 0,\n\t\t\t\t\tscore: score\n\t\t\t\t};\n\t\t\t};\n\t\t\treturn txt === true ? { 'search' : search } : search(txt);\n\t\t};\n\t\t$.vakata.search.defaults = {\n\t\t\tlocation : 0,\n\t\t\tdistance : 100,\n\t\t\tthreshold : 0.6,\n\t\t\tfuzzy : false,\n\t\t\tcaseSensitive : false\n\t\t};\n\t}($));\n\n\t// include the search plugin by default\n\t// $.jstree.defaults.plugins.push(\"search\");\n\n\n/**\n * ### Sort plugin\n *\n * Automatically sorts all siblings in the tree according to a sorting function.\n */\n\n\t/**\n\t * the settings function used to sort the nodes.\n\t * It is executed in the tree's context, accepts two nodes as arguments and should return `1` or `-1`.\n\t * @name $.jstree.defaults.sort\n\t * @plugin sort\n\t */\n\t$.jstree.defaults.sort = function (a, b) {\n\t\t//return this.get_type(a) === this.get_type(b) ? (this.get_text(a) > this.get_text(b) ? 1 : -1) : this.get_type(a) >= this.get_type(b);\n\t\treturn this.get_text(a) > this.get_text(b) ? 1 : -1;\n\t};\n\t$.jstree.plugins.sort = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\t\t\tthis.element\n\t\t\t\t.on(\"model.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.sort(data.parent, true);\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"rename_node.jstree create_node.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.sort(data.parent || data.node.parent, false);\n\t\t\t\t\t\tthis.redraw_node(data.parent || data.node.parent, true);\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"move_node.jstree copy_node.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.sort(data.parent, false);\n\t\t\t\t\t\tthis.redraw_node(data.parent, true);\n\t\t\t\t\t}.bind(this));\n\t\t};\n\t\t/**\n\t\t * used to sort a node's children\n\t\t * @private\n\t\t * @name sort(obj [, deep])\n\t\t * @param  {mixed} obj the node\n\t\t * @param {Boolean} deep if set to `true` nodes are sorted recursively.\n\t\t * @plugin sort\n\t\t * @trigger search.jstree\n\t\t */\n\t\tthis.sort = function (obj, deep) {\n\t\t\tvar i, j;\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(obj && obj.children && obj.children.length) {\n\t\t\t\tobj.children.sort(this.settings.sort.bind(this));\n\t\t\t\tif(deep) {\n\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\tthis.sort(obj.children_d[i], false);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t};\n\n\t// include the sort plugin by default\n\t// $.jstree.defaults.plugins.push(\"sort\");\n\n/**\n * ### State plugin\n *\n * Saves the state of the tree (selected nodes, opened nodes) on the user's computer using available options (localStorage, cookies, etc)\n */\n\n\tvar to = false;\n\t/**\n\t * stores all defaults for the state plugin\n\t * @name $.jstree.defaults.state\n\t * @plugin state\n\t */\n\t$.jstree.defaults.state = {\n\t\t/**\n\t\t * A string for the key to use when saving the current tree (change if using multiple trees in your project). Defaults to `jstree`.\n\t\t * @name $.jstree.defaults.state.key\n\t\t * @plugin state\n\t\t */\n\t\tkey\t\t: 'jstree',\n\t\t/**\n\t\t * A space separated list of events that trigger a state save. Defaults to `changed.jstree open_node.jstree close_node.jstree`.\n\t\t * @name $.jstree.defaults.state.events\n\t\t * @plugin state\n\t\t */\n\t\tevents\t: 'changed.jstree open_node.jstree close_node.jstree check_node.jstree uncheck_node.jstree',\n\t\t/**\n\t\t * Time in milliseconds after which the state will expire. Defaults to 'false' meaning - no expire.\n\t\t * @name $.jstree.defaults.state.ttl\n\t\t * @plugin state\n\t\t */\n\t\tttl\t\t: false,\n\t\t/**\n\t\t * A function that will be executed prior to restoring state with one argument - the state object. Can be used to clear unwanted parts of the state.\n\t\t * @name $.jstree.defaults.state.filter\n\t\t * @plugin state\n\t\t */\n\t\tfilter\t: false,\n\t\t/**\n\t\t * Should loaded nodes be restored (setting this to true means that it is possible that the whole tree will be loaded for some users - use with caution). Defaults to `false`\n\t\t * @name $.jstree.defaults.state.preserve_loaded\n\t\t * @plugin state\n\t\t */\n\t\tpreserve_loaded : false\n\t};\n\t$.jstree.plugins.state = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\t\t\tvar bind = function () {\n\t\t\t\tthis.element.on(this.settings.state.events, function () {\n\t\t\t\t\tif(to) { clearTimeout(to); }\n\t\t\t\t\tto = setTimeout(function () { this.save_state(); }.bind(this), 100);\n\t\t\t\t}.bind(this));\n\t\t\t\t/**\n\t\t\t\t * triggered when the state plugin is finished restoring the state (and immediately after ready if there is no state to restore).\n\t\t\t\t * @event\n\t\t\t\t * @name state_ready.jstree\n\t\t\t\t * @plugin state\n\t\t\t\t */\n\t\t\t\tthis.trigger('state_ready');\n\t\t\t}.bind(this);\n\t\t\tthis.element\n\t\t\t\t.on(\"ready.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.element.one(\"restore_state.jstree\", bind);\n\t\t\t\t\t\tif(!this.restore_state()) { bind(); }\n\t\t\t\t\t}.bind(this));\n\t\t};\n\t\t/**\n\t\t * save the state\n\t\t * @name save_state()\n\t\t * @plugin state\n\t\t */\n\t\tthis.save_state = function () {\n\t\t\tvar tm = this.get_state();\n\t\t\tif (!this.settings.state.preserve_loaded) {\n\t\t\t\tdelete tm.core.loaded;\n\t\t\t}\n\t\t\tvar st = { 'state' : tm, 'ttl' : this.settings.state.ttl, 'sec' : +(new Date()) };\n\t\t\t$.vakata.storage.set(this.settings.state.key, JSON.stringify(st));\n\t\t};\n\t\t/**\n\t\t * restore the state from the user's computer\n\t\t * @name restore_state()\n\t\t * @plugin state\n\t\t */\n\t\tthis.restore_state = function () {\n\t\t\tvar k = $.vakata.storage.get(this.settings.state.key);\n\t\t\tif(!!k) { try { k = JSON.parse(k); } catch(ex) { return false; } }\n\t\t\tif(!!k && k.ttl && k.sec && +(new Date()) - k.sec > k.ttl) { return false; }\n\t\t\tif(!!k && k.state) { k = k.state; }\n\t\t\tif(!!k && $.vakata.is_function(this.settings.state.filter)) { k = this.settings.state.filter.call(this, k); }\n\t\t\tif(!!k) {\n\t\t\t\tif (!this.settings.state.preserve_loaded) {\n\t\t\t\t\tdelete k.core.loaded;\n\t\t\t\t}\n\t\t\t\tthis.element.one(\"set_state.jstree\", function (e, data) { data.instance.trigger('restore_state', { 'state' : $.extend(true, {}, k) }); });\n\t\t\t\tthis.set_state(k);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n\t\t/**\n\t\t * clear the state on the user's computer\n\t\t * @name clear_state()\n\t\t * @plugin state\n\t\t */\n\t\tthis.clear_state = function () {\n\t\t\treturn $.vakata.storage.del(this.settings.state.key);\n\t\t};\n\t};\n\n\t(function ($, undefined) {\n\t\t$.vakata.storage = {\n\t\t\t// simply specifying the functions in FF throws an error\n\t\t\tset : function (key, val) { return window.localStorage.setItem(key, val); },\n\t\t\tget : function (key) { return window.localStorage.getItem(key); },\n\t\t\tdel : function (key) { return window.localStorage.removeItem(key); }\n\t\t};\n\t}($));\n\n\t// include the state plugin by default\n\t// $.jstree.defaults.plugins.push(\"state\");\n\n/**\n * ### Types plugin\n *\n * Makes it possible to add predefined types for groups of nodes, which make it possible to easily control nesting rules and icon for each group.\n */\n\n\t/**\n\t * An object storing all types as key value pairs, where the key is the type name and the value is an object that could contain following keys (all optional).\n\t *\n\t * * `max_children` the maximum number of immediate children this node type can have. Do not specify or set to `-1` for unlimited.\n\t * * `max_depth` the maximum number of nesting this node type can have. A value of `1` would mean that the node can have children, but no grandchildren. Do not specify or set to `-1` for unlimited.\n\t * * `valid_children` an array of node type strings, that nodes of this type can have as children. Do not specify or set to `-1` for no limits.\n\t * * `icon` a string - can be a path to an icon or a className, if using an image that is in the current directory use a `./` prefix, otherwise it will be detected as a class. Omit to use the default icon from your theme.\n\t * * `li_attr` an object of values which will be used to add HTML attributes on the resulting LI DOM node (merged with the node's own data)\n\t * * `a_attr` an object of values which will be used to add HTML attributes on the resulting A DOM node (merged with the node's own data)\n\t *\n\t * There are two predefined types:\n\t *\n\t * * `#` represents the root of the tree, for example `max_children` would control the maximum number of root nodes.\n\t * * `default` represents the default node - any settings here will be applied to all nodes that do not have a type specified.\n\t *\n\t * @name $.jstree.defaults.types\n\t * @plugin types\n\t */\n\t$.jstree.defaults.types = {\n\t\t'default' : {}\n\t};\n\t$.jstree.defaults.types[$.jstree.root] = {};\n\n\t$.jstree.plugins.types = function (options, parent) {\n\t\tthis.init = function (el, options) {\n\t\t\tvar i, j;\n\t\t\tif(options && options.types && options.types['default']) {\n\t\t\t\tfor(i in options.types) {\n\t\t\t\t\tif(i !== \"default\" && i !== $.jstree.root && options.types.hasOwnProperty(i)) {\n\t\t\t\t\t\tfor(j in options.types['default']) {\n\t\t\t\t\t\t\tif(options.types['default'].hasOwnProperty(j) && options.types[i][j] === undefined) {\n\t\t\t\t\t\t\t\toptions.types[i][j] = options.types['default'][j];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tparent.init.call(this, el, options);\n\t\t\tthis._model.data[$.jstree.root].type = $.jstree.root;\n\t\t};\n\t\tthis.refresh = function (skip_loading, forget_state) {\n\t\t\tparent.refresh.call(this, skip_loading, forget_state);\n\t\t\tthis._model.data[$.jstree.root].type = $.jstree.root;\n\t\t};\n\t\tthis.bind = function () {\n\t\t\tthis.element\n\t\t\t\t.on('model.jstree', function (e, data) {\n\t\t\t\t\t\tvar m = this._model.data,\n\t\t\t\t\t\t\tdpc = data.nodes,\n\t\t\t\t\t\t\tt = this.settings.types,\n\t\t\t\t\t\t\ti, j, c = 'default', k;\n\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\tc = 'default';\n\t\t\t\t\t\t\tif(m[dpc[i]].original && m[dpc[i]].original.type && t[m[dpc[i]].original.type]) {\n\t\t\t\t\t\t\t\tc = m[dpc[i]].original.type;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(m[dpc[i]].data && m[dpc[i]].data.jstree && m[dpc[i]].data.jstree.type && t[m[dpc[i]].data.jstree.type]) {\n\t\t\t\t\t\t\t\tc = m[dpc[i]].data.jstree.type;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tm[dpc[i]].type = c;\n\t\t\t\t\t\t\tif(m[dpc[i]].icon === true && t[c].icon !== undefined) {\n\t\t\t\t\t\t\t\tm[dpc[i]].icon = t[c].icon;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(t[c].li_attr !== undefined && typeof t[c].li_attr === 'object') {\n\t\t\t\t\t\t\t\tfor (k in t[c].li_attr) {\n\t\t\t\t\t\t\t\t\tif (t[c].li_attr.hasOwnProperty(k)) {\n\t\t\t\t\t\t\t\t\t\tif (k === 'id') {\n\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (m[dpc[i]].li_attr[k] === undefined) {\n\t\t\t\t\t\t\t\t\t\t\tm[dpc[i]].li_attr[k] = t[c].li_attr[k];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (k === 'class') {\n\t\t\t\t\t\t\t\t\t\t\tm[dpc[i]].li_attr['class'] = t[c].li_attr['class'] + ' ' + m[dpc[i]].li_attr['class'];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(t[c].a_attr !== undefined && typeof t[c].a_attr === 'object') {\n\t\t\t\t\t\t\t\tfor (k in t[c].a_attr) {\n\t\t\t\t\t\t\t\t\tif (t[c].a_attr.hasOwnProperty(k)) {\n\t\t\t\t\t\t\t\t\t\tif (k === 'id') {\n\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (m[dpc[i]].a_attr[k] === undefined) {\n\t\t\t\t\t\t\t\t\t\t\tm[dpc[i]].a_attr[k] = t[c].a_attr[k];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (k === 'href' && m[dpc[i]].a_attr[k] === '#') {\n\t\t\t\t\t\t\t\t\t\t\tm[dpc[i]].a_attr['href'] = t[c].a_attr['href'];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (k === 'class') {\n\t\t\t\t\t\t\t\t\t\t\tm[dpc[i]].a_attr['class'] = t[c].a_attr['class'] + ' ' + m[dpc[i]].a_attr['class'];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tm[$.jstree.root].type = $.jstree.root;\n\t\t\t\t\t}.bind(this));\n\t\t\tparent.bind.call(this);\n\t\t};\n\t\tthis.get_json = function (obj, options, flat) {\n\t\t\tvar i, j,\n\t\t\t\tm = this._model.data,\n\t\t\t\topt = options ? $.extend(true, {}, options, {no_id:false}) : {},\n\t\t\t\ttmp = parent.get_json.call(this, obj, opt, flat);\n\t\t\tif(tmp === false) { return false; }\n\t\t\tif($.vakata.is_array(tmp)) {\n\t\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\t\ttmp[i].type = (tmp[i].id || tmp[i].id === 0) && m[tmp[i].id] && m[tmp[i].id].type ? m[tmp[i].id].type : \"default\";\n\t\t\t\t\tif(options && options.no_id) {\n\t\t\t\t\t\tdelete tmp[i].id;\n\t\t\t\t\t\tif(tmp[i].li_attr && tmp[i].li_attr.id) {\n\t\t\t\t\t\t\tdelete tmp[i].li_attr.id;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(tmp[i].a_attr && tmp[i].a_attr.id) {\n\t\t\t\t\t\t\tdelete tmp[i].a_attr.id;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\ttmp.type = (tmp.id || tmp.id === 0) && m[tmp.id] && m[tmp.id].type ? m[tmp.id].type : \"default\";\n\t\t\t\tif(options && options.no_id) {\n\t\t\t\t\ttmp = this._delete_ids(tmp);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn tmp;\n\t\t};\n\t\tthis._delete_ids = function (tmp) {\n\t\t\tif($.vakata.is_array(tmp)) {\n\t\t\t\tfor(var i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\t\ttmp[i] = this._delete_ids(tmp[i]);\n\t\t\t\t}\n\t\t\t\treturn tmp;\n\t\t\t}\n\t\t\tdelete tmp.id;\n\t\t\tif(tmp.li_attr && tmp.li_attr.id) {\n\t\t\t\tdelete tmp.li_attr.id;\n\t\t\t}\n\t\t\tif(tmp.a_attr && tmp.a_attr.id) {\n\t\t\t\tdelete tmp.a_attr.id;\n\t\t\t}\n\t\t\tif(tmp.children && $.vakata.is_array(tmp.children)) {\n\t\t\t\ttmp.children = this._delete_ids(tmp.children);\n\t\t\t}\n\t\t\treturn tmp;\n\t\t};\n\t\tthis.check = function (chk, obj, par, pos, more) {\n\t\t\tif(parent.check.call(this, chk, obj, par, pos, more) === false) { return false; }\n\t\t\tobj = obj && (obj.id || obj.id === 0) ? obj : this.get_node(obj);\n\t\t\tpar = par && (par.id || par.id === 0) ? par : this.get_node(par);\n\t\t\tvar m = obj && (obj.id || obj.id === 0) ? (more && more.origin ? more.origin : $.jstree.reference(obj.id)) : null, tmp, d, i, j;\n\t\t\tm = m && m._model && m._model.data ? m._model.data : null;\n\t\t\tswitch(chk) {\n\t\t\t\tcase \"create_node\":\n\t\t\t\tcase \"move_node\":\n\t\t\t\tcase \"copy_node\":\n\t\t\t\t\tif(chk !== 'move_node' || $.inArray(obj.id, par.children) === -1) {\n\t\t\t\t\t\ttmp = this.get_rules(par);\n\t\t\t\t\t\tif(tmp.max_children !== undefined && tmp.max_children !== -1 && tmp.max_children === par.children.length) {\n\t\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'types', 'id' : 'types_01', 'reason' : 'max_children prevents function: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(tmp.valid_children !== undefined && tmp.valid_children !== -1 && $.inArray((obj.type || 'default'), tmp.valid_children) === -1) {\n\t\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'types', 'id' : 'types_02', 'reason' : 'valid_children prevents function: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(m && obj.children_d && obj.parents) {\n\t\t\t\t\t\t\td = 0;\n\t\t\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\td = Math.max(d, m[obj.children_d[i]].parents.length);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\td = d - obj.parents.length + 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(d <= 0 || d === undefined) { d = 1; }\n\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\tif(tmp.max_depth !== undefined && tmp.max_depth !== -1 && tmp.max_depth < d) {\n\t\t\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'types', 'id' : 'types_03', 'reason' : 'max_depth prevents function: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpar = this.get_node(par.parent);\n\t\t\t\t\t\t\ttmp = this.get_rules(par);\n\t\t\t\t\t\t\td++;\n\t\t\t\t\t\t} while(par);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\treturn true;\n\t\t};\n\t\t/**\n\t\t * used to retrieve the type settings object for a node\n\t\t * @name get_rules(obj)\n\t\t * @param {mixed} obj the node to find the rules for\n\t\t * @return {Object}\n\t\t * @plugin types\n\t\t */\n\t\tthis.get_rules = function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar tmp = this.get_type(obj, true);\n\t\t\tif(tmp.max_depth === undefined) { tmp.max_depth = -1; }\n\t\t\tif(tmp.max_children === undefined) { tmp.max_children = -1; }\n\t\t\tif(tmp.valid_children === undefined) { tmp.valid_children = -1; }\n\t\t\treturn tmp;\n\t\t};\n\t\t/**\n\t\t * used to retrieve the type string or settings object for a node\n\t\t * @name get_type(obj [, rules])\n\t\t * @param {mixed} obj the node to find the rules for\n\t\t * @param {Boolean} rules if set to `true` instead of a string the settings object will be returned\n\t\t * @return {String|Object}\n\t\t * @plugin types\n\t\t */\n\t\tthis.get_type = function (obj, rules) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn (!obj) ? false : ( rules ? $.extend({ 'type' : obj.type }, this.settings.types[obj.type]) : obj.type);\n\t\t};\n\t\t/**\n\t\t * used to change a node's type\n\t\t * @name set_type(obj, type)\n\t\t * @param {mixed} obj the node to change\n\t\t * @param {String} type the new type\n\t\t * @plugin types\n\t\t */\n\t\tthis.set_type = function (obj, type) {\n\t\t\tvar m = this._model.data, t, t1, t2, old_type, old_icon, k, d, a;\n\t\t\tif($.vakata.is_array(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.set_type(obj[t1], type);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tt = this.settings.types;\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!t[type] || !obj) { return false; }\n\t\t\td = this.get_node(obj, true);\n\t\t\tif (d && d.length) {\n\t\t\t\ta = d.children('.jstree-anchor');\n\t\t\t}\n\t\t\told_type = obj.type;\n\t\t\told_icon = this.get_icon(obj);\n\t\t\tobj.type = type;\n\t\t\tif(old_icon === true || !t[old_type] || (t[old_type].icon !== undefined && old_icon === t[old_type].icon)) {\n\t\t\t\tthis.set_icon(obj, t[type].icon !== undefined ? t[type].icon : true);\n\t\t\t}\n\n\t\t\t// remove old type props\n\t\t\tif(t[old_type] && t[old_type].li_attr !== undefined && typeof t[old_type].li_attr === 'object') {\n\t\t\t\tfor (k in t[old_type].li_attr) {\n\t\t\t\t\tif (t[old_type].li_attr.hasOwnProperty(k)) {\n\t\t\t\t\t\tif (k === 'id') {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (k === 'class') {\n\t\t\t\t\t\t\tm[obj.id].li_attr['class'] = (m[obj.id].li_attr['class'] || '').replace(t[old_type].li_attr[k], '');\n\t\t\t\t\t\t\tif (d) { d.removeClass(t[old_type].li_attr[k]); }\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (m[obj.id].li_attr[k] === t[old_type].li_attr[k]) {\n\t\t\t\t\t\t\tm[obj.id].li_attr[k] = null;\n\t\t\t\t\t\t\tif (d) { d.removeAttr(k); }\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(t[old_type] && t[old_type].a_attr !== undefined && typeof t[old_type].a_attr === 'object') {\n\t\t\t\tfor (k in t[old_type].a_attr) {\n\t\t\t\t\tif (t[old_type].a_attr.hasOwnProperty(k)) {\n\t\t\t\t\t\tif (k === 'id') {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (k === 'class') {\n\t\t\t\t\t\t\tm[obj.id].a_attr['class'] = (m[obj.id].a_attr['class'] || '').replace(t[old_type].a_attr[k], '');\n\t\t\t\t\t\t\tif (a) { a.removeClass(t[old_type].a_attr[k]); }\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (m[obj.id].a_attr[k] === t[old_type].a_attr[k]) {\n\t\t\t\t\t\t\tif (k === 'href') {\n\t\t\t\t\t\t\t\tm[obj.id].a_attr[k] = '#';\n\t\t\t\t\t\t\t\tif (a) { a.attr('href', '#'); }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tdelete m[obj.id].a_attr[k];\n\t\t\t\t\t\t\t\tif (a) { a.removeAttr(k); }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// add new props\n\t\t\tif(t[type].li_attr !== undefined && typeof t[type].li_attr === 'object') {\n\t\t\t\tfor (k in t[type].li_attr) {\n\t\t\t\t\tif (t[type].li_attr.hasOwnProperty(k)) {\n\t\t\t\t\t\tif (k === 'id') {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (m[obj.id].li_attr[k] === undefined) {\n\t\t\t\t\t\t\tm[obj.id].li_attr[k] = t[type].li_attr[k];\n\t\t\t\t\t\t\tif (d) {\n\t\t\t\t\t\t\t\tif (k === 'class') {\n\t\t\t\t\t\t\t\t\td.addClass(t[type].li_attr[k]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\td.attr(k, t[type].li_attr[k]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (k === 'class') {\n\t\t\t\t\t\t\tm[obj.id].li_attr['class'] = t[type].li_attr[k] + ' ' + m[obj.id].li_attr['class'];\n\t\t\t\t\t\t\tif (d) { d.addClass(t[type].li_attr[k]); }\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(t[type].a_attr !== undefined && typeof t[type].a_attr === 'object') {\n\t\t\t\tfor (k in t[type].a_attr) {\n\t\t\t\t\tif (t[type].a_attr.hasOwnProperty(k)) {\n\t\t\t\t\t\tif (k === 'id') {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (m[obj.id].a_attr[k] === undefined) {\n\t\t\t\t\t\t\tm[obj.id].a_attr[k] = t[type].a_attr[k];\n\t\t\t\t\t\t\tif (a) {\n\t\t\t\t\t\t\t\tif (k === 'class') {\n\t\t\t\t\t\t\t\t\ta.addClass(t[type].a_attr[k]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\ta.attr(k, t[type].a_attr[k]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (k === 'href' && m[obj.id].a_attr[k] === '#') {\n\t\t\t\t\t\t\tm[obj.id].a_attr['href'] = t[type].a_attr['href'];\n\t\t\t\t\t\t\tif (a) { a.attr('href', t[type].a_attr['href']); }\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (k === 'class') {\n\t\t\t\t\t\t\tm[obj.id].a_attr['class'] = t[type].a_attr['class'] + ' ' + m[obj.id].a_attr['class'];\n\t\t\t\t\t\t\tif (a) { a.addClass(t[type].a_attr[k]); }\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn true;\n\t\t};\n\t};\n\t// include the types plugin by default\n\t// $.jstree.defaults.plugins.push(\"types\");\n\n\n/**\n * ### Unique plugin\n *\n * Enforces that no nodes with the same name can coexist as siblings.\n */\n\n\t/**\n\t * stores all defaults for the unique plugin\n\t * @name $.jstree.defaults.unique\n\t * @plugin unique\n\t */\n\t$.jstree.defaults.unique = {\n\t\t/**\n\t\t * Indicates if the comparison should be case sensitive. Default is `false`.\n\t\t * @name $.jstree.defaults.unique.case_sensitive\n\t\t * @plugin unique\n\t\t */\n\t\tcase_sensitive : false,\n\t\t/**\n\t\t * Indicates if white space should be trimmed before the comparison. Default is `false`.\n\t\t * @name $.jstree.defaults.unique.trim_whitespace\n\t\t * @plugin unique\n\t\t */\n\t\ttrim_whitespace : false,\n\t\t/**\n\t\t * A callback executed in the instance's scope when a new node is created with no name and a node with the default name already exists, the two arguments are the conflicting name and the counter. The default will produce results like `New node (2)`.\n\t\t * @name $.jstree.defaults.unique.duplicate\n\t\t * @plugin unique\n\t\t */\n\t\tduplicate : function (name, counter) {\n\t\t\treturn name + ' (' + counter + ')';\n\t\t}\n\t};\n\n\t$.jstree.plugins.unique = function (options, parent) {\n\t\tthis.check = function (chk, obj, par, pos, more) {\n\t\t\tif(parent.check.call(this, chk, obj, par, pos, more) === false) { return false; }\n\t\t\tobj = obj && (obj.id || obj.id === 0) ? obj : this.get_node(obj);\n\t\t\tpar = par && (par.id || par.id === 0) ? par : this.get_node(par);\n\t\t\tif(!par || !par.children) { return true; }\n\t\t\tvar n = chk === \"rename_node\" ? pos : obj.text,\n\t\t\t\tc = [],\n\t\t\t\ts = this.settings.unique.case_sensitive,\n\t\t\t\tw = this.settings.unique.trim_whitespace,\n\t\t\t\tm = this._model.data, i, j, t;\n\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\tt = m[par.children[i]].text;\n\t\t\t\tif (!s) {\n\t\t\t\t\tt = t.toLowerCase();\n\t\t\t\t}\n\t\t\t\tif (w) {\n\t\t\t\t\tt = t.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\t\t\t\t}\n\t\t\t\tc.push(t);\n\t\t\t}\n\t\t\tif(!s) { n = n.toLowerCase(); }\n\t\t\tif (w) { n = n.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, ''); }\n\t\t\tswitch(chk) {\n\t\t\t\tcase \"delete_node\":\n\t\t\t\t\treturn true;\n\t\t\t\tcase \"rename_node\":\n\t\t\t\t\tt = obj.text || '';\n\t\t\t\t\tif (!s) {\n\t\t\t\t\t\tt = t.toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\tif (w) {\n\t\t\t\t\t\tt = t.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\t\t\t\t\t}\n\t\t\t\t\ti = ($.inArray(n, c) === -1 || (obj.text && t === n));\n\t\t\t\t\tif(!i) {\n\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'unique', 'id' : 'unique_01', 'reason' : 'Child with name ' + n + ' already exists. Preventing: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t}\n\t\t\t\t\treturn i;\n\t\t\t\tcase \"create_node\":\n\t\t\t\t\ti = ($.inArray(n, c) === -1);\n\t\t\t\t\tif(!i) {\n\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'unique', 'id' : 'unique_04', 'reason' : 'Child with name ' + n + ' already exists. Preventing: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t}\n\t\t\t\t\treturn i;\n\t\t\t\tcase \"copy_node\":\n\t\t\t\t\ti = ($.inArray(n, c) === -1);\n\t\t\t\t\tif(!i) {\n\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'unique', 'id' : 'unique_02', 'reason' : 'Child with name ' + n + ' already exists. Preventing: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t}\n\t\t\t\t\treturn i;\n\t\t\t\tcase \"move_node\":\n\t\t\t\t\ti = ( (obj.parent === par.id && (!more || !more.is_multi)) || $.inArray(n, c) === -1);\n\t\t\t\t\tif(!i) {\n\t\t\t\t\t\tthis._data.core.last_error = { 'error' : 'check', 'plugin' : 'unique', 'id' : 'unique_03', 'reason' : 'Child with name ' + n + ' already exists. Preventing: ' + chk, 'data' : JSON.stringify({ 'chk' : chk, 'pos' : pos, 'obj' : obj && (obj.id || obj.id === 0) ? obj.id : false, 'par' : par && (par.id || par.id === 0) ? par.id : false }) };\n\t\t\t\t\t}\n\t\t\t\t\treturn i;\n\t\t\t}\n\t\t\treturn true;\n\t\t};\n\t\tthis.create_node = function (par, node, pos, callback, is_loaded) {\n\t\t\tif(!node || (typeof node === 'object' && node.text === undefined)) {\n\t\t\t\tif(par === null) {\n\t\t\t\t\tpar = $.jstree.root;\n\t\t\t\t}\n\t\t\t\tpar = this.get_node(par);\n\t\t\t\tif(!par) {\n\t\t\t\t\treturn parent.create_node.call(this, par, node, pos, callback, is_loaded);\n\t\t\t\t}\n\t\t\t\tpos = pos === undefined ? \"last\" : pos;\n\t\t\t\tif(!pos.toString().match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\t\treturn parent.create_node.call(this, par, node, pos, callback, is_loaded);\n\t\t\t\t}\n\t\t\t\tif(!node) { node = {}; }\n\t\t\t\tvar tmp, n, dpc, i, j, m = this._model.data, s = this.settings.unique.case_sensitive, w = this.settings.unique.trim_whitespace, cb = this.settings.unique.duplicate, t;\n\t\t\t\tn = tmp = this.get_string('New node');\n\t\t\t\tdpc = [];\n\t\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\t\tt = m[par.children[i]].text;\n\t\t\t\t\tif (!s) {\n\t\t\t\t\t\tt = t.toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\tif (w) {\n\t\t\t\t\t\tt = t.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\t\t\t\t\t}\n\t\t\t\t\tdpc.push(t);\n\t\t\t\t}\n\t\t\t\ti = 1;\n\t\t\t\tt = n;\n\t\t\t\tif (!s) {\n\t\t\t\t\tt = t.toLowerCase();\n\t\t\t\t}\n\t\t\t\tif (w) {\n\t\t\t\t\tt = t.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\t\t\t\t}\n\t\t\t\twhile($.inArray(t, dpc) !== -1) {\n\t\t\t\t\tn = cb.call(this, tmp, (++i)).toString();\n\t\t\t\t\tt = n;\n\t\t\t\t\tif (!s) {\n\t\t\t\t\t\tt = t.toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\tif (w) {\n\t\t\t\t\t\tt = t.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tnode.text = n;\n\t\t\t}\n\t\t\treturn parent.create_node.call(this, par, node, pos, callback, is_loaded);\n\t\t};\n\t};\n\n\t// include the unique plugin by default\n\t// $.jstree.defaults.plugins.push(\"unique\");\n\n\n/**\n * ### Wholerow plugin\n *\n * Makes each node appear block level. Making selection easier. May cause slow down for large trees in old browsers.\n */\n\n\tvar div = document.createElement('DIV');\n\tdiv.setAttribute('unselectable','on');\n\tdiv.setAttribute('role','presentation');\n\tdiv.className = 'jstree-wholerow';\n\tdiv.innerHTML = '&#160;';\n\t$.jstree.plugins.wholerow = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis.element\n\t\t\t\t.on('ready.jstree set_state.jstree', function () {\n\t\t\t\t\t\tthis.hide_dots();\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"init.jstree loading.jstree ready.jstree\", function () {\n\t\t\t\t\t\t//div.style.height = this._data.core.li_height + 'px';\n\t\t\t\t\t\tthis.get_container_ul().addClass('jstree-wholerow-ul');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"deselect_all.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.element.find('.jstree-wholerow-clicked').removeClass('jstree-wholerow-clicked');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"changed.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.element.find('.jstree-wholerow-clicked').removeClass('jstree-wholerow-clicked');\n\t\t\t\t\t\tvar tmp = false, i, j;\n\t\t\t\t\t\tfor(i = 0, j = data.selected.length; i < j; i++) {\n\t\t\t\t\t\t\ttmp = this.get_node(data.selected[i], true);\n\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\ttmp.children('.jstree-wholerow').addClass('jstree-wholerow-clicked');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"open_node.jstree\", function (e, data) {\n\t\t\t\t\t\tthis.get_node(data.node, true).find('.jstree-clicked').parent().children('.jstree-wholerow').addClass('jstree-wholerow-clicked');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"hover_node.jstree dehover_node.jstree\", function (e, data) {\n\t\t\t\t\t\tif(e.type === \"hover_node\" && this.is_disabled(data.node)) { return; }\n\t\t\t\t\t\tthis.get_node(data.node, true).children('.jstree-wholerow')[e.type === \"hover_node\"?\"addClass\":\"removeClass\"]('jstree-wholerow-hovered');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"contextmenu.jstree\", \".jstree-wholerow\", function (e) {\n\t\t\t\t\t\tif (this._data.contextmenu) {\n\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\tvar tmp = $.Event('contextmenu', { metaKey : e.metaKey, ctrlKey : e.ctrlKey, altKey : e.altKey, shiftKey : e.shiftKey, pageX : e.pageX, pageY : e.pageY });\n\t\t\t\t\t\t\t$(e.currentTarget).closest(\".jstree-node\").children(\".jstree-anchor\").first().trigger(tmp);\n\t\t\t\t\t\t}\n\t\t\t\t\t}.bind(this))\n\t\t\t\t/*!\n\t\t\t\t.on(\"mousedown.jstree touchstart.jstree\", \".jstree-wholerow\", function (e) {\n\t\t\t\t\t\tif(e.target === e.currentTarget) {\n\t\t\t\t\t\t\tvar a = $(e.currentTarget).closest(\".jstree-node\").children(\".jstree-anchor\");\n\t\t\t\t\t\t\te.target = a[0];\n\t\t\t\t\t\t\ta.trigger(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t*/\n\t\t\t\t.on(\"click.jstree\", \".jstree-wholerow\", function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\tvar tmp = $.Event('click', { metaKey : e.metaKey, ctrlKey : e.ctrlKey, altKey : e.altKey, shiftKey : e.shiftKey });\n\t\t\t\t\t\t$(e.currentTarget).closest(\".jstree-node\").children(\".jstree-anchor\").first().trigger(tmp).trigger('focus');\n\t\t\t\t\t})\n\t\t\t\t.on(\"dblclick.jstree\", \".jstree-wholerow\", function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\tvar tmp = $.Event('dblclick', { metaKey : e.metaKey, ctrlKey : e.ctrlKey, altKey : e.altKey, shiftKey : e.shiftKey });\n\t\t\t\t\t\t$(e.currentTarget).closest(\".jstree-node\").children(\".jstree-anchor\").first().trigger(tmp).trigger('focus');\n\t\t\t\t\t})\n\t\t\t\t.on(\"click.jstree\", \".jstree-leaf > .jstree-ocl\", function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\tvar tmp = $.Event('click', { metaKey : e.metaKey, ctrlKey : e.ctrlKey, altKey : e.altKey, shiftKey : e.shiftKey });\n\t\t\t\t\t\t$(e.currentTarget).closest(\".jstree-node\").children(\".jstree-anchor\").first().trigger(tmp).trigger('focus');\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"mouseover.jstree\", \".jstree-wholerow, .jstree-icon\", function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\tif(!this.is_disabled(e.currentTarget)) {\n\t\t\t\t\t\t\tthis.hover_node(e.currentTarget);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}.bind(this))\n\t\t\t\t.on(\"mouseleave.jstree\", \".jstree-node\", function (e) {\n\t\t\t\t\t\tthis.dehover_node(e.currentTarget);\n\t\t\t\t\t}.bind(this));\n\t\t};\n\t\tthis.teardown = function () {\n\t\t\tif(this.settings.wholerow) {\n\t\t\t\tthis.element.find(\".jstree-wholerow\").remove();\n\t\t\t}\n\t\t\tparent.teardown.call(this);\n\t\t};\n\t\tthis.redraw_node = function(obj, deep, callback, force_render) {\n\t\t\tobj = parent.redraw_node.apply(this, arguments);\n\t\t\tif(obj) {\n\t\t\t\tvar tmp = div.cloneNode(true);\n\t\t\t\t//tmp.style.height = this._data.core.li_height + 'px';\n\t\t\t\tif($.inArray(obj.id, this._data.core.selected) !== -1) { tmp.className += ' jstree-wholerow-clicked'; }\n\t\t\t\tif(this._data.core.focused && this._data.core.focused === obj.id) { tmp.className += ' jstree-wholerow-hovered'; }\n\t\t\t\tobj.insertBefore(tmp, obj.childNodes[0]);\n\t\t\t}\n\t\t\treturn obj;\n\t\t};\n\t};\n\t// include the wholerow plugin by default\n\t// $.jstree.defaults.plugins.push(\"wholerow\");\n\tif(window.customElements && Object && Object.create) {\n\t\tvar proto = Object.create(HTMLElement.prototype);\n\t\tproto.createdCallback = function () {\n\t\t\tvar c = { core : {}, plugins : [] }, i;\n\t\t\tfor(i in $.jstree.plugins) {\n\t\t\t\tif($.jstree.plugins.hasOwnProperty(i) && this.attributes[i]) {\n\t\t\t\t\tc.plugins.push(i);\n\t\t\t\t\tif(this.getAttribute(i) && JSON.parse(this.getAttribute(i))) {\n\t\t\t\t\t\tc[i] = JSON.parse(this.getAttribute(i));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tfor(i in $.jstree.defaults.core) {\n\t\t\t\tif($.jstree.defaults.core.hasOwnProperty(i) && this.attributes[i]) {\n\t\t\t\t\tc.core[i] = JSON.parse(this.getAttribute(i)) || this.getAttribute(i);\n\t\t\t\t}\n\t\t\t}\n\t\t\t$(this).jstree(c);\n\t\t};\n\t\t// proto.attributeChangedCallback = function (name, previous, value) { };\n\t\ttry {\n\t\t\twindow.customElements.define(\"vakata-jstree\", function() {}, { prototype: proto });\n\t\t} catch (ignore) { }\n\t}\n\n}));\n\n//# sourceURL=webpack://Materialize/./node_modules/jstree/dist/jstree.js?")},jquery:function(t){"use strict";t.exports=__WEBPACK_EXTERNAL_MODULE_jquery__}},__webpack_module_cache__={};function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var n=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return __webpack_require__.d(e,{a:e}),e},__webpack_require__.d=function(t,e){for(var n in e)__webpack_require__.o(e,n)&&!__webpack_require__.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},__webpack_require__.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},__webpack_require__.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/jstree/jstree.js");return __webpack_exports__}()}));