/* Avatar
******************************************************************************* */

/* Avatar Styles */
.avatar {
  --#{$prefix}avatar-size: #{$avatar-size};
  --#{$prefix}avatar-group-border: #{$avatar-group-border};
  --#{$prefix}avatar-initial-inline: 3px;
  --#{$prefix}avatar-initial-bg: #{$avatar-initial-bg};
  position: relative;
  block-size: var(--#{$prefix}avatar-size);
  cursor: pointer;
  inline-size: var(--#{$prefix}avatar-size);

  .avatar-initial {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--#{$prefix}avatar-initial-bg);
    color: var(--#{$prefix}white);
    font-size: var(--#{$prefix}avatar-initial);
    font-weight: $font-weight-medium;
    inset: 0;
    text-transform: uppercase;
  }

  &.avatar-online,
  &.avatar-offline,
  &.avatar-away,
  &.avatar-busy {
    &::after {
      position: absolute;
      @include border-radius(100%);
      block-size: calc(var(--#{$prefix}avatar-size) * .2);
      box-shadow: 0 0 0 2px var(--#{$prefix}white);
      content: "";
      inline-size: calc(var(--#{$prefix}avatar-size) * .2);
      inset-block-end: 0;
      inset-inline-end: var(--#{$prefix}avatar-initial-inline);
    }
  }
  img {
    block-size: 100%;
    inline-size: 100%;
  }

  &.avatar-online::after {
    background-color: var(--#{$prefix}success);
  }

  &.avatar-offline::after {
    background-color: var(--#{$prefix}secondary);
  }

  &.avatar-away::after {
    background-color: var(--#{$prefix}warning);
  }

  &.avatar-busy::after {
    background-color: var(--#{$prefix}danger);
  }
}

/* Pull up avatar style */
.pull-up {
  transition: all .25s ease;

  &:hover {
    z-index: 30;
    @include border-radius(50%);
    box-shadow: var(--#{$prefix}box-shadow);
    transform: translateY(-4px) scale(1.02);
  }
}


@each $size, $values in $avatar-sizes {
  .avatar-#{$size} {
    --#{$prefix}avatar-size: #{nth($values, 1)};
    --#{$prefix}avatar-initial: #{nth($values, 2)};
    --#{$prefix}avatar-initial-inline: #{nth($values, 3)};
  }
}

// Avatar group z-index
$avatar-group-count: 8;
$avatar-group-zindex: $avatar-group-count;
@for $i from 1 to $avatar-group-count {
  .avatar-group {
    .avatar:nth-child(#{$i}) {
      z-index: $avatar-group-zindex;
    }
  }
  $avatar-group-zindex: $avatar-group-zindex - 1;
}

/* Avatar Group SCSS */
.avatar-group {
  .avatar {
    margin-inline-start: -.65rem;
    transition: all .25s ease;

    &:first-child {
      margin-inline-start: 0;
    }

    img,
    .avatar-initial {
      border: 2px solid var(--#{$prefix}avatar-group-border);
      color: var(--#{$prefix}heading-color);
    }

    &:hover {
      z-index: 30;
      transition: all .25s ease;
    }
  }

  // Avatar Group Sizings
  .avatar-xs {
    margin-inline-start: -.5rem;
  }

  .avatar-sm {
    margin-inline-start: -.6rem;
  }

  .avatar-md {
    margin-inline-start: -.8rem;
  }

  .avatar-lg {
    margin-inline-start: -1rem;
  }

  .avatar-xl {
    margin-inline-start: -1.1rem;
  }
}

/* Avatar dark css */
@if $enable-dark-mode {
  @include color-mode(dark) {
    /* Avatar Status indication */
    .avatar {
      --#{$prefix}avatar-initial-bg: #3f3b59;
      &.avatar-online,
      &.avatar-offline,
      &.avatar-away,
      &.avatar-busy {
        &::after {
          box-shadow: 0 0 0 2px var(--#{$prefix}body-bg);
        }
      }
    }
  }
}
