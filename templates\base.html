{% load static %}
<!doctype html>
<html
  lang="fr"
  class="layout-navbar-fixed layout-menu-fixed layout-compact"
  dir="ltr"
  data-skin="default"
  data-bs-theme="light"
  data-assets-path="{% static '' %}"
  data-template="vertical-menu-template-starter">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <title>{% block title %}Gestion Hospitalière{% endblock %} - Hospital SaaS</title>
    <meta name="description" content="{% block description %}Système de gestion hospitalière multi-organisations{% endblock %}" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicon/favicon.ico' %}" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"
      rel="stylesheet" />

    <link rel="stylesheet" href="{% static 'vendor/fonts/iconify-icons.css' %}" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="{% static 'vendor/libs/node-waves/node-waves.css' %}" />
    <link rel="stylesheet" href="{% static 'vendor/libs/pickr/pickr-themes.css' %}" />
    <link rel="stylesheet" href="{% static 'vendor/css/core.css' %}" />
    <link rel="stylesheet" href="{% static 'css/demo.css' %}" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="{% static 'vendor/libs/perfect-scrollbar/perfect-scrollbar.css' %}" />

    <!-- Page CSS -->
    {% block extra_css %}{% endblock %}

    <!-- Helpers -->
    <script src="{% static 'js/helpers.js' %}"></script>
    <script src="{% static 'js/config.js' %}"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        
        <!-- Menu -->
        <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
          <div class="app-brand demo">
            <a href="{% url 'organisations:liste' %}" class="app-brand-link">
              <span class="app-brand-logo demo">
                <i class="ti ti-building-hospital ti-lg text-primary"></i>
              </span>
              <span class="app-brand-text demo menu-text fw-bold ms-2">Hospital SaaS</span>
            </a>

            <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
              <i class="ti menu-toggle-icon d-none d-xl-block align-middle"></i>
              <i class="ti ti-x d-block d-xl-none ti-md align-middle"></i>
            </a>
          </div>

          <div class="menu-inner-shadow"></div>

          <ul class="menu-inner py-1">
            <!-- Tableau de bord -->
            <li class="menu-item">
              <a href="{% url 'organisations:liste' %}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-dashboard"></i>
                <div data-i18n="Tableau de bord">Tableau de bord</div>
              </a>
            </li>

            <!-- Organisations -->
            <li class="menu-header small text-uppercase">
              <span class="menu-header-text">Gestion</span>
            </li>
            <li class="menu-item {% if request.resolver_match.namespace == 'organisations' %}active open{% endif %}">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon tf-icons ti ti-building"></i>
                <div data-i18n="Organisations">Organisations</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item {% if request.resolver_match.url_name == 'liste' %}active{% endif %}">
                  <a href="{% url 'organisations:liste' %}" class="menu-link">
                    <div data-i18n="Liste">Liste</div>
                  </a>
                </li>
                <li class="menu-item {% if request.resolver_match.url_name == 'creer' %}active{% endif %}">
                  <a href="{% url 'organisations:creer' %}" class="menu-link">
                    <div data-i18n="Créer">Créer</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Futurs modules -->
            <li class="menu-header small text-uppercase">
              <span class="menu-header-text">Modules (À venir)</span>
            </li>
            <li class="menu-item">
              <a href="#" class="menu-link">
                <i class="menu-icon tf-icons ti ti-users"></i>
                <div data-i18n="Patients">Patients</div>
              </a>
            </li>
            <li class="menu-item">
              <a href="#" class="menu-link">
                <i class="menu-icon tf-icons ti ti-calendar"></i>
                <div data-i18n="Rendez-vous">Rendez-vous</div>
              </a>
            </li>
          </ul>
        </aside>
        <!-- / Menu -->

        <!-- Layout container -->
        <div class="layout-page">
          <!-- Navbar -->
          <nav
            class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
            id="layout-navbar">
            <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
              <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
                <i class="ti ti-menu-2 ti-md"></i>
              </a>
            </div>

            <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
              <!-- Search -->
              <div class="navbar-nav align-items-center">
                <div class="nav-item navbar-search-wrapper mb-0">
                  <a class="nav-item nav-link search-toggler d-flex align-items-center px-0" href="javascript:void(0);">
                    <i class="ti ti-search ti-md me-2 me-lg-4 ti-lg"></i>
                    <span class="d-none d-md-inline-block text-muted fw-normal">Rechercher...</span>
                  </a>
                </div>
              </div>
              <!-- /Search -->

              <ul class="navbar-nav flex-row align-items-center ms-auto">
                <!-- User -->
                <li class="nav-item navbar-dropdown dropdown-user dropdown">
                  <a
                    class="nav-link dropdown-toggle hide-arrow p-0"
                    href="javascript:void(0);"
                    data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                      <img src="{% static 'img/avatars/1.png' %}" alt class="rounded-circle" />
                    </div>
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                      <a class="dropdown-item mt-0" href="#">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0 me-2">
                            <div class="avatar avatar-online">
                              <img src="{% static 'img/avatars/1.png' %}" alt class="rounded-circle" />
                            </div>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-0">
                              {% if user.is_authenticated %}
                                {{ user.get_full_name|default:user.username }}
                              {% else %}
                                Utilisateur
                              {% endif %}
                            </h6>
                            <small class="text-muted">Administrateur</small>
                          </div>
                        </div>
                      </a>
                    </li>
                    <li>
                      <div class="dropdown-divider my-1 mx-n2"></div>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#">
                        <i class="ti ti-user me-3 ti-md"></i><span class="align-middle">Mon Profil</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#">
                        <i class="ti ti-settings me-3 ti-md"></i><span class="align-middle">Paramètres</span>
                      </a>
                    </li>
                    <li>
                      <div class="dropdown-divider my-1 mx-n2"></div>
                    </li>
                    <li>
                      <a class="dropdown-item" href="{% url 'admin:logout' %}">
                        <i class="ti ti-logout me-3 ti-md"></i><span class="align-middle">Déconnexion</span>
                      </a>
                    </li>
                  </ul>
                </li>
                <!--/ User -->
              </ul>
            </div>
          </nav>
          <!-- / Navbar -->

          <!-- Content wrapper -->
          <div class="content-wrapper">
            <!-- Content -->
            <div class="container-xxl flex-grow-1 container-p-y">
              
              <!-- Messages Django -->
              {% if messages %}
                {% for message in messages %}
                  <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                {% endfor %}
              {% endif %}

              <!-- Breadcrumb -->
              {% block breadcrumb %}{% endblock %}

              <!-- Page Content -->
              {% block content %}{% endblock %}
            </div>
            <!-- / Content -->

            <!-- Footer -->
            <footer class="content-footer footer bg-footer-theme">
              <div class="container-xxl">
                <div
                  class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                  <div class="text-body">
                    © 2025 Hospital SaaS. Tous droits réservés.
                  </div>
                  <div class="d-none d-lg-inline-block">
                    <a href="#" class="footer-link me-4">Support</a>
                    <a href="#" class="footer-link">Documentation</a>
                  </div>
                </div>
              </div>
            </footer>
            <!-- / Footer -->

            <div class="content-backdrop fade"></div>
          </div>
          <!-- Content wrapper -->
        </div>
        <!-- / Layout page -->
      </div>

      <!-- Overlay -->
      <div class="layout-overlay layout-menu-toggle"></div>

      <!-- Drag Target Area To SlideIn Menu On Small Screens -->
      <div class="drag-target"></div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <script src="{% static 'vendor/libs/jquery/jquery.js' %}"></script>
    <script src="{% static 'vendor/libs/popper/popper.js' %}"></script>
    <script src="{% static 'js/bootstrap.js' %}"></script>
    <script src="{% static 'vendor/libs/node-waves/node-waves.js' %}"></script>
    <script src="{% static 'vendor/libs/perfect-scrollbar/perfect-scrollbar.js' %}"></script>
    <script src="{% static 'vendor/libs/hammer/hammer.js' %}"></script>
    <script src="{% static 'js/menu.js' %}"></script>

    <!-- Main JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Page JS -->
    {% block extra_js %}{% endblock %}
  </body>
</html>
