from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Organisation, MembreOrganisation
from .forms import OrganisationForm, RechercheOrganisationForm


@login_required
def liste_organisations(request):
    """Vue pour afficher la liste des organisations avec recherche et pagination"""

    # Formulaire de recherche
    form_recherche = RechercheOrganisationForm(request.GET or None)

    # Récupération de toutes les organisations
    organisations = Organisation.objects.all().order_by('-date_creation')

    # Application des filtres de recherche
    if form_recherche.is_valid():
        nom = form_recherche.cleaned_data.get('nom')
        type_abonnement = form_recherche.cleaned_data.get('type_abonnement')
        actif = form_recherche.cleaned_data.get('actif')

        if nom:
            organisations = organisations.filter(
                Q(nom__icontains=nom) | Q(email__icontains=nom)
            )

        if type_abonnement:
            organisations = organisations.filter(type_abonnement=type_abonnement)

        if actif:
            organisations = organisations.filter(actif=actif == 'True')

    # Pagination
    paginator = Paginator(organisations, 10)  # 10 organisations par page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'form_recherche': form_recherche,
        'total_organisations': organisations.count(),
    }

    return render(request, 'organisations/liste.html', context)


@login_required
def detail_organisation(request, pk):
    """Vue pour afficher les détails d'une organisation"""

    organisation = get_object_or_404(Organisation, pk=pk)

    # Récupération des membres de l'organisation
    membres = MembreOrganisation.objects.filter(
        organisation=organisation,
        actif=True
    ).select_related('utilisateur').order_by('role', 'utilisateur__last_name')

    context = {
        'organisation': organisation,
        'membres': membres,
    }

    return render(request, 'organisations/detail.html', context)


@login_required
def creer_organisation(request):
    """Vue pour créer une nouvelle organisation"""

    if request.method == 'POST':
        form = OrganisationForm(request.POST)
        if form.is_valid():
            organisation = form.save()

            # Ajouter l'utilisateur actuel comme administrateur de l'organisation
            MembreOrganisation.objects.create(
                utilisateur=request.user,
                organisation=organisation,
                role=MembreOrganisation.RoleMembre.ADMIN
            )

            messages.success(
                request,
                f"L'organisation '{organisation.nom}' a été créée avec succès."
            )
            return redirect('organisations:detail', pk=organisation.pk)
    else:
        form = OrganisationForm()

    context = {
        'form': form,
        'titre': 'Créer une organisation',
        'action': 'Créer'
    }

    return render(request, 'organisations/form.html', context)


@login_required
def modifier_organisation(request, pk):
    """Vue pour modifier une organisation existante"""

    organisation = get_object_or_404(Organisation, pk=pk)

    if request.method == 'POST':
        form = OrganisationForm(request.POST, instance=organisation)
        if form.is_valid():
            organisation = form.save()
            messages.success(
                request,
                f"L'organisation '{organisation.nom}' a été modifiée avec succès."
            )
            return redirect('organisations:detail', pk=organisation.pk)
    else:
        form = OrganisationForm(instance=organisation)

    context = {
        'form': form,
        'organisation': organisation,
        'titre': f'Modifier {organisation.nom}',
        'action': 'Modifier'
    }

    return render(request, 'organisations/form.html', context)


@login_required
def supprimer_organisation(request, pk):
    """Vue pour supprimer une organisation"""

    organisation = get_object_or_404(Organisation, pk=pk)

    if request.method == 'POST':
        nom_organisation = organisation.nom
        organisation.delete()
        messages.success(
            request,
            f"L'organisation '{nom_organisation}' a été supprimée avec succès."
        )
        return redirect('organisations:liste')

    context = {
        'organisation': organisation,
    }

    return render(request, 'organisations/supprimer.html', context)
