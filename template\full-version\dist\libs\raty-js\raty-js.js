!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t=e();for(var s in t)("object"==typeof exports?exports:n)[s]=t[s]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./libs/raty-js/raty-js.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Raty: function() { return /* reexport safe */ raty_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }\n/* harmony export */ });\n/* harmony import */ var raty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! raty-js */ "./node_modules/raty-js/src/raty.js");\n\ntry {\n  window.Raty = raty_js__WEBPACK_IMPORTED_MODULE_0__["default"];\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/raty-js/raty-js.js?')},"./node_modules/raty-js/src/raty.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/*!\n * Raty - A Star Rating Plugin\n *\n * author: Washington Botelho\n * github: wbotelhos/raty\n * version: 4.3.0\n */\nclass Raty {\n  /**\n   *\n   * @param {object} element\n   * @param {object} options\n   *\n   */\n  constructor(element, options = {}) {\n    this.element = element;\n    this.opt = { ...this.defaultOptions(), ...options, ...this._parseOptions(element.dataset) };\n  }\n\n  defaultOptions() {\n    return {\n      cancelButton: false,\n      cancelClass: 'raty-cancel',\n      cancelHint: 'Cancel this rating!',\n      cancelOff: 'cancel-off.png',\n      cancelOn: 'cancel-on.png',\n      cancelPlace: 'left',\n      click: undefined,\n      half: false,\n      halfShow: true,\n      hints: ['bad', 'poor', 'regular', 'good', 'gorgeous'],\n      iconRange: undefined,\n      iconRangeSame: false,\n      mouseout: undefined,\n      mouseover: undefined,\n      noRatedMsg: 'Not rated yet!',\n      number: 5,\n      numberMax: 20,\n      path: undefined,\n      precision: false,\n      readOnly: false,\n      round: { down: 0.25, full: 0.6, up: 0.76 },\n      score: undefined,\n      scoreName: 'score',\n      single: false,\n      space: true,\n      starHalf: 'star-half.png',\n      starOff: 'star-off.png',\n      starOn: 'star-on.png',\n      starType: 'img',\n      target: undefined,\n      targetFormat: '{score}',\n      targetKeep: false,\n      targetScore: undefined,\n      targetText: '',\n      targetType: 'hint',\n    };\n  }\n\n  cancel(click) {\n    if (!this._isReadOnly()) {\n      this[click ? 'click' : 'score'](null);\n      this.scoreField.removeAttribute('value');\n    }\n  }\n\n  // TODO: model spec\n  click(score) {\n    if (!this._isReadOnly()) {\n      score = this._adjustedScore(score);\n\n      this._apply(score);\n\n      if (this.opt.click) {\n        this.opt.click.call(this, score, this.element);\n      }\n\n      this._target(score);\n    }\n  }\n\n  // TODO: model spec\n  move(score) {\n    var integer = parseInt(score, 10);\n    var decimal = this._getDecimal(score, 1);\n\n    if (integer >= this.opt.number) {\n      integer = this.opt.number - 1;\n      decimal = 10;\n    }\n\n    var width = this._getWidth();\n    var steps = width / 10;\n    var star = this.stars[integer];\n    var percent = star.offsetLeft + steps * decimal;\n    var evt = new Event('mousemove');\n    evt.pageX = percent;\n    this.isMove = true;\n    star.dispatchEvent(evt);\n    this.isMove = false;\n  }\n\n  // TODO: model spec\n  readOnly(readonly) {\n    if (this._isReadOnly() !== readonly) {\n      if (readonly) {\n        this._lock();\n      } else {\n        this._binds();\n        this._unlock();\n      }\n      this.element.dataset.readOnly = readonly;\n    }\n  }\n\n  score() {\n    return arguments.length ? this.setScore(arguments[0]) : this.getScore();\n  }\n\n  setScore(score) {\n    if (!this._isReadOnly()) {\n      score = this._adjustedScore(score);\n\n      this._apply(score);\n      this._target(score);\n    }\n  }\n  // TODO: model spec\n  getScore() {\n    var score = [];\n    var value;\n\n    value = this.scoreField.value;\n\n    score.push(value ? +value : undefined);\n\n    return score.length > 1 ? score : score[0];\n  }\n\n  init() {\n    this._executeCallbacks();\n    this._adjustNumber();\n    this._adjustHints();\n\n    this.opt.score = this._adjustedScore(this.opt.score);\n\n    if (this.opt.starType !== 'img') {\n      this._adjustStarName();\n    }\n\n    this._setPath();\n    this._createStars();\n\n    if (this.opt.cancelButton) {\n      this._createCancel();\n    }\n\n    if (this.opt.precision) {\n      this._adjustPrecision();\n    }\n\n    this._createScore();\n\n    this._apply(this.opt.score);\n    this._setTitle(this.opt.score);\n    this._target(this.opt.score);\n\n    if (this.opt.readOnly) {\n      this._lock();\n    } else {\n      this.element.style.cursor = 'pointer';\n      this._binds();\n    }\n\n    return this;\n  }\n\n  // private\n\n  // TODO: model spec\n  _adjustedScore(score) {\n    if (score || score === 0) {\n      return this._between(score, 0, this.opt.number);\n    }\n  }\n\n  _adjustHints() {\n    // TODO: is it possible `hints` does not exist?\n    if (!this.opt.hints) {\n      this.opt.hints = [];\n    }\n\n    if (!this.opt.halfShow && !this.opt.half) {\n      return;\n    }\n\n    var steps = this.opt.precision ? 10 : 2;\n\n    for (let i = 0; i < this.opt.number; i++) {\n      var group = this.opt.hints[i];\n\n      if (Object.prototype.toString.call(group) !== '[object Array]') {\n        group = [group];\n      }\n\n      this.opt.hints[i] = [];\n\n      for (let j = 0; j < steps; j++) {\n        var hint = group[j];\n        var last = group[group.length - 1];\n\n        if (last === undefined) {\n          last = null;\n        }\n\n        this.opt.hints[i][j] = hint === undefined ? last : hint;\n      }\n    }\n  }\n\n  _adjustNumber() {\n    this.opt.number = this._between(this.opt.number, 1, this.opt.numberMax);\n  }\n\n  _adjustPrecision() {\n    this.opt.half = true;\n  }\n\n  _adjustStarName() {\n    const replaces = ['cancelOff', 'cancelOn', 'starHalf', 'starOff', 'starOn'];\n\n    this.opt.path = '';\n\n    for (let i = 0; i < replaces.length; i++) {\n      this.opt[replaces[i]] = this.opt[replaces[i]].replace('.', '-');\n    }\n  }\n\n  // TODO: model spec\n  _apply(score) {\n    this._fill(score);\n\n    if (score) {\n      if (score > 0) {\n        this.scoreField.value = score;\n      }\n\n      this._roundStars(score);\n    }\n  }\n\n  _attributesForIndex(i) {\n    var name = this._nameForIndex(i);\n    var attributes = { alt: i, src: this.opt.path + this.opt[name] };\n\n    if (this.opt.starType !== 'img') {\n      attributes = { 'data-alt': i, 'class': this.opt[name] };\n    }\n\n    attributes.title = this._getHint(i);\n\n    return attributes;\n  }\n\n  _between(value, min, max) {\n    return Math.min(Math.max(parseFloat(value), min), max);\n  }\n\n  // TODO: model spec\n  _binds() {\n    if (this.cancelButton) {\n      this._bindOverCancel();\n      this._bindClickCancel();\n      this._bindOutCancel();\n    }\n    this._bindOver();\n    this._bindClick();\n    this._bindOut();\n  }\n\n  // TODO: model spec\n  _bindClick() {\n    this.stars.forEach((value) => {\n      value.addEventListener('click', (evt) => {\n        if (this._isReadOnly()) {\n          return;\n        }\n\n        let execute;\n        let score = this.opt.half || this.opt.precision ? this.element.dataset.score : value.alt || value.dataset.alt;\n\n        if (this.opt.half && !this.opt.precision) {\n          score = this._roundHalfScore(score);\n        }\n\n        if (this.opt.click) {\n          execute = this.opt.click.call(this, +score, this.element, evt);\n        }\n\n        if (execute || execute === undefined) {\n          this._apply(+score);\n        }\n      });\n    });\n  }\n\n  // TODO: model spec\n  _bindClickCancel() {\n    this.cancelButton.addEventListener('click', (evt) => {\n      this.scoreField.removeAttribute('value');\n\n      if (this.opt.click) {\n        this.opt.click.call(this, null, this.element, evt);\n      }\n    });\n  }\n\n  // TODO: model spec\n  _bindOut() {\n    this.element.addEventListener('mouseleave', (evt) => {\n      const score = +this.scoreField.value || undefined;\n\n      this._apply(score);\n      this._target(score, evt);\n      this._resetTitle();\n\n      if (this.opt.mouseout) {\n        this.opt.mouseout.call(this, score, this.element, evt);\n      }\n    });\n  }\n\n  // TODO: model spec\n  _bindOutCancel() {\n    this.cancelButton.addEventListener('mouseleave', (evt) => {\n      let icon = this.opt.cancelOff;\n\n      if (this.opt.starType !== 'img') {\n        icon = `${this.opt.cancelClass} ${icon}`;\n      }\n\n      this._setIcon(this.cancelButton, icon);\n\n      if (this.opt.mouseout) {\n        const score = +this.scoreField.value || undefined;\n\n        this.opt.mouseout.call(this, score, this.element, evt);\n      }\n    });\n  }\n\n  // TODO: model spec\n  _bindOver() {\n    const action = this.opt.half ? 'mousemove' : 'mouseover';\n\n    this.stars.forEach((value) => {\n      value.addEventListener(action, (evt) => {\n        const score = this._getScoreByPosition(evt, value);\n\n        this._fill(score);\n\n        if (this.opt.half) {\n          this._roundStars(score, evt);\n          this._setTitle(score, evt);\n\n          this.element.dataset.score = score;\n        }\n\n        this._target(score, evt);\n\n        if (this.opt.mouseover) {\n          this.opt.mouseover.call(this, score, this.element, evt);\n        }\n      });\n    });\n  }\n\n  // TODO: model spec\n  _bindOverCancel() {\n    this.cancelButton.addEventListener('mouseover', (evt) => {\n      if (this._isReadOnly()) {\n        return;\n      }\n\n      const starOff = this.opt.path + this.opt.starOff;\n\n      let icon = this.opt.cancelOn;\n\n      if (this.opt.starType === 'img') {\n        this.stars.forEach((value) => {\n          value.src = starOff;\n        });\n      } else {\n        icon = this.opt.cancelClass + ' ' + icon;\n\n        this.stars.forEach((value) => {\n          value.className = starOff;\n        });\n      }\n\n      this._setIcon(this.cancelButton, icon);\n      this._target(null, evt);\n\n      if (this.opt.mouseover) {\n        this.opt.mouseover.call(this, null, this.element, evt);\n      }\n    });\n  }\n\n  // TODO: model spec\n  _buildScoreField() {\n    const input = document.createElement('input');\n\n    input.name = this.opt.scoreName;\n    input.type = 'hidden';\n\n    this.element.appendChild(input);\n\n    return input;\n  }\n\n  // TODO: model spec\n  _createCancel() {\n    const button = document.createElement(this.opt.starType);\n    const icon = this.opt.path + this.opt.cancelOff;\n\n    button.setAttribute('class', this.opt.cancelClass);\n    button.setAttribute('title', this.opt.cancelHint);\n\n    if (this.opt.starType === 'img') {\n      button.setAttribute('alt', 'x');\n      button.setAttribute('src', icon);\n    } else {\n      button.classList.add(icon);\n\n      // TODO: use the dataset\n      button.setAttribute('data-alt', 'x');\n    }\n\n    if (this.opt.cancelPlace === 'left') {\n      this.element.prepend('\\u00A0');\n      this.element.prepend(button);\n    } else {\n      this.element.append('\\u00A0');\n      this.element.appendChild(button);\n    }\n\n    this.cancelButton = button;\n  }\n\n  // TODO: model spec\n  _createScore() {\n    this.scoreField = document.querySelector(this.opt.targetScore) || this._buildScoreField();\n  }\n\n  _createStars() {\n    for (let i = 1; i <= this.opt.number; i++) {\n      const attributes = this._attributesForIndex(i);\n\n      let star = document.createElement(this.opt.starType);\n\n      for (const key in attributes) {\n        star.setAttribute(key, attributes[key]);\n      }\n\n      this.element.appendChild(star);\n\n      if (this.opt.space && i < this.opt.number) {\n        this.element.append('\\u00A0');\n      }\n    }\n\n    this.stars = this.element.querySelectorAll(this.opt.starType);\n  }\n\n  // TODO: model spec\n  _error(message) {\n    throw new Error(message);\n  }\n\n  _executeCallbacks() {\n    const options = ['number', 'readOnly', 'score', 'scoreName', 'target', 'path'];\n\n    for (let i = 0; i < options.length; i++) {\n      if (typeof this.opt[options[i]] === 'function') {\n        const value = this.opt[options[i]].call(this, this.element);\n\n        if (value) {\n          this.opt[options[i]] = value;\n        } else {\n          delete this.opt[options[i]];\n        }\n      }\n    }\n  }\n\n  // TODO: model spec\n  _fill(score) {\n    let hash = 0;\n\n    if (this.opt.iconRangeSame && this.opt.iconRange) {\n      while (hash < this.opt.iconRange.length && this.opt.iconRange[hash].range < score) {\n        hash++;\n      }\n    }\n\n    for (let i = 1; i <= this.stars.length; i++) {\n      const star = this.stars[i - 1];\n      const turnOn = this._turnOn(i, score);\n\n      let icon;\n\n      if (this.opt.iconRange && this.opt.iconRange.length > hash) {\n        const irange = this.opt.iconRange[hash];\n\n        icon = this._getRangeIcon(irange, turnOn);\n\n        if (i <= irange.range) {\n          this._setIcon(star, icon);\n        }\n\n        if (i === irange.range) {\n          hash++;\n        }\n      } else {\n        icon = this.opt[turnOn ? 'starOn' : 'starOff'];\n\n        this._setIcon(star, icon);\n      }\n    }\n  }\n\n  _getDecimal(number, fractions) {\n    const decimal = number.toString().split('.')[1];\n\n    let result = 0;\n\n    if (decimal) {\n      result = parseInt(decimal.slice(0, fractions), 10);\n\n      if (decimal.slice(1, 5) === '9999') {\n        result++;\n      }\n    }\n\n    return result;\n  }\n\n  // TODO: model spec\n  _getRangeIcon(irange, turnOn) {\n    return turnOn ? irange.on || this.opt.starOn : irange.off || this.opt.starOff;\n  }\n\n  // TODO: model spec\n  _getScoreByPosition(evt, icon) {\n    let score = parseInt(icon.alt || icon.getAttribute('data-alt'), 10);\n\n    if (this.opt.half) {\n      const size = this._getWidth();\n      const percent = parseFloat((evt.pageX - icon.getBoundingClientRect().x) / size);\n\n      score = score - 1 + percent;\n    }\n\n    return score;\n  }\n\n  // TODO: model spec\n  _getHint(score, evt) {\n    if (score !== 0 && !score) {\n      return this.opt.noRatedMsg;\n    }\n\n    const integer = Math.ceil(score);\n    const group = this.opt.hints[(integer || 1) - 1];\n    const set = !evt || this.isMove;\n\n    let decimal = this._getDecimal(score, 1);\n    let hint = group;\n\n    if (this.opt.precision) {\n      if (set) {\n        decimal = decimal === 0 ? 9 : decimal - 1;\n      }\n\n      hint = group[decimal];\n    } else if (this.opt.halfShow || this.opt.half) {\n      decimal = set && decimal === 0 ? 1 : decimal > 5 ? 1 : 0;\n\n      hint = group[decimal];\n    }\n\n    return hint === '' ? '' : hint || score;\n  }\n\n  // TODO: model spec\n  _getWidth() {\n    // 16 is the default font-size px when icons is not redered yet\n    const width = parseFloat(this.stars[0].offsetWidth) || 16;\n\n    if (!width) {\n      this._error('Could not get the icon width!');\n    }\n\n    return width;\n  }\n\n  _isReadOnly() {\n    return { true: true }[this.element.dataset.readOnly] || false;\n  }\n\n  // TODO: model spec\n  _lock() {\n    const hint = this._getHint(this.scoreField.value);\n\n    this.element.style.pointerEvents = 'none';\n    this.element.title = hint;\n\n    this.scoreField.readOnly = true;\n\n    this.stars.forEach((value) => {\n      value.title = hint;\n    });\n\n    if (this.cancelButton) {\n      this.cancelButton.style.display = 'none';\n    }\n\n    this.element.dataset.readOnly = true;\n  }\n\n  _nameForIndex(i) {\n    return this.opt.score && this.opt.score >= i ? 'starOn' : 'starOff';\n  }\n\n  // TODO: model spec\n  _resetTitle() {\n    for (let i = 0; i < this.opt.number; i++) {\n      this.stars[i].title = this._getHint(i + 1);\n    }\n  }\n\n  _parseOptions(dataset) {\n    return Object.keys(dataset).reduce((acc, key) => {\n      let value = { true: true, false: false }[dataset[key]];\n\n      value = value !== null && value !== undefined ? value : dataset[key];\n\n      if (!isNaN(value) && Number.isInteger(parseFloat(value))) {\n        value = Number(value);\n      }\n\n      acc[key] = value;\n\n      return acc;\n    }, {});\n  }\n\n  // TODO: model spec\n  _roundHalfScore(score) {\n    const integer = parseInt(score, 10);\n\n    let decimal = this._getDecimal(score, 1);\n\n    if (decimal !== 0) {\n      decimal = decimal > 5 ? 1 : 0.5;\n    }\n\n    return integer + decimal;\n  }\n\n  // TODO: model spec\n  _roundStars(score, evt) {\n    const name = this._starName(score, evt);\n\n    if (name) {\n      const icon = this.opt[name];\n      const star = this.stars[Math.ceil(score) - 1];\n\n      star && this._setIcon(star, icon);\n    } // Full down: [x.00 .. x.25]\n  }\n\n  // TODO: model spec\n  _setIcon(star, icon) {\n    star[this.opt.starType === 'img' ? 'src' : 'className'] = this.opt.path + icon;\n  }\n\n  _setPath() {\n    this.opt.path = this.opt.path || '';\n\n    if (this.opt.path && this.opt.path.slice(-1) !== '/') {\n      this.opt.path += '/';\n    }\n  }\n\n  // TODO: model spec\n  _setTarget(target, score) {\n    if (score) {\n      score = this.opt.targetFormat.toString().replace('{score}', score);\n    }\n    if (target instanceof HTMLInputElement || target instanceof HTMLSelectElement) {\n      target.value = score;\n    } else {\n      target.textContent = score;\n    }\n  }\n\n  // TODO: model spec\n  _setTitle(score, evt) {\n    if (score) {\n      const integer = parseInt(Math.ceil(score), 10);\n      const star = this.stars.item(integer - 1);\n\n      star.title = this._getHint(score, evt);\n    }\n  }\n\n  _starName(score, evt) {\n    const decimal = +(score % 1).toFixed(2);\n\n    if (evt || this.isMove) {\n      return decimal > 0.5 ? 'starOn' : 'starHalf';\n    }\n\n    if (decimal <= this.opt.round.down) {\n      // Down: [x.00 ... x.25]\n      return;\n    }\n\n    if (this.opt.halfShow && decimal < this.opt.round.up) {\n      // Half: [x.26 ... x.75]\n      return 'starHalf';\n    }\n\n    if (decimal < this.opt.round.full) {\n      // Off: [x.26 .. x.6]\n      return 'starOff';\n    }\n\n    return 'starOn'; // Up: [x.26 ...] || [x.6 ...]\n  }\n\n  // TODO: model spec\n  _target(score, evt) {\n    if (this.opt.target) {\n      const target = document.querySelector(this.opt.target);\n\n      if (!target) {\n        this._error('Target selector invalid or missing!');\n      }\n\n      const mouseover = evt && evt.type === 'mouseover';\n\n      if (score === undefined) {\n        score = this.opt.targetText;\n      } else if (score === null) {\n        score = mouseover ? this.opt.cancelHint : this.opt.targetText;\n      } else {\n        if (this.opt.targetType === 'hint') {\n          score = this._getHint(score, evt);\n        } else if (this.opt.precision) {\n          score = parseFloat(score).toFixed(1);\n        }\n\n        const mousemove = evt && evt.type === 'mousemove';\n\n        if (!mouseover && !mousemove && !this.opt.targetKeep) {\n          score = this.opt.targetText;\n        }\n      }\n\n      this._setTarget(target, score);\n    }\n  }\n\n  // TODO: model spec\n  _turnOn(i, score) {\n    return this.opt.single ? i === score : i <= score;\n  }\n\n  // TODO: model spec\n  _unlock() {\n    this.element.style.cursor = 'pointer';\n    this.element.style.pointerEvents = 'auto';\n\n    this.element.removeAttribute('title');\n\n    this.element.dataset.readOnly = false;\n\n    this.scoreField.readOnly = false;\n\n    this._resetTitle();\n\n    if (this.cancelButton) {\n      this.cancelButton.style.display = '';\n    }\n  }\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (Raty);\n\n\n//# sourceURL=webpack://Materialize/./node_modules/raty-js/src/raty.js?")}},__webpack_module_cache__={};function __webpack_require__(n){var e=__webpack_module_cache__[n];if(void 0!==e)return e.exports;var t=__webpack_module_cache__[n]={exports:{}};return __webpack_modules__[n](t,t.exports,__webpack_require__),t.exports}__webpack_require__.d=function(n,e){for(var t in e)__webpack_require__.o(e,t)&&!__webpack_require__.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},__webpack_require__.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},__webpack_require__.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/raty-js/raty-js.js");return __webpack_exports__}()}));