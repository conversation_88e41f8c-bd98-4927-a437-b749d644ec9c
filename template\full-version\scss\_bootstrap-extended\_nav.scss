/* Nav
******************************************************************************* */

.nav {
  flex-wrap: inherit;
  padding-inline-start: 0;
  &.nav-pills:not(.nav-align-right):not(.nav-align-left) {
    flex-wrap: wrap;
  }
  .nav-item {
    white-space: nowrap;
  }
  &:not(.nav-pills){
    .nav-link {
      &:hover,
      &:focus {
        color: $nav-link-hover-color;
      }
    }
  }

  &.nav-sm,
  .nav-sm > & {
    --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y-sm};
    --#{$prefix}nav-link-padding-x: #{$nav-link-padding-x-sm};
    --#{$prefix}nav-link-font-size: #{$font-size-sm};
    --#{$prefix}nav-link-line-height: #{$nav-link-line-height-sm};
  }

  &.nav-lg,
  .nav-lg > & {
    --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y-lg};
    --#{$prefix}nav-link-padding-x: #{$nav-link-padding-x-lg};
    --#{$prefix}nav-link-font-size: #{$font-size-lg};
    --#{$prefix}nav-link-line-height: #{$nav-link-line-height-lg};
  }
}
.nav .nav-link {
  &:not(.active):hover {
    color: $nav-link-hover-color;
  }
}

/* nav tabs shadow */
.nav-tabs-shadow {
  border: 1px solid var(--#{$prefix}nav-border-color);
  box-shadow: var(--#{$prefix}nav-box-shadow);
  .card & {
    box-shadow: none;
  }
}

/* Tab and pills style */
.nav-tabs,
.nav-pills {
  .nav-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
  }

  &:not(.nav-fill):not(.nav-justified) .nav-link {
    inline-size: 100%;
  }
}

.tab-content:not(.doc-example-content) {
  z-index: 1;
  padding: $card-spacer-y;
  .tab-pane {
    opacity: 0;
    transform: translateX(-30px);
    @include transition(all linear .1s);
    :dir(rtl) & {
      transform: translateX(30px);
    }
    &.show {
      opacity: 1;
      transform: unset !important;
      @include transition(all ease-out .2s .1s);
    }
  }
}

// Widget Tabs
// --------------------------------------------------

.nav-tabs {
  div:not(.nav-align-left):not(.nav-align-right) > & {
    display: inline-flex;
    inline-size: 100%;
    overflow-x: auto !important;
    overflow-y: hidden;
  }
  &.nav-tabs-widget {
    border: 0 !important;
    overflow-x: auto;
    .nav-link {
      border: 2px dashed var(--#{$prefix}border-color);
      @include media-breakpoint-up(md) {
        block-size: 86px !important;
        inline-size: 92px !important;
        @include border-radius($border-radius-xl);
      }
      @include media-breakpoint-down(md) {
        padding: 0;
        border: 0 !important;
      }
      &.active {
        border-style: solid;
        border-color: var(--#{$prefix}primary);
        .avatar-initial {
          background-color: rgba(var(--#{$prefix}primary-rgb), .22) !important;
          color: var(--#{$prefix}primary) !important;
        }
      }
    }

    // Remove bottom border of tabs
    .tab-slider {
      display: none;
    }
    & + .tab-content {
      .tab-pane {
        transform: none !important;
        @include transition(unset);
      }
    }
  }
}

/* For scrollable navs/tabs/pills */
.nav-scrollable {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  overflow: auto;
  flex-wrap: nowrap;
  inline-size: 100%;
}

.nav-tabs {
  position: relative;
  .tab-slider {
    position: absolute;
    block-size: 2px;
    .nav-align-left &,
    .nav-align-right & {
      inline-size: 2px !important;
    }
  }

  /* Tab link */
  .nav-link {
    @include border-radius(0);
    background-clip: padding-box;
    &.waves-effect {
      .waves-ripple {
        background:
          radial-gradient(
            rgba(var(--#{$prefix}primary-rgb), .2) 0,
            rgba(var(--#{$prefix}primary-rgb), .3) 40%,
            rgba(var(--#{$prefix}primary-rgb), .4) 50%,
            rgba(var(--#{$prefix}primary-rgb), .5) 60%,
            rgba(var(--#{$prefix}white-rgb), 0) 70%
          );
      }
    }
  }
  .nav-link.active,
  .nav-item.show .nav-link {
    &,
    &:hover,
    &:focus {
      .nav-align-top & {
        box-shadow: 0 -2px 0 $nav-tabs-link-active-border-color inset;
      }
      .nav-align-bottom & {
        box-shadow: 0 2px 0 $nav-tabs-link-active-border-color inset;
      }
      .nav-align-left & {
        box-shadow: -2px 0 0 $nav-tabs-link-active-border-color inset;
        :dir(rtl) & {
          box-shadow: 2px 0 0 $nav-tabs-link-active-border-color inset;
        }
      }
      .nav-align-right & {
        box-shadow: 2px 0 0 $nav-tabs-link-active-border-color inset;
        :dir(rtl) & {
          box-shadow: -2px 0 0 $nav-tabs-link-active-border-color inset;
        }
      }
    }
  }
}

.nav-pills {
  .nav-link {
    padding-block: $nav-pills-padding-y;
    padding-inline: $nav-pills-padding-x;
    &:not(.active){
      &:hover,
      &:focus {
        &.waves-effect {
          .waves-ripple {
            background:
              radial-gradient(
                rgba(var(--#{$prefix}primary-rgb), .2) 0,
                rgba(var(--#{$prefix}primary-rgb), .3) 40%,
                rgba(var(--#{$prefix}primary-rgb), .4) 50%,
                rgba(var(--#{$prefix}primary-rgb), .5) 60%,
                rgba(var(--#{$prefix}white-rgb), 0) 70%
              );
          }
        }
      }
    }
    &.active {
      box-shadow: var(--#{$prefix}box-shadow-xs);
    }
  }
  & .nav-item .nav-link:not(.active):hover {
    background-color: $nav-pills-link-hover-bg;
    border-block-end: none;
    color: $nav-pills-link-hover-color;
    padding-block-end: $nav-link-padding-y;
  }
  ~ .tab-content {
    border: 1px solid var(--#{$prefix}nav-border-color);
    box-shadow: var(--#{$prefix}nav-box-shadow);
  }
}

/* Top, Right, Bottom & Left Tabbed panels */

.nav-align-top,
.nav-align-right,
.nav-align-bottom,
.nav-align-left {
  > .tab-content {
    background: $nav-tabs-bg;
  }
  .nav-tabs {
    background: $nav-tabs-bg;
  }
  display: flex;

  > .nav,
  > div > .nav {
    position: relative;
    z-index: 1;
  }

  &:has(.nav-tabs) {
    @include border-radius($card-inner-border-radius !important);
  }
}

.nav-align-right,
.nav-align-left {
  align-items: stretch;

  > .nav,
  > div > .nav {
    flex-direction: column;
    flex-grow: 0;
    border-block-end-width: 0;
  }

  > .nav.nav-pills .nav-item:not(:last-child),
  > div > .nav.nav-pills .nav-item:not(:last-child) {
    margin-block: 0 $nav-spacer !important;
    margin-inline: 0 !important;
  }

  > .tab-content {
    flex-grow: 1;
    .tab-pane {
      transform: translateY(-30px);
      &.show {
        transform: translateY(0);
      }
    }
  }
}

/* Top tabs */
.nav-align-top {
  .tab-content {
    @include border-bottom-radius($card-inner-border-radius);
  }
  flex-direction: column;
  .nav-tabs {
    border-block-end: $border-width solid var(--#{$prefix}nav-tabs-border-color);
    @include border-top-radius($card-inner-border-radius);
    & .nav-link:not(.active):hover {
      border-block-end: 2px solid $nav-pills-link-hover-bg !important;
      padding-block-end: calc($nav-link-padding-y - .125rem);
    }
    &.nav-lg .nav-link:not(.active):hover {
      padding-block-end: calc($nav-link-padding-y-lg - .125rem);
    }
    &.nav-sm .nav-link:not(.active):hover {
      padding-block-end: calc($nav-link-padding-y-sm - .1125rem);
    }
  }
  .nav-pills ~ .tab-content {
    @include border-top-radius($card-inner-border-radius);
  }
}
.nav-align-top,
.nav-align-bottom,
.card {
  > .tab-content {
    .tab-pane {
      transform: translateX(-30px);
      :dir(rtl) & {
        transform: translateX(30px);
      }
      &.show {
        transform: translateX(0) !important;
      }
    }
  }
  > .nav.nav-pills .nav-item:not(:last-child) {
    margin-inline-end: $nav-spacer;
  }
}

/* Right tabs */
.nav-align-right {
  .tab-content {
    border-end-start-radius: $card-inner-border-radius;
    border-start-start-radius: $card-inner-border-radius;
  }
  flex-direction: row-reverse;
  .nav-tabs {
    position: relative;
    border-end-end-radius: $card-inner-border-radius;
    border-inline-start: $border-width solid var(--#{$prefix}nav-tabs-border-color);
    border-start-end-radius: $card-inner-border-radius;
    .tab-slider {
      inset-inline-start: 0;
    }
    ~ .tab-content {
      .card & {
        border-inline-end: $nav-tabs-border-width solid var(--#{$prefix}nav-tabs-border-color);
      }
    }
    & .nav-link:not(.active):hover {
      border-inline-start: 2px solid $nav-pills-link-hover-bg !important;
      padding-inline-start: calc($nav-link-padding-x - .125rem);
    }
    &.nav-lg .nav-link:not(.active):hover {
      padding-inline-start: calc($nav-link-padding-x-lg - .125rem);
    }
    &.nav-sm .nav-link:not(.active):hover {
      padding-inline-start: calc($nav-link-padding-x-sm - .125rem);
    }
  }

  > .nav .nav-item,
  > div > .nav .nav-item {
    margin-inline: 0;
  }
  .nav-link {
    justify-content: end;
    text-align: end;
  }
  .nav-pills ~ .tab-content {
    @include border-radius($card-inner-border-radius);
  }
}

/* Bottom tabs */
.nav-align-bottom {
  .tab-content {
    @include border-top-radius($card-inner-border-radius);
  }
  flex-direction: column-reverse;

  > .nav .nav-item,
  > div > .nav .nav-item {
    margin-block: 0;
  }

  > .nav,
  > div > .nav {
    border-block-end-width: 0;
    border-block-start: $nav-tabs-border-width solid var(--#{$prefix}nav-tabs-border-color);
  }
  .nav-tabs {
    border-block-start: $border-width solid var(--#{$prefix}nav-tabs-border-color);
    @include border-bottom-radius($card-inner-border-radius);
    .tab-slider {
      inset-block-end: inherit !important;
    }
    & .nav-link:not(.active):hover {
      border-block-start: 2px solid $nav-pills-link-hover-bg !important;
      padding-block-start: calc($nav-link-padding-y - .125rem);
    }
    &.nav-lg .nav-link:not(.active):hover {
      padding-block-start: calc($nav-link-padding-y-lg - .125rem);
    }
    &.nav-sm .nav-link:not(.active):hover {
      padding-block-start: calc($nav-link-padding-y-sm - .1125rem);
    }
  }
  .nav-pills ~ .tab-content {
    @include border-bottom-radius($card-inner-border-radius);
  }
}

/* Left tabs */
.nav-align-left {
  .tab-content {
    @include border-end-radius($card-inner-border-radius);
  }
  .nav-tabs {
    position: relative;
    ~ .tab-content {
      .card & {
        border-inline-start: $nav-tabs-border-width solid var(--#{$prefix}nav-tabs-border-color);
      }
    }
    border-inline-end: $border-width solid var(--#{$prefix}nav-tabs-border-color);
    & .nav-link:not(.active):hover {
      border-inline-end: 2px solid $nav-pills-link-hover-bg !important;
      padding-inline-end: calc($nav-link-padding-x - .125rem);
    }
    &.nav-lg .nav-link:not(.active):hover {
      padding-inline-end: calc($nav-link-padding-x-lg - .125rem);
    }
    &.nav-sm .nav-link:not(.active):hover {
      padding-inline-end: calc($nav-link-padding-x-sm - .125rem);
    }
  }
  > .nav .nav-item,
  > div > .nav .nav-item {
    margin-inline: 0;
  }
  .nav-link {
    justify-content: start;
    text-align: start;
  }
  .nav-pills ~ .tab-content {
    @include border-start-radius($card-inner-border-radius !important);
  }
  &:has(.nav-tabs) {
    overflow: hidden;
  }
}
