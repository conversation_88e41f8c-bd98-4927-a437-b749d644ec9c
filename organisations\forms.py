from django import forms
from django.contrib.auth.models import User
from .models import Organisation, MembreOrganisation, TypeAbonnement
from .utils import valider_telephone_international


class OrganisationForm(forms.ModelForm):
    """Formulaire pour créer et modifier une organisation"""
    
    class Meta:
        model = Organisation
        fields = [
            'nom', 
            'adresse', 
            'telephone', 
            'email', 
            'type_abonnement'
        ]
        
        widgets = {
            'nom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de l\'organisation'
            }),
            'adresse': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Adresse complète'
            }),
            'telephone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+225 01 23 45 67 89'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'type_abonnement': forms.Select(attrs={
                'class': 'form-control'
            })
        }
        
        labels = {
            'nom': 'Nom de l\'organisation',
            'adresse': 'Adresse complète',
            'telephone': 'Téléphone',
            'email': 'Email de contact',
            'type_abonnement': 'Type d\'abonnement'
        }

    def clean_nom(self):
        """Validation personnalisée pour le nom"""
        nom = self.cleaned_data.get('nom')
        if nom and len(nom.strip()) < 3:
            raise forms.ValidationError(
                "Le nom de l'organisation doit contenir au moins 3 caractères."
            )
        return nom.strip() if nom else nom

    def clean_telephone(self):
        """Validation personnalisée pour le téléphone"""
        telephone = self.cleaned_data.get('telephone')
        if telephone:
            if not valider_telephone_international(telephone):
                raise forms.ValidationError(
                    "Format de téléphone invalide. Utilisez le format international "
                    "(ex: +225 01 23 45 67 89 pour la Côte d'Ivoire)."
                )
        return telephone

    def clean_email(self):
        """Validation personnalisée pour l'email"""
        email = self.cleaned_data.get('email')
        if email:
            # Vérifier si l'email n'est pas déjà utilisé par une autre organisation
            existing_org = Organisation.objects.filter(email=email)
            if self.instance.pk:
                existing_org = existing_org.exclude(pk=self.instance.pk)

            if existing_org.exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée par une autre organisation."
                )
        return email


class MembreOrganisationForm(forms.ModelForm):
    """Formulaire pour ajouter/modifier un membre d'organisation"""
    
    utilisateur_email = forms.EmailField(
        label="Email de l'utilisateur",
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        }),
        help_text="L'utilisateur doit déjà avoir un compte sur la plateforme"
    )
    
    class Meta:
        model = MembreOrganisation
        fields = ['role']
        
        widgets = {
            'role': forms.Select(attrs={
                'class': 'form-control'
            })
        }
        
        labels = {
            'role': 'Rôle dans l\'organisation'
        }

    def __init__(self, *args, **kwargs):
        self.organisation = kwargs.pop('organisation', None)
        super().__init__(*args, **kwargs)

    def clean_utilisateur_email(self):
        """Validation de l'email utilisateur"""
        email = self.cleaned_data.get('utilisateur_email')
        if email:
            try:
                user = User.objects.get(email=email)
                # Vérifier si l'utilisateur n'est pas déjà membre de cette organisation
                if (self.organisation and 
                    MembreOrganisation.objects.filter(
                        utilisateur=user, 
                        organisation=self.organisation
                    ).exists()):
                    raise forms.ValidationError(
                        "Cet utilisateur est déjà membre de cette organisation."
                    )
                return email
            except User.DoesNotExist:
                raise forms.ValidationError(
                    "Aucun utilisateur trouvé avec cette adresse email."
                )
        return email

    def save(self, commit=True):
        """Sauvegarde personnalisée pour associer l'utilisateur"""
        instance = super().save(commit=False)
        if self.cleaned_data.get('utilisateur_email'):
            user = User.objects.get(email=self.cleaned_data['utilisateur_email'])
            instance.utilisateur = user
        if self.organisation:
            instance.organisation = self.organisation
        if commit:
            instance.save()
        return instance


class RechercheOrganisationForm(forms.Form):
    """Formulaire de recherche d'organisations"""
    
    nom = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher par nom...'
        }),
        label="Nom"
    )
    
    type_abonnement = forms.ChoiceField(
        required=False,
        choices=[('', 'Tous les types')] + list(TypeAbonnement.choices),
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label="Type d'abonnement"
    )
    
    actif = forms.ChoiceField(
        required=False,
        choices=[
            ('', 'Toutes'),
            ('True', 'Actives seulement'),
            ('False', 'Inactives seulement')
        ],
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label="Statut"
    )
