from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone
from .utils import formater_telephone, obtenir_pays_depuis_telephone


class TypeAbonnement(models.TextChoices):
    """Types d'abonnement disponibles"""
    GRATUIT = 'GRATUIT', 'Gratuit'
    PREMIUM = 'PREMIUM', 'Premium'


class Organisation(models.Model):
    """Modèle représentant une organisation hospitalière"""

    nom = models.CharField(
        max_length=200,
        verbose_name="Nom de l'organisation",
        help_text="Nom complet de l'établissement hospitalier"
    )

    adresse = models.TextField(
        verbose_name="Adresse complète",
        help_text="Adresse postale complète de l'organisation"
    )

    telephone = models.CharField(
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+[1-9]\d{1,14}$',
            message="Format de téléphone invalide. Utilisez le format international (+XXX XXXXXXXXX)."
        )],
        verbose_name="Téléphone",
        help_text="Numéro de téléphone principal au format international (ex: +225 01 23 45 67 89)"
    )

    email = models.EmailField(
        verbose_name="Email de contact",
        help_text="Adresse email principale de l'organisation"
    )

    type_abonnement = models.CharField(
        max_length=10,
        choices=TypeAbonnement.choices,
        default=TypeAbonnement.GRATUIT,
        verbose_name="Type d'abonnement",
        help_text="Type d'abonnement de l'organisation"
    )

    date_creation = models.DateTimeField(
        default=timezone.now,
        verbose_name="Date de création"
    )

    date_modification = models.DateTimeField(
        auto_now=True,
        verbose_name="Dernière modification"
    )

    actif = models.BooleanField(
        default=True,
        verbose_name="Organisation active",
        help_text="Indique si l'organisation est active"
    )

    # Relation avec les utilisateurs
    utilisateurs = models.ManyToManyField(
        User,
        through='MembreOrganisation',
        related_name='organisations',
        verbose_name="Utilisateurs"
    )

    class Meta:
        verbose_name = "Organisation"
        verbose_name_plural = "Organisations"
        ordering = ['nom']

    def __str__(self):
        return self.nom

    def get_nombre_utilisateurs(self):
        """Retourne le nombre d'utilisateurs dans l'organisation"""
        return self.utilisateurs.count()

    def est_premium(self):
        """Vérifie si l'organisation a un abonnement premium"""
        return self.type_abonnement == TypeAbonnement.PREMIUM

    def peut_ajouter_utilisateur(self):
        """Vérifie si l'organisation peut ajouter un nouvel utilisateur"""
        if self.est_premium():
            return True  # Pas de limite pour Premium
        return self.get_nombre_utilisateurs() < 5  # Limite de 5 pour Gratuit

    def get_telephone_formate(self):
        """Retourne le numéro de téléphone formaté pour l'affichage"""
        return formater_telephone(self.telephone)

    def get_pays_telephone(self):
        """Retourne le pays d'origine du numéro de téléphone"""
        return obtenir_pays_depuis_telephone(self.telephone)


class MembreOrganisation(models.Model):
    """Modèle intermédiaire pour la relation User-Organisation"""

    class RoleMembre(models.TextChoices):
        ADMIN = 'ADMIN', 'Administrateur'
        MEDECIN = 'MEDECIN', 'Médecin'
        INFIRMIER = 'INFIRMIER', 'Infirmier'
        RECEPTIONNISTE = 'RECEPTIONNISTE', 'Réceptionniste'
        COMPTABLE = 'COMPTABLE', 'Comptable'

    utilisateur = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name="Utilisateur"
    )

    organisation = models.ForeignKey(
        Organisation,
        on_delete=models.CASCADE,
        verbose_name="Organisation"
    )

    role = models.CharField(
        max_length=20,
        choices=RoleMembre.choices,
        default=RoleMembre.RECEPTIONNISTE,
        verbose_name="Rôle dans l'organisation"
    )

    date_ajout = models.DateTimeField(
        default=timezone.now,
        verbose_name="Date d'ajout"
    )

    actif = models.BooleanField(
        default=True,
        verbose_name="Membre actif"
    )

    class Meta:
        verbose_name = "Membre d'organisation"
        verbose_name_plural = "Membres d'organisation"
        unique_together = ['utilisateur', 'organisation']

    def __str__(self):
        return f"{self.utilisateur.get_full_name()} - {self.organisation.nom} ({self.role})"
