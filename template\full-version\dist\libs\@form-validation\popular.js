!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n=e();for(var i in n)("object"==typeof exports?exports:t)[i]=n[i]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./node_modules/@form-validation/bundle/lib/cjs/popular.js":function(__unused_webpack_module,exports){eval('\n\nvar lib$B = {exports: {}};\n\nvar index_min$B = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/core\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$B;\n\nfunction requireIndex_min$B () {\n\tif (hasRequiredIndex_min$B) return index_min$B;\n\thasRequiredIndex_min$B = 1;\nvar e={luhn:function(e){for(var t=e.length,i=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],r=0,n=0;t--;)n+=i[r][parseInt(e.charAt(t),10)],r=1-r;return n%10==0&&n>0},mod11And10:function(e){for(var t=e.length,i=5,r=0;r<t;r++)i=(2*(i||10)%11+parseInt(e.charAt(r),10))%10;return 1===i},mod37And36:function(e,t){void 0===t&&(t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");for(var i=e.length,r=t.length,n=Math.floor(r/2),s=0;s<i;s++)n=(2*(n||r)%(r+1)+t.indexOf(e.charAt(s)))%r;return 1===n},mod97And10:function(e){for(var t=function(e){return e.split("").map((function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-55:e})).join("").split("").map((function(e){return parseInt(e,10)}))}(e),i=0,r=t.length,n=0;n<r-1;++n)i=10*(i+t[n])%97;return (i+=t[r-1])%97==1},verhoeff:function(e){for(var t=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],i=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],r=e.reverse(),n=0,s=0;s<r.length;s++)n=t[n][i[s%8][r[s]]];return 0===n}};var t=function(){function e(e,t){this.fields={},this.elements={},this.ee={fns:{},clear:function(){this.fns={};},emit:function(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];(this.fns[e]||[]).map((function(e){return e.apply(e,t)}));},off:function(e,t){if(this.fns[e]){var i=this.fns[e].indexOf(t);i>=0&&this.fns[e].splice(i,1);}},on:function(e,t){(this.fns[e]=this.fns[e]||[]).push(t);}},this.filter={filters:{},add:function(e,t){(this.filters[e]=this.filters[e]||[]).push(t);},clear:function(){this.filters={};},execute:function(e,t,i){if(!this.filters[e]||!this.filters[e].length)return t;for(var r=t,n=this.filters[e],s=n.length,l=0;l<s;l++)r=n[l].apply(r,i);return r},remove:function(e,t){this.filters[e]&&(this.filters[e]=this.filters[e].filter((function(e){return e!==t})));}},this.plugins={},this.results=new Map,this.validators={},this.form=e,this.fields=t;}return e.prototype.on=function(e,t){return this.ee.on(e,t),this},e.prototype.off=function(e,t){return this.ee.off(e,t),this},e.prototype.emit=function(e){for(var t,i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];return (t=this.ee).emit.apply(t,function(e,t,i){if(i||2===arguments.length)for(var r,n=0,s=t.length;n<s;n++)!r&&n in t||(r||(r=Array.prototype.slice.call(t,0,n)),r[n]=t[n]);return e.concat(r||Array.prototype.slice.call(t))}([e],i,!1)),this},e.prototype.registerPlugin=function(e,t){if(this.plugins[e])throw new Error("The plguin ".concat(e," is registered"));return t.setCore(this),t.install(),this.plugins[e]=t,this},e.prototype.deregisterPlugin=function(e){var t=this.plugins[e];return t&&t.uninstall(),delete this.plugins[e],this},e.prototype.enablePlugin=function(e){var t=this.plugins[e];return t&&t.enable(),this},e.prototype.disablePlugin=function(e){var t=this.plugins[e];return t&&t.disable(),this},e.prototype.isPluginEnabled=function(e){var t=this.plugins[e];return !!t&&t.isPluginEnabled()},e.prototype.registerValidator=function(e,t){if(this.validators[e])throw new Error("The validator ".concat(e," is registered"));return this.validators[e]=t,this},e.prototype.registerFilter=function(e,t){return this.filter.add(e,t),this},e.prototype.deregisterFilter=function(e,t){return this.filter.remove(e,t),this},e.prototype.executeFilter=function(e,t,i){return this.filter.execute(e,t,i)},e.prototype.addField=function(e,t){var i=Object.assign({},{selector:"",validators:{}},t);return this.fields[e]=this.fields[e]?{selector:i.selector||this.fields[e].selector,validators:Object.assign({},this.fields[e].validators,i.validators)}:i,this.elements[e]=this.queryElements(e),this.emit("core.field.added",{elements:this.elements[e],field:e,options:this.fields[e]}),this},e.prototype.removeField=function(e){if(!this.fields[e])throw new Error("The field ".concat(e," validators are not defined. Please ensure the field is added first"));var t=this.elements[e],i=this.fields[e];return delete this.elements[e],delete this.fields[e],this.emit("core.field.removed",{elements:t,field:e,options:i}),this},e.prototype.validate=function(){var e=this;return this.emit("core.form.validating",{formValidation:this}),this.filter.execute("validate-pre",Promise.resolve(),[]).then((function(){return Promise.all(Object.keys(e.fields).map((function(t){return e.validateField(t)}))).then((function(t){switch(!0){case-1!==t.indexOf("Invalid"):return e.emit("core.form.invalid",{formValidation:e}),Promise.resolve("Invalid");case-1!==t.indexOf("NotValidated"):return e.emit("core.form.notvalidated",{formValidation:e}),Promise.resolve("NotValidated");default:return e.emit("core.form.valid",{formValidation:e}),Promise.resolve("Valid")}}))}))},e.prototype.validateField=function(e){var t=this,i=this.results.get(e);if("Valid"===i||"Invalid"===i)return Promise.resolve(i);this.emit("core.field.validating",e);var r=this.elements[e];if(0===r.length)return this.emit("core.field.valid",e),Promise.resolve("Valid");var n=r[0].getAttribute("type");return "radio"===n||"checkbox"===n||1===r.length?this.validateElement(e,r[0]):Promise.all(r.map((function(i){return t.validateElement(e,i)}))).then((function(i){switch(!0){case-1!==i.indexOf("Invalid"):return t.emit("core.field.invalid",e),t.results.set(e,"Invalid"),Promise.resolve("Invalid");case-1!==i.indexOf("NotValidated"):return t.emit("core.field.notvalidated",e),t.results.delete(e),Promise.resolve("NotValidated");default:return t.emit("core.field.valid",e),t.results.set(e,"Valid"),Promise.resolve("Valid")}}))},e.prototype.validateElement=function(e,t){var i=this;this.results.delete(e);var r=this.elements[e];if(this.filter.execute("element-ignored",!1,[e,t,r]))return this.emit("core.element.ignored",{element:t,elements:r,field:e}),Promise.resolve("Ignored");var n=this.fields[e].validators;this.emit("core.element.validating",{element:t,elements:r,field:e});var s=Object.keys(n).map((function(r){return function(){return i.executeValidator(e,t,r,n[r])}}));return this.waterfall(s).then((function(n){var s=-1===n.indexOf("Invalid");i.emit("core.element.validated",{element:t,elements:r,field:e,valid:s});var l=t.getAttribute("type");return "radio"!==l&&"checkbox"!==l&&1!==r.length||i.emit(s?"core.field.valid":"core.field.invalid",e),Promise.resolve(s?"Valid":"Invalid")})).catch((function(n){return i.emit("core.element.notvalidated",{element:t,elements:r,field:e}),Promise.resolve(n)}))},e.prototype.executeValidator=function(e,t,i,r){var n=this,s=this.elements[e],l=this.filter.execute("validator-name",i,[i,e]);if(r.message=this.filter.execute("validator-message",r.message,[this.locale,e,l]),!this.validators[l]||!1===r.enabled)return this.emit("core.validator.validated",{element:t,elements:s,field:e,result:this.normalizeResult(e,l,{valid:!0}),validator:l}),Promise.resolve("Valid");var o=this.validators[l],a=this.getElementValue(e,t,l);if(!this.filter.execute("field-should-validate",!0,[e,t,a,i]))return this.emit("core.validator.notvalidated",{element:t,elements:s,field:e,validator:i}),Promise.resolve("NotValidated");this.emit("core.validator.validating",{element:t,elements:s,field:e,validator:i});var d=o().validate({element:t,elements:s,field:e,l10n:this.localization,options:r,value:a});if("function"==typeof d.then)return d.then((function(r){var l=n.normalizeResult(e,i,r);return n.emit("core.validator.validated",{element:t,elements:s,field:e,result:l,validator:i}),l.valid?"Valid":"Invalid"}));var c=this.normalizeResult(e,i,d);return this.emit("core.validator.validated",{element:t,elements:s,field:e,result:c,validator:i}),Promise.resolve(c.valid?"Valid":"Invalid")},e.prototype.getElementValue=function(e,t,i){var r=function(e,t,i,r){var n=(i.getAttribute("type")||"").toLowerCase(),s=i.tagName.toLowerCase();if("textarea"===s)return i.value;if("select"===s){var l=i,o=l.selectedIndex;return o>=0?l.options.item(o).value:""}if("input"===s){if("radio"===n||"checkbox"===n){var a=r.filter((function(e){return e.checked})).length;return 0===a?"":a+""}return i.value}return ""}(this.form,0,t,this.elements[e]);return this.filter.execute("field-value",r,[r,e,t,i])},e.prototype.getElements=function(e){return this.elements[e]},e.prototype.getFields=function(){return this.fields},e.prototype.getFormElement=function(){return this.form},e.prototype.getLocale=function(){return this.locale},e.prototype.getPlugin=function(e){return this.plugins[e]},e.prototype.updateFieldStatus=function(e,t,i){var r=this,n=this.elements[e],s=n[0].getAttribute("type");if(("radio"===s||"checkbox"===s?[n[0]]:n).forEach((function(n){return r.updateElementStatus(e,n,t,i)})),i)"Invalid"===t&&(this.emit("core.field.invalid",e),this.results.set(e,"Invalid"));else switch(t){case"NotValidated":this.emit("core.field.notvalidated",e),this.results.delete(e);break;case"Validating":this.emit("core.field.validating",e),this.results.delete(e);break;case"Valid":this.emit("core.field.valid",e),this.results.set(e,"Valid");break;case"Invalid":this.emit("core.field.invalid",e),this.results.set(e,"Invalid");}return this},e.prototype.updateElementStatus=function(e,t,i,r){var n=this,s=this.elements[e],l=this.fields[e].validators,o=r?[r]:Object.keys(l);switch(i){case"NotValidated":o.forEach((function(i){return n.emit("core.validator.notvalidated",{element:t,elements:s,field:e,validator:i})})),this.emit("core.element.notvalidated",{element:t,elements:s,field:e});break;case"Validating":o.forEach((function(i){return n.emit("core.validator.validating",{element:t,elements:s,field:e,validator:i})})),this.emit("core.element.validating",{element:t,elements:s,field:e});break;case"Valid":o.forEach((function(i){return n.emit("core.validator.validated",{element:t,elements:s,field:e,result:{message:l[i].message,valid:!0},validator:i})})),this.emit("core.element.validated",{element:t,elements:s,field:e,valid:!0});break;case"Invalid":o.forEach((function(i){return n.emit("core.validator.validated",{element:t,elements:s,field:e,result:{message:l[i].message,valid:!1},validator:i})})),this.emit("core.element.validated",{element:t,elements:s,field:e,valid:!1});}return this},e.prototype.resetForm=function(e){var t=this;return Object.keys(this.fields).forEach((function(i){return t.resetField(i,e)})),this.emit("core.form.reset",{formValidation:this,reset:e}),this},e.prototype.resetField=function(e,t){if(t){var i=this.elements[e],r=i[0].getAttribute("type");i.forEach((function(e){"radio"===r||"checkbox"===r?(e.removeAttribute("selected"),e.removeAttribute("checked"),e.checked=!1):(e.setAttribute("value",""),(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&(e.value=""));}));}return this.updateFieldStatus(e,"NotValidated"),this.emit("core.field.reset",{field:e,reset:t}),this},e.prototype.revalidateField=function(e){return this.fields[e]?(this.updateFieldStatus(e,"NotValidated"),this.validateField(e)):Promise.resolve("Ignored")},e.prototype.disableValidator=function(e,t){if(!this.fields[e])return this;var i=this.elements[e];return this.toggleValidator(!1,e,t),this.emit("core.validator.disabled",{elements:i,field:e,formValidation:this,validator:t}),this},e.prototype.enableValidator=function(e,t){if(!this.fields[e])return this;var i=this.elements[e];return this.toggleValidator(!0,e,t),this.emit("core.validator.enabled",{elements:i,field:e,formValidation:this,validator:t}),this},e.prototype.updateValidatorOption=function(e,t,i,r){return this.fields[e]&&this.fields[e].validators&&this.fields[e].validators[t]&&(this.fields[e].validators[t][i]=r),this},e.prototype.setFieldOptions=function(e,t){return this.fields[e]=t,this},e.prototype.destroy=function(){var e=this;return Object.keys(this.plugins).forEach((function(t){return e.plugins[t].uninstall()})),this.ee.clear(),this.filter.clear(),this.results.clear(),this.plugins={},this},e.prototype.setLocale=function(e,t){return this.locale=e,this.localization=t,this},e.prototype.waterfall=function(e){return e.reduce((function(e,t){return e.then((function(e){return t().then((function(t){return e.push(t),e}))}))}),Promise.resolve([]))},e.prototype.queryElements=function(e){var t=this.fields[e].selector?"#"===this.fields[e].selector.charAt(0)?\'[id="\'.concat(this.fields[e].selector.substring(1),\'"]\'):this.fields[e].selector:\'[name="\'.concat(e.replace(/"/g,\'\\\\"\'),\'"]\');return [].slice.call(this.form.querySelectorAll(t))},e.prototype.normalizeResult=function(e,t,i){var r=this.fields[e].validators[t];return Object.assign({},i,{message:i.message||(r?r.message:"")||(this.localization&&this.localization[t]&&this.localization[t].default?this.localization[t].default:"")||"The field ".concat(e," is not valid")})},e.prototype.toggleValidator=function(e,t,i){var r=this,n=this.fields[t].validators;return i&&n&&n[i]?this.fields[t].validators[i].enabled=e:i||Object.keys(n).forEach((function(i){return r.fields[t].validators[i].enabled=e})),this.updateFieldStatus(t,"NotValidated",i)},e}();var i=function(){function e(e){this.opts=e,this.isEnabled=!0;}return e.prototype.setCore=function(e){return this.core=e,this},e.prototype.enable=function(){return this.isEnabled=!0,this.onEnabled(),this},e.prototype.disable=function(){return this.isEnabled=!1,this.onDisabled(),this},e.prototype.isPluginEnabled=function(){return this.isEnabled},e.prototype.onEnabled=function(){},e.prototype.onDisabled=function(){},e.prototype.install=function(){},e.prototype.uninstall=function(){},e}();var r=function(e,t){var i=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;return i?i.call(e,t):[].slice.call(e.parentElement.querySelectorAll(t)).indexOf(e)>=0},n={call:function(e,t){if("function"==typeof e)return e.apply(this,t);if("string"==typeof e){var i=e;"()"===i.substring(i.length-2)&&(i=i.substring(0,i.length-2));for(var r=i.split("."),n=r.pop(),s=window,l=0,o=r;l<o.length;l++){s=s[o[l]];}return void 0===s[n]?null:s[n].apply(this,t)}},classSet:function(e,t){var i=[],r=[];Object.keys(t).forEach((function(e){e&&(t[e]?i.push(e):r.push(e));})),r.forEach((function(t){return function(e,t){t.split(" ").forEach((function(t){e.classList?e.classList.remove(t):e.className=e.className.replace(t,"");}));}(e,t)})),i.forEach((function(t){return function(e,t){t.split(" ").forEach((function(t){e.classList?e.classList.add(t):" ".concat(e.className," ").indexOf(" ".concat(t," "))&&(e.className+=" ".concat(t));}));}(e,t)}));},closest:function(e,t){for(var i=e;i&&!r(i,t);)i=i.parentElement;return i},fetch:function(e,t){return new Promise((function(i,r){var n,s=Object.assign({},{crossDomain:!1,headers:{},method:"GET",params:{}},t),l=Object.keys(s.params).map((function(e){return "".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(s.params[e]))})).join("&"),o=e.indexOf("?")>-1,a="GET"===s.method?"".concat(e).concat(o?"&":"?").concat(l):e;if(s.crossDomain){var d=document.createElement("script"),c="___FormValidationFetch_".concat(Array(12).fill("").map((function(e){return Math.random().toString(36).charAt(2)})).join(""),"___");window[c]=function(e){delete window[c],i(e);},d.src="".concat(a).concat(o?"&":"?","callback=").concat(c),d.async=!0,d.addEventListener("load",(function(){d.parentNode.removeChild(d);})),d.addEventListener("error",(function(){return r})),document.head.appendChild(d);}else {var u=new XMLHttpRequest;u.open(s.method,a),u.setRequestHeader("X-Requested-With","XMLHttpRequest"),"POST"===s.method&&u.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),Object.keys(s.headers).forEach((function(e){return u.setRequestHeader(e,s.headers[e])})),u.addEventListener("load",(function(){i(JSON.parse(this.responseText));})),u.addEventListener("error",(function(){return r})),u.send((n=s.params,Object.keys(n).map((function(e){return "".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(n[e]))})).join("&")));}}))},format:function(e,t){var i=Array.isArray(t)?t:[t],r=e;return i.forEach((function(e){r=r.replace("%s",e);})),r},hasClass:function(e,t){return e.classList?e.classList.contains(t):new RegExp("(^| )".concat(t,"( |$)"),"gi").test(e.className)},isValidDate:function(e,t,i,r){if(isNaN(e)||isNaN(t)||isNaN(i))return !1;if(e<1e3||e>9999||t<=0||t>12)return !1;if(i<=0||i>[31,e%400==0||e%100!=0&&e%4==0?29:28,31,30,31,30,31,31,30,31,30,31][t-1])return !1;if(!0===r){var n=new Date,s=n.getFullYear(),l=n.getMonth(),o=n.getDate();return e<s||e===s&&t-1<l||e===s&&t-1===l&&i<o}return !0},removeUndefined:function(e){return e?Object.entries(e).reduce((function(e,t){var i=t[0],r=t[1];return void 0===r||(e[i]=r),e}),{}):{}}};index_min$B.Plugin=i,index_min$B.algorithms=e,index_min$B.formValidation=function(e,i){var r=Object.assign({},{fields:{},locale:"en_US",plugins:{},init:function(e){}},i),n=new t(e,r.fields);return n.setLocale(r.locale,r.localization),Object.keys(r.plugins).forEach((function(e){return n.registerPlugin(e,r.plugins[e])})),r.init(n),Object.keys(r.fields).forEach((function(e){return n.addField(e,r.fields[e])})),n},index_min$B.utils=n;\n\treturn index_min$B;\n}\n\nvar cjs$B = {};\n\nvar hasRequiredCjs$B;\n\nfunction requireCjs$B () {\n\tif (hasRequiredCjs$B) return cjs$B;\n\thasRequiredCjs$B = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Implement Luhn validation algorithm\n\t * Credit to https://gist.github.com/ShirtlessKirk/2134376\n\t *\n\t * @see http://en.wikipedia.org/wiki/Luhn\n\t * @param {string} value\n\t * @returns {boolean}\n\t */\n\tfunction luhn(value) {\n\t    var length = value.length;\n\t    var prodArr = [\n\t        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],\n\t        [0, 2, 4, 6, 8, 1, 3, 5, 7, 9],\n\t    ];\n\t    var mul = 0;\n\t    var sum = 0;\n\t    while (length--) {\n\t        sum += prodArr[mul][parseInt(value.charAt(length), 10)];\n\t        mul = 1 - mul;\n\t    }\n\t    return sum % 10 === 0 && sum > 0;\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Implement modulus 11, 10 (ISO 7064) algorithm\n\t *\n\t * @param {string} value\n\t * @returns {boolean}\n\t */\n\tfunction mod11And10(value) {\n\t    var length = value.length;\n\t    var check = 5;\n\t    for (var i = 0; i < length; i++) {\n\t        check = ((((check || 10) * 2) % 11) + parseInt(value.charAt(i), 10)) % 10;\n\t    }\n\t    return check === 1;\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Implements Mod 37, 36 (ISO 7064) algorithm\n\t *\n\t * @param {string} value\n\t * @param {string} [alphabet]\n\t * @returns {boolean}\n\t */\n\tfunction mod37And36(value, alphabet) {\n\t    if (alphabet === void 0) { alphabet = \'0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\'; }\n\t    var length = value.length;\n\t    var modulus = alphabet.length;\n\t    var check = Math.floor(modulus / 2);\n\t    for (var i = 0; i < length; i++) {\n\t        check = ((((check || modulus) * 2) % (modulus + 1)) + alphabet.indexOf(value.charAt(i))) % modulus;\n\t    }\n\t    return check === 1;\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction transform(input) {\n\t    return input\n\t        .split(\'\')\n\t        .map(function (c) {\n\t        var code = c.charCodeAt(0);\n\t        // 65, 66, ..., 90 are the char code of A, B, ..., Z\n\t        return code >= 65 && code <= 90\n\t            ? // Replace A, B, C, ..., Z with 10, 11, ..., 35\n\t                code - 55\n\t            : c;\n\t    })\n\t        .join(\'\')\n\t        .split(\'\')\n\t        .map(function (c) { return parseInt(c, 10); });\n\t}\n\tfunction mod97And10(input) {\n\t    var digits = transform(input);\n\t    var temp = 0;\n\t    var length = digits.length;\n\t    for (var i = 0; i < length - 1; ++i) {\n\t        temp = ((temp + digits[i]) * 10) % 97;\n\t    }\n\t    temp += digits[length - 1];\n\t    return temp % 97 === 1;\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Implement Verhoeff validation algorithm\n\t * Credit to Sergey Petushkov, 2014\n\t *\n\t * @see https://en.wikipedia.org/wiki/Verhoeff_algorithm\n\t * @param {string} value\n\t * @returns {boolean}\n\t */\n\tfunction verhoeff(value) {\n\t    // Multiplication table d\n\t    var d = [\n\t        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],\n\t        [1, 2, 3, 4, 0, 6, 7, 8, 9, 5],\n\t        [2, 3, 4, 0, 1, 7, 8, 9, 5, 6],\n\t        [3, 4, 0, 1, 2, 8, 9, 5, 6, 7],\n\t        [4, 0, 1, 2, 3, 9, 5, 6, 7, 8],\n\t        [5, 9, 8, 7, 6, 0, 4, 3, 2, 1],\n\t        [6, 5, 9, 8, 7, 1, 0, 4, 3, 2],\n\t        [7, 6, 5, 9, 8, 2, 1, 0, 4, 3],\n\t        [8, 7, 6, 5, 9, 3, 2, 1, 0, 4],\n\t        [9, 8, 7, 6, 5, 4, 3, 2, 1, 0],\n\t    ];\n\t    // Permutation table p\n\t    var p = [\n\t        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],\n\t        [1, 5, 7, 6, 2, 8, 3, 0, 9, 4],\n\t        [5, 8, 0, 3, 7, 9, 6, 1, 4, 2],\n\t        [8, 9, 1, 6, 0, 4, 3, 5, 2, 7],\n\t        [9, 4, 5, 3, 1, 2, 6, 8, 7, 0],\n\t        [4, 2, 8, 6, 5, 7, 3, 9, 0, 1],\n\t        [2, 7, 9, 3, 8, 0, 6, 4, 1, 5],\n\t        [7, 0, 4, 6, 9, 1, 3, 2, 5, 8],\n\t    ];\n\t    // Inverse table inv\n\t    var invertedArray = value.reverse();\n\t    var c = 0;\n\t    for (var i = 0; i < invertedArray.length; i++) {\n\t        c = d[c][p[i % 8][invertedArray[i]]];\n\t    }\n\t    return c === 0;\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar index$1 = {\n\t    luhn: luhn,\n\t    mod11And10: mod11And10,\n\t    mod37And36: mod37And36,\n\t    mod97And10: mod97And10,\n\t    verhoeff: verhoeff,\n\t};\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\r\n\tfunction __spreadArray(to, from, pack) {\r\n\t    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n\t        if (ar || !(i in from)) {\r\n\t            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n\t            ar[i] = from[i];\r\n\t        }\r\n\t    }\r\n\t    return to.concat(ar || Array.prototype.slice.call(from));\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * @param {HTMLElement} form The form element\n\t * @param {string} field The field name\n\t * @param {HTMLElement} element The field element\n\t * @param {HTMLElement[]} elements The list of elements which have the same name as `field`\n\t * @return {string}\n\t */\n\tfunction getFieldValue(form, field, element, elements) {\n\t    var type = (element.getAttribute(\'type\') || \'\').toLowerCase();\n\t    var tagName = element.tagName.toLowerCase();\n\t    if (tagName === \'textarea\') {\n\t        return element.value;\n\t    }\n\t    if (tagName === \'select\') {\n\t        var select = element;\n\t        var index = select.selectedIndex;\n\t        return index >= 0 ? select.options.item(index).value : \'\';\n\t    }\n\t    if (tagName === \'input\') {\n\t        if (\'radio\' === type || \'checkbox\' === type) {\n\t            var checked = elements.filter(function (ele) { return ele.checked; }).length;\n\t            return checked === 0 ? \'\' : checked + \'\';\n\t        }\n\t        else {\n\t            return element.value;\n\t        }\n\t    }\n\t    return \'\';\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction emitter() {\n\t    return {\n\t        fns: {},\n\t        clear: function () {\n\t            this.fns = {};\n\t        },\n\t        emit: function (event) {\n\t            var args = [];\n\t            for (var _i = 1; _i < arguments.length; _i++) {\n\t                args[_i - 1] = arguments[_i];\n\t            }\n\t            (this.fns[event] || []).map(function (handler) { return handler.apply(handler, args); });\n\t        },\n\t        off: function (event, func) {\n\t            if (this.fns[event]) {\n\t                var index = this.fns[event].indexOf(func);\n\t                if (index >= 0) {\n\t                    this.fns[event].splice(index, 1);\n\t                }\n\t            }\n\t        },\n\t        on: function (event, func) {\n\t            (this.fns[event] = this.fns[event] || []).push(func);\n\t        },\n\t    };\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction filter() {\n\t    return {\n\t        filters: {},\n\t        add: function (name, func) {\n\t            (this.filters[name] = this.filters[name] || []).push(func);\n\t        },\n\t        clear: function () {\n\t            this.filters = {};\n\t        },\n\t        execute: function (name, defaultValue, args) {\n\t            if (!this.filters[name] || !this.filters[name].length) {\n\t                return defaultValue;\n\t            }\n\t            var result = defaultValue;\n\t            var filters = this.filters[name];\n\t            var count = filters.length;\n\t            for (var i = 0; i < count; i++) {\n\t                result = filters[i].apply(result, args);\n\t            }\n\t            return result;\n\t        },\n\t        remove: function (name, func) {\n\t            if (this.filters[name]) {\n\t                this.filters[name] = this.filters[name].filter(function (f) { return f !== func; });\n\t            }\n\t        },\n\t    };\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar Core = /** @class */ (function () {\n\t    function Core(form, fields) {\n\t        this.fields = {};\n\t        this.elements = {};\n\t        this.ee = emitter();\n\t        this.filter = filter();\n\t        this.plugins = {};\n\t        // Store the result of validation for each field\n\t        this.results = new Map();\n\t        this.validators = {};\n\t        this.form = form;\n\t        this.fields = fields;\n\t    }\n\t    Core.prototype.on = function (event, func) {\n\t        this.ee.on(event, func);\n\t        return this;\n\t    };\n\t    Core.prototype.off = function (event, func) {\n\t        this.ee.off(event, func);\n\t        return this;\n\t    };\n\t    Core.prototype.emit = function (event) {\n\t        var _a;\n\t        var args = [];\n\t        for (var _i = 1; _i < arguments.length; _i++) {\n\t            args[_i - 1] = arguments[_i];\n\t        }\n\t        (_a = this.ee).emit.apply(_a, __spreadArray([event], args, false));\n\t        return this;\n\t    };\n\t    Core.prototype.registerPlugin = function (name, plugin) {\n\t        // Check if whether the plugin is registered\n\t        if (this.plugins[name]) {\n\t            throw new Error("The plguin ".concat(name, " is registered"));\n\t        }\n\t        // Install the plugin\n\t        plugin.setCore(this);\n\t        plugin.install();\n\t        this.plugins[name] = plugin;\n\t        return this;\n\t    };\n\t    Core.prototype.deregisterPlugin = function (name) {\n\t        var plugin = this.plugins[name];\n\t        if (plugin) {\n\t            plugin.uninstall();\n\t        }\n\t        delete this.plugins[name];\n\t        return this;\n\t    };\n\t    Core.prototype.enablePlugin = function (name) {\n\t        var plugin = this.plugins[name];\n\t        if (plugin) {\n\t            plugin.enable();\n\t        }\n\t        return this;\n\t    };\n\t    Core.prototype.disablePlugin = function (name) {\n\t        var plugin = this.plugins[name];\n\t        if (plugin) {\n\t            plugin.disable();\n\t        }\n\t        return this;\n\t    };\n\t    Core.prototype.isPluginEnabled = function (name) {\n\t        var plugin = this.plugins[name];\n\t        return plugin ? plugin.isPluginEnabled() : false;\n\t    };\n\t    Core.prototype.registerValidator = function (name, func) {\n\t        if (this.validators[name]) {\n\t            throw new Error("The validator ".concat(name, " is registered"));\n\t        }\n\t        this.validators[name] = func;\n\t        return this;\n\t    };\n\t    /**\n\t     * Add a filter\n\t     *\n\t     * @param {string} name The name of filter\n\t     * @param {Function} func The filter function\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.registerFilter = function (name, func) {\n\t        this.filter.add(name, func);\n\t        return this;\n\t    };\n\t    /**\n\t     * Remove a filter\n\t     *\n\t     * @param {string} name The name of filter\n\t     * @param {Function} func The filter function\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.deregisterFilter = function (name, func) {\n\t        this.filter.remove(name, func);\n\t        return this;\n\t    };\n\t    /**\n\t     * Execute a filter\n\t     *\n\t     * @param {string} name The name of filter\n\t     * @param {T} defaultValue The default value returns by the filter\n\t     * @param {array} args The filter arguments\n\t     * @returns {T}\n\t     */\n\t    Core.prototype.executeFilter = function (name, defaultValue, args) {\n\t        return this.filter.execute(name, defaultValue, args);\n\t    };\n\t    /**\n\t     * Add a field\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {FieldOptions} options The field options. The options will be merged with the original validator rules\n\t     * if the field is already defined\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.addField = function (field, options) {\n\t        var opts = Object.assign({}, {\n\t            selector: \'\',\n\t            validators: {},\n\t        }, options);\n\t        // Merge the options\n\t        this.fields[field] = this.fields[field]\n\t            ? {\n\t                selector: opts.selector || this.fields[field].selector,\n\t                validators: Object.assign({}, this.fields[field].validators, opts.validators),\n\t            }\n\t            : opts;\n\t        this.elements[field] = this.queryElements(field);\n\t        this.emit(\'core.field.added\', {\n\t            elements: this.elements[field],\n\t            field: field,\n\t            options: this.fields[field],\n\t        });\n\t        return this;\n\t    };\n\t    /**\n\t     * Remove given field by name\n\t     *\n\t     * @param {string} field The field name\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.removeField = function (field) {\n\t        if (!this.fields[field]) {\n\t            throw new Error("The field ".concat(field, " validators are not defined. Please ensure the field is added first"));\n\t        }\n\t        var elements = this.elements[field];\n\t        var options = this.fields[field];\n\t        delete this.elements[field];\n\t        delete this.fields[field];\n\t        this.emit(\'core.field.removed\', {\n\t            elements: elements,\n\t            field: field,\n\t            options: options,\n\t        });\n\t        return this;\n\t    };\n\t    /**\n\t     * Validate all fields\n\t     *\n\t     * @return {Promise<string>}\n\t     */\n\t    Core.prototype.validate = function () {\n\t        var _this = this;\n\t        this.emit(\'core.form.validating\', {\n\t            formValidation: this,\n\t        });\n\t        return this.filter.execute(\'validate-pre\', Promise.resolve(), []).then(function () {\n\t            return Promise.all(Object.keys(_this.fields).map(function (field) { return _this.validateField(field); })).then(function (results) {\n\t                // `results` is an array of `Valid`, `Invalid` and `NotValidated`\n\t                switch (true) {\n\t                    case results.indexOf(\'Invalid\') !== -1:\n\t                        _this.emit(\'core.form.invalid\', {\n\t                            formValidation: _this,\n\t                        });\n\t                        return Promise.resolve(\'Invalid\');\n\t                    case results.indexOf(\'NotValidated\') !== -1:\n\t                        _this.emit(\'core.form.notvalidated\', {\n\t                            formValidation: _this,\n\t                        });\n\t                        return Promise.resolve(\'NotValidated\');\n\t                    default:\n\t                        _this.emit(\'core.form.valid\', {\n\t                            formValidation: _this,\n\t                        });\n\t                        return Promise.resolve(\'Valid\');\n\t                }\n\t            });\n\t        });\n\t    };\n\t    /**\n\t     * Validate a particular field\n\t     *\n\t     * @param {string} field The field name\n\t     * @return {Promise<string>}\n\t     */\n\t    Core.prototype.validateField = function (field) {\n\t        var _this = this;\n\t        // Stop validation process if the field is already validated\n\t        var result = this.results.get(field);\n\t        if (result === \'Valid\' || result === \'Invalid\') {\n\t            return Promise.resolve(result);\n\t        }\n\t        this.emit(\'core.field.validating\', field);\n\t        var elements = this.elements[field];\n\t        if (elements.length === 0) {\n\t            this.emit(\'core.field.valid\', field);\n\t            return Promise.resolve(\'Valid\');\n\t        }\n\t        var type = elements[0].getAttribute(\'type\');\n\t        if (\'radio\' === type || \'checkbox\' === type || elements.length === 1) {\n\t            return this.validateElement(field, elements[0]);\n\t        }\n\t        else {\n\t            return Promise.all(elements.map(function (ele) { return _this.validateElement(field, ele); })).then(function (results) {\n\t                // `results` is an array of `Valid`, `Invalid` and `NotValidated`\n\t                switch (true) {\n\t                    case results.indexOf(\'Invalid\') !== -1:\n\t                        _this.emit(\'core.field.invalid\', field);\n\t                        _this.results.set(field, \'Invalid\');\n\t                        return Promise.resolve(\'Invalid\');\n\t                    case results.indexOf(\'NotValidated\') !== -1:\n\t                        _this.emit(\'core.field.notvalidated\', field);\n\t                        _this.results.delete(field);\n\t                        return Promise.resolve(\'NotValidated\');\n\t                    default:\n\t                        _this.emit(\'core.field.valid\', field);\n\t                        _this.results.set(field, \'Valid\');\n\t                        return Promise.resolve(\'Valid\');\n\t                }\n\t            });\n\t        }\n\t    };\n\t    /**\n\t     * Validate particular element\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {HTMLElement} ele The field element\n\t     * @return {Promise<string>}\n\t     */\n\t    Core.prototype.validateElement = function (field, ele) {\n\t        var _this = this;\n\t        // Reset validation result\n\t        this.results.delete(field);\n\t        var elements = this.elements[field];\n\t        var ignored = this.filter.execute(\'element-ignored\', false, [field, ele, elements]);\n\t        if (ignored) {\n\t            this.emit(\'core.element.ignored\', {\n\t                element: ele,\n\t                elements: elements,\n\t                field: field,\n\t            });\n\t            return Promise.resolve(\'Ignored\');\n\t        }\n\t        var validatorList = this.fields[field].validators;\n\t        this.emit(\'core.element.validating\', {\n\t            element: ele,\n\t            elements: elements,\n\t            field: field,\n\t        });\n\t        var promises = Object.keys(validatorList).map(function (v) {\n\t            return function () { return _this.executeValidator(field, ele, v, validatorList[v]); };\n\t        });\n\t        return this.waterfall(promises)\n\t            .then(function (results) {\n\t            // `results` is an array of `Valid` or `Invalid`\n\t            var isValid = results.indexOf(\'Invalid\') === -1;\n\t            _this.emit(\'core.element.validated\', {\n\t                element: ele,\n\t                elements: elements,\n\t                field: field,\n\t                valid: isValid,\n\t            });\n\t            var type = ele.getAttribute(\'type\');\n\t            if (\'radio\' === type || \'checkbox\' === type || elements.length === 1) {\n\t                _this.emit(isValid ? \'core.field.valid\' : \'core.field.invalid\', field);\n\t            }\n\t            return Promise.resolve(isValid ? \'Valid\' : \'Invalid\');\n\t        })\n\t            .catch(function (reason) {\n\t            // reason is `NotValidated`\n\t            _this.emit(\'core.element.notvalidated\', {\n\t                element: ele,\n\t                elements: elements,\n\t                field: field,\n\t            });\n\t            return Promise.resolve(reason);\n\t        });\n\t    };\n\t    /**\n\t     * Perform given validator on field\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {HTMLElement} ele The field element\n\t     * @param {string} v The validator name\n\t     * @param {ValidatorOptions} opts The validator options\n\t     * @return {Promise<string>}\n\t     */\n\t    Core.prototype.executeValidator = function (field, ele, v, opts) {\n\t        var _this = this;\n\t        var elements = this.elements[field];\n\t        var name = this.filter.execute(\'validator-name\', v, [v, field]);\n\t        opts.message = this.filter.execute(\'validator-message\', opts.message, [this.locale, field, name]);\n\t        // Simply pass the validator if\n\t        // - it isn\'t defined yet\n\t        // - or the associated validator isn\'t enabled\n\t        if (!this.validators[name] || opts.enabled === false) {\n\t            this.emit(\'core.validator.validated\', {\n\t                element: ele,\n\t                elements: elements,\n\t                field: field,\n\t                result: this.normalizeResult(field, name, { valid: true }),\n\t                validator: name,\n\t            });\n\t            return Promise.resolve(\'Valid\');\n\t        }\n\t        var validator = this.validators[name];\n\t        // Get the field value\n\t        var value = this.getElementValue(field, ele, name);\n\t        var willValidate = this.filter.execute(\'field-should-validate\', true, [field, ele, value, v]);\n\t        if (!willValidate) {\n\t            this.emit(\'core.validator.notvalidated\', {\n\t                element: ele,\n\t                elements: elements,\n\t                field: field,\n\t                validator: v,\n\t            });\n\t            return Promise.resolve(\'NotValidated\');\n\t        }\n\t        this.emit(\'core.validator.validating\', {\n\t            element: ele,\n\t            elements: elements,\n\t            field: field,\n\t            validator: v,\n\t        });\n\t        // Perform validation\n\t        var result = validator().validate({\n\t            element: ele,\n\t            elements: elements,\n\t            field: field,\n\t            l10n: this.localization,\n\t            options: opts,\n\t            value: value,\n\t        });\n\t        // Check whether the result is a `Promise`\n\t        var isPromise = \'function\' === typeof result[\'then\'];\n\t        if (isPromise) {\n\t            return result.then(function (r) {\n\t                var data = _this.normalizeResult(field, v, r);\n\t                _this.emit(\'core.validator.validated\', {\n\t                    element: ele,\n\t                    elements: elements,\n\t                    field: field,\n\t                    result: data,\n\t                    validator: v,\n\t                });\n\t                return data.valid ? \'Valid\' : \'Invalid\';\n\t            });\n\t        }\n\t        else {\n\t            var data = this.normalizeResult(field, v, result);\n\t            this.emit(\'core.validator.validated\', {\n\t                element: ele,\n\t                elements: elements,\n\t                field: field,\n\t                result: data,\n\t                validator: v,\n\t            });\n\t            return Promise.resolve(data.valid ? \'Valid\' : \'Invalid\');\n\t        }\n\t    };\n\t    Core.prototype.getElementValue = function (field, ele, validator) {\n\t        var defaultValue = getFieldValue(this.form, field, ele, this.elements[field]);\n\t        return this.filter.execute(\'field-value\', defaultValue, [defaultValue, field, ele, validator]);\n\t    };\n\t    // Some getter methods\n\t    Core.prototype.getElements = function (field) {\n\t        return this.elements[field];\n\t    };\n\t    Core.prototype.getFields = function () {\n\t        return this.fields;\n\t    };\n\t    Core.prototype.getFormElement = function () {\n\t        return this.form;\n\t    };\n\t    Core.prototype.getLocale = function () {\n\t        return this.locale;\n\t    };\n\t    Core.prototype.getPlugin = function (name) {\n\t        return this.plugins[name];\n\t    };\n\t    /**\n\t     * Update the field status\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {string} status The new status\n\t     * @param {string} [validator] The validator name. If it isn\'t specified, all validators will be updated\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.updateFieldStatus = function (field, status, validator) {\n\t        var _this = this;\n\t        var elements = this.elements[field];\n\t        var type = elements[0].getAttribute(\'type\');\n\t        var list = \'radio\' === type || \'checkbox\' === type ? [elements[0]] : elements;\n\t        list.forEach(function (ele) { return _this.updateElementStatus(field, ele, status, validator); });\n\t        if (!validator) {\n\t            switch (status) {\n\t                case \'NotValidated\':\n\t                    this.emit(\'core.field.notvalidated\', field);\n\t                    this.results.delete(field);\n\t                    break;\n\t                case \'Validating\':\n\t                    this.emit(\'core.field.validating\', field);\n\t                    this.results.delete(field);\n\t                    break;\n\t                case \'Valid\':\n\t                    this.emit(\'core.field.valid\', field);\n\t                    this.results.set(field, \'Valid\');\n\t                    break;\n\t                case \'Invalid\':\n\t                    this.emit(\'core.field.invalid\', field);\n\t                    this.results.set(field, \'Invalid\');\n\t                    break;\n\t            }\n\t        }\n\t        else if (status === \'Invalid\') {\n\t            // We need to mark the field as invalid because it doesn\'t pass the `validator`\n\t            this.emit(\'core.field.invalid\', field);\n\t            this.results.set(field, \'Invalid\');\n\t        }\n\t        return this;\n\t    };\n\t    /**\n\t     * Update the element status\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {HTMLElement} ele The field element\n\t     * @param {string} status The new status\n\t     * @param {string} [validator] The validator name. If it isn\'t specified, all validators will be updated\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.updateElementStatus = function (field, ele, status, validator) {\n\t        var _this = this;\n\t        var elements = this.elements[field];\n\t        var fieldValidators = this.fields[field].validators;\n\t        var validatorArr = validator ? [validator] : Object.keys(fieldValidators);\n\t        switch (status) {\n\t            case \'NotValidated\':\n\t                validatorArr.forEach(function (v) {\n\t                    return _this.emit(\'core.validator.notvalidated\', {\n\t                        element: ele,\n\t                        elements: elements,\n\t                        field: field,\n\t                        validator: v,\n\t                    });\n\t                });\n\t                this.emit(\'core.element.notvalidated\', {\n\t                    element: ele,\n\t                    elements: elements,\n\t                    field: field,\n\t                });\n\t                break;\n\t            case \'Validating\':\n\t                validatorArr.forEach(function (v) {\n\t                    return _this.emit(\'core.validator.validating\', {\n\t                        element: ele,\n\t                        elements: elements,\n\t                        field: field,\n\t                        validator: v,\n\t                    });\n\t                });\n\t                this.emit(\'core.element.validating\', {\n\t                    element: ele,\n\t                    elements: elements,\n\t                    field: field,\n\t                });\n\t                break;\n\t            case \'Valid\':\n\t                validatorArr.forEach(function (v) {\n\t                    return _this.emit(\'core.validator.validated\', {\n\t                        element: ele,\n\t                        elements: elements,\n\t                        field: field,\n\t                        result: {\n\t                            message: fieldValidators[v].message,\n\t                            valid: true,\n\t                        },\n\t                        validator: v,\n\t                    });\n\t                });\n\t                this.emit(\'core.element.validated\', {\n\t                    element: ele,\n\t                    elements: elements,\n\t                    field: field,\n\t                    valid: true,\n\t                });\n\t                break;\n\t            case \'Invalid\':\n\t                validatorArr.forEach(function (v) {\n\t                    return _this.emit(\'core.validator.validated\', {\n\t                        element: ele,\n\t                        elements: elements,\n\t                        field: field,\n\t                        result: {\n\t                            message: fieldValidators[v].message,\n\t                            valid: false,\n\t                        },\n\t                        validator: v,\n\t                    });\n\t                });\n\t                this.emit(\'core.element.validated\', {\n\t                    element: ele,\n\t                    elements: elements,\n\t                    field: field,\n\t                    valid: false,\n\t                });\n\t                break;\n\t        }\n\t        return this;\n\t    };\n\t    /**\n\t     * Reset the form. It also clears all the messages, hide the feedback icons, etc.\n\t     *\n\t     * @param {boolean} reset If true, the method resets field value to empty\n\t     * or remove `checked`, `selected` attributes\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.resetForm = function (reset) {\n\t        var _this = this;\n\t        Object.keys(this.fields).forEach(function (field) { return _this.resetField(field, reset); });\n\t        this.emit(\'core.form.reset\', {\n\t            formValidation: this,\n\t            reset: reset,\n\t        });\n\t        return this;\n\t    };\n\t    /**\n\t     * Reset the field. It also clears all the messages, hide the feedback icons, etc.\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {boolean} reset If true, the method resets field value to empty\n\t     * or remove `checked`, `selected` attributes\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.resetField = function (field, reset) {\n\t        // Reset the field element value if needed\n\t        if (reset) {\n\t            var elements = this.elements[field];\n\t            var type_1 = elements[0].getAttribute(\'type\');\n\t            elements.forEach(function (ele) {\n\t                if (\'radio\' === type_1 || \'checkbox\' === type_1) {\n\t                    ele.removeAttribute(\'selected\');\n\t                    ele.removeAttribute(\'checked\');\n\t                    ele.checked = false;\n\t                }\n\t                else {\n\t                    ele.setAttribute(\'value\', \'\');\n\t                    if (ele instanceof HTMLInputElement || ele instanceof HTMLTextAreaElement) {\n\t                        ele.value = \'\';\n\t                    }\n\t                }\n\t            });\n\t        }\n\t        // Mark the field as not validated yet\n\t        this.updateFieldStatus(field, \'NotValidated\');\n\t        this.emit(\'core.field.reset\', {\n\t            field: field,\n\t            reset: reset,\n\t        });\n\t        return this;\n\t    };\n\t    /**\n\t     * Revalidate a particular field. It\'s useful when the field value is effected by third parties\n\t     * (for example, attach another UI library to the field).\n\t     * Since there isn\'t an automatic way for FormValidation to know when the field value is modified in those cases,\n\t     * we need to revalidate the field manually.\n\t     *\n\t     * @param {string} field The field name\n\t     * @return {Promise<string>}\n\t     */\n\t    Core.prototype.revalidateField = function (field) {\n\t        if (!this.fields[field]) {\n\t            return Promise.resolve(\'Ignored\');\n\t        }\n\t        this.updateFieldStatus(field, \'NotValidated\');\n\t        return this.validateField(field);\n\t    };\n\t    /**\n\t     * Disable particular validator for given field\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {string} validator The validator name. If it isn\'t specified, all validators will be disabled\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.disableValidator = function (field, validator) {\n\t        if (!this.fields[field]) {\n\t            return this;\n\t        }\n\t        var elements = this.elements[field];\n\t        this.toggleValidator(false, field, validator);\n\t        this.emit(\'core.validator.disabled\', {\n\t            elements: elements,\n\t            field: field,\n\t            formValidation: this,\n\t            validator: validator,\n\t        });\n\t        return this;\n\t    };\n\t    /**\n\t     * Enable particular validator for given field\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {string} validator The validator name. If it isn\'t specified, all validators will be enabled\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.enableValidator = function (field, validator) {\n\t        if (!this.fields[field]) {\n\t            return this;\n\t        }\n\t        var elements = this.elements[field];\n\t        this.toggleValidator(true, field, validator);\n\t        this.emit(\'core.validator.enabled\', {\n\t            elements: elements,\n\t            field: field,\n\t            formValidation: this,\n\t            validator: validator,\n\t        });\n\t        return this;\n\t    };\n\t    /**\n\t     * Update option of particular validator for given field\n\t     *\n\t     * @param {string} field The field name\n\t     * @param {string} validator The validator name\n\t     * @param {string} name The option\'s name\n\t     * @param {unknown} value The option\'s value\n\t     * @return {Core}\n\t     */\n\t    Core.prototype.updateValidatorOption = function (field, validator, name, value) {\n\t        if (this.fields[field] && this.fields[field].validators && this.fields[field].validators[validator]) {\n\t            this.fields[field].validators[validator][name] = value;\n\t        }\n\t        return this;\n\t    };\n\t    Core.prototype.setFieldOptions = function (field, options) {\n\t        this.fields[field] = options;\n\t        return this;\n\t    };\n\t    Core.prototype.destroy = function () {\n\t        var _this = this;\n\t        // Remove plugins and filters\n\t        Object.keys(this.plugins).forEach(function (id) { return _this.plugins[id].uninstall(); });\n\t        this.ee.clear();\n\t        this.filter.clear();\n\t        this.results.clear();\n\t        this.plugins = {};\n\t        return this;\n\t    };\n\t    Core.prototype.setLocale = function (locale, localization) {\n\t        this.locale = locale;\n\t        this.localization = localization;\n\t        return this;\n\t    };\n\t    Core.prototype.waterfall = function (promises) {\n\t        return promises.reduce(function (p, c) {\n\t            return p.then(function (res) {\n\t                return c().then(function (result) {\n\t                    res.push(result);\n\t                    return res;\n\t                });\n\t            });\n\t        }, Promise.resolve([]));\n\t    };\n\t    Core.prototype.queryElements = function (field) {\n\t        var selector = this.fields[field].selector\n\t            ? // Check if the selector is an ID selector which starts with `#`\n\t                \'#\' === this.fields[field].selector.charAt(0)\n\t                    ? "[id=\\"".concat(this.fields[field].selector.substring(1), "\\"]")\n\t                    : this.fields[field].selector\n\t            : "[name=\\"".concat(field.replace(/"/g, \'\\\\"\'), "\\"]");\n\t        return [].slice.call(this.form.querySelectorAll(selector));\n\t    };\n\t    Core.prototype.normalizeResult = function (field, validator, result) {\n\t        var opts = this.fields[field].validators[validator];\n\t        return Object.assign({}, result, {\n\t            message: result.message ||\n\t                (opts ? opts.message : \'\') ||\n\t                (this.localization && this.localization[validator] && this.localization[validator][\'default\']\n\t                    ? this.localization[validator][\'default\']\n\t                    : \'\') ||\n\t                "The field ".concat(field, " is not valid"),\n\t        });\n\t    };\n\t    Core.prototype.toggleValidator = function (enabled, field, validator) {\n\t        var _this = this;\n\t        var validatorArr = this.fields[field].validators;\n\t        if (validator && validatorArr && validatorArr[validator]) {\n\t            this.fields[field].validators[validator].enabled = enabled;\n\t        }\n\t        else if (!validator) {\n\t            Object.keys(validatorArr).forEach(function (v) { return (_this.fields[field].validators[v].enabled = enabled); });\n\t        }\n\t        return this.updateFieldStatus(field, \'NotValidated\', validator);\n\t    };\n\t    return Core;\n\t}());\n\tfunction formValidation(form, options) {\n\t    var opts = Object.assign({}, {\n\t        fields: {},\n\t        locale: \'en_US\',\n\t        plugins: {},\n\t        init: function (_) { },\n\t    }, options);\n\t    var core = new Core(form, opts.fields);\n\t    core.setLocale(opts.locale, opts.localization);\n\t    // Register plugins\n\t    Object.keys(opts.plugins).forEach(function (name) { return core.registerPlugin(name, opts.plugins[name]); });\n\t    // It\'s the single point that users can do a particular task before adding fields\n\t    // Some initialization tasks must be done at that point\n\t    opts.init(core);\n\t    // and add fields\n\t    Object.keys(opts.fields).forEach(function (field) { return core.addField(field, opts.fields[field]); });\n\t    return core;\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar Plugin = /** @class */ (function () {\n\t    function Plugin(opts) {\n\t        this.opts = opts;\n\t        this.isEnabled = true;\n\t    }\n\t    Plugin.prototype.setCore = function (core) {\n\t        this.core = core;\n\t        return this;\n\t    };\n\t    Plugin.prototype.enable = function () {\n\t        this.isEnabled = true;\n\t        this.onEnabled();\n\t        return this;\n\t    };\n\t    Plugin.prototype.disable = function () {\n\t        this.isEnabled = false;\n\t        this.onDisabled();\n\t        return this;\n\t    };\n\t    Plugin.prototype.isPluginEnabled = function () {\n\t        return this.isEnabled;\n\t    };\n\t    Plugin.prototype.onEnabled = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n\t    Plugin.prototype.onDisabled = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n\t    Plugin.prototype.install = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n\t    Plugin.prototype.uninstall = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n\t    return Plugin;\n\t}());\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Execute a callback function\n\t *\n\t * @param {Function | string} functionName Can be\n\t * - name of global function\n\t * - name of namespace function (such as A.B.C)\n\t * - a function\n\t * @param {any[]} args The callback arguments\n\t * @return {any}\n\t */\n\tfunction call(functionName, args) {\n\t    if (\'function\' === typeof functionName) {\n\t        return functionName.apply(this, args);\n\t    }\n\t    else if (\'string\' === typeof functionName) {\n\t        // Node that it doesn\'t support node.js based environment because we are trying to access `window`\n\t        var name_1 = functionName;\n\t        if (\'()\' === name_1.substring(name_1.length - 2)) {\n\t            name_1 = name_1.substring(0, name_1.length - 2);\n\t        }\n\t        var ns = name_1.split(\'.\');\n\t        var func = ns.pop();\n\t        var context_1 = window;\n\t        for (var _i = 0, ns_1 = ns; _i < ns_1.length; _i++) {\n\t            var t = ns_1[_i];\n\t            context_1 = context_1[t];\n\t        }\n\t        return typeof context_1[func] === \'undefined\' ? null : context_1[func].apply(this, args);\n\t    }\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar addClass = function (element, classes) {\n\t    classes.split(\' \').forEach(function (clazz) {\n\t        if (element.classList) {\n\t            element.classList.add(clazz);\n\t        }\n\t        else if (" ".concat(element.className, " ").indexOf(" ".concat(clazz, " "))) {\n\t            element.className += " ".concat(clazz);\n\t        }\n\t    });\n\t};\n\tvar removeClass = function (element, classes) {\n\t    classes.split(\' \').forEach(function (clazz) {\n\t        element.classList\n\t            ? element.classList.remove(clazz)\n\t            : (element.className = element.className.replace(clazz, \'\'));\n\t    });\n\t};\n\tvar classSet = function (element, classes) {\n\t    var adding = [];\n\t    var removing = [];\n\t    Object.keys(classes).forEach(function (clazz) {\n\t        if (clazz) {\n\t            classes[clazz] ? adding.push(clazz) : removing.push(clazz);\n\t        }\n\t    });\n\t    // Always remove before adding class because there might be a class which belong to both sets.\n\t    // For example, the element will have class `a` after calling\n\t    //  ```\n\t    //  classSet(element, {\n\t    //      \'a a1 a2\': true,\n\t    //      \'a b1 b2\': false\n\t    //  })\n\t    //  ```\n\t    removing.forEach(function (clazz) { return removeClass(element, clazz); });\n\t    adding.forEach(function (clazz) { return addClass(element, clazz); });\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar matches = function (element, selector) {\n\t    var nativeMatches = element.matches ||\n\t        element.webkitMatchesSelector ||\n\t        element[\'mozMatchesSelector\'] ||\n\t        element[\'msMatchesSelector\'];\n\t    if (nativeMatches) {\n\t        return nativeMatches.call(element, selector);\n\t    }\n\t    // In case `matchesselector` isn\'t supported (such as IE10)\n\t    // See http://caniuse.com/matchesselector\n\t    var nodes = [].slice.call(element.parentElement.querySelectorAll(selector));\n\t    return nodes.indexOf(element) >= 0;\n\t};\n\tvar closest = function (element, selector) {\n\t    var ele = element;\n\t    while (ele) {\n\t        if (matches(ele, selector)) {\n\t            break;\n\t        }\n\t        ele = ele.parentElement;\n\t    }\n\t    return ele;\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar generateString = function (length) {\n\t    return Array(length)\n\t        .fill(\'\')\n\t        .map(function (v) { return Math.random().toString(36).charAt(2); })\n\t        .join(\'\');\n\t};\n\tvar fetch = function (url, options) {\n\t    var toQuery = function (obj) {\n\t        return Object.keys(obj)\n\t            .map(function (k) { return "".concat(encodeURIComponent(k), "=").concat(encodeURIComponent(obj[k])); })\n\t            .join(\'&\');\n\t    };\n\t    return new Promise(function (resolve, reject) {\n\t        var opts = Object.assign({}, {\n\t            crossDomain: false,\n\t            headers: {},\n\t            method: \'GET\',\n\t            params: {},\n\t        }, options);\n\t        // Build the params for GET request\n\t        var params = Object.keys(opts.params)\n\t            .map(function (k) { return "".concat(encodeURIComponent(k), "=").concat(encodeURIComponent(opts.params[k])); })\n\t            .join(\'&\');\n\t        var hasQuery = url.indexOf(\'?\') > -1;\n\t        var requestUrl = \'GET\' === opts.method ? "".concat(url).concat(hasQuery ? \'&\' : \'?\').concat(params) : url;\n\t        if (opts.crossDomain) {\n\t            // User is making cross domain request\n\t            var script_1 = document.createElement(\'script\');\n\t            // In some very fast systems, the different `Date.now()` invocations can return the same value\n\t            // which leads to the issue where there are multiple remove validators are used, for example.\n\t            // Appending it with a generated random string can fix the value\n\t            var callback_1 = "___FormValidationFetch_".concat(generateString(12), "___");\n\t            window[callback_1] = function (data) {\n\t                delete window[callback_1];\n\t                resolve(data);\n\t            };\n\t            script_1.src = "".concat(requestUrl).concat(hasQuery ? \'&\' : \'?\', "callback=").concat(callback_1);\n\t            script_1.async = true;\n\t            script_1.addEventListener(\'load\', function () {\n\t                script_1.parentNode.removeChild(script_1);\n\t            });\n\t            script_1.addEventListener(\'error\', function () { return reject; });\n\t            document.head.appendChild(script_1);\n\t        }\n\t        else {\n\t            var request_1 = new XMLHttpRequest();\n\t            request_1.open(opts.method, requestUrl);\n\t            // Set the headers\n\t            request_1.setRequestHeader(\'X-Requested-With\', \'XMLHttpRequest\');\n\t            if (\'POST\' === opts.method) {\n\t                request_1.setRequestHeader(\'Content-Type\', \'application/x-www-form-urlencoded\');\n\t            }\n\t            Object.keys(opts.headers).forEach(function (k) { return request_1.setRequestHeader(k, opts.headers[k]); });\n\t            request_1.addEventListener(\'load\', function () {\n\t                // Cannot use arrow function here due to the `this` scope\n\t                resolve(JSON.parse(this.responseText));\n\t            });\n\t            request_1.addEventListener(\'error\', function () { return reject; });\n\t            // GET request will ignore the passed data here\n\t            request_1.send(toQuery(opts.params));\n\t        }\n\t    });\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Format a string\n\t * It\'s used to format the error message\n\t * format(\'The field must between %s and %s\', [10, 20]) = \'The field must between 10 and 20\'\n\t *\n\t * @param {string} message\n\t * @param {string|string[]} parameters\n\t * @returns {string}\n\t */\n\tvar format = function (message, parameters) {\n\t    var params = Array.isArray(parameters) ? parameters : [parameters];\n\t    var output = message;\n\t    params.forEach(function (p) {\n\t        output = output.replace(\'%s\', p);\n\t    });\n\t    return output;\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar hasClass = function (element, clazz) {\n\t    return element.classList\n\t        ? element.classList.contains(clazz)\n\t        : new RegExp("(^| )".concat(clazz, "( |$)"), \'gi\').test(element.className);\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Validate a date\n\t *\n\t * @param {string} year The full year in 4 digits\n\t * @param {string} month The month number\n\t * @param {string} day The day number\n\t * @param {boolean} [notInFuture] If true, the date must not be in the future\n\t * @returns {boolean}\n\t */\n\tvar isValidDate = function (year, month, day, notInFuture) {\n\t    if (isNaN(year) || isNaN(month) || isNaN(day)) {\n\t        return false;\n\t    }\n\t    if (year < 1000 || year > 9999 || month <= 0 || month > 12) {\n\t        return false;\n\t    }\n\t    var numDays = [\n\t        31,\n\t        // Update the number of days in Feb of leap year\n\t        year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0) ? 29 : 28,\n\t        31,\n\t        30,\n\t        31,\n\t        30,\n\t        31,\n\t        31,\n\t        30,\n\t        31,\n\t        30,\n\t        31,\n\t    ];\n\t    // Check the day\n\t    if (day <= 0 || day > numDays[month - 1]) {\n\t        return false;\n\t    }\n\t    if (notInFuture === true) {\n\t        var currentDate = new Date();\n\t        var currentYear = currentDate.getFullYear();\n\t        var currentMonth = currentDate.getMonth();\n\t        var currentDay = currentDate.getDate();\n\t        return (year < currentYear ||\n\t            (year === currentYear && month - 1 < currentMonth) ||\n\t            (year === currentYear && month - 1 === currentMonth && day < currentDay));\n\t    }\n\t    return true;\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = function (obj) {\n\t    return obj\n\t        ? Object.entries(obj).reduce(function (a, _a) {\n\t            var k = _a[0], v = _a[1];\n\t            return (v === undefined ? a : ((a[k] = v), a));\n\t        }, {})\n\t        : {};\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar index = {\n\t    call: call,\n\t    classSet: classSet,\n\t    closest: closest,\n\t    fetch: fetch,\n\t    format: format,\n\t    hasClass: hasClass,\n\t    isValidDate: isValidDate,\n\t    removeUndefined: removeUndefined,\n\t};\n\n\tcjs$B.Plugin = Plugin;\n\tcjs$B.algorithms = index$1;\n\tcjs$B.formValidation = formValidation;\n\tcjs$B.utils = index;\n\treturn cjs$B;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$B.exports = requireCjs$B();\n}\n\nvar libExports$B = lib$B.exports;\n\nvar lib$A = {exports: {}};\n\nvar index_min$A = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-alias\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$A;\n\nfunction requireIndex_min$A () {\n\tif (hasRequiredIndex_min$A) return index_min$A;\n\thasRequiredIndex_min$A = 1;\nvar t=libExports$B,r=function(t,o){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r;}||function(t,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o]);},r(t,o)};var o=function(t){function o(r){var o=t.call(this,r)||this;return o.opts=r||{},o.validatorNameFilter=o.getValidatorName.bind(o),o}return function(t,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function e(){this.constructor=t;}r(t,o),t.prototype=null===o?Object.create(o):(e.prototype=o.prototype,new e);}(o,t),o.prototype.install=function(){this.core.registerFilter("validator-name",this.validatorNameFilter);},o.prototype.uninstall=function(){this.core.deregisterFilter("validator-name",this.validatorNameFilter);},o.prototype.getValidatorName=function(t,r){return this.isEnabled&&this.opts[t]||t},o}(t.Plugin);index_min$A.Alias=o;\n\treturn index_min$A;\n}\n\nvar cjs$A = {};\n\nvar hasRequiredCjs$A;\n\nfunction requireCjs$A () {\n\tif (hasRequiredCjs$A) return cjs$A;\n\thasRequiredCjs$A = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * This plugin allows to use multiple instances of the same validator by defining alias.\n\t * ```\n\t *  formValidation(form, {\n\t *      fields: {\n\t *          email: {\n\t *              validators: {\n\t *                  required: ...,\n\t *                  pattern: ...,\n\t *                  regexp: ...\n\t *              }\n\t *          }\n\t *      },\n\t *      plugins: {\n\t *          alias: new Alias({\n\t *              required: \'notEmpty\',\n\t *              pattern: \'regexp\'\n\t *          })\n\t *      }\n\t *  })\n\t * ```\n\t * Then, you can use the `required`, `pattern` as the same as `notEmpty`, `regexp` validators.\n\t */\n\tvar Alias = /** @class */ (function (_super) {\n\t    __extends(Alias, _super);\n\t    function Alias(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.opts = opts || {};\n\t        _this.validatorNameFilter = _this.getValidatorName.bind(_this);\n\t        return _this;\n\t    }\n\t    Alias.prototype.install = function () {\n\t        this.core.registerFilter(\'validator-name\', this.validatorNameFilter);\n\t    };\n\t    Alias.prototype.uninstall = function () {\n\t        this.core.deregisterFilter(\'validator-name\', this.validatorNameFilter);\n\t    };\n\t    Alias.prototype.getValidatorName = function (validatorName, _field) {\n\t        return this.isEnabled ? this.opts[validatorName] || validatorName : validatorName;\n\t    };\n\t    return Alias;\n\t}(core.Plugin));\n\n\tcjs$A.Alias = Alias;\n\treturn cjs$A;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$A.exports = requireCjs$A();\n}\n\nvar libExports$A = lib$A.exports;\n\nvar lib$z = {exports: {}};\n\nvar index_min$z = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-aria\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$z;\n\nfunction requireIndex_min$z () {\n\tif (hasRequiredIndex_min$z) return index_min$z;\n\thasRequiredIndex_min$z = 1;\nvar e=libExports$B,t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);},t(e,i)};var i=function(e){function i(){var t=e.call(this,{})||this;return t.elementValidatedHandler=t.onElementValidated.bind(t),t.fieldValidHandler=t.onFieldValid.bind(t),t.fieldInvalidHandler=t.onFieldInvalid.bind(t),t.messageDisplayedHandler=t.onMessageDisplayed.bind(t),t}return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e;}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n);}(i,e),i.prototype.install=function(){this.core.on("core.field.valid",this.fieldValidHandler).on("core.field.invalid",this.fieldInvalidHandler).on("core.element.validated",this.elementValidatedHandler).on("plugins.message.displayed",this.messageDisplayedHandler);},i.prototype.uninstall=function(){this.core.off("core.field.valid",this.fieldValidHandler).off("core.field.invalid",this.fieldInvalidHandler).off("core.element.validated",this.elementValidatedHandler).off("plugins.message.displayed",this.messageDisplayedHandler);},i.prototype.onElementValidated=function(e){e.valid&&(e.element.setAttribute("aria-invalid","false"),e.element.removeAttribute("aria-describedby"));},i.prototype.onFieldValid=function(e){var t=this.core.getElements(e);t&&t.forEach((function(e){e.setAttribute("aria-invalid","false"),e.removeAttribute("aria-describedby");}));},i.prototype.onFieldInvalid=function(e){var t=this.core.getElements(e);t&&t.forEach((function(e){return e.setAttribute("aria-invalid","true")}));},i.prototype.onMessageDisplayed=function(e){e.messageElement.setAttribute("role","alert"),e.messageElement.setAttribute("aria-hidden","false");var t=this.core.getElements(e.field),i=t.indexOf(e.element),n="js-fv-".concat(e.field,"-").concat(i,"-").concat(Date.now(),"-message");e.messageElement.setAttribute("id",n),e.element.setAttribute("aria-describedby",n);var a=e.element.getAttribute("type");"radio"!==a&&"checkbox"!==a||t.forEach((function(e){return e.setAttribute("aria-describedby",n)}));},i}(e.Plugin);index_min$z.Aria=i;\n\treturn index_min$z;\n}\n\nvar cjs$z = {};\n\nvar hasRequiredCjs$z;\n\nfunction requireCjs$z () {\n\tif (hasRequiredCjs$z) return cjs$z;\n\thasRequiredCjs$z = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * This plugin adds ARIA attributes based on the field validity.\n\t * The list include:\n\t *  - `aria-invalid`, `aria-describedby` for field element\n\t *  - `aria-hidden`, `role` for associated message element\n\t * @see https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/ARIA_Techniques\n\t */\n\tvar Aria = /** @class */ (function (_super) {\n\t    __extends(Aria, _super);\n\t    function Aria() {\n\t        var _this = _super.call(this, {}) || this;\n\t        _this.elementValidatedHandler = _this.onElementValidated.bind(_this);\n\t        _this.fieldValidHandler = _this.onFieldValid.bind(_this);\n\t        _this.fieldInvalidHandler = _this.onFieldInvalid.bind(_this);\n\t        _this.messageDisplayedHandler = _this.onMessageDisplayed.bind(_this);\n\t        return _this;\n\t    }\n\t    Aria.prototype.install = function () {\n\t        this.core\n\t            .on(\'core.field.valid\', this.fieldValidHandler)\n\t            .on(\'core.field.invalid\', this.fieldInvalidHandler)\n\t            .on(\'core.element.validated\', this.elementValidatedHandler)\n\t            .on(\'plugins.message.displayed\', this.messageDisplayedHandler);\n\t    };\n\t    Aria.prototype.uninstall = function () {\n\t        this.core\n\t            .off(\'core.field.valid\', this.fieldValidHandler)\n\t            .off(\'core.field.invalid\', this.fieldInvalidHandler)\n\t            .off(\'core.element.validated\', this.elementValidatedHandler)\n\t            .off(\'plugins.message.displayed\', this.messageDisplayedHandler);\n\t    };\n\t    Aria.prototype.onElementValidated = function (e) {\n\t        if (e.valid) {\n\t            e.element.setAttribute(\'aria-invalid\', \'false\');\n\t            e.element.removeAttribute(\'aria-describedby\');\n\t        }\n\t    };\n\t    Aria.prototype.onFieldValid = function (field) {\n\t        var elements = this.core.getElements(field);\n\t        if (elements) {\n\t            elements.forEach(function (ele) {\n\t                ele.setAttribute(\'aria-invalid\', \'false\');\n\t                ele.removeAttribute(\'aria-describedby\');\n\t            });\n\t        }\n\t    };\n\t    Aria.prototype.onFieldInvalid = function (field) {\n\t        var elements = this.core.getElements(field);\n\t        if (elements) {\n\t            elements.forEach(function (ele) { return ele.setAttribute(\'aria-invalid\', \'true\'); });\n\t        }\n\t    };\n\t    Aria.prototype.onMessageDisplayed = function (e) {\n\t        e.messageElement.setAttribute(\'role\', \'alert\');\n\t        e.messageElement.setAttribute(\'aria-hidden\', \'false\');\n\t        var elements = this.core.getElements(e.field);\n\t        var index = elements.indexOf(e.element);\n\t        var id = "js-fv-".concat(e.field, "-").concat(index, "-").concat(Date.now(), "-message");\n\t        e.messageElement.setAttribute(\'id\', id);\n\t        e.element.setAttribute(\'aria-describedby\', id);\n\t        var type = e.element.getAttribute(\'type\');\n\t        if (\'radio\' === type || \'checkbox\' === type) {\n\t            elements.forEach(function (ele) { return ele.setAttribute(\'aria-describedby\', id); });\n\t        }\n\t    };\n\t    return Aria;\n\t}(core.Plugin));\n\n\tcjs$z.Aria = Aria;\n\treturn cjs$z;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$z.exports = requireCjs$z();\n}\n\nvar libExports$z = lib$z.exports;\n\nvar lib$y = {exports: {}};\n\nvar index_min$y = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-declarative\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$y;\n\nfunction requireIndex_min$y () {\n\tif (hasRequiredIndex_min$y) return index_min$y;\n\thasRequiredIndex_min$y = 1;\nvar e=libExports$B,t=function(e,a){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);},t(e,a)};var a=function(e){function a(t){var a=e.call(this,t)||this;return a.addedFields=new Map,a.opts=Object.assign({},{html5Input:!1,pluginPrefix:"data-fvp-",prefix:"data-fv-"},t),a.fieldAddedHandler=a.onFieldAdded.bind(a),a.fieldRemovedHandler=a.onFieldRemoved.bind(a),a}return function(e,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function r(){this.constructor=e;}t(e,a),e.prototype=null===a?Object.create(a):(r.prototype=a.prototype,new r);}(a,e),a.prototype.install=function(){var e=this;this.parsePlugins();var t=this.parseOptions();Object.keys(t).forEach((function(a){e.addedFields.has(a)||e.addedFields.set(a,!0),e.core.addField(a,t[a]);})),this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler);},a.prototype.uninstall=function(){this.addedFields.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler);},a.prototype.onFieldAdded=function(e){var t=this,a=e.elements;a&&0!==a.length&&!this.addedFields.has(e.field)&&(this.addedFields.set(e.field,!0),a.forEach((function(a){var r=t.parseElement(a);if(!t.isEmptyOption(r)){var n={selector:e.options.selector,validators:Object.assign({},e.options.validators||{},r.validators)};t.core.setFieldOptions(e.field,n);}})));},a.prototype.onFieldRemoved=function(e){e.field&&this.addedFields.has(e.field)&&this.addedFields.delete(e.field);},a.prototype.parseOptions=function(){var e=this,t=this.opts.prefix,a={},r=this.core.getFields(),n=this.core.getFormElement();return [].slice.call(n.querySelectorAll("[name], [".concat(t,"field]"))).forEach((function(r){var n=e.parseElement(r);if(!e.isEmptyOption(n)){var i=r.getAttribute("name")||r.getAttribute("".concat(t,"field"));a[i]=Object.assign({},a[i],n);}})),Object.keys(a).forEach((function(e){Object.keys(a[e].validators).forEach((function(t){a[e].validators[t].enabled=a[e].validators[t].enabled||!1,r[e]&&r[e].validators&&r[e].validators[t]&&Object.assign(a[e].validators[t],r[e].validators[t]);}));})),Object.assign({},r,a)},a.prototype.createPluginInstance=function(e,t){for(var a=e.split("."),r=window||this,n=0,i=a.length;n<i;n++)r=r[a[n]];if("function"!=typeof r)throw new Error("the plugin ".concat(e," doesn\'t exist"));return new r(t)},a.prototype.parsePlugins=function(){for(var e,t=this,a=this.core.getFormElement(),r=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),n=a.attributes.length,i={},s=0;s<n;s++){var o=a.attributes[s].name,l=a.attributes[s].value,d=r.exec(o);if(d&&4===d.length){var c=this.toCamelCase(d[1]);i[c]=Object.assign({},d[3]?((e={})[this.toCamelCase(d[3])]=l,e):{enabled:""===l||"true"===l},i[c]);}}Object.keys(i).forEach((function(e){var a=i[e],r=a.enabled,n=a.class;if(r&&n){delete a.enabled,delete a.clazz;var s=t.createPluginInstance(n,a);t.core.registerPlugin(e,s);}}));},a.prototype.isEmptyOption=function(e){var t=e.validators;return 0===Object.keys(t).length&&t.constructor===Object},a.prototype.parseElement=function(e){for(var t=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),a=e.attributes.length,r={},n=e.getAttribute("type"),i=0;i<a;i++){var s=e.attributes[i].name,o=e.attributes[i].value;if(this.opts.html5Input)switch(!0){case"minlength"===s:r.stringLength=Object.assign({},{enabled:!0,min:parseInt(o,10)},r.stringLength);break;case"maxlength"===s:r.stringLength=Object.assign({},{enabled:!0,max:parseInt(o,10)},r.stringLength);break;case"pattern"===s:r.regexp=Object.assign({},{enabled:!0,regexp:o},r.regexp);break;case"required"===s:r.notEmpty=Object.assign({},{enabled:!0},r.notEmpty);break;case"type"===s&&"color"===o:r.color=Object.assign({},{enabled:!0,type:"hex"},r.color);break;case"type"===s&&"email"===o:r.emailAddress=Object.assign({},{enabled:!0},r.emailAddress);break;case"type"===s&&"url"===o:r.uri=Object.assign({},{enabled:!0},r.uri);break;case"type"===s&&"range"===o:r.between=Object.assign({},{enabled:!0,max:parseFloat(e.getAttribute("max")),min:parseFloat(e.getAttribute("min"))},r.between);break;case"min"===s&&"date"!==n&&"range"!==n:r.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(o)},r.greaterThan);break;case"max"===s&&"date"!==n&&"range"!==n:r.lessThan=Object.assign({},{enabled:!0,max:parseFloat(o)},r.lessThan);}var l=t.exec(s);if(l&&4===l.length){var d=this.toCamelCase(l[1]);r[d]||(r[d]={}),l[3]?r[d][this.toCamelCase(l[3])]=this.normalizeValue(o):!0===r[d].enabled&&!1===r[d].enabled||(r[d].enabled=""===o||"true"===o);}}return {validators:r}},a.prototype.normalizeValue=function(e){return "true"===e||""===e||"false"!==e&&e},a.prototype.toUpperCase=function(e){return e.charAt(1).toUpperCase()},a.prototype.toCamelCase=function(e){return e.replace(/-./g,this.toUpperCase)},a}(e.Plugin);index_min$y.Declarative=a;\n\treturn index_min$y;\n}\n\nvar cjs$y = {};\n\nvar hasRequiredCjs$y;\n\nfunction requireCjs$y () {\n\tif (hasRequiredCjs$y) return cjs$y;\n\thasRequiredCjs$y = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * This plugin provides the ability of declaring validator options via HTML attributes.\n\t * All attributes are declared in lowercase\n\t * ```\n\t *  <input\n\t *      data-fv-field="${fieldName}"\n\t *      data-fv-{validator}="true"\n\t *      data-fv-{validator}___{option}="..." />\n\t * ```\n\t */\n\tvar Declarative = /** @class */ (function (_super) {\n\t    __extends(Declarative, _super);\n\t    function Declarative(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.addedFields = new Map();\n\t        _this.opts = Object.assign({}, {\n\t            html5Input: false,\n\t            pluginPrefix: \'data-fvp-\',\n\t            prefix: \'data-fv-\',\n\t        }, opts);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n\t        return _this;\n\t    }\n\t    Declarative.prototype.install = function () {\n\t        var _this = this;\n\t        // Parse the plugin options\n\t        this.parsePlugins();\n\t        var opts = this.parseOptions();\n\t        Object.keys(opts).forEach(function (field) {\n\t            if (!_this.addedFields.has(field)) {\n\t                _this.addedFields.set(field, true);\n\t            }\n\t            _this.core.addField(field, opts[field]);\n\t        });\n\t        this.core.on(\'core.field.added\', this.fieldAddedHandler).on(\'core.field.removed\', this.fieldRemovedHandler);\n\t    };\n\t    Declarative.prototype.uninstall = function () {\n\t        this.addedFields.clear();\n\t        this.core.off(\'core.field.added\', this.fieldAddedHandler).off(\'core.field.removed\', this.fieldRemovedHandler);\n\t    };\n\t    Declarative.prototype.onFieldAdded = function (e) {\n\t        var _this = this;\n\t        var elements = e.elements;\n\t        // Don\'t add the element which is already available in the field lists\n\t        // Otherwise, it can cause an infinite loop\n\t        if (!elements || elements.length === 0 || this.addedFields.has(e.field)) {\n\t            return;\n\t        }\n\t        this.addedFields.set(e.field, true);\n\t        elements.forEach(function (ele) {\n\t            var declarativeOptions = _this.parseElement(ele);\n\t            if (!_this.isEmptyOption(declarativeOptions)) {\n\t                // Update validator options\n\t                var mergeOptions = {\n\t                    selector: e.options.selector,\n\t                    validators: Object.assign({}, e.options.validators || {}, declarativeOptions.validators),\n\t                };\n\t                _this.core.setFieldOptions(e.field, mergeOptions);\n\t            }\n\t        });\n\t    };\n\t    Declarative.prototype.onFieldRemoved = function (e) {\n\t        if (e.field && this.addedFields.has(e.field)) {\n\t            this.addedFields.delete(e.field);\n\t        }\n\t    };\n\t    Declarative.prototype.parseOptions = function () {\n\t        var _this = this;\n\t        // Find all fields which have either `name` or `data-fv-field` attribute\n\t        var prefix = this.opts.prefix;\n\t        var opts = {};\n\t        var fields = this.core.getFields();\n\t        var form = this.core.getFormElement();\n\t        var elements = [].slice.call(form.querySelectorAll("[name], [".concat(prefix, "field]")));\n\t        elements.forEach(function (ele) {\n\t            var validators = _this.parseElement(ele);\n\t            // Do not try to merge the options if it\'s empty\n\t            // For instance, there are multiple elements having the same name,\n\t            // we only set the HTML attribute to one of them\n\t            if (!_this.isEmptyOption(validators)) {\n\t                var field = ele.getAttribute(\'name\') || ele.getAttribute("".concat(prefix, "field"));\n\t                opts[field] = Object.assign({}, opts[field], validators);\n\t            }\n\t        });\n\t        Object.keys(opts).forEach(function (field) {\n\t            Object.keys(opts[field].validators).forEach(function (v) {\n\t                // Set the `enabled` key to `false` if it isn\'t set\n\t                // (the data-fv-{validator} attribute is missing, for example)\n\t                opts[field].validators[v].enabled = opts[field].validators[v].enabled || false;\n\t                // Mix the options in declarative and programmatic modes\n\t                if (fields[field] && fields[field].validators && fields[field].validators[v]) {\n\t                    Object.assign(opts[field].validators[v], fields[field].validators[v]);\n\t                }\n\t            });\n\t        });\n\t        return Object.assign({}, fields, opts);\n\t    };\n\t    Declarative.prototype.createPluginInstance = function (clazz, opts) {\n\t        var arr = clazz.split(\'.\');\n\t        // TODO: Find a safer way to create a plugin instance from the class\n\t        // Currently, I have to use `any` here instead of a construtable interface\n\t        var fn = window || this; // eslint-disable-line @typescript-eslint/no-explicit-any\n\t        for (var i = 0, len = arr.length; i < len; i++) {\n\t            fn = fn[arr[i]];\n\t        }\n\t        if (typeof fn !== \'function\') {\n\t            throw new Error("the plugin ".concat(clazz, " doesn\'t exist"));\n\t        }\n\t        return new fn(opts);\n\t    };\n\t    Declarative.prototype.parsePlugins = function () {\n\t        var _a;\n\t        var _this = this;\n\t        var form = this.core.getFormElement();\n\t        var reg = new RegExp("^".concat(this.opts.pluginPrefix, "([a-z0-9-]+)(___)*([a-z0-9-]+)*$"));\n\t        var numAttributes = form.attributes.length;\n\t        var plugins = {};\n\t        for (var i = 0; i < numAttributes; i++) {\n\t            var name_1 = form.attributes[i].name;\n\t            var value = form.attributes[i].value;\n\t            var items = reg.exec(name_1);\n\t            if (items && items.length === 4) {\n\t                var pluginName = this.toCamelCase(items[1]);\n\t                plugins[pluginName] = Object.assign({}, items[3] ? (_a = {}, _a[this.toCamelCase(items[3])] = value, _a) : { enabled: \'\' === value || \'true\' === value }, plugins[pluginName]);\n\t            }\n\t        }\n\t        Object.keys(plugins).forEach(function (pluginName) {\n\t            var opts = plugins[pluginName];\n\t            var enabled = opts[\'enabled\'];\n\t            var clazz = opts[\'class\'];\n\t            if (enabled && clazz) {\n\t                delete opts[\'enabled\'];\n\t                delete opts[\'clazz\'];\n\t                var p = _this.createPluginInstance(clazz, opts);\n\t                _this.core.registerPlugin(pluginName, p);\n\t            }\n\t        });\n\t    };\n\t    Declarative.prototype.isEmptyOption = function (opts) {\n\t        var validators = opts.validators;\n\t        return Object.keys(validators).length === 0 && validators.constructor === Object;\n\t    };\n\t    Declarative.prototype.parseElement = function (ele) {\n\t        var reg = new RegExp("^".concat(this.opts.prefix, "([a-z0-9-]+)(___)*([a-z0-9-]+)*$"));\n\t        var numAttributes = ele.attributes.length;\n\t        var opts = {};\n\t        var type = ele.getAttribute(\'type\');\n\t        for (var i = 0; i < numAttributes; i++) {\n\t            var name_2 = ele.attributes[i].name;\n\t            var value = ele.attributes[i].value;\n\t            if (this.opts.html5Input) {\n\t                switch (true) {\n\t                    case \'minlength\' === name_2:\n\t                        opts[\'stringLength\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            min: parseInt(value, 10),\n\t                        }, opts[\'stringLength\']);\n\t                        break;\n\t                    case \'maxlength\' === name_2:\n\t                        opts[\'stringLength\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            max: parseInt(value, 10),\n\t                        }, opts[\'stringLength\']);\n\t                        break;\n\t                    case \'pattern\' === name_2:\n\t                        opts[\'regexp\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            regexp: value,\n\t                        }, opts[\'regexp\']);\n\t                        break;\n\t                    case \'required\' === name_2:\n\t                        opts[\'notEmpty\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                        }, opts[\'notEmpty\']);\n\t                        break;\n\t                    case \'type\' === name_2 && \'color\' === value:\n\t                        // Only accept 6 hex character values due to the HTML 5 spec\n\t                        // See http://www.w3.org/TR/html-markup/input.color.html#input.color.attrs.value\n\t                        opts[\'color\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            type: \'hex\',\n\t                        }, opts[\'color\']);\n\t                        break;\n\t                    case \'type\' === name_2 && \'email\' === value:\n\t                        opts[\'emailAddress\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                        }, opts[\'emailAddress\']);\n\t                        break;\n\t                    case \'type\' === name_2 && \'url\' === value:\n\t                        opts[\'uri\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                        }, opts[\'uri\']);\n\t                        break;\n\t                    case \'type\' === name_2 && \'range\' === value:\n\t                        opts[\'between\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            max: parseFloat(ele.getAttribute(\'max\')),\n\t                            min: parseFloat(ele.getAttribute(\'min\')),\n\t                        }, opts[\'between\']);\n\t                        break;\n\t                    case \'min\' === name_2 && type !== \'date\' && type !== \'range\':\n\t                        opts[\'greaterThan\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            min: parseFloat(value),\n\t                        }, opts[\'greaterThan\']);\n\t                        break;\n\t                    case \'max\' === name_2 && type !== \'date\' && type !== \'range\':\n\t                        opts[\'lessThan\'] = Object.assign({}, {\n\t                            enabled: true,\n\t                            max: parseFloat(value),\n\t                        }, opts[\'lessThan\']);\n\t                        break;\n\t                }\n\t            }\n\t            var items = reg.exec(name_2);\n\t            if (items && items.length === 4) {\n\t                var v = this.toCamelCase(items[1]);\n\t                if (!opts[v]) {\n\t                    opts[v] = {};\n\t                }\n\t                if (items[3]) {\n\t                    opts[v][this.toCamelCase(items[3])] = this.normalizeValue(value);\n\t                }\n\t                else if (opts[v][\'enabled\'] !== true || opts[v][\'enabled\'] !== false) {\n\t                    opts[v][\'enabled\'] = \'\' === value || \'true\' === value;\n\t                }\n\t            }\n\t        }\n\t        return { validators: opts };\n\t    };\n\t    // Many validators accept `boolean` options, for example\n\t    // `data-fv-between___inclusive="false"` should be identical to `inclusive: false`, not `inclusive: \'false\'`\n\t    Declarative.prototype.normalizeValue = function (value) {\n\t        return value === \'true\' || value === \'\' ? true : value === \'false\' ? false : value;\n\t    };\n\t    Declarative.prototype.toUpperCase = function (input) {\n\t        return input.charAt(1).toUpperCase();\n\t    };\n\t    Declarative.prototype.toCamelCase = function (input) {\n\t        return input.replace(/-./g, this.toUpperCase);\n\t    };\n\t    return Declarative;\n\t}(core.Plugin));\n\n\tcjs$y.Declarative = Declarative;\n\treturn cjs$y;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$y.exports = requireCjs$y();\n}\n\nvar libExports$y = lib$y.exports;\n\nvar lib$x = {exports: {}};\n\nvar index_min$x = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-default-submit\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$x;\n\nfunction requireIndex_min$x () {\n\tif (hasRequiredIndex_min$x) return index_min$x;\n\thasRequiredIndex_min$x = 1;\nvar t=libExports$B,o=function(t,r){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,o){t.__proto__=o;}||function(t,o){for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(t[r]=o[r]);},o(t,r)};var r=function(t){function r(){var o=t.call(this,{})||this;return o.onValidHandler=o.onFormValid.bind(o),o}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t;}o(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n);}(r,t),r.prototype.install=function(){if(this.core.getFormElement().querySelectorAll(\'[type="submit"][name="submit"]\').length)throw new Error("Do not use `submit` for the name attribute of submit button");this.core.on("core.form.valid",this.onValidHandler);},r.prototype.uninstall=function(){this.core.off("core.form.valid",this.onValidHandler);},r.prototype.onFormValid=function(){var t=this.core.getFormElement();this.isEnabled&&t instanceof HTMLFormElement&&t.submit();},r}(t.Plugin);index_min$x.DefaultSubmit=r;\n\treturn index_min$x;\n}\n\nvar cjs$x = {};\n\nvar hasRequiredCjs$x;\n\nfunction requireCjs$x () {\n\tif (hasRequiredCjs$x) return cjs$x;\n\thasRequiredCjs$x = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * This plugin will submit the form if all fields are valid after validating\n\t */\n\tvar DefaultSubmit = /** @class */ (function (_super) {\n\t    __extends(DefaultSubmit, _super);\n\t    function DefaultSubmit() {\n\t        var _this = _super.call(this, {}) || this;\n\t        _this.onValidHandler = _this.onFormValid.bind(_this);\n\t        return _this;\n\t    }\n\t    DefaultSubmit.prototype.install = function () {\n\t        var form = this.core.getFormElement();\n\t        if (form.querySelectorAll(\'[type="submit"][name="submit"]\').length) {\n\t            throw new Error(\'Do not use `submit` for the name attribute of submit button\');\n\t        }\n\t        this.core.on(\'core.form.valid\', this.onValidHandler);\n\t    };\n\t    DefaultSubmit.prototype.uninstall = function () {\n\t        this.core.off(\'core.form.valid\', this.onValidHandler);\n\t    };\n\t    DefaultSubmit.prototype.onFormValid = function () {\n\t        var form = this.core.getFormElement();\n\t        if (this.isEnabled && form instanceof HTMLFormElement) {\n\t            form.submit();\n\t        }\n\t    };\n\t    return DefaultSubmit;\n\t}(core.Plugin));\n\n\tcjs$x.DefaultSubmit = DefaultSubmit;\n\treturn cjs$x;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$x.exports = requireCjs$x();\n}\n\nvar libExports$x = lib$x.exports;\n\nvar lib$w = {exports: {}};\n\nvar index_min$w = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-dependency\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$w;\n\nfunction requireIndex_min$w () {\n\tif (hasRequiredIndex_min$w) return index_min$w;\n\thasRequiredIndex_min$w = 1;\nvar t=libExports$B,e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e;}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);},e(t,r)};var r=function(t){function r(e){var r=t.call(this,e)||this;return r.opts=e||{},r.triggerExecutedHandler=r.onTriggerExecuted.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=t;}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o);}(r,t),r.prototype.install=function(){this.core.on("plugins.trigger.executed",this.triggerExecutedHandler);},r.prototype.uninstall=function(){this.core.off("plugins.trigger.executed",this.triggerExecutedHandler);},r.prototype.onTriggerExecuted=function(t){if(this.isEnabled&&this.opts[t.field])for(var e=0,r=this.opts[t.field].split(" ");e<r.length;e++){var o=r[e].trim();this.opts[o]&&this.core.revalidateField(o);}},r}(t.Plugin);index_min$w.Dependency=r;\n\treturn index_min$w;\n}\n\nvar cjs$w = {};\n\nvar hasRequiredCjs$w;\n\nfunction requireCjs$w () {\n\tif (hasRequiredCjs$w) return cjs$w;\n\thasRequiredCjs$w = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar Dependency = /** @class */ (function (_super) {\n\t    __extends(Dependency, _super);\n\t    function Dependency(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.opts = opts || {};\n\t        _this.triggerExecutedHandler = _this.onTriggerExecuted.bind(_this);\n\t        return _this;\n\t    }\n\t    Dependency.prototype.install = function () {\n\t        this.core.on(\'plugins.trigger.executed\', this.triggerExecutedHandler);\n\t    };\n\t    Dependency.prototype.uninstall = function () {\n\t        this.core.off(\'plugins.trigger.executed\', this.triggerExecutedHandler);\n\t    };\n\t    Dependency.prototype.onTriggerExecuted = function (e) {\n\t        if (this.isEnabled && this.opts[e.field]) {\n\t            var dependencies = this.opts[e.field].split(\' \');\n\t            for (var _i = 0, dependencies_1 = dependencies; _i < dependencies_1.length; _i++) {\n\t                var d = dependencies_1[_i];\n\t                var dependentField = d.trim();\n\t                if (this.opts[dependentField]) {\n\t                    // Revalidate the dependent field\n\t                    this.core.revalidateField(dependentField);\n\t                }\n\t            }\n\t        }\n\t    };\n\t    return Dependency;\n\t}(core.Plugin));\n\n\tcjs$w.Dependency = Dependency;\n\treturn cjs$w;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$w.exports = requireCjs$w();\n}\n\nvar libExports$w = lib$w.exports;\n\nvar lib$v = {exports: {}};\n\nvar index_min$v = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-excluded\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$v;\n\nfunction requireIndex_min$v () {\n\tif (hasRequiredIndex_min$v) return index_min$v;\n\thasRequiredIndex_min$v = 1;\nvar t=libExports$B,e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e;}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);},e(t,n)};var n=t.utils.removeUndefined,i=function(t){function i(e){var r=t.call(this,e)||this;return r.opts=Object.assign({},{excluded:i.defaultIgnore},n(e)),r.ignoreValidationFilter=r.ignoreValidation.bind(r),r}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t;}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i);}(i,t),i.defaultIgnore=function(t,e,n){var i=!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length),r=e.getAttribute("disabled");return ""===r||"disabled"===r||"hidden"===e.getAttribute("type")||!i},i.prototype.install=function(){this.core.registerFilter("element-ignored",this.ignoreValidationFilter);},i.prototype.uninstall=function(){this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter);},i.prototype.ignoreValidation=function(t,e,n){return !!this.isEnabled&&this.opts.excluded.apply(this,[t,e,n])},i}(t.Plugin);index_min$v.Excluded=i;\n\treturn index_min$v;\n}\n\nvar cjs$v = {};\n\nvar hasRequiredCjs$v;\n\nfunction requireCjs$v () {\n\tif (hasRequiredCjs$v) return cjs$v;\n\thasRequiredCjs$v = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\tvar Excluded = /** @class */ (function (_super) {\n\t    __extends(Excluded, _super);\n\t    function Excluded(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.opts = Object.assign({}, { excluded: Excluded.defaultIgnore }, removeUndefined(opts));\n\t        _this.ignoreValidationFilter = _this.ignoreValidation.bind(_this);\n\t        return _this;\n\t    }\n\t    Excluded.defaultIgnore = function (_field, element, _elements) {\n\t        var isVisible = !!(element.offsetWidth || element.offsetHeight || element.getClientRects().length);\n\t        var disabled = element.getAttribute(\'disabled\');\n\t        return disabled === \'\' || disabled === \'disabled\' || element.getAttribute(\'type\') === \'hidden\' || !isVisible;\n\t    };\n\t    Excluded.prototype.install = function () {\n\t        this.core.registerFilter(\'element-ignored\', this.ignoreValidationFilter);\n\t    };\n\t    Excluded.prototype.uninstall = function () {\n\t        this.core.deregisterFilter(\'element-ignored\', this.ignoreValidationFilter);\n\t    };\n\t    Excluded.prototype.ignoreValidation = function (field, element, elements) {\n\t        if (!this.isEnabled) {\n\t            return false;\n\t        }\n\t        return this.opts.excluded.apply(this, [field, element, elements]);\n\t    };\n\t    return Excluded;\n\t}(core.Plugin));\n\n\tcjs$v.Excluded = Excluded;\n\treturn cjs$v;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$v.exports = requireCjs$v();\n}\n\nvar libExports$v = lib$v.exports;\n\nvar lib$u = {exports: {}};\n\nvar index_min$u = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-field-status\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$u;\n\nfunction requireIndex_min$u () {\n\tif (hasRequiredIndex_min$u) return index_min$u;\n\thasRequiredIndex_min$u = 1;\nvar e=libExports$B,t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);},t(e,n)};var n=function(e){function n(t){var n=e.call(this,t)||this;return n.statuses=new Map,n.opts=Object.assign({},{onStatusChanged:function(){}},t),n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.elementIgnoredHandler=n.onElementIgnored.bind(n),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function d(){this.constructor=e;}t(e,n),e.prototype=null===n?Object.create(n):(d.prototype=n.prototype,new d);}(n,e),n.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler);},n.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler);},n.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every((function(e){return "Valid"===e||"NotValidated"===e||"Ignored"===e}))},n.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},n.prototype.onFieldAdded=function(e){this.statuses.set(e.field,"NotValidated");},n.prototype.onFieldRemoved=function(e){this.statuses.has(e.field)&&this.statuses.delete(e.field),this.handleStatusChanged(this.areFieldsValid());},n.prototype.onElementValidating=function(e){this.statuses.set(e.field,"Validating"),this.handleStatusChanged(!1);},n.prototype.onElementValidated=function(e){this.statuses.set(e.field,e.valid?"Valid":"Invalid"),e.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1);},n.prototype.onElementNotValidated=function(e){this.statuses.set(e.field,"NotValidated"),this.handleStatusChanged(!1);},n.prototype.onElementIgnored=function(e){this.statuses.set(e.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid());},n.prototype.handleStatusChanged=function(e){this.isEnabled&&this.opts.onStatusChanged(e);},n}(e.Plugin);index_min$u.FieldStatus=n;\n\treturn index_min$u;\n}\n\nvar cjs$u = {};\n\nvar hasRequiredCjs$u;\n\nfunction requireCjs$u () {\n\tif (hasRequiredCjs$u) return cjs$u;\n\thasRequiredCjs$u = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar FieldStatus = /** @class */ (function (_super) {\n\t    __extends(FieldStatus, _super);\n\t    function FieldStatus(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.statuses = new Map();\n\t        _this.opts = Object.assign({}, {\n\t            onStatusChanged: function () { },\n\t        }, opts);\n\t        _this.elementValidatingHandler = _this.onElementValidating.bind(_this);\n\t        _this.elementValidatedHandler = _this.onElementValidated.bind(_this);\n\t        _this.elementNotValidatedHandler = _this.onElementNotValidated.bind(_this);\n\t        _this.elementIgnoredHandler = _this.onElementIgnored.bind(_this);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n\t        return _this;\n\t    }\n\t    FieldStatus.prototype.install = function () {\n\t        this.core\n\t            .on(\'core.element.validating\', this.elementValidatingHandler)\n\t            .on(\'core.element.validated\', this.elementValidatedHandler)\n\t            .on(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .on(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .on(\'core.field.added\', this.fieldAddedHandler)\n\t            .on(\'core.field.removed\', this.fieldRemovedHandler);\n\t    };\n\t    FieldStatus.prototype.uninstall = function () {\n\t        this.statuses.clear();\n\t        this.core\n\t            .off(\'core.element.validating\', this.elementValidatingHandler)\n\t            .off(\'core.element.validated\', this.elementValidatedHandler)\n\t            .off(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .off(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .off(\'core.field.added\', this.fieldAddedHandler)\n\t            .off(\'core.field.removed\', this.fieldRemovedHandler);\n\t    };\n\t    FieldStatus.prototype.areFieldsValid = function () {\n\t        return Array.from(this.statuses.values()).every(function (value) {\n\t            return value === \'Valid\' || value === \'NotValidated\' || value === \'Ignored\';\n\t        });\n\t    };\n\t    FieldStatus.prototype.getStatuses = function () {\n\t        return this.isEnabled ? this.statuses : new Map();\n\t    };\n\t    FieldStatus.prototype.onFieldAdded = function (e) {\n\t        this.statuses.set(e.field, \'NotValidated\');\n\t    };\n\t    FieldStatus.prototype.onFieldRemoved = function (e) {\n\t        if (this.statuses.has(e.field)) {\n\t            this.statuses.delete(e.field);\n\t        }\n\t        this.handleStatusChanged(this.areFieldsValid());\n\t    };\n\t    FieldStatus.prototype.onElementValidating = function (e) {\n\t        this.statuses.set(e.field, \'Validating\');\n\t        this.handleStatusChanged(false);\n\t    };\n\t    FieldStatus.prototype.onElementValidated = function (e) {\n\t        this.statuses.set(e.field, e.valid ? \'Valid\' : \'Invalid\');\n\t        if (e.valid) {\n\t            this.handleStatusChanged(this.areFieldsValid());\n\t        }\n\t        else {\n\t            this.handleStatusChanged(false);\n\t        }\n\t    };\n\t    FieldStatus.prototype.onElementNotValidated = function (e) {\n\t        this.statuses.set(e.field, \'NotValidated\');\n\t        this.handleStatusChanged(false);\n\t    };\n\t    FieldStatus.prototype.onElementIgnored = function (e) {\n\t        this.statuses.set(e.field, \'Ignored\');\n\t        this.handleStatusChanged(this.areFieldsValid());\n\t    };\n\t    FieldStatus.prototype.handleStatusChanged = function (areFieldsValid) {\n\t        if (this.isEnabled) {\n\t            this.opts.onStatusChanged(areFieldsValid);\n\t        }\n\t    };\n\t    return FieldStatus;\n\t}(core.Plugin));\n\n\tcjs$u.FieldStatus = FieldStatus;\n\treturn cjs$u;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$u.exports = requireCjs$u();\n}\n\nvar libExports$u = lib$u.exports;\n\nvar lib$t = {exports: {}};\n\nvar index_min$t = {};\n\nvar lib$s = {exports: {}};\n\nvar index_min$s = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-message\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$t;\n\nfunction requireIndex_min$t () {\n\tif (hasRequiredIndex_min$t) return index_min$s;\n\thasRequiredIndex_min$t = 1;\nvar e=libExports$B,t=function(e,a){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);},t(e,a)};var a=e.utils.classSet,n=function(e){function n(t){var a=e.call(this,t)||this;return a.useDefaultContainer=!1,a.messages=new Map,a.defaultContainer=document.createElement("div"),a.useDefaultContainer=!t||!t.container,a.opts=Object.assign({},{container:function(e,t){return a.defaultContainer}},t),a.elementIgnoredHandler=a.onElementIgnored.bind(a),a.fieldAddedHandler=a.onFieldAdded.bind(a),a.fieldRemovedHandler=a.onFieldRemoved.bind(a),a.validatorValidatedHandler=a.onValidatorValidated.bind(a),a.validatorNotValidatedHandler=a.onValidatorNotValidated.bind(a),a}return function(e,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function n(){this.constructor=e;}t(e,a),e.prototype=null===a?Object.create(a):(n.prototype=a.prototype,new n);}(n,e),n.getClosestContainer=function(e,t,a){for(var n=e;n&&n!==t&&(n=n.parentElement,!a.test(n.className)););return n},n.prototype.install=function(){this.useDefaultContainer&&this.core.getFormElement().appendChild(this.defaultContainer),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.validator.notvalidated",this.validatorNotValidatedHandler);},n.prototype.uninstall=function(){this.useDefaultContainer&&this.core.getFormElement().removeChild(this.defaultContainer),this.messages.forEach((function(e){return e.parentNode.removeChild(e)})),this.messages.clear(),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.validator.notvalidated",this.validatorNotValidatedHandler);},n.prototype.onEnabled=function(){this.messages.forEach((function(e,t,n){a(t,{"fv-plugins-message-container--enabled":!0,"fv-plugins-message-container--disabled":!1});}));},n.prototype.onDisabled=function(){this.messages.forEach((function(e,t,n){a(t,{"fv-plugins-message-container--enabled":!1,"fv-plugins-message-container--disabled":!0});}));},n.prototype.onFieldAdded=function(e){var t=this,a=e.elements;a&&(a.forEach((function(e){var a=t.messages.get(e);a&&(a.parentNode.removeChild(a),t.messages.delete(e));})),this.prepareFieldContainer(e.field,a));},n.prototype.onFieldRemoved=function(e){var t=this;if(e.elements.length&&e.field){var a=e.elements[0].getAttribute("type");("radio"===a||"checkbox"===a?[e.elements[0]]:e.elements).forEach((function(e){if(t.messages.has(e)){var a=t.messages.get(e);a.parentNode.removeChild(a),t.messages.delete(e);}}));}},n.prototype.prepareFieldContainer=function(e,t){var a=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementContainer(e,t[0],t):t.forEach((function(n){return a.prepareElementContainer(e,n,t)}));}},n.prototype.prepareElementContainer=function(e,t,n){var i;if("string"==typeof this.opts.container){var o="#"===this.opts.container.charAt(0)?\'[id="\'.concat(this.opts.container.substring(1),\'"]\'):this.opts.container;i=this.core.getFormElement().querySelector(o);}else i=this.opts.container(e,t);var r=document.createElement("div");i.appendChild(r),a(r,{"fv-plugins-message-container":!0,"fv-plugins-message-container--enabled":this.isEnabled,"fv-plugins-message-container--disabled":!this.isEnabled}),this.core.emit("plugins.message.placed",{element:t,elements:n,field:e,messageElement:r}),this.messages.set(t,r);},n.prototype.getMessage=function(e){return "string"==typeof e.message?e.message:e.message[this.core.getLocale()]},n.prototype.onValidatorValidated=function(e){var t,n=e.elements,i=e.element.getAttribute("type"),o=("radio"===i||"checkbox"===i)&&n.length>0?n[0]:e.element;if(this.messages.has(o)){var r=this.messages.get(o),s=r.querySelector(\'[data-field="\'.concat(e.field.replace(/"/g,\'\\\\"\'),\'"][data-validator="\').concat(e.validator.replace(/"/g,\'\\\\"\'),\'"]\'));if(s||e.result.valid)s&&!e.result.valid?(s.innerHTML=this.getMessage(e.result),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:s,meta:e.result.meta,validator:e.validator})):s&&e.result.valid&&r.removeChild(s);else {var l=document.createElement("div");l.innerHTML=this.getMessage(e.result),l.setAttribute("data-field",e.field),l.setAttribute("data-validator",e.validator),this.opts.clazz&&a(l,((t={})[this.opts.clazz]=!0,t)),r.appendChild(l),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:l,meta:e.result.meta,validator:e.validator});}}},n.prototype.onValidatorNotValidated=function(e){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element;if(this.messages.has(n)){var i=this.messages.get(n),o=i.querySelector(\'[data-field="\'.concat(e.field.replace(/"/g,\'\\\\"\'),\'"][data-validator="\').concat(e.validator.replace(/"/g,\'\\\\"\'),\'"]\'));o&&i.removeChild(o);}},n.prototype.onElementIgnored=function(e){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element;if(this.messages.has(n)){var i=this.messages.get(n);[].slice.call(i.querySelectorAll(\'[data-field="\'.concat(e.field.replace(/"/g,\'\\\\"\'),\'"]\'))).forEach((function(e){i.removeChild(e);}));}},n}(e.Plugin);index_min$s.Message=n;\n\treturn index_min$s;\n}\n\nvar cjs$t = {};\n\nvar hasRequiredCjs$t;\n\nfunction requireCjs$t () {\n\tif (hasRequiredCjs$t) return cjs$t;\n\thasRequiredCjs$t = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar classSet = core.utils.classSet;\n\tvar Message = /** @class */ (function (_super) {\n\t    __extends(Message, _super);\n\t    function Message(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.useDefaultContainer = false;\n\t        // Map the field element to message container\n\t        _this.messages = new Map();\n\t        // By default, we will display error messages at the bottom of form\n\t        _this.defaultContainer = document.createElement(\'div\');\n\t        _this.useDefaultContainer = !opts || !opts.container;\n\t        _this.opts = Object.assign({}, {\n\t            container: function (_field, _element) { return _this.defaultContainer; },\n\t        }, opts);\n\t        _this.elementIgnoredHandler = _this.onElementIgnored.bind(_this);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n\t        _this.validatorValidatedHandler = _this.onValidatorValidated.bind(_this);\n\t        _this.validatorNotValidatedHandler = _this.onValidatorNotValidated.bind(_this);\n\t        return _this;\n\t    }\n\t    /**\n\t     * Determine the closest element that its class matches with given pattern.\n\t     * In popular cases, all the fields might follow the same markup, so that closest element\n\t     * can be used as message container.\n\t     *\n\t     * For example, if we use the Bootstrap framework then the field often be placed inside a\n\t     * `col-{size}-{numberOfColumns}` class, we can register the plugin as following:\n\t     * ```\n\t     *  formValidation(form, {\n\t     *      plugins: {\n\t     *          message: new Message({\n\t     *              container: function(field, element) {\n\t     *                  return Message.getClosestContainer(element, form, /^(.*)(col|offset)-(xs|sm|md|lg)-[0-9]+(.*)$/)\n\t     *              }\n\t     *          })\n\t     *      }\n\t     *  })\n\t     * ```\n\t     *\n\t     * @param element The field element\n\t     * @param upper The upper element, so we don\'t have to look for the entire page\n\t     * @param pattern The pattern\n\t     * @return {HTMLElement}\n\t     */\n\t    Message.getClosestContainer = function (element, upper, pattern) {\n\t        var ele = element;\n\t        while (ele) {\n\t            if (ele === upper) {\n\t                break;\n\t            }\n\t            ele = ele.parentElement;\n\t            if (pattern.test(ele.className)) {\n\t                break;\n\t            }\n\t        }\n\t        return ele;\n\t    };\n\t    Message.prototype.install = function () {\n\t        if (this.useDefaultContainer) {\n\t            this.core.getFormElement().appendChild(this.defaultContainer);\n\t        }\n\t        this.core\n\t            .on(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .on(\'core.field.added\', this.fieldAddedHandler)\n\t            .on(\'core.field.removed\', this.fieldRemovedHandler)\n\t            .on(\'core.validator.validated\', this.validatorValidatedHandler)\n\t            .on(\'core.validator.notvalidated\', this.validatorNotValidatedHandler);\n\t    };\n\t    Message.prototype.uninstall = function () {\n\t        if (this.useDefaultContainer) {\n\t            this.core.getFormElement().removeChild(this.defaultContainer);\n\t        }\n\t        this.messages.forEach(function (message) { return message.parentNode.removeChild(message); });\n\t        this.messages.clear();\n\t        this.core\n\t            .off(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .off(\'core.field.added\', this.fieldAddedHandler)\n\t            .off(\'core.field.removed\', this.fieldRemovedHandler)\n\t            .off(\'core.validator.validated\', this.validatorValidatedHandler)\n\t            .off(\'core.validator.notvalidated\', this.validatorNotValidatedHandler);\n\t    };\n\t    Message.prototype.onEnabled = function () {\n\t        this.messages.forEach(function (_element, message, _map) {\n\t            classSet(message, {\n\t                \'fv-plugins-message-container--enabled\': true,\n\t                \'fv-plugins-message-container--disabled\': false,\n\t            });\n\t        });\n\t    };\n\t    Message.prototype.onDisabled = function () {\n\t        this.messages.forEach(function (_element, message, _map) {\n\t            classSet(message, {\n\t                \'fv-plugins-message-container--enabled\': false,\n\t                \'fv-plugins-message-container--disabled\': true,\n\t            });\n\t        });\n\t    };\n\t    // Prepare message container for new added field\n\t    Message.prototype.onFieldAdded = function (e) {\n\t        var _this = this;\n\t        var elements = e.elements;\n\t        if (elements) {\n\t            elements.forEach(function (ele) {\n\t                var msg = _this.messages.get(ele);\n\t                if (msg) {\n\t                    msg.parentNode.removeChild(msg);\n\t                    _this.messages.delete(ele);\n\t                }\n\t            });\n\t            this.prepareFieldContainer(e.field, elements);\n\t        }\n\t    };\n\t    // When a field is removed, we remove all error messages that associates with the field\n\t    Message.prototype.onFieldRemoved = function (e) {\n\t        var _this = this;\n\t        if (!e.elements.length || !e.field) {\n\t            return;\n\t        }\n\t        var type = e.elements[0].getAttribute(\'type\');\n\t        var elements = \'radio\' === type || \'checkbox\' === type ? [e.elements[0]] : e.elements;\n\t        elements.forEach(function (ele) {\n\t            if (_this.messages.has(ele)) {\n\t                var container = _this.messages.get(ele);\n\t                container.parentNode.removeChild(container);\n\t                _this.messages.delete(ele);\n\t            }\n\t        });\n\t    };\n\t    Message.prototype.prepareFieldContainer = function (field, elements) {\n\t        var _this = this;\n\t        if (elements.length) {\n\t            var type = elements[0].getAttribute(\'type\');\n\t            if (\'radio\' === type || \'checkbox\' === type) {\n\t                this.prepareElementContainer(field, elements[0], elements);\n\t            }\n\t            else {\n\t                elements.forEach(function (ele) { return _this.prepareElementContainer(field, ele, elements); });\n\t            }\n\t        }\n\t    };\n\t    Message.prototype.prepareElementContainer = function (field, element, elements) {\n\t        var container;\n\t        if (\'string\' === typeof this.opts.container) {\n\t            var selector = \'#\' === this.opts.container.charAt(0)\n\t                ? "[id=\\"".concat(this.opts.container.substring(1), "\\"]")\n\t                : this.opts.container;\n\t            container = this.core.getFormElement().querySelector(selector);\n\t        }\n\t        else {\n\t            container = this.opts.container(field, element);\n\t        }\n\t        var message = document.createElement(\'div\');\n\t        container.appendChild(message);\n\t        classSet(message, {\n\t            \'fv-plugins-message-container\': true,\n\t            \'fv-plugins-message-container--enabled\': this.isEnabled,\n\t            \'fv-plugins-message-container--disabled\': !this.isEnabled,\n\t        });\n\t        this.core.emit(\'plugins.message.placed\', {\n\t            element: element,\n\t            elements: elements,\n\t            field: field,\n\t            messageElement: message,\n\t        });\n\t        this.messages.set(element, message);\n\t    };\n\t    Message.prototype.getMessage = function (result) {\n\t        return typeof result.message === \'string\' ? result.message : result.message[this.core.getLocale()];\n\t    };\n\t    Message.prototype.onValidatorValidated = function (e) {\n\t        var _a;\n\t        var elements = e.elements;\n\t        var type = e.element.getAttribute(\'type\');\n\t        var element = (\'radio\' === type || \'checkbox\' === type) && elements.length > 0 ? elements[0] : e.element;\n\t        if (this.messages.has(element)) {\n\t            var container = this.messages.get(element);\n\t            var messageEle = container.querySelector("[data-field=\\"".concat(e.field.replace(/"/g, \'\\\\"\'), "\\"][data-validator=\\"").concat(e.validator.replace(/"/g, \'\\\\"\'), "\\"]"));\n\t            if (!messageEle && !e.result.valid) {\n\t                var ele = document.createElement(\'div\');\n\t                ele.innerHTML = this.getMessage(e.result);\n\t                ele.setAttribute(\'data-field\', e.field);\n\t                ele.setAttribute(\'data-validator\', e.validator);\n\t                if (this.opts.clazz) {\n\t                    classSet(ele, (_a = {},\n\t                        _a[this.opts.clazz] = true,\n\t                        _a));\n\t                }\n\t                container.appendChild(ele);\n\t                this.core.emit(\'plugins.message.displayed\', {\n\t                    element: e.element,\n\t                    field: e.field,\n\t                    message: e.result.message,\n\t                    messageElement: ele,\n\t                    meta: e.result.meta,\n\t                    validator: e.validator,\n\t                });\n\t            }\n\t            else if (messageEle && !e.result.valid) {\n\t                // The validator returns new message\n\t                messageEle.innerHTML = this.getMessage(e.result);\n\t                this.core.emit(\'plugins.message.displayed\', {\n\t                    element: e.element,\n\t                    field: e.field,\n\t                    message: e.result.message,\n\t                    messageElement: messageEle,\n\t                    meta: e.result.meta,\n\t                    validator: e.validator,\n\t                });\n\t            }\n\t            else if (messageEle && e.result.valid) {\n\t                // Field is valid\n\t                container.removeChild(messageEle);\n\t            }\n\t        }\n\t    };\n\t    Message.prototype.onValidatorNotValidated = function (e) {\n\t        var elements = e.elements;\n\t        var type = e.element.getAttribute(\'type\');\n\t        var element = \'radio\' === type || \'checkbox\' === type ? elements[0] : e.element;\n\t        if (this.messages.has(element)) {\n\t            var container = this.messages.get(element);\n\t            var messageEle = container.querySelector("[data-field=\\"".concat(e.field.replace(/"/g, \'\\\\"\'), "\\"][data-validator=\\"").concat(e.validator.replace(/"/g, \'\\\\"\'), "\\"]"));\n\t            if (messageEle) {\n\t                container.removeChild(messageEle);\n\t            }\n\t        }\n\t    };\n\t    Message.prototype.onElementIgnored = function (e) {\n\t        var elements = e.elements;\n\t        var type = e.element.getAttribute(\'type\');\n\t        var element = \'radio\' === type || \'checkbox\' === type ? elements[0] : e.element;\n\t        if (this.messages.has(element)) {\n\t            var container_1 = this.messages.get(element);\n\t            var messageElements = [].slice.call(container_1.querySelectorAll("[data-field=\\"".concat(e.field.replace(/"/g, \'\\\\"\'), "\\"]")));\n\t            messageElements.forEach(function (messageEle) {\n\t                container_1.removeChild(messageEle);\n\t            });\n\t        }\n\t    };\n\t    return Message;\n\t}(core.Plugin));\n\n\tcjs$t.Message = Message;\n\treturn cjs$t;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$s.exports = requireCjs$t();\n}\n\nvar libExports$t = lib$s.exports;\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-framework\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$s;\n\nfunction requireIndex_min$s () {\n\tif (hasRequiredIndex_min$s) return index_min$t;\n\thasRequiredIndex_min$s = 1;\nvar e=libExports$B,t=libExports$t,o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);},o(e,t)};var n=e.utils.classSet,s=e.utils.closest,i=function(e){function i(t){var o=e.call(this,t)||this;return o.results=new Map,o.containers=new Map,o.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:"",eleValidClass:"",rowClasses:"",rowValidatingClass:""},t),o.elementIgnoredHandler=o.onElementIgnored.bind(o),o.elementValidatingHandler=o.onElementValidating.bind(o),o.elementValidatedHandler=o.onElementValidated.bind(o),o.elementNotValidatedHandler=o.onElementNotValidated.bind(o),o.iconPlacedHandler=o.onIconPlaced.bind(o),o.fieldAddedHandler=o.onFieldAdded.bind(o),o.fieldRemovedHandler=o.onFieldRemoved.bind(o),o.messagePlacedHandler=o.onMessagePlaced.bind(o),o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e;}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n);}(i,e),i.prototype.install=function(){var e,o=this;n(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e["fv-plugins-framework"]=!0,e)),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.registerPlugin(i.MESSAGE_PLUGIN,new t.Message({clazz:this.opts.messageClass,container:function(e,n){var i="string"==typeof o.opts.rowSelector?o.opts.rowSelector:o.opts.rowSelector(e,n),a=s(n,i);return t.Message.getClosestContainer(n,a,o.opts.rowPattern)}})),this.core.on("plugins.message.placed",this.messagePlacedHandler));},i.prototype.uninstall=function(){var e;this.results.clear(),this.containers.clear(),n(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e["fv-plugins-framework"]=!1,e)),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.deregisterPlugin(i.MESSAGE_PLUGIN),this.core.off("plugins.message.placed",this.messagePlacedHandler));},i.prototype.onEnabled=function(){var e;n(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e)),this.opts.defaultMessageContainer&&this.core.enablePlugin(i.MESSAGE_PLUGIN);},i.prototype.onDisabled=function(){var e;n(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e)),this.opts.defaultMessageContainer&&this.core.disablePlugin(i.MESSAGE_PLUGIN);},i.prototype.onIconPlaced=function(e){},i.prototype.onMessagePlaced=function(e){},i.prototype.onFieldAdded=function(e){var t=this,o=e.elements;o&&(o.forEach((function(e){var o,s=t.containers.get(e);s&&(n(s,((o={})[t.opts.rowInvalidClass]=!1,o[t.opts.rowValidatingClass]=!1,o[t.opts.rowValidClass]=!1,o["fv-plugins-icon-container"]=!1,o)),t.containers.delete(e));})),this.prepareFieldContainer(e.field,o));},i.prototype.onFieldRemoved=function(e){var t=this;e.elements.forEach((function(e){var o,s=t.containers.get(e);s&&n(s,((o={})[t.opts.rowInvalidClass]=!1,o[t.opts.rowValidatingClass]=!1,o[t.opts.rowValidClass]=!1,o));}));},i.prototype.prepareFieldContainer=function(e,t){var o=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementContainer(e,t[0]):t.forEach((function(t){return o.prepareElementContainer(e,t)}));}},i.prototype.prepareElementContainer=function(e,t){var o,i="string"==typeof this.opts.rowSelector?this.opts.rowSelector:this.opts.rowSelector(e,t),a=s(t,i);a!==t&&(n(a,((o={})[this.opts.rowClasses]=!0,o["fv-plugins-icon-container"]=!0,o)),this.containers.set(t,a));},i.prototype.onElementValidating=function(e){this.removeClasses(e.element,e.elements);},i.prototype.onElementNotValidated=function(e){this.removeClasses(e.element,e.elements);},i.prototype.onElementIgnored=function(e){this.removeClasses(e.element,e.elements);},i.prototype.removeClasses=function(e,t){var o,s=this,i=e.getAttribute("type"),a="radio"===i||"checkbox"===i?t[0]:e;t.forEach((function(e){var t;n(e,((t={})[s.opts.eleValidClass]=!1,t[s.opts.eleInvalidClass]=!1,t));}));var l=this.containers.get(a);l&&n(l,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!1,o));},i.prototype.onElementValidated=function(e){var t,o,s=this,i=e.elements,a=e.element.getAttribute("type"),l="radio"===a||"checkbox"===a?i[0]:e.element;i.forEach((function(t){var o;n(t,((o={})[s.opts.eleValidClass]=e.valid,o[s.opts.eleInvalidClass]=!e.valid,o));}));var r=this.containers.get(l);if(r)if(e.valid){this.results.delete(l);var d=!0;this.containers.forEach((function(e,t){e===r&&!1===s.results.get(t)&&(d=!1);})),d&&n(r,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!0,o));}else this.results.set(l,!1),n(r,((t={})[this.opts.rowInvalidClass]=!0,t[this.opts.rowValidatingClass]=!1,t[this.opts.rowValidClass]=!1,t));},i.MESSAGE_PLUGIN="___frameworkMessage",i}(e.Plugin);index_min$t.Framework=i;\n\treturn index_min$t;\n}\n\nvar cjs$s = {};\n\nvar hasRequiredCjs$s;\n\nfunction requireCjs$s () {\n\tif (hasRequiredCjs$s) return cjs$s;\n\thasRequiredCjs$s = 1;\n\n\tvar core = libExports$B;\n\tvar pluginMessage = libExports$t;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar classSet = core.utils.classSet, closest = core.utils.closest;\n\tvar Framework = /** @class */ (function (_super) {\n\t    __extends(Framework, _super);\n\t    function Framework(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.results = new Map();\n\t        _this.containers = new Map();\n\t        _this.opts = Object.assign({}, {\n\t            defaultMessageContainer: true,\n\t            eleInvalidClass: \'\',\n\t            eleValidClass: \'\',\n\t            rowClasses: \'\',\n\t            rowValidatingClass: \'\',\n\t        }, opts);\n\t        _this.elementIgnoredHandler = _this.onElementIgnored.bind(_this);\n\t        _this.elementValidatingHandler = _this.onElementValidating.bind(_this);\n\t        _this.elementValidatedHandler = _this.onElementValidated.bind(_this);\n\t        _this.elementNotValidatedHandler = _this.onElementNotValidated.bind(_this);\n\t        _this.iconPlacedHandler = _this.onIconPlaced.bind(_this);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n\t        _this.messagePlacedHandler = _this.onMessagePlaced.bind(_this);\n\t        return _this;\n\t    }\n\t    Framework.prototype.install = function () {\n\t        var _a;\n\t        var _this = this;\n\t        classSet(this.core.getFormElement(), (_a = {},\n\t            _a[this.opts.formClass] = true,\n\t            _a[\'fv-plugins-framework\'] = true,\n\t            _a));\n\t        this.core\n\t            .on(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .on(\'core.element.validating\', this.elementValidatingHandler)\n\t            .on(\'core.element.validated\', this.elementValidatedHandler)\n\t            .on(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .on(\'plugins.icon.placed\', this.iconPlacedHandler)\n\t            .on(\'core.field.added\', this.fieldAddedHandler)\n\t            .on(\'core.field.removed\', this.fieldRemovedHandler);\n\t        if (this.opts.defaultMessageContainer) {\n\t            this.core.registerPlugin(Framework.MESSAGE_PLUGIN, new pluginMessage.Message({\n\t                clazz: this.opts.messageClass,\n\t                container: function (field, element) {\n\t                    var selector = \'string\' === typeof _this.opts.rowSelector\n\t                        ? _this.opts.rowSelector\n\t                        : _this.opts.rowSelector(field, element);\n\t                    var groupEle = closest(element, selector);\n\t                    return pluginMessage.Message.getClosestContainer(element, groupEle, _this.opts.rowPattern);\n\t                },\n\t            }));\n\t            this.core.on(\'plugins.message.placed\', this.messagePlacedHandler);\n\t        }\n\t    };\n\t    Framework.prototype.uninstall = function () {\n\t        var _a;\n\t        this.results.clear();\n\t        this.containers.clear();\n\t        classSet(this.core.getFormElement(), (_a = {},\n\t            _a[this.opts.formClass] = false,\n\t            _a[\'fv-plugins-framework\'] = false,\n\t            _a));\n\t        this.core\n\t            .off(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .off(\'core.element.validating\', this.elementValidatingHandler)\n\t            .off(\'core.element.validated\', this.elementValidatedHandler)\n\t            .off(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .off(\'plugins.icon.placed\', this.iconPlacedHandler)\n\t            .off(\'core.field.added\', this.fieldAddedHandler)\n\t            .off(\'core.field.removed\', this.fieldRemovedHandler);\n\t        if (this.opts.defaultMessageContainer) {\n\t            this.core.deregisterPlugin(Framework.MESSAGE_PLUGIN);\n\t            this.core.off(\'plugins.message.placed\', this.messagePlacedHandler);\n\t        }\n\t    };\n\t    Framework.prototype.onEnabled = function () {\n\t        var _a;\n\t        classSet(this.core.getFormElement(), (_a = {},\n\t            _a[this.opts.formClass] = true,\n\t            _a));\n\t        if (this.opts.defaultMessageContainer) {\n\t            this.core.enablePlugin(Framework.MESSAGE_PLUGIN);\n\t        }\n\t    };\n\t    Framework.prototype.onDisabled = function () {\n\t        var _a;\n\t        classSet(this.core.getFormElement(), (_a = {},\n\t            _a[this.opts.formClass] = false,\n\t            _a));\n\t        if (this.opts.defaultMessageContainer) {\n\t            this.core.disablePlugin(Framework.MESSAGE_PLUGIN);\n\t        }\n\t    };\n\t    Framework.prototype.onIconPlaced = function (_e) { }; // eslint-disable-line @typescript-eslint/no-empty-function\n\t    Framework.prototype.onMessagePlaced = function (_e) { }; // eslint-disable-line @typescript-eslint/no-empty-function\n\t    Framework.prototype.onFieldAdded = function (e) {\n\t        var _this = this;\n\t        var elements = e.elements;\n\t        if (elements) {\n\t            elements.forEach(function (ele) {\n\t                var _a;\n\t                var groupEle = _this.containers.get(ele);\n\t                if (groupEle) {\n\t                    classSet(groupEle, (_a = {},\n\t                        _a[_this.opts.rowInvalidClass] = false,\n\t                        _a[_this.opts.rowValidatingClass] = false,\n\t                        _a[_this.opts.rowValidClass] = false,\n\t                        _a[\'fv-plugins-icon-container\'] = false,\n\t                        _a));\n\t                    _this.containers.delete(ele);\n\t                }\n\t            });\n\t            this.prepareFieldContainer(e.field, elements);\n\t        }\n\t    };\n\t    Framework.prototype.onFieldRemoved = function (e) {\n\t        var _this = this;\n\t        e.elements.forEach(function (ele) {\n\t            var _a;\n\t            var groupEle = _this.containers.get(ele);\n\t            if (groupEle) {\n\t                classSet(groupEle, (_a = {},\n\t                    _a[_this.opts.rowInvalidClass] = false,\n\t                    _a[_this.opts.rowValidatingClass] = false,\n\t                    _a[_this.opts.rowValidClass] = false,\n\t                    _a));\n\t            }\n\t        });\n\t    };\n\t    Framework.prototype.prepareFieldContainer = function (field, elements) {\n\t        var _this = this;\n\t        if (elements.length) {\n\t            var type = elements[0].getAttribute(\'type\');\n\t            if (\'radio\' === type || \'checkbox\' === type) {\n\t                this.prepareElementContainer(field, elements[0]);\n\t            }\n\t            else {\n\t                elements.forEach(function (ele) { return _this.prepareElementContainer(field, ele); });\n\t            }\n\t        }\n\t    };\n\t    Framework.prototype.prepareElementContainer = function (field, element) {\n\t        var _a;\n\t        var selector = \'string\' === typeof this.opts.rowSelector ? this.opts.rowSelector : this.opts.rowSelector(field, element);\n\t        var groupEle = closest(element, selector);\n\t        if (groupEle !== element) {\n\t            classSet(groupEle, (_a = {},\n\t                _a[this.opts.rowClasses] = true,\n\t                _a[\'fv-plugins-icon-container\'] = true,\n\t                _a));\n\t            this.containers.set(element, groupEle);\n\t        }\n\t    };\n\t    Framework.prototype.onElementValidating = function (e) {\n\t        this.removeClasses(e.element, e.elements);\n\t    };\n\t    Framework.prototype.onElementNotValidated = function (e) {\n\t        this.removeClasses(e.element, e.elements);\n\t    };\n\t    Framework.prototype.onElementIgnored = function (e) {\n\t        this.removeClasses(e.element, e.elements);\n\t    };\n\t    Framework.prototype.removeClasses = function (element, elements) {\n\t        var _a;\n\t        var _this = this;\n\t        var type = element.getAttribute(\'type\');\n\t        var ele = \'radio\' === type || \'checkbox\' === type ? elements[0] : element;\n\t        elements.forEach(function (ele) {\n\t            var _a;\n\t            classSet(ele, (_a = {},\n\t                _a[_this.opts.eleValidClass] = false,\n\t                _a[_this.opts.eleInvalidClass] = false,\n\t                _a));\n\t        });\n\t        var groupEle = this.containers.get(ele);\n\t        if (groupEle) {\n\t            classSet(groupEle, (_a = {},\n\t                _a[this.opts.rowInvalidClass] = false,\n\t                _a[this.opts.rowValidatingClass] = false,\n\t                _a[this.opts.rowValidClass] = false,\n\t                _a));\n\t        }\n\t    };\n\t    Framework.prototype.onElementValidated = function (e) {\n\t        var _a, _b;\n\t        var _this = this;\n\t        var elements = e.elements;\n\t        var type = e.element.getAttribute(\'type\');\n\t        var element = \'radio\' === type || \'checkbox\' === type ? elements[0] : e.element;\n\t        // Set the valid or invalid class for all elements\n\t        elements.forEach(function (ele) {\n\t            var _a;\n\t            classSet(ele, (_a = {},\n\t                _a[_this.opts.eleValidClass] = e.valid,\n\t                _a[_this.opts.eleInvalidClass] = !e.valid,\n\t                _a));\n\t        });\n\t        var groupEle = this.containers.get(element);\n\t        if (groupEle) {\n\t            if (!e.valid) {\n\t                this.results.set(element, false);\n\t                classSet(groupEle, (_a = {},\n\t                    _a[this.opts.rowInvalidClass] = true,\n\t                    _a[this.opts.rowValidatingClass] = false,\n\t                    _a[this.opts.rowValidClass] = false,\n\t                    _a));\n\t            }\n\t            else {\n\t                this.results.delete(element);\n\t                // Maybe there\'re multiple fields belong to the same row\n\t                var isValid_1 = true;\n\t                this.containers.forEach(function (value, key) {\n\t                    if (value === groupEle && _this.results.get(key) === false) {\n\t                        isValid_1 = false;\n\t                    }\n\t                });\n\t                // If all field(s) belonging to the row are valid\n\t                if (isValid_1) {\n\t                    classSet(groupEle, (_b = {},\n\t                        _b[this.opts.rowInvalidClass] = false,\n\t                        _b[this.opts.rowValidatingClass] = false,\n\t                        _b[this.opts.rowValidClass] = true,\n\t                        _b));\n\t                }\n\t            }\n\t        }\n\t    };\n\t    Framework.MESSAGE_PLUGIN = \'___frameworkMessage\';\n\t    return Framework;\n\t}(core.Plugin));\n\n\tcjs$s.Framework = Framework;\n\treturn cjs$s;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$t.exports = requireCjs$s();\n}\n\nvar libExports$s = lib$t.exports;\n\nvar lib$r = {exports: {}};\n\nvar index_min$r = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-icon\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$r;\n\nfunction requireIndex_min$r () {\n\tif (hasRequiredIndex_min$r) return index_min$r;\n\thasRequiredIndex_min$r = 1;\nvar e=libExports$B,t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);},t(e,n)};var n=e.utils.classSet,i=function(e){function i(t){var n=e.call(this,t)||this;return n.icons=new Map,n.opts=Object.assign({},{invalid:"fv-plugins-icon--invalid",onPlaced:function(){},onSet:function(){},valid:"fv-plugins-icon--valid",validating:"fv-plugins-icon--validating"},t),n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.elementIgnoredHandler=n.onElementIgnored.bind(n),n.fieldAddedHandler=n.onFieldAdded.bind(n),n}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e;}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i);}(i,e),i.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler);},i.prototype.uninstall=function(){this.icons.forEach((function(e){return e.parentNode.removeChild(e)})),this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler);},i.prototype.onEnabled=function(){this.icons.forEach((function(e,t,i){n(t,{"fv-plugins-icon--enabled":!0,"fv-plugins-icon--disabled":!1});}));},i.prototype.onDisabled=function(){this.icons.forEach((function(e,t,i){n(t,{"fv-plugins-icon--enabled":!1,"fv-plugins-icon--disabled":!0});}));},i.prototype.onFieldAdded=function(e){var t=this,n=e.elements;n&&(n.forEach((function(e){var n=t.icons.get(e);n&&(n.parentNode.removeChild(n),t.icons.delete(e));})),this.prepareFieldIcon(e.field,n));},i.prototype.prepareFieldIcon=function(e,t){var n=this;if(t.length){var i=t[0].getAttribute("type");"radio"===i||"checkbox"===i?this.prepareElementIcon(e,t[0]):t.forEach((function(t){return n.prepareElementIcon(e,t)}));}},i.prototype.prepareElementIcon=function(e,t){var i=document.createElement("i");i.setAttribute("data-field",e),t.parentNode.insertBefore(i,t.nextSibling),n(i,{"fv-plugins-icon":!0,"fv-plugins-icon--enabled":this.isEnabled,"fv-plugins-icon--disabled":!this.isEnabled});var o={classes:{invalid:this.opts.invalid,valid:this.opts.valid,validating:this.opts.validating},element:t,field:e,iconElement:i};this.core.emit("plugins.icon.placed",o),this.opts.onPlaced(o),this.icons.set(t,i);},i.prototype.onElementValidating=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!0,t)),i={element:e.element,field:e.field,iconElement:n,status:"Validating"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i);},i.prototype.onElementValidated=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!e.valid,t[this.opts.valid]=e.valid,t[this.opts.validating]=!1,t)),i={element:e.element,field:e.field,iconElement:n,status:e.valid?"Valid":"Invalid"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i);},i.prototype.onElementNotValidated=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),i={element:e.element,field:e.field,iconElement:n,status:"NotValidated"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i);},i.prototype.onElementIgnored=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),i={element:e.element,field:e.field,iconElement:n,status:"Ignored"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i);},i.prototype.setClasses=function(e,t,i,o){var l=t.getAttribute("type"),a="radio"===l||"checkbox"===l?i[0]:t;if(this.icons.has(a)){var s=this.icons.get(a);return n(s,o),s}return null},i}(e.Plugin);index_min$r.Icon=i;\n\treturn index_min$r;\n}\n\nvar cjs$r = {};\n\nvar hasRequiredCjs$r;\n\nfunction requireCjs$r () {\n\tif (hasRequiredCjs$r) return cjs$r;\n\thasRequiredCjs$r = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar classSet = core.utils.classSet;\n\tvar Icon = /** @class */ (function (_super) {\n\t    __extends(Icon, _super);\n\t    function Icon(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        // Map the field element with icon\n\t        _this.icons = new Map();\n\t        _this.opts = Object.assign({}, {\n\t            invalid: \'fv-plugins-icon--invalid\',\n\t            onPlaced: function () { },\n\t            onSet: function () { },\n\t            valid: \'fv-plugins-icon--valid\',\n\t            validating: \'fv-plugins-icon--validating\',\n\t        }, opts);\n\t        _this.elementValidatingHandler = _this.onElementValidating.bind(_this);\n\t        _this.elementValidatedHandler = _this.onElementValidated.bind(_this);\n\t        _this.elementNotValidatedHandler = _this.onElementNotValidated.bind(_this);\n\t        _this.elementIgnoredHandler = _this.onElementIgnored.bind(_this);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        return _this;\n\t    }\n\t    Icon.prototype.install = function () {\n\t        this.core\n\t            .on(\'core.element.validating\', this.elementValidatingHandler)\n\t            .on(\'core.element.validated\', this.elementValidatedHandler)\n\t            .on(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .on(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .on(\'core.field.added\', this.fieldAddedHandler);\n\t    };\n\t    Icon.prototype.uninstall = function () {\n\t        this.icons.forEach(function (icon) { return icon.parentNode.removeChild(icon); });\n\t        this.icons.clear();\n\t        this.core\n\t            .off(\'core.element.validating\', this.elementValidatingHandler)\n\t            .off(\'core.element.validated\', this.elementValidatedHandler)\n\t            .off(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .off(\'core.element.ignored\', this.elementIgnoredHandler)\n\t            .off(\'core.field.added\', this.fieldAddedHandler);\n\t    };\n\t    Icon.prototype.onEnabled = function () {\n\t        this.icons.forEach(function (_element, i, _map) {\n\t            classSet(i, {\n\t                \'fv-plugins-icon--enabled\': true,\n\t                \'fv-plugins-icon--disabled\': false,\n\t            });\n\t        });\n\t    };\n\t    Icon.prototype.onDisabled = function () {\n\t        this.icons.forEach(function (_element, i, _map) {\n\t            classSet(i, {\n\t                \'fv-plugins-icon--enabled\': false,\n\t                \'fv-plugins-icon--disabled\': true,\n\t            });\n\t        });\n\t    };\n\t    Icon.prototype.onFieldAdded = function (e) {\n\t        var _this = this;\n\t        var elements = e.elements;\n\t        if (elements) {\n\t            elements.forEach(function (ele) {\n\t                var icon = _this.icons.get(ele);\n\t                if (icon) {\n\t                    icon.parentNode.removeChild(icon);\n\t                    _this.icons.delete(ele);\n\t                }\n\t            });\n\t            this.prepareFieldIcon(e.field, elements);\n\t        }\n\t    };\n\t    Icon.prototype.prepareFieldIcon = function (field, elements) {\n\t        var _this = this;\n\t        if (elements.length) {\n\t            var type = elements[0].getAttribute(\'type\');\n\t            if (\'radio\' === type || \'checkbox\' === type) {\n\t                this.prepareElementIcon(field, elements[0]);\n\t            }\n\t            else {\n\t                elements.forEach(function (ele) { return _this.prepareElementIcon(field, ele); });\n\t            }\n\t        }\n\t    };\n\t    Icon.prototype.prepareElementIcon = function (field, ele) {\n\t        var i = document.createElement(\'i\');\n\t        i.setAttribute(\'data-field\', field);\n\t        // Append the icon right after the field element\n\t        ele.parentNode.insertBefore(i, ele.nextSibling);\n\t        classSet(i, {\n\t            \'fv-plugins-icon\': true,\n\t            \'fv-plugins-icon--enabled\': this.isEnabled,\n\t            \'fv-plugins-icon--disabled\': !this.isEnabled,\n\t        });\n\t        var e = {\n\t            classes: {\n\t                invalid: this.opts.invalid,\n\t                valid: this.opts.valid,\n\t                validating: this.opts.validating,\n\t            },\n\t            element: ele,\n\t            field: field,\n\t            iconElement: i,\n\t        };\n\t        this.core.emit(\'plugins.icon.placed\', e);\n\t        this.opts.onPlaced(e);\n\t        this.icons.set(ele, i);\n\t    };\n\t    Icon.prototype.onElementValidating = function (e) {\n\t        var _a;\n\t        var icon = this.setClasses(e.field, e.element, e.elements, (_a = {},\n\t            _a[this.opts.invalid] = false,\n\t            _a[this.opts.valid] = false,\n\t            _a[this.opts.validating] = true,\n\t            _a));\n\t        var evt = {\n\t            element: e.element,\n\t            field: e.field,\n\t            iconElement: icon,\n\t            status: \'Validating\',\n\t        };\n\t        this.core.emit(\'plugins.icon.set\', evt);\n\t        this.opts.onSet(evt);\n\t    };\n\t    Icon.prototype.onElementValidated = function (e) {\n\t        var _a;\n\t        var icon = this.setClasses(e.field, e.element, e.elements, (_a = {},\n\t            _a[this.opts.invalid] = !e.valid,\n\t            _a[this.opts.valid] = e.valid,\n\t            _a[this.opts.validating] = false,\n\t            _a));\n\t        var evt = {\n\t            element: e.element,\n\t            field: e.field,\n\t            iconElement: icon,\n\t            status: e.valid ? \'Valid\' : \'Invalid\',\n\t        };\n\t        this.core.emit(\'plugins.icon.set\', evt);\n\t        this.opts.onSet(evt);\n\t    };\n\t    Icon.prototype.onElementNotValidated = function (e) {\n\t        var _a;\n\t        var icon = this.setClasses(e.field, e.element, e.elements, (_a = {},\n\t            _a[this.opts.invalid] = false,\n\t            _a[this.opts.valid] = false,\n\t            _a[this.opts.validating] = false,\n\t            _a));\n\t        var evt = {\n\t            element: e.element,\n\t            field: e.field,\n\t            iconElement: icon,\n\t            status: \'NotValidated\',\n\t        };\n\t        this.core.emit(\'plugins.icon.set\', evt);\n\t        this.opts.onSet(evt);\n\t    };\n\t    Icon.prototype.onElementIgnored = function (e) {\n\t        var _a;\n\t        var icon = this.setClasses(e.field, e.element, e.elements, (_a = {},\n\t            _a[this.opts.invalid] = false,\n\t            _a[this.opts.valid] = false,\n\t            _a[this.opts.validating] = false,\n\t            _a));\n\t        var evt = {\n\t            element: e.element,\n\t            field: e.field,\n\t            iconElement: icon,\n\t            status: \'Ignored\',\n\t        };\n\t        this.core.emit(\'plugins.icon.set\', evt);\n\t        this.opts.onSet(evt);\n\t    };\n\t    Icon.prototype.setClasses = function (_field, element, elements, classes) {\n\t        var type = element.getAttribute(\'type\');\n\t        var ele = \'radio\' === type || \'checkbox\' === type ? elements[0] : element;\n\t        if (this.icons.has(ele)) {\n\t            var icon = this.icons.get(ele);\n\t            classSet(icon, classes);\n\t            return icon;\n\t        }\n\t        else {\n\t            return null;\n\t        }\n\t    };\n\t    return Icon;\n\t}(core.Plugin));\n\n\tcjs$r.Icon = Icon;\n\treturn cjs$r;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$r.exports = requireCjs$r();\n}\n\nvar libExports$r = lib$r.exports;\n\nvar lib$q = {exports: {}};\n\nvar index_min$q = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-sequence\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$q;\n\nfunction requireIndex_min$q () {\n\tif (hasRequiredIndex_min$q) return index_min$q;\n\thasRequiredIndex_min$q = 1;\nvar e=libExports$B,t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);},t(e,i)};var i=e.utils.removeUndefined,l=function(e){function l(t){var l=e.call(this,t)||this;return l.invalidFields=new Map,l.opts=Object.assign({},{enabled:!0},i(t)),l.validatorHandler=l.onValidatorValidated.bind(l),l.shouldValidateFilter=l.shouldValidate.bind(l),l.fieldAddedHandler=l.onFieldAdded.bind(l),l.elementNotValidatedHandler=l.onElementNotValidated.bind(l),l.elementValidatingHandler=l.onElementValidating.bind(l),l}return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function l(){this.constructor=e;}t(e,i),e.prototype=null===i?Object.create(i):(l.prototype=i.prototype,new l);}(l,e),l.prototype.install=function(){this.core.on("core.validator.validated",this.validatorHandler).on("core.field.added",this.fieldAddedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.validating",this.elementValidatingHandler).registerFilter("field-should-validate",this.shouldValidateFilter);},l.prototype.uninstall=function(){this.invalidFields.clear(),this.core.off("core.validator.validated",this.validatorHandler).off("core.field.added",this.fieldAddedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.validating",this.elementValidatingHandler).deregisterFilter("field-should-validate",this.shouldValidateFilter);},l.prototype.shouldValidate=function(e,t,i,l){return !this.isEnabled||!((!0===this.opts.enabled||!0===this.opts.enabled[e])&&this.invalidFields.has(t)&&!!this.invalidFields.get(t).length&&-1===this.invalidFields.get(t).indexOf(l))},l.prototype.onValidatorValidated=function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],i=t.indexOf(e.validator);e.result.valid&&i>=0?t.splice(i,1):e.result.valid||-1!==i||t.push(e.validator),this.invalidFields.set(e.element,t);},l.prototype.onFieldAdded=function(e){e.elements&&this.clearInvalidFields(e.elements);},l.prototype.onElementNotValidated=function(e){this.clearInvalidFields(e.elements);},l.prototype.onElementValidating=function(e){this.clearInvalidFields(e.elements);},l.prototype.clearInvalidFields=function(e){var t=this;e.forEach((function(e){return t.invalidFields.delete(e)}));},l}(e.Plugin);index_min$q.Sequence=l;\n\treturn index_min$q;\n}\n\nvar cjs$q = {};\n\nvar hasRequiredCjs$q;\n\nfunction requireCjs$q () {\n\tif (hasRequiredCjs$q) return cjs$q;\n\thasRequiredCjs$q = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\t/**\n\t * ```\n\t *  new Core(form, { ... })\n\t *      .registerPlugin(\'sequence\', new Sequence({\n\t *          enabled: false // Default value is `true`\n\t *      }));\n\t * ```\n\t *\n\t * The `enabled` option can be:\n\t * - `true` (default): When a field has multiple validators, all of them will be checked respectively.\n\t * If errors occur in multiple validators, all of them will be displayed to the user\n\t * - `false`: When a field has multiple validators, validation for this field will be terminated upon the\n\t * first encountered error.\n\t * Thus, only the very first error message related to this field will be displayed to the user\n\t *\n\t * User can set the `enabled` option to all fields as sample code above, or apply it for specific fields as following:\n\t * ```\n\t *  new Core(form, { ... })\n\t *      .registerPlugin(\'sequence\', new Sequence({\n\t *          enabled: {\n\t *              fullName: true, // It\'s not necessary since the default value is `true`\n\t *              username: false,\n\t *              email: false\n\t *          }\n\t *      }));\n\t * ```\n\t */\n\tvar Sequence = /** @class */ (function (_super) {\n\t    __extends(Sequence, _super);\n\t    function Sequence(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.invalidFields = new Map();\n\t        _this.opts = Object.assign({}, { enabled: true }, removeUndefined(opts));\n\t        _this.validatorHandler = _this.onValidatorValidated.bind(_this);\n\t        _this.shouldValidateFilter = _this.shouldValidate.bind(_this);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        _this.elementNotValidatedHandler = _this.onElementNotValidated.bind(_this);\n\t        _this.elementValidatingHandler = _this.onElementValidating.bind(_this);\n\t        return _this;\n\t    }\n\t    Sequence.prototype.install = function () {\n\t        this.core\n\t            .on(\'core.validator.validated\', this.validatorHandler)\n\t            .on(\'core.field.added\', this.fieldAddedHandler)\n\t            .on(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .on(\'core.element.validating\', this.elementValidatingHandler)\n\t            .registerFilter(\'field-should-validate\', this.shouldValidateFilter);\n\t    };\n\t    Sequence.prototype.uninstall = function () {\n\t        this.invalidFields.clear();\n\t        this.core\n\t            .off(\'core.validator.validated\', this.validatorHandler)\n\t            .off(\'core.field.added\', this.fieldAddedHandler)\n\t            .off(\'core.element.notvalidated\', this.elementNotValidatedHandler)\n\t            .off(\'core.element.validating\', this.elementValidatingHandler)\n\t            .deregisterFilter(\'field-should-validate\', this.shouldValidateFilter);\n\t    };\n\t    Sequence.prototype.shouldValidate = function (field, element, _value, validator) {\n\t        if (!this.isEnabled) {\n\t            return true;\n\t        }\n\t        // Stop validating\n\t        // if the `enabled` option is set to `false`\n\t        // and there\'s at least one validator that field doesn\'t pass\n\t        var stop = (this.opts.enabled === true || this.opts.enabled[field] === true) &&\n\t            this.invalidFields.has(element) &&\n\t            !!this.invalidFields.get(element).length &&\n\t            this.invalidFields.get(element).indexOf(validator) === -1;\n\t        return !stop;\n\t    };\n\t    Sequence.prototype.onValidatorValidated = function (e) {\n\t        var validators = this.invalidFields.has(e.element) ? this.invalidFields.get(e.element) : [];\n\t        var index = validators.indexOf(e.validator);\n\t        if (e.result.valid && index >= 0) {\n\t            validators.splice(index, 1);\n\t        }\n\t        else if (!e.result.valid && index === -1) {\n\t            validators.push(e.validator);\n\t        }\n\t        this.invalidFields.set(e.element, validators);\n\t    };\n\t    Sequence.prototype.onFieldAdded = function (e) {\n\t        // Remove the field element from set of invalid elements\n\t        if (e.elements) {\n\t            this.clearInvalidFields(e.elements);\n\t        }\n\t    };\n\t    Sequence.prototype.onElementNotValidated = function (e) {\n\t        this.clearInvalidFields(e.elements);\n\t    };\n\t    Sequence.prototype.onElementValidating = function (e) {\n\t        this.clearInvalidFields(e.elements);\n\t    };\n\t    Sequence.prototype.clearInvalidFields = function (elements) {\n\t        var _this = this;\n\t        elements.forEach(function (ele) { return _this.invalidFields.delete(ele); });\n\t    };\n\t    return Sequence;\n\t}(core.Plugin));\n\n\tcjs$q.Sequence = Sequence;\n\treturn cjs$q;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$q.exports = requireCjs$q();\n}\n\nvar libExports$q = lib$q.exports;\n\nvar lib$p = {exports: {}};\n\nvar index_min$p = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-submit-button\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$p;\n\nfunction requireIndex_min$p () {\n\tif (hasRequiredIndex_min$p) return index_min$p;\n\thasRequiredIndex_min$p = 1;\nvar t=libExports$B,e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e;}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);},e(t,i)};var i=function(t){function i(e){var i=t.call(this,e)||this;return i.isFormValid=!1,i.isButtonClicked=!1,i.opts=Object.assign({},{aspNetButton:!1,buttons:function(t){return [].slice.call(t.querySelectorAll(\'[type="submit"]:not([formnovalidate])\'))},liveMode:!0},e),i.submitHandler=i.handleSubmitEvent.bind(i),i.buttonClickHandler=i.handleClickEvent.bind(i),i.ignoreValidationFilter=i.ignoreValidation.bind(i),i}return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t;}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n);}(i,t),i.prototype.install=function(){var t=this;if(this.core.getFormElement()instanceof HTMLFormElement){var e=this.core.getFormElement();this.submitButtons=this.opts.buttons(e),e.setAttribute("novalidate","novalidate"),e.addEventListener("submit",this.submitHandler),this.hiddenClickedEle=document.createElement("input"),this.hiddenClickedEle.setAttribute("type","hidden"),e.appendChild(this.hiddenClickedEle),this.submitButtons.forEach((function(e){e.addEventListener("click",t.buttonClickHandler);})),this.core.registerFilter("element-ignored",this.ignoreValidationFilter);}},i.prototype.uninstall=function(){var t=this,e=this.core.getFormElement();e instanceof HTMLFormElement&&e.removeEventListener("submit",this.submitHandler),this.submitButtons.forEach((function(e){e.removeEventListener("click",t.buttonClickHandler);})),this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle),this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter);},i.prototype.handleSubmitEvent=function(t){this.validateForm(t);},i.prototype.handleClickEvent=function(t){var e=t.currentTarget;if(this.isButtonClicked=!0,e instanceof HTMLElement)if(this.opts.aspNetButton&&!0===this.isFormValid);else {this.core.getFormElement().removeEventListener("submit",this.submitHandler),this.clickedButton=t.target;var i=this.clickedButton.getAttribute("name"),n=this.clickedButton.getAttribute("value");i&&n&&(this.hiddenClickedEle.setAttribute("name",i),this.hiddenClickedEle.setAttribute("value",n)),this.validateForm(t);}},i.prototype.validateForm=function(t){var e=this;this.isEnabled&&(t.preventDefault(),this.core.validate().then((function(t){"Valid"===t&&e.opts.aspNetButton&&!e.isFormValid&&e.clickedButton&&(e.isFormValid=!0,e.clickedButton.removeEventListener("click",e.buttonClickHandler),e.clickedButton.click());})));},i.prototype.ignoreValidation=function(t,e,i){return !!this.isEnabled&&(!this.opts.liveMode&&!this.isButtonClicked)},i}(t.Plugin);index_min$p.SubmitButton=i;\n\treturn index_min$p;\n}\n\nvar cjs$p = {};\n\nvar hasRequiredCjs$p;\n\nfunction requireCjs$p () {\n\tif (hasRequiredCjs$p) return cjs$p;\n\thasRequiredCjs$p = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar SubmitButton = /** @class */ (function (_super) {\n\t    __extends(SubmitButton, _super);\n\t    function SubmitButton(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.isFormValid = false;\n\t        _this.isButtonClicked = false;\n\t        _this.opts = Object.assign({}, {\n\t            // Set it to `true` to support classical ASP.Net form\n\t            aspNetButton: false,\n\t            // By default, don\'t perform validation when clicking on\n\t            // the submit button/input which have `formnovalidate` attribute\n\t            buttons: function (form) {\n\t                return [].slice.call(form.querySelectorAll(\'[type="submit"]:not([formnovalidate])\'));\n\t            },\n\t            liveMode: true,\n\t        }, opts);\n\t        _this.submitHandler = _this.handleSubmitEvent.bind(_this);\n\t        _this.buttonClickHandler = _this.handleClickEvent.bind(_this);\n\t        _this.ignoreValidationFilter = _this.ignoreValidation.bind(_this);\n\t        return _this;\n\t    }\n\t    SubmitButton.prototype.install = function () {\n\t        var _this = this;\n\t        if (!(this.core.getFormElement() instanceof HTMLFormElement)) {\n\t            return;\n\t        }\n\t        var form = this.core.getFormElement();\n\t        this.submitButtons = this.opts.buttons(form);\n\t        // Disable client side validation in HTML 5\n\t        form.setAttribute(\'novalidate\', \'novalidate\');\n\t        // Disable the default submission first\n\t        form.addEventListener(\'submit\', this.submitHandler);\n\t        this.hiddenClickedEle = document.createElement(\'input\');\n\t        this.hiddenClickedEle.setAttribute(\'type\', \'hidden\');\n\t        form.appendChild(this.hiddenClickedEle);\n\t        this.submitButtons.forEach(function (button) {\n\t            button.addEventListener(\'click\', _this.buttonClickHandler);\n\t        });\n\t        this.core.registerFilter(\'element-ignored\', this.ignoreValidationFilter);\n\t    };\n\t    SubmitButton.prototype.uninstall = function () {\n\t        var _this = this;\n\t        var form = this.core.getFormElement();\n\t        if (form instanceof HTMLFormElement) {\n\t            form.removeEventListener(\'submit\', this.submitHandler);\n\t        }\n\t        this.submitButtons.forEach(function (button) {\n\t            button.removeEventListener(\'click\', _this.buttonClickHandler);\n\t        });\n\t        this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle);\n\t        this.core.deregisterFilter(\'element-ignored\', this.ignoreValidationFilter);\n\t    };\n\t    SubmitButton.prototype.handleSubmitEvent = function (e) {\n\t        this.validateForm(e);\n\t    };\n\t    SubmitButton.prototype.handleClickEvent = function (e) {\n\t        var target = e.currentTarget;\n\t        this.isButtonClicked = true;\n\t        if (target instanceof HTMLElement) {\n\t            if (this.opts.aspNetButton && this.isFormValid === true) ;\n\t            else {\n\t                var form = this.core.getFormElement();\n\t                form.removeEventListener(\'submit\', this.submitHandler);\n\t                this.clickedButton = e.target;\n\t                var name_1 = this.clickedButton.getAttribute(\'name\');\n\t                var value = this.clickedButton.getAttribute(\'value\');\n\t                if (name_1 && value) {\n\t                    this.hiddenClickedEle.setAttribute(\'name\', name_1);\n\t                    this.hiddenClickedEle.setAttribute(\'value\', value);\n\t                }\n\t                this.validateForm(e);\n\t            }\n\t        }\n\t    };\n\t    SubmitButton.prototype.validateForm = function (e) {\n\t        var _this = this;\n\t        if (!this.isEnabled) {\n\t            return;\n\t        }\n\t        e.preventDefault();\n\t        this.core.validate().then(function (result) {\n\t            if (result === \'Valid\' && _this.opts.aspNetButton && !_this.isFormValid && _this.clickedButton) {\n\t                _this.isFormValid = true;\n\t                _this.clickedButton.removeEventListener(\'click\', _this.buttonClickHandler);\n\t                // It\'s the time for ASP.Net submit button to do its own submission\n\t                _this.clickedButton.click();\n\t            }\n\t        });\n\t    };\n\t    SubmitButton.prototype.ignoreValidation = function (_field, _element, _elements) {\n\t        if (!this.isEnabled) {\n\t            return false;\n\t        }\n\t        return this.opts.liveMode ? false : !this.isButtonClicked;\n\t    };\n\t    return SubmitButton;\n\t}(core.Plugin));\n\n\tcjs$p.SubmitButton = SubmitButton;\n\treturn cjs$p;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$p.exports = requireCjs$p();\n}\n\nvar libExports$p = lib$p.exports;\n\nvar lib$o = {exports: {}};\n\nvar index_min$o = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-tooltip\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$o;\n\nfunction requireIndex_min$o () {\n\tif (hasRequiredIndex_min$o) return index_min$o;\n\thasRequiredIndex_min$o = 1;\nvar t=libExports$B,e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e;}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);},e(t,i)};var i=t.utils.classSet,o=function(t){function o(e){var i=t.call(this,e)||this;return i.messages=new Map,i.opts=Object.assign({},{placement:"top",trigger:"click"},e),i.iconPlacedHandler=i.onIconPlaced.bind(i),i.validatorValidatedHandler=i.onValidatorValidated.bind(i),i.elementValidatedHandler=i.onElementValidated.bind(i),i.documentClickHandler=i.onDocumentClicked.bind(i),i}return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function o(){this.constructor=t;}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o);}(o,t),o.prototype.install=function(){var t;this.tip=document.createElement("div"),i(this.tip,((t={"fv-plugins-tooltip":!0})["fv-plugins-tooltip--".concat(this.opts.placement)]=!0,t)),document.body.appendChild(this.tip),this.core.on("plugins.icon.placed",this.iconPlacedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.addEventListener("click",this.documentClickHandler);},o.prototype.uninstall=function(){this.messages.clear(),document.body.removeChild(this.tip),this.core.off("plugins.icon.placed",this.iconPlacedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.removeEventListener("click",this.documentClickHandler);},o.prototype.onIconPlaced=function(t){var e=this;if(i(t.iconElement,{"fv-plugins-tooltip-icon":!0}),"hover"===this.opts.trigger)t.iconElement.addEventListener("mouseenter",(function(i){return e.show(t.element,i)})),t.iconElement.addEventListener("mouseleave",(function(t){return e.hide()}));else t.iconElement.addEventListener("click",(function(i){return e.show(t.element,i)}));},o.prototype.onValidatorValidated=function(t){if(!t.result.valid){var e=t.elements,i=t.element.getAttribute("type"),o="radio"===i||"checkbox"===i?e[0]:t.element,n="string"==typeof t.result.message?t.result.message:t.result.message[this.core.getLocale()];this.messages.set(o,n);}},o.prototype.onElementValidated=function(t){if(t.valid){var e=t.elements,i=t.element.getAttribute("type"),o="radio"===i||"checkbox"===i?e[0]:t.element;this.messages.delete(o);}},o.prototype.onDocumentClicked=function(t){this.hide();},o.prototype.show=function(t,e){if(this.isEnabled&&(e.preventDefault(),e.stopPropagation(),this.messages.has(t))){i(this.tip,{"fv-plugins-tooltip--hide":!1}),this.tip.innerHTML=\'<div class="fv-plugins-tooltip__content">\'.concat(this.messages.get(t),"</div>");var o=e.target.getBoundingClientRect(),n=this.tip.getBoundingClientRect(),l=n.height,a=n.width,s=0,r=0;switch(this.opts.placement){case"bottom":s=o.top+o.height,r=o.left+o.width/2-a/2;break;case"bottom-left":s=o.top+o.height,r=o.left;break;case"bottom-right":s=o.top+o.height,r=o.left+o.width-a;break;case"left":s=o.top+o.height/2-l/2,r=o.left-a;break;case"right":s=o.top+o.height/2-l/2,r=o.left+o.width;break;case"top-left":s=o.top-l,r=o.left;break;case"top-right":s=o.top-l,r=o.left+o.width-a;break;default:s=o.top-l,r=o.left+o.width/2-a/2;}s+=window.scrollY||document.documentElement.scrollTop||document.body.scrollTop||0,r+=window.scrollX||document.documentElement.scrollLeft||document.body.scrollLeft||0,this.tip.setAttribute("style","top: ".concat(s,"px; left: ").concat(r,"px"));}},o.prototype.hide=function(){this.isEnabled&&i(this.tip,{"fv-plugins-tooltip--hide":!0});},o}(t.Plugin);index_min$o.Tooltip=o;\n\treturn index_min$o;\n}\n\nvar cjs$o = {};\n\nvar hasRequiredCjs$o;\n\nfunction requireCjs$o () {\n\tif (hasRequiredCjs$o) return cjs$o;\n\thasRequiredCjs$o = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar classSet = core.utils.classSet;\n\tvar Tooltip = /** @class */ (function (_super) {\n\t    __extends(Tooltip, _super);\n\t    function Tooltip(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        // Map the element with message\n\t        _this.messages = new Map();\n\t        _this.opts = Object.assign({}, {\n\t            placement: \'top\',\n\t            trigger: \'click\',\n\t        }, opts);\n\t        _this.iconPlacedHandler = _this.onIconPlaced.bind(_this);\n\t        _this.validatorValidatedHandler = _this.onValidatorValidated.bind(_this);\n\t        _this.elementValidatedHandler = _this.onElementValidated.bind(_this);\n\t        _this.documentClickHandler = _this.onDocumentClicked.bind(_this);\n\t        return _this;\n\t    }\n\t    Tooltip.prototype.install = function () {\n\t        var _a;\n\t        this.tip = document.createElement(\'div\');\n\t        classSet(this.tip, (_a = {\n\t                \'fv-plugins-tooltip\': true\n\t            },\n\t            _a["fv-plugins-tooltip--".concat(this.opts.placement)] = true,\n\t            _a));\n\t        document.body.appendChild(this.tip);\n\t        this.core\n\t            .on(\'plugins.icon.placed\', this.iconPlacedHandler)\n\t            .on(\'core.validator.validated\', this.validatorValidatedHandler)\n\t            .on(\'core.element.validated\', this.elementValidatedHandler);\n\t        if (\'click\' === this.opts.trigger) {\n\t            document.addEventListener(\'click\', this.documentClickHandler);\n\t        }\n\t    };\n\t    Tooltip.prototype.uninstall = function () {\n\t        this.messages.clear();\n\t        document.body.removeChild(this.tip);\n\t        this.core\n\t            .off(\'plugins.icon.placed\', this.iconPlacedHandler)\n\t            .off(\'core.validator.validated\', this.validatorValidatedHandler)\n\t            .off(\'core.element.validated\', this.elementValidatedHandler);\n\t        if (\'click\' === this.opts.trigger) {\n\t            document.removeEventListener(\'click\', this.documentClickHandler);\n\t        }\n\t    };\n\t    Tooltip.prototype.onIconPlaced = function (e) {\n\t        var _this = this;\n\t        classSet(e.iconElement, {\n\t            \'fv-plugins-tooltip-icon\': true,\n\t        });\n\t        switch (this.opts.trigger) {\n\t            case \'hover\':\n\t                e.iconElement.addEventListener(\'mouseenter\', function (evt) { return _this.show(e.element, evt); });\n\t                e.iconElement.addEventListener(\'mouseleave\', function (_evt) { return _this.hide(); });\n\t                break;\n\t            case \'click\':\n\t            default:\n\t                e.iconElement.addEventListener(\'click\', function (evt) { return _this.show(e.element, evt); });\n\t                break;\n\t        }\n\t    };\n\t    Tooltip.prototype.onValidatorValidated = function (e) {\n\t        if (!e.result.valid) {\n\t            var elements = e.elements;\n\t            var type = e.element.getAttribute(\'type\');\n\t            var ele = \'radio\' === type || \'checkbox\' === type ? elements[0] : e.element;\n\t            // Get the message\n\t            var message = typeof e.result.message === \'string\' ? e.result.message : e.result.message[this.core.getLocale()];\n\t            this.messages.set(ele, message);\n\t        }\n\t    };\n\t    Tooltip.prototype.onElementValidated = function (e) {\n\t        if (e.valid) {\n\t            // Clear the message\n\t            var elements = e.elements;\n\t            var type = e.element.getAttribute(\'type\');\n\t            var ele = \'radio\' === type || \'checkbox\' === type ? elements[0] : e.element;\n\t            this.messages.delete(ele);\n\t        }\n\t    };\n\t    Tooltip.prototype.onDocumentClicked = function (_e) {\n\t        this.hide();\n\t    };\n\t    Tooltip.prototype.show = function (ele, e) {\n\t        if (!this.isEnabled) {\n\t            return;\n\t        }\n\t        e.preventDefault();\n\t        e.stopPropagation();\n\t        if (!this.messages.has(ele)) {\n\t            return;\n\t        }\n\t        classSet(this.tip, {\n\t            \'fv-plugins-tooltip--hide\': false,\n\t        });\n\t        this.tip.innerHTML = "<div class=\\"fv-plugins-tooltip__content\\">".concat(this.messages.get(ele), "</div>");\n\t        // Calculate position of the icon element\n\t        var icon = e.target;\n\t        var targetRect = icon.getBoundingClientRect();\n\t        var _a = this.tip.getBoundingClientRect(), height = _a.height, width = _a.width;\n\t        var top = 0;\n\t        var left = 0;\n\t        switch (this.opts.placement) {\n\t            case \'bottom\':\n\t                top = targetRect.top + targetRect.height;\n\t                left = targetRect.left + targetRect.width / 2 - width / 2;\n\t                break;\n\t            case \'bottom-left\':\n\t                top = targetRect.top + targetRect.height;\n\t                left = targetRect.left;\n\t                break;\n\t            case \'bottom-right\':\n\t                top = targetRect.top + targetRect.height;\n\t                left = targetRect.left + targetRect.width - width;\n\t                break;\n\t            case \'left\':\n\t                top = targetRect.top + targetRect.height / 2 - height / 2;\n\t                left = targetRect.left - width;\n\t                break;\n\t            case \'right\':\n\t                top = targetRect.top + targetRect.height / 2 - height / 2;\n\t                left = targetRect.left + targetRect.width;\n\t                break;\n\t            case \'top-left\':\n\t                top = targetRect.top - height;\n\t                left = targetRect.left;\n\t                break;\n\t            case \'top-right\':\n\t                top = targetRect.top - height;\n\t                left = targetRect.left + targetRect.width - width;\n\t                break;\n\t            case \'top\':\n\t            default:\n\t                top = targetRect.top - height;\n\t                left = targetRect.left + targetRect.width / 2 - width / 2;\n\t                break;\n\t        }\n\t        var scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;\n\t        var scrollLeft = window.scrollX || document.documentElement.scrollLeft || document.body.scrollLeft || 0;\n\t        top = top + scrollTop;\n\t        left = left + scrollLeft;\n\t        this.tip.setAttribute(\'style\', "top: ".concat(top, "px; left: ").concat(left, "px"));\n\t    };\n\t    Tooltip.prototype.hide = function () {\n\t        if (this.isEnabled) {\n\t            classSet(this.tip, {\n\t                \'fv-plugins-tooltip--hide\': true,\n\t            });\n\t        }\n\t    };\n\t    return Tooltip;\n\t}(core.Plugin));\n\n\tcjs$o.Tooltip = Tooltip;\n\treturn cjs$o;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$o.exports = requireCjs$o();\n}\n\nvar libExports$o = lib$o.exports;\n\nvar lib$n = {exports: {}};\n\nvar index_min$n = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/plugin-trigger\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$n;\n\nfunction requireIndex_min$n () {\n\tif (hasRequiredIndex_min$n) return index_min$n;\n\thasRequiredIndex_min$n = 1;\nvar e=libExports$B,t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t;}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);},t(e,n)};var n=function(e){function n(t){var n=e.call(this,t)||this;n.handlers=[],n.timers=new Map;var r=document.createElement("div");return n.defaultEvent="oninput"in r?"input":"keyup",n.opts=Object.assign({},{delay:0,event:n.defaultEvent,threshold:0},t),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e;}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r);}(n,e),n.prototype.install=function(){this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler);},n.prototype.uninstall=function(){this.handlers.forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.handlers=[],this.timers.forEach((function(e){return window.clearTimeout(e)})),this.timers.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler);},n.prototype.prepareHandler=function(e,t){var n=this;t.forEach((function(t){var r=[];if(n.opts.event&&!1===n.opts.event[e])r=[];else if(n.opts.event&&n.opts.event[e]&&"function"!=typeof n.opts.event[e])r=n.opts.event[e].split(" ");else if("string"==typeof n.opts.event&&n.opts.event!==n.defaultEvent)r=n.opts.event.split(" ");else {var o=t.getAttribute("type"),i=t.tagName.toLowerCase();r=["radio"===o||"checkbox"===o||"file"===o||"select"===i?"change":n.ieVersion>=10&&t.getAttribute("placeholder")?"keyup":n.defaultEvent];}r.forEach((function(r){var o=function(r){return n.handleEvent(r,e,t)};n.handlers.push({element:t,event:r,field:e,handler:o}),t.addEventListener(r,o);}));}));},n.prototype.handleEvent=function(e,t,n){var r=this;if(this.isEnabled&&this.exceedThreshold(t,n)&&this.core.executeFilter("plugins-trigger-should-validate",!0,[t,n])){var o=function(){return r.core.validateElement(t,n).then((function(o){r.core.emit("plugins.trigger.executed",{element:n,event:e,field:t});}))},i=this.opts.delay[t]||this.opts.delay;if(0===i)o();else {var l=this.timers.get(n);l&&window.clearTimeout(l),this.timers.set(n,window.setTimeout(o,1e3*i));}}},n.prototype.onFieldAdded=function(e){this.handlers.filter((function(t){return t.field===e.field})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.prepareHandler(e.field,e.elements);},n.prototype.onFieldRemoved=function(e){this.handlers.filter((function(t){return t.field===e.field&&e.elements.indexOf(t.element)>=0})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)}));},n.prototype.exceedThreshold=function(e,t){var n=0!==this.opts.threshold[e]&&0!==this.opts.threshold&&(this.opts.threshold[e]||this.opts.threshold);if(!n)return !0;var r=t.getAttribute("type");return -1!==["button","checkbox","file","hidden","image","radio","reset","submit"].indexOf(r)||this.core.getElementValue(e,t).length>=n},n}(e.Plugin);index_min$n.Trigger=n;\n\treturn index_min$n;\n}\n\nvar cjs$n = {};\n\nvar hasRequiredCjs$n;\n\nfunction requireCjs$n () {\n\tif (hasRequiredCjs$n) return cjs$n;\n\thasRequiredCjs$n = 1;\n\n\tvar core = libExports$B;\n\n\t/******************************************************************************\r\n\tCopyright (c) Microsoft Corporation.\r\n\r\n\tPermission to use, copy, modify, and/or distribute this software for any\r\n\tpurpose with or without fee is hereby granted.\r\n\r\n\tTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n\tREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n\tAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n\tINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n\tLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n\tOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n\tPERFORMANCE OF THIS SOFTWARE.\r\n\t***************************************************************************** */\r\n\t/* global Reflect, Promise */\r\n\r\n\tvar extendStatics = function(d, b) {\r\n\t    extendStatics = Object.setPrototypeOf ||\r\n\t        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n\t        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\t    return extendStatics(d, b);\r\n\t};\r\n\r\n\tfunction __extends(d, b) {\r\n\t    if (typeof b !== "function" && b !== null)\r\n\t        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");\r\n\t    extendStatics(d, b);\r\n\t    function __() { this.constructor = d; }\r\n\t    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n\t}\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * Indicate the events which the validation will be executed when these events are triggered\n\t *\n\t * ```\n\t *  const fv = formValidation(form, {\n\t *      fields: {\n\t *          fullName: {},\n\t *          email: {},\n\t *      },\n\t *  });\n\t *\n\t *  // Validate fields when the `blur` events are triggered\n\t *  fv.registerPlugin(Trigger, {\n\t *      event: \'blur\',\n\t *  });\n\t *\n\t *  // We can indicate different events for each particular field\n\t *  fv.registerPlugin(Trigger, {\n\t *      event: {\n\t *          fullName: \'blur\',\n\t *          email: \'change\',\n\t *      },\n\t *  });\n\t *\n\t *  // If we don\'t want the field to be validated automatically, set the associate value to `false`\n\t *  fv.registerPlugin(Trigger, {\n\t *      event: {\n\t *          email: false,    // The field is only validated when we click the submit button of form\n\t *      },\n\t *  });\n\t * ```\n\t */\n\tvar Trigger = /** @class */ (function (_super) {\n\t    __extends(Trigger, _super);\n\t    function Trigger(opts) {\n\t        var _this = _super.call(this, opts) || this;\n\t        _this.handlers = [];\n\t        _this.timers = new Map();\n\t        var ele = document.createElement(\'div\');\n\t        _this.defaultEvent = !(\'oninput\' in ele) ? \'keyup\' : \'input\';\n\t        _this.opts = Object.assign({}, {\n\t            delay: 0,\n\t            event: _this.defaultEvent,\n\t            threshold: 0,\n\t        }, opts);\n\t        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n\t        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n\t        return _this;\n\t    }\n\t    Trigger.prototype.install = function () {\n\t        this.core.on(\'core.field.added\', this.fieldAddedHandler).on(\'core.field.removed\', this.fieldRemovedHandler);\n\t    };\n\t    Trigger.prototype.uninstall = function () {\n\t        this.handlers.forEach(function (item) { return item.element.removeEventListener(item.event, item.handler); });\n\t        this.handlers = [];\n\t        this.timers.forEach(function (t) { return window.clearTimeout(t); });\n\t        this.timers.clear();\n\t        this.core.off(\'core.field.added\', this.fieldAddedHandler).off(\'core.field.removed\', this.fieldRemovedHandler);\n\t    };\n\t    Trigger.prototype.prepareHandler = function (field, elements) {\n\t        var _this = this;\n\t        elements.forEach(function (ele) {\n\t            var events = [];\n\t            if (!!_this.opts.event && _this.opts.event[field] === false) {\n\t                events = [];\n\t            }\n\t            else if (!!_this.opts.event && !!_this.opts.event[field] && typeof _this.opts.event[field] !== \'function\') {\n\t                // To fix the case where `field` is a special property of String\n\t                // For example, `link` is the special function of `String.prototype`\n\t                // In this case, `this.opts.event[field]` is a function, not a string\n\t                events = _this.opts.event[field].split(\' \');\n\t            }\n\t            else if (\'string\' === typeof _this.opts.event && _this.opts.event !== _this.defaultEvent) {\n\t                events = _this.opts.event.split(\' \');\n\t            }\n\t            else {\n\t                var type = ele.getAttribute(\'type\');\n\t                var tagName = ele.tagName.toLowerCase();\n\t                // IE10/11 fires the `input` event when focus on the field having a placeholder\n\t                var event_1 = \'radio\' === type || \'checkbox\' === type || \'file\' === type || \'select\' === tagName\n\t                    ? \'change\'\n\t                    : _this.ieVersion >= 10 && ele.getAttribute(\'placeholder\')\n\t                        ? \'keyup\'\n\t                        : _this.defaultEvent;\n\t                events = [event_1];\n\t            }\n\t            events.forEach(function (evt) {\n\t                var evtHandler = function (e) { return _this.handleEvent(e, field, ele); };\n\t                _this.handlers.push({\n\t                    element: ele,\n\t                    event: evt,\n\t                    field: field,\n\t                    handler: evtHandler,\n\t                });\n\t                ele.addEventListener(evt, evtHandler);\n\t            });\n\t        });\n\t    };\n\t    Trigger.prototype.handleEvent = function (e, field, ele) {\n\t        var _this = this;\n\t        if (this.isEnabled &&\n\t            this.exceedThreshold(field, ele) &&\n\t            this.core.executeFilter(\'plugins-trigger-should-validate\', true, [field, ele])) {\n\t            var handler = function () {\n\t                return _this.core.validateElement(field, ele).then(function (_) {\n\t                    _this.core.emit(\'plugins.trigger.executed\', {\n\t                        element: ele,\n\t                        event: e,\n\t                        field: field,\n\t                    });\n\t                });\n\t            };\n\t            var delay = this.opts.delay[field] || this.opts.delay;\n\t            if (delay === 0) {\n\t                handler();\n\t            }\n\t            else {\n\t                var timer = this.timers.get(ele);\n\t                if (timer) {\n\t                    window.clearTimeout(timer);\n\t                }\n\t                this.timers.set(ele, window.setTimeout(handler, delay * 1000));\n\t            }\n\t        }\n\t    };\n\t    Trigger.prototype.onFieldAdded = function (e) {\n\t        this.handlers\n\t            .filter(function (item) { return item.field === e.field; })\n\t            .forEach(function (item) { return item.element.removeEventListener(item.event, item.handler); });\n\t        this.prepareHandler(e.field, e.elements);\n\t    };\n\t    Trigger.prototype.onFieldRemoved = function (e) {\n\t        this.handlers\n\t            .filter(function (item) { return item.field === e.field && e.elements.indexOf(item.element) >= 0; })\n\t            .forEach(function (item) { return item.element.removeEventListener(item.event, item.handler); });\n\t    };\n\t    Trigger.prototype.exceedThreshold = function (field, element) {\n\t        var threshold = this.opts.threshold[field] === 0 || this.opts.threshold === 0\n\t            ? false\n\t            : this.opts.threshold[field] || this.opts.threshold;\n\t        if (!threshold) {\n\t            return true;\n\t        }\n\t        // List of input type which user can\'t type in\n\t        var type = element.getAttribute(\'type\');\n\t        if ([\'button\', \'checkbox\', \'file\', \'hidden\', \'image\', \'radio\', \'reset\', \'submit\'].indexOf(type) !== -1) {\n\t            return true;\n\t        }\n\t        var value = this.core.getElementValue(field, element);\n\t        return value.length >= threshold;\n\t    };\n\t    return Trigger;\n\t}(core.Plugin));\n\n\tcjs$n.Trigger = Trigger;\n\treturn cjs$n;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$n.exports = requireCjs$n();\n}\n\nvar libExports$n = lib$n.exports;\n\nvar lib$m = {exports: {}};\n\nvar index_min$m = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-between\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$m;\n\nfunction requireIndex_min$m () {\n\tif (hasRequiredIndex_min$m) return index_min$m;\n\thasRequiredIndex_min$m = 1;\nvar e=libExports$B,a=e.utils.format,n=e.utils.removeUndefined;index_min$m.between=function(){var e=function(e){return parseFloat("".concat(e).replace(",","."))};return {validate:function(t){var s=t.value;if(""===s)return {valid:!0};var r=Object.assign({},{inclusive:!0,message:""},n(t.options)),i=e(r.min),l=e(r.max);return r.inclusive?{message:a(t.l10n?r.message||t.l10n.between.default:r.message,["".concat(i),"".concat(l)]),valid:parseFloat(s)>=i&&parseFloat(s)<=l}:{message:a(t.l10n?r.message||t.l10n.between.notInclusive:r.message,["".concat(i),"".concat(l)]),valid:parseFloat(s)>i&&parseFloat(s)<l}}}};\n\treturn index_min$m;\n}\n\nvar cjs$m = {};\n\nvar hasRequiredCjs$m;\n\nfunction requireCjs$m () {\n\tif (hasRequiredCjs$m) return cjs$m;\n\thasRequiredCjs$m = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar format = core.utils.format, removeUndefined = core.utils.removeUndefined;\n\tfunction between() {\n\t    var formatValue = function (value) {\n\t        return parseFloat("".concat(value).replace(\',\', \'.\'));\n\t    };\n\t    return {\n\t        validate: function (input) {\n\t            var value = input.value;\n\t            if (value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, { inclusive: true, message: \'\' }, removeUndefined(input.options));\n\t            var minValue = formatValue(opts.min);\n\t            var maxValue = formatValue(opts.max);\n\t            return opts.inclusive\n\t                ? {\n\t                    message: format(input.l10n ? opts.message || input.l10n.between.default : opts.message, [\n\t                        "".concat(minValue),\n\t                        "".concat(maxValue),\n\t                    ]),\n\t                    valid: parseFloat(value) >= minValue && parseFloat(value) <= maxValue,\n\t                }\n\t                : {\n\t                    message: format(input.l10n ? opts.message || input.l10n.between.notInclusive : opts.message, [\n\t                        "".concat(minValue),\n\t                        "".concat(maxValue),\n\t                    ]),\n\t                    valid: parseFloat(value) > minValue && parseFloat(value) < maxValue,\n\t                };\n\t        },\n\t    };\n\t}\n\n\tcjs$m.between = between;\n\treturn cjs$m;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$m.exports = requireCjs$m();\n}\n\nvar libExports$m = lib$m.exports;\n\nvar lib$l = {exports: {}};\n\nvar index_min$l = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-blank\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$l;\n\nfunction requireIndex_min$l () {\n\tif (hasRequiredIndex_min$l) return index_min$l;\n\thasRequiredIndex_min$l = 1;\nindex_min$l.blank=function(){return {validate:function(t){return {valid:!0}}}};\n\treturn index_min$l;\n}\n\nvar cjs$l = {};\n\nvar hasRequiredCjs$l;\n\nfunction requireCjs$l () {\n\tif (hasRequiredCjs$l) return cjs$l;\n\thasRequiredCjs$l = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t/**\n\t * This validator always returns valid.\n\t * It can be used when we want to show the custom message returned from server\n\t */\n\tfunction blank() {\n\t    return {\n\t        validate: function (_input) {\n\t            return { valid: true };\n\t        },\n\t    };\n\t}\n\n\tcjs$l.blank = blank;\n\treturn cjs$l;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$l.exports = requireCjs$l();\n}\n\nvar libExports$l = lib$l.exports;\n\nvar lib$k = {exports: {}};\n\nvar index_min$k = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-callback\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$k;\n\nfunction requireIndex_min$k () {\n\tif (hasRequiredIndex_min$k) return index_min$k;\n\thasRequiredIndex_min$k = 1;\nvar a=libExports$B.utils.call;index_min$k.callback=function(){return {validate:function(r){var t=a(r.options.callback,[r]);return "boolean"==typeof t?{valid:t}:t}}};\n\treturn index_min$k;\n}\n\nvar cjs$k = {};\n\nvar hasRequiredCjs$k;\n\nfunction requireCjs$k () {\n\tif (hasRequiredCjs$k) return cjs$k;\n\thasRequiredCjs$k = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar call = core.utils.call;\n\tfunction callback() {\n\t    return {\n\t        validate: function (input) {\n\t            var response = call(input.options.callback, [input]);\n\t            return \'boolean\' === typeof response\n\t                ? { valid: response } // Deprecated\n\t                : response;\n\t        },\n\t    };\n\t}\n\n\tcjs$k.callback = callback;\n\treturn cjs$k;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$k.exports = requireCjs$k();\n}\n\nvar libExports$k = lib$k.exports;\n\nvar lib$j = {exports: {}};\n\nvar index_min$j = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-choice\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$j;\n\nfunction requireIndex_min$j () {\n\tif (hasRequiredIndex_min$j) return index_min$j;\n\thasRequiredIndex_min$j = 1;\nvar e=libExports$B.utils.format;index_min$j.choice=function(){return {validate:function(n){var t="select"===n.element.tagName.toLowerCase()?n.element.querySelectorAll("option:checked").length:n.elements.filter((function(e){return e.checked})).length,o=n.options.min?"".concat(n.options.min):"",s=n.options.max?"".concat(n.options.max):"",a=n.l10n?n.options.message||n.l10n.choice.default:n.options.message,c=!(o&&t<parseInt(o,10)||s&&t>parseInt(s,10));switch(!0){case!!o&&!!s:a=e(n.l10n?n.l10n.choice.between:n.options.message,[o,s]);break;case!!o:a=e(n.l10n?n.l10n.choice.more:n.options.message,o);break;case!!s:a=e(n.l10n?n.l10n.choice.less:n.options.message,s);}return {message:a,valid:c}}}};\n\treturn index_min$j;\n}\n\nvar cjs$j = {};\n\nvar hasRequiredCjs$j;\n\nfunction requireCjs$j () {\n\tif (hasRequiredCjs$j) return cjs$j;\n\thasRequiredCjs$j = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar format = core.utils.format;\n\tfunction choice() {\n\t    return {\n\t        validate: function (input) {\n\t            var numChoices = \'select\' === input.element.tagName.toLowerCase()\n\t                ? input.element.querySelectorAll(\'option:checked\').length\n\t                : input.elements.filter(function (ele) { return ele.checked; }).length;\n\t            var min = input.options.min ? "".concat(input.options.min) : \'\';\n\t            var max = input.options.max ? "".concat(input.options.max) : \'\';\n\t            var msg = input.l10n ? input.options.message || input.l10n.choice.default : input.options.message;\n\t            var isValid = !((min && numChoices < parseInt(min, 10)) || (max && numChoices > parseInt(max, 10)));\n\t            switch (true) {\n\t                case !!min && !!max:\n\t                    msg = format(input.l10n ? input.l10n.choice.between : input.options.message, [min, max]);\n\t                    break;\n\t                case !!min:\n\t                    msg = format(input.l10n ? input.l10n.choice.more : input.options.message, min);\n\t                    break;\n\t                case !!max:\n\t                    msg = format(input.l10n ? input.l10n.choice.less : input.options.message, max);\n\t                    break;\n\t            }\n\t            return {\n\t                message: msg,\n\t                valid: isValid,\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$j.choice = choice;\n\treturn cjs$j;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$j.exports = requireCjs$j();\n}\n\nvar libExports$j = lib$j.exports;\n\nvar lib$i = {exports: {}};\n\nvar index_min$i = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-credit-card\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$i;\n\nfunction requireIndex_min$i () {\n\tif (hasRequiredIndex_min$i) return index_min$i;\n\thasRequiredIndex_min$i = 1;\nvar e=libExports$B.algorithms.luhn,r={AMERICAN_EXPRESS:{length:[15],prefix:["34","37"]},DANKORT:{length:[16],prefix:["5019"]},DINERS_CLUB:{length:[14],prefix:["300","301","302","303","304","305","36"]},DINERS_CLUB_US:{length:[16],prefix:["54","55"]},DISCOVER:{length:[16],prefix:["6011","622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925","644","645","646","647","648","649","65"]},ELO:{length:[16],prefix:["4011","4312","4389","4514","4573","4576","5041","5066","5067","509","6277","6362","6363","650","6516","6550"]},FORBRUGSFORENINGEN:{length:[16],prefix:["600722"]},JCB:{length:[16],prefix:["3528","3529","353","354","355","356","357","358"]},LASER:{length:[16,17,18,19],prefix:["6304","6706","6771","6709"]},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:["5018","5020","5038","5868","6304","6759","6761","6762","6763","6764","6765","6766"]},MASTERCARD:{length:[16],prefix:["51","52","53","54","55"]},SOLO:{length:[16,18,19],prefix:["6334","6767"]},UNIONPAY:{length:[16,17,18,19],prefix:["622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925"]},VISA:{length:[16],prefix:["4"]},VISA_ELECTRON:{length:[16],prefix:["4026","417500","4405","4508","4844","4913","4917"]}};index_min$i.CREDIT_CARD_TYPES=r,index_min$i.creditCard=function(){return {validate:function(t){if(""===t.value)return {meta:{type:null},valid:!0};if(/[^0-9-\\s]+/.test(t.value))return {meta:{type:null},valid:!1};var l=t.value.replace(/\\D/g,"");if(!e(l))return {meta:{type:null},valid:!1};for(var i=0,n=Object.keys(r);i<n.length;i++){var f=n[i];for(var a in r[f].prefix)if(t.value.substr(0,r[f].prefix[a].length)===r[f].prefix[a]&&-1!==r[f].length.indexOf(l.length))return {meta:{type:f},valid:!0}}return {meta:{type:null},valid:!1}}}};\n\treturn index_min$i;\n}\n\nvar cjs$i = {};\n\nvar hasRequiredCjs$i;\n\nfunction requireCjs$i () {\n\tif (hasRequiredCjs$i) return cjs$i;\n\thasRequiredCjs$i = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar luhn = core.algorithms.luhn;\n\tvar CREDIT_CARD_TYPES = {\n\t    AMERICAN_EXPRESS: {\n\t        length: [15],\n\t        prefix: [\'34\', \'37\'],\n\t    },\n\t    DANKORT: {\n\t        length: [16],\n\t        prefix: [\'5019\'],\n\t    },\n\t    DINERS_CLUB: {\n\t        length: [14],\n\t        prefix: [\'300\', \'301\', \'302\', \'303\', \'304\', \'305\', \'36\'],\n\t    },\n\t    DINERS_CLUB_US: {\n\t        length: [16],\n\t        prefix: [\'54\', \'55\'],\n\t    },\n\t    DISCOVER: {\n\t        length: [16],\n\t        prefix: [\n\t            \'6011\',\n\t            \'622126\',\n\t            \'622127\',\n\t            \'622128\',\n\t            \'622129\',\n\t            \'62213\',\n\t            \'62214\',\n\t            \'62215\',\n\t            \'62216\',\n\t            \'62217\',\n\t            \'62218\',\n\t            \'62219\',\n\t            \'6222\',\n\t            \'6223\',\n\t            \'6224\',\n\t            \'6225\',\n\t            \'6226\',\n\t            \'6227\',\n\t            \'6228\',\n\t            \'62290\',\n\t            \'62291\',\n\t            \'622920\',\n\t            \'622921\',\n\t            \'622922\',\n\t            \'622923\',\n\t            \'622924\',\n\t            \'622925\',\n\t            \'644\',\n\t            \'645\',\n\t            \'646\',\n\t            \'647\',\n\t            \'648\',\n\t            \'649\',\n\t            \'65\',\n\t        ],\n\t    },\n\t    ELO: {\n\t        length: [16],\n\t        prefix: [\n\t            \'4011\',\n\t            \'4312\',\n\t            \'4389\',\n\t            \'4514\',\n\t            \'4573\',\n\t            \'4576\',\n\t            \'5041\',\n\t            \'5066\',\n\t            \'5067\',\n\t            \'509\',\n\t            \'6277\',\n\t            \'6362\',\n\t            \'6363\',\n\t            \'650\',\n\t            \'6516\',\n\t            \'6550\',\n\t        ],\n\t    },\n\t    FORBRUGSFORENINGEN: {\n\t        length: [16],\n\t        prefix: [\'600722\'],\n\t    },\n\t    JCB: {\n\t        length: [16],\n\t        prefix: [\'3528\', \'3529\', \'353\', \'354\', \'355\', \'356\', \'357\', \'358\'],\n\t    },\n\t    LASER: {\n\t        length: [16, 17, 18, 19],\n\t        prefix: [\'6304\', \'6706\', \'6771\', \'6709\'],\n\t    },\n\t    MAESTRO: {\n\t        length: [12, 13, 14, 15, 16, 17, 18, 19],\n\t        prefix: [\'5018\', \'5020\', \'5038\', \'5868\', \'6304\', \'6759\', \'6761\', \'6762\', \'6763\', \'6764\', \'6765\', \'6766\'],\n\t    },\n\t    MASTERCARD: {\n\t        length: [16],\n\t        prefix: [\'51\', \'52\', \'53\', \'54\', \'55\'],\n\t    },\n\t    SOLO: {\n\t        length: [16, 18, 19],\n\t        prefix: [\'6334\', \'6767\'],\n\t    },\n\t    UNIONPAY: {\n\t        length: [16, 17, 18, 19],\n\t        prefix: [\n\t            \'622126\',\n\t            \'622127\',\n\t            \'622128\',\n\t            \'622129\',\n\t            \'62213\',\n\t            \'62214\',\n\t            \'62215\',\n\t            \'62216\',\n\t            \'62217\',\n\t            \'62218\',\n\t            \'62219\',\n\t            \'6222\',\n\t            \'6223\',\n\t            \'6224\',\n\t            \'6225\',\n\t            \'6226\',\n\t            \'6227\',\n\t            \'6228\',\n\t            \'62290\',\n\t            \'62291\',\n\t            \'622920\',\n\t            \'622921\',\n\t            \'622922\',\n\t            \'622923\',\n\t            \'622924\',\n\t            \'622925\',\n\t        ],\n\t    },\n\t    VISA: {\n\t        length: [16],\n\t        prefix: [\'4\'],\n\t    },\n\t    VISA_ELECTRON: {\n\t        length: [16],\n\t        prefix: [\'4026\', \'417500\', \'4405\', \'4508\', \'4844\', \'4913\', \'4917\'],\n\t    },\n\t};\n\tfunction creditCard() {\n\t    return {\n\t        /**\n\t         * Return true if the input value is valid credit card number\n\t         */\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return {\n\t                    meta: {\n\t                        type: null,\n\t                    },\n\t                    valid: true,\n\t                };\n\t            }\n\t            // Accept only digits, dashes or spaces\n\t            if (/[^0-9-\\s]+/.test(input.value)) {\n\t                return {\n\t                    meta: {\n\t                        type: null,\n\t                    },\n\t                    valid: false,\n\t                };\n\t            }\n\t            var v = input.value.replace(/\\D/g, \'\');\n\t            if (!luhn(v)) {\n\t                return {\n\t                    meta: {\n\t                        type: null,\n\t                    },\n\t                    valid: false,\n\t                };\n\t            }\n\t            for (var _i = 0, _a = Object.keys(CREDIT_CARD_TYPES); _i < _a.length; _i++) {\n\t                var tpe = _a[_i];\n\t                for (var i in CREDIT_CARD_TYPES[tpe].prefix) {\n\t                    // Check the prefix and length\n\t                    if (input.value.substr(0, CREDIT_CARD_TYPES[tpe].prefix[i].length) ===\n\t                        CREDIT_CARD_TYPES[tpe].prefix[i] &&\n\t                        CREDIT_CARD_TYPES[tpe].length.indexOf(v.length) !== -1) {\n\t                        return {\n\t                            meta: {\n\t                                type: tpe,\n\t                            },\n\t                            valid: true,\n\t                        };\n\t                    }\n\t                }\n\t            }\n\t            return {\n\t                meta: {\n\t                    type: null,\n\t                },\n\t                valid: false,\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$i.CREDIT_CARD_TYPES = CREDIT_CARD_TYPES;\n\tcjs$i.creditCard = creditCard;\n\treturn cjs$i;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$i.exports = requireCjs$i();\n}\n\nvar libExports$i = lib$i.exports;\n\nvar lib$h = {exports: {}};\n\nvar index_min$h = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-date\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$h;\n\nfunction requireIndex_min$h () {\n\tif (hasRequiredIndex_min$h) return index_min$h;\n\thasRequiredIndex_min$h = 1;\nvar e=libExports$B,t=e.utils.format,n=e.utils.isValidDate,a=e.utils.removeUndefined,r=function(e,t,n){var a=t.indexOf("YYYY"),r=t.indexOf("MM"),l=t.indexOf("DD");if(-1===a||-1===r||-1===l)return null;var s=e.split(" "),i=s[0].split(n);if(i.length<3)return null;var c=new Date(parseInt(i[a],10),parseInt(i[r],10)-1,parseInt(i[l],10)),o=s.length>2?s[2]:null;if(s.length>1){var g=s[1].split(":"),u=g.length>0?parseInt(g[0],10):0;c.setHours(o&&"PM"===o.toUpperCase()&&u<12?u+12:u),c.setMinutes(g.length>1?parseInt(g[1],10):0),c.setSeconds(g.length>2?parseInt(g[2],10):0);}return c},l=function(e,t){var n=t.replace(/Y/g,"y").replace(/M/g,"m").replace(/D/g,"d").replace(/:m/g,":M").replace(/:mm/g,":MM").replace(/:S/,":s").replace(/:SS/,":ss"),a=e.getDate(),r=a<10?"0".concat(a):a,l=e.getMonth()+1,s=l<10?"0".concat(l):l,i="".concat(e.getFullYear()).substr(2),c=e.getFullYear(),o=e.getHours()%12||12,g=o<10?"0".concat(o):o,u=e.getHours(),d=u<10?"0".concat(u):u,f=e.getMinutes(),m=f<10?"0".concat(f):f,p=e.getSeconds(),h=p<10?"0".concat(p):p,v={H:"".concat(u),HH:"".concat(d),M:"".concat(f),MM:"".concat(m),d:"".concat(a),dd:"".concat(r),h:"".concat(o),hh:"".concat(g),m:"".concat(l),mm:"".concat(s),s:"".concat(p),ss:"".concat(h),yy:"".concat(i),yyyy:"".concat(c)};return n.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\\1?|"[^"]*"|\'[^\']*\'/g,(function(e){return v[e]?v[e]:e.slice(1,e.length-1)}))};index_min$h.date=function(){return {validate:function(e){if(""===e.value)return {meta:{date:null},valid:!0};var s=Object.assign({},{format:e.element&&"date"===e.element.getAttribute("type")?"YYYY-MM-DD":"MM/DD/YYYY",message:""},a(e.options)),i=e.l10n?e.l10n.date.default:s.message,c={message:"".concat(i),meta:{date:null},valid:!1},o=s.format.split(" "),g=o.length>1?o[1]:null,u=o.length>2?o[2]:null,d=e.value.split(" "),f=d[0],m=d.length>1?d[1]:null,p=d.length>2?d[2]:null;if(o.length!==d.length)return c;var h=s.separator||(-1!==f.indexOf("/")?"/":-1!==f.indexOf("-")?"-":-1!==f.indexOf(".")?".":"/");if(null===h||-1===f.indexOf(h))return c;var v=f.split(h),M=o[0].split(h);if(v.length!==M.length)return c;var Y=v[M.indexOf("YYYY")],D=v[M.indexOf("MM")],x=v[M.indexOf("DD")];if(!/^\\d+$/.test(Y)||!/^\\d+$/.test(D)||!/^\\d+$/.test(x)||Y.length>4||D.length>2||x.length>2)return c;var y=parseInt(Y,10),I=parseInt(D,10),O=parseInt(x,10);if(!n(y,I,O))return c;var H=new Date(y,I-1,O);if(g){var T=m.split(":");if(g.split(":").length!==T.length)return c;var S=T.length>0?T[0].length<=2&&/^\\d+$/.test(T[0])?parseInt(T[0],10):-1:0,$=T.length>1?T[1].length<=2&&/^\\d+$/.test(T[1])?parseInt(T[1],10):-1:0,b=T.length>2?T[2].length<=2&&/^\\d+$/.test(T[2])?parseInt(T[2],10):-1:0;if(-1===S||-1===$||-1===b)return c;if(b<0||b>60)return c;if(S<0||S>=24||u&&S>12)return c;if($<0||$>59)return c;H.setHours(p&&"PM"===p.toUpperCase()&&S<12?S+12:S),H.setMinutes($),H.setSeconds(b);}var w="function"==typeof s.min?s.min():s.min,U=w instanceof Date?w:w?r(w,M,h):H,C="function"==typeof s.max?s.max():s.max,F=C instanceof Date?C:C?r(C,M,h):H,P=w instanceof Date?l(U,s.format):w,j=C instanceof Date?l(F,s.format):C;switch(!0){case!!P&&!j:return {message:t(e.l10n?e.l10n.date.min:i,P),meta:{date:H},valid:H.getTime()>=U.getTime()};case!!j&&!P:return {message:t(e.l10n?e.l10n.date.max:i,j),meta:{date:H},valid:H.getTime()<=F.getTime()};case!!j&&!!P:return {message:t(e.l10n?e.l10n.date.range:i,[P,j]),meta:{date:H},valid:H.getTime()<=F.getTime()&&H.getTime()>=U.getTime()};default:return {message:"".concat(i),meta:{date:H},valid:!0}}}}};\n\treturn index_min$h;\n}\n\nvar cjs$h = {};\n\nvar hasRequiredCjs$h;\n\nfunction requireCjs$h () {\n\tif (hasRequiredCjs$h) return cjs$h;\n\thasRequiredCjs$h = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar format = core.utils.format, isValidDate = core.utils.isValidDate, removeUndefined = core.utils.removeUndefined;\n\t/**\n\t * Return a date object after parsing the date string\n\t *\n\t * @param {string} input The date to parse\n\t * @param {string[]} inputFormat The date format\n\t * The format can be:\n\t * - date: Consist of DD, MM, YYYY parts which are separated by the separator option\n\t * - date and time: The time can consist of h, m, s parts which are separated by :\n\t * @param {string} separator The separator used to separate the date, month, and year\n\t * @return {Date}\n\t * @private\n\t */\n\tvar parseDate = function (input, inputFormat, separator) {\n\t    // Ensure that the format must consist of year, month and day patterns\n\t    var yearIndex = inputFormat.indexOf(\'YYYY\');\n\t    var monthIndex = inputFormat.indexOf(\'MM\');\n\t    var dayIndex = inputFormat.indexOf(\'DD\');\n\t    if (yearIndex === -1 || monthIndex === -1 || dayIndex === -1) {\n\t        return null;\n\t    }\n\t    var sections = input.split(\' \');\n\t    var dateSection = sections[0].split(separator);\n\t    if (dateSection.length < 3) {\n\t        return null;\n\t    }\n\t    var d = new Date(parseInt(dateSection[yearIndex], 10), parseInt(dateSection[monthIndex], 10) - 1, parseInt(dateSection[dayIndex], 10));\n\t    var amPmSection = sections.length > 2 ? sections[2] : null;\n\t    if (sections.length > 1) {\n\t        var timeSection = sections[1].split(\':\');\n\t        var h = timeSection.length > 0 ? parseInt(timeSection[0], 10) : 0;\n\t        d.setHours(amPmSection && amPmSection.toUpperCase() === \'PM\' && h < 12 ? h + 12 : h);\n\t        d.setMinutes(timeSection.length > 1 ? parseInt(timeSection[1], 10) : 0);\n\t        d.setSeconds(timeSection.length > 2 ? parseInt(timeSection[2], 10) : 0);\n\t    }\n\t    return d;\n\t};\n\t/**\n\t * Format date\n\t *\n\t * @param {Date} input The date object to format\n\t * @param {string} inputFormat The date format\n\t * The format can consist of the following tokens:\n\t *      d       Day of the month without leading zeros (1 through 31)\n\t *      dd      Day of the month with leading zeros (01 through 31)\n\t *      m       Month without leading zeros (1 through 12)\n\t *      mm      Month with leading zeros (01 through 12)\n\t *      yy      Last two digits of year (for example: 14)\n\t *      yyyy    Full four digits of year (for example: 2014)\n\t *      h       Hours without leading zeros (1 through 12)\n\t *      hh      Hours with leading zeros (01 through 12)\n\t *      H       Hours without leading zeros (0 through 23)\n\t *      HH      Hours with leading zeros (00 through 23)\n\t *      M       Minutes without leading zeros (0 through 59)\n\t *      MM      Minutes with leading zeros (00 through 59)\n\t *      s       Seconds without leading zeros (0 through 59)\n\t *      ss      Seconds with leading zeros (00 through 59)\n\t * @return {string}\n\t * @private\n\t */\n\tvar formatDate = function (input, inputFormat) {\n\t    var dateFormat = inputFormat\n\t        .replace(/Y/g, \'y\')\n\t        .replace(/M/g, \'m\')\n\t        .replace(/D/g, \'d\')\n\t        .replace(/:m/g, \':M\')\n\t        .replace(/:mm/g, \':MM\')\n\t        .replace(/:S/, \':s\')\n\t        .replace(/:SS/, \':ss\');\n\t    var d = input.getDate();\n\t    var dd = d < 10 ? "0".concat(d) : d;\n\t    var m = input.getMonth() + 1;\n\t    var mm = m < 10 ? "0".concat(m) : m;\n\t    var yy = "".concat(input.getFullYear()).substr(2);\n\t    var yyyy = input.getFullYear();\n\t    var h = input.getHours() % 12 || 12;\n\t    var hh = h < 10 ? "0".concat(h) : h;\n\t    var H = input.getHours();\n\t    var HH = H < 10 ? "0".concat(H) : H;\n\t    var M = input.getMinutes();\n\t    var MM = M < 10 ? "0".concat(M) : M;\n\t    var s = input.getSeconds();\n\t    var ss = s < 10 ? "0".concat(s) : s;\n\t    var replacer = {\n\t        H: "".concat(H),\n\t        HH: "".concat(HH),\n\t        M: "".concat(M),\n\t        MM: "".concat(MM),\n\t        d: "".concat(d),\n\t        dd: "".concat(dd),\n\t        h: "".concat(h),\n\t        hh: "".concat(hh),\n\t        m: "".concat(m),\n\t        mm: "".concat(mm),\n\t        s: "".concat(s),\n\t        ss: "".concat(ss),\n\t        yy: "".concat(yy),\n\t        yyyy: "".concat(yyyy),\n\t    };\n\t    return dateFormat.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\\1?|"[^"]*"|\'[^\']*\'/g, function (match) {\n\t        return replacer[match] ? replacer[match] : match.slice(1, match.length - 1);\n\t    });\n\t};\n\tvar date = function () {\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return {\n\t                    meta: {\n\t                        date: null,\n\t                    },\n\t                    valid: true,\n\t                };\n\t            }\n\t            var opts = Object.assign({}, {\n\t                // Force the format to `YYYY-MM-DD` as the default browser behaviour when using type="date" attribute\n\t                format: input.element && input.element.getAttribute(\'type\') === \'date\' ? \'YYYY-MM-DD\' : \'MM/DD/YYYY\',\n\t                message: \'\',\n\t            }, removeUndefined(input.options));\n\t            var message = input.l10n ? input.l10n.date.default : opts.message;\n\t            var invalidResult = {\n\t                message: "".concat(message),\n\t                meta: {\n\t                    date: null,\n\t                },\n\t                valid: false,\n\t            };\n\t            var formats = opts.format.split(\' \');\n\t            var timeFormat = formats.length > 1 ? formats[1] : null;\n\t            var amOrPm = formats.length > 2 ? formats[2] : null;\n\t            var sections = input.value.split(\' \');\n\t            var dateSection = sections[0];\n\t            var timeSection = sections.length > 1 ? sections[1] : null;\n\t            var amPmSection = sections.length > 2 ? sections[2] : null;\n\t            if (formats.length !== sections.length) {\n\t                return invalidResult;\n\t            }\n\t            // Determine the separator\n\t            var separator = opts.separator ||\n\t                (dateSection.indexOf(\'/\') !== -1\n\t                    ? \'/\'\n\t                    : dateSection.indexOf(\'-\') !== -1\n\t                        ? \'-\'\n\t                        : dateSection.indexOf(\'.\') !== -1\n\t                            ? \'.\'\n\t                            : \'/\');\n\t            if (separator === null || dateSection.indexOf(separator) === -1) {\n\t                return invalidResult;\n\t            }\n\t            // Determine the date\n\t            var dateStr = dateSection.split(separator);\n\t            var dateFormat = formats[0].split(separator);\n\t            if (dateStr.length !== dateFormat.length) {\n\t                return invalidResult;\n\t            }\n\t            var yearStr = dateStr[dateFormat.indexOf(\'YYYY\')];\n\t            var monthStr = dateStr[dateFormat.indexOf(\'MM\')];\n\t            var dayStr = dateStr[dateFormat.indexOf(\'DD\')];\n\t            if (!/^\\d+$/.test(yearStr) ||\n\t                !/^\\d+$/.test(monthStr) ||\n\t                !/^\\d+$/.test(dayStr) ||\n\t                yearStr.length > 4 ||\n\t                monthStr.length > 2 ||\n\t                dayStr.length > 2) {\n\t                return invalidResult;\n\t            }\n\t            var year = parseInt(yearStr, 10);\n\t            var month = parseInt(monthStr, 10);\n\t            var day = parseInt(dayStr, 10);\n\t            if (!isValidDate(year, month, day)) {\n\t                return invalidResult;\n\t            }\n\t            // Determine the time\n\t            var d = new Date(year, month - 1, day);\n\t            if (timeFormat) {\n\t                var hms = timeSection.split(\':\');\n\t                if (timeFormat.split(\':\').length !== hms.length) {\n\t                    return invalidResult;\n\t                }\n\t                var h = hms.length > 0 ? (hms[0].length <= 2 && /^\\d+$/.test(hms[0]) ? parseInt(hms[0], 10) : -1) : 0;\n\t                var m = hms.length > 1 ? (hms[1].length <= 2 && /^\\d+$/.test(hms[1]) ? parseInt(hms[1], 10) : -1) : 0;\n\t                var s = hms.length > 2 ? (hms[2].length <= 2 && /^\\d+$/.test(hms[2]) ? parseInt(hms[2], 10) : -1) : 0;\n\t                if (h === -1 || m === -1 || s === -1) {\n\t                    return invalidResult;\n\t                }\n\t                // Validate seconds\n\t                if (s < 0 || s > 60) {\n\t                    return invalidResult;\n\t                }\n\t                // Validate hours\n\t                if (h < 0 || h >= 24 || (amOrPm && h > 12)) {\n\t                    return invalidResult;\n\t                }\n\t                // Validate minutes\n\t                if (m < 0 || m > 59) {\n\t                    return invalidResult;\n\t                }\n\t                d.setHours(amPmSection && amPmSection.toUpperCase() === \'PM\' && h < 12 ? h + 12 : h);\n\t                d.setMinutes(m);\n\t                d.setSeconds(s);\n\t            }\n\t            // Validate day, month, and year\n\t            var minOption = typeof opts.min === \'function\' ? opts.min() : opts.min;\n\t            var min = minOption instanceof Date\n\t                ? minOption\n\t                : minOption\n\t                    ? parseDate(minOption, dateFormat, separator)\n\t                    : d;\n\t            var maxOption = typeof opts.max === \'function\' ? opts.max() : opts.max;\n\t            var max = maxOption instanceof Date\n\t                ? maxOption\n\t                : maxOption\n\t                    ? parseDate(maxOption, dateFormat, separator)\n\t                    : d;\n\t            // In order to avoid displaying a date string like "Mon Dec 08 2014 19:14:12 GMT+0000 (WET)"\n\t            var minOptionStr = minOption instanceof Date ? formatDate(min, opts.format) : minOption;\n\t            var maxOptionStr = maxOption instanceof Date ? formatDate(max, opts.format) : maxOption;\n\t            switch (true) {\n\t                case !!minOptionStr && !maxOptionStr:\n\t                    return {\n\t                        message: format(input.l10n ? input.l10n.date.min : message, minOptionStr),\n\t                        meta: {\n\t                            date: d,\n\t                        },\n\t                        valid: d.getTime() >= min.getTime(),\n\t                    };\n\t                case !!maxOptionStr && !minOptionStr:\n\t                    return {\n\t                        message: format(input.l10n ? input.l10n.date.max : message, maxOptionStr),\n\t                        meta: {\n\t                            date: d,\n\t                        },\n\t                        valid: d.getTime() <= max.getTime(),\n\t                    };\n\t                case !!maxOptionStr && !!minOptionStr:\n\t                    return {\n\t                        message: format(input.l10n ? input.l10n.date.range : message, [minOptionStr, maxOptionStr]),\n\t                        meta: {\n\t                            date: d,\n\t                        },\n\t                        valid: d.getTime() <= max.getTime() && d.getTime() >= min.getTime(),\n\t                    };\n\t                default:\n\t                    return {\n\t                        message: "".concat(message),\n\t                        meta: {\n\t                            date: d,\n\t                        },\n\t                        valid: true,\n\t                    };\n\t            }\n\t        },\n\t    };\n\t};\n\n\tcjs$h.date = date;\n\treturn cjs$h;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$h.exports = requireCjs$h();\n}\n\nvar libExports$h = lib$h.exports;\n\nvar lib$g = {exports: {}};\n\nvar index_min$g = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-different\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$g;\n\nfunction requireIndex_min$g () {\n\tif (hasRequiredIndex_min$g) return index_min$g;\n\thasRequiredIndex_min$g = 1;\nindex_min$g.different=function(){return {validate:function(t){var o="function"==typeof t.options.compare?t.options.compare.call(this):t.options.compare;return {valid:""===o||t.value!==o}}}};\n\treturn index_min$g;\n}\n\nvar cjs$g = {};\n\nvar hasRequiredCjs$g;\n\nfunction requireCjs$g () {\n\tif (hasRequiredCjs$g) return cjs$g;\n\thasRequiredCjs$g = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction different() {\n\t    return {\n\t        validate: function (input) {\n\t            var compareWith = \'function\' === typeof input.options.compare\n\t                ? input.options.compare.call(this)\n\t                : input.options.compare;\n\t            return {\n\t                valid: compareWith === \'\' || input.value !== compareWith,\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$g.different = different;\n\treturn cjs$g;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$g.exports = requireCjs$g();\n}\n\nvar libExports$g = lib$g.exports;\n\nvar lib$f = {exports: {}};\n\nvar index_min$f = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-digits\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$f;\n\nfunction requireIndex_min$f () {\n\tif (hasRequiredIndex_min$f) return index_min$f;\n\thasRequiredIndex_min$f = 1;\nindex_min$f.digits=function(){return {validate:function(t){return {valid:""===t.value||/^\\d+$/.test(t.value)}}}};\n\treturn index_min$f;\n}\n\nvar cjs$f = {};\n\nvar hasRequiredCjs$f;\n\nfunction requireCjs$f () {\n\tif (hasRequiredCjs$f) return cjs$f;\n\thasRequiredCjs$f = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction digits() {\n\t    return {\n\t        /**\n\t         * Return true if the input value contains digits only\n\t         */\n\t        validate: function (input) {\n\t            return { valid: input.value === \'\' || /^\\d+$/.test(input.value) };\n\t        },\n\t    };\n\t}\n\n\tcjs$f.digits = digits;\n\treturn cjs$f;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$f.exports = requireCjs$f();\n}\n\nvar libExports$f = lib$f.exports;\n\nvar lib$e = {exports: {}};\n\nvar index_min$e = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-email-address\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$e;\n\nfunction requireIndex_min$e () {\n\tif (hasRequiredIndex_min$e) return index_min$e;\n\thasRequiredIndex_min$e = 1;\nvar e=libExports$B.utils.removeUndefined,a=/^(([^<>()[\\]\\\\.,;:\\s@"]+(\\.[^<>()[\\]\\\\.,;:\\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,r=/^(([^<>()[\\]\\\\.,;:\\s@"]+(\\.[^<>()[\\]\\\\.,;:\\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;index_min$e.emailAddress=function(){return {validate:function(t){if(""===t.value)return {valid:!0};var i=Object.assign({},{multiple:!1,requireGlobalDomain:!1,separator:/[,;]/},e(t.options)),l=i.requireGlobalDomain?r:a;if(!0===i.multiple||"true"==="".concat(i.multiple)){for(var s=i.separator||/[,;]/,u=function(e,a){for(var r=e.split(/"/),t=r.length,i=[],l="",s=0;s<t;s++)if(s%2==0){var u=r[s].split(a),n=u.length;if(1===n)l+=u[0];else {i.push(l+u[0]);for(var o=1;o<n-1;o++)i.push(u[o]);l=u[n-1];}}else l+=\'"\'+r[s],s<t-1&&(l+=\'"\');return i.push(l),i}(t.value,s),n=u.length,o=0;o<n;o++)if(!l.test(u[o]))return {valid:!1};return {valid:!0}}return {valid:l.test(t.value)}}}};\n\treturn index_min$e;\n}\n\nvar cjs$e = {};\n\nvar hasRequiredCjs$e;\n\nfunction requireCjs$e () {\n\tif (hasRequiredCjs$e) return cjs$e;\n\thasRequiredCjs$e = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\t// Email address regular expression\n\t// http://stackoverflow.com/questions/46155/validate-email-address-in-javascript\n\tvar GLOBAL_DOMAIN_OPTIONAL = /^(([^<>()[\\]\\\\.,;:\\s@"]+(\\.[^<>()[\\]\\\\.,;:\\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n\tvar GLOBAL_DOMAIN_REQUIRED = /^(([^<>()[\\]\\\\.,;:\\s@"]+(\\.[^<>()[\\]\\\\.,;:\\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;\n\tfunction emailAddress() {\n\t    var splitEmailAddresses = function (emailAddresses, separator) {\n\t        var quotedFragments = emailAddresses.split(/"/);\n\t        var quotedFragmentCount = quotedFragments.length;\n\t        var emailAddressArray = [];\n\t        var nextEmailAddress = \'\';\n\t        for (var i = 0; i < quotedFragmentCount; i++) {\n\t            if (i % 2 === 0) {\n\t                var splitEmailAddressFragments = quotedFragments[i].split(separator);\n\t                var splitEmailAddressFragmentCount = splitEmailAddressFragments.length;\n\t                if (splitEmailAddressFragmentCount === 1) {\n\t                    nextEmailAddress += splitEmailAddressFragments[0];\n\t                }\n\t                else {\n\t                    emailAddressArray.push(nextEmailAddress + splitEmailAddressFragments[0]);\n\t                    for (var j = 1; j < splitEmailAddressFragmentCount - 1; j++) {\n\t                        emailAddressArray.push(splitEmailAddressFragments[j]);\n\t                    }\n\t                    nextEmailAddress = splitEmailAddressFragments[splitEmailAddressFragmentCount - 1];\n\t                }\n\t            }\n\t            else {\n\t                nextEmailAddress += \'"\' + quotedFragments[i];\n\t                if (i < quotedFragmentCount - 1) {\n\t                    nextEmailAddress += \'"\';\n\t                }\n\t            }\n\t        }\n\t        emailAddressArray.push(nextEmailAddress);\n\t        return emailAddressArray;\n\t    };\n\t    return {\n\t        /**\n\t         * Return true if and only if the input value is a valid email address\n\t         */\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, {\n\t                multiple: false,\n\t                requireGlobalDomain: false,\n\t                separator: /[,;]/,\n\t            }, removeUndefined(input.options));\n\t            var emailRegExp = opts.requireGlobalDomain ? GLOBAL_DOMAIN_REQUIRED : GLOBAL_DOMAIN_OPTIONAL;\n\t            var allowMultiple = opts.multiple === true || "".concat(opts.multiple) === \'true\';\n\t            if (allowMultiple) {\n\t                var separator = opts.separator || /[,;]/;\n\t                var addresses = splitEmailAddresses(input.value, separator);\n\t                var length_1 = addresses.length;\n\t                for (var i = 0; i < length_1; i++) {\n\t                    if (!emailRegExp.test(addresses[i])) {\n\t                        return { valid: false };\n\t                    }\n\t                }\n\t                return { valid: true };\n\t            }\n\t            else {\n\t                return { valid: emailRegExp.test(input.value) };\n\t            }\n\t        },\n\t    };\n\t}\n\n\tcjs$e.emailAddress = emailAddress;\n\treturn cjs$e;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$e.exports = requireCjs$e();\n}\n\nvar libExports$e = lib$e.exports;\n\nvar lib$d = {exports: {}};\n\nvar index_min$d = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-file\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$d;\n\nfunction requireIndex_min$d () {\n\tif (hasRequiredIndex_min$d) return index_min$d;\n\thasRequiredIndex_min$d = 1;\nvar e=function(e){return -1===e.indexOf(".")?e:e.split(".").slice(0,-1).join(".")};index_min$d.file=function(){return {validate:function(t){if(""===t.value)return {valid:!0};var i,n,a=t.options.extension?t.options.extension.toLowerCase().split(",").map((function(e){return e.trim()})):[],r=t.options.type?t.options.type.toLowerCase().split(",").map((function(e){return e.trim()})):[];if(window.File&&window.FileList&&window.FileReader){var o=t.element.files,s=o.length,l=0;if(t.options.maxFiles&&s>parseInt("".concat(t.options.maxFiles),10))return {meta:{error:"INVALID_MAX_FILES"},valid:!1};if(t.options.minFiles&&s<parseInt("".concat(t.options.minFiles),10))return {meta:{error:"INVALID_MIN_FILES"},valid:!1};for(var I={},p=0;p<s;p++){if(l+=o[p].size,I={ext:i=o[p].name.substr(o[p].name.lastIndexOf(".")+1),file:o[p],size:o[p].size,type:o[p].type},t.options.minSize&&o[p].size<parseInt("".concat(t.options.minSize),10))return {meta:Object.assign({},{error:"INVALID_MIN_SIZE"},I),valid:!1};if(t.options.maxSize&&o[p].size>parseInt("".concat(t.options.maxSize),10))return {meta:Object.assign({},{error:"INVALID_MAX_SIZE"},I),valid:!1};if(a.length>0&&-1===a.indexOf(i.toLowerCase()))return {meta:Object.assign({},{error:"INVALID_EXTENSION"},I),valid:!1};if(r.length>0&&o[p].type&&-1===r.indexOf(o[p].type.toLowerCase()))return {meta:Object.assign({},{error:"INVALID_TYPE"},I),valid:!1};if(t.options.validateFileName&&!t.options.validateFileName(e(o[p].name)))return {meta:Object.assign({},{error:"INVALID_NAME"},I),valid:!1}}if(t.options.maxTotalSize&&l>parseInt("".concat(t.options.maxTotalSize),10))return {meta:Object.assign({},{error:"INVALID_MAX_TOTAL_SIZE",totalSize:l},I),valid:!1};if(t.options.minTotalSize&&l<parseInt("".concat(t.options.minTotalSize),10))return {meta:Object.assign({},{error:"INVALID_MIN_TOTAL_SIZE",totalSize:l},I),valid:!1}}else {if(i=t.value.substr(t.value.lastIndexOf(".")+1),a.length>0&&-1===a.indexOf(i.toLowerCase()))return {meta:{error:"INVALID_EXTENSION",ext:i},valid:!1};if(n=e(t.value),t.options.validateFileName&&!t.options.validateFileName(n))return {meta:{error:"INVALID_NAME",name:n},valid:!1}}return {valid:!0}}}};\n\treturn index_min$d;\n}\n\nvar cjs$d = {};\n\nvar hasRequiredCjs$d;\n\nfunction requireCjs$d () {\n\tif (hasRequiredCjs$d) return cjs$d;\n\thasRequiredCjs$d = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\t// Get the file name without extension\n\tvar getFileName = function (fileName) {\n\t    return fileName.indexOf(\'.\') === -1 ? fileName : fileName.split(\'.\').slice(0, -1).join(\'.\');\n\t};\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction file() {\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var extension;\n\t            var name;\n\t            var extensions = input.options.extension\n\t                ? input.options.extension\n\t                    .toLowerCase()\n\t                    .split(\',\')\n\t                    .map(function (item) { return item.trim(); })\n\t                : [];\n\t            var types = input.options.type\n\t                ? input.options.type\n\t                    .toLowerCase()\n\t                    .split(\',\')\n\t                    .map(function (item) { return item.trim(); })\n\t                : [];\n\t            var html5 = window[\'File\'] && window[\'FileList\'] && window[\'FileReader\'];\n\t            if (html5) {\n\t                // Get FileList instance\n\t                var files = input.element.files;\n\t                var total = files.length;\n\t                var allSize = 0;\n\t                // Check the maxFiles\n\t                if (input.options.maxFiles && total > parseInt("".concat(input.options.maxFiles), 10)) {\n\t                    return {\n\t                        meta: { error: \'INVALID_MAX_FILES\' },\n\t                        valid: false,\n\t                    };\n\t                }\n\t                // Check the minFiles\n\t                if (input.options.minFiles && total < parseInt("".concat(input.options.minFiles), 10)) {\n\t                    return {\n\t                        meta: { error: \'INVALID_MIN_FILES\' },\n\t                        valid: false,\n\t                    };\n\t                }\n\t                var metaData = {};\n\t                for (var i = 0; i < total; i++) {\n\t                    allSize += files[i].size;\n\t                    extension = files[i].name.substr(files[i].name.lastIndexOf(\'.\') + 1);\n\t                    metaData = {\n\t                        ext: extension,\n\t                        file: files[i],\n\t                        size: files[i].size,\n\t                        type: files[i].type,\n\t                    };\n\t                    // Check the minSize\n\t                    if (input.options.minSize && files[i].size < parseInt("".concat(input.options.minSize), 10)) {\n\t                        return {\n\t                            meta: Object.assign({}, { error: \'INVALID_MIN_SIZE\' }, metaData),\n\t                            valid: false,\n\t                        };\n\t                    }\n\t                    // Check the maxSize\n\t                    if (input.options.maxSize && files[i].size > parseInt("".concat(input.options.maxSize), 10)) {\n\t                        return {\n\t                            meta: Object.assign({}, { error: \'INVALID_MAX_SIZE\' }, metaData),\n\t                            valid: false,\n\t                        };\n\t                    }\n\t                    // Check file extension\n\t                    if (extensions.length > 0 && extensions.indexOf(extension.toLowerCase()) === -1) {\n\t                        return {\n\t                            meta: Object.assign({}, { error: \'INVALID_EXTENSION\' }, metaData),\n\t                            valid: false,\n\t                        };\n\t                    }\n\t                    // Check file type\n\t                    if (types.length > 0 && files[i].type && types.indexOf(files[i].type.toLowerCase()) === -1) {\n\t                        return {\n\t                            meta: Object.assign({}, { error: \'INVALID_TYPE\' }, metaData),\n\t                            valid: false,\n\t                        };\n\t                    }\n\t                    // Check file name\n\t                    if (input.options.validateFileName && !input.options.validateFileName(getFileName(files[i].name))) {\n\t                        return {\n\t                            meta: Object.assign({}, { error: \'INVALID_NAME\' }, metaData),\n\t                            valid: false,\n\t                        };\n\t                    }\n\t                }\n\t                // Check the maxTotalSize\n\t                if (input.options.maxTotalSize && allSize > parseInt("".concat(input.options.maxTotalSize), 10)) {\n\t                    return {\n\t                        meta: Object.assign({}, {\n\t                            error: \'INVALID_MAX_TOTAL_SIZE\',\n\t                            totalSize: allSize,\n\t                        }, metaData),\n\t                        valid: false,\n\t                    };\n\t                }\n\t                // Check the minTotalSize\n\t                if (input.options.minTotalSize && allSize < parseInt("".concat(input.options.minTotalSize), 10)) {\n\t                    return {\n\t                        meta: Object.assign({}, {\n\t                            error: \'INVALID_MIN_TOTAL_SIZE\',\n\t                            totalSize: allSize,\n\t                        }, metaData),\n\t                        valid: false,\n\t                    };\n\t                }\n\t            }\n\t            else {\n\t                // Check file extension\n\t                extension = input.value.substr(input.value.lastIndexOf(\'.\') + 1);\n\t                if (extensions.length > 0 && extensions.indexOf(extension.toLowerCase()) === -1) {\n\t                    return {\n\t                        meta: {\n\t                            error: \'INVALID_EXTENSION\',\n\t                            ext: extension,\n\t                        },\n\t                        valid: false,\n\t                    };\n\t                }\n\t                // Check file name\n\t                name = getFileName(input.value);\n\t                if (input.options.validateFileName && !input.options.validateFileName(name)) {\n\t                    return {\n\t                        meta: {\n\t                            error: \'INVALID_NAME\',\n\t                            name: name,\n\t                        },\n\t                        valid: false,\n\t                    };\n\t                }\n\t            }\n\t            return { valid: true };\n\t        },\n\t    };\n\t}\n\n\tcjs$d.file = file;\n\treturn cjs$d;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$d.exports = requireCjs$d();\n}\n\nvar libExports$d = lib$d.exports;\n\nvar lib$c = {exports: {}};\n\nvar index_min$c = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-greater-than\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$c;\n\nfunction requireIndex_min$c () {\n\tif (hasRequiredIndex_min$c) return index_min$c;\n\thasRequiredIndex_min$c = 1;\nvar e=libExports$B,a=e.utils.format,s=e.utils.removeUndefined;index_min$c.greaterThan=function(){return {validate:function(e){if(""===e.value)return {valid:!0};var n=Object.assign({},{inclusive:!0,message:""},s(e.options)),r=parseFloat("".concat(n.min).replace(",","."));return n.inclusive?{message:a(e.l10n?n.message||e.l10n.greaterThan.default:n.message,"".concat(r)),valid:parseFloat(e.value)>=r}:{message:a(e.l10n?n.message||e.l10n.greaterThan.notInclusive:n.message,"".concat(r)),valid:parseFloat(e.value)>r}}}};\n\treturn index_min$c;\n}\n\nvar cjs$c = {};\n\nvar hasRequiredCjs$c;\n\nfunction requireCjs$c () {\n\tif (hasRequiredCjs$c) return cjs$c;\n\thasRequiredCjs$c = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar format = core.utils.format, removeUndefined = core.utils.removeUndefined;\n\tfunction greaterThan() {\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, { inclusive: true, message: \'\' }, removeUndefined(input.options));\n\t            var minValue = parseFloat("".concat(opts.min).replace(\',\', \'.\'));\n\t            return opts.inclusive\n\t                ? {\n\t                    message: format(input.l10n ? opts.message || input.l10n.greaterThan.default : opts.message, "".concat(minValue)),\n\t                    valid: parseFloat(input.value) >= minValue,\n\t                }\n\t                : {\n\t                    message: format(input.l10n ? opts.message || input.l10n.greaterThan.notInclusive : opts.message, "".concat(minValue)),\n\t                    valid: parseFloat(input.value) > minValue,\n\t                };\n\t        },\n\t    };\n\t}\n\n\tcjs$c.greaterThan = greaterThan;\n\treturn cjs$c;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$c.exports = requireCjs$c();\n}\n\nvar libExports$c = lib$c.exports;\n\nvar lib$b = {exports: {}};\n\nvar index_min$b = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-identical\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$b;\n\nfunction requireIndex_min$b () {\n\tif (hasRequiredIndex_min$b) return index_min$b;\n\thasRequiredIndex_min$b = 1;\nindex_min$b.identical=function(){return {validate:function(t){var o="function"==typeof t.options.compare?t.options.compare.call(this):t.options.compare;return {valid:""===o||t.value===o}}}};\n\treturn index_min$b;\n}\n\nvar cjs$b = {};\n\nvar hasRequiredCjs$b;\n\nfunction requireCjs$b () {\n\tif (hasRequiredCjs$b) return cjs$b;\n\thasRequiredCjs$b = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction identical() {\n\t    return {\n\t        validate: function (input) {\n\t            var compareWith = \'function\' === typeof input.options.compare\n\t                ? input.options.compare.call(this)\n\t                : input.options.compare;\n\t            return {\n\t                valid: compareWith === \'\' || input.value === compareWith,\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$b.identical = identical;\n\treturn cjs$b;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$b.exports = requireCjs$b();\n}\n\nvar libExports$b = lib$b.exports;\n\nvar lib$a = {exports: {}};\n\nvar index_min$a = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-integer\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$a;\n\nfunction requireIndex_min$a () {\n\tif (hasRequiredIndex_min$a) return index_min$a;\n\thasRequiredIndex_min$a = 1;\nvar a=libExports$B.utils.removeUndefined;index_min$a.integer=function(){return {validate:function(e){if(""===e.value)return {valid:!0};var r=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},a(e.options)),t="."===r.decimalSeparator?"\\\\.":r.decimalSeparator,i="."===r.thousandsSeparator?"\\\\.":r.thousandsSeparator,o=new RegExp("^-?[0-9]{1,3}(".concat(i,"[0-9]{3})*(").concat(t,"[0-9]+)?$")),n=new RegExp(i,"g"),s="".concat(e.value);if(!o.test(s))return {valid:!1};i&&(s=s.replace(n,"")),t&&(s=s.replace(t,"."));var c=parseFloat(s);return {valid:!isNaN(c)&&isFinite(c)&&Math.floor(c)===c}}}};\n\treturn index_min$a;\n}\n\nvar cjs$a = {};\n\nvar hasRequiredCjs$a;\n\nfunction requireCjs$a () {\n\tif (hasRequiredCjs$a) return cjs$a;\n\thasRequiredCjs$a = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\tfunction integer() {\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, {\n\t                decimalSeparator: \'.\',\n\t                thousandsSeparator: \'\',\n\t            }, removeUndefined(input.options));\n\t            var decimalSeparator = opts.decimalSeparator === \'.\' ? \'\\\\.\' : opts.decimalSeparator;\n\t            var thousandsSeparator = opts.thousandsSeparator === \'.\' ? \'\\\\.\' : opts.thousandsSeparator;\n\t            var testRegexp = new RegExp("^-?[0-9]{1,3}(".concat(thousandsSeparator, "[0-9]{3})*(").concat(decimalSeparator, "[0-9]+)?$"));\n\t            var thousandsReplacer = new RegExp(thousandsSeparator, \'g\');\n\t            var v = "".concat(input.value);\n\t            if (!testRegexp.test(v)) {\n\t                return { valid: false };\n\t            }\n\t            // Replace thousands separator with blank\n\t            if (thousandsSeparator) {\n\t                v = v.replace(thousandsReplacer, \'\');\n\t            }\n\t            // Replace decimal separator with a dot\n\t            if (decimalSeparator) {\n\t                v = v.replace(decimalSeparator, \'.\');\n\t            }\n\t            var n = parseFloat(v);\n\t            return { valid: !isNaN(n) && isFinite(n) && Math.floor(n) === n };\n\t        },\n\t    };\n\t}\n\n\tcjs$a.integer = integer;\n\treturn cjs$a;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$a.exports = requireCjs$a();\n}\n\nvar libExports$a = lib$a.exports;\n\nvar lib$9 = {exports: {}};\n\nvar index_min$9 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-ip\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$9;\n\nfunction requireIndex_min$9 () {\n\tif (hasRequiredIndex_min$9) return index_min$9;\n\thasRequiredIndex_min$9 = 1;\nvar d=libExports$B.utils.removeUndefined;index_min$9.ip=function(){return {validate:function(a){if(""===a.value)return {valid:!0};var e=Object.assign({},{ipv4:!0,ipv6:!0},d(a.options)),s=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\/([0-9]|[1-2][0-9]|3[0-2]))?$/,i=/^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*(\\/(\\d|\\d\\d|1[0-1]\\d|12[0-8]))?$/;switch(!0){case e.ipv4&&!e.ipv6:return {message:a.l10n?e.message||a.l10n.ip.ipv4:e.message,valid:s.test(a.value)};case!e.ipv4&&e.ipv6:return {message:a.l10n?e.message||a.l10n.ip.ipv6:e.message,valid:i.test(a.value)};case e.ipv4&&e.ipv6:default:return {message:a.l10n?e.message||a.l10n.ip.default:e.message,valid:s.test(a.value)||i.test(a.value)}}}}};\n\treturn index_min$9;\n}\n\nvar cjs$9 = {};\n\nvar hasRequiredCjs$9;\n\nfunction requireCjs$9 () {\n\tif (hasRequiredCjs$9) return cjs$9;\n\thasRequiredCjs$9 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\tfunction ip() {\n\t    return {\n\t        /**\n\t         * Return true if the input value is a IP address.\n\t         */\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, {\n\t                ipv4: true,\n\t                ipv6: true,\n\t            }, removeUndefined(input.options));\n\t            var ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\/([0-9]|[1-2][0-9]|3[0-2]))?$/;\n\t            var ipv6Regex = /^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*(\\/(\\d|\\d\\d|1[0-1]\\d|12[0-8]))?$/;\n\t            switch (true) {\n\t                case opts.ipv4 && !opts.ipv6:\n\t                    return {\n\t                        message: input.l10n ? opts.message || input.l10n.ip.ipv4 : opts.message,\n\t                        valid: ipv4Regex.test(input.value),\n\t                    };\n\t                case !opts.ipv4 && opts.ipv6:\n\t                    return {\n\t                        message: input.l10n ? opts.message || input.l10n.ip.ipv6 : opts.message,\n\t                        valid: ipv6Regex.test(input.value),\n\t                    };\n\t                case opts.ipv4 && opts.ipv6:\n\t                default:\n\t                    return {\n\t                        message: input.l10n ? opts.message || input.l10n.ip.default : opts.message,\n\t                        valid: ipv4Regex.test(input.value) || ipv6Regex.test(input.value),\n\t                    };\n\t            }\n\t        },\n\t    };\n\t}\n\n\tcjs$9.ip = ip;\n\treturn cjs$9;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$9.exports = requireCjs$9();\n}\n\nvar libExports$9 = lib$9.exports;\n\nvar lib$8 = {exports: {}};\n\nvar index_min$8 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-less-than\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$8;\n\nfunction requireIndex_min$8 () {\n\tif (hasRequiredIndex_min$8) return index_min$8;\n\thasRequiredIndex_min$8 = 1;\nvar e=libExports$B,a=e.utils.format,s=e.utils.removeUndefined;index_min$8.lessThan=function(){return {validate:function(e){if(""===e.value)return {valid:!0};var n=Object.assign({},{inclusive:!0,message:""},s(e.options)),l=parseFloat("".concat(n.max).replace(",","."));return n.inclusive?{message:a(e.l10n?n.message||e.l10n.lessThan.default:n.message,"".concat(l)),valid:parseFloat(e.value)<=l}:{message:a(e.l10n?n.message||e.l10n.lessThan.notInclusive:n.message,"".concat(l)),valid:parseFloat(e.value)<l}}}};\n\treturn index_min$8;\n}\n\nvar cjs$8 = {};\n\nvar hasRequiredCjs$8;\n\nfunction requireCjs$8 () {\n\tif (hasRequiredCjs$8) return cjs$8;\n\thasRequiredCjs$8 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar format = core.utils.format, removeUndefined = core.utils.removeUndefined;\n\tfunction lessThan() {\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, { inclusive: true, message: \'\' }, removeUndefined(input.options));\n\t            var maxValue = parseFloat("".concat(opts.max).replace(\',\', \'.\'));\n\t            return opts.inclusive\n\t                ? {\n\t                    message: format(input.l10n ? opts.message || input.l10n.lessThan.default : opts.message, "".concat(maxValue)),\n\t                    valid: parseFloat(input.value) <= maxValue,\n\t                }\n\t                : {\n\t                    message: format(input.l10n ? opts.message || input.l10n.lessThan.notInclusive : opts.message, "".concat(maxValue)),\n\t                    valid: parseFloat(input.value) < maxValue,\n\t                };\n\t        },\n\t    };\n\t}\n\n\tcjs$8.lessThan = lessThan;\n\treturn cjs$8;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$8.exports = requireCjs$8();\n}\n\nvar libExports$8 = lib$8.exports;\n\nvar lib$7 = {exports: {}};\n\nvar index_min$7 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-not-empty\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$7;\n\nfunction requireIndex_min$7 () {\n\tif (hasRequiredIndex_min$7) return index_min$7;\n\thasRequiredIndex_min$7 = 1;\nindex_min$7.notEmpty=function(){return {validate:function(t){var i=!!t.options&&!!t.options.trim,n=t.value;return {valid:!i&&""!==n||i&&""!==n&&""!==n.trim()}}}};\n\treturn index_min$7;\n}\n\nvar cjs$7 = {};\n\nvar hasRequiredCjs$7;\n\nfunction requireCjs$7 () {\n\tif (hasRequiredCjs$7) return cjs$7;\n\thasRequiredCjs$7 = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction notEmpty() {\n\t    return {\n\t        validate: function (input) {\n\t            var trim = !!input.options && !!input.options.trim;\n\t            var value = input.value;\n\t            return {\n\t                valid: (!trim && value !== \'\') || (trim && value !== \'\' && value.trim() !== \'\'),\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$7.notEmpty = notEmpty;\n\treturn cjs$7;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$7.exports = requireCjs$7();\n}\n\nvar libExports$7 = lib$7.exports;\n\nvar lib$6 = {exports: {}};\n\nvar index_min$6 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-numeric\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$6;\n\nfunction requireIndex_min$6 () {\n\tif (hasRequiredIndex_min$6) return index_min$6;\n\thasRequiredIndex_min$6 = 1;\nvar a=libExports$B.utils.removeUndefined;index_min$6.numeric=function(){return {validate:function(r){if(""===r.value)return {valid:!0};var e=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},a(r.options)),t="".concat(r.value);t.substr(0,1)===e.decimalSeparator?t="0".concat(e.decimalSeparator).concat(t.substr(1)):t.substr(0,2)==="-".concat(e.decimalSeparator)&&(t="-0".concat(e.decimalSeparator).concat(t.substr(2)));var c="."===e.decimalSeparator?"\\\\.":e.decimalSeparator,o="."===e.thousandsSeparator?"\\\\.":e.thousandsSeparator,i=new RegExp("^-?[0-9]{1,3}(".concat(o,"[0-9]{3})*(").concat(c,"[0-9]+)?$")),n=new RegExp(o,"g");if(!i.test(t))return {valid:!1};o&&(t=t.replace(n,"")),c&&(t=t.replace(c,"."));var s=parseFloat(t);return {valid:!isNaN(s)&&isFinite(s)}}}};\n\treturn index_min$6;\n}\n\nvar cjs$6 = {};\n\nvar hasRequiredCjs$6;\n\nfunction requireCjs$6 () {\n\tif (hasRequiredCjs$6) return cjs$6;\n\thasRequiredCjs$6 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\tfunction numeric() {\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, {\n\t                decimalSeparator: \'.\',\n\t                thousandsSeparator: \'\',\n\t            }, removeUndefined(input.options));\n\t            var v = "".concat(input.value);\n\t            // Support preceding zero numbers such as .5, -.5\n\t            if (v.substr(0, 1) === opts.decimalSeparator) {\n\t                v = "0".concat(opts.decimalSeparator).concat(v.substr(1));\n\t            }\n\t            else if (v.substr(0, 2) === "-".concat(opts.decimalSeparator)) {\n\t                v = "-0".concat(opts.decimalSeparator).concat(v.substr(2));\n\t            }\n\t            var decimalSeparator = opts.decimalSeparator === \'.\' ? \'\\\\.\' : opts.decimalSeparator;\n\t            var thousandsSeparator = opts.thousandsSeparator === \'.\' ? \'\\\\.\' : opts.thousandsSeparator;\n\t            var testRegexp = new RegExp("^-?[0-9]{1,3}(".concat(thousandsSeparator, "[0-9]{3})*(").concat(decimalSeparator, "[0-9]+)?$"));\n\t            var thousandsReplacer = new RegExp(thousandsSeparator, \'g\');\n\t            if (!testRegexp.test(v)) {\n\t                return { valid: false };\n\t            }\n\t            // Replace thousands separator with blank\n\t            if (thousandsSeparator) {\n\t                v = v.replace(thousandsReplacer, \'\');\n\t            }\n\t            // Replace decimal separator with a dot\n\t            if (decimalSeparator) {\n\t                v = v.replace(decimalSeparator, \'.\');\n\t            }\n\t            var n = parseFloat(v);\n\t            return { valid: !isNaN(n) && isFinite(n) };\n\t        },\n\t    };\n\t}\n\n\tcjs$6.numeric = numeric;\n\treturn cjs$6;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$6.exports = requireCjs$6();\n}\n\nvar libExports$6 = lib$6.exports;\n\nvar lib$5 = {exports: {}};\n\nvar index_min$5 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-promise\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$5;\n\nfunction requireIndex_min$5 () {\n\tif (hasRequiredIndex_min$5) return index_min$5;\n\thasRequiredIndex_min$5 = 1;\nvar r=libExports$B.utils.call;index_min$5.promise=function(){return {validate:function(i){return r(i.options.promise,[i])}}};\n\treturn index_min$5;\n}\n\nvar cjs$5 = {};\n\nvar hasRequiredCjs$5;\n\nfunction requireCjs$5 () {\n\tif (hasRequiredCjs$5) return cjs$5;\n\thasRequiredCjs$5 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar call = core.utils.call;\n\tfunction promise() {\n\t    return {\n\t        /**\n\t         * The following example demonstrates how to use a promise validator to requires both width and height\n\t         * of an image to be less than 300 px\n\t         *  ```\n\t         *  const p = new Promise((resolve, reject) => {\n\t         *      const img = new Image()\n\t         *      img.addEventListener(\'load\', function() {\n\t         *          const w = this.width\n\t         *          const h = this.height\n\t         *          resolve({\n\t         *              valid: w <= 300 && h <= 300\n\t         *              meta: {\n\t         *                  source: img.src // So, you can reuse it later if you want\n\t         *              }\n\t         *          })\n\t         *      })\n\t         *      img.addEventListener(\'error\', function() {\n\t         *          reject({\n\t         *              valid: false,\n\t         *              message: Please choose an image\n\t         *          })\n\t         *      })\n\t         *  })\n\t         *  ```\n\t         *\n\t         * @param input\n\t         * @return {Promise<ValidateResult>}\n\t         */\n\t        validate: function (input) {\n\t            return call(input.options.promise, [input]);\n\t        },\n\t    };\n\t}\n\n\tcjs$5.promise = promise;\n\treturn cjs$5;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$5.exports = requireCjs$5();\n}\n\nvar libExports$5 = lib$5.exports;\n\nvar lib$4 = {exports: {}};\n\nvar index_min$4 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-regexp\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$4;\n\nfunction requireIndex_min$4 () {\n\tif (hasRequiredIndex_min$4) return index_min$4;\n\thasRequiredIndex_min$4 = 1;\nindex_min$4.regexp=function(){return {validate:function(e){if(""===e.value)return {valid:!0};var t=e.options.regexp;if(t instanceof RegExp)return {valid:t.test(e.value)};var n=t.toString();return {valid:(e.options.flags?new RegExp(n,e.options.flags):new RegExp(n)).test(e.value)}}}};\n\treturn index_min$4;\n}\n\nvar cjs$4 = {};\n\nvar hasRequiredCjs$4;\n\nfunction requireCjs$4 () {\n\tif (hasRequiredCjs$4) return cjs$4;\n\thasRequiredCjs$4 = 1;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tfunction regexp() {\n\t    return {\n\t        /**\n\t         * Check if the element value matches given regular expression\n\t         */\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var reg = input.options.regexp;\n\t            if (reg instanceof RegExp) {\n\t                return { valid: reg.test(input.value) };\n\t            }\n\t            else {\n\t                var pattern = reg.toString();\n\t                var exp = input.options.flags ? new RegExp(pattern, input.options.flags) : new RegExp(pattern);\n\t                return { valid: exp.test(input.value) };\n\t            }\n\t        },\n\t    };\n\t}\n\n\tcjs$4.regexp = regexp;\n\treturn cjs$4;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$4.exports = requireCjs$4();\n}\n\nvar libExports$4 = lib$4.exports;\n\nvar lib$3 = {exports: {}};\n\nvar index_min$3 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-remote\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$3;\n\nfunction requireIndex_min$3 () {\n\tif (hasRequiredIndex_min$3) return index_min$3;\n\thasRequiredIndex_min$3 = 1;\nvar e=libExports$B,a=e.utils.fetch,r=e.utils.removeUndefined;index_min$3.remote=function(){var e={crossDomain:!1,data:{},headers:{},method:"GET",validKey:"valid"};return {validate:function(t){if(""===t.value)return Promise.resolve({valid:!0});var i=Object.assign({},e,r(t.options)),o=i.data;"function"==typeof i.data&&(o=i.data.call(this,t)),"string"==typeof o&&(o=JSON.parse(o)),o[i.name||t.field]=t.value;var s="function"==typeof i.url?i.url.call(this,t):i.url;return a(s,{crossDomain:i.crossDomain,headers:i.headers,method:i.method,params:o}).then((function(e){return Promise.resolve({message:e.message,meta:e,valid:"true"==="".concat(e[i.validKey])})})).catch((function(e){return Promise.reject({valid:!1})}))}}};\n\treturn index_min$3;\n}\n\nvar cjs$3 = {};\n\nvar hasRequiredCjs$3;\n\nfunction requireCjs$3 () {\n\tif (hasRequiredCjs$3) return cjs$3;\n\thasRequiredCjs$3 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar fetch = core.utils.fetch, removeUndefined = core.utils.removeUndefined;\n\tfunction remote() {\n\t    var DEFAULT_OPTIONS = {\n\t        crossDomain: false,\n\t        data: {},\n\t        headers: {},\n\t        method: \'GET\',\n\t        validKey: \'valid\',\n\t    };\n\t    return {\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return Promise.resolve({\n\t                    valid: true,\n\t                });\n\t            }\n\t            var opts = Object.assign({}, DEFAULT_OPTIONS, removeUndefined(input.options));\n\t            var data = opts.data;\n\t            // Support dynamic data\n\t            if (\'function\' === typeof opts.data) {\n\t                data = opts.data.call(this, input);\n\t            }\n\t            // Parse string data from HTML5 attribute\n\t            if (\'string\' === typeof data) {\n\t                data = JSON.parse(data);\n\t            }\n\t            data[opts.name || input.field] = input.value;\n\t            // Support dynamic url\n\t            var url = \'function\' === typeof opts.url\n\t                ? opts.url.call(this, input)\n\t                : opts.url;\n\t            return fetch(url, {\n\t                crossDomain: opts.crossDomain,\n\t                headers: opts.headers,\n\t                method: opts.method,\n\t                params: data,\n\t            })\n\t                .then(function (response) {\n\t                return Promise.resolve({\n\t                    message: response[\'message\'],\n\t                    meta: response,\n\t                    valid: "".concat(response[opts.validKey]) === \'true\',\n\t                });\n\t            })\n\t                .catch(function (_reason) {\n\t                return Promise.reject({\n\t                    valid: false,\n\t                });\n\t            });\n\t        },\n\t    };\n\t}\n\n\tcjs$3.remote = remote;\n\treturn cjs$3;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$3.exports = requireCjs$3();\n}\n\nvar libExports$3 = lib$3.exports;\n\nvar lib$2 = {exports: {}};\n\nvar index_min$2 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-string-case\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$2;\n\nfunction requireIndex_min$2 () {\n\tif (hasRequiredIndex_min$2) return index_min$2;\n\thasRequiredIndex_min$2 = 1;\nvar e=libExports$B.utils.removeUndefined;index_min$2.stringCase=function(){return {validate:function(a){if(""===a.value)return {valid:!0};var r=Object.assign({},{case:"lower"},e(a.options)),s=(r.case||"lower").toLowerCase();return {message:r.message||(a.l10n?"upper"===s?a.l10n.stringCase.upper:a.l10n.stringCase.default:r.message),valid:"upper"===s?a.value===a.value.toUpperCase():a.value===a.value.toLowerCase()}}}};\n\treturn index_min$2;\n}\n\nvar cjs$2 = {};\n\nvar hasRequiredCjs$2;\n\nfunction requireCjs$2 () {\n\tif (hasRequiredCjs$2) return cjs$2;\n\thasRequiredCjs$2 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\tfunction stringCase() {\n\t    return {\n\t        /**\n\t         * Check if a string is a lower or upper case one\n\t         */\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, { case: \'lower\' }, removeUndefined(input.options));\n\t            var caseOpt = (opts.case || \'lower\').toLowerCase();\n\t            return {\n\t                message: opts.message ||\n\t                    (input.l10n\n\t                        ? \'upper\' === caseOpt\n\t                            ? input.l10n.stringCase.upper\n\t                            : input.l10n.stringCase.default\n\t                        : opts.message),\n\t                valid: \'upper\' === caseOpt\n\t                    ? input.value === input.value.toUpperCase()\n\t                    : input.value === input.value.toLowerCase(),\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$2.stringCase = stringCase;\n\treturn cjs$2;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$2.exports = requireCjs$2();\n}\n\nvar libExports$2 = lib$2.exports;\n\nvar lib$1 = {exports: {}};\n\nvar index_min$1 = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-string-length\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min$1;\n\nfunction requireIndex_min$1 () {\n\tif (hasRequiredIndex_min$1) return index_min$1;\n\thasRequiredIndex_min$1 = 1;\nvar e=libExports$B,t=e.utils.format,n=e.utils.removeUndefined;index_min$1.stringLength=function(){return {validate:function(e){var s=Object.assign({},{message:"",trim:!1,utf8Bytes:!1},n(e.options)),a=!0===s.trim||"true"==="".concat(s.trim)?e.value.trim():e.value;if(""===a)return {valid:!0};var r=s.min?"".concat(s.min):"",i=s.max?"".concat(s.max):"",g=s.utf8Bytes?function(e){for(var t=e.length,n=e.length-1;n>=0;n--){var s=e.charCodeAt(n);s>127&&s<=2047?t++:s>2047&&s<=65535&&(t+=2),s>=56320&&s<=57343&&n--;}return t}(a):a.length,m=!0,c=e.l10n?s.message||e.l10n.stringLength.default:s.message;switch((r&&g<parseInt(r,10)||i&&g>parseInt(i,10))&&(m=!1),!0){case!!r&&!!i:c=t(e.l10n?s.message||e.l10n.stringLength.between:s.message,[r,i]);break;case!!r:c=t(e.l10n?s.message||e.l10n.stringLength.more:s.message,"".concat(parseInt(r,10)));break;case!!i:c=t(e.l10n?s.message||e.l10n.stringLength.less:s.message,"".concat(parseInt(i,10)));}return {message:c,valid:m}}}};\n\treturn index_min$1;\n}\n\nvar cjs$1 = {};\n\nvar hasRequiredCjs$1;\n\nfunction requireCjs$1 () {\n\tif (hasRequiredCjs$1) return cjs$1;\n\thasRequiredCjs$1 = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar format = core.utils.format, removeUndefined = core.utils.removeUndefined;\n\t// Credit to http://stackoverflow.com/a/23329386 (@lovasoa) for UTF-8 byte length code\n\tvar utf8Length = function (str) {\n\t    var s = str.length;\n\t    for (var i = str.length - 1; i >= 0; i--) {\n\t        var code = str.charCodeAt(i);\n\t        if (code > 0x7f && code <= 0x7ff) {\n\t            s++;\n\t        }\n\t        else if (code > 0x7ff && code <= 0xffff) {\n\t            s += 2;\n\t        }\n\t        if (code >= 0xdc00 && code <= 0xdfff) {\n\t            i--;\n\t        }\n\t    }\n\t    return s;\n\t};\n\tfunction stringLength() {\n\t    return {\n\t        /**\n\t         * Check if the length of element value is less or more than given number\n\t         */\n\t        validate: function (input) {\n\t            var opts = Object.assign({}, {\n\t                message: \'\',\n\t                trim: false,\n\t                utf8Bytes: false,\n\t            }, removeUndefined(input.options));\n\t            var v = opts.trim === true || "".concat(opts.trim) === \'true\' ? input.value.trim() : input.value;\n\t            if (v === \'\') {\n\t                return { valid: true };\n\t            }\n\t            // TODO: `min`, `max` can be dynamic options\n\t            var min = opts.min ? "".concat(opts.min) : \'\';\n\t            var max = opts.max ? "".concat(opts.max) : \'\';\n\t            var length = opts.utf8Bytes ? utf8Length(v) : v.length;\n\t            var isValid = true;\n\t            var msg = input.l10n ? opts.message || input.l10n.stringLength.default : opts.message;\n\t            if ((min && length < parseInt(min, 10)) || (max && length > parseInt(max, 10))) {\n\t                isValid = false;\n\t            }\n\t            switch (true) {\n\t                case !!min && !!max:\n\t                    msg = format(input.l10n ? opts.message || input.l10n.stringLength.between : opts.message, [\n\t                        min,\n\t                        max,\n\t                    ]);\n\t                    break;\n\t                case !!min:\n\t                    msg = format(input.l10n ? opts.message || input.l10n.stringLength.more : opts.message, "".concat(parseInt(min, 10)));\n\t                    break;\n\t                case !!max:\n\t                    msg = format(input.l10n ? opts.message || input.l10n.stringLength.less : opts.message, "".concat(parseInt(max, 10)));\n\t                    break;\n\t            }\n\t            return {\n\t                message: msg,\n\t                valid: isValid,\n\t            };\n\t        },\n\t    };\n\t}\n\n\tcjs$1.stringLength = stringLength;\n\treturn cjs$1;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib$1.exports = requireCjs$1();\n}\n\nvar libExports$1 = lib$1.exports;\n\nvar lib = {exports: {}};\n\nvar index_min = {};\n\n/** \n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n *\n * @license https://formvalidation.io/license\n * @package @form-validation/validator-uri\n * @version 2.4.0\n */\n\nvar hasRequiredIndex_min;\n\nfunction requireIndex_min () {\n\tif (hasRequiredIndex_min) return index_min;\n\thasRequiredIndex_min = 1;\nvar o=libExports$B.utils.removeUndefined;index_min.uri=function(){var a={allowEmptyProtocol:!1,allowLocal:!1,protocol:"http, https, ftp"};return {validate:function(t){if(""===t.value)return {valid:!0};var l=Object.assign({},a,o(t.options)),f=!0===l.allowLocal||"true"==="".concat(l.allowLocal),r=!0===l.allowEmptyProtocol||"true"==="".concat(l.allowEmptyProtocol),e=l.protocol.split(",").join("|").replace(/\\s/g,"");return {valid:new RegExp("^(?:(?:"+e+")://)"+(r?"?":"")+"(?:\\\\S+(?::\\\\S*)?@)?(?:"+(f?"":"(?!(?:10|127)(?:\\\\.\\\\d{1,3}){3})(?!(?:169\\\\.254|192\\\\.168)(?:\\\\.\\\\d{1,3}){2})(?!172\\\\.(?:1[6-9]|2\\\\d|3[0-1])(?:\\\\.\\\\d{1,3}){2})")+"(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[1-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-?)*[a-z\\\\u00a1-\\\\uffff0-9])*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))"+(f?"?":"")+")(?::\\\\d{2,5})?(?:/[^\\\\s]*)?$","i").test(t.value)}}}};\n\treturn index_min;\n}\n\nvar cjs = {};\n\nvar hasRequiredCjs;\n\nfunction requireCjs () {\n\tif (hasRequiredCjs) return cjs;\n\thasRequiredCjs = 1;\n\n\tvar core = libExports$B;\n\n\t/**\n\t * FormValidation (https://formvalidation.io)\n\t * The best validation library for JavaScript\n\t * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n\t */\n\tvar removeUndefined = core.utils.removeUndefined;\n\tfunction uri() {\n\t    var DEFAULT_OPTIONS = {\n\t        allowEmptyProtocol: false,\n\t        allowLocal: false,\n\t        protocol: \'http, https, ftp\',\n\t    };\n\t    return {\n\t        /**\n\t         * Return true if the input value is a valid URL\n\t         */\n\t        validate: function (input) {\n\t            if (input.value === \'\') {\n\t                return { valid: true };\n\t            }\n\t            var opts = Object.assign({}, DEFAULT_OPTIONS, removeUndefined(input.options));\n\t            // Credit to https://gist.github.com/dperini/729294\n\t            //\n\t            // Regular Expression for URL validation\n\t            //\n\t            // Author: Diego Perini\n\t            // Updated: 2010/12/05\n\t            //\n\t            // the regular expression composed & commented\n\t            // could be easily tweaked for RFC compliance,\n\t            // it was expressly modified to fit & satisfy\n\t            // these test for an URL shortener:\n\t            //\n\t            //   http://mathiasbynens.be/demo/url-regex\n\t            //\n\t            // Notes on possible differences from a standard/generic validation:\n\t            //\n\t            // - utf-8 char class take in consideration the full Unicode range\n\t            // - TLDs are mandatory unless `allowLocal` is true\n\t            // - protocols have been restricted to ftp, http and https only as requested\n\t            //\n\t            // Changes:\n\t            //\n\t            // - IP address dotted notation validation, range: ******* - ***************\n\t            //   first and last IP address of each class is considered invalid\n\t            //   (since they are broadcast/network addresses)\n\t            //\n\t            // - Added exclusion of private, reserved and/or local networks ranges\n\t            //   unless `allowLocal` is true\n\t            //\n\t            // - Added possibility of choosing a custom protocol\n\t            //\n\t            // - Add option to validate without protocol\n\t            //\n\t            var allowLocal = opts.allowLocal === true || "".concat(opts.allowLocal) === \'true\';\n\t            var allowEmptyProtocol = opts.allowEmptyProtocol === true || "".concat(opts.allowEmptyProtocol) === \'true\';\n\t            var protocol = opts.protocol.split(\',\').join(\'|\').replace(/\\s/g, \'\');\n\t            var urlExp = new RegExp(\'^\' +\n\t                // protocol identifier\n\t                \'(?:(?:\' +\n\t                protocol +\n\t                \')://)\' +\n\t                // allow empty protocol\n\t                (allowEmptyProtocol ? \'?\' : \'\') +\n\t                // user:pass authentication\n\t                \'(?:\\\\S+(?::\\\\S*)?@)?\' +\n\t                \'(?:\' +\n\t                // IP address exclusion\n\t                // private & local networks\n\t                (allowLocal\n\t                    ? \'\'\n\t                    : \'(?!(?:10|127)(?:\\\\.\\\\d{1,3}){3})\' +\n\t                        \'(?!(?:169\\\\.254|192\\\\.168)(?:\\\\.\\\\d{1,3}){2})\' +\n\t                        \'(?!172\\\\.(?:1[6-9]|2\\\\d|3[0-1])(?:\\\\.\\\\d{1,3}){2})\') +\n\t                // IP address dotted notation octets\n\t                // excludes loopback network 0.0.0.0\n\t                // excludes reserved space >= *********\n\t                // excludes network & broadcast addresses\n\t                // (first & last IP address of each class)\n\t                \'(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])\' +\n\t                \'(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}\' +\n\t                \'(?:\\\\.(?:[1-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))\' +\n\t                \'|\' +\n\t                // host name\n\t                \'(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)\' +\n\t                // domain name\n\t                \'(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-?)*[a-z\\\\u00a1-\\\\uffff0-9])*\' +\n\t                // TLD identifier\n\t                \'(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\' +\n\t                // Allow intranet sites (no TLD) if `allowLocal` is true\n\t                (allowLocal ? \'?\' : \'\') +\n\t                \')\' +\n\t                // port number\n\t                \'(?::\\\\d{2,5})?\' +\n\t                // resource path\n\t                \'(?:/[^\\\\s]*)?$\', \'i\');\n\t            return { valid: urlExp.test(input.value) };\n\t        },\n\t    };\n\t}\n\n\tcjs.uri = uri;\n\treturn cjs;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n\nif (false) {} else {\n    lib.exports = requireCjs();\n}\n\nvar libExports = lib.exports;\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar plugins = {\n    Alias: libExports$A.Alias,\n    Aria: libExports$z.Aria,\n    Declarative: libExports$y.Declarative,\n    DefaultSubmit: libExports$x.DefaultSubmit,\n    Dependency: libExports$w.Dependency,\n    Excluded: libExports$v.Excluded,\n    FieldStatus: libExports$u.FieldStatus,\n    Framework: libExports$s.Framework,\n    Icon: libExports$r.Icon,\n    Message: libExports$t.Message,\n    Sequence: libExports$q.Sequence,\n    SubmitButton: libExports$p.SubmitButton,\n    Tooltip: libExports$o.Tooltip,\n    Trigger: libExports$n.Trigger,\n};\nvar validators = {\n    between: libExports$m.between,\n    blank: libExports$l.blank,\n    callback: libExports$k.callback,\n    choice: libExports$j.choice,\n    creditCard: libExports$i.creditCard,\n    date: libExports$h.date,\n    different: libExports$g.different,\n    digits: libExports$f.digits,\n    emailAddress: libExports$e.emailAddress,\n    file: libExports$d.file,\n    greaterThan: libExports$c.greaterThan,\n    identical: libExports$b.identical,\n    integer: libExports$a.integer,\n    ip: libExports$9.ip,\n    lessThan: libExports$8.lessThan,\n    notEmpty: libExports$7.notEmpty,\n    numeric: libExports$6.numeric,\n    promise: libExports$5.promise,\n    regexp: libExports$4.regexp,\n    remote: libExports$3.remote,\n    stringCase: libExports$2.stringCase,\n    stringLength: libExports$1.stringLength,\n    uri: libExports.uri,\n};\n// Register popular validators\nvar formValidationWithPopularValidators = function (form, options) {\n    var instance = libExports$B.formValidation(form, options);\n    Object.keys(validators).forEach(function (name) { return instance.registerValidator(name, validators[name]); });\n    return instance;\n};\n\nexports.Plugin = libExports$B.Plugin;\nexports.algorithms = libExports$B.algorithms;\nexports.formValidation = formValidationWithPopularValidators;\nexports.plugins = plugins;\nexports.utils = libExports$B.utils;\nexports.validators = validators;\n\n\n//# <AUTHOR> <EMAIL>\n */\n\n\n\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/popular.js */ "./node_modules/@form-validation/bundle/lib/cjs/popular.js");\n}\n\n\n//# sourceURL=webpack://Materialize/./node_modules/@form-validation/bundle/lib/popular.js?')},"./libs/@form-validation/popular.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormValidation: function() { return /* reexport default export from named module */ _form_validation_bundle_popular__WEBPACK_IMPORTED_MODULE_0__; }\n/* harmony export */ });\n/* harmony import */ var _form_validation_bundle_popular__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @form-validation/bundle/popular */ "./node_modules/@form-validation/bundle/lib/popular.js");\n\ntry {\n  window.FormValidation = _form_validation_bundle_popular__WEBPACK_IMPORTED_MODULE_0__;\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/@form-validation/popular.js?')}},__webpack_module_cache__={};function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var n=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](n,n.exports,__webpack_require__),n.exports}__webpack_require__.d=function(t,e){for(var n in e)__webpack_require__.o(e,n)&&!__webpack_require__.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},__webpack_require__.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},__webpack_require__.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/@form-validation/popular.js");return __webpack_exports__}()}));