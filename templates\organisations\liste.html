{% extends 'base.html' %} {% load static %} {% block title %}Organisations{% endblock %} {% block breadcrumb %}
<div class="row">
  <div class="col-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Accueil</a>
        </li>
        <li class="breadcrumb-item active">Organisations</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <!-- En-tête de page -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h4 class="fw-bold py-3 mb-2">
          <i class="ti ti-building me-2"></i>Gestion des Organisations
        </h4>
        <p class="text-muted">
          G<PERSON>rez les organisations hospitalières et leurs abonnements
        </p>
      </div>
      <div>
        <a href="{% url 'organisations:creer' %}" class="btn btn-primary">
          <i class="ti ti-plus me-1"></i>Nouvelle Organisation
        </a>
      </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
      <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-start justify-content-between">
              <div class="content-left">
                <span class="text-heading">Total</span>
                <div class="d-flex align-items-center my-1">
                  <h4 class="mb-0 me-2">{{ total_organisations }}</h4>
                </div>
                <small class="mb-0">Organisations</small>
              </div>
              <div class="avatar">
                <span class="avatar-initial rounded bg-label-primary">
                  <i class="ti ti-building ti-26px"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-start justify-content-between">
              <div class="content-left">
                <span class="text-heading">Premium</span>
                <div class="d-flex align-items-center my-1">
                  <h4 class="mb-0 me-2">
                    {{ page_obj.object_list|length|add:"-1" }}
                  </h4>
                </div>
                <small class="mb-0">Abonnements</small>
              </div>
              <div class="avatar">
                <span class="avatar-initial rounded bg-label-success">
                  <i class="ti ti-crown ti-26px"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Formulaire de recherche -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="ti ti-search me-2"></i>Recherche et Filtres
        </h5>
      </div>
      <div class="card-body">
        <form method="get" class="row g-3">
          <div class="col-md-4">
            <label
              for="{{ form_recherche.nom.id_for_label }}"
              class="form-label"
            >
              {{ form_recherche.nom.label }}
            </label>
            {{ form_recherche.nom }}
          </div>
          <div class="col-md-3">
            <label
              for="{{ form_recherche.type_abonnement.id_for_label }}"
              class="form-label"
            >
              {{ form_recherche.type_abonnement.label }}
            </label>
            {{ form_recherche.type_abonnement }}
          </div>
          <div class="col-md-3">
            <label
              for="{{ form_recherche.actif.id_for_label }}"
              class="form-label"
            >
              {{ form_recherche.actif.label }}
            </label>
            {{ form_recherche.actif }}
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
              <button type="submit" class="btn btn-primary">
                <i class="ti ti-search me-1"></i>Rechercher
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Liste des organisations -->
    <div class="card">
      <div
        class="card-header d-flex justify-content-between align-items-center"
      >
        <h5 class="card-title mb-0">
          Liste des Organisations ({{ page_obj.paginator.count }} résultat{{
          page_obj.paginator.count|pluralize }})
        </h5>
      </div>

      {% if page_obj %}
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>Organisation</th>
              <th>Contact</th>
              <th>Abonnement</th>
              <th>Utilisateurs</th>
              <th>Statut</th>
              <th>Date création</th>
              <th class="text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for organisation in page_obj %}
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="avatar avatar-sm me-3">
                    <span class="avatar-initial rounded bg-label-primary">
                      {{ organisation.nom|first|upper }}
                    </span>
                  </div>
                  <div>
                    <h6 class="mb-0">{{ organisation.nom }}</h6>
                    <small class="text-muted"
                      >{{ organisation.adresse|truncatechars:50 }}</small
                    >
                  </div>
                </div>
              </td>
              <td>
                <div>
                  <small class="text-muted d-block"
                    >{{ organisation.email }}</small
                  >
                  <small class="text-muted"
                    >{{ organisation.get_telephone_formate }}</small
                  >
                  {% if organisation.get_pays_telephone %}
                  <br /><small class="text-muted"
                    >{{ organisation.get_pays_telephone }}</small
                  >
                  {% endif %}
                </div>
              </td>
              <td>
                {% if organisation.type_abonnement == 'PREMIUM' %}
                <span class="badge bg-label-success">
                  <i class="ti ti-crown ti-xs me-1"></i>Premium
                </span>
                {% else %}
                <span class="badge bg-label-secondary">
                  <i class="ti ti-gift ti-xs me-1"></i>Gratuit
                </span>
                {% endif %}
              </td>
              <td>
                <span class="badge bg-label-info">
                  {{ organisation.get_nombre_utilisateurs }} utilisateur{{  organisation.get_nombre_utilisateurs|pluralize }}
                </span>
              </td>
              <td>
                {% if organisation.actif %}
                <span class="badge bg-label-success">
                  <i class="ti ti-check ti-xs me-1"></i>Active
                </span>
                {% else %}
                <span class="badge bg-label-danger">
                  <i class="ti ti-x ti-xs me-1"></i>Inactive
                </span>
                {% endif %}
              </td>
              <td>
                <small class="text-muted"
                  >{{ organisation.date_creation|date:"d/m/Y" }}</small
                >
              </td>
              <td class="text-center">
                <div class="dropdown">
                  <button
                    type="button"
                    class="btn p-0 dropdown-toggle hide-arrow"
                    data-bs-toggle="dropdown"
                  >
                    <i class="ti ti-dots-vertical"></i>
                  </button>
                  <div class="dropdown-menu">
                    <a
                      class="dropdown-item"
                      href="{% url 'organisations:detail' organisation.pk %}"
                    >
                      <i class="ti ti-eye me-1"></i>Voir
                    </a>
                    <a
                      class="dropdown-item"
                      href="{% url 'organisations:modifier' organisation.pk %}"
                    >
                      <i class="ti ti-pencil me-1"></i>Modifier
                    </a>
                    <div class="dropdown-divider"></div>
                    <a
                      class="dropdown-item text-danger"
                      href="{% url 'organisations:supprimer' organisation.pk %}"
                    >
                      <i class="ti ti-trash me-1"></i>Supprimer
                    </a>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      {% if page_obj.has_other_pages %}
      <div class="card-footer">
        <nav aria-label="Pagination des organisations">
          <ul class="pagination justify-content-center mb-0">
            {% if page_obj.has_previous %}
            <li class="page-item">
              <a
                class="page-link"
                href="?page=1{% if request.GET.nom %}&nom={{ request.GET.nom }}{% endif %}{% if request.GET.type_abonnement %}&type_abonnement={{ request.GET.type_abonnement }}{% endif %}{% if request.GET.actif %}&actif={{ request.GET.actif }}{% endif %}"
              >
                <i class="ti ti-chevrons-left ti-xs"></i>
              </a>
            </li>
            <li class="page-item">
              <a
                class="page-link"
                href="?page={{ page_obj.previous_page_number }}{% if request.GET.nom %}&nom={{ request.GET.nom }}{% endif %}{% if request.GET.type_abonnement %}&type_abonnement={{ request.GET.type_abonnement }}{% endif %}{% if request.GET.actif %}&actif={{ request.GET.actif }}{% endif %}"
              >
                <i class="ti ti-chevron-left ti-xs"></i>
              </a>
            </li>
            {% endif %}

            <li class="page-item active">
              <span class="page-link">
                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages
                }}
              </span>
            </li>

            {% if page_obj.has_next %}
            <li class="page-item">
              <a
                class="page-link"
                href="?page={{ page_obj.next_page_number }}{% if request.GET.nom %}&nom={{ request.GET.nom }}{% endif %}{% if request.GET.type_abonnement %}&type_abonnement={{ request.GET.type_abonnement }}{% endif %}{% if request.GET.actif %}&actif={{ request.GET.actif }}{% endif %}"
              >
                <i class="ti ti-chevron-right ti-xs"></i>
              </a>
            </li>
            <li class="page-item">
              <a
                class="page-link"
                href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.nom %}&nom={{ request.GET.nom }}{% endif %}{% if request.GET.type_abonnement %}&type_abonnement={{ request.GET.type_abonnement }}{% endif %}{% if request.GET.actif %}&actif={{ request.GET.actif }}{% endif %}"
              >
                <i class="ti ti-chevrons-right ti-xs"></i>
              </a>
            </li>
            {% endif %}
          </ul>
        </nav>
      </div>
      {% endif %} {% else %}
      <div class="card-body text-center py-5">
        <div class="mb-4">
          <i class="ti ti-building ti-48px text-muted"></i>
        </div>
        <h5 class="mb-2">Aucune organisation trouvée</h5>
        <p class="text-muted mb-4">
          Commencez par créer votre première organisation hospitalière.
        </p>
        <a href="{% url 'organisations:creer' %}" class="btn btn-primary">
          <i class="ti ti-plus me-1"></i>Créer une organisation
        </a>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
