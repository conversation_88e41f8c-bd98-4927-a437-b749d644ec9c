!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t=e();for(var a in t)("object"==typeof exports?exports:n)[a]=t[a]}}(self,(function(){return function(){var __webpack_modules__={"./libs/jquery-repeater/jquery-repeater.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var jquery_repeater_jquery_repeater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery.repeater/jquery.repeater */ "./node_modules/jquery.repeater/jquery.repeater.js");\n/* harmony import */ var jquery_repeater_jquery_repeater__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery_repeater_jquery_repeater__WEBPACK_IMPORTED_MODULE_0__);\n\n\n//# <AUTHOR> <EMAIL> (http://www.briandetering.net/)\n(function ($) {\n'use strict';\n\nvar identity = function (x) {\n    return x;\n};\n\nvar isArray = function (value) {\n    return $.isArray(value);\n};\n\nvar isObject = function (value) {\n    return !isArray(value) && (value instanceof Object);\n};\n\nvar isNumber = function (value) {\n    return value instanceof Number;\n};\n\nvar isFunction = function (value) {\n    return value instanceof Function;\n};\n\nvar indexOf = function (object, value) {\n    return $.inArray(value, object);\n};\n\nvar inArray = function (array, value) {\n    return indexOf(array, value) !== -1;\n};\n\nvar foreach = function (collection, callback) {\n    for(var i in collection) {\n        if(collection.hasOwnProperty(i)) {\n            callback(collection[i], i, collection);\n        }\n    }\n};\n\n\nvar last = function (array) {\n    return array[array.length - 1];\n};\n\nvar argumentsToArray = function (args) {\n    return Array.prototype.slice.call(args);\n};\n\nvar extend = function () {\n    var extended = {};\n    foreach(argumentsToArray(arguments), function (o) {\n        foreach(o, function (val, key) {\n            extended[key] = val;\n        });\n    });\n    return extended;\n};\n\nvar mapToArray = function (collection, callback) {\n    var mapped = [];\n    foreach(collection, function (value, key, coll) {\n        mapped.push(callback(value, key, coll));\n    });\n    return mapped;\n};\n\nvar mapToObject = function (collection, callback, keyCallback) {\n    var mapped = {};\n    foreach(collection, function (value, key, coll) {\n        key = keyCallback ? keyCallback(key, value) : key;\n        mapped[key] = callback(value, key, coll);\n    });\n    return mapped;\n};\n\nvar map = function (collection, callback, keyCallback) {\n    return isArray(collection) ?\n        mapToArray(collection, callback) :\n        mapToObject(collection, callback, keyCallback);\n};\n\nvar pluck = function (arrayOfObjects, key) {\n    return map(arrayOfObjects, function (val) {\n        return val[key];\n    });\n};\n\nvar filter = function (collection, callback) {\n    var filtered;\n\n    if(isArray(collection)) {\n        filtered = [];\n        foreach(collection, function (val, key, coll) {\n            if(callback(val, key, coll)) {\n                filtered.push(val);\n            }\n        });\n    }\n    else {\n        filtered = {};\n        foreach(collection, function (val, key, coll) {\n            if(callback(val, key, coll)) {\n                filtered[key] = val;\n            }\n        });\n    }\n\n    return filtered;\n};\n\nvar call = function (collection, functionName, args) {\n    return map(collection, function (object, name) {\n        return object[functionName].apply(object, args || []);\n    });\n};\n\n//execute callback immediately and at most one time on the minimumInterval,\n//ignore block attempts\nvar throttle = function (minimumInterval, callback) {\n    var timeout = null;\n    return function () {\n        var that = this, args = arguments;\n        if(timeout === null) {\n            timeout = setTimeout(function () {\n                timeout = null;\n            }, minimumInterval);\n            callback.apply(that, args);\n        }\n    };\n};\n\n\nvar mixinPubSub = function (object) {\n    object = object || {};\n    var topics = {};\n\n    object.publish = function (topic, data) {\n        foreach(topics[topic], function (callback) {\n            callback(data);\n        });\n    };\n\n    object.subscribe = function (topic, callback) {\n        topics[topic] = topics[topic] || [];\n        topics[topic].push(callback);\n    };\n\n    object.unsubscribe = function (callback) {\n        foreach(topics, function (subscribers) {\n            var index = indexOf(subscribers, callback);\n            if(index !== -1) {\n                subscribers.splice(index, 1);\n            }\n        });\n    };\n\n    return object;\n};\n\n// jquery.input version 0.0.0\n// https://github.com/DubFriend/jquery.input\n// (MIT) 09-04-2014\n// Brian Detering <<EMAIL>> (http://www.briandetering.net/)\n(function ($) {\n'use strict';\n\nvar createBaseInput = function (fig, my) {\n    var self = mixinPubSub(),\n        $self = fig.$;\n\n    self.getType = function () {\n        throw 'implement me (return type. \"text\", \"radio\", etc.)';\n    };\n\n    self.$ = function (selector) {\n        return selector ? $self.find(selector) : $self;\n    };\n\n    self.disable = function () {\n        self.$().prop('disabled', true);\n        self.publish('isEnabled', false);\n    };\n\n    self.enable = function () {\n        self.$().prop('disabled', false);\n        self.publish('isEnabled', true);\n    };\n\n    my.equalTo = function (a, b) {\n        return a === b;\n    };\n\n    my.publishChange = (function () {\n        var oldValue;\n        return function (e, domElement) {\n            var newValue = self.get();\n            if(!my.equalTo(newValue, oldValue)) {\n                self.publish('change', { e: e, domElement: domElement });\n            }\n            oldValue = newValue;\n        };\n    }());\n\n    return self;\n};\n\n\nvar createInput = function (fig, my) {\n    var self = createBaseInput(fig, my);\n\n    self.get = function () {\n        return self.$().val();\n    };\n\n    self.set = function (newValue) {\n        self.$().val(newValue);\n    };\n\n    self.clear = function () {\n        self.set('');\n    };\n\n    my.buildSetter = function (callback) {\n        return function (newValue) {\n            callback.call(self, newValue);\n        };\n    };\n\n    return self;\n};\n\nvar inputEqualToArray = function (a, b) {\n    a = isArray(a) ? a : [a];\n    b = isArray(b) ? b : [b];\n\n    var isEqual = true;\n    if(a.length !== b.length) {\n        isEqual = false;\n    }\n    else {\n        foreach(a, function (value) {\n            if(!inArray(b, value)) {\n                isEqual = false;\n            }\n        });\n    }\n\n    return isEqual;\n};\n\nvar createInputButton = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'button';\n    };\n\n    self.$().on('change', function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputCheckbox = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'checkbox';\n    };\n\n    self.get = function () {\n        var values = [];\n        self.$().filter(':checked').each(function () {\n            values.push($(this).val());\n        });\n        return values;\n    };\n\n    self.set = function (newValues) {\n        newValues = isArray(newValues) ? newValues : [newValues];\n\n        self.$().each(function () {\n            $(this).prop('checked', false);\n        });\n\n        foreach(newValues, function (value) {\n            self.$().filter('[value=\"' + value + '\"]')\n                .prop('checked', true);\n        });\n    };\n\n    my.equalTo = inputEqualToArray;\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputEmail = function (fig) {\n    var my = {},\n        self = createInputText(fig, my);\n\n    self.getType = function () {\n        return 'email';\n    };\n\n    return self;\n};\n\nvar createInputFile = function (fig) {\n    var my = {},\n        self = createBaseInput(fig, my);\n\n    self.getType = function () {\n        return 'file';\n    };\n\n    self.get = function () {\n        return last(self.$().val().split('\\\\'));\n    };\n\n    self.clear = function () {\n        // http://stackoverflow.com/questions/1043957/clearing-input-type-file-using-jquery\n        this.$().each(function () {\n            $(this).wrap('<form>').closest('form').get(0).reset();\n            $(this).unwrap();\n        });\n    };\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n        // self.publish('change', self);\n    });\n\n    return self;\n};\n\nvar createInputHidden = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'hidden';\n    };\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\nvar createInputMultipleFile = function (fig) {\n    var my = {},\n        self = createBaseInput(fig, my);\n\n    self.getType = function () {\n        return 'file[multiple]';\n    };\n\n    self.get = function () {\n        // http://stackoverflow.com/questions/14035530/how-to-get-value-of-html-5-multiple-file-upload-variable-using-jquery\n        var fileListObject = self.$().get(0).files || [],\n            names = [], i;\n\n        for(i = 0; i < (fileListObject.length || 0); i += 1) {\n            names.push(fileListObject[i].name);\n        }\n\n        return names;\n    };\n\n    self.clear = function () {\n        // http://stackoverflow.com/questions/1043957/clearing-input-type-file-using-jquery\n        this.$().each(function () {\n            $(this).wrap('<form>').closest('form').get(0).reset();\n            $(this).unwrap();\n        });\n    };\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputMultipleSelect = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'select[multiple]';\n    };\n\n    self.get = function () {\n        return self.$().val() || [];\n    };\n\n    self.set = function (newValues) {\n        self.$().val(\n            newValues === '' ? [] : isArray(newValues) ? newValues : [newValues]\n        );\n    };\n\n    my.equalTo = inputEqualToArray;\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputPassword = function (fig) {\n    var my = {},\n        self = createInputText(fig, my);\n\n    self.getType = function () {\n        return 'password';\n    };\n\n    return self;\n};\n\nvar createInputRadio = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'radio';\n    };\n\n    self.get = function () {\n        return self.$().filter(':checked').val() || null;\n    };\n\n    self.set = function (newValue) {\n        if(!newValue) {\n            self.$().each(function () {\n                $(this).prop('checked', false);\n            });\n        }\n        else {\n            self.$().filter('[value=\"' + newValue + '\"]').prop('checked', true);\n        }\n    };\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputRange = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'range';\n    };\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputSelect = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'select';\n    };\n\n    self.$().change(function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputText = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'text';\n    };\n\n    self.$().on('change keyup keydown', function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputTextarea = function (fig) {\n    var my = {},\n        self = createInput(fig, my);\n\n    self.getType = function () {\n        return 'textarea';\n    };\n\n    self.$().on('change keyup keydown', function (e) {\n        my.publishChange(e, this);\n    });\n\n    return self;\n};\n\nvar createInputURL = function (fig) {\n    var my = {},\n        self = createInputText(fig, my);\n\n    self.getType = function () {\n        return 'url';\n    };\n\n    return self;\n};\n\nvar buildFormInputs = function (fig) {\n    var inputs = {},\n        $self = fig.$;\n\n    var constructor = fig.constructorOverride || {\n        button: createInputButton,\n        text: createInputText,\n        url: createInputURL,\n        email: createInputEmail,\n        password: createInputPassword,\n        range: createInputRange,\n        textarea: createInputTextarea,\n        select: createInputSelect,\n        'select[multiple]': createInputMultipleSelect,\n        radio: createInputRadio,\n        checkbox: createInputCheckbox,\n        file: createInputFile,\n        'file[multiple]': createInputMultipleFile,\n        hidden: createInputHidden\n    };\n\n    var addInputsBasic = function (type, selector) {\n        var $input = isObject(selector) ? selector : $self.find(selector);\n\n        $input.each(function () {\n            var name = $(this).attr('name');\n            inputs[name] = constructor[type]({\n                $: $(this)\n            });\n        });\n    };\n\n    var addInputsGroup = function (type, selector) {\n        var names = [],\n            $input = isObject(selector) ? selector : $self.find(selector);\n\n        if(isObject(selector)) {\n            inputs[$input.attr('name')] = constructor[type]({\n                $: $input\n            });\n        }\n        else {\n            // group by name attribute\n            $input.each(function () {\n                if(indexOf(names, $(this).attr('name')) === -1) {\n                    names.push($(this).attr('name'));\n                }\n            });\n\n            foreach(names, function (name) {\n                inputs[name] = constructor[type]({\n                    $: $self.find('input[name=\"' + name + '\"]')\n                });\n            });\n        }\n    };\n\n\n    if($self.is('input, select, textarea')) {\n        if($self.is('input[type=\"button\"], button, input[type=\"submit\"]')) {\n            addInputsBasic('button', $self);\n        }\n        else if($self.is('textarea')) {\n            addInputsBasic('textarea', $self);\n        }\n        else if(\n            $self.is('input[type=\"text\"]') ||\n            $self.is('input') && !$self.attr('type')\n        ) {\n            addInputsBasic('text', $self);\n        }\n        else if($self.is('input[type=\"password\"]')) {\n            addInputsBasic('password', $self);\n        }\n        else if($self.is('input[type=\"email\"]')) {\n            addInputsBasic('email', $self);\n        }\n        else if($self.is('input[type=\"url\"]')) {\n            addInputsBasic('url', $self);\n        }\n        else if($self.is('input[type=\"range\"]')) {\n            addInputsBasic('range', $self);\n        }\n        else if($self.is('select')) {\n            if($self.is('[multiple]')) {\n                addInputsBasic('select[multiple]', $self);\n            }\n            else {\n                addInputsBasic('select', $self);\n            }\n        }\n        else if($self.is('input[type=\"file\"]')) {\n            if($self.is('[multiple]')) {\n                addInputsBasic('file[multiple]', $self);\n            }\n            else {\n                addInputsBasic('file', $self);\n            }\n        }\n        else if($self.is('input[type=\"hidden\"]')) {\n            addInputsBasic('hidden', $self);\n        }\n        else if($self.is('input[type=\"radio\"]')) {\n            addInputsGroup('radio', $self);\n        }\n        else if($self.is('input[type=\"checkbox\"]')) {\n            addInputsGroup('checkbox', $self);\n        }\n        else {\n            //in all other cases default to a \"text\" input interface.\n            addInputsBasic('text', $self);\n        }\n    }\n    else {\n        addInputsBasic('button', 'input[type=\"button\"], button, input[type=\"submit\"]');\n        addInputsBasic('text', 'input[type=\"text\"]');\n        addInputsBasic('password', 'input[type=\"password\"]');\n        addInputsBasic('email', 'input[type=\"email\"]');\n        addInputsBasic('url', 'input[type=\"url\"]');\n        addInputsBasic('range', 'input[type=\"range\"]');\n        addInputsBasic('textarea', 'textarea');\n        addInputsBasic('select', 'select:not([multiple])');\n        addInputsBasic('select[multiple]', 'select[multiple]');\n        addInputsBasic('file', 'input[type=\"file\"]:not([multiple])');\n        addInputsBasic('file[multiple]', 'input[type=\"file\"][multiple]');\n        addInputsBasic('hidden', 'input[type=\"hidden\"]');\n        addInputsGroup('radio', 'input[type=\"radio\"]');\n        addInputsGroup('checkbox', 'input[type=\"checkbox\"]');\n    }\n\n    return inputs;\n};\n\n$.fn.inputVal = function (newValue) {\n    var $self = $(this);\n\n    var inputs = buildFormInputs({ $: $self });\n\n    if($self.is('input, textarea, select')) {\n        if(typeof newValue === 'undefined') {\n            return inputs[$self.attr('name')].get();\n        }\n        else {\n            inputs[$self.attr('name')].set(newValue);\n            return $self;\n        }\n    }\n    else {\n        if(typeof newValue === 'undefined') {\n            return call(inputs, 'get');\n        }\n        else {\n            foreach(newValue, function (value, inputName) {\n                inputs[inputName].set(value);\n            });\n            return $self;\n        }\n    }\n};\n\n$.fn.inputOnChange = function (callback) {\n    var $self = $(this);\n    var inputs = buildFormInputs({ $: $self });\n    foreach(inputs, function (input) {\n        input.subscribe('change', function (data) {\n            callback.call(data.domElement, data.e);\n        });\n    });\n    return $self;\n};\n\n$.fn.inputDisable = function () {\n    var $self = $(this);\n    call(buildFormInputs({ $: $self }), 'disable');\n    return $self;\n};\n\n$.fn.inputEnable = function () {\n    var $self = $(this);\n    call(buildFormInputs({ $: $self }), 'enable');\n    return $self;\n};\n\n$.fn.inputClear = function () {\n    var $self = $(this);\n    call(buildFormInputs({ $: $self }), 'clear');\n    return $self;\n};\n\n}(jQuery));\n\n$.fn.repeaterVal = function () {\n    var parse = function (raw) {\n        var parsed = [];\n\n        foreach(raw, function (val, key) {\n            var parsedKey = [];\n            if(key !== \"undefined\") {\n                parsedKey.push(key.match(/^[^\\[]*/)[0]);\n                parsedKey = parsedKey.concat(map(\n                    key.match(/\\[[^\\]]*\\]/g),\n                    function (bracketed) {\n                        return bracketed.replace(/[\\[\\]]/g, '');\n                    }\n                ));\n\n                parsed.push({\n                    val: val,\n                    key: parsedKey\n                });\n            }\n        });\n\n        return parsed;\n    };\n\n    var build = function (parsed) {\n        if(\n            parsed.length === 1 &&\n            (parsed[0].key.length === 0 || parsed[0].key.length === 1 && !parsed[0].key[0])\n        ) {\n            return parsed[0].val;\n        }\n\n        foreach(parsed, function (p) {\n            p.head = p.key.shift();\n        });\n\n        var grouped = (function () {\n            var grouped = {};\n\n            foreach(parsed, function (p) {\n                if(!grouped[p.head]) {\n                    grouped[p.head] = [];\n                }\n                grouped[p.head].push(p);\n            });\n\n            return grouped;\n        }());\n\n        var built;\n\n        if(/^[0-9]+$/.test(parsed[0].head)) {\n            built = [];\n            foreach(grouped, function (group) {\n                built.push(build(group));\n            });\n        }\n        else {\n            built = {};\n            foreach(grouped, function (group, key) {\n                built[key] = build(group);\n            });\n        }\n\n        return built;\n    };\n\n    return build(parse($(this).inputVal()));\n};\n\n$.fn.repeater = function (fig) {\n    fig = fig || {};\n\n    var setList;\n\n    $(this).each(function () {\n\n        var $self = $(this);\n\n        var show = fig.show || function () {\n            $(this).show();\n        };\n\n        var hide = fig.hide || function (removeElement) {\n            removeElement();\n        };\n\n        var $list = $self.find('[data-repeater-list]').first();\n\n        var $filterNested = function ($items, repeaters) {\n            return $items.filter(function () {\n                return repeaters ?\n                    $(this).closest(\n                        pluck(repeaters, 'selector').join(',')\n                    ).length === 0 : true;\n            });\n        };\n\n        var $items = function () {\n            return $filterNested($list.find('[data-repeater-item]'), fig.repeaters);\n        };\n\n        var $itemTemplate = $list.find('[data-repeater-item]')\n                                 .first().clone().hide();\n\n        var $firstDeleteButton = $filterNested(\n            $filterNested($(this).find('[data-repeater-item]'), fig.repeaters)\n            .first().find('[data-repeater-delete]'),\n            fig.repeaters\n        );\n\n        if(fig.isFirstItemUndeletable && $firstDeleteButton) {\n            $firstDeleteButton.remove();\n        }\n\n        var getGroupName = function () {\n            var groupName = $list.data('repeater-list');\n            return fig.$parent ?\n                fig.$parent.data('item-name') + '[' + groupName + ']' :\n                groupName;\n        };\n\n        var initNested = function ($listItems) {\n            if(fig.repeaters) {\n                $listItems.each(function () {\n                    var $item = $(this);\n                    foreach(fig.repeaters, function (nestedFig) {\n                        $item.find(nestedFig.selector).repeater(extend(\n                            nestedFig, { $parent: $item }\n                        ));\n                    });\n                });\n            }\n        };\n\n        var $foreachRepeaterInItem = function (repeaters, $item, cb) {\n            if(repeaters) {\n                foreach(repeaters, function (nestedFig) {\n                    cb.call($item.find(nestedFig.selector)[0], nestedFig);\n                });\n            }\n        };\n\n        var setIndexes = function ($items, groupName, repeaters) {\n            $items.each(function (index) {\n                var $item = $(this);\n                $item.data('item-name', groupName + '[' + index + ']');\n                $filterNested($item.find('[name]'), repeaters)\n                .each(function () {\n                    var $input = $(this);\n                    // match non empty brackets (ex: \"[foo]\")\n                    var matches = $input.attr('name').match(/\\[[^\\]]+\\]/g);\n\n                    var name = matches ?\n                        // strip \"[\" and \"]\" characters\n                        last(matches).replace(/\\[|\\]/g, '') :\n                        $input.attr('name');\n\n\n                    var newName = groupName + '[' + index + '][' + name + ']' +\n                        ($input.is(':checkbox') || $input.attr('multiple') ? '[]' : '');\n\n                    $input.attr('name', newName);\n\n                    $foreachRepeaterInItem(repeaters, $item, function (nestedFig) {\n                        var $repeater = $(this);\n                        setIndexes(\n                            $filterNested($repeater.find('[data-repeater-item]'), nestedFig.repeaters || []),\n                            groupName + '[' + index + ']' +\n                                        '[' + $repeater.find('[data-repeater-list]').first().data('repeater-list') + ']',\n                            nestedFig.repeaters\n                        );\n                    });\n                });\n            });\n\n            $list.find('input[name][checked]')\n                .removeAttr('checked')\n                .prop('checked', true);\n        };\n\n        setIndexes($items(), getGroupName(), fig.repeaters);\n        initNested($items());\n        if(fig.initEmpty) {\n            $items().remove();\n        }\n\n        if(fig.ready) {\n            fig.ready(function () {\n                setIndexes($items(), getGroupName(), fig.repeaters);\n            });\n        }\n\n        var appendItem = (function () {\n            var setItemsValues = function ($item, data, repeaters) {\n                if(data || fig.defaultValues) {\n                    var inputNames = {};\n                    $filterNested($item.find('[name]'), repeaters).each(function () {\n                        var key = $(this).attr('name').match(/\\[([^\\]]*)(\\]|\\]\\[\\])$/)[1];\n                        inputNames[key] = $(this).attr('name');\n                    });\n\n                    $item.inputVal(map(\n                        filter(data || fig.defaultValues, function (val, name) {\n                            return inputNames[name];\n                        }),\n                        identity,\n                        function (name) {\n                            return inputNames[name];\n                        }\n                    ));\n                }\n\n\n                $foreachRepeaterInItem(repeaters, $item, function (nestedFig) {\n                    var $repeater = $(this);\n                    $filterNested(\n                        $repeater.find('[data-repeater-item]'),\n                        nestedFig.repeaters\n                    )\n                    .each(function () {\n                        var fieldName = $repeater.find('[data-repeater-list]').data('repeater-list');\n                        if(data && data[fieldName]) {\n                            var $template = $(this).clone();\n                            $repeater.find('[data-repeater-item]').remove();\n                            foreach(data[fieldName], function (data) {\n                                var $item = $template.clone();\n                                setItemsValues(\n                                    $item,\n                                    data,\n                                    nestedFig.repeaters || []\n                                );\n                                $repeater.find('[data-repeater-list]').append($item);\n                            });\n                        }\n                        else {\n                            setItemsValues(\n                                $(this),\n                                nestedFig.defaultValues,\n                                nestedFig.repeaters || []\n                            );\n                        }\n                    });\n                });\n\n            };\n\n            return function ($item, data) {\n                $list.append($item);\n                setIndexes($items(), getGroupName(), fig.repeaters);\n                $item.find('[name]').each(function () {\n                    $(this).inputClear();\n                });\n                setItemsValues($item, data || fig.defaultValues, fig.repeaters);\n            };\n        }());\n\n        var addItem = function (data) {\n            var $item = $itemTemplate.clone();\n            appendItem($item, data);\n            if(fig.repeaters) {\n                initNested($item);\n            }\n            show.call($item.get(0));\n        };\n\n        setList = function (rows) {\n            $items().remove();\n            foreach(rows, addItem);\n        };\n\n        $filterNested($self.find('[data-repeater-create]'), fig.repeaters).click(function () {\n            addItem();\n        });\n\n        $list.on('click', '[data-repeater-delete]', function () {\n            var self = $(this).closest('[data-repeater-item]').get(0);\n            hide.call(self, function () {\n                $(self).remove();\n                setIndexes($items(), getGroupName(), fig.repeaters);\n            });\n        });\n    });\n\n    this.setList = setList;\n\n    return this;\n};\n\n}(jQuery));\n\n//# sourceURL=webpack://Materialize/./node_modules/jquery.repeater/jquery.repeater.js?")}},__webpack_module_cache__={};function __webpack_require__(n){var e=__webpack_module_cache__[n];if(void 0!==e)return e.exports;var t=__webpack_module_cache__[n]={exports:{}};return __webpack_modules__[n](t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return __webpack_require__.d(e,{a:e}),e},__webpack_require__.d=function(n,e){for(var t in e)__webpack_require__.o(e,t)&&!__webpack_require__.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},__webpack_require__.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},__webpack_require__.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/jquery-repeater/jquery-repeater.js");return __webpack_exports__}()}));