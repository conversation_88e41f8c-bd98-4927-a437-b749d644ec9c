"""
Utilitaires pour la gestion des organisations
"""
import re
from typing import Dict, Optional


# Codes pays africains les plus courants
CODES_PAYS_AFRIQUE = {
    '+213': 'Algérie',
    '+244': 'Angola',
    '+229': '<PERSON><PERSON><PERSON>',
    '+267': 'Botswana',
    '+226': 'Burkina Faso',
    '+257': 'Burundi',
    '+237': 'Cameroun',
    '+238': 'Cap-Vert',
    '+236': 'République centrafricaine',
    '+235': 'Tchad',
    '+269': 'Comores',
    '+242': 'République du Congo',
    '+243': 'République démocratique du Congo',
    '+225': 'Côte d\'Ivoire',
    '+253': 'Djibouti',
    '+20': 'Égypte',
    '+240': 'Guinée équatoriale',
    '+291': 'Érythrée',
    '+251': 'Éthiopie',
    '+241': 'Gabon',
    '+220': '<PERSON><PERSON><PERSON>',
    '+233': 'Ghana',
    '+224': 'Guinée',
    '+245': 'Guinée-Bissau',
    '+254': 'Kenya',
    '+266': 'Lesotho',
    '+231': 'Lib<PERSON><PERSON>',
    '+218': 'Libye',
    '+261': 'Madagascar',
    '+265': 'Malawi',
    '+223': 'Mali',
    '+222': 'Mauritanie',
    '+230': 'Maurice',
    '+212': 'Maroc',
    '+258': 'Mozambique',
    '+264': 'Namibie',
    '+227': 'Niger',
    '+234': 'Nigéria',
    '+250': 'Rwanda',
    '+239': 'São Tomé-et-Principe',
    '+221': 'Sénégal',
    '+248': 'Seychelles',
    '+232': 'Sierra Leone',
    '+252': 'Somalie',
    '+27': 'Afrique du Sud',
    '+211': 'Soudan du Sud',
    '+249': 'Soudan',
    '+268': 'Eswatini',
    '+255': 'Tanzanie',
    '+228': 'Togo',
    '+216': 'Tunisie',
    '+256': 'Ouganda',
    '+260': 'Zambie',
    '+263': 'Zimbabwe',
}


def valider_telephone_international(numero: str) -> bool:
    """
    Valide un numéro de téléphone au format international
    
    Args:
        numero (str): Le numéro de téléphone à valider
        
    Returns:
        bool: True si le numéro est valide, False sinon
    """
    if not numero:
        return False
    
    # Nettoyer le numéro (supprimer espaces, tirets, etc.)
    numero_nettoye = re.sub(r'[\s\-\(\)]', '', numero)
    
    # Vérifier le format international de base
    pattern = r'^\+[1-9]\d{1,14}$'
    return bool(re.match(pattern, numero_nettoye))


def formater_telephone(numero: str) -> str:
    """
    Formate un numéro de téléphone pour l'affichage
    
    Args:
        numero (str): Le numéro de téléphone à formater
        
    Returns:
        str: Le numéro formaté
    """
    if not numero:
        return ""
    
    # Nettoyer le numéro
    numero_nettoye = re.sub(r'[\s\-\(\)]', '', numero)
    
    if not numero_nettoye.startswith('+'):
        return numero
    
    # Formater selon le code pays
    for code_pays in CODES_PAYS_AFRIQUE.keys():
        if numero_nettoye.startswith(code_pays):
            # Séparer le code pays du reste
            reste = numero_nettoye[len(code_pays):]
            
            # Formater selon la longueur
            if len(reste) >= 8:
                # Format : +XXX XX XX XX XX XX
                parties = [reste[i:i+2] for i in range(0, len(reste), 2)]
                return f"{code_pays} {' '.join(parties)}"
    
    return numero


def obtenir_pays_depuis_telephone(numero: str) -> Optional[str]:
    """
    Détermine le pays d'origine d'un numéro de téléphone
    
    Args:
        numero (str): Le numéro de téléphone
        
    Returns:
        Optional[str]: Le nom du pays ou None si non trouvé
    """
    if not numero:
        return None
    
    numero_nettoye = re.sub(r'[\s\-\(\)]', '', numero)
    
    for code_pays, nom_pays in CODES_PAYS_AFRIQUE.items():
        if numero_nettoye.startswith(code_pays):
            return nom_pays
    
    return None


def obtenir_exemples_telephone() -> Dict[str, str]:
    """
    Retourne des exemples de numéros de téléphone pour les pays africains principaux
    
    Returns:
        Dict[str, str]: Dictionnaire avec pays comme clé et exemple comme valeur
    """
    exemples = {
        'Côte d\'Ivoire': '+225 01 23 45 67 89',
        'Sénégal': '+221 77 123 45 67',
        'Cameroun': '+237 6 12 34 56 78',
        'Mali': '+223 20 12 34 56',
        'Burkina Faso': '+226 70 12 34 56',
        'Niger': '+227 96 12 34 56',
        'Guinée': '+224 622 12 34 56',
        'Bénin': '+229 97 12 34 56',
        'Togo': '+228 90 12 34 56',
        'Ghana': '+233 24 123 4567',
        'Nigéria': '+234 ************',
        'Maroc': '+212 6 12 34 56 78',
        'Tunisie': '+216 20 123 456',
        'Algérie': '+213 5 12 34 56 78',
        'Égypte': '+20 10 1234 5678',
        'Afrique du Sud': '+27 82 123 4567',
        'Kenya': '+254 712 345 678',
        'Éthiopie': '+251 911 123 456',
        'Ouganda': '+256 772 123 456',
        'Tanzanie': '+255 754 123 456',
    }
    
    return exemples


def est_numero_africain(numero: str) -> bool:
    """
    Vérifie si un numéro de téléphone est d'un pays africain
    
    Args:
        numero (str): Le numéro de téléphone à vérifier
        
    Returns:
        bool: True si le numéro est africain, False sinon
    """
    return obtenir_pays_depuis_telephone(numero) is not None
