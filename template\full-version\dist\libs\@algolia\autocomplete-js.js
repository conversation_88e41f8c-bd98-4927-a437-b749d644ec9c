!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./node_modules/@algolia/autocomplete-core/dist/esm/checkOptions.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOptions: function() { return /* binding */ checkOptions; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ \"./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js\");\n\nfunction checkOptions(options) {\n   true ? (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.warn)(!options.debug, 'The `debug` option is meant for development debugging and should not be used in production.') : 0;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/checkOptions.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/createAutocomplete.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAutocomplete: function() { return /* binding */ createAutocomplete; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_plugin_algolia_insights__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @algolia/autocomplete-plugin-algolia-insights */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createAlgoliaInsightsPlugin.js");\n/* harmony import */ var _checkOptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./checkOptions */ "./node_modules/@algolia/autocomplete-core/dist/esm/checkOptions.js");\n/* harmony import */ var _createStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createStore */ "./node_modules/@algolia/autocomplete-core/dist/esm/createStore.js");\n/* harmony import */ var _getAutocompleteSetters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getAutocompleteSetters */ "./node_modules/@algolia/autocomplete-core/dist/esm/getAutocompleteSetters.js");\n/* harmony import */ var _getDefaultProps__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDefaultProps */ "./node_modules/@algolia/autocomplete-core/dist/esm/getDefaultProps.js");\n/* harmony import */ var _getPropGetters__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getPropGetters */ "./node_modules/@algolia/autocomplete-core/dist/esm/getPropGetters.js");\n/* harmony import */ var _metadata__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./metadata */ "./node_modules/@algolia/autocomplete-core/dist/esm/metadata.js");\n/* harmony import */ var _onInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./onInput */ "./node_modules/@algolia/autocomplete-core/dist/esm/onInput.js");\n/* harmony import */ var _stateReducer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stateReducer */ "./node_modules/@algolia/autocomplete-core/dist/esm/stateReducer.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\n\n\n\n\n\n\n\n\nfunction createAutocomplete(options) {\n  (0,_checkOptions__WEBPACK_IMPORTED_MODULE_0__.checkOptions)(options);\n  var subscribers = [];\n  var props = (0,_getDefaultProps__WEBPACK_IMPORTED_MODULE_1__.getDefaultProps)(options, subscribers);\n  var store = (0,_createStore__WEBPACK_IMPORTED_MODULE_2__.createStore)(_stateReducer__WEBPACK_IMPORTED_MODULE_3__.stateReducer, props, onStoreStateChange);\n  var setters = (0,_getAutocompleteSetters__WEBPACK_IMPORTED_MODULE_4__.getAutocompleteSetters)({\n    store: store\n  });\n  var propGetters = (0,_getPropGetters__WEBPACK_IMPORTED_MODULE_5__.getPropGetters)(_objectSpread({\n    props: props,\n    refresh: refresh,\n    store: store,\n    navigator: props.navigator\n  }, setters));\n  function onStoreStateChange(_ref) {\n    var _state$context, _state$context$algoli;\n    var prevState = _ref.prevState,\n      state = _ref.state;\n    props.onStateChange(_objectSpread({\n      prevState: prevState,\n      state: state,\n      refresh: refresh,\n      navigator: props.navigator\n    }, setters));\n    if (!isAlgoliaInsightsPluginEnabled() && (_state$context = state.context) !== null && _state$context !== void 0 && (_state$context$algoli = _state$context.algoliaInsightsPlugin) !== null && _state$context$algoli !== void 0 && _state$context$algoli.__automaticInsights && props.insights !== false) {\n      var plugin = (0,_algolia_autocomplete_plugin_algolia_insights__WEBPACK_IMPORTED_MODULE_6__.createAlgoliaInsightsPlugin)({\n        __autocomplete_clickAnalytics: false\n      });\n      props.plugins.push(plugin);\n      subscribePlugins([plugin]);\n    }\n  }\n  function refresh() {\n    return (0,_onInput__WEBPACK_IMPORTED_MODULE_7__.onInput)(_objectSpread({\n      event: new Event(\'input\'),\n      nextState: {\n        isOpen: store.getState().isOpen\n      },\n      props: props,\n      navigator: props.navigator,\n      query: store.getState().query,\n      refresh: refresh,\n      store: store\n    }, setters));\n  }\n  function subscribePlugins(plugins) {\n    plugins.forEach(function (plugin) {\n      var _plugin$subscribe;\n      return (_plugin$subscribe = plugin.subscribe) === null || _plugin$subscribe === void 0 ? void 0 : _plugin$subscribe.call(plugin, _objectSpread(_objectSpread({}, setters), {}, {\n        navigator: props.navigator,\n        refresh: refresh,\n        onSelect: function onSelect(fn) {\n          subscribers.push({\n            onSelect: fn\n          });\n        },\n        onActive: function onActive(fn) {\n          subscribers.push({\n            onActive: fn\n          });\n        },\n        onResolve: function onResolve(fn) {\n          subscribers.push({\n            onResolve: fn\n          });\n        }\n      }));\n    });\n  }\n  function isAlgoliaInsightsPluginEnabled() {\n    return props.plugins.some(function (plugin) {\n      return plugin.name === \'aa.algoliaInsightsPlugin\';\n    });\n  }\n  if (props.insights && !isAlgoliaInsightsPluginEnabled()) {\n    var insightsParams = typeof props.insights === \'boolean\' ? {} : props.insights;\n    props.plugins.push((0,_algolia_autocomplete_plugin_algolia_insights__WEBPACK_IMPORTED_MODULE_6__.createAlgoliaInsightsPlugin)(insightsParams));\n  }\n  subscribePlugins(props.plugins);\n  (0,_metadata__WEBPACK_IMPORTED_MODULE_8__.injectMetadata)({\n    metadata: (0,_metadata__WEBPACK_IMPORTED_MODULE_8__.getMetadata)({\n      plugins: props.plugins,\n      options: options\n    }),\n    environment: props.environment\n  });\n  return _objectSpread(_objectSpread({\n    refresh: refresh,\n    navigator: props.navigator\n  }, propGetters), setters);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/createAutocomplete.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/createStore.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: function() { return /* binding */ createStore; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromiseList.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\nfunction createStore(reducer, props, onStoreStateChange) {\n  var state = props.initialState;\n  return {\n    getState: function getState() {\n      return state;\n    },\n    dispatch: function dispatch(action, payload) {\n      var prevState = _objectSpread({}, state);\n      state = reducer(state, {\n        type: action,\n        props: props,\n        payload: payload\n      });\n      onStoreStateChange({\n        state: state,\n        prevState: prevState\n      });\n    },\n    pendingRequests: (0,_utils__WEBPACK_IMPORTED_MODULE_0__.createCancelablePromiseList)()\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/createStore.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/getAutocompleteSetters.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAutocompleteSetters: function() { return /* binding */ getAutocompleteSetters; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\nfunction getAutocompleteSetters(_ref) {\n  var store = _ref.store;\n  var setActiveItemId = function setActiveItemId(value) {\n    store.dispatch(\'setActiveItemId\', value);\n  };\n  var setQuery = function setQuery(value) {\n    store.dispatch(\'setQuery\', value);\n  };\n  var setCollections = function setCollections(rawValue) {\n    var baseItemId = 0;\n    var value = rawValue.map(function (collection) {\n      return _objectSpread(_objectSpread({}, collection), {}, {\n        // We flatten the stored items to support calling `getAlgoliaResults`\n        // from the source itself.\n        items: (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.flatten)(collection.items).map(function (item) {\n          return _objectSpread(_objectSpread({}, item), {}, {\n            __autocomplete_id: baseItemId++\n          });\n        })\n      });\n    });\n    store.dispatch(\'setCollections\', value);\n  };\n  var setIsOpen = function setIsOpen(value) {\n    store.dispatch(\'setIsOpen\', value);\n  };\n  var setStatus = function setStatus(value) {\n    store.dispatch(\'setStatus\', value);\n  };\n  var setContext = function setContext(value) {\n    store.dispatch(\'setContext\', value);\n  };\n  return {\n    setActiveItemId: setActiveItemId,\n    setQuery: setQuery,\n    setCollections: setCollections,\n    setIsOpen: setIsOpen,\n    setStatus: setStatus,\n    setContext: setContext\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/getAutocompleteSetters.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/getCompletion.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCompletion: function() { return /* binding */ getCompletion; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js");\n\nfunction getCompletion(_ref) {\n  var _getActiveItem;\n  var state = _ref.state;\n  if (state.isOpen === false || state.activeItemId === null) {\n    return null;\n  }\n  return ((_getActiveItem = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getActiveItem)(state)) === null || _getActiveItem === void 0 ? void 0 : _getActiveItem.itemInputValue) || null;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/getCompletion.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/getDefaultProps.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultProps: function() { return /* binding */ getDefaultProps; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/getItemsCount.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/generateAutocompleteId.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNormalizedSources.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\n\nfunction getDefaultProps(props, pluginSubscribers) {\n  var _props$id;\n  /* eslint-disable no-restricted-globals */\n  var environment = typeof window !== \'undefined\' ? window : {};\n  /* eslint-enable no-restricted-globals */\n  var plugins = props.plugins || [];\n  return _objectSpread(_objectSpread({\n    debug: false,\n    openOnFocus: false,\n    enterKeyHint: undefined,\n    ignoreCompositionEvents: false,\n    placeholder: \'\',\n    autoFocus: false,\n    defaultActiveItemId: null,\n    stallThreshold: 300,\n    insights: undefined,\n    environment: environment,\n    shouldPanelOpen: function shouldPanelOpen(_ref) {\n      var state = _ref.state;\n      return (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.getItemsCount)(state) > 0;\n    },\n    reshape: function reshape(_ref2) {\n      var sources = _ref2.sources;\n      return sources;\n    }\n  }, props), {}, {\n    // Since `generateAutocompleteId` triggers a side effect (it increments\n    // an internal counter), we don\'t want to execute it if unnecessary.\n    id: (_props$id = props.id) !== null && _props$id !== void 0 ? _props$id : (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__.generateAutocompleteId)(),\n    plugins: plugins,\n    // The following props need to be deeply defaulted.\n    initialState: _objectSpread({\n      activeItemId: null,\n      query: \'\',\n      completion: null,\n      collections: [],\n      isOpen: false,\n      status: \'idle\',\n      context: {}\n    }, props.initialState),\n    onStateChange: function onStateChange(params) {\n      var _props$onStateChange;\n      (_props$onStateChange = props.onStateChange) === null || _props$onStateChange === void 0 ? void 0 : _props$onStateChange.call(props, params);\n      plugins.forEach(function (x) {\n        var _x$onStateChange;\n        return (_x$onStateChange = x.onStateChange) === null || _x$onStateChange === void 0 ? void 0 : _x$onStateChange.call(x, params);\n      });\n    },\n    onSubmit: function onSubmit(params) {\n      var _props$onSubmit;\n      (_props$onSubmit = props.onSubmit) === null || _props$onSubmit === void 0 ? void 0 : _props$onSubmit.call(props, params);\n      plugins.forEach(function (x) {\n        var _x$onSubmit;\n        return (_x$onSubmit = x.onSubmit) === null || _x$onSubmit === void 0 ? void 0 : _x$onSubmit.call(x, params);\n      });\n    },\n    onReset: function onReset(params) {\n      var _props$onReset;\n      (_props$onReset = props.onReset) === null || _props$onReset === void 0 ? void 0 : _props$onReset.call(props, params);\n      plugins.forEach(function (x) {\n        var _x$onReset;\n        return (_x$onReset = x.onReset) === null || _x$onReset === void 0 ? void 0 : _x$onReset.call(x, params);\n      });\n    },\n    getSources: function getSources(params) {\n      return Promise.all([].concat(_toConsumableArray(plugins.map(function (plugin) {\n        return plugin.getSources;\n      })), [props.getSources]).filter(Boolean).map(function (getSources) {\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getNormalizedSources)(getSources, params);\n      })).then(function (nested) {\n        return (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.flatten)(nested);\n      }).then(function (sources) {\n        return sources.map(function (source) {\n          return _objectSpread(_objectSpread({}, source), {}, {\n            onSelect: function onSelect(params) {\n              source.onSelect(params);\n              pluginSubscribers.forEach(function (x) {\n                var _x$onSelect;\n                return (_x$onSelect = x.onSelect) === null || _x$onSelect === void 0 ? void 0 : _x$onSelect.call(x, params);\n              });\n            },\n            onActive: function onActive(params) {\n              source.onActive(params);\n              pluginSubscribers.forEach(function (x) {\n                var _x$onActive;\n                return (_x$onActive = x.onActive) === null || _x$onActive === void 0 ? void 0 : _x$onActive.call(x, params);\n              });\n            },\n            onResolve: function onResolve(params) {\n              source.onResolve(params);\n              pluginSubscribers.forEach(function (x) {\n                var _x$onResolve;\n                return (_x$onResolve = x.onResolve) === null || _x$onResolve === void 0 ? void 0 : _x$onResolve.call(x, params);\n              });\n            }\n          });\n        });\n      });\n    },\n    navigator: _objectSpread({\n      navigate: function navigate(_ref3) {\n        var itemUrl = _ref3.itemUrl;\n        environment.location.assign(itemUrl);\n      },\n      navigateNewTab: function navigateNewTab(_ref4) {\n        var itemUrl = _ref4.itemUrl;\n        var windowReference = environment.open(itemUrl, \'_blank\', \'noopener\');\n        windowReference === null || windowReference === void 0 ? void 0 : windowReference.focus();\n      },\n      navigateNewWindow: function navigateNewWindow(_ref5) {\n        var itemUrl = _ref5.itemUrl;\n        environment.open(itemUrl, \'_blank\', \'noopener\');\n      }\n    }, props.navigator)\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/getDefaultProps.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/getPropGetters.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPropGetters: function() { return /* binding */ getPropGetters; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @algolia/autocomplete-shared */ \"./node_modules/@algolia/autocomplete-shared/dist/esm/noop.js\");\n/* harmony import */ var _onInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onInput */ \"./node_modules/@algolia/autocomplete-core/dist/esm/onInput.js\");\n/* harmony import */ var _onKeyDown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./onKeyDown */ \"./node_modules/@algolia/autocomplete-core/dist/esm/onKeyDown.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/isOrContainsNode.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getAutocompleteElementId.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getPluginSubmitPromise.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/isSamsung.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNativeEvent.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar _excluded = [\"props\", \"refresh\", \"store\"],\n  _excluded2 = [\"inputElement\", \"formElement\", \"panelElement\"],\n  _excluded3 = [\"inputElement\"],\n  _excluded4 = [\"inputElement\", \"maxLength\"],\n  _excluded5 = [\"source\"],\n  _excluded6 = [\"item\", \"source\"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\n\n\nfunction getPropGetters(_ref) {\n  var props = _ref.props,\n    refresh = _ref.refresh,\n    store = _ref.store,\n    setters = _objectWithoutProperties(_ref, _excluded);\n  var getEnvironmentProps = function getEnvironmentProps(providedProps) {\n    var inputElement = providedProps.inputElement,\n      formElement = providedProps.formElement,\n      panelElement = providedProps.panelElement,\n      rest = _objectWithoutProperties(providedProps, _excluded2);\n    function onMouseDownOrTouchStart(event) {\n      // The `onTouchStart`/`onMouseDown` events shouldn't trigger the `blur`\n      // handler when it's not an interaction with Autocomplete.\n      // We detect it with the following heuristics:\n      // - the panel is closed AND there are no pending requests\n      //   (no interaction with the autocomplete, no future state updates)\n      // - OR the touched target is the input element (should open the panel)\n      var isAutocompleteInteraction = store.getState().isOpen || !store.pendingRequests.isEmpty();\n      if (!isAutocompleteInteraction || event.target === inputElement) {\n        return;\n      }\n\n      // @TODO: support cases where there are multiple Autocomplete instances.\n      // Right now, a second instance makes this computation return false.\n      var isTargetWithinAutocomplete = [formElement, panelElement].some(function (contextNode) {\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.isOrContainsNode)(contextNode, event.target);\n      });\n      if (isTargetWithinAutocomplete === false) {\n        store.dispatch('blur', null);\n\n        // If requests are still pending when the user closes the panel, they\n        // could reopen the panel once they resolve.\n        // We want to prevent any subsequent query from reopening the panel\n        // because it would result in an unsolicited UI behavior.\n        if (!props.debug) {\n          store.pendingRequests.cancelAll();\n        }\n      }\n    }\n    return _objectSpread({\n      // We do not rely on the native `blur` event of the input to close the\n      // panel, but rather on a custom `touchstart`/`mousedown` event outside\n      // of the autocomplete elements.\n      // This ensures we don't mistakenly interpret interactions within the\n      // autocomplete (but outside of the input) as a signal to close the panel.\n      // For example, clicking reset button causes an input blur, but if\n      // `openOnFocus=true`, it shouldn't close the panel.\n      // On touch devices, scrolling results (`touchmove`) causes an input blur\n      // but shouldn't close the panel.\n      onTouchStart: onMouseDownOrTouchStart,\n      onMouseDown: onMouseDownOrTouchStart,\n      // When scrolling on touch devices (mobiles, tablets, etc.), we want to\n      // mimic the native platform behavior where the input is blurred to\n      // hide the virtual keyboard. This gives more vertical space to\n      // discover all the suggestions showing up in the panel.\n      onTouchMove: function onTouchMove(event) {\n        if (store.getState().isOpen === false || inputElement !== props.environment.document.activeElement || event.target === inputElement) {\n          return;\n        }\n        inputElement.blur();\n      }\n    }, rest);\n  };\n  var getRootProps = function getRootProps(rest) {\n    return _objectSpread({\n      role: 'combobox',\n      'aria-expanded': store.getState().isOpen,\n      'aria-haspopup': 'listbox',\n      'aria-controls': store.getState().isOpen ? store.getState().collections.map(function (_ref2) {\n        var source = _ref2.source;\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'list', source);\n      }).join(' ') : undefined,\n      'aria-labelledby': (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'label')\n    }, rest);\n  };\n  var getFormProps = function getFormProps(providedProps) {\n    var inputElement = providedProps.inputElement,\n      rest = _objectWithoutProperties(providedProps, _excluded3);\n    var handleSubmit = function handleSubmit(event) {\n      var _providedProps$inputE;\n      props.onSubmit(_objectSpread({\n        event: event,\n        refresh: refresh,\n        state: store.getState()\n      }, setters));\n      store.dispatch('submit', null);\n      (_providedProps$inputE = providedProps.inputElement) === null || _providedProps$inputE === void 0 ? void 0 : _providedProps$inputE.blur();\n    };\n    return _objectSpread({\n      action: '',\n      noValidate: true,\n      role: 'search',\n      onSubmit: function onSubmit(event) {\n        event.preventDefault();\n        var waitForSubmit = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getPluginSubmitPromise)(props.plugins, store.pendingRequests);\n        if (waitForSubmit !== undefined) {\n          waitForSubmit.then(function () {\n            return handleSubmit(event);\n          });\n        } else {\n          handleSubmit(event);\n        }\n      },\n      onReset: function onReset(event) {\n        var _providedProps$inputE2;\n        event.preventDefault();\n        props.onReset(_objectSpread({\n          event: event,\n          refresh: refresh,\n          state: store.getState()\n        }, setters));\n        store.dispatch('reset', null);\n        (_providedProps$inputE2 = providedProps.inputElement) === null || _providedProps$inputE2 === void 0 ? void 0 : _providedProps$inputE2.focus();\n      }\n    }, rest);\n  };\n  var getInputProps = function getInputProps(providedProps) {\n    var _props$environment$na;\n    function onFocus(event) {\n      // We want to trigger a query when `openOnFocus` is true\n      // because the panel should open with the current query.\n      if (props.openOnFocus || Boolean(store.getState().query)) {\n        (0,_onInput__WEBPACK_IMPORTED_MODULE_3__.onInput)(_objectSpread({\n          event: event,\n          props: props,\n          query: store.getState().completion || store.getState().query,\n          refresh: refresh,\n          store: store\n        }, setters));\n      }\n      store.dispatch('focus', null);\n    }\n    var _ref3 = providedProps || {},\n      inputElement = _ref3.inputElement,\n      _ref3$maxLength = _ref3.maxLength,\n      maxLength = _ref3$maxLength === void 0 ? 512 : _ref3$maxLength,\n      rest = _objectWithoutProperties(_ref3, _excluded4);\n    var activeItem = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.getActiveItem)(store.getState());\n    var userAgent = ((_props$environment$na = props.environment.navigator) === null || _props$environment$na === void 0 ? void 0 : _props$environment$na.userAgent) || '';\n    var shouldFallbackKeyHint = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.isSamsung)(userAgent);\n    var enterKeyHint = props.enterKeyHint || (activeItem !== null && activeItem !== void 0 && activeItem.itemUrl && !shouldFallbackKeyHint ? 'go' : 'search');\n    return _objectSpread({\n      'aria-autocomplete': 'both',\n      'aria-activedescendant': store.getState().isOpen && store.getState().activeItemId !== null ? (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, \"item-\".concat(store.getState().activeItemId), activeItem === null || activeItem === void 0 ? void 0 : activeItem.source) : undefined,\n      'aria-controls': store.getState().isOpen ? store.getState().collections.filter(function (collection) {\n        return collection.items.length > 0;\n      }).map(function (_ref4) {\n        var source = _ref4.source;\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'list', source);\n      }).join(' ') : undefined,\n      'aria-labelledby': (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'label'),\n      value: store.getState().completion || store.getState().query,\n      id: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'input'),\n      autoComplete: 'off',\n      autoCorrect: 'off',\n      autoCapitalize: 'off',\n      enterKeyHint: enterKeyHint,\n      spellCheck: 'false',\n      autoFocus: props.autoFocus,\n      placeholder: props.placeholder,\n      maxLength: maxLength,\n      type: 'search',\n      onChange: function onChange(event) {\n        var value = event.currentTarget.value;\n        if (props.ignoreCompositionEvents && (0,_utils__WEBPACK_IMPORTED_MODULE_6__.getNativeEvent)(event).isComposing) {\n          setters.setQuery(value);\n          return;\n        }\n        (0,_onInput__WEBPACK_IMPORTED_MODULE_3__.onInput)(_objectSpread({\n          event: event,\n          props: props,\n          query: value.slice(0, maxLength),\n          refresh: refresh,\n          store: store\n        }, setters));\n      },\n      onCompositionEnd: function onCompositionEnd(event) {\n        (0,_onInput__WEBPACK_IMPORTED_MODULE_3__.onInput)(_objectSpread({\n          event: event,\n          props: props,\n          query: event.currentTarget.value.slice(0, maxLength),\n          refresh: refresh,\n          store: store\n        }, setters));\n      },\n      onKeyDown: function onKeyDown(event) {\n        if ((0,_utils__WEBPACK_IMPORTED_MODULE_6__.getNativeEvent)(event).isComposing) {\n          return;\n        }\n        (0,_onKeyDown__WEBPACK_IMPORTED_MODULE_7__.onKeyDown)(_objectSpread({\n          event: event,\n          props: props,\n          refresh: refresh,\n          store: store\n        }, setters));\n      },\n      onFocus: onFocus,\n      // We don't rely on the `blur` event.\n      // See explanation in `onTouchStart`/`onMouseDown`.\n      // @MAJOR See if we need to keep this handler.\n      onBlur: _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_8__.noop,\n      onClick: function onClick(event) {\n        // When the panel is closed and you click on the input while\n        // the input is focused, the `onFocus` event is not triggered\n        // (default browser behavior).\n        // In an autocomplete context, it makes sense to open the panel in this\n        // case.\n        // We mimic this event by catching the `onClick` event which\n        // triggers the `onFocus` for the panel to open.\n        if (providedProps.inputElement === props.environment.document.activeElement && !store.getState().isOpen) {\n          onFocus(event);\n        }\n      }\n    }, rest);\n  };\n  var getLabelProps = function getLabelProps(rest) {\n    return _objectSpread({\n      htmlFor: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'input'),\n      id: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'label')\n    }, rest);\n  };\n  var getListProps = function getListProps(providedProps) {\n    var _ref5 = providedProps || {},\n      source = _ref5.source,\n      rest = _objectWithoutProperties(_ref5, _excluded5);\n    return _objectSpread({\n      role: 'listbox',\n      'aria-labelledby': (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'label'),\n      id: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, 'list', source)\n    }, rest);\n  };\n  var getPanelProps = function getPanelProps(rest) {\n    return _objectSpread({\n      onMouseDown: function onMouseDown(event) {\n        // Prevents the `activeElement` from being changed to the panel so\n        // that the blur event is not triggered, otherwise it closes the\n        // panel.\n        event.preventDefault();\n      },\n      onMouseLeave: function onMouseLeave() {\n        store.dispatch('mouseleave', null);\n      }\n    }, rest);\n  };\n  var getItemProps = function getItemProps(providedProps) {\n    var item = providedProps.item,\n      source = providedProps.source,\n      rest = _objectWithoutProperties(providedProps, _excluded6);\n    return _objectSpread({\n      id: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, \"item-\".concat(item.__autocomplete_id), source),\n      role: 'option',\n      'aria-selected': store.getState().activeItemId === item.__autocomplete_id,\n      onMouseMove: function onMouseMove(event) {\n        if (item.__autocomplete_id === store.getState().activeItemId) {\n          return;\n        }\n        store.dispatch('mousemove', item.__autocomplete_id);\n        var activeItem = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.getActiveItem)(store.getState());\n        if (store.getState().activeItemId !== null && activeItem) {\n          var _item = activeItem.item,\n            itemInputValue = activeItem.itemInputValue,\n            itemUrl = activeItem.itemUrl,\n            _source = activeItem.source;\n          _source.onActive(_objectSpread({\n            event: event,\n            item: _item,\n            itemInputValue: itemInputValue,\n            itemUrl: itemUrl,\n            refresh: refresh,\n            source: _source,\n            state: store.getState()\n          }, setters));\n        }\n      },\n      onMouseDown: function onMouseDown(event) {\n        // Prevents the `activeElement` from being changed to the item so it\n        // can remain with the current `activeElement`.\n        event.preventDefault();\n      },\n      onClick: function onClick(event) {\n        var itemInputValue = source.getItemInputValue({\n          item: item,\n          state: store.getState()\n        });\n        var itemUrl = source.getItemUrl({\n          item: item,\n          state: store.getState()\n        });\n\n        // If `getItemUrl` is provided, it means that the suggestion\n        // is a link, not plain text that aims at updating the query.\n        // We can therefore skip the state change because it will update\n        // the `activeItemId`, resulting in a UI flash, especially\n        // noticeable on mobile.\n        var runPreCommand = itemUrl ? Promise.resolve() : (0,_onInput__WEBPACK_IMPORTED_MODULE_3__.onInput)(_objectSpread({\n          event: event,\n          nextState: {\n            isOpen: false\n          },\n          props: props,\n          query: itemInputValue,\n          refresh: refresh,\n          store: store\n        }, setters));\n        runPreCommand.then(function () {\n          source.onSelect(_objectSpread({\n            event: event,\n            item: item,\n            itemInputValue: itemInputValue,\n            itemUrl: itemUrl,\n            refresh: refresh,\n            source: source,\n            state: store.getState()\n          }, setters));\n        });\n      }\n    }, rest);\n  };\n  return {\n    getEnvironmentProps: getEnvironmentProps,\n    getRootProps: getRootProps,\n    getFormProps: getFormProps,\n    getLabelProps: getLabelProps,\n    getInputProps: getInputProps,\n    getPanelProps: getPanelProps,\n    getListProps: getListProps,\n    getItemProps: getItemProps\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/getPropGetters.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/metadata.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMetadata: function() { return /* binding */ getMetadata; },\n/* harmony export */   injectMetadata: function() { return /* binding */ injectMetadata; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/userAgents.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\nfunction getMetadata(_ref) {\n  var _, _options$__autocomple, _options$__autocomple2, _options$__autocomple3;\n  var plugins = _ref.plugins,\n    options = _ref.options;\n  var optionsKey = (_ = (((_options$__autocomple = options.__autocomplete_metadata) === null || _options$__autocomple === void 0 ? void 0 : _options$__autocomple.userAgents) || [])[0]) === null || _ === void 0 ? void 0 : _.segment;\n  var extraOptions = optionsKey ? _defineProperty({}, optionsKey, Object.keys(((_options$__autocomple2 = options.__autocomplete_metadata) === null || _options$__autocomple2 === void 0 ? void 0 : _options$__autocomple2.options) || {})) : {};\n  return {\n    plugins: plugins.map(function (plugin) {\n      return {\n        name: plugin.name,\n        options: Object.keys(plugin.__autocomplete_pluginOptions || [])\n      };\n    }),\n    options: _objectSpread({\n      \'autocomplete-core\': Object.keys(options)\n    }, extraOptions),\n    ua: _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.userAgents.concat(((_options$__autocomple3 = options.__autocomplete_metadata) === null || _options$__autocomple3 === void 0 ? void 0 : _options$__autocomple3.userAgents) || [])\n  };\n}\nfunction injectMetadata(_ref3) {\n  var _environment$navigato, _environment$navigato2;\n  var metadata = _ref3.metadata,\n    environment = _ref3.environment;\n  var isMetadataEnabled = (_environment$navigato = environment.navigator) === null || _environment$navigato === void 0 ? void 0 : (_environment$navigato2 = _environment$navigato.userAgent) === null || _environment$navigato2 === void 0 ? void 0 : _environment$navigato2.includes(\'Algolia Crawler\');\n  if (isMetadataEnabled) {\n    var metadataContainer = environment.document.createElement(\'meta\');\n    var headRef = environment.document.querySelector(\'head\');\n    metadataContainer.name = \'algolia:metadata\';\n    setTimeout(function () {\n      metadataContainer.content = JSON.stringify(metadata);\n      headRef.appendChild(metadataContainer);\n    }, 0);\n  }\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/metadata.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/onInput.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onInput: function() { return /* binding */ onInput; }\n/* harmony export */ });\n/* harmony import */ var _reshape__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./reshape */ "./node_modules/@algolia/autocomplete-core/dist/esm/reshape.js");\n/* harmony import */ var _resolve__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolve */ "./node_modules/@algolia/autocomplete-core/dist/esm/resolve.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/createConcurrentSafePromise.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromise.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nvar _excluded = ["event", "nextState", "props", "query", "refresh", "store"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\n\nvar lastStalledId = null;\nvar runConcurrentSafePromise = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.createConcurrentSafePromise)();\nfunction onInput(_ref) {\n  var event = _ref.event,\n    _ref$nextState = _ref.nextState,\n    nextState = _ref$nextState === void 0 ? {} : _ref$nextState,\n    props = _ref.props,\n    query = _ref.query,\n    refresh = _ref.refresh,\n    store = _ref.store,\n    setters = _objectWithoutProperties(_ref, _excluded);\n  if (lastStalledId) {\n    props.environment.clearTimeout(lastStalledId);\n  }\n  var setCollections = setters.setCollections,\n    setIsOpen = setters.setIsOpen,\n    setQuery = setters.setQuery,\n    setActiveItemId = setters.setActiveItemId,\n    setStatus = setters.setStatus,\n    setContext = setters.setContext;\n  setQuery(query);\n  setActiveItemId(props.defaultActiveItemId);\n  if (!query && props.openOnFocus === false) {\n    var _nextState$isOpen;\n    var collections = store.getState().collections.map(function (collection) {\n      return _objectSpread(_objectSpread({}, collection), {}, {\n        items: []\n      });\n    });\n    setStatus(\'idle\');\n    setCollections(collections);\n    setIsOpen((_nextState$isOpen = nextState.isOpen) !== null && _nextState$isOpen !== void 0 ? _nextState$isOpen : props.shouldPanelOpen({\n      state: store.getState()\n    }));\n\n    // We make sure to update the latest resolved value of the tracked\n    // promises to keep late resolving promises from "cancelling" the state\n    // updates performed in this code path.\n    // We chain with a void promise to respect `onInput`\'s expected return type.\n    var _request = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.cancelable)(runConcurrentSafePromise(collections).then(function () {\n      return Promise.resolve();\n    }));\n    return store.pendingRequests.add(_request);\n  }\n  setStatus(\'loading\');\n  lastStalledId = props.environment.setTimeout(function () {\n    setStatus(\'stalled\');\n  }, props.stallThreshold);\n\n  // We track the entire promise chain triggered by `onInput` before mutating\n  // the Autocomplete state to make sure that any state manipulation is based on\n  // fresh data regardless of when promises individually resolve.\n  // We don\'t track nested promises and only rely on the full chain resolution,\n  // meaning we should only ever manipulate the state once this concurrent-safe\n  // promise is resolved.\n  var request = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.cancelable)(runConcurrentSafePromise(props.getSources(_objectSpread({\n    query: query,\n    refresh: refresh,\n    state: store.getState()\n  }, setters)).then(function (sources) {\n    return Promise.all(sources.map(function (source) {\n      return Promise.resolve(source.getItems(_objectSpread({\n        query: query,\n        refresh: refresh,\n        state: store.getState()\n      }, setters))).then(function (itemsOrDescription) {\n        return (0,_resolve__WEBPACK_IMPORTED_MODULE_2__.preResolve)(itemsOrDescription, source.sourceId, store.getState());\n      });\n    })).then(_resolve__WEBPACK_IMPORTED_MODULE_2__.resolve).then(function (responses) {\n      var __automaticInsights = responses.some(function (_ref2) {\n        var items = _ref2.items;\n        return isSearchResponseWithAutomaticInsightsFlag(items);\n      });\n\n      // No need to pollute the context if `__automaticInsights=false`\n      if (__automaticInsights) {\n        var _store$getState$conte;\n        setContext({\n          algoliaInsightsPlugin: _objectSpread(_objectSpread({}, ((_store$getState$conte = store.getState().context) === null || _store$getState$conte === void 0 ? void 0 : _store$getState$conte.algoliaInsightsPlugin) || {}), {}, {\n            __automaticInsights: __automaticInsights\n          })\n        });\n      }\n      return (0,_resolve__WEBPACK_IMPORTED_MODULE_2__.postResolve)(responses, sources, store);\n    }).then(function (collections) {\n      return (0,_reshape__WEBPACK_IMPORTED_MODULE_3__.reshape)({\n        collections: collections,\n        props: props,\n        state: store.getState()\n      });\n    });\n  }))).then(function (collections) {\n    var _nextState$isOpen2;\n    // Parameters passed to `onInput` could be stale when the following code\n    // executes, because `onInput` calls may not resolve in order.\n    // If it becomes a problem we\'ll need to save the last passed parameters.\n    // See: https://codesandbox.io/s/agitated-cookies-y290z\n\n    setStatus(\'idle\');\n    setCollections(collections);\n    var isPanelOpen = props.shouldPanelOpen({\n      state: store.getState()\n    });\n    setIsOpen((_nextState$isOpen2 = nextState.isOpen) !== null && _nextState$isOpen2 !== void 0 ? _nextState$isOpen2 : props.openOnFocus && !query && isPanelOpen || isPanelOpen);\n    var highlightedItem = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.getActiveItem)(store.getState());\n    if (store.getState().activeItemId !== null && highlightedItem) {\n      var item = highlightedItem.item,\n        itemInputValue = highlightedItem.itemInputValue,\n        itemUrl = highlightedItem.itemUrl,\n        source = highlightedItem.source;\n      source.onActive(_objectSpread({\n        event: event,\n        item: item,\n        itemInputValue: itemInputValue,\n        itemUrl: itemUrl,\n        refresh: refresh,\n        source: source,\n        state: store.getState()\n      }, setters));\n    }\n  }).finally(function () {\n    setStatus(\'idle\');\n    if (lastStalledId) {\n      props.environment.clearTimeout(lastStalledId);\n    }\n  });\n  return store.pendingRequests.add(request);\n}\nfunction isSearchResponseWithAutomaticInsightsFlag(items) {\n  return !Array.isArray(items) && Boolean(items === null || items === void 0 ? void 0 : items._automaticInsights);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/onInput.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/onKeyDown.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onKeyDown: function() { return /* binding */ onKeyDown; }\n/* harmony export */ });\n/* harmony import */ var _onInput__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./onInput */ "./node_modules/@algolia/autocomplete-core/dist/esm/onInput.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/getAutocompleteElementId.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/getPluginSubmitPromise.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nvar _excluded = ["event", "props", "refresh", "store"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\nfunction onKeyDown(_ref) {\n  var event = _ref.event,\n    props = _ref.props,\n    refresh = _ref.refresh,\n    store = _ref.store,\n    setters = _objectWithoutProperties(_ref, _excluded);\n  if (event.key === \'ArrowUp\' || event.key === \'ArrowDown\') {\n    // eslint-disable-next-line no-inner-declarations\n    var triggerScrollIntoView = function triggerScrollIntoView() {\n      var highlightedItem = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getActiveItem)(store.getState());\n      var nodeItem = props.environment.document.getElementById((0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAutocompleteElementId)(props.id, "item-".concat(store.getState().activeItemId), highlightedItem === null || highlightedItem === void 0 ? void 0 : highlightedItem.source));\n      if (nodeItem) {\n        if (nodeItem.scrollIntoViewIfNeeded) {\n          nodeItem.scrollIntoViewIfNeeded(false);\n        } else {\n          nodeItem.scrollIntoView(false);\n        }\n      }\n    }; // eslint-disable-next-line no-inner-declarations\n    var triggerOnActive = function triggerOnActive() {\n      var highlightedItem = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getActiveItem)(store.getState());\n      if (store.getState().activeItemId !== null && highlightedItem) {\n        var item = highlightedItem.item,\n          itemInputValue = highlightedItem.itemInputValue,\n          itemUrl = highlightedItem.itemUrl,\n          source = highlightedItem.source;\n        source.onActive(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n      }\n    }; // Default browser behavior changes the caret placement on ArrowUp and\n    // ArrowDown.\n    event.preventDefault();\n\n    // When re-opening the panel, we need to split the logic to keep the actions\n    // synchronized as `onInput` returns a promise.\n    if (store.getState().isOpen === false && (props.openOnFocus || Boolean(store.getState().query))) {\n      (0,_onInput__WEBPACK_IMPORTED_MODULE_2__.onInput)(_objectSpread({\n        event: event,\n        props: props,\n        query: store.getState().query,\n        refresh: refresh,\n        store: store\n      }, setters)).then(function () {\n        store.dispatch(event.key, {\n          nextActiveItemId: props.defaultActiveItemId\n        });\n        triggerOnActive();\n        // Since we rely on the DOM, we need to wait for all the micro tasks to\n        // finish (which include re-opening the panel) to make sure all the\n        // elements are available.\n        setTimeout(triggerScrollIntoView, 0);\n      });\n    } else {\n      store.dispatch(event.key, {});\n      triggerOnActive();\n      triggerScrollIntoView();\n    }\n  } else if (event.key === \'Escape\') {\n    // This prevents the default browser behavior on `input[type="search"]`\n    // from removing the query right away because we first want to close the\n    // panel.\n    event.preventDefault();\n    store.dispatch(event.key, null);\n\n    // Hitting the `Escape` key signals the end of a user interaction with the\n    // autocomplete. At this point, we should ignore any requests that are still\n    // pending and could reopen the panel once they resolve, because that would\n    // result in an unsolicited UI behavior.\n    store.pendingRequests.cancelAll();\n  } else if (event.key === \'Tab\') {\n    store.dispatch(\'blur\', null);\n\n    // Hitting the `Tab` key signals the end of a user interaction with the\n    // autocomplete. At this point, we should ignore any requests that are still\n    // pending and could reopen the panel once they resolve, because that would\n    // result in an unsolicited UI behavior.\n    store.pendingRequests.cancelAll();\n  } else if (event.key === \'Enter\') {\n    // No active item, so we let the browser handle the native `onSubmit` form\n    // event.\n    if (store.getState().activeItemId === null || store.getState().collections.every(function (collection) {\n      return collection.items.length === 0;\n    })) {\n      var waitForSubmit = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.getPluginSubmitPromise)(props.plugins, store.pendingRequests);\n      if (waitForSubmit !== undefined) {\n        waitForSubmit.then(store.pendingRequests.cancelAll); // Cancel the rest if timeout number is provided\n      } else if (!props.debug) {\n        // If requests are still pending when the panel closes, they could reopen\n        // the panel once they resolve.\n        // We want to prevent any subsequent query from reopening the panel\n        // because it would result in an unsolicited UI behavior.\n        store.pendingRequests.cancelAll();\n      }\n      return;\n    }\n\n    // This prevents the `onSubmit` event to be sent because an item is\n    // highlighted.\n    event.preventDefault();\n    var _ref2 = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getActiveItem)(store.getState()),\n      item = _ref2.item,\n      itemInputValue = _ref2.itemInputValue,\n      itemUrl = _ref2.itemUrl,\n      source = _ref2.source;\n    if (event.metaKey || event.ctrlKey) {\n      if (itemUrl !== undefined) {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n        props.navigator.navigateNewTab({\n          itemUrl: itemUrl,\n          item: item,\n          state: store.getState()\n        });\n      }\n    } else if (event.shiftKey) {\n      if (itemUrl !== undefined) {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n        props.navigator.navigateNewWindow({\n          itemUrl: itemUrl,\n          item: item,\n          state: store.getState()\n        });\n      }\n    } else if (event.altKey) {\n      // Keep native browser behavior\n    } else {\n      if (itemUrl !== undefined) {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n        props.navigator.navigate({\n          itemUrl: itemUrl,\n          item: item,\n          state: store.getState()\n        });\n        return;\n      }\n      (0,_onInput__WEBPACK_IMPORTED_MODULE_2__.onInput)(_objectSpread({\n        event: event,\n        nextState: {\n          isOpen: false\n        },\n        props: props,\n        query: itemInputValue,\n        refresh: refresh,\n        store: store\n      }, setters)).then(function () {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n      });\n    }\n  }\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/onKeyDown.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/reshape.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reshape: function() { return /* binding */ reshape; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\nfunction reshape(_ref) {\n  var collections = _ref.collections,\n    props = _ref.props,\n    state = _ref.state;\n  // Sources are grouped by `sourceId` to conveniently pick them via destructuring.\n  // Example: `const { recentSearchesPlugin } = sourcesBySourceId`\n  var originalSourcesBySourceId = collections.reduce(function (acc, collection) {\n    return _objectSpread(_objectSpread({}, acc), {}, _defineProperty({}, collection.source.sourceId, _objectSpread(_objectSpread({}, collection.source), {}, {\n      getItems: function getItems() {\n        // We provide the resolved items from the collection to the `reshape` prop.\n        return (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.flatten)(collection.items);\n      }\n    })));\n  }, {});\n  var _props$plugins$reduce = props.plugins.reduce(function (acc, plugin) {\n      if (plugin.reshape) {\n        return plugin.reshape(acc);\n      }\n      return acc;\n    }, {\n      sourcesBySourceId: originalSourcesBySourceId,\n      state: state\n    }),\n    sourcesBySourceId = _props$plugins$reduce.sourcesBySourceId;\n  var reshapeSources = props.reshape({\n    sourcesBySourceId: sourcesBySourceId,\n    sources: Object.values(sourcesBySourceId),\n    state: state\n  });\n\n  // We reconstruct the collections with the items modified by the `reshape` prop.\n  return (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.flatten)(reshapeSources).filter(Boolean).map(function (source) {\n    return {\n      source: source,\n      items: source.getItems()\n    };\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/reshape.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/resolve.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postResolve: function() { return /* binding */ postResolve; },\n/* harmony export */   preResolve: function() { return /* binding */ preResolve; },\n/* harmony export */   resolve: function() { return /* binding */ resolve; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/decycle.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-core/dist/esm/utils/mapToAlgoliaResponse.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\n\n\nfunction isDescription(item) {\n  return Boolean(item.execute);\n}\nfunction isRequesterDescription(description) {\n  return Boolean(description === null || description === void 0 ? void 0 : description.execute);\n}\nfunction preResolve(itemsOrDescription, sourceId, state) {\n  if (isRequesterDescription(itemsOrDescription)) {\n    var contextParameters = itemsOrDescription.requesterId === \'algolia\' ? Object.assign.apply(Object, [{}].concat(_toConsumableArray(Object.keys(state.context).map(function (key) {\n      var _state$context$key;\n      return (_state$context$key = state.context[key]) === null || _state$context$key === void 0 ? void 0 : _state$context$key.__algoliaSearchParameters;\n    })))) : {};\n    return _objectSpread(_objectSpread({}, itemsOrDescription), {}, {\n      requests: itemsOrDescription.queries.map(function (query) {\n        return {\n          query: itemsOrDescription.requesterId === \'algolia\' ? _objectSpread(_objectSpread({}, query), {}, {\n            params: _objectSpread(_objectSpread({}, contextParameters), query.params)\n          }) : query,\n          sourceId: sourceId,\n          transformResponse: itemsOrDescription.transformResponse\n        };\n      })\n    });\n  }\n  return {\n    items: itemsOrDescription,\n    sourceId: sourceId\n  };\n}\nfunction resolve(items) {\n  var packed = items.reduce(function (acc, current) {\n    if (!isDescription(current)) {\n      acc.push(current);\n      return acc;\n    }\n    var searchClient = current.searchClient,\n      execute = current.execute,\n      requesterId = current.requesterId,\n      requests = current.requests;\n    var container = acc.find(function (item) {\n      return isDescription(current) && isDescription(item) && item.searchClient === searchClient && Boolean(requesterId) && item.requesterId === requesterId;\n    });\n    if (container) {\n      var _container$items;\n      (_container$items = container.items).push.apply(_container$items, _toConsumableArray(requests));\n    } else {\n      var request = {\n        execute: execute,\n        requesterId: requesterId,\n        items: requests,\n        searchClient: searchClient\n      };\n      acc.push(request);\n    }\n    return acc;\n  }, []);\n  var values = packed.map(function (maybeDescription) {\n    if (!isDescription(maybeDescription)) {\n      return Promise.resolve(maybeDescription);\n    }\n    var _ref = maybeDescription,\n      execute = _ref.execute,\n      items = _ref.items,\n      searchClient = _ref.searchClient;\n    return execute({\n      searchClient: searchClient,\n      requests: items\n    });\n  });\n  return Promise.all(values).then(function (responses) {\n    return (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.flatten)(responses);\n  });\n}\nfunction postResolve(responses, sources, store) {\n  return sources.map(function (source) {\n    var matches = responses.filter(function (response) {\n      return response.sourceId === source.sourceId;\n    });\n    var results = matches.map(function (_ref2) {\n      var items = _ref2.items;\n      return items;\n    });\n    var transform = matches[0].transformResponse;\n    var items = transform ? transform((0,_utils__WEBPACK_IMPORTED_MODULE_1__.mapToAlgoliaResponse)(results)) : results;\n    source.onResolve({\n      source: source,\n      results: results,\n      items: items,\n      state: store.getState()\n    });\n    (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__.invariant)(Array.isArray(items), function () {\n      return "The `getItems` function from source \\"".concat(source.sourceId, "\\" must return an array of items but returned type ").concat(JSON.stringify(_typeof(items)), ":\\n\\n").concat(JSON.stringify((0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.decycle)(items), null, 2), ".\\n\\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems");\n    });\n    (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__.invariant)(items.every(Boolean), "The `getItems` function from source \\"".concat(source.sourceId, "\\" must return an array of items but returned ").concat(JSON.stringify(undefined), ".\\n\\nDid you forget to return items?\\n\\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems"));\n    return {\n      source: source,\n      items: items\n    };\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/resolve.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/stateReducer.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stateReducer: function() { return /* binding */ stateReducer; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @algolia/autocomplete-shared */ \"./node_modules/@algolia/autocomplete-shared/dist/esm/getItemsCount.js\");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @algolia/autocomplete-shared */ \"./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js\");\n/* harmony import */ var _getCompletion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getCompletion */ \"./node_modules/@algolia/autocomplete-core/dist/esm/getCompletion.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNextActiveItemId.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\n\n\nvar stateReducer = function stateReducer(state, action) {\n  switch (action.type) {\n    case 'setActiveItemId':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.payload\n        });\n      }\n    case 'setQuery':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          query: action.payload,\n          completion: null\n        });\n      }\n    case 'setCollections':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          collections: action.payload\n        });\n      }\n    case 'setIsOpen':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isOpen: action.payload\n        });\n      }\n    case 'setStatus':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          status: action.payload\n        });\n      }\n    case 'setContext':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          context: _objectSpread(_objectSpread({}, state.context), action.payload)\n        });\n      }\n    case 'ArrowDown':\n      {\n        var nextState = _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.payload.hasOwnProperty('nextActiveItemId') ? action.payload.nextActiveItemId : (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getNextActiveItemId)(1, state.activeItemId, (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__.getItemsCount)(state), action.props.defaultActiveItemId)\n        });\n        return _objectSpread(_objectSpread({}, nextState), {}, {\n          completion: (0,_getCompletion__WEBPACK_IMPORTED_MODULE_2__.getCompletion)({\n            state: nextState\n          })\n        });\n      }\n    case 'ArrowUp':\n      {\n        var _nextState = _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getNextActiveItemId)(-1, state.activeItemId, (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__.getItemsCount)(state), action.props.defaultActiveItemId)\n        });\n        return _objectSpread(_objectSpread({}, _nextState), {}, {\n          completion: (0,_getCompletion__WEBPACK_IMPORTED_MODULE_2__.getCompletion)({\n            state: _nextState\n          })\n        });\n      }\n    case 'Escape':\n      {\n        if (state.isOpen) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            activeItemId: null,\n            isOpen: false,\n            completion: null\n          });\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: null,\n          query: '',\n          status: 'idle',\n          collections: []\n        });\n      }\n    case 'submit':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: null,\n          isOpen: false,\n          status: 'idle'\n        });\n      }\n    case 'reset':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId:\n          // Since we open the panel on reset when openOnFocus=true\n          // we need to restore the highlighted index to the defaultActiveItemId. (DocSearch use-case)\n\n          // Since we close the panel when openOnFocus=false\n          // we lose track of the highlighted index. (Query-suggestions use-case)\n          action.props.openOnFocus === true ? action.props.defaultActiveItemId : null,\n          status: 'idle',\n          completion: null,\n          query: ''\n        });\n      }\n    case 'focus':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.props.defaultActiveItemId,\n          isOpen: (action.props.openOnFocus || Boolean(state.query)) && action.props.shouldPanelOpen({\n            state: state\n          })\n        });\n      }\n    case 'blur':\n      {\n        if (action.props.debug) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isOpen: false,\n          activeItemId: null\n        });\n      }\n    case 'mousemove':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.payload\n        });\n      }\n    case 'mouseleave':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.props.defaultActiveItemId\n        });\n      }\n    default:\n      (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.invariant)(false, \"The reducer action \".concat(JSON.stringify(action.type), \" is not supported.\"));\n      return state;\n  }\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/stateReducer.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromise.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelable: function() { return /* binding */ cancelable; },\n/* harmony export */   createCancelablePromise: function() { return /* binding */ createCancelablePromise; }\n/* harmony export */ });\nfunction createInternalCancelablePromise(promise, initialState) {\n  var state = initialState;\n  return {\n    then: function then(onfulfilled, onrejected) {\n      return createInternalCancelablePromise(promise.then(createCallback(onfulfilled, state, promise), createCallback(onrejected, state, promise)), state);\n    },\n    catch: function _catch(onrejected) {\n      return createInternalCancelablePromise(promise.catch(createCallback(onrejected, state, promise)), state);\n    },\n    finally: function _finally(onfinally) {\n      if (onfinally) {\n        state.onCancelList.push(onfinally);\n      }\n      return createInternalCancelablePromise(promise.finally(createCallback(onfinally && function () {\n        state.onCancelList = [];\n        return onfinally();\n      }, state, promise)), state);\n    },\n    cancel: function cancel() {\n      state.isCanceled = true;\n      var callbacks = state.onCancelList;\n      state.onCancelList = [];\n      callbacks.forEach(function (callback) {\n        callback();\n      });\n    },\n    isCanceled: function isCanceled() {\n      return state.isCanceled === true;\n    }\n  };\n}\nfunction createCancelablePromise(executor) {\n  return createInternalCancelablePromise(new Promise(function (resolve, reject) {\n    return executor(resolve, reject);\n  }), {\n    isCanceled: false,\n    onCancelList: []\n  });\n}\ncreateCancelablePromise.resolve = function (value) {\n  return cancelable(Promise.resolve(value));\n};\ncreateCancelablePromise.reject = function (reason) {\n  return cancelable(Promise.reject(reason));\n};\nfunction cancelable(promise) {\n  return createInternalCancelablePromise(promise, {\n    isCanceled: false,\n    onCancelList: []\n  });\n}\nfunction createCallback(onResult, state, fallback) {\n  if (!onResult) {\n    return fallback;\n  }\n  return function callback(arg) {\n    if (state.isCanceled) {\n      return arg;\n    }\n    return onResult(arg);\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromise.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromiseList.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCancelablePromiseList: function() { return /* binding */ createCancelablePromiseList; }\n/* harmony export */ });\n// Ensures multiple callers sync to the same promise.\nvar _hasWaitPromiseResolved = true;\nvar _waitPromise;\nfunction createCancelablePromiseList() {\n  var list = [];\n  return {\n    add: function add(cancelablePromise) {\n      list.push(cancelablePromise);\n      return cancelablePromise.finally(function () {\n        list = list.filter(function (item) {\n          return item !== cancelablePromise;\n        });\n      });\n    },\n    cancelAll: function cancelAll() {\n      list.forEach(function (promise) {\n        return promise.cancel();\n      });\n    },\n    isEmpty: function isEmpty() {\n      return list.length === 0;\n    },\n    wait: function wait(timeout) {\n      // Reuse promise if already exists. Keeps multiple callers subscribed to the same promise.\n      if (!_hasWaitPromiseResolved) {\n        return _waitPromise;\n      }\n\n      // Creates a promise which either resolves after all pending requests complete\n      // or the timeout is reached (if provided). Whichever comes first.\n      _hasWaitPromiseResolved = false;\n      _waitPromise = !timeout ? Promise.all(list) : Promise.race([Promise.all(list), new Promise(function (resolve) {\n        return setTimeout(resolve, timeout);\n      })]);\n      return _waitPromise.then(function () {\n        _hasWaitPromiseResolved = true;\n      });\n    }\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromiseList.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/createConcurrentSafePromise.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConcurrentSafePromise: function() { return /* binding */ createConcurrentSafePromise; }\n/* harmony export */ });\n/**\n * Creates a runner that executes promises in a concurrent-safe way.\n *\n * This is useful to prevent older promises to resolve after a newer promise,\n * otherwise resulting in stale resolved values.\n */\nfunction createConcurrentSafePromise() {\n  var basePromiseId = -1;\n  var latestResolvedId = -1;\n  var latestResolvedValue = undefined;\n  return function runConcurrentSafePromise(promise) {\n    basePromiseId++;\n    var currentPromiseId = basePromiseId;\n    return Promise.resolve(promise).then(function (x) {\n      // The promise might take too long to resolve and get outdated. This would\n      // result in resolving stale values.\n      // When this happens, we ignore the promise value and return the one\n      // coming from the latest resolved value.\n      //\n      // +----------------------------------+\n      // |        100ms                     |\n      // | run(1) +---\x3e  R1                 |\n      // |        300ms                     |\n      // | run(2) +-------------\x3e R2 (SKIP) |\n      // |        200ms                     |\n      // | run(3) +--------\x3e R3             |\n      // +----------------------------------+\n      if (latestResolvedValue && currentPromiseId < latestResolvedId) {\n        return latestResolvedValue;\n      }\n      latestResolvedId = currentPromiseId;\n      latestResolvedValue = x;\n      return x;\n    });\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/createConcurrentSafePromise.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getActiveItem: function() { return /* binding */ getActiveItem; }\n/* harmony export */ });\n// We don't have access to the autocomplete source when we call `onKeyDown`\n// or `onClick` because those are native browser events.\n// However, we can get the source from the suggestion index.\nfunction getCollectionFromActiveItemId(state) {\n  // Given 3 sources with respectively 1, 2 and 3 suggestions: [1, 2, 3]\n  // We want to get the accumulated counts:\n  // [1, 1 + 2, 1 + 2 + 3] = [1, 3, 3 + 3] = [1, 3, 6]\n  var accumulatedCollectionsCount = state.collections.map(function (collections) {\n    return collections.items.length;\n  }).reduce(function (acc, collectionsCount, index) {\n    var previousValue = acc[index - 1] || 0;\n    var nextValue = previousValue + collectionsCount;\n    acc.push(nextValue);\n    return acc;\n  }, []);\n\n  // Based on the accumulated counts, we can infer the index of the suggestion.\n  var collectionIndex = accumulatedCollectionsCount.reduce(function (acc, current) {\n    if (current <= state.activeItemId) {\n      return acc + 1;\n    }\n    return acc;\n  }, 0);\n  return state.collections[collectionIndex];\n}\n\n/**\n * Gets the highlighted index relative to a suggestion object (not the absolute\n * highlighted index).\n *\n * Example:\n *  [['a', 'b'], ['c', 'd', 'e'], ['f']]\n *                      ↑\n *         (absolute: 3, relative: 1)\n */\nfunction getRelativeActiveItemId(_ref) {\n  var state = _ref.state,\n    collection = _ref.collection;\n  var isOffsetFound = false;\n  var counter = 0;\n  var previousItemsOffset = 0;\n  while (isOffsetFound === false) {\n    var currentCollection = state.collections[counter];\n    if (currentCollection === collection) {\n      isOffsetFound = true;\n      break;\n    }\n    previousItemsOffset += currentCollection.items.length;\n    counter++;\n  }\n  return state.activeItemId - previousItemsOffset;\n}\nfunction getActiveItem(state) {\n  var collection = getCollectionFromActiveItemId(state);\n  if (!collection) {\n    return null;\n  }\n  var item = collection.items[getRelativeActiveItemId({\n    state: state,\n    collection: collection\n  })];\n  var source = collection.source;\n  var itemInputValue = source.getItemInputValue({\n    item: item,\n    state: state\n  });\n  var itemUrl = source.getItemUrl({\n    item: item,\n    state: state\n  });\n  return {\n    item: item,\n    itemInputValue: itemInputValue,\n    itemUrl: itemUrl,\n    source: source\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getAutocompleteElementId.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAutocompleteElementId: function() { return /* binding */ getAutocompleteElementId; }\n/* harmony export */ });\n/**\n * Returns a full element id for an autocomplete element.\n *\n * @param autocompleteInstanceId The id of the autocomplete instance\n * @param elementId The specific element id\n * @param source The source of the element, when it needs to be scoped\n */\nfunction getAutocompleteElementId(autocompleteInstanceId, elementId, source) {\n  return [autocompleteInstanceId, source === null || source === void 0 ? void 0 : source.sourceId, elementId].filter(Boolean).join('-').replace(/\\s/g, '');\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/getAutocompleteElementId.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNativeEvent.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNativeEvent: function() { return /* binding */ getNativeEvent; }\n/* harmony export */ });\nfunction getNativeEvent(event) {\n  return event.nativeEvent || event;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNativeEvent.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNextActiveItemId.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextActiveItemId: function() { return /* binding */ getNextActiveItemId; }\n/* harmony export */ });\n/**\n * Returns the next active item ID from the current state.\n *\n * We allow circular keyboard navigation from the base index.\n * The base index can either be `null` (nothing is highlighted) or `0`\n * (the first item is highlighted).\n * The base index is allowed to get assigned `null` only if\n * `props.defaultActiveItemId` is `null`. This pattern allows to "stop"\n * by the actual query before navigating to other suggestions as seen on\n * Google or Amazon.\n *\n * @param moveAmount The offset to increment (or decrement) the last index\n * @param baseIndex The current index to compute the next index from\n * @param itemCount The number of items\n * @param defaultActiveItemId The default active index to fallback to\n */\nfunction getNextActiveItemId(moveAmount, baseIndex, itemCount, defaultActiveItemId) {\n  if (!itemCount) {\n    return null;\n  }\n  if (moveAmount < 0 && (baseIndex === null || defaultActiveItemId !== null && baseIndex === 0)) {\n    return itemCount + moveAmount;\n  }\n  var numericIndex = (baseIndex === null ? -1 : baseIndex) + moveAmount;\n  if (numericIndex <= -1 || numericIndex >= itemCount) {\n    return defaultActiveItemId === null ? null : 0;\n  }\n  return numericIndex;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNextActiveItemId.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNormalizedSources.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNormalizedSources: function() { return /* binding */ getNormalizedSources; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/decycle.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/noop.js");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\n\nfunction getNormalizedSources(getSources, params) {\n  var seenSourceIds = [];\n  return Promise.resolve(getSources(params)).then(function (sources) {\n    (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.invariant)(Array.isArray(sources), function () {\n      return "The `getSources` function must return an array of sources but returned type ".concat(JSON.stringify(_typeof(sources)), ":\\n\\n").concat(JSON.stringify((0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__.decycle)(sources), null, 2));\n    });\n    return Promise.all(sources\n    // We allow `undefined` and `false` sources to allow users to use\n    // `Boolean(query) && source` (=> `false`).\n    // We need to remove these values at this point.\n    .filter(function (maybeSource) {\n      return Boolean(maybeSource);\n    }).map(function (source) {\n      (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.sourceId === \'string\', \'A source must provide a `sourceId` string.\');\n      if (seenSourceIds.includes(source.sourceId)) {\n        throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(source.sourceId), " is not unique."));\n      }\n      seenSourceIds.push(source.sourceId);\n      var defaultSource = {\n        getItemInputValue: function getItemInputValue(_ref) {\n          var state = _ref.state;\n          return state.query;\n        },\n        getItemUrl: function getItemUrl() {\n          return undefined;\n        },\n        onSelect: function onSelect(_ref2) {\n          var setIsOpen = _ref2.setIsOpen;\n          setIsOpen(false);\n        },\n        onActive: _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__.noop,\n        onResolve: _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__.noop\n      };\n      Object.keys(defaultSource).forEach(function (key) {\n        defaultSource[key].__default = true;\n      });\n      var normalizedSource = _objectSpread(_objectSpread({}, defaultSource), source);\n      return Promise.resolve(normalizedSource);\n    }));\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/getNormalizedSources.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/getPluginSubmitPromise.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPluginSubmitPromise: function() { return /* binding */ getPluginSubmitPromise; }\n/* harmony export */ });\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\n/**\n * If a plugin is configured to await a submit event, this returns a promise\n * for either the max timeout value found or until it completes.\n * Otherwise, return undefined.\n */\nvar getPluginSubmitPromise = function getPluginSubmitPromise(plugins, pendingRequests) {\n  var waitUntilComplete = false;\n  var timeouts = [];\n  var _iterator = _createForOfIteratorHelper(plugins),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var _plugin$__autocomplet, _plugin$__autocomplet2, _plugin$__autocomplet3;\n      var plugin = _step.value;\n      var value = (_plugin$__autocomplet = plugin.__autocomplete_pluginOptions) === null || _plugin$__autocomplet === void 0 ? void 0 : (_plugin$__autocomplet2 = (_plugin$__autocomplet3 = _plugin$__autocomplet).awaitSubmit) === null || _plugin$__autocomplet2 === void 0 ? void 0 : _plugin$__autocomplet2.call(_plugin$__autocomplet3);\n      if (typeof value === \'number\') {\n        timeouts.push(value);\n      } else if (value === true) {\n        waitUntilComplete = true;\n        break; // break loop as bool overrides num array below\n      }\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  if (waitUntilComplete) {\n    return pendingRequests.wait();\n  } else if (timeouts.length > 0) {\n    return pendingRequests.wait(Math.max.apply(Math, timeouts));\n  }\n  return undefined;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/getPluginSubmitPromise.js?')},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/isOrContainsNode.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isOrContainsNode: function() { return /* binding */ isOrContainsNode; }\n/* harmony export */ });\nfunction isOrContainsNode(parent, child) {\n  return parent === child || parent.contains(child);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/isOrContainsNode.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/isSamsung.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSamsung: function() { return /* binding */ isSamsung; }\n/* harmony export */ });\nvar regex = /((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;\nfunction isSamsung(userAgent) {\n  return Boolean(userAgent && userAgent.match(regex));\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/isSamsung.js?")},"./node_modules/@algolia/autocomplete-core/dist/esm/utils/mapToAlgoliaResponse.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mapToAlgoliaResponse: function() { return /* binding */ mapToAlgoliaResponse; }\n/* harmony export */ });\nfunction mapToAlgoliaResponse(rawResults) {\n  return {\n    results: rawResults,\n    hits: rawResults.map(function (result) {\n      return result.hits;\n    }).filter(Boolean),\n    facetHits: rawResults.map(function (result) {\n      var _facetHits;\n      return (_facetHits = result.facetHits) === null || _facetHits === void 0 ? void 0 : _facetHits.map(function (facetHit) {\n        // Bring support for the highlighting components.\n        return {\n          label: facetHit.value,\n          count: facetHit.count,\n          _highlightResult: {\n            label: {\n              value: facetHit.highlighted\n            }\n          }\n        };\n      });\n    }).filter(Boolean)\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-core/dist/esm/utils/mapToAlgoliaResponse.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/autocomplete.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autocomplete: function() { return /* binding */ autocomplete; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @algolia/autocomplete-core */ "./node_modules/@algolia/autocomplete-core/dist/esm/createAutocomplete.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/getItemsCount.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js");\n/* harmony import */ var htm__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! htm */ "./node_modules/htm/dist/htm.module.js");\n/* harmony import */ var _createAutocompleteDom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./createAutocompleteDom */ "./node_modules/@algolia/autocomplete-js/dist/esm/createAutocompleteDom.js");\n/* harmony import */ var _createEffectWrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createEffectWrapper */ "./node_modules/@algolia/autocomplete-js/dist/esm/createEffectWrapper.js");\n/* harmony import */ var _createReactiveWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createReactiveWrapper */ "./node_modules/@algolia/autocomplete-js/dist/esm/createReactiveWrapper.js");\n/* harmony import */ var _getDefaultOptions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getDefaultOptions */ "./node_modules/@algolia/autocomplete-js/dist/esm/getDefaultOptions.js");\n/* harmony import */ var _getPanelPlacementStyle__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./getPanelPlacementStyle */ "./node_modules/@algolia/autocomplete-js/dist/esm/getPanelPlacementStyle.js");\n/* harmony import */ var _render__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./render */ "./node_modules/@algolia/autocomplete-js/dist/esm/render.js");\n/* harmony import */ var _userAgents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./userAgents */ "./node_modules/@algolia/autocomplete-js/dist/esm/userAgents.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/setProperties.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/mergeDeep.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/pickBy.js");\nvar _excluded = ["components"];\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\n\n\n\n\n\n\n\n\n\n\nvar instancesCount = 0;\nfunction autocomplete(options) {\n  var _createEffectWrapper = (0,_createEffectWrapper__WEBPACK_IMPORTED_MODULE_1__.createEffectWrapper)(),\n    runEffect = _createEffectWrapper.runEffect,\n    cleanupEffects = _createEffectWrapper.cleanupEffects,\n    runEffects = _createEffectWrapper.runEffects;\n  var _createReactiveWrappe = (0,_createReactiveWrapper__WEBPACK_IMPORTED_MODULE_2__.createReactiveWrapper)(),\n    reactive = _createReactiveWrappe.reactive,\n    runReactives = _createReactiveWrappe.runReactives;\n  var hasNoResultsSourceTemplateRef = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.createRef)(false);\n  var optionsRef = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.createRef)(options);\n  var onStateChangeRef = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.createRef)(undefined);\n  var props = reactive(function () {\n    return (0,_getDefaultOptions__WEBPACK_IMPORTED_MODULE_4__.getDefaultOptions)(optionsRef.current);\n  });\n  var isDetached = reactive(function () {\n    return props.value.core.environment.matchMedia(props.value.renderer.detachedMediaQuery).matches;\n  });\n  var autocomplete = reactive(function () {\n    return (0,_algolia_autocomplete_core__WEBPACK_IMPORTED_MODULE_5__.createAutocomplete)(_objectSpread(_objectSpread({}, props.value.core), {}, {\n      onStateChange: function onStateChange(params) {\n        var _onStateChangeRef$cur, _props$value$core$onS, _props$value$core;\n        hasNoResultsSourceTemplateRef.current = params.state.collections.some(function (collection) {\n          return collection.source.templates.noResults;\n        });\n        (_onStateChangeRef$cur = onStateChangeRef.current) === null || _onStateChangeRef$cur === void 0 ? void 0 : _onStateChangeRef$cur.call(onStateChangeRef, params);\n        (_props$value$core$onS = (_props$value$core = props.value.core).onStateChange) === null || _props$value$core$onS === void 0 ? void 0 : _props$value$core$onS.call(_props$value$core, params);\n      },\n      shouldPanelOpen: optionsRef.current.shouldPanelOpen || function (_ref) {\n        var state = _ref.state;\n        if (isDetached.value) {\n          return true;\n        }\n        var hasItems = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_6__.getItemsCount)(state) > 0;\n        if (!props.value.core.openOnFocus && !state.query) {\n          return hasItems;\n        }\n        var hasNoResultsTemplate = Boolean(hasNoResultsSourceTemplateRef.current || props.value.renderer.renderNoResults);\n        return !hasItems && hasNoResultsTemplate || hasItems;\n      },\n      __autocomplete_metadata: {\n        userAgents: _userAgents__WEBPACK_IMPORTED_MODULE_7__.userAgents,\n        options: options\n      }\n    }));\n  });\n  var lastStateRef = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.createRef)(_objectSpread({\n    collections: [],\n    completion: null,\n    context: {},\n    isOpen: false,\n    query: \'\',\n    activeItemId: null,\n    status: \'idle\'\n  }, props.value.core.initialState));\n  var propGetters = {\n    getEnvironmentProps: props.value.renderer.getEnvironmentProps,\n    getFormProps: props.value.renderer.getFormProps,\n    getInputProps: props.value.renderer.getInputProps,\n    getItemProps: props.value.renderer.getItemProps,\n    getLabelProps: props.value.renderer.getLabelProps,\n    getListProps: props.value.renderer.getListProps,\n    getPanelProps: props.value.renderer.getPanelProps,\n    getRootProps: props.value.renderer.getRootProps\n  };\n  var autocompleteScopeApi = {\n    setActiveItemId: autocomplete.value.setActiveItemId,\n    setQuery: autocomplete.value.setQuery,\n    setCollections: autocomplete.value.setCollections,\n    setIsOpen: autocomplete.value.setIsOpen,\n    setStatus: autocomplete.value.setStatus,\n    setContext: autocomplete.value.setContext,\n    refresh: autocomplete.value.refresh,\n    navigator: autocomplete.value.navigator\n  };\n  var html = reactive(function () {\n    return htm__WEBPACK_IMPORTED_MODULE_0__["default"].bind(props.value.renderer.renderer.createElement);\n  });\n  var dom = reactive(function () {\n    return (0,_createAutocompleteDom__WEBPACK_IMPORTED_MODULE_8__.createAutocompleteDom)({\n      autocomplete: autocomplete.value,\n      autocompleteScopeApi: autocompleteScopeApi,\n      classNames: props.value.renderer.classNames,\n      environment: props.value.core.environment,\n      isDetached: isDetached.value,\n      placeholder: props.value.core.placeholder,\n      propGetters: propGetters,\n      setIsModalOpen: setIsModalOpen,\n      state: lastStateRef.current,\n      translations: props.value.renderer.translations\n    });\n  });\n  function setPanelPosition() {\n    (0,_utils__WEBPACK_IMPORTED_MODULE_9__.setProperties)(dom.value.panel, {\n      style: isDetached.value ? {} : (0,_getPanelPlacementStyle__WEBPACK_IMPORTED_MODULE_10__.getPanelPlacementStyle)({\n        panelPlacement: props.value.renderer.panelPlacement,\n        container: dom.value.root,\n        form: dom.value.form,\n        environment: props.value.core.environment\n      })\n    });\n  }\n  function scheduleRender(state) {\n    lastStateRef.current = state;\n    var renderProps = {\n      autocomplete: autocomplete.value,\n      autocompleteScopeApi: autocompleteScopeApi,\n      classNames: props.value.renderer.classNames,\n      components: props.value.renderer.components,\n      container: props.value.renderer.container,\n      html: html.value,\n      dom: dom.value,\n      panelContainer: isDetached.value ? dom.value.detachedContainer : props.value.renderer.panelContainer,\n      propGetters: propGetters,\n      state: lastStateRef.current,\n      renderer: props.value.renderer.renderer\n    };\n    var render = !(0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_6__.getItemsCount)(state) && !hasNoResultsSourceTemplateRef.current && props.value.renderer.renderNoResults || props.value.renderer.render;\n    (0,_render__WEBPACK_IMPORTED_MODULE_11__.renderSearchBox)(renderProps);\n    (0,_render__WEBPACK_IMPORTED_MODULE_11__.renderPanel)(render, renderProps);\n  }\n  runEffect(function () {\n    var environmentProps = autocomplete.value.getEnvironmentProps({\n      formElement: dom.value.form,\n      panelElement: dom.value.panel,\n      inputElement: dom.value.input\n    });\n    (0,_utils__WEBPACK_IMPORTED_MODULE_9__.setProperties)(props.value.core.environment, environmentProps);\n    return function () {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_9__.setProperties)(props.value.core.environment, Object.keys(environmentProps).reduce(function (acc, key) {\n        return _objectSpread(_objectSpread({}, acc), {}, _defineProperty({}, key, undefined));\n      }, {}));\n    };\n  });\n  runEffect(function () {\n    var panelContainerElement = isDetached.value ? props.value.core.environment.document.body : props.value.renderer.panelContainer;\n    var panelElement = isDetached.value ? dom.value.detachedOverlay : dom.value.panel;\n    if (isDetached.value && lastStateRef.current.isOpen) {\n      setIsModalOpen(true);\n    }\n    scheduleRender(lastStateRef.current);\n    return function () {\n      if (panelContainerElement.contains(panelElement)) {\n        panelContainerElement.removeChild(panelElement);\n        panelContainerElement.classList.remove(\'aa-Detached\');\n      }\n    };\n  });\n  runEffect(function () {\n    var containerElement = props.value.renderer.container;\n    containerElement.appendChild(dom.value.root);\n    return function () {\n      containerElement.removeChild(dom.value.root);\n    };\n  });\n  runEffect(function () {\n    var debouncedRender = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_12__.debounce)(function (_ref2) {\n      var state = _ref2.state;\n      scheduleRender(state);\n    }, 0);\n    onStateChangeRef.current = function (_ref3) {\n      var state = _ref3.state,\n        prevState = _ref3.prevState;\n      if (isDetached.value && prevState.isOpen !== state.isOpen) {\n        setIsModalOpen(state.isOpen);\n      }\n\n      // The outer DOM might have changed since the last time the panel was\n      // positioned. The layout might have shifted vertically for instance.\n      // It\'s therefore safer to re-calculate the panel position before opening\n      // it again.\n      if (!isDetached.value && state.isOpen && !prevState.isOpen) {\n        setPanelPosition();\n      }\n\n      // We scroll to the top of the panel whenever the query changes (i.e. new\n      // results come in) so that users don\'t have to.\n      if (state.query !== prevState.query) {\n        var scrollablePanels = props.value.core.environment.document.querySelectorAll(\'.aa-Panel--scrollable\');\n        scrollablePanels.forEach(function (scrollablePanel) {\n          if (scrollablePanel.scrollTop !== 0) {\n            scrollablePanel.scrollTop = 0;\n          }\n        });\n      }\n      debouncedRender({\n        state: state\n      });\n    };\n    return function () {\n      onStateChangeRef.current = undefined;\n    };\n  });\n  runEffect(function () {\n    var onResize = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_12__.debounce)(function () {\n      var previousIsDetached = isDetached.value;\n      isDetached.value = props.value.core.environment.matchMedia(props.value.renderer.detachedMediaQuery).matches;\n      if (previousIsDetached !== isDetached.value) {\n        update({});\n      } else {\n        requestAnimationFrame(setPanelPosition);\n      }\n    }, 20);\n    props.value.core.environment.addEventListener(\'resize\', onResize);\n    return function () {\n      props.value.core.environment.removeEventListener(\'resize\', onResize);\n    };\n  });\n  runEffect(function () {\n    if (!isDetached.value) {\n      return function () {};\n    }\n    function toggleModalClassname(isActive) {\n      dom.value.detachedContainer.classList.toggle(\'aa-DetachedContainer--modal\', isActive);\n    }\n    function onChange(event) {\n      toggleModalClassname(event.matches);\n    }\n    var isModalDetachedMql = props.value.core.environment.matchMedia(getComputedStyle(props.value.core.environment.document.documentElement).getPropertyValue(\'--aa-detached-modal-media-query\'));\n    toggleModalClassname(isModalDetachedMql.matches);\n\n    // Prior to Safari 14, `MediaQueryList` isn\'t based on `EventTarget`,\n    // so we must use `addListener` and `removeListener` to observe media query lists.\n    // See https://developer.mozilla.org/en-US/docs/Web/API/MediaQueryList/addListener\n    var hasModernEventListener = Boolean(isModalDetachedMql.addEventListener);\n    hasModernEventListener ? isModalDetachedMql.addEventListener(\'change\', onChange) : isModalDetachedMql.addListener(onChange);\n    return function () {\n      hasModernEventListener ? isModalDetachedMql.removeEventListener(\'change\', onChange) : isModalDetachedMql.removeListener(onChange);\n    };\n  });\n  runEffect(function () {\n    requestAnimationFrame(setPanelPosition);\n    return function () {};\n  });\n  function destroy() {\n    instancesCount--;\n    cleanupEffects();\n  }\n  function update() {\n    var updatedOptions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    cleanupEffects();\n    var _props$value$renderer = props.value.renderer,\n      components = _props$value$renderer.components,\n      rendererProps = _objectWithoutProperties(_props$value$renderer, _excluded);\n    optionsRef.current = (0,_utils__WEBPACK_IMPORTED_MODULE_13__.mergeDeep)(rendererProps, props.value.core, {\n      // We need to filter out default components so they can be replaced with\n      // a new `renderer`, without getting rid of user components.\n      // @MAJOR Deal with registering components with the same name as the\n      // default ones. If we disallow overriding default components, we\'d just\n      // need to pass all `components` here.\n      components: (0,_utils__WEBPACK_IMPORTED_MODULE_14__.pickBy)(components, function (_ref4) {\n        var value = _ref4.value;\n        return !value.hasOwnProperty(\'__autocomplete_componentName\');\n      }),\n      initialState: lastStateRef.current\n    }, updatedOptions);\n    runReactives();\n    runEffects();\n    autocomplete.value.refresh().then(function () {\n      scheduleRender(lastStateRef.current);\n    });\n  }\n  function setIsModalOpen(value) {\n    var prevValue = props.value.core.environment.document.body.contains(dom.value.detachedOverlay);\n    if (value === prevValue) {\n      return;\n    }\n    if (value) {\n      props.value.core.environment.document.body.appendChild(dom.value.detachedOverlay);\n      props.value.core.environment.document.body.classList.add(\'aa-Detached\');\n      dom.value.input.focus();\n    } else {\n      props.value.core.environment.document.body.removeChild(dom.value.detachedOverlay);\n      props.value.core.environment.document.body.classList.remove(\'aa-Detached\');\n    }\n  }\n   true ? (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_15__.warn)(instancesCount === 0, "Autocomplete doesn\'t support multiple instances running at the same time. Make sure to destroy the previous instance before creating a new one.\\n\\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/api-reference/autocomplete-js/autocomplete/#param-destroy") : 0;\n  instancesCount++;\n  return _objectSpread(_objectSpread({}, autocompleteScopeApi), {}, {\n    update: update,\n    destroy: destroy\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/autocomplete.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/components/Highlight.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHighlightComponent: function() { return /* binding */ createHighlightComponent; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-preset-algolia */ \"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitHighlight.js\");\n\nfunction createHighlightComponent(_ref) {\n  var createElement = _ref.createElement,\n    Fragment = _ref.Fragment;\n  function Highlight(_ref2) {\n    var hit = _ref2.hit,\n      attribute = _ref2.attribute,\n      _ref2$tagName = _ref2.tagName,\n      tagName = _ref2$tagName === void 0 ? 'mark' : _ref2$tagName;\n    return createElement(Fragment, {}, (0,_algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__.parseAlgoliaHitHighlight)({\n      hit: hit,\n      attribute: attribute\n    }).map(function (x, index) {\n      return x.isHighlighted ? createElement(tagName, {\n        key: index\n      }, x.value) : x.value;\n    }));\n  }\n  Highlight.__autocomplete_componentName = 'Highlight';\n  return Highlight;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/components/Highlight.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/components/ReverseHighlight.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createReverseHighlightComponent: function() { return /* binding */ createReverseHighlightComponent; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-preset-algolia */ \"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitReverseHighlight.js\");\n\nfunction createReverseHighlightComponent(_ref) {\n  var createElement = _ref.createElement,\n    Fragment = _ref.Fragment;\n  function ReverseHighlight(_ref2) {\n    var hit = _ref2.hit,\n      attribute = _ref2.attribute,\n      _ref2$tagName = _ref2.tagName,\n      tagName = _ref2$tagName === void 0 ? 'mark' : _ref2$tagName;\n    return createElement(Fragment, {}, (0,_algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__.parseAlgoliaHitReverseHighlight)({\n      hit: hit,\n      attribute: attribute\n    }).map(function (x, index) {\n      return x.isHighlighted ? createElement(tagName, {\n        key: index\n      }, x.value) : x.value;\n    }));\n  }\n  ReverseHighlight.__autocomplete_componentName = 'ReverseHighlight';\n  return ReverseHighlight;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/components/ReverseHighlight.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/components/ReverseSnippet.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createReverseSnippetComponent: function() { return /* binding */ createReverseSnippetComponent; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-preset-algolia */ \"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitReverseSnippet.js\");\n\nfunction createReverseSnippetComponent(_ref) {\n  var createElement = _ref.createElement,\n    Fragment = _ref.Fragment;\n  function ReverseSnippet(_ref2) {\n    var hit = _ref2.hit,\n      attribute = _ref2.attribute,\n      _ref2$tagName = _ref2.tagName,\n      tagName = _ref2$tagName === void 0 ? 'mark' : _ref2$tagName;\n    return createElement(Fragment, {}, (0,_algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__.parseAlgoliaHitReverseSnippet)({\n      hit: hit,\n      attribute: attribute\n    }).map(function (x, index) {\n      return x.isHighlighted ? createElement(tagName, {\n        key: index\n      }, x.value) : x.value;\n    }));\n  }\n  ReverseSnippet.__autocomplete_componentName = 'ReverseSnippet';\n  return ReverseSnippet;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/components/ReverseSnippet.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/components/Snippet.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSnippetComponent: function() { return /* binding */ createSnippetComponent; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-preset-algolia */ \"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitSnippet.js\");\n\nfunction createSnippetComponent(_ref) {\n  var createElement = _ref.createElement,\n    Fragment = _ref.Fragment;\n  function Snippet(_ref2) {\n    var hit = _ref2.hit,\n      attribute = _ref2.attribute,\n      _ref2$tagName = _ref2.tagName,\n      tagName = _ref2$tagName === void 0 ? 'mark' : _ref2$tagName;\n    return createElement(Fragment, {}, (0,_algolia_autocomplete_preset_algolia__WEBPACK_IMPORTED_MODULE_0__.parseAlgoliaHitSnippet)({\n      hit: hit,\n      attribute: attribute\n    }).map(function (x, index) {\n      return x.isHighlighted ? createElement(tagName, {\n        key: index\n      }, x.value) : x.value;\n    }));\n  }\n  Snippet.__autocomplete_componentName = 'Snippet';\n  return Snippet;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/components/Snippet.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/createAutocompleteDom.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAutocompleteDom: function() { return /* binding */ createAutocompleteDom; }\n/* harmony export */ });\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./elements */ \"./node_modules/@algolia/autocomplete-js/dist/esm/elements/SearchIcon.js\");\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./elements */ \"./node_modules/@algolia/autocomplete-js/dist/esm/elements/ClearIcon.js\");\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./elements */ \"./node_modules/@algolia/autocomplete-js/dist/esm/elements/LoadingIcon.js\");\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./elements */ \"./node_modules/@algolia/autocomplete-js/dist/esm/elements/Input.js\");\n/* harmony import */ var _getCreateDomElement__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getCreateDomElement */ \"./node_modules/@algolia/autocomplete-js/dist/esm/getCreateDomElement.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\n\n\nfunction createAutocompleteDom(_ref) {\n  var autocomplete = _ref.autocomplete,\n    autocompleteScopeApi = _ref.autocompleteScopeApi,\n    classNames = _ref.classNames,\n    environment = _ref.environment,\n    isDetached = _ref.isDetached,\n    _ref$placeholder = _ref.placeholder,\n    placeholder = _ref$placeholder === void 0 ? 'Search' : _ref$placeholder,\n    propGetters = _ref.propGetters,\n    setIsModalOpen = _ref.setIsModalOpen,\n    state = _ref.state,\n    translations = _ref.translations;\n  var createDomElement = (0,_getCreateDomElement__WEBPACK_IMPORTED_MODULE_0__.getCreateDomElement)(environment);\n  var rootProps = propGetters.getRootProps(_objectSpread({\n    state: state,\n    props: autocomplete.getRootProps({})\n  }, autocompleteScopeApi));\n  var root = createDomElement('div', _objectSpread({\n    class: classNames.root\n  }, rootProps));\n  var detachedContainer = createDomElement('div', {\n    class: classNames.detachedContainer,\n    onMouseDown: function onMouseDown(event) {\n      event.stopPropagation();\n    }\n  });\n  var detachedOverlay = createDomElement('div', {\n    class: classNames.detachedOverlay,\n    children: [detachedContainer],\n    onMouseDown: function onMouseDown() {\n      setIsModalOpen(false);\n      autocomplete.setIsOpen(false);\n    }\n  });\n  var labelProps = propGetters.getLabelProps(_objectSpread({\n    state: state,\n    props: autocomplete.getLabelProps({})\n  }, autocompleteScopeApi));\n  var submitButton = createDomElement('button', {\n    class: classNames.submitButton,\n    type: 'submit',\n    title: translations.submitButtonTitle,\n    children: [(0,_elements__WEBPACK_IMPORTED_MODULE_1__.SearchIcon)({\n      environment: environment\n    })]\n  });\n  // @MAJOR Remove the label wrapper for the submit button.\n  // The submit button is sufficient for accessibility purposes, and\n  // wrapping it with the label actually makes it less accessible (see CR-6077).\n  var label = createDomElement('label', _objectSpread({\n    class: classNames.label,\n    children: [submitButton],\n    ariaLabel: translations.submitButtonTitle\n  }, labelProps));\n  var clearButton = createDomElement('button', {\n    class: classNames.clearButton,\n    type: 'reset',\n    title: translations.clearButtonTitle,\n    children: [(0,_elements__WEBPACK_IMPORTED_MODULE_2__.ClearIcon)({\n      environment: environment\n    })]\n  });\n  var loadingIndicator = createDomElement('div', {\n    class: classNames.loadingIndicator,\n    children: [(0,_elements__WEBPACK_IMPORTED_MODULE_3__.LoadingIcon)({\n      environment: environment\n    })]\n  });\n  var input = (0,_elements__WEBPACK_IMPORTED_MODULE_4__.Input)({\n    class: classNames.input,\n    environment: environment,\n    state: state,\n    getInputProps: propGetters.getInputProps,\n    getInputPropsCore: autocomplete.getInputProps,\n    autocompleteScopeApi: autocompleteScopeApi,\n    isDetached: isDetached\n  });\n  var inputWrapperPrefix = createDomElement('div', {\n    class: classNames.inputWrapperPrefix,\n    children: [label, loadingIndicator]\n  });\n  var inputWrapperSuffix = createDomElement('div', {\n    class: classNames.inputWrapperSuffix,\n    children: [clearButton]\n  });\n  var inputWrapper = createDomElement('div', {\n    class: classNames.inputWrapper,\n    children: [input]\n  });\n  var formProps = propGetters.getFormProps(_objectSpread({\n    state: state,\n    props: autocomplete.getFormProps({\n      inputElement: input\n    })\n  }, autocompleteScopeApi));\n  var form = createDomElement('form', _objectSpread({\n    class: classNames.form,\n    children: [inputWrapperPrefix, inputWrapper, inputWrapperSuffix]\n  }, formProps));\n  var panelProps = propGetters.getPanelProps(_objectSpread({\n    state: state,\n    props: autocomplete.getPanelProps({})\n  }, autocompleteScopeApi));\n  var panel = createDomElement('div', _objectSpread({\n    class: classNames.panel\n  }, panelProps));\n  var detachedSearchButtonQuery = createDomElement('div', {\n    class: classNames.detachedSearchButtonQuery,\n    textContent: state.query\n  });\n  var detachedSearchButtonPlaceholder = createDomElement('div', {\n    class: classNames.detachedSearchButtonPlaceholder,\n    hidden: Boolean(state.query),\n    textContent: placeholder\n  });\n  if (false) {}\n  if (isDetached) {\n    var detachedSearchButtonIcon = createDomElement('div', {\n      class: classNames.detachedSearchButtonIcon,\n      children: [(0,_elements__WEBPACK_IMPORTED_MODULE_1__.SearchIcon)({\n        environment: environment\n      })]\n    });\n    var detachedSearchButton = createDomElement('button', {\n      type: 'button',\n      class: classNames.detachedSearchButton,\n      title: translations.detachedSearchButtonTitle,\n      id: labelProps.id,\n      onClick: function onClick() {\n        setIsModalOpen(true);\n      },\n      children: [detachedSearchButtonIcon, detachedSearchButtonPlaceholder, detachedSearchButtonQuery]\n    });\n    var detachedCancelButton = createDomElement('button', {\n      type: 'button',\n      class: classNames.detachedCancelButton,\n      textContent: translations.detachedCancelButtonText,\n      // Prevent `onTouchStart` from closing the panel\n      // since it should be initiated by `onClick` only\n      onTouchStart: function onTouchStart(event) {\n        event.stopPropagation();\n      },\n      onClick: function onClick() {\n        autocomplete.setIsOpen(false);\n        setIsModalOpen(false);\n      }\n    });\n    var detachedFormContainer = createDomElement('div', {\n      class: classNames.detachedFormContainer,\n      children: [form, detachedCancelButton]\n    });\n    detachedContainer.appendChild(detachedFormContainer);\n    root.appendChild(detachedSearchButton);\n  } else {\n    root.appendChild(form);\n  }\n  return {\n    detachedContainer: detachedContainer,\n    detachedOverlay: detachedOverlay,\n    detachedSearchButtonQuery: detachedSearchButtonQuery,\n    detachedSearchButtonPlaceholder: detachedSearchButtonPlaceholder,\n    inputWrapper: inputWrapper,\n    input: input,\n    root: root,\n    form: form,\n    label: label,\n    submitButton: submitButton,\n    clearButton: clearButton,\n    loadingIndicator: loadingIndicator,\n    panel: panel\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/createAutocompleteDom.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/createEffectWrapper.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEffectWrapper: function() { return /* binding */ createEffectWrapper; }\n/* harmony export */ });\nfunction createEffectWrapper() {\n  var effects = [];\n  var cleanups = [];\n  function runEffect(fn) {\n    effects.push(fn);\n    var effectCleanup = fn();\n    cleanups.push(effectCleanup);\n  }\n  return {\n    runEffect: runEffect,\n    cleanupEffects: function cleanupEffects() {\n      var currentCleanups = cleanups;\n      cleanups = [];\n      currentCleanups.forEach(function (cleanup) {\n        cleanup();\n      });\n    },\n    runEffects: function runEffects() {\n      var currentEffects = effects;\n      effects = [];\n      currentEffects.forEach(function (effect) {\n        runEffect(effect);\n      });\n    }\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/createEffectWrapper.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/createReactiveWrapper.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createReactiveWrapper: function() { return /* binding */ createReactiveWrapper; }\n/* harmony export */ });\nfunction createReactiveWrapper() {\n  var reactives = [];\n  return {\n    reactive: function reactive(value) {\n      var current = value();\n      var reactive = {\n        _fn: value,\n        _ref: {\n          current: current\n        },\n        get value() {\n          return this._ref.current;\n        },\n        set value(value) {\n          this._ref.current = value;\n        }\n      };\n      reactives.push(reactive);\n      return reactive;\n    },\n    runReactives: function runReactives() {\n      reactives.forEach(function (value) {\n        value._ref.current = value._fn();\n      });\n    }\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/createReactiveWrapper.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/elements/ClearIcon.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClearIcon: function() { return /* binding */ ClearIcon; }\n/* harmony export */ });\nvar ClearIcon = function ClearIcon(_ref) {\n  var environment = _ref.environment;\n  var element = environment.document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n  element.setAttribute('class', 'aa-ClearIcon');\n  element.setAttribute('viewBox', '0 0 24 24');\n  element.setAttribute('width', '18');\n  element.setAttribute('height', '18');\n  element.setAttribute('fill', 'currentColor');\n  var path = environment.document.createElementNS('http://www.w3.org/2000/svg', 'path');\n  path.setAttribute('d', 'M5.293 6.707l5.293 5.293-5.293 5.293c-0.391 0.391-0.391 1.024 0 1.414s1.024 0.391 1.414 0l5.293-5.293 5.293 5.293c0.391 0.391 1.024 0.391 1.414 0s0.391-1.024 0-1.414l-5.293-5.293 5.293-5.293c0.391-0.391 0.391-1.024 0-1.414s-1.024-0.391-1.414 0l-5.293 5.293-5.293-5.293c-0.391-0.391-1.024-0.391-1.414 0s-0.391 1.024 0 1.414z');\n  element.appendChild(path);\n  return element;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/elements/ClearIcon.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/elements/Input.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: function() { return /* binding */ Input; }\n/* harmony export */ });\n/* harmony import */ var _getCreateDomElement__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../getCreateDomElement */ "./node_modules/@algolia/autocomplete-js/dist/esm/getCreateDomElement.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/setProperties.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nvar _excluded = ["autocompleteScopeApi", "environment", "classNames", "getInputProps", "getInputPropsCore", "isDetached", "state"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\nvar Input = function Input(_ref) {\n  var autocompleteScopeApi = _ref.autocompleteScopeApi,\n    environment = _ref.environment,\n    classNames = _ref.classNames,\n    getInputProps = _ref.getInputProps,\n    getInputPropsCore = _ref.getInputPropsCore,\n    isDetached = _ref.isDetached,\n    state = _ref.state,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var createDomElement = (0,_getCreateDomElement__WEBPACK_IMPORTED_MODULE_0__.getCreateDomElement)(environment);\n  var element = createDomElement(\'input\', props);\n  var inputProps = getInputProps(_objectSpread({\n    state: state,\n    props: getInputPropsCore({\n      inputElement: element\n    }),\n    inputElement: element\n  }, autocompleteScopeApi));\n  (0,_utils__WEBPACK_IMPORTED_MODULE_1__.setProperties)(element, _objectSpread(_objectSpread({}, inputProps), {}, {\n    onKeyDown: function onKeyDown(event) {\n      // In detached mode we don\'t want to close the panel when hitting `Tab`.\n      if (isDetached && event.key === \'Tab\') {\n        return;\n      }\n      inputProps.onKeyDown(event);\n    }\n  }));\n  return element;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/elements/Input.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/elements/LoadingIcon.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingIcon: function() { return /* binding */ LoadingIcon; }\n/* harmony export */ });\nvar LoadingIcon = function LoadingIcon(_ref) {\n  var environment = _ref.environment;\n  var element = environment.document.createElementNS(\'http://www.w3.org/2000/svg\', \'svg\');\n  element.setAttribute(\'class\', \'aa-LoadingIcon\');\n  element.setAttribute(\'viewBox\', \'0 0 100 100\');\n  element.setAttribute(\'width\', \'20\');\n  element.setAttribute(\'height\', \'20\');\n  element.innerHTML = "<circle\\n  cx=\\"50\\"\\n  cy=\\"50\\"\\n  fill=\\"none\\"\\n  r=\\"35\\"\\n  stroke=\\"currentColor\\"\\n  stroke-dasharray=\\"164.93361431346415 56.97787143782138\\"\\n  stroke-width=\\"6\\"\\n>\\n  <animateTransform\\n    attributeName=\\"transform\\"\\n    type=\\"rotate\\"\\n    repeatCount=\\"indefinite\\"\\n    dur=\\"1s\\"\\n    values=\\"0 50 50;90 50 50;180 50 50;360 50 50\\"\\n    keyTimes=\\"0;0.40;0.65;1\\"\\n  />\\n</circle>";\n  return element;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/elements/LoadingIcon.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/elements/SearchIcon.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchIcon: function() { return /* binding */ SearchIcon; }\n/* harmony export */ });\nvar SearchIcon = function SearchIcon(_ref) {\n  var environment = _ref.environment;\n  var element = environment.document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n  element.setAttribute('class', 'aa-SubmitIcon');\n  element.setAttribute('viewBox', '0 0 24 24');\n  element.setAttribute('width', '20');\n  element.setAttribute('height', '20');\n  element.setAttribute('fill', 'currentColor');\n  var path = environment.document.createElementNS('http://www.w3.org/2000/svg', 'path');\n  path.setAttribute('d', 'M16.041 15.856c-0.034 0.026-0.067 0.055-0.099 0.087s-0.060 0.064-0.087 0.099c-1.258 1.213-2.969 1.958-4.855 1.958-1.933 0-3.682-0.782-4.95-2.050s-2.050-3.017-2.050-4.95 0.782-3.682 2.050-4.95 3.017-2.050 4.95-2.050 3.682 0.782 4.95 2.050 2.050 3.017 2.050 4.95c0 1.886-0.745 3.597-1.959 4.856zM21.707 20.293l-3.675-3.675c1.231-1.54 1.968-3.493 1.968-5.618 0-2.485-1.008-4.736-2.636-6.364s-3.879-2.636-6.364-2.636-4.736 1.008-6.364 2.636-2.636 3.879-2.636 6.364 1.008 4.736 2.636 6.364 3.879 2.636 6.364 2.636c2.125 0 4.078-0.737 5.618-1.968l3.675 3.675c0.391 0.391 1.024 0.391 1.414 0s0.391-1.024 0-1.414z');\n  element.appendChild(path);\n  return element;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/elements/SearchIcon.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/getCreateDomElement.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCreateDomElement: function() { return /* binding */ getCreateDomElement; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/setProperties.js");\nvar _excluded = ["children"];\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction getCreateDomElement(environment) {\n  return function createDomElement(tagName, _ref) {\n    var _ref$children = _ref.children,\n      children = _ref$children === void 0 ? [] : _ref$children,\n      props = _objectWithoutProperties(_ref, _excluded);\n    var element = environment.document.createElement(tagName);\n    (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setProperties)(element, props);\n    element.append.apply(element, _toConsumableArray(children));\n    return element;\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/getCreateDomElement.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/getDefaultOptions.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultOptions: function() { return /* binding */ getDefaultOptions; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/generateAutocompleteId.js");\n/* harmony import */ var preact__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! preact */ "./node_modules/preact/dist/preact.module.js");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ "./node_modules/@algolia/autocomplete-js/dist/esm/components/Highlight.js");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components */ "./node_modules/@algolia/autocomplete-js/dist/esm/components/ReverseHighlight.js");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components */ "./node_modules/@algolia/autocomplete-js/dist/esm/components/ReverseSnippet.js");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components */ "./node_modules/@algolia/autocomplete-js/dist/esm/components/Snippet.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/getHTMLElement.js");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/mergeClassNames.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nvar _excluded = ["classNames", "container", "getEnvironmentProps", "getFormProps", "getInputProps", "getItemProps", "getLabelProps", "getListProps", "getPanelProps", "getRootProps", "panelContainer", "panelPlacement", "render", "renderNoResults", "renderer", "detachedMediaQuery", "components", "translations"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\n\n\nvar defaultClassNames = {\n  clearButton: \'aa-ClearButton\',\n  detachedCancelButton: \'aa-DetachedCancelButton\',\n  detachedContainer: \'aa-DetachedContainer\',\n  detachedFormContainer: \'aa-DetachedFormContainer\',\n  detachedOverlay: \'aa-DetachedOverlay\',\n  detachedSearchButton: \'aa-DetachedSearchButton\',\n  detachedSearchButtonIcon: \'aa-DetachedSearchButtonIcon\',\n  detachedSearchButtonPlaceholder: \'aa-DetachedSearchButtonPlaceholder\',\n  detachedSearchButtonQuery: \'aa-DetachedSearchButtonQuery\',\n  form: \'aa-Form\',\n  input: \'aa-Input\',\n  inputWrapper: \'aa-InputWrapper\',\n  inputWrapperPrefix: \'aa-InputWrapperPrefix\',\n  inputWrapperSuffix: \'aa-InputWrapperSuffix\',\n  item: \'aa-Item\',\n  label: \'aa-Label\',\n  list: \'aa-List\',\n  loadingIndicator: \'aa-LoadingIndicator\',\n  panel: \'aa-Panel\',\n  panelLayout: \'aa-PanelLayout aa-Panel--scrollable\',\n  root: \'aa-Autocomplete\',\n  source: \'aa-Source\',\n  sourceFooter: \'aa-SourceFooter\',\n  sourceHeader: \'aa-SourceHeader\',\n  sourceNoResults: \'aa-SourceNoResults\',\n  submitButton: \'aa-SubmitButton\'\n};\nvar defaultRender = function defaultRender(_ref, root) {\n  var children = _ref.children,\n    render = _ref.render;\n  render(children, root);\n};\nvar defaultRenderer = {\n  createElement: preact__WEBPACK_IMPORTED_MODULE_0__.createElement,\n  Fragment: preact__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n  render: preact__WEBPACK_IMPORTED_MODULE_0__.render\n};\nfunction getDefaultOptions(options) {\n  var _core$id;\n  var classNames = options.classNames,\n    container = options.container,\n    getEnvironmentProps = options.getEnvironmentProps,\n    getFormProps = options.getFormProps,\n    getInputProps = options.getInputProps,\n    getItemProps = options.getItemProps,\n    getLabelProps = options.getLabelProps,\n    getListProps = options.getListProps,\n    getPanelProps = options.getPanelProps,\n    getRootProps = options.getRootProps,\n    panelContainer = options.panelContainer,\n    panelPlacement = options.panelPlacement,\n    render = options.render,\n    renderNoResults = options.renderNoResults,\n    renderer = options.renderer,\n    detachedMediaQuery = options.detachedMediaQuery,\n    components = options.components,\n    translations = options.translations,\n    core = _objectWithoutProperties(options, _excluded);\n\n  /* eslint-disable no-restricted-globals */\n  var environment = typeof window !== \'undefined\' ? window : {};\n  /* eslint-enable no-restricted-globals */\n  var containerElement = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getHTMLElement)(environment, container);\n  (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__.invariant)(containerElement.tagName !== \'INPUT\', \'The `container` option does not support `input` elements. You need to change the container to a `div`.\');\n   true ? (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.warn)(!(render && renderer && !(renderer !== null && renderer !== void 0 && renderer.render)), "You provided the `render` option but did not provide a `renderer.render`. Since v1.6.0, you can provide a `render` function directly in `renderer`." + "\\nTo get rid of this warning, do any of the following depending on your use case." + "\\n- If you are using the `render` option only to override Autocomplete\'s default `render` function, pass the `render` function into `renderer` and remove the `render` option." + \'\\n- If you are using the `render` option to customize the layout, pass your `render` function into `renderer` and use it from the provided parameters of the `render` option.\' + \'\\n- If you are using the `render` option to work with React 18, pass an empty `render` function into `renderer`.\' + \'\\nSee https://www.algolia.com/doc/ui-libraries/autocomplete/api-reference/autocomplete-js/autocomplete/#param-render\') : 0;\n   true ? (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_3__.warn)(!renderer || render || renderer.Fragment && renderer.createElement && renderer.render, "You provided an incomplete `renderer` (missing: ".concat([!(renderer !== null && renderer !== void 0 && renderer.createElement) && \'`renderer.createElement`\', !(renderer !== null && renderer !== void 0 && renderer.Fragment) && \'`renderer.Fragment`\', !(renderer !== null && renderer !== void 0 && renderer.render) && \'`renderer.render`\'].filter(Boolean).join(\', \'), "). This can cause rendering issues.") + \'\\nSee https://www.algolia.com/doc/ui-libraries/autocomplete/api-reference/autocomplete-js/autocomplete/#param-renderer\') : 0;\n  var defaultedRenderer = _objectSpread(_objectSpread({}, defaultRenderer), renderer);\n  var defaultComponents = {\n    Highlight: (0,_components__WEBPACK_IMPORTED_MODULE_4__.createHighlightComponent)(defaultedRenderer),\n    ReverseHighlight: (0,_components__WEBPACK_IMPORTED_MODULE_5__.createReverseHighlightComponent)(defaultedRenderer),\n    ReverseSnippet: (0,_components__WEBPACK_IMPORTED_MODULE_6__.createReverseSnippetComponent)(defaultedRenderer),\n    Snippet: (0,_components__WEBPACK_IMPORTED_MODULE_7__.createSnippetComponent)(defaultedRenderer)\n  };\n  var defaultTranslations = {\n    clearButtonTitle: \'Clear\',\n    detachedCancelButtonText: \'Cancel\',\n    detachedSearchButtonTitle: \'Search\',\n    submitButtonTitle: \'Submit\'\n  };\n  return {\n    renderer: {\n      classNames: (0,_utils__WEBPACK_IMPORTED_MODULE_8__.mergeClassNames)(defaultClassNames, classNames !== null && classNames !== void 0 ? classNames : {}),\n      container: containerElement,\n      getEnvironmentProps: getEnvironmentProps !== null && getEnvironmentProps !== void 0 ? getEnvironmentProps : function (_ref2) {\n        var props = _ref2.props;\n        return props;\n      },\n      getFormProps: getFormProps !== null && getFormProps !== void 0 ? getFormProps : function (_ref3) {\n        var props = _ref3.props;\n        return props;\n      },\n      getInputProps: getInputProps !== null && getInputProps !== void 0 ? getInputProps : function (_ref4) {\n        var props = _ref4.props;\n        return props;\n      },\n      getItemProps: getItemProps !== null && getItemProps !== void 0 ? getItemProps : function (_ref5) {\n        var props = _ref5.props;\n        return props;\n      },\n      getLabelProps: getLabelProps !== null && getLabelProps !== void 0 ? getLabelProps : function (_ref6) {\n        var props = _ref6.props;\n        return props;\n      },\n      getListProps: getListProps !== null && getListProps !== void 0 ? getListProps : function (_ref7) {\n        var props = _ref7.props;\n        return props;\n      },\n      getPanelProps: getPanelProps !== null && getPanelProps !== void 0 ? getPanelProps : function (_ref8) {\n        var props = _ref8.props;\n        return props;\n      },\n      getRootProps: getRootProps !== null && getRootProps !== void 0 ? getRootProps : function (_ref9) {\n        var props = _ref9.props;\n        return props;\n      },\n      panelContainer: panelContainer ? (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getHTMLElement)(environment, panelContainer) : environment.document.body,\n      panelPlacement: panelPlacement !== null && panelPlacement !== void 0 ? panelPlacement : \'input-wrapper-width\',\n      render: render !== null && render !== void 0 ? render : defaultRender,\n      renderNoResults: renderNoResults,\n      renderer: defaultedRenderer,\n      detachedMediaQuery: detachedMediaQuery !== null && detachedMediaQuery !== void 0 ? detachedMediaQuery : getComputedStyle(environment.document.documentElement).getPropertyValue(\'--aa-detached-media-query\'),\n      components: _objectSpread(_objectSpread({}, defaultComponents), components),\n      translations: _objectSpread(_objectSpread({}, defaultTranslations), translations)\n    },\n    core: _objectSpread(_objectSpread({}, core), {}, {\n      id: (_core$id = core.id) !== null && _core$id !== void 0 ? _core$id : (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_9__.generateAutocompleteId)(),\n      environment: environment\n    })\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/getDefaultOptions.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/getPanelPlacementStyle.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPanelPlacementStyle: function() { return /* binding */ getPanelPlacementStyle; }\n/* harmony export */ });\nfunction getPanelPlacementStyle(_ref) {\n  var panelPlacement = _ref.panelPlacement,\n    container = _ref.container,\n    form = _ref.form,\n    environment = _ref.environment;\n  var containerRect = container.getBoundingClientRect();\n  // Some browsers have specificities to retrieve the document scroll position.\n  // See https://stackoverflow.com/a/28633515/9940315\n  var scrollTop = environment.pageYOffset || environment.document.documentElement.scrollTop || environment.document.body.scrollTop || 0;\n  var top = scrollTop + containerRect.top + containerRect.height;\n  switch (panelPlacement) {\n    case 'start':\n      {\n        return {\n          top: top,\n          left: containerRect.left\n        };\n      }\n    case 'end':\n      {\n        return {\n          top: top,\n          right: environment.document.documentElement.clientWidth - (containerRect.left + containerRect.width)\n        };\n      }\n    case 'full-width':\n      {\n        return {\n          top: top,\n          left: 0,\n          right: 0,\n          width: 'unset',\n          maxWidth: 'unset'\n        };\n      }\n    case 'input-wrapper-width':\n      {\n        var formRect = form.getBoundingClientRect();\n        return {\n          top: top,\n          left: formRect.left,\n          right: environment.document.documentElement.clientWidth - (formRect.left + formRect.width),\n          width: 'unset',\n          maxWidth: 'unset'\n        };\n      }\n    default:\n      {\n        throw new Error(\"[Autocomplete] The `panelPlacement` value \".concat(JSON.stringify(panelPlacement), \" is not valid.\"));\n      }\n  }\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/getPanelPlacementStyle.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/render.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderPanel: function() { return /* binding */ renderPanel; },\n/* harmony export */   renderSearchBox: function() { return /* binding */ renderSearchBox; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/@algolia/autocomplete-js/dist/esm/utils/setProperties.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n/** @jsxRuntime classic */\n/** @jsx renderer.createElement */\n\n\nfunction renderSearchBox(_ref) {\n  var autocomplete = _ref.autocomplete,\n    autocompleteScopeApi = _ref.autocompleteScopeApi,\n    dom = _ref.dom,\n    propGetters = _ref.propGetters,\n    state = _ref.state;\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setPropertiesWithoutEvents)(dom.root, propGetters.getRootProps(_objectSpread({\n    state: state,\n    props: autocomplete.getRootProps({})\n  }, autocompleteScopeApi)));\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setPropertiesWithoutEvents)(dom.input, propGetters.getInputProps(_objectSpread({\n    state: state,\n    props: autocomplete.getInputProps({\n      inputElement: dom.input\n    }),\n    inputElement: dom.input\n  }, autocompleteScopeApi)));\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setProperties)(dom.label, {\n    hidden: state.status === \'stalled\'\n  });\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setProperties)(dom.loadingIndicator, {\n    hidden: state.status !== \'stalled\'\n  });\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setProperties)(dom.clearButton, {\n    hidden: !state.query\n  });\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setProperties)(dom.detachedSearchButtonQuery, {\n    textContent: state.query\n  });\n  (0,_utils__WEBPACK_IMPORTED_MODULE_0__.setProperties)(dom.detachedSearchButtonPlaceholder, {\n    hidden: Boolean(state.query)\n  });\n}\nfunction renderPanel(render, _ref2) {\n  var autocomplete = _ref2.autocomplete,\n    autocompleteScopeApi = _ref2.autocompleteScopeApi,\n    classNames = _ref2.classNames,\n    html = _ref2.html,\n    dom = _ref2.dom,\n    panelContainer = _ref2.panelContainer,\n    propGetters = _ref2.propGetters,\n    state = _ref2.state,\n    components = _ref2.components,\n    renderer = _ref2.renderer;\n  if (!state.isOpen) {\n    if (panelContainer.contains(dom.panel)) {\n      panelContainer.removeChild(dom.panel);\n    }\n    return;\n  }\n\n  // We add the panel element to the DOM when it\'s not yet appended and that the\n  // items are fetched.\n  if (!panelContainer.contains(dom.panel) && state.status !== \'loading\') {\n    panelContainer.appendChild(dom.panel);\n  }\n  dom.panel.classList.toggle(\'aa-Panel--stalled\', state.status === \'stalled\');\n  var sections = state.collections.filter(function (_ref3) {\n    var source = _ref3.source,\n      items = _ref3.items;\n    return source.templates.noResults || items.length > 0;\n  }).map(function (_ref4, sourceIndex) {\n    var source = _ref4.source,\n      items = _ref4.items;\n    return renderer.createElement("section", {\n      key: sourceIndex,\n      className: classNames.source,\n      "data-autocomplete-source-id": source.sourceId\n    }, source.templates.header && renderer.createElement("div", {\n      className: classNames.sourceHeader\n    }, source.templates.header({\n      components: components,\n      createElement: renderer.createElement,\n      Fragment: renderer.Fragment,\n      items: items,\n      source: source,\n      state: state,\n      html: html\n    })), source.templates.noResults && items.length === 0 ? renderer.createElement("div", {\n      className: classNames.sourceNoResults\n    }, source.templates.noResults({\n      components: components,\n      createElement: renderer.createElement,\n      Fragment: renderer.Fragment,\n      source: source,\n      state: state,\n      html: html\n    })) : renderer.createElement("ul", _extends({\n      className: classNames.list\n    }, propGetters.getListProps(_objectSpread({\n      state: state,\n      props: autocomplete.getListProps({\n        source: source\n      })\n    }, autocompleteScopeApi))), items.map(function (item) {\n      var itemProps = autocomplete.getItemProps({\n        item: item,\n        source: source\n      });\n      return renderer.createElement("li", _extends({\n        key: itemProps.id,\n        className: classNames.item\n      }, propGetters.getItemProps(_objectSpread({\n        state: state,\n        props: itemProps\n      }, autocompleteScopeApi))), source.templates.item({\n        components: components,\n        createElement: renderer.createElement,\n        Fragment: renderer.Fragment,\n        item: item,\n        state: state,\n        html: html\n      }));\n    })), source.templates.footer && renderer.createElement("div", {\n      className: classNames.sourceFooter\n    }, source.templates.footer({\n      components: components,\n      createElement: renderer.createElement,\n      Fragment: renderer.Fragment,\n      items: items,\n      source: source,\n      state: state,\n      html: html\n    })));\n  });\n  var children = renderer.createElement(renderer.Fragment, null, renderer.createElement("div", {\n    className: classNames.panelLayout\n  }, sections), renderer.createElement("div", {\n    className: "aa-GradientBottom"\n  }));\n  var elements = sections.reduce(function (acc, current) {\n    acc[current.props[\'data-autocomplete-source-id\']] = current;\n    return acc;\n  }, {});\n  render(_objectSpread(_objectSpread({\n    children: children,\n    state: state,\n    sections: sections,\n    elements: elements\n  }, renderer), {}, {\n    components: components,\n    html: html\n  }, autocompleteScopeApi), dom.panel);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/render.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/userAgents.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userAgents: function() { return /* binding */ userAgents; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ \"./node_modules/@algolia/autocomplete-shared/dist/esm/version.js\");\n\nvar userAgents = [{\n  segment: 'autocomplete-js',\n  version: _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.version\n}];\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/userAgents.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/utils/getHTMLElement.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHTMLElement: function() { return /* binding */ getHTMLElement; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js");\n\nfunction getHTMLElement(environment, value) {\n  if (typeof value === \'string\') {\n    var element = environment.document.querySelector(value);\n    (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.invariant)(element !== null, "The element ".concat(JSON.stringify(value), " is not in the document."));\n    return element;\n  }\n  return value;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/utils/getHTMLElement.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/utils/mergeClassNames.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeClassNames: function() { return /* binding */ mergeClassNames; }\n/* harmony export */ });\nfunction mergeClassNames() {\n  for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {\n    values[_key] = arguments[_key];\n  }\n  return values.reduce(function (acc, current) {\n    Object.keys(current).forEach(function (key) {\n      var accValue = acc[key];\n      var currentValue = current[key];\n      if (accValue !== currentValue) {\n        acc[key] = [accValue, currentValue].filter(Boolean).join(' ');\n      }\n    });\n    return acc;\n  }, {});\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/utils/mergeClassNames.js?")},"./node_modules/@algolia/autocomplete-js/dist/esm/utils/mergeDeep.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeDeep: function() { return /* binding */ mergeDeep; }\n/* harmony export */ });\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nvar isPlainObject = function isPlainObject(value) {\n  return value && _typeof(value) === \'object\' && Object.prototype.toString.call(value) === \'[object Object]\';\n};\nfunction mergeDeep() {\n  for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {\n    values[_key] = arguments[_key];\n  }\n  return values.reduce(function (acc, current) {\n    Object.keys(current).forEach(function (key) {\n      var accValue = acc[key];\n      var currentValue = current[key];\n      if (Array.isArray(accValue) && Array.isArray(currentValue)) {\n        acc[key] = accValue.concat.apply(accValue, _toConsumableArray(currentValue));\n      } else if (isPlainObject(accValue) && isPlainObject(currentValue)) {\n        acc[key] = mergeDeep(accValue, currentValue);\n      } else {\n        acc[key] = currentValue;\n      }\n    });\n    return acc;\n  }, {});\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/utils/mergeDeep.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/utils/pickBy.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickBy: function() { return /* binding */ pickBy; }\n/* harmony export */ });\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction pickBy(obj, predicate) {\n  return Object.entries(obj).reduce(function (acc, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      key = _ref2[0],\n      value = _ref2[1];\n    if (predicate({\n      key: key,\n      value: value\n    })) {\n      return _objectSpread(_objectSpread({}, acc), {}, _defineProperty({}, key, value));\n    }\n    return acc;\n  }, {});\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/utils/pickBy.js?')},"./node_modules/@algolia/autocomplete-js/dist/esm/utils/setProperties.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setProperties: function() { return /* binding */ setProperties; },\n/* harmony export */   setPropertiesWithoutEvents: function() { return /* binding */ setPropertiesWithoutEvents; },\n/* harmony export */   setProperty: function() { return /* binding */ setProperty; }\n/* harmony export */ });\n/* eslint-disable */\n\n/**\n * Touch-specific event aliases\n *\n * See https://w3c.github.io/touch-events/#extensions-to-the-globaleventhandlers-mixin\n */\nvar TOUCH_EVENTS_ALIASES = ['ontouchstart', 'ontouchend', 'ontouchmove', 'ontouchcancel'];\n\n/*\n * Taken from Preact\n *\n * See https://github.com/preactjs/preact/blob/6ab49d9020740127577bf4af66bf63f4af7f9fee/src/diff/props.js#L58-L151\n */\n\nfunction setStyle(style, key, value) {\n  if (value === null) {\n    style[key] = '';\n  } else if (typeof value !== 'number') {\n    style[key] = value;\n  } else {\n    style[key] = value + 'px';\n  }\n}\n\n/**\n * Proxy an event to hooked event handlers\n */\nfunction eventProxy(event) {\n  this._listeners[event.type](event);\n}\n\n/**\n * Set a property value on a DOM node\n */\nfunction setProperty(dom, name, value) {\n  var useCapture;\n  var nameLower;\n  var oldValue = dom[name];\n  if (name === 'style') {\n    if (typeof value == 'string') {\n      dom.style = value;\n    } else {\n      if (value === null) {\n        dom.style = '';\n      } else {\n        for (name in value) {\n          if (!oldValue || value[name] !== oldValue[name]) {\n            setStyle(dom.style, name, value[name]);\n          }\n        }\n      }\n    }\n  }\n  // Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n  else if (name[0] === 'o' && name[1] === 'n') {\n    useCapture = name !== (name = name.replace(/Capture$/, ''));\n    nameLower = name.toLowerCase();\n    if (nameLower in dom || TOUCH_EVENTS_ALIASES.includes(nameLower)) name = nameLower;\n    name = name.slice(2);\n    if (!dom._listeners) dom._listeners = {};\n    dom._listeners[name] = value;\n    if (value) {\n      if (!oldValue) dom.addEventListener(name, eventProxy, useCapture);\n    } else {\n      dom.removeEventListener(name, eventProxy, useCapture);\n    }\n  } else if (name !== 'list' && name !== 'tagName' &&\n  // HTMLButtonElement.form and HTMLInputElement.form are read-only but can be set using\n  // setAttribute\n  name !== 'form' && name !== 'type' && name !== 'size' && name !== 'download' && name !== 'href' && name in dom) {\n    dom[name] = value == null ? '' : value;\n  } else if (typeof value != 'function' && name !== 'dangerouslySetInnerHTML') {\n    if (value == null || value === false &&\n    // ARIA-attributes have a different notion of boolean values.\n    // The value `false` is different from the attribute not\n    // existing on the DOM, so we can't remove it. For non-boolean\n    // ARIA-attributes we could treat false as a removal, but the\n    // amount of exceptions would cost us too many bytes. On top of\n    // that other VDOM frameworks also always stringify `false`.\n    !/^ar/.test(name)) {\n      dom.removeAttribute(name);\n    } else {\n      dom.setAttribute(name, value);\n    }\n  }\n}\nfunction getNormalizedName(name) {\n  switch (name) {\n    case 'onChange':\n      return 'onInput';\n    // see: https://github.com/preactjs/preact/issues/1978\n    case 'onCompositionEnd':\n      return 'oncompositionend';\n    default:\n      return name;\n  }\n}\nfunction setProperties(dom, props) {\n  for (var name in props) {\n    setProperty(dom, getNormalizedName(name), props[name]);\n  }\n}\nfunction setPropertiesWithoutEvents(dom, props) {\n  for (var name in props) {\n    if (!(name[0] === 'o' && name[1] === 'n')) {\n      setProperty(dom, getNormalizedName(name), props[name]);\n    }\n  }\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-js/dist/esm/utils/setProperties.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createAlgoliaInsightsPlugin.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAlgoliaInsightsPlugin: function() { return /* binding */ createAlgoliaInsightsPlugin; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/safelyRunOnBrowser.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/isEqual.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/noop.js");\n/* harmony import */ var _createClickedEvent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./createClickedEvent */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createClickedEvent.js");\n/* harmony import */ var _createSearchInsightsApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createSearchInsightsApi */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createSearchInsightsApi.js");\n/* harmony import */ var _createViewedEvents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createViewedEvents */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createViewedEvents.js");\n/* harmony import */ var _isAlgoliaInsightsHit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./isAlgoliaInsightsHit */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isAlgoliaInsightsHit.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\n\n\n\n\nvar VIEW_EVENT_DELAY = 400;\nvar ALGOLIA_INSIGHTS_VERSION = \'2.15.0\';\nvar ALGOLIA_INSIGHTS_SRC = "https://cdn.jsdelivr.net/npm/search-insights@".concat(ALGOLIA_INSIGHTS_VERSION, "/dist/search-insights.min.js");\nvar sendViewedObjectIDs = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.debounce)(function (_ref) {\n  var onItemsChange = _ref.onItemsChange,\n    items = _ref.items,\n    insights = _ref.insights,\n    state = _ref.state;\n  onItemsChange({\n    insights: insights,\n    insightsEvents: (0,_createViewedEvents__WEBPACK_IMPORTED_MODULE_1__.createViewedEvents)({\n      items: items\n    }).map(function (event) {\n      return _objectSpread({\n        eventName: \'Items Viewed\'\n      }, event);\n    }),\n    state: state\n  });\n}, VIEW_EVENT_DELAY);\nfunction createAlgoliaInsightsPlugin(options) {\n  var _getOptions = getOptions(options),\n    providedInsightsClient = _getOptions.insightsClient,\n    insightsInitParams = _getOptions.insightsInitParams,\n    onItemsChange = _getOptions.onItemsChange,\n    onSelectEvent = _getOptions.onSelect,\n    onActiveEvent = _getOptions.onActive,\n    __autocomplete_clickAnalytics = _getOptions.__autocomplete_clickAnalytics;\n  var insightsClient = providedInsightsClient;\n  if (!providedInsightsClient) {\n    (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_2__.safelyRunOnBrowser)(function (_ref2) {\n      var window = _ref2.window;\n      var pointer = window.AlgoliaAnalyticsObject || \'aa\';\n      if (typeof pointer === \'string\') {\n        insightsClient = window[pointer];\n      }\n      if (!insightsClient) {\n        window.AlgoliaAnalyticsObject = pointer;\n        if (!window[pointer]) {\n          window[pointer] = function () {\n            if (!window[pointer].queue) {\n              window[pointer].queue = [];\n            }\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n            window[pointer].queue.push(args);\n          };\n        }\n        window[pointer].version = ALGOLIA_INSIGHTS_VERSION;\n        insightsClient = window[pointer];\n        loadInsights(window);\n      }\n    });\n  }\n\n  // We return an empty plugin if `insightsClient` is still undefined at\n  // this stage, which can happen in server environments.\n  if (!insightsClient) {\n    return {};\n  }\n  if (insightsInitParams) {\n    insightsClient(\'init\', _objectSpread({\n      partial: true\n    }, insightsInitParams));\n  }\n  var insights = (0,_createSearchInsightsApi__WEBPACK_IMPORTED_MODULE_3__.createSearchInsightsApi)(insightsClient);\n  var previousItems = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_4__.createRef)([]);\n  var debouncedOnStateChange = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.debounce)(function (_ref3) {\n    var state = _ref3.state;\n    if (!state.isOpen) {\n      return;\n    }\n    var items = state.collections.reduce(function (acc, current) {\n      return [].concat(_toConsumableArray(acc), _toConsumableArray(current.items));\n    }, []).filter(_isAlgoliaInsightsHit__WEBPACK_IMPORTED_MODULE_5__.isAlgoliaInsightsHit);\n    if (!(0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_6__.isEqual)(previousItems.current.map(function (x) {\n      return x.objectID;\n    }), items.map(function (x) {\n      return x.objectID;\n    }))) {\n      previousItems.current = items;\n      if (items.length > 0) {\n        sendViewedObjectIDs({\n          onItemsChange: onItemsChange,\n          items: items,\n          insights: insights,\n          state: state\n        });\n      }\n    }\n  }, 0);\n  return {\n    name: \'aa.algoliaInsightsPlugin\',\n    subscribe: function subscribe(_ref4) {\n      var setContext = _ref4.setContext,\n        onSelect = _ref4.onSelect,\n        onActive = _ref4.onActive;\n      function setInsightsContext(userToken) {\n        setContext({\n          algoliaInsightsPlugin: {\n            __algoliaSearchParameters: _objectSpread(_objectSpread({}, __autocomplete_clickAnalytics ? {\n              clickAnalytics: true\n            } : {}), userToken ? {\n              userToken: normalizeUserToken(userToken)\n            } : {}),\n            insights: insights\n          }\n        });\n      }\n      insightsClient(\'addAlgoliaAgent\', \'insights-plugin\');\n      setInsightsContext();\n\n      // Handles user token changes\n      insightsClient(\'onUserTokenChange\', function (userToken) {\n        setInsightsContext(userToken);\n      });\n      insightsClient(\'getUserToken\', null, function (_error, userToken) {\n        setInsightsContext(userToken);\n      });\n      onSelect(function (_ref5) {\n        var item = _ref5.item,\n          state = _ref5.state,\n          event = _ref5.event,\n          source = _ref5.source;\n        if (!(0,_isAlgoliaInsightsHit__WEBPACK_IMPORTED_MODULE_5__.isAlgoliaInsightsHit)(item)) {\n          return;\n        }\n        onSelectEvent({\n          state: state,\n          event: event,\n          insights: insights,\n          item: item,\n          insightsEvents: [_objectSpread({\n            eventName: \'Item Selected\'\n          }, (0,_createClickedEvent__WEBPACK_IMPORTED_MODULE_7__.createClickedEvent)({\n            item: item,\n            items: source.getItems().filter(_isAlgoliaInsightsHit__WEBPACK_IMPORTED_MODULE_5__.isAlgoliaInsightsHit)\n          }))]\n        });\n      });\n      onActive(function (_ref6) {\n        var item = _ref6.item,\n          source = _ref6.source,\n          state = _ref6.state,\n          event = _ref6.event;\n        if (!(0,_isAlgoliaInsightsHit__WEBPACK_IMPORTED_MODULE_5__.isAlgoliaInsightsHit)(item)) {\n          return;\n        }\n        onActiveEvent({\n          state: state,\n          event: event,\n          insights: insights,\n          item: item,\n          insightsEvents: [_objectSpread({\n            eventName: \'Item Active\'\n          }, (0,_createClickedEvent__WEBPACK_IMPORTED_MODULE_7__.createClickedEvent)({\n            item: item,\n            items: source.getItems().filter(_isAlgoliaInsightsHit__WEBPACK_IMPORTED_MODULE_5__.isAlgoliaInsightsHit)\n          }))]\n        });\n      });\n    },\n    onStateChange: function onStateChange(_ref7) {\n      var state = _ref7.state;\n      debouncedOnStateChange({\n        state: state\n      });\n    },\n    __autocomplete_pluginOptions: options\n  };\n}\nfunction getAlgoliaSources() {\n  var _context$algoliaInsig;\n  var algoliaSourceBase = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var context = arguments.length > 1 ? arguments[1] : undefined;\n  return [].concat(_toConsumableArray(algoliaSourceBase), [\'autocomplete-internal\'], _toConsumableArray((_context$algoliaInsig = context.algoliaInsightsPlugin) !== null && _context$algoliaInsig !== void 0 && _context$algoliaInsig.__automaticInsights ? [\'autocomplete-automatic\'] : []));\n}\nfunction getOptions(options) {\n  return _objectSpread({\n    onItemsChange: function onItemsChange(_ref8) {\n      var insights = _ref8.insights,\n        insightsEvents = _ref8.insightsEvents,\n        state = _ref8.state;\n      insights.viewedObjectIDs.apply(insights, _toConsumableArray(insightsEvents.map(function (event) {\n        return _objectSpread(_objectSpread({}, event), {}, {\n          algoliaSource: getAlgoliaSources(event.algoliaSource, state.context)\n        });\n      })));\n    },\n    onSelect: function onSelect(_ref9) {\n      var insights = _ref9.insights,\n        insightsEvents = _ref9.insightsEvents,\n        state = _ref9.state;\n      insights.clickedObjectIDsAfterSearch.apply(insights, _toConsumableArray(insightsEvents.map(function (event) {\n        return _objectSpread(_objectSpread({}, event), {}, {\n          algoliaSource: getAlgoliaSources(event.algoliaSource, state.context)\n        });\n      })));\n    },\n    onActive: _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_8__.noop,\n    __autocomplete_clickAnalytics: true\n  }, options);\n}\nfunction loadInsights(environment) {\n  var errorMessage = "[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";\n  try {\n    var script = environment.document.createElement(\'script\');\n    script.async = true;\n    script.src = ALGOLIA_INSIGHTS_SRC;\n    script.onerror = function () {\n      // eslint-disable-next-line no-console\n      console.error(errorMessage);\n    };\n    document.body.appendChild(script);\n  } catch (cause) {\n    // eslint-disable-next-line no-console\n    console.error(errorMessage);\n  }\n}\n\n/**\n * While `search-insights` supports both string and number user tokens,\n * the Search API only accepts strings. This function normalizes the user token.\n */\nfunction normalizeUserToken(userToken) {\n  return typeof userToken === \'number\' ? userToken.toString() : userToken;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createAlgoliaInsightsPlugin.js?')},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createClickedEvent.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClickedEvent: function() { return /* binding */ createClickedEvent; }\n/* harmony export */ });\nfunction createClickedEvent(_ref) {\n  var item = _ref.item,\n    _ref$items = _ref.items,\n    items = _ref$items === void 0 ? [] : _ref$items;\n  return {\n    index: item.__autocomplete_indexName,\n    items: [item],\n    positions: [1 + items.findIndex(function (x) {\n      return x.objectID === item.objectID;\n    })],\n    queryID: item.__autocomplete_queryID,\n    algoliaSource: ['autocomplete']\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createClickedEvent.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createSearchInsightsApi.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSearchInsightsApi: function() { return /* binding */ createSearchInsightsApi; }\n/* harmony export */ });\n/* harmony import */ var _isModernInsightsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isModernInsightsClient */ "./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isModernInsightsClient.js");\nvar _excluded = ["items"],\n  _excluded2 = ["items"];\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\nfunction chunk(item) {\n  var chunkSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;\n  var chunks = [];\n  for (var i = 0; i < item.objectIDs.length; i += chunkSize) {\n    chunks.push(_objectSpread(_objectSpread({}, item), {}, {\n      objectIDs: item.objectIDs.slice(i, i + chunkSize)\n    }));\n  }\n  return chunks;\n}\nfunction mapToInsightsParamsApi(params) {\n  return params.map(function (_ref) {\n    var items = _ref.items,\n      param = _objectWithoutProperties(_ref, _excluded);\n    return _objectSpread(_objectSpread({}, param), {}, {\n      objectIDs: (items === null || items === void 0 ? void 0 : items.map(function (_ref2) {\n        var objectID = _ref2.objectID;\n        return objectID;\n      })) || param.objectIDs\n    });\n  });\n}\nfunction createSearchInsightsApi(searchInsights) {\n  var canSendHeaders = (0,_isModernInsightsClient__WEBPACK_IMPORTED_MODULE_0__.isModernInsightsClient)(searchInsights);\n  function sendToInsights(method, payloads, items) {\n    if (canSendHeaders && typeof items !== \'undefined\') {\n      var _items$0$__autocomple = items[0].__autocomplete_algoliaCredentials,\n        appId = _items$0$__autocomple.appId,\n        apiKey = _items$0$__autocomple.apiKey;\n      var headers = {\n        \'X-Algolia-Application-Id\': appId,\n        \'X-Algolia-API-Key\': apiKey\n      };\n      searchInsights.apply(void 0, [method].concat(_toConsumableArray(payloads), [{\n        headers: headers\n      }]));\n    } else {\n      searchInsights.apply(void 0, [method].concat(_toConsumableArray(payloads)));\n    }\n  }\n  return {\n    /**\n     * Initializes Insights with Algolia credentials.\n     */\n    init: function init(appId, apiKey) {\n      searchInsights(\'init\', {\n        appId: appId,\n        apiKey: apiKey\n      });\n    },\n    /**\n     * Sets the authenticated user token to attach to events.\n     * Unsets the authenticated token by passing `undefined`.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/set-authenticated-user-token/\n     */\n    setAuthenticatedUserToken: function setAuthenticatedUserToken(authenticatedUserToken) {\n      searchInsights(\'setAuthenticatedUserToken\', authenticatedUserToken);\n    },\n    /**\n     * Sets the user token to attach to events.\n     */\n    setUserToken: function setUserToken(userToken) {\n      searchInsights(\'setUserToken\', userToken);\n    },\n    /**\n     * Sends click events to capture a query and its clicked items and positions.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/clicked-object-ids-after-search/\n     */\n    clickedObjectIDsAfterSearch: function clickedObjectIDsAfterSearch() {\n      for (var _len = arguments.length, params = new Array(_len), _key = 0; _key < _len; _key++) {\n        params[_key] = arguments[_key];\n      }\n      if (params.length > 0) {\n        sendToInsights(\'clickedObjectIDsAfterSearch\', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends click events to capture clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/clicked-object-ids/\n     */\n    clickedObjectIDs: function clickedObjectIDs() {\n      for (var _len2 = arguments.length, params = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        params[_key2] = arguments[_key2];\n      }\n      if (params.length > 0) {\n        sendToInsights(\'clickedObjectIDs\', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends click events to capture the filters a user clicks on.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/clicked-filters/\n     */\n    clickedFilters: function clickedFilters() {\n      for (var _len3 = arguments.length, params = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        params[_key3] = arguments[_key3];\n      }\n      if (params.length > 0) {\n        searchInsights.apply(void 0, [\'clickedFilters\'].concat(params));\n      }\n    },\n    /**\n     * Sends conversion events to capture a query and its clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/converted-object-ids-after-search/\n     */\n    convertedObjectIDsAfterSearch: function convertedObjectIDsAfterSearch() {\n      for (var _len4 = arguments.length, params = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        params[_key4] = arguments[_key4];\n      }\n      if (params.length > 0) {\n        sendToInsights(\'convertedObjectIDsAfterSearch\', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends conversion events to capture clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/converted-object-ids/\n     */\n    convertedObjectIDs: function convertedObjectIDs() {\n      for (var _len5 = arguments.length, params = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        params[_key5] = arguments[_key5];\n      }\n      if (params.length > 0) {\n        sendToInsights(\'convertedObjectIDs\', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends conversion events to capture the filters a user uses when converting.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/converted-filters/\n     */\n    convertedFilters: function convertedFilters() {\n      for (var _len6 = arguments.length, params = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n        params[_key6] = arguments[_key6];\n      }\n      if (params.length > 0) {\n        searchInsights.apply(void 0, [\'convertedFilters\'].concat(params));\n      }\n    },\n    /**\n     * Sends view events to capture clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/viewed-object-ids/\n     */\n    viewedObjectIDs: function viewedObjectIDs() {\n      for (var _len7 = arguments.length, params = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        params[_key7] = arguments[_key7];\n      }\n      if (params.length > 0) {\n        params.reduce(function (acc, _ref3) {\n          var items = _ref3.items,\n            param = _objectWithoutProperties(_ref3, _excluded2);\n          return [].concat(_toConsumableArray(acc), _toConsumableArray(chunk(_objectSpread(_objectSpread({}, param), {}, {\n            objectIDs: (items === null || items === void 0 ? void 0 : items.map(function (_ref4) {\n              var objectID = _ref4.objectID;\n              return objectID;\n            })) || param.objectIDs\n          })).map(function (payload) {\n            return {\n              items: items,\n              payload: payload\n            };\n          })));\n        }, []).forEach(function (_ref5) {\n          var items = _ref5.items,\n            payload = _ref5.payload;\n          return sendToInsights(\'viewedObjectIDs\', [payload], items);\n        });\n      }\n    },\n    /**\n     * Sends view events to capture the filters a user uses when viewing.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/viewed-filters/\n     */\n    viewedFilters: function viewedFilters() {\n      for (var _len8 = arguments.length, params = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n        params[_key8] = arguments[_key8];\n      }\n      if (params.length > 0) {\n        searchInsights.apply(void 0, [\'viewedFilters\'].concat(params));\n      }\n    }\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createSearchInsightsApi.js?')},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createViewedEvents.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createViewedEvents: function() { return /* binding */ createViewedEvents; }\n/* harmony export */ });\nfunction createViewedEvents(_ref) {\n  var items = _ref.items;\n  var itemsByIndexName = items.reduce(function (acc, current) {\n    var _acc$current$__autoco;\n    acc[current.__autocomplete_indexName] = ((_acc$current$__autoco = acc[current.__autocomplete_indexName]) !== null && _acc$current$__autoco !== void 0 ? _acc$current$__autoco : []).concat(current);\n    return acc;\n  }, {});\n  return Object.keys(itemsByIndexName).map(function (indexName) {\n    var items = itemsByIndexName[indexName];\n    return {\n      index: indexName,\n      items: items,\n      algoliaSource: ['autocomplete']\n    };\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createViewedEvents.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isAlgoliaInsightsHit.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAlgoliaInsightsHit: function() { return /* binding */ isAlgoliaInsightsHit; }\n/* harmony export */ });\nfunction isAlgoliaInsightsHit(hit) {\n  return hit.objectID && hit.__autocomplete_indexName && hit.__autocomplete_queryID;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isAlgoliaInsightsHit.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isModernInsightsClient.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isModernInsightsClient: function() { return /* binding */ isModernInsightsClient; }\n/* harmony export */ });\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n/**\n * Determines if a given insights `client` supports the optional call to `init`\n * and the ability to set credentials via extra parameters when sending events.\n */\nfunction isModernInsightsClient(client) {\n  var _split$map = (client.version || \'\').split(\'.\').map(Number),\n    _split$map2 = _slicedToArray(_split$map, 2),\n    major = _split$map2[0],\n    minor = _split$map2[1];\n\n  /* eslint-disable @typescript-eslint/camelcase */\n  var v3 = major >= 3;\n  var v2_4 = major === 2 && minor >= 4;\n  var v1_10 = major === 1 && minor >= 10;\n  return v3 || v2_4 || v1_10;\n  /* eslint-enable @typescript-eslint/camelcase */\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isModernInsightsClient.js?')},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRef: function() { return /* binding */ createRef; }\n/* harmony export */ });\nfunction createRef(initialValue) {\n  return {\n    current: initialValue\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   debounce: function() { return /* binding */ debounce; }\n/* harmony export */ });\nfunction debounce(fn, time) {\n  var timerId = undefined;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (timerId) {\n      clearTimeout(timerId);\n    }\n    timerId = setTimeout(function () {\n      return fn.apply(void 0, args);\n    }, time);\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/isEqual.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEqual: function() { return /* binding */ isEqual; }\n/* harmony export */ });\nfunction isPrimitive(obj) {\n  return obj !== Object(obj);\n}\nfunction isEqual(first, second) {\n  if (first === second) {\n    return true;\n  }\n  if (isPrimitive(first) || isPrimitive(second) || typeof first === 'function' || typeof second === 'function') {\n    return first === second;\n  }\n  if (Object.keys(first).length !== Object.keys(second).length) {\n    return false;\n  }\n  for (var _i = 0, _Object$keys = Object.keys(first); _i < _Object$keys.length; _i++) {\n    var key = _Object$keys[_i];\n    if (!(key in second)) {\n      return false;\n    }\n    if (!isEqual(first[key], second[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/isEqual.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/noop.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: function() { return /* binding */ noop; }\n/* harmony export */ });\nvar noop = function noop() {};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/noop.js?")},"./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/safelyRunOnBrowser.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safelyRunOnBrowser: function() { return /* binding */ safelyRunOnBrowser; }\n/* harmony export */ });\n/**\n * Safely runs code meant for browser environments only.\n */\nfunction safelyRunOnBrowser(callback) {\n  if (typeof window !== 'undefined') {\n    return callback({\n      window: window\n    });\n  }\n  return undefined;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-plugin-algolia-insights/node_modules/@algolia/autocomplete-shared/dist/esm/safelyRunOnBrowser.js?")},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/constants/index.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HIGHLIGHT_POST_TAG: function() { return /* binding */ HIGHLIGHT_POST_TAG; },\n/* harmony export */   HIGHLIGHT_PRE_TAG: function() { return /* binding */ HIGHLIGHT_PRE_TAG; }\n/* harmony export */ });\nvar HIGHLIGHT_PRE_TAG = '__aa-highlight__';\nvar HIGHLIGHT_POST_TAG = '__/aa-highlight__';\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/constants/index.js?")},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/isPartHighlighted.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPartHighlighted: function() { return /* binding */ isPartHighlighted; }\n/* harmony export */ });\nvar htmlEscapes = {\n  '&amp;': '&',\n  '&lt;': '<',\n  '&gt;': '>',\n  '&quot;': '\"',\n  '&#39;': \"'\"\n};\nvar hasAlphanumeric = new RegExp(/\\w/i);\nvar regexEscapedHtml = /&(amp|quot|lt|gt|#39);/g;\nvar regexHasEscapedHtml = RegExp(regexEscapedHtml.source);\nfunction unescape(value) {\n  return value && regexHasEscapedHtml.test(value) ? value.replace(regexEscapedHtml, function (character) {\n    return htmlEscapes[character];\n  }) : value;\n}\nfunction isPartHighlighted(parts, i) {\n  var _parts, _parts2;\n  var current = parts[i];\n  var isNextHighlighted = ((_parts = parts[i + 1]) === null || _parts === void 0 ? void 0 : _parts.isHighlighted) || true;\n  var isPreviousHighlighted = ((_parts2 = parts[i - 1]) === null || _parts2 === void 0 ? void 0 : _parts2.isHighlighted) || true;\n  if (!hasAlphanumeric.test(unescape(current.value)) && isPreviousHighlighted === isNextHighlighted) {\n    return isPreviousHighlighted;\n  }\n  return current.isHighlighted;\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/isPartHighlighted.js?")},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitHighlight.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAlgoliaHitHighlight: function() { return /* binding */ parseAlgoliaHitHighlight; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/getAttributeValueByPath.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js");\n/* harmony import */ var _parseAttribute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parseAttribute */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAttribute.js");\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\n\n\nfunction parseAlgoliaHitHighlight(_ref) {\n  var hit = _ref.hit,\n    attribute = _ref.attribute;\n  var path = Array.isArray(attribute) ? attribute : [attribute];\n  var highlightedValue = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.getAttributeValueByPath)(hit, [\'_highlightResult\'].concat(_toConsumableArray(path), [\'value\']));\n  if (typeof highlightedValue !== \'string\') {\n     true ? (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__.warn)(false, "The attribute \\"".concat(path.join(\'.\'), "\\" described by the path ").concat(JSON.stringify(path), " does not exist on the hit. Did you set it in `attributesToHighlight`?") + \'\\nSee https://www.algolia.com/doc/api-reference/api-parameters/attributesToHighlight/\') : 0;\n    highlightedValue = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.getAttributeValueByPath)(hit, path) || \'\';\n  }\n  return (0,_parseAttribute__WEBPACK_IMPORTED_MODULE_2__.parseAttribute)({\n    highlightedValue: highlightedValue\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitHighlight.js?')},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitReverseHighlight.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAlgoliaHitReverseHighlight: function() { return /* binding */ parseAlgoliaHitReverseHighlight; }\n/* harmony export */ });\n/* harmony import */ var _parseAlgoliaHitHighlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parseAlgoliaHitHighlight */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitHighlight.js");\n/* harmony import */ var _reverseHighlightedParts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reverseHighlightedParts */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/reverseHighlightedParts.js");\n\n\nfunction parseAlgoliaHitReverseHighlight(props) {\n  return (0,_reverseHighlightedParts__WEBPACK_IMPORTED_MODULE_0__.reverseHighlightedParts)((0,_parseAlgoliaHitHighlight__WEBPACK_IMPORTED_MODULE_1__.parseAlgoliaHitHighlight)(props));\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitReverseHighlight.js?')},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitReverseSnippet.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAlgoliaHitReverseSnippet: function() { return /* binding */ parseAlgoliaHitReverseSnippet; }\n/* harmony export */ });\n/* harmony import */ var _parseAlgoliaHitSnippet__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parseAlgoliaHitSnippet */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitSnippet.js");\n/* harmony import */ var _reverseHighlightedParts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reverseHighlightedParts */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/reverseHighlightedParts.js");\n\n\nfunction parseAlgoliaHitReverseSnippet(props) {\n  return (0,_reverseHighlightedParts__WEBPACK_IMPORTED_MODULE_0__.reverseHighlightedParts)((0,_parseAlgoliaHitSnippet__WEBPACK_IMPORTED_MODULE_1__.parseAlgoliaHitSnippet)(props));\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitReverseSnippet.js?')},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitSnippet.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAlgoliaHitSnippet: function() { return /* binding */ parseAlgoliaHitSnippet; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/getAttributeValueByPath.js");\n/* harmony import */ var _algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @algolia/autocomplete-shared */ "./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js");\n/* harmony import */ var _parseAttribute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parseAttribute */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAttribute.js");\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\n\n\nfunction parseAlgoliaHitSnippet(_ref) {\n  var hit = _ref.hit,\n    attribute = _ref.attribute;\n  var path = Array.isArray(attribute) ? attribute : [attribute];\n  var highlightedValue = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.getAttributeValueByPath)(hit, [\'_snippetResult\'].concat(_toConsumableArray(path), [\'value\']));\n  if (typeof highlightedValue !== \'string\') {\n     true ? (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_1__.warn)(false, "The attribute \\"".concat(path.join(\'.\'), "\\" described by the path ").concat(JSON.stringify(path), " does not exist on the hit. Did you set it in `attributesToSnippet`?") + \'\\nSee https://www.algolia.com/doc/api-reference/api-parameters/attributesToSnippet/\') : 0;\n    highlightedValue = (0,_algolia_autocomplete_shared__WEBPACK_IMPORTED_MODULE_0__.getAttributeValueByPath)(hit, path) || \'\';\n  }\n  return (0,_parseAttribute__WEBPACK_IMPORTED_MODULE_2__.parseAttribute)({\n    highlightedValue: highlightedValue\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAlgoliaHitSnippet.js?')},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAttribute.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAttribute: function() { return /* binding */ parseAttribute; }\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/constants/index.js\");\n\n/**\n * Creates a data structure that allows to concatenate similar highlighting\n * parts in a single value.\n */\nfunction createAttributeSet() {\n  var initialValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var value = initialValue;\n  return {\n    get: function get() {\n      return value;\n    },\n    add: function add(part) {\n      var lastPart = value[value.length - 1];\n      if ((lastPart === null || lastPart === void 0 ? void 0 : lastPart.isHighlighted) === part.isHighlighted) {\n        value[value.length - 1] = {\n          value: lastPart.value + part.value,\n          isHighlighted: lastPart.isHighlighted\n        };\n      } else {\n        value.push(part);\n      }\n    }\n  };\n}\nfunction parseAttribute(_ref) {\n  var highlightedValue = _ref.highlightedValue;\n  var preTagParts = highlightedValue.split(_constants__WEBPACK_IMPORTED_MODULE_0__.HIGHLIGHT_PRE_TAG);\n  var firstValue = preTagParts.shift();\n  var parts = createAttributeSet(firstValue ? [{\n    value: firstValue,\n    isHighlighted: false\n  }] : []);\n  preTagParts.forEach(function (part) {\n    var postTagParts = part.split(_constants__WEBPACK_IMPORTED_MODULE_0__.HIGHLIGHT_POST_TAG);\n    parts.add({\n      value: postTagParts[0],\n      isHighlighted: true\n    });\n    if (postTagParts[1] !== '') {\n      parts.add({\n        value: postTagParts[1],\n        isHighlighted: false\n      });\n    }\n  });\n  return parts.get();\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/parseAttribute.js?")},"./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/reverseHighlightedParts.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reverseHighlightedParts: function() { return /* binding */ reverseHighlightedParts; }\n/* harmony export */ });\n/* harmony import */ var _isPartHighlighted__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isPartHighlighted */ "./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/isPartHighlighted.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }\n\nfunction reverseHighlightedParts(parts) {\n  // We don\'t want to highlight the whole word when no parts match.\n  if (!parts.some(function (part) {\n    return part.isHighlighted;\n  })) {\n    return parts.map(function (part) {\n      return _objectSpread(_objectSpread({}, part), {}, {\n        isHighlighted: false\n      });\n    });\n  }\n  return parts.map(function (part, i) {\n    return _objectSpread(_objectSpread({}, part), {}, {\n      isHighlighted: !(0,_isPartHighlighted__WEBPACK_IMPORTED_MODULE_0__.isPartHighlighted)(parts, i)\n    });\n  });\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-preset-algolia/dist/esm/highlight/reverseHighlightedParts.js?')},"./node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRef: function() { return /* binding */ createRef; }\n/* harmony export */ });\nfunction createRef(initialValue) {\n  return {\n    current: initialValue\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   debounce: function() { return /* binding */ debounce; }\n/* harmony export */ });\nfunction debounce(fn, time) {\n  var timerId = undefined;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (timerId) {\n      clearTimeout(timerId);\n    }\n    timerId = setTimeout(function () {\n      return fn.apply(void 0, args);\n    }, time);\n  };\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/decycle.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decycle: function() { return /* binding */ decycle; }\n/* harmony export */ });\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }\n/**\n * Decycles objects with circular references.\n * This is used to print cyclic structures in development environment only.\n */\nfunction decycle(obj) {\n  var seen = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Set();\n  if ( false || !obj || _typeof(obj) !== \'object\') {\n    return obj;\n  }\n  if (seen.has(obj)) {\n    return \'[Circular]\';\n  }\n  var newSeen = seen.add(obj);\n  if (Array.isArray(obj)) {\n    return obj.map(function (x) {\n      return decycle(x, newSeen);\n    });\n  }\n  return Object.fromEntries(Object.entries(obj).map(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      key = _ref2[0],\n      value = _ref2[1];\n    return [key, decycle(value, newSeen)];\n  }));\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/decycle.js?')},"./node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flatten: function() { return /* binding */ flatten; }\n/* harmony export */ });\nfunction flatten(values) {\n  return values.reduce(function (a, b) {\n    return a.concat(b);\n  }, []);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/generateAutocompleteId.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAutocompleteId: function() { return /* binding */ generateAutocompleteId; }\n/* harmony export */ });\nvar autocompleteId = 0;\nfunction generateAutocompleteId() {\n  return "autocomplete-".concat(autocompleteId++);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/generateAutocompleteId.js?')},"./node_modules/@algolia/autocomplete-shared/dist/esm/getAttributeValueByPath.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributeValueByPath: function() { return /* binding */ getAttributeValueByPath; }\n/* harmony export */ });\nfunction getAttributeValueByPath(record, path) {\n  return path.reduce(function (current, key) {\n    return current && current[key];\n  }, record);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/getAttributeValueByPath.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/getItemsCount.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getItemsCount: function() { return /* binding */ getItemsCount; }\n/* harmony export */ });\nfunction getItemsCount(state) {\n  if (state.collections.length === 0) {\n    return 0;\n  }\n  return state.collections.reduce(function (sum, collection) {\n    return sum + collection.items.length;\n  }, 0);\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/getItemsCount.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: function() { return /* binding */ invariant; }\n/* harmony export */ });\n/**\n * Throws an error if the condition is not met in development mode.\n * This is used to make development a better experience to provide guidance as\n * to where the error comes from.\n */\nfunction invariant(condition, message) {\n  if (false) {}\n  if (!condition) {\n    throw new Error(\"[Autocomplete] \".concat(typeof message === 'function' ? message() : message));\n  }\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/invariant.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/noop.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: function() { return /* binding */ noop; }\n/* harmony export */ });\nvar noop = function noop() {};\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/noop.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/userAgents.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userAgents: function() { return /* binding */ userAgents; }\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"./node_modules/@algolia/autocomplete-shared/dist/esm/version.js\");\n\nvar userAgents = [{\n  segment: 'autocomplete-core',\n  version: _version__WEBPACK_IMPORTED_MODULE_0__.version\n}];\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/userAgents.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/version.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: function() { return /* binding */ version; }\n/* harmony export */ });\nvar version = '1.19.0';\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/version.js?")},"./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   warn: function() { return /* binding */ warn; },\n/* harmony export */   warnCache: function() { return /* binding */ warnCache; }\n/* harmony export */ });\nvar warnCache = {\n  current: {}\n};\n\n/**\n * Logs a warning if the condition is not met.\n * This is used to log issues in development environment only.\n */\nfunction warn(condition, message) {\n  if (false) {}\n  if (condition) {\n    return;\n  }\n  var sanitizedMessage = message.trim();\n  var hasAlreadyPrinted = warnCache.current[sanitizedMessage];\n  if (!hasAlreadyPrinted) {\n    warnCache.current[sanitizedMessage] = true;\n\n    // eslint-disable-next-line no-console\n    console.warn("[Autocomplete] ".concat(sanitizedMessage));\n  }\n}\n\n//# sourceURL=webpack://Materialize/./node_modules/@algolia/autocomplete-shared/dist/esm/warn.js?')},"./libs/@algolia/autocomplete-js.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autocomplete: function() { return /* reexport safe */ _algolia_autocomplete_js__WEBPACK_IMPORTED_MODULE_0__.autocomplete; }\n/* harmony export */ });\n/* harmony import */ var _algolia_autocomplete_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @algolia/autocomplete-js */ \"./node_modules/@algolia/autocomplete-js/dist/esm/autocomplete.js\");\n// import { autocomplete } from '@algolia/autocomplete-js';\n\n\ntry {\n  window.autocomplete = _algolia_autocomplete_js__WEBPACK_IMPORTED_MODULE_0__.autocomplete;\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/@algolia/autocomplete-js.js?")},"./node_modules/htm/dist/htm.module.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": function() { return /* export default binding */ __WEBPACK_DEFAULT_EXPORT__; }\n/* harmony export */ });\nvar n=function(t,s,r,e){var u;s[0]=0;for(var h=1;h<s.length;h++){var p=s[h++],a=s[h]?(s[0]|=p?1:2,r[s[h++]]):s[++h];3===p?e[0]=a:4===p?e[1]=Object.assign(e[1]||{},a):5===p?(e[1]=e[1]||{})[s[++h]]=a:6===p?e[1][s[++h]]+=a+"":p?(u=t.apply(a,n(t,a,r,["",null])),e.push(u),a[0]?s[0]|=2:(s[h-2]=0,s[h]=u)):e.push(a)}return e},t=new Map;/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(s){var r=t.get(this);return r||(r=new Map,t.set(this,r)),(r=n(this,r.get(s)||(r.set(s,r=function(n){for(var t,s,r=1,e="",u="",h=[0],p=function(n){1===r&&(n||(e=e.replace(/^\\s*\\n\\s*|\\s*\\n\\s*$/g,"")))?h.push(0,n,e):3===r&&(n||e)?(h.push(3,n,e),r=2):2===r&&"..."===e&&n?h.push(4,n,0):2===r&&e&&!n?h.push(5,0,!0,e):r>=5&&((e||!n&&5===r)&&(h.push(r,0,e,s),r=6),n&&(h.push(r,n,0,s),r=6)),e=""},a=0;a<n.length;a++){a&&(1===r&&p(),p(a));for(var l=0;l<n[a].length;l++)t=n[a][l],1===r?"<"===t?(p(),h=[h],r=3):e+=t:4===r?"--"===e&&">"===t?(r=1,e=""):e=t+e[0]:u?t===u?u="":e+=t:\'"\'===t||"\'"===t?u=t:">"===t?(p(),r=1):r&&("="===t?(r=5,s=e,e=""):"/"===t&&(r<5||">"===n[a][l+1])?(p(),3===r&&(h=h[0]),r=h,(h=h[0]).push(2,0,r),r=0):" "===t||"\\t"===t||"\\n"===t||"\\r"===t?(p(),r=2):e+=t),3===r&&"!--"===e&&(r=4,h=h[0])}return p(),h}(s)),r),arguments,[])).length>1?r:r[0]}\n\n\n//# sourceURL=webpack://Materialize/./node_modules/htm/dist/htm.module.js?')},"./node_modules/preact/dist/preact.module.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Component: function() { return /* binding */ x; },\n/* harmony export */   Fragment: function() { return /* binding */ k; },\n/* harmony export */   cloneElement: function() { return /* binding */ J; },\n/* harmony export */   createContext: function() { return /* binding */ K; },\n/* harmony export */   createElement: function() { return /* binding */ _; },\n/* harmony export */   createRef: function() { return /* binding */ b; },\n/* harmony export */   h: function() { return /* binding */ _; },\n/* harmony export */   hydrate: function() { return /* binding */ G; },\n/* harmony export */   isValidElement: function() { return /* binding */ t; },\n/* harmony export */   options: function() { return /* binding */ l; },\n/* harmony export */   render: function() { return /* binding */ E; },\n/* harmony export */   toChildArray: function() { return /* binding */ H; }\n/* harmony export */ });\nvar n,l,u,t,i,r,o,e,f,c,s,a,h,p={},y=[],v=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,w=Array.isArray;function d(n,l){for(var u in l)n[u]=l[u];return n}function g(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function _(l,u,t){var i,r,o,e={};for(o in u)"key"==o?i=u[o]:"ref"==o?r=u[o]:e[o]=u[o];if(arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):t),"function"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps)null==e[o]&&(e[o]=l.defaultProps[o]);return m(l,e,i,r,null)}function m(n,t,i,r,o){var e={type:n,props:t,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==o?++u:o,__i:-1,__u:0};return null==o&&null!=l.vnode&&l.vnode(e),e}function b(){return{current:null}}function k(n){return n.children}function x(n,l){this.props=n,this.context=l}function S(n,l){if(null==l)return n.__?S(n.__,n.__i+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return"function"==typeof n.type?S(n):null}function C(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return C(n)}}function M(n){(!n.__d&&(n.__d=!0)&&i.push(n)&&!$.__r++||r!=l.debounceRendering)&&((r=l.debounceRendering)||o)($)}function $(){for(var n,u,t,r,o,f,c,s=1;i.length;)i.length>s&&i.sort(e),n=i.shift(),s=i.length,n.__d&&(t=void 0,o=(r=(u=n).__v).__e,f=[],c=[],u.__P&&((t=d({},r)).__v=r.__v+1,l.vnode&&l.vnode(t),O(u.__P,t,r,u.__n,u.__P.namespaceURI,32&r.__u?[o]:null,f,null==o?S(r):o,!!(32&r.__u),c),t.__v=r.__v,t.__.__k[t.__i]=t,z(f,t,c),t.__e!=o&&C(t)));$.__r=0}function I(n,l,u,t,i,r,o,e,f,c,s){var a,h,v,w,d,g,_=t&&t.__k||y,m=l.length;for(f=P(u,l,_,f,m),a=0;a<m;a++)null!=(v=u.__k[a])&&(h=-1==v.__i?p:_[v.__i]||p,v.__i=a,g=O(n,v,h,i,r,o,e,f,c,s),w=v.__e,v.ref&&h.ref!=v.ref&&(h.ref&&q(h.ref,null,v),s.push(v.ref,v.__c||w,v)),null==d&&null!=w&&(d=w),4&v.__u||h.__k===v.__k?f=A(v,f,n):"function"==typeof v.type&&void 0!==g?f=g:w&&(f=w.nextSibling),v.__u&=-7);return u.__e=d,f}function P(n,l,u,t,i){var r,o,e,f,c,s=u.length,a=s,h=0;for(n.__k=new Array(i),r=0;r<i;r++)null!=(o=l[r])&&"boolean"!=typeof o&&"function"!=typeof o?(f=r+h,(o=n.__k[r]="string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?m(null,o,null,null,null):w(o)?m(k,{children:o},null,null,null):null==o.constructor&&o.__b>0?m(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=n,o.__b=n.__b+1,e=null,-1!=(c=o.__i=L(o,u,f,a))&&(a--,(e=u[c])&&(e.__u|=2)),null==e||null==e.__v?(-1==c&&(i>s?h--:i<s&&h++),"function"!=typeof o.type&&(o.__u|=4)):c!=f&&(c==f-1?h--:c==f+1?h++:(c>f?h--:h++,o.__u|=4))):n.__k[r]=null;if(a)for(r=0;r<s;r++)null!=(e=u[r])&&0==(2&e.__u)&&(e.__e==t&&(t=S(e)),B(e,e));return t}function A(n,l,u){var t,i;if("function"==typeof n.type){for(t=n.__k,i=0;t&&i<t.length;i++)t[i]&&(t[i].__=n,l=A(t[i],l,u));return l}n.__e!=l&&(l&&n.type&&!u.contains(l)&&(l=S(n)),u.insertBefore(n.__e,l||null),l=n.__e);do{l=l&&l.nextSibling}while(null!=l&&8==l.nodeType);return l}function H(n,l){return l=l||[],null==n||"boolean"==typeof n||(w(n)?n.some(function(n){H(n,l)}):l.push(n)),l}function L(n,l,u,t){var i,r,o=n.key,e=n.type,f=l[u];if(null===f&&null==n.key||f&&o==f.key&&e==f.type&&0==(2&f.__u))return u;if(t>(null!=f&&0==(2&f.__u)?1:0))for(i=u-1,r=u+1;i>=0||r<l.length;){if(i>=0){if((f=l[i])&&0==(2&f.__u)&&o==f.key&&e==f.type)return i;i--}if(r<l.length){if((f=l[r])&&0==(2&f.__u)&&o==f.key&&e==f.type)return r;r++}}return-1}function T(n,l,u){"-"==l[0]?n.setProperty(l,null==u?"":u):n[l]=null==u?"":"number"!=typeof u||v.test(l)?u:u+"px"}function j(n,l,u,t,i){var r;n:if("style"==l)if("string"==typeof u)n.style.cssText=u;else{if("string"==typeof t&&(n.style.cssText=t=""),t)for(l in t)u&&l in u||T(n.style,l,"");if(u)for(l in u)t&&u[l]==t[l]||T(n.style,l,u[l])}else if("o"==l[0]&&"n"==l[1])r=l!=(l=l.replace(f,"$1")),l=l.toLowerCase()in n||"onFocusOut"==l||"onFocusIn"==l?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+r]=u,u?t?u.u=t.u:(u.u=c,n.addEventListener(l,r?a:s,r)):n.removeEventListener(l,r?a:s,r);else{if("http://www.w3.org/2000/svg"==i)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=l&&"height"!=l&&"href"!=l&&"list"!=l&&"form"!=l&&"tabIndex"!=l&&"download"!=l&&"rowSpan"!=l&&"colSpan"!=l&&"role"!=l&&"popover"!=l&&l in n)try{n[l]=null==u?"":u;break n}catch(n){}"function"==typeof u||(null==u||!1===u&&"-"!=l[4]?n.removeAttribute(l):n.setAttribute(l,"popover"==l&&1==u?"":u))}}function F(n){return function(u){if(this.l){var t=this.l[u.type+n];if(null==u.t)u.t=c++;else if(u.t<t.u)return;return t(l.event?l.event(u):u)}}}function O(n,u,t,i,r,o,e,f,c,s){var a,h,p,y,v,_,m,b,S,C,M,$,P,A,H,L,T,j=u.type;if(null!=u.constructor)return null;128&t.__u&&(c=!!(32&t.__u),o=[f=u.__e=t.__e]),(a=l.__b)&&a(u);n:if("function"==typeof j)try{if(b=u.props,S="prototype"in j&&j.prototype.render,C=(a=j.contextType)&&i[a.__c],M=a?C?C.props.value:a.__:i,t.__c?m=(h=u.__c=t.__c).__=h.__E:(S?u.__c=h=new j(b,M):(u.__c=h=new x(b,M),h.constructor=j,h.render=D),C&&C.sub(h),h.props=b,h.state||(h.state={}),h.context=M,h.__n=i,p=h.__d=!0,h.__h=[],h._sb=[]),S&&null==h.__s&&(h.__s=h.state),S&&null!=j.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=d({},h.__s)),d(h.__s,j.getDerivedStateFromProps(b,h.__s))),y=h.props,v=h.state,h.__v=u,p)S&&null==j.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),S&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(S&&null==j.getDerivedStateFromProps&&b!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(b,M),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(b,h.__s,M)||u.__v==t.__v){for(u.__v!=t.__v&&(h.props=b,h.state=h.__s,h.__d=!1),u.__e=t.__e,u.__k=t.__k,u.__k.some(function(n){n&&(n.__=u)}),$=0;$<h._sb.length;$++)h.__h.push(h._sb[$]);h._sb=[],h.__h.length&&e.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(b,h.__s,M),S&&null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(y,v,_)})}if(h.context=M,h.props=b,h.__P=n,h.__e=!1,P=l.__r,A=0,S){for(h.state=h.__s,h.__d=!1,P&&P(u),a=h.render(h.props,h.state,h.context),H=0;H<h._sb.length;H++)h.__h.push(h._sb[H]);h._sb=[]}else do{h.__d=!1,P&&P(u),a=h.render(h.props,h.state,h.context),h.state=h.__s}while(h.__d&&++A<25);h.state=h.__s,null!=h.getChildContext&&(i=d(d({},i),h.getChildContext())),S&&!p&&null!=h.getSnapshotBeforeUpdate&&(_=h.getSnapshotBeforeUpdate(y,v)),L=a,null!=a&&a.type===k&&null==a.key&&(L=N(a.props.children)),f=I(n,w(L)?L:[L],u,t,i,r,o,e,f,c,s),h.base=u.__e,u.__u&=-161,h.__h.length&&e.push(h),m&&(h.__E=h.__=null)}catch(n){if(u.__v=null,c||null!=o)if(n.then){for(u.__u|=c?160:128;f&&8==f.nodeType&&f.nextSibling;)f=f.nextSibling;o[o.indexOf(f)]=null,u.__e=f}else for(T=o.length;T--;)g(o[T]);else u.__e=t.__e,u.__k=t.__k;l.__e(n,u,t)}else null==o&&u.__v==t.__v?(u.__k=t.__k,u.__e=t.__e):f=u.__e=V(t.__e,u,t,i,r,o,e,c,s);return(a=l.diffed)&&a(u),128&u.__u?void 0:f}function z(n,u,t){for(var i=0;i<t.length;i++)q(t[i],t[++i],t[++i]);l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function N(n){return"object"!=typeof n||null==n||n.__b&&n.__b>0?n:w(n)?n.map(N):d({},n)}function V(u,t,i,r,o,e,f,c,s){var a,h,y,v,d,_,m,b=i.props,k=t.props,x=t.type;if("svg"==x?o="http://www.w3.org/2000/svg":"math"==x?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),null!=e)for(a=0;a<e.length;a++)if((d=e[a])&&"setAttribute"in d==!!x&&(x?d.localName==x:3==d.nodeType)){u=d,e[a]=null;break}if(null==u){if(null==x)return document.createTextNode(k);u=document.createElementNS(o,x,k.is&&k),c&&(l.__m&&l.__m(t,e),c=!1),e=null}if(null==x)b===k||c&&u.data==k||(u.data=k);else{if(e=e&&n.call(u.childNodes),b=i.props||p,!c&&null!=e)for(b={},a=0;a<u.attributes.length;a++)b[(d=u.attributes[a]).name]=d.value;for(a in b)if(d=b[a],"children"==a);else if("dangerouslySetInnerHTML"==a)y=d;else if(!(a in k)){if("value"==a&&"defaultValue"in k||"checked"==a&&"defaultChecked"in k)continue;j(u,a,null,d,o)}for(a in k)d=k[a],"children"==a?v=d:"dangerouslySetInnerHTML"==a?h=d:"value"==a?_=d:"checked"==a?m=d:c&&"function"!=typeof d||b[a]===d||j(u,a,d,b[a],o);if(h)c||y&&(h.__html==y.__html||h.__html==u.innerHTML)||(u.innerHTML=h.__html),t.__k=[];else if(y&&(u.innerHTML=""),I("template"==t.type?u.content:u,w(v)?v:[v],t,i,r,"foreignObject"==x?"http://www.w3.org/1999/xhtml":o,e,f,e?e[0]:i.__k&&S(i,0),c,s),null!=e)for(a=e.length;a--;)g(e[a]);c||(a="value","progress"==x&&null==_?u.removeAttribute("value"):null!=_&&(_!==u[a]||"progress"==x&&!_||"option"==x&&_!=b[a])&&j(u,a,_,b[a],o),a="checked",null!=m&&m!=u[a]&&j(u,a,m,b[a],o))}return u}function q(n,u,t){try{if("function"==typeof n){var i="function"==typeof n.__u;i&&n.__u(),i&&null==u||(n.__u=n(u))}else n.current=u}catch(n){l.__e(n,t)}}function B(n,u,t){var i,r;if(l.unmount&&l.unmount(n),(i=n.ref)&&(i.current&&i.current!=n.__e||q(i,null,u)),null!=(i=n.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(n){l.__e(n,u)}i.base=i.__P=null}if(i=n.__k)for(r=0;r<i.length;r++)i[r]&&B(i[r],u,t||"function"!=typeof n.type);t||g(n.__e),n.__c=n.__=n.__e=void 0}function D(n,l,u){return this.constructor(n,u)}function E(u,t,i){var r,o,e,f;t==document&&(t=document.documentElement),l.__&&l.__(u,t),o=(r="function"==typeof i)?null:i&&i.__k||t.__k,e=[],f=[],O(t,u=(!r&&i||t).__k=_(k,null,[u]),o||p,p,t.namespaceURI,!r&&i?[i]:o?null:t.firstChild?n.call(t.childNodes):null,e,!r&&i?i:o?o.__e:t.firstChild,r,f),z(e,u,f)}function G(n,l){E(n,l,G)}function J(l,u,t){var i,r,o,e,f=d({},l.props);for(o in l.type&&l.type.defaultProps&&(e=l.type.defaultProps),u)"key"==o?i=u[o]:"ref"==o?r=u[o]:f[o]=null==u[o]&&null!=e?e[o]:u[o];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):t),m(l.type,f,i||l.key,r||l.ref,null)}function K(n){function l(n){var u,t;return this.getChildContext||(u=new Set,(t={})[l.__c]=this,this.getChildContext=function(){return t},this.componentWillUnmount=function(){u=null},this.shouldComponentUpdate=function(n){this.props.value!=n.value&&u.forEach(function(n){n.__e=!0,M(n)})},this.sub=function(n){u.add(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u&&u.delete(n),l&&l.call(n)}}),n.children}return l.__c="__cC"+h++,l.__=n,l.Provider=l.__l=(l.Consumer=function(n,l){return n.children(l)}).contextType=l,l}n=y.slice,l={__e:function(n,l,u,t){for(var i,r,o;l=l.__;)if((i=l.__c)&&!i.__)try{if((r=i.constructor)&&null!=r.getDerivedStateFromError&&(i.setState(r.getDerivedStateFromError(n)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(n,t||{}),o=i.__d),o)return i.__E=i}catch(l){n=l}throw n}},u=0,t=function(n){return null!=n&&null==n.constructor},x.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=d({},this.state),"function"==typeof n&&(n=n(d({},u),this.props)),n&&d(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),M(this))},x.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),M(this))},x.prototype.render=k,i=[],o="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,e=function(n,l){return n.__v.__b-l.__v.__b},$.__r=0,f=/(PointerCapture)$|Capture$/i,c=0,s=F(!1),a=F(!0),h=0;\n//# sourceMappingURL=preact.module.js.map\n\n\n//# sourceURL=webpack://Materialize/./node_modules/preact/dist/preact.module.js?')}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](n,n.exports,__webpack_require__),n.exports}__webpack_require__.d=function(e,t){for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/@algolia/autocomplete-js.js");return __webpack_exports__}()}));