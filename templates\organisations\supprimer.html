{% extends 'base.html' %}
{% load static %}

{% block title %}Supprimer {{ organisation.nom }}{% endblock %}

{% block breadcrumb %}
<div class="row">
  <div class="col-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Accueil</a>
        </li>
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Organisations</a>
        </li>
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:detail' organisation.pk %}">{{ organisation.nom }}</a>
        </li>
        <li class="breadcrumb-item active">Supprimer</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <!-- En-tête de page -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h4 class="fw-bold py-3 mb-2 text-danger">
          <i class="ti ti-trash me-2"></i>Supprimer l'organisation
        </h4>
        <p class="text-muted">
          Cette action est irréversible
        </p>
      </div>
      <div>
        <a href="{% url 'organisations:detail' organisation.pk %}" class="btn btn-outline-secondary">
          <i class="ti ti-arrow-left me-1"></i>Retour
        </a>
      </div>
    </div>

    <div class="row justify-content-center">
      <div class="col-xl-6 col-lg-8 col-md-10">
        <!-- Avertissement de suppression -->
        <div class="card border-danger">
          <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0 text-white">
              <i class="ti ti-alert-triangle me-2"></i>Confirmation de suppression
            </h5>
          </div>
          <div class="card-body">
            <div class="alert alert-danger" role="alert">
              <h6 class="alert-heading">
                <i class="ti ti-exclamation-mark me-1"></i>Attention !
              </h6>
              <p class="mb-0">
                Vous êtes sur le point de supprimer définitivement l'organisation 
                <strong>"{{ organisation.nom }}"</strong>. Cette action ne peut pas être annulée.
              </p>
            </div>

            <!-- Informations de l'organisation à supprimer -->
            <div class="card bg-light">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="ti ti-building me-2"></i>{{ organisation.nom }}
                </h6>
                <div class="row">
                  <div class="col-sm-4 text-muted">
                    <strong>Email :</strong>
                  </div>
                  <div class="col-sm-8">
                    {{ organisation.email }}
                  </div>
                </div>
                <hr class="my-2">
                <div class="row">
                  <div class="col-sm-4 text-muted">
                    <strong>Téléphone :</strong>
                  </div>
                  <div class="col-sm-8">
                    {{ organisation.telephone }}
                  </div>
                </div>
                <hr class="my-2">
                <div class="row">
                  <div class="col-sm-4 text-muted">
                    <strong>Abonnement :</strong>
                  </div>
                  <div class="col-sm-8">
                    {% if organisation.type_abonnement == 'PREMIUM' %}
                      <span class="badge bg-label-success">Premium</span>
                    {% else %}
                      <span class="badge bg-label-secondary">Gratuit</span>
                    {% endif %}
                  </div>
                </div>
                <hr class="my-2">
                <div class="row">
                  <div class="col-sm-4 text-muted">
                    <strong>Utilisateurs :</strong>
                  </div>
                  <div class="col-sm-8">
                    {{ organisation.get_nombre_utilisateurs }} membre{{ organisation.get_nombre_utilisateurs|pluralize }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Conséquences de la suppression -->
            <div class="mt-4">
              <h6 class="text-danger">
                <i class="ti ti-info-circle me-1"></i>Conséquences de cette suppression :
              </h6>
              <ul class="text-muted">
                <li>L'organisation sera définitivement supprimée</li>
                <li>Tous les membres seront retirés de l'organisation</li>
                <li>Les données associées seront perdues</li>
                <li>Cette action ne peut pas être annulée</li>
              </ul>
            </div>

            <!-- Formulaire de confirmation -->
            <form method="post" class="mt-4">
              {% csrf_token %}
              
              <div class="mb-3">
                <label for="confirmation" class="form-label">
                  Pour confirmer, tapez le nom de l'organisation : <strong>{{ organisation.nom }}</strong>
                </label>
                <input 
                  type="text" 
                  class="form-control" 
                  id="confirmation" 
                  name="confirmation"
                  placeholder="Tapez le nom exact de l'organisation"
                  required
                >
                <div class="form-text text-danger">
                  Cette vérification permet d'éviter les suppressions accidentelles.
                </div>
              </div>

              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'organisations:detail' organisation.pk %}" class="btn btn-outline-secondary">
                  <i class="ti ti-x me-1"></i>Annuler
                </a>
                <button type="submit" class="btn btn-danger" id="btnSupprimer" disabled>
                  <i class="ti ti-trash me-1"></i>Supprimer définitivement
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Alternatives à la suppression -->
        <div class="card mt-4">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ti ti-lightbulb me-2"></i>Alternatives à la suppression
            </h6>
          </div>
          <div class="card-body">
            <p class="text-muted mb-3">
              Plutôt que de supprimer l'organisation, vous pourriez :
            </p>
            <div class="d-grid gap-2">
              <a href="{% url 'organisations:modifier' organisation.pk %}" class="btn btn-outline-warning">
                <i class="ti ti-edit me-1"></i>Désactiver l'organisation temporairement
              </a>
              <button class="btn btn-outline-info">
                <i class="ti ti-archive me-1"></i>Archiver l'organisation
              </button>
              <button class="btn btn-outline-secondary">
                <i class="ti ti-download me-1"></i>Exporter les données avant suppression
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmationInput = document.getElementById('confirmation');
    const btnSupprimer = document.getElementById('btnSupprimer');
    const nomOrganisation = "{{ organisation.nom }}";

    // Activer/désactiver le bouton de suppression selon la saisie
    confirmationInput.addEventListener('input', function() {
        if (this.value.trim() === nomOrganisation) {
            btnSupprimer.disabled = false;
            btnSupprimer.classList.remove('btn-outline-danger');
            btnSupprimer.classList.add('btn-danger');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            btnSupprimer.disabled = true;
            btnSupprimer.classList.remove('btn-danger');
            btnSupprimer.classList.add('btn-outline-danger');
            this.classList.remove('is-valid');
            if (this.value.trim() !== '') {
                this.classList.add('is-invalid');
            }
        }
    });

    // Confirmation finale avant soumission
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!confirm('Êtes-vous absolument certain de vouloir supprimer cette organisation ? Cette action est irréversible.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
