/* Logistics Fleet
******************************************************************************* */

@import "../_bootstrap-extended/include";
@import "../_components/include";

// Custom variables for Logistics-fleet page
$logistics-fleet-sidebar-width: 20.75rem !default;
$logistics-fleet-height: calc(100vh - 10.3rem);
$logistics-fleet-sidebar-height: calc(100vh - 13.5rem);
$logistics-fleet-horizontal-height-diff: 2rem;

/* App logistics-fleet wrapper for sidebar */
.app-logistics-fleet-wrapper {
  position: relative;
  overflow: hidden;
  border: none;
  block-size: $logistics-fleet-height;

  @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
    & {
      block-size: calc(100vh - 10.5rem);
    }
  }

  /* setting wrapper height when navbar is hidden */
  .layout-navbar-hidden & {
    block-size: calc(100vh - 6.5rem);

    // For Sidebar Map menu Scroll
    .logistics-fleet-sidebar-body {
      block-size: calc($logistics-fleet-sidebar-height + 4rem);
    }
  }

  /* Sidebar functionality */
  .app-logistics-fleet-sidebar {
    position: absolute;
    z-index: 2;
    overflow: hidden;
    flex-basis: $logistics-fleet-sidebar-width;
    flex-grow: 0;
    background-color: var(--#{$prefix}paper-bg);
    block-size: 100%;
    inline-size: $logistics-fleet-sidebar-width;
    inset-inline-start: calc(-#{$logistics-fleet-sidebar-width} - 1.2rem);
    transition: all .3s;

    &.show {
      inset-inline-start: 0;
    }
  }

  /* Close sidebar button */
  .close-sidebar {
    position: absolute;
    background-color: var(--#{$prefix}heading-color);
    inset-block-start: .5rem;
    inset-inline-end: .5rem;
  }
  // this radius should be according to card radius
  #map {
    @include media-breakpoint-down(md) {
      @include border-radius(.625rem);
    }
  }

  /* Setting sidebar height */
  .logistics-fleet-sidebar-body {
    block-size: calc(100vh - 15rem);
    @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
      block-size: $logistics-fleet-sidebar-height;
    }
  }

  @include media-breakpoint-down(md) {
    /* Setting wrapper height when screen < md */
    & {
      block-size: calc(100vh - 11.5rem);

      /* For Sidebar Map menu Scroll */
      .logistics-fleet-sidebar-body {
        block-size: $logistics-fleet-sidebar-height;
      }
    }

    /* Setting wrapper height when navbar is hidden */
    .layout-navbar-hidden & {
      block-size: calc(100vh - 7.5rem);

      /* For Sidebar Map menu Scroll */
      .logistics-fleet-sidebar-body {
        block-size: calc($logistics-fleet-sidebar-height + 2.5rem);
      }
    }
  }

  /* For Sidebar Map menu Button to open in full screen */
  @include media-breakpoint-up(md) {
    .app-logistics-fleet-sidebar {
      position: static;
      background-color: transparent;
      block-size: auto;
    }
  }

  .btn-white.btn-white-dark-variant {
    box-shadow: var(--#{$prefix}box-shadow-xs);
    color: $headings-color;
  }
}
