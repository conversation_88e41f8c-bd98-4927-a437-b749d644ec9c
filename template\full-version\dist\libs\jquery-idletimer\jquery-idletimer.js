!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(self,(function(){return function(){var __webpack_modules__={"./libs/jquery-idletimer/jquery-idletimer.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var jquery_idletimer_src_idle_timer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery-idletimer/src/idle-timer */ \"./node_modules/jquery-idletimer/src/idle-timer.js\");\n/* harmony import */ var jquery_idletimer_src_idle_timer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery_idletimer_src_idle_timer__WEBPACK_IMPORTED_MODULE_0__);\n// import 'src/jquery-idletimer';\n\n\n//# sourceURL=webpack://Materialize/./libs/jquery-idletimer/jquery-idletimer.js?")},"./node_modules/jquery-idletimer/src/idle-timer.js":function(){eval('/*\n * Copyright (c) 2009 Nicholas C. Zakas\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the "Software"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sub-license, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/*\n\tmousewheel (deprecated) -> IE6.0, Chrome, Opera, Safari\n\tDOMMouseScroll (deprecated) -> Firefox 1.0\n\twheel (standard) -> Chrome 31, Firefox 17, IE9, Firefox Mobile 17.0\n\n\t//No need to use, use DOMMouseScroll\n\tMozMousePixelScroll -> Firefox 3.5, Firefox Mobile 1.0\n\n\t//Events\n\tWheelEvent -> see wheel\n\tMouseWheelEvent -> see mousewheel\n\tMouseScrollEvent -> Firefox 3.5, Firefox Mobile 1.0\n*/\n(function ($) {\n\n    $.idleTimer = function (firstParam, elem, uniqueId) {\n        var opts;\n        if ( typeof firstParam === "object" ) {\n            opts = firstParam;\n            firstParam = null;\n        } else if (typeof firstParam === "number") {\n            opts = { timeout: firstParam };\n            firstParam = null;\n        }\n\n        // element to watch\n        elem = elem || document;\n\n        uniqueId = uniqueId || "";\n\n        // defaults that are to be stored as instance props on the elem\n        opts = $.extend({\n            idle: false,                // indicates if the user is idle\n            timeout: 30000,             // the amount of time (ms) before the user is considered idle\n            events: "mousemove keydown wheel DOMMouseScroll mousewheel mousedown touchstart touchmove MSPointerDown MSPointerMove" // define active events\n        }, opts);\n\n        var jqElem = $(elem),\n            obj = jqElem.data("idleTimerObj" + uniqueId) || {},\n\n            /* (intentionally not documented)\n             * Toggles the idle state and fires an appropriate event.\n             * @return {void}\n             */\n            toggleIdleState = function (e) {\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n\n                // toggle the state\n                obj.idle = !obj.idle;\n\n                // store toggle state date time\n                obj.olddate = +new Date();\n\n                // create a custom event, with state and name space\n                var event = $.Event((obj.idle ? "idle" : "active") + ".idleTimer" + uniqueId);\n\n                // trigger event on object with elem and copy of obj\n                $(elem).trigger(event, [elem, $.extend({}, obj), e]);\n            },\n            /**\n             * Handle event triggers\n             * @return {void}\n             * @method event\n             * @static\n             */\n            handleEvent = function (e) {\n\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n                \n\t\t            // ignore writting to storage unless related to idleTimer\n                if (e.type === "storage" && e.originalEvent.key !== obj.timerSyncId) {\n                    return;\n                }\n\n                // this is already paused, ignore events for now\n                if (obj.remaining != null) { return; }\n\n                /*\n                mousemove is kinda buggy, it can be triggered when it should be idle.\n                Typically is happening between 115 - 150 milliseconds after idle triggered.\n                @psyafter & @kaellis report "always triggered if using modal (jQuery ui, with overlay)"\n                @thorst has similar issues on ios7 "after $.scrollTop() on text area"\n                */\n                if (e.type === "mousemove") {\n                    // if coord are same, it didn\'t move\n                    if (e.pageX === obj.pageX && e.pageY === obj.pageY) {\n                        return;\n                    }\n                    // if coord don\'t exist how could it move\n                    if (typeof e.pageX === "undefined" && typeof e.pageY === "undefined") {\n                        return;\n                    }\n                    // under 200 ms is hard to do, and you would have to stop, as continuous activity will bypass this\n                    var elapsed = (+new Date()) - obj.olddate;\n                    if (elapsed < 200) {\n                        return;\n                    }\n                }\n\n                // clear any existing timeout\n                clearTimeout(obj.tId);\n\n                // if the idle timer is enabled, flip\n                if (obj.idle) {\n                    toggleIdleState(e);\n                }\n\n                // store when user was last active\n                obj.lastActive = +new Date();\n\n                // update mouse coord\n                obj.pageX = e.pageX;\n                obj.pageY = e.pageY;\n\n                // sync lastActive\n                if (e.type !== "storage" && obj.timerSyncId) {\n                  if (typeof(localStorage) !== "undefined") {\n                    localStorage.setItem(obj.timerSyncId, obj.lastActive);\n                  }\n                }\n\n                // set a new timeout\n                obj.tId = setTimeout(toggleIdleState, obj.timeout);\n            },\n            /**\n             * Restore initial settings and restart timer\n             * @return {void}\n             * @method reset\n             * @static\n             */\n            reset = function () {\n\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n\n                // reset settings\n                obj.idle = obj.idleBackup;\n                obj.olddate = +new Date();\n                obj.lastActive = obj.olddate;\n                obj.remaining = null;\n\n                // reset Timers\n                clearTimeout(obj.tId);\n                if (!obj.idle) {\n                    obj.tId = setTimeout(toggleIdleState, obj.timeout);\n                }\n\n            },\n            /**\n             * Store remaining time, stop timer\n             * You can pause from an idle OR active state\n             * @return {void}\n             * @method pause\n             * @static\n             */\n            pause = function () {\n\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n\n                // this is already paused\n                if ( obj.remaining != null ) { return; }\n\n                // define how much is left on the timer\n                obj.remaining = obj.timeout - ((+new Date()) - obj.olddate);\n\n                // clear any existing timeout\n                clearTimeout(obj.tId);\n            },\n            /**\n             * Start timer with remaining value\n             * @return {void}\n             * @method resume\n             * @static\n             */\n            resume = function () {\n\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n\n                // this isn\'t paused yet\n                if ( obj.remaining == null ) { return; }\n\n                // start timer\n                if ( !obj.idle ) {\n                    obj.tId = setTimeout(toggleIdleState, obj.remaining);\n                }\n\n                // clear remaining\n                obj.remaining = null;\n            },\n            /**\n             * Stops the idle timer. This removes appropriate event handlers\n             * and cancels any pending timeouts.\n             * @return {void}\n             * @method destroy\n             * @static\n             */\n            destroy = function () {\n\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n\n                //clear any pending timeouts\n                clearTimeout(obj.tId);\n\n                //Remove data\n                jqElem.removeData("idleTimerObj" + uniqueId);\n\n                //detach the event handlers\n                jqElem.off("._idleTimer" + uniqueId);\n            },\n            /**\n            * Returns the time until becoming idle\n            * @return {number}\n            * @method remainingtime\n            * @static\n            */\n            remainingtime = function () {\n\n                var obj = $.data(elem, "idleTimerObj" + uniqueId) || {};\n\n                //If idle there is no time remaining\n                if ( obj.idle ) { return 0; }\n\n                //If its paused just return that\n                if ( obj.remaining != null ) { return obj.remaining; }\n\n                //Determine remaining, if negative idle didn\'t finish flipping, just return 0\n                var remaining = obj.timeout - ((+new Date()) - obj.lastActive);\n                if (remaining < 0) { remaining = 0; }\n\n                //If this is paused return that number, else return current remaining\n                return remaining;\n            };\n\n\n        // determine which function to call\n        if (firstParam === null && typeof obj.idle !== "undefined") {\n            // they think they want to init, but it already is, just reset\n            reset();\n            return jqElem;\n        } else if (firstParam === null) {\n            // they want to init\n        } else if (firstParam !== null && typeof obj.idle === "undefined") {\n            // they want to do something, but it isnt init\n            // not sure the best way to handle this\n            return false;\n        } else if (firstParam === "destroy") {\n            destroy();\n            return jqElem;\n        } else if (firstParam === "pause") {\n            pause();\n            return jqElem;\n        } else if (firstParam === "resume") {\n            resume();\n            return jqElem;\n        } else if (firstParam === "reset") {\n            reset();\n            return jqElem;\n        } else if (firstParam === "getRemainingTime") {\n            return remainingtime();\n        } else if (firstParam === "getElapsedTime") {\n            return (+new Date()) - obj.olddate;\n        } else if (firstParam === "getLastActiveTime") {\n            return obj.lastActive;\n        } else if (firstParam === "isIdle") {\n            return obj.idle;\n        }\n\n\t// Test via a getter in the options object to see if the passive property is accessed\n\t// This isnt working in jquery, though is planned for 4.0\n\t// https://github.com/jquery/jquery/issues/2871\n        /*var supportsPassive = false;\n        try {\n            var Popts = Object.defineProperty({}, "passive", {\n                get: function() {\n                    supportsPassive = true;\n                }\n            });\n            window.addEventListener("test", null, Popts);\n        } catch (e) {}\n\t*/\n\n        /* (intentionally not documented)\n         * Handles a user event indicating that the user isn\'t idle. namespaced with internal idleTimer\n         * @param {Event} event A DOM2-normalized event object.\n         * @return {void}\n         */\n        jqElem.on((opts.events + " ").split(" ").join("._idleTimer" + uniqueId + " ").trim(), function (e) {\n\n            handleEvent(e);\n        });\n        //}, supportsPassive ? { passive: true } : false);\n\n        if (opts.timerSyncId) {\n            $(window).on("storage", handleEvent);\n        }\n\n        // Internal Object Properties, This isn\'t all necessary, but we\n        // explicitly define all keys here so we know what we are working with\n        obj = $.extend({}, {\n            olddate : +new Date(),          // the last time state changed\n            lastActive: +new Date(),       // the last time timer was active\n            idle : opts.idle,               // current state\n            idleBackup : opts.idle,         // backup of idle parameter since it gets modified\n            timeout : opts.timeout,         // the interval to change state\n            remaining : null,               // how long until state changes\n            timerSyncId : opts.timerSyncId, // localStorage key to use for syncing this timer\n            tId : null,                     // the idle timer setTimeout\n            pageX : null,                   // used to store the mouse coord\n            pageY : null\n        });\n\n        // set a timeout to toggle state. May wish to omit this in some situations\n        if (!obj.idle) {\n            obj.tId = setTimeout(toggleIdleState, obj.timeout);\n        }\n\n        // store our instance on the object\n        $.data(elem, "idleTimerObj" + uniqueId, obj);\n\n        return jqElem;\n    };\n\n    // This allows binding to element\n    $.fn.idleTimer = function (firstParam, uniqueId) {\n        if (this[0]) {\n            return $.idleTimer(firstParam, this[0], uniqueId);\n        }\n\n        return this;\n    };\n\n})(jQuery);\n\n\n//# sourceURL=webpack://Materialize/./node_modules/jquery-idletimer/src/idle-timer.js?')}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(n,{a:n}),n},__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/jquery-idletimer/jquery-idletimer.js");return __webpack_exports__}()}));