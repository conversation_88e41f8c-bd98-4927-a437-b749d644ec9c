@charset "UTF-8";
.ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
  counter-reset:  list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-1::before {
  content: counter(list-1, lower-alpha) ". ";
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-inline-start: 2rem;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-inline-start: 4rem;
}
.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-inline-end: 2rem;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-inline-end: 4rem;
}
.ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
  counter-reset:  list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2::before {
  content: counter(list-2, lower-roman) ". ";
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-inline-start: 4rem;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-inline-start: 6rem;
}
.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-inline-end: 4rem;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-inline-end: 6rem;
}
.ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
  counter-reset:  list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3::before {
  content: counter(list-3, decimal) ". ";
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-inline-start: 6rem;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-inline-start: 8rem;
}
.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-inline-end: 6rem;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-inline-end: 8rem;
}
.ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
  counter-reset:  list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4::before {
  content: counter(list-4, lower-alpha) ". ";
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-inline-start: 8rem;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-inline-start: 10rem;
}
.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-inline-end: 8rem;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-inline-end: 10rem;
}
.ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
  counter-reset:  list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5::before {
  content: counter(list-5, lower-roman) ". ";
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-inline-start: 10rem;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-inline-start: 12rem;
}
.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-inline-end: 10rem;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-inline-end: 12rem;
}
.ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
  counter-reset:  list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6::before {
  content: counter(list-6, decimal) ". ";
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-inline-start: 12rem;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-inline-start: 14rem;
}
.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-inline-end: 12rem;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-inline-end: 14rem;
}
.ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
  counter-reset:  list-8 list-9;
}
.ql-editor ol li.ql-indent-7::before {
  content: counter(list-7, lower-alpha) ". ";
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-inline-start: 14rem;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-inline-start: 16rem;
}
.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-inline-end: 14rem;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-inline-end: 16rem;
}
.ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
  counter-reset:  list-9;
}
.ql-editor ol li.ql-indent-8::before {
  content: counter(list-8, lower-roman) ". ";
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-inline-start: 16rem;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-inline-start: 18rem;
}
.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-inline-end: 16rem;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-inline-end: 18rem;
}
.ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9::before {
  content: counter(list-9, decimal) ". ";
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-inline-start: 18rem;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-inline-start: 20rem;
}
.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-inline-end: 18rem;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-inline-end: 20rem;
}
.ql-editor .ql-video {
  display: block;
  max-inline-size: 100%;
}
.ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
:dir(rtl) .ql-editor .ql-direction-rtl {
  direction: ltr;
  text-align: inherit;
}
.ql-editor .ql-align-center {
  text-align: center;
}
.ql-editor .ql-align-justify {
  text-align: justify;
}
.ql-editor .ql-align-right:not(.ql-direction-rtl) {
  text-align: end;
}
.ql-editor img {
  max-inline-size: 100%;
}
.ql-editor blockquote {
  font-size: 0.9375rem;
}
.ql-editor .ql-font-serif {
  font-family: georgia, "Times New Roman", serif;
}
.ql-editor .ql-font-monospace {
  font-family: "SFMono-Regular", menlo, monaco, consolas, "Liberation Mono", "Courier New", monospace;
}
.ql-editor .ql-size-large {
  font-size: 1.0625rem;
}
.ql-editor .ql-size-huge {
  font-size: 1.25rem;
}
.ql-editor .ql-size-small {
  font-size: 0.8125rem;
}

.ql-content ul li[data-list=bullet],
.ql-content ol li[data-list=bullet],
.ql-editor ul li[data-list=bullet],
.ql-editor ol li[data-list=bullet] {
  position: relative;
  list-style: none;
  padding-inline-start: 1em;
}
.ql-content ul > li[data-list=bullet]::before,
.ql-content ol > li[data-list=bullet]::before,
.ql-editor ul > li[data-list=bullet]::before,
.ql-editor ol > li[data-list=bullet]::before {
  position: absolute;
  content: "•";
  font-size: 1em;
  inset-inline-start: 0;
}
.ql-content li::before,
.ql-editor li::before {
  display: inline-block;
  inline-size: calc(2rem - 0.3em);
  white-space: nowrap;
}
