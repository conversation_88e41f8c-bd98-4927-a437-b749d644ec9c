<!doctype html>
<html lang="fr" class="layout-navbar-fixed layout-menu-fixed layout-compact" dir="ltr" data-skin="default" data-bs-theme="light">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <title>Demande d'inscription - Hospital SaaS</title>
    <meta name="description" content="Demandez l'inscription de votre établissement hospitalier" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap" rel="stylesheet" />

    <link rel="stylesheet" href="/static/vendor/fonts/iconify-icons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/node-waves/node-waves.css" />
    <link rel="stylesheet" href="/static/vendor/css/core.css" />
    <link rel="stylesheet" href="/static/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- Helpers -->
    <script src="/static/js/helpers.js"></script>
    <script src="/static/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        
        <!-- Content wrapper -->
        <div class="content-wrapper">
          <!-- Content -->
          <div class="container-xxl flex-grow-1 container-p-y">
            
            <!-- Header -->
            <div class="row justify-content-center">
              <div class="col-xl-8 col-lg-10">
                <div class="text-center mb-4">
                  <a href="{% url 'accounts:accueil' %}" class="text-decoration-none">
                    <i class="ti ti-building-hospital ti-48px text-primary mb-3"></i>
                    <h2 class="fw-bold text-primary">Hospital SaaS</h2>
                  </a>
                  <h3 class="h4 mb-2">Demande d'inscription</h3>
                  <p class="text-muted">
                    Rejoignez les établissements de santé qui font confiance à Hospital SaaS
                  </p>
                </div>
              </div>
            </div>

            <!-- Messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="row justify-content-center mb-4">
                  <div class="col-xl-8 col-lg-10">
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                      {{ message }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            {% endif %}

            <!-- Formulaire d'onboarding -->
            <div class="row justify-content-center">
              <div class="col-xl-8 col-lg-10">
                <div class="card">
                  <div class="card-body p-4">
                    <form method="post" novalidate>
                      {% csrf_token %}
                      
                      <!-- Erreurs générales -->
                      {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                          <h6 class="alert-heading">
                            <i class="ti ti-alert-circle me-1"></i>Erreurs de validation
                          </h6>
                          {% for error in form.non_field_errors %}
                            <div>{{ error }}</div>
                          {% endfor %}
                        </div>
                      {% endif %}

                      <!-- Section Organisation -->
                      <div class="mb-5">
                        <h5 class="card-title mb-3">
                          <i class="ti ti-building me-2 text-primary"></i>Informations de l'établissement
                        </h5>
                        
                        <div class="row">
                          <div class="col-12 mb-3">
                            <label for="{{ form.nom_organisation.id_for_label }}" class="form-label">
                              {{ form.nom_organisation.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.nom_organisation }}
                            {% if form.nom_organisation.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.nom_organisation.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-12 mb-3">
                            <label for="{{ form.adresse_organisation.id_for_label }}" class="form-label">
                              {{ form.adresse_organisation.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.adresse_organisation }}
                            {% if form.adresse_organisation.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.adresse_organisation.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.telephone_organisation.id_for_label }}" class="form-label">
                              {{ form.telephone_organisation.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.telephone_organisation }}
                            {% if form.telephone_organisation.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.telephone_organisation.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                            <div class="form-text">
                              <small class="text-muted">Format international requis (ex: +225 01 23 45 67 89)</small>
                            </div>
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.email_organisation.id_for_label }}" class="form-label">
                              {{ form.email_organisation.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.email_organisation }}
                            {% if form.email_organisation.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.email_organisation.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.type_abonnement_demande.id_for_label }}" class="form-label">
                              {{ form.type_abonnement_demande.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.type_abonnement_demande }}
                            {% if form.type_abonnement_demande.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.type_abonnement_demande.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>
                        </div>
                      </div>

                      <!-- Section Administrateur -->
                      <div class="mb-5">
                        <h5 class="card-title mb-3">
                          <i class="ti ti-user-shield me-2 text-success"></i>Informations de l'administrateur
                        </h5>
                        <p class="text-muted mb-4">
                          Cette personne sera l'administrateur principal de l'organisation et pourra gérer les autres utilisateurs.
                        </p>
                        
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="{{ form.prenom_admin.id_for_label }}" class="form-label">
                              {{ form.prenom_admin.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.prenom_admin }}
                            {% if form.prenom_admin.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.prenom_admin.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.nom_admin.id_for_label }}" class="form-label">
                              {{ form.nom_admin.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.nom_admin }}
                            {% if form.nom_admin.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.nom_admin.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.email_admin.id_for_label }}" class="form-label">
                              {{ form.email_admin.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.email_admin }}
                            {% if form.email_admin.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.email_admin.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                            <div class="form-text">
                              <small class="text-muted">Cette adresse servira pour la connexion</small>
                            </div>
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.telephone_admin.id_for_label }}" class="form-label">
                              {{ form.telephone_admin.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.telephone_admin }}
                            {% if form.telephone_admin.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.telephone_admin.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.mot_de_passe.id_for_label }}" class="form-label">
                              {{ form.mot_de_passe.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.mot_de_passe }}
                            {% if form.mot_de_passe.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.mot_de_passe.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.confirmer_mot_de_passe.id_for_label }}" class="form-label">
                              {{ form.confirmer_mot_de_passe.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.confirmer_mot_de_passe }}
                            {% if form.confirmer_mot_de_passe.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.confirmer_mot_de_passe.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>
                        </div>
                      </div>

                      <!-- Conditions d'utilisation -->
                      <div class="mb-4">
                        <div class="form-check">
                          {{ form.accepter_conditions }}
                          <label class="form-check-label" for="{{ form.accepter_conditions.id_for_label }}">
                            {{ form.accepter_conditions.label }}
                          </label>
                        </div>
                        {% if form.accepter_conditions.errors %}
                          <div class="invalid-feedback d-block">
                            {% for error in form.accepter_conditions.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                      </div>

                      <!-- Informations sur les abonnements -->
                      <div class="alert alert-info mb-4" role="alert">
                        <h6 class="alert-heading">
                          <i class="ti ti-info-circle me-1"></i>Types d'abonnement
                        </h6>
                        <div class="row">
                          <div class="col-md-6">
                            <strong>Gratuit :</strong>
                            <ul class="mb-0 mt-1">
                              <li>Jusqu'à 5 utilisateurs</li>
                              <li>Fonctionnalités de base</li>
                              <li>Support par email</li>
                            </ul>
                          </div>
                          <div class="col-md-6">
                            <strong>Premium :</strong>
                            <ul class="mb-0 mt-1">
                              <li>Utilisateurs illimités</li>
                              <li>Toutes les fonctionnalités</li>
                              <li>Support prioritaire</li>
                              <li>Rapports avancés</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <!-- Boutons d'action -->
                      <div class="d-flex justify-content-between">
                        <a href="{% url 'accounts:accueil' %}" class="btn btn-outline-secondary">
                          <i class="ti ti-arrow-left me-1"></i>Retour à l'accueil
                        </a>
                        <button type="submit" class="btn btn-primary">
                          <i class="ti ti-send me-1"></i>Soumettre la demande
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <!-- / Content -->

          <!-- Footer -->
          <footer class="content-footer footer bg-footer-theme mt-5">
            <div class="container-xxl">
              <div class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                <div class="text-body">
                  © 2025 Hospital SaaS. Tous droits réservés.
                </div>
                <div class="d-none d-lg-inline-block">
                  <a href="#" class="footer-link me-4">Support</a>
                  <a href="#" class="footer-link me-4">Documentation</a>
                  <a href="#" class="footer-link">Contact</a>
                </div>
              </div>
            </div>
          </footer>
          <!-- / Footer -->

        </div>
        <!-- Content wrapper -->
      </div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <script src="/static/vendor/libs/jquery/jquery.js"></script>
    <script src="/static/vendor/libs/popper/popper.js"></script>
    <script src="/static/js/bootstrap.js"></script>
    <script src="/static/vendor/libs/node-waves/node-waves.js"></script>
    <script src="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="/static/vendor/libs/hammer/hammer.js"></script>
    <script src="/static/js/menu.js"></script>

    <!-- Main JS -->
    <script src="/static/js/main.js"></script>

    <!-- Validation JS -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const motDePasseField = document.getElementById('{{ form.mot_de_passe.id_for_label }}');
        const confirmerMotDePasseField = document.getElementById('{{ form.confirmer_mot_de_passe.id_for_label }}');
        
        // Validation des mots de passe en temps réel
        function validatePasswords() {
            if (motDePasseField.value && confirmerMotDePasseField.value) {
                if (motDePasseField.value !== confirmerMotDePasseField.value) {
                    confirmerMotDePasseField.classList.add('is-invalid');
                    confirmerMotDePasseField.classList.remove('is-valid');
                } else {
                    confirmerMotDePasseField.classList.remove('is-invalid');
                    confirmerMotDePasseField.classList.add('is-valid');
                }
            }
        }
        
        if (motDePasseField && confirmerMotDePasseField) {
            motDePasseField.addEventListener('input', validatePasswords);
            confirmerMotDePasseField.addEventListener('input', validatePasswords);
        }
        
        // Validation des téléphones
        const telephoneFields = document.querySelectorAll('input[type="text"][id*="telephone"]');
        telephoneFields.forEach(field => {
            field.addEventListener('blur', function() {
                const phoneRegex = /^\+[1-9]\d{1,14}$/;
                if (this.value && !phoneRegex.test(this.value.replace(/\s/g, ''))) {
                    this.classList.add('is-invalid');
                } else if (this.value) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
    });
    </script>
  </body>
</html>
