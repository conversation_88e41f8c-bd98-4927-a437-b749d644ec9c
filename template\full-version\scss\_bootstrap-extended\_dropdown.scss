// Dropdowns
// *****************************************************************

.dropdown-menu {
  box-shadow: var(--#{$prefix}dropdown-box-shadow);

  text-align: start;
  > li:not(.disabled) > a:not(.dropdown-item):active,
  > li:not(.disabled) > a:not(.dropdown-item).active,
  > li.active:not(.disabled) > a:not(.dropdown-item) {
    background-color: var(--#{$prefix}dropdown-link-active-bg);
    color: var(--#{$prefix}dropdown-link-active-color);
  }

  /* Mega dropdown inside the dropdown menu */
  .mega-dropdown > & {
    inset-inline: 0;
  }

  .list-group-item{
    border-color: var(--#{$prefix}border-color);
  }
}

.btn-xs.dropdown-toggle::after {
  @include caret-down(.55em);
}

/* Split dropdowns */
.dropdown-toggle-split {
  &::after,
  .dropup &::after,
  .dropend &::after {
    margin-inline: 0;
  }
  .dropstart &::before {
    margin-inline: 0;
  }
}

/* Dropdown item line height */
.dropdown-item {
  &.waves-effect {
    .waves-ripple {
      background:
        radial-gradient(
          rgba(var(--#{$prefix}primary-rgb), .2) 0,
          rgba(var(--#{$prefix}primary-rgb), .3) 40%,
          rgba(var(--#{$prefix}primary-rgb), .4) 50%,
          rgba(var(--#{$prefix}primary-rgb), .5) 60%,
          rgba(var(--#{$prefix}white-rgb), 0) 70%
        );
    }
  }
  &.text-danger:active {
    color: var(--#{$prefix}primary) !important;
  }
}

/* Hidden dropdown toggle arrow */
.dropdown-toggle.hide-arrow,
.dropdown-toggle-hide-arrow > .dropdown-toggle {
  &::before,
  &::after {
    display: none;
  }
}

@each $breakpoint in map-keys($grid-breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

    .dropdown-menu#{$infix}-start {
      --#{$prefix}position: start;
      &[data-bs-popper] {
        inset-inline: 0 auto;
      }
    }

    .dropdown-menu#{$infix}-end {
      --#{$prefix}position: end;
      &[data-bs-popper] {
        inset-inline: auto 0;
      }
    }
  }
}
