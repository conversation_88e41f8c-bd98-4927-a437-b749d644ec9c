{"name": "Materialize", "version": "3.0.0", "private": true, "license": "Commercial", "files": [".buildrc.json", ".browserslistrc"], "scripts": {"build": "npx gulp build", "build:js": "npx gulp build:js", "build:theme": "npx gulp build:theme", "build:css": "npx gulp build:css", "build:fonts": "npx gulp build:fonts", "build:prod": "npx gulp build --env=production", "build:prod:js": "npx gulp build:js --env=production", "build:prod:css": "npx gulp build:css --env=production", "build:prod:fonts": "npx gulp build:fonts --env=production", "watch": "npx gulp watch", "serve": "npx gulp serve", "format:scss": "npx stylelint --fix \"**/*.scss\""}, "dependencies": {"@algolia/autocomplete-js": "1.19.0", "@algolia/autocomplete-theme-classic": "1.19.0", "@iconify/json": "2.2.331", "@iconify/tools": "4.1.2", "@iconify/types": "2.0.0", "@iconify/utils": "2.3.0", "@popperjs/core": "2.11.8", "@simonwep/pickr": "1.9.1", "bootstrap": "5.3.5", "hammerjs": "2.0.8", "jquery": "3.7.1", "node-waves": "0.7.6", "perfect-scrollbar": "1.5.6"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/plugin-transform-destructuring": "7.23.3", "@babel/plugin-transform-object-rest-spread": "7.23.4", "@babel/plugin-transform-template-literals": "7.23.3", "@babel/preset-env": "7.26.9", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.3", "ajv": "8.17.1", "ansi-colors": "4.1.3", "babel-loader": "9.1.3", "browser-sync": "3.0.4", "color-support": "1.1.3", "css-loader": "6.9.1", "deepmerge": "4.3.1", "del": "8.0.0", "eslint": "9.16.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.6", "fancy-log": "2.0.0", "gulp": "4.0.2", "gulp-autoprefixer": "8.0.0", "gulp-dart-sass": "1.1.0", "gulp-environment": "1.5.2", "gulp-exec": "5.0.0", "gulp-if": "3.0.0", "gulp-purgecss": "7.0.2", "gulp-rename": "2.0.0", "gulp-replace": "1.1.4", "gulp-sourcemaps": "3.0.0", "gulp-uglify": "3.0.2", "gulp-useref": "5.0.0", "html-loader": "4.2.0", "js-beautify": "1.15.4", "prettier": "3.5.3", "sass": "1.78.0", "sass-loader": "14.0.0", "string-replace-webpack-plugin": "0.1.3", "style-loader": "3.3.4", "stylelint": "16.18.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "terser-webpack-plugin": "5.3.14", "webpack": "5.89.0", "yarn": "1.22.22"}, "overrides": {"algoliasearch": "5.17.1", "@algolia/client-search": "5.17.1", "@algolia/autocomplete-plugin-algolia-insights": "1.18.1", "prop-types": "15.8.1", "postcss": "8.5.3", "search-insights": "2.17.3", "superagent": "3.8.3", "chokidar": "3.6.0", "source-map-resolve": "0.6.0", "sass": "1.78.0", "rimraf": "4.0.0"}, "resolutions": {"algoliasearch": "5.17.1", "@algolia/client-search": "5.17.1", "@algolia/autocomplete-plugin-algolia-insights": "1.18.1", "prop-types": "15.8.1", "postcss": "8.5.3", "search-insights": "2.17.3", "superagent": "3.8.3", "chokidar": "3.6.0", "source-map-resolve": "0.6.0", "sass": "1.78.0", "rimraf": "4.0.0", "eslint": "9.16.0", "react": "19.1.0", "react-dom": "19.1.0"}, "gulp-environment": {"environments": [{"name": "development", "aliases": ["dev"]}, {"name": "production", "aliases": ["prod"]}], "default": "development"}}