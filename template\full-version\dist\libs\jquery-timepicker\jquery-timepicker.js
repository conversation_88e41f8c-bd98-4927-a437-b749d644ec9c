!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n(require("jQuery"));else if("function"==typeof define&&define.amd)define(["jQuery"],n);else{var t="object"==typeof exports?n(require("jQuery")):n(e.jQuery);for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(self,(function(__WEBPACK_EXTERNAL_MODULE_jquery__){return function(){var __webpack_modules__={"./libs/jquery-timepicker/jquery-timepicker.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var timepicker_jquery_timepicker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! timepicker/jquery.timepicker */ "./node_modules/timepicker/jquery.timepicker.js");\n/* harmony import */ var timepicker_jquery_timepicker__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(timepicker_jquery_timepicker__WEBPACK_IMPORTED_MODULE_0__);\n\n\n//# sourceURL=webpack://Materialize/./libs/jquery-timepicker/jquery-timepicker.js?')},"./node_modules/timepicker/jquery.timepicker.js":function(module,exports,__webpack_require__){eval('/* module decorator */ module = __webpack_require__.nmd(module);\nvar __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*!\n * jquery-timepicker v1.14.1 - A jQuery timepicker plugin inspired by Google Calendar. It supports both mouse and keyboard navigation.\n * Copyright (c) 2021 Jon Thornton - https://www.jonthornton.com/jquery-timepicker/\n * License: MIT\n */\n(function () {\n  \'use strict\';\n\n  function _typeof(obj) {\n    "@babel/helpers - typeof";\n\n    if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError("Cannot call a class as a function");\n    }\n  }\n\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if ("value" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n  }\n\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n\n    return obj;\n  }\n\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n      if (enumerableOnly) symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n      keys.push.apply(keys, symbols);\n    }\n\n    return keys;\n  }\n\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n\n    return target;\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === "string") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === "Object" && o.constructor) n = o.constructor.name;\n    if (n === "Map" || n === "Set") return Array.from(o);\n    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _createForOfIteratorHelper(o, allowArrayLike) {\n    var it;\n\n    if (typeof Symbol === "undefined" || o[Symbol.iterator] == null) {\n      if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {\n        if (it) o = it;\n        var i = 0;\n\n        var F = function () {};\n\n        return {\n          s: F,\n          n: function () {\n            if (i >= o.length) return {\n              done: true\n            };\n            return {\n              done: false,\n              value: o[i++]\n            };\n          },\n          e: function (e) {\n            throw e;\n          },\n          f: F\n        };\n      }\n\n      throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");\n    }\n\n    var normalCompletion = true,\n        didErr = false,\n        err;\n    return {\n      s: function () {\n        it = o[Symbol.iterator]();\n      },\n      n: function () {\n        var step = it.next();\n        normalCompletion = step.done;\n        return step;\n      },\n      e: function (e) {\n        didErr = true;\n        err = e;\n      },\n      f: function () {\n        try {\n          if (!normalCompletion && it.return != null) it.return();\n        } finally {\n          if (didErr) throw err;\n        }\n      }\n    };\n  }\n\n  var ONE_DAY = 86400;\n  var EVENT_DEFAULTS = {\n    bubbles: true,\n    cancelable: false,\n    detail: null\n  };\n\n  var roundingFunction = function roundingFunction(seconds, settings) {\n    if (seconds === null) {\n      return null;\n    }\n\n    var i = 0;\n    var nextVal = 0;\n\n    while (nextVal < seconds) {\n      i++;\n      nextVal += settings.step(i) * 60;\n    }\n\n    var prevVal = nextVal - settings.step(i - 1) * 60;\n\n    if (seconds - prevVal < nextVal - seconds) {\n      return moduloSeconds(prevVal, settings);\n    } else {\n      return moduloSeconds(nextVal, settings);\n    }\n  };\n\n  function moduloSeconds(seconds, settings) {\n    if (seconds == ONE_DAY && settings.show2400) {\n      return seconds;\n    }\n\n    return seconds % ONE_DAY;\n  }\n\n  var DEFAULT_SETTINGS = {\n    appendTo: "body",\n    className: null,\n    closeOnWindow: false,\n    closeOnScroll: false,\n    disableTextInput: false,\n    disableTimeRanges: [],\n    disableTouchKeyboard: false,\n    durationTime: null,\n    forceRoundTime: false,\n    lang: {},\n    listWidth: null,\n    maxTime: null,\n    minTime: null,\n    noneOption: false,\n    orientation: "l",\n    roundingFunction: roundingFunction,\n    scrollDefault: null,\n    selectOnBlur: false,\n    show2400: false,\n    showDuration: false,\n    showOn: ["click", "focus"],\n    step: 30,\n    stopScrollPropagation: false,\n    timeFormat: "g:ia",\n    typeaheadHighlight: true,\n    useSelect: false,\n    wrapHours: true\n  };\n  var DEFAULT_LANG = {\n    am: "am",\n    pm: "pm",\n    AM: "AM",\n    PM: "PM",\n    decimal: ".",\n    mins: "mins",\n    hr: "hr",\n    hrs: "hrs"\n  };\n\n  var Timepicker = /*#__PURE__*/function () {\n    function Timepicker(targetEl) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n      _classCallCheck(this, Timepicker);\n\n      this._handleFormatValue = this._handleFormatValue.bind(this);\n      this._handleKeyUp = this._handleKeyUp.bind(this);\n      this.targetEl = targetEl;\n      var attrOptions = Timepicker.extractAttrOptions(targetEl, Object.keys(DEFAULT_SETTINGS));\n      this.settings = this.parseSettings(_objectSpread2(_objectSpread2(_objectSpread2({}, DEFAULT_SETTINGS), options), attrOptions));\n    }\n\n    _createClass(Timepicker, [{\n      key: "hideMe",\n      value: function hideMe() {\n        if (this.settings.useSelect) {\n          this.targetEl.blur();\n          return;\n        }\n\n        if (!this.list || !Timepicker.isVisible(this.list)) {\n          return;\n        }\n\n        if (this.settings.selectOnBlur) {\n          this._selectValue();\n        }\n\n        this.list.hide();\n        var hideTimepickerEvent = new CustomEvent(\'hideTimepicker\', EVENT_DEFAULTS);\n        this.targetEl.dispatchEvent(hideTimepickerEvent);\n      }\n    }, {\n      key: "_findRow",\n      value: function _findRow(value) {\n        if (!value && value !== 0) {\n          return false;\n        }\n\n        var out = false;\n        var value = this.settings.roundingFunction(value, this.settings);\n\n        if (!this.list) {\n          return false;\n        }\n\n        this.list.find("li").each(function (i, obj) {\n          var parsed = parseInt(obj.dataset.time);\n\n          if (isNaN(parsed)) {\n            return;\n          }\n\n          if (parsed == value) {\n            out = obj;\n            return false;\n          }\n        });\n        return out;\n      }\n    }, {\n      key: "_hideKeyboard",\n      value: function _hideKeyboard() {\n        return (window.navigator.msMaxTouchPoints || "ontouchstart" in document) && this.settings.disableTouchKeyboard;\n      }\n    }, {\n      key: "_setTimeValue",\n      value: function _setTimeValue(value, source) {\n        if (this.targetEl.nodeName === "INPUT") {\n          if (value !== null || this.targetEl.value != "") {\n            this.targetEl.value = value;\n          }\n\n          var tp = this;\n          var settings = tp.settings;\n\n          if (settings.useSelect && source != "select" && tp.list) {\n            tp.list.val(tp._roundAndFormatTime(tp.anytime2int(value)));\n          }\n        }\n\n        var selectTimeEvent = new CustomEvent(\'selectTime\', EVENT_DEFAULTS);\n\n        if (this.selectedValue != value) {\n          this.selectedValue = value;\n          var changeTimeEvent = new CustomEvent(\'changeTime\', EVENT_DEFAULTS);\n          var changeEvent = new CustomEvent(\'change\', Object.assign(EVENT_DEFAULTS, {\n            detail: \'timepicker\'\n          }));\n\n          if (source == "select") {\n            this.targetEl.dispatchEvent(selectTimeEvent);\n            this.targetEl.dispatchEvent(changeTimeEvent);\n            this.targetEl.dispatchEvent(changeEvent);\n          } else if (["error", "initial"].indexOf(source) == -1) {\n            this.targetEl.dispatchEvent(changeTimeEvent);\n          }\n\n          return true;\n        } else {\n          if (["error", "initial"].indexOf(source) == -1) {\n            this.targetEl.dispatchEvent(selectTimeEvent);\n          }\n\n          return false;\n        }\n      }\n    }, {\n      key: "_getTimeValue",\n      value: function _getTimeValue() {\n        if (this.targetEl.nodeName === "INPUT") {\n          return this.targetEl.value;\n        } else {\n          // use the element\'s data attributes to store values\n          return this.selectedValue;\n        }\n      }\n    }, {\n      key: "_selectValue",\n      value: function _selectValue() {\n        var tp = this;\n        tp.settings;\n        var list = tp.list;\n        var cursor = list.find(".ui-timepicker-selected");\n\n        if (cursor.hasClass("ui-timepicker-disabled")) {\n          return false;\n        }\n\n        if (!cursor.length) {\n          return true;\n        }\n\n        var timeValue = cursor.get(0).dataset.time; // selected value found\n\n        if (timeValue) {\n          var parsedTimeValue = parseInt(timeValue);\n\n          if (!isNaN(parsedTimeValue)) {\n            timeValue = parsedTimeValue;\n          }\n        }\n\n        if (timeValue !== null) {\n          if (typeof timeValue != "string") {\n            timeValue = tp._int2time(timeValue);\n          }\n\n          tp._setTimeValue(timeValue, "select");\n        }\n\n        return true;\n      }\n    }, {\n      key: "anytime2int",\n      value: function anytime2int(input) {\n        if (typeof input === \'number\') {\n          return input;\n        } else if (typeof input === \'string\') {\n          return this.time2int(input);\n        } else if (_typeof(input) === \'object\' && input instanceof Date) {\n          return input.getHours() * 3600 + input.getMinutes() * 60 + input.getSeconds();\n        } else if (typeof input == \'function\') {\n          return input();\n        } else {\n          return null;\n        }\n      }\n    }, {\n      key: "time2int",\n      value: function time2int(timeString) {\n        if (timeString === "" || timeString === null || timeString === undefined) {\n          return null;\n        }\n\n        if (timeString === \'now\') {\n          return this.anytime2int(new Date());\n        }\n\n        if (typeof timeString != "string") {\n          return timeString;\n        }\n\n        timeString = timeString.toLowerCase().replace(/[\\s\\.]/g, ""); // if the last character is an "a" or "p", add the "m"\n\n        if (this.settings.lang.am === "am" && (timeString.slice(-1) == "a" || timeString.slice(-1) == "p")) {\n          timeString += "m";\n        }\n\n        var pattern = /^(([^0-9]*))?([0-9]?[0-9])(([0-5][0-9]))?(([0-5][0-9]))?(([^0-9]*))$/;\n        var hasDelimetersMatch = timeString.match(/\\W/);\n\n        if (hasDelimetersMatch) {\n          pattern = /^(([^0-9]*))?([0-9]?[0-9])(\\W+([0-5][0-9]?))?(\\W+([0-5][0-9]))?(([^0-9]*))$/;\n        }\n\n        var time = timeString.match(pattern);\n\n        if (!time) {\n          return null;\n        }\n\n        var hour = parseInt(time[3] * 1, 10);\n        var ampm = time[2] || time[9];\n        var minutes = this.parseMinuteString(time[5]);\n        var seconds = time[7] * 1 || 0;\n\n        if (!ampm && time[3].length == 2 && time[3][0] == "0") {\n          // preceding \'0\' implies AM\n          ampm = "am";\n        }\n\n        if (hour > 24 && !minutes) {\n          // if someone types in something like "83", turn it into "8h 30m"\n          hour = time[3][0] * 1;\n          minutes = this.parseMinuteString(time[3][1]);\n        }\n\n        var hours = hour;\n\n        if (hour <= 12 && ampm) {\n          ampm = ampm.trim();\n          var isPm = ampm == this.settings.lang.pm || ampm == this.settings.lang.PM;\n\n          if (hour == 12) {\n            hours = isPm ? 12 : 0;\n          } else {\n            hours = hour + (isPm ? 12 : 0);\n          }\n        } else {\n          var t = hour * 3600 + minutes * 60 + seconds;\n\n          if (t >= ONE_DAY + (this.settings.show2400 ? 1 : 0)) {\n            if (this.settings.wrapHours === false) {\n              return null;\n            }\n\n            hours = hour % 24;\n          }\n        }\n\n        var timeInt = hours * 3600 + minutes * 60 + seconds; // if no am/pm provided, intelligently guess based on the scrollDefault\n\n        if (hour < 12 && !ampm && this.settings._twelveHourTime && this.settings.scrollDefault()) {\n          var delta = timeInt - this.settings.scrollDefault();\n\n          if (delta < 0 && delta >= ONE_DAY / -2) {\n            timeInt = (timeInt + ONE_DAY / 2) % ONE_DAY;\n          }\n        }\n\n        return timeInt;\n      }\n    }, {\n      key: "parseMinuteString",\n      value: function parseMinuteString(minutesString) {\n        if (!minutesString) {\n          minutesString = 0;\n        }\n\n        var multiplier = 1;\n\n        if (minutesString.length == 1) {\n          multiplier = 10;\n        }\n\n        return parseInt(minutesString) * multiplier || 0;\n      }\n    }, {\n      key: "intStringDateOrFunc2func",\n      value: function intStringDateOrFunc2func(input) {\n        var _this = this;\n\n        if (input === null || input === undefined) {\n          return function () {\n            return null;\n          };\n        } else if (typeof input === \'function\') {\n          return function () {\n            return _this.anytime2int(input());\n          };\n        } else {\n          return function () {\n            return _this.anytime2int(input);\n          };\n        }\n      }\n    }, {\n      key: "parseSettings",\n      value: function parseSettings(settings) {\n        settings.lang = _objectSpread2(_objectSpread2({}, DEFAULT_LANG), settings.lang); // lang is used by other functions the rest of this depends on\n        // todo: unwind circular dependency on lang\n\n        this.settings = settings;\n\n        if (settings.listWidth) {\n          settings.listWidth = this.anytime2int(settings.listWidth);\n        }\n\n        settings.minTime = this.intStringDateOrFunc2func(settings.minTime);\n        settings.maxTime = this.intStringDateOrFunc2func(settings.maxTime);\n        settings.durationTime = this.intStringDateOrFunc2func(settings.durationTime);\n\n        if (settings.scrollDefault) {\n          settings.scrollDefault = this.intStringDateOrFunc2func(settings.scrollDefault);\n        } else {\n          settings.scrollDefault = settings.minTime;\n        }\n\n        if (typeof settings.timeFormat === "string" && settings.timeFormat.match(/[gh]/)) {\n          settings._twelveHourTime = true;\n        }\n\n        if (typeof settings.step != \'function\') {\n          var curryStep = settings.step;\n\n          settings.step = function () {\n            return curryStep;\n          };\n        }\n\n        settings.disableTimeRanges = this._parseDisableTimeRanges(settings.disableTimeRanges);\n\n        if (settings.closeOnWindowScroll && !settings.closeOnScroll) {\n          settings.closeOnScroll = settings.closeOnWindowScroll;\n        }\n\n        if (settings.closeOnScroll === true) {\n          settings.closeOnScroll = window.document;\n        }\n\n        return settings;\n      }\n    }, {\n      key: "_parseDisableTimeRanges",\n      value: function _parseDisableTimeRanges(disableTimeRanges) {\n        if (!disableTimeRanges || disableTimeRanges.length == 0) {\n          return [];\n        } // convert string times to integers\n\n\n        for (var i in disableTimeRanges) {\n          disableTimeRanges[i] = [this.anytime2int(disableTimeRanges[i][0]), this.anytime2int(disableTimeRanges[i][1])];\n        } // sort by starting time\n\n\n        disableTimeRanges = disableTimeRanges.sort(function (a, b) {\n          return a[0] - b[0];\n        }); // merge any overlapping ranges\n\n        for (var i = disableTimeRanges.length - 1; i > 0; i--) {\n          if (disableTimeRanges[i][0] <= disableTimeRanges[i - 1][1]) {\n            disableTimeRanges[i - 1] = [Math.min(disableTimeRanges[i][0], disableTimeRanges[i - 1][0]), Math.max(disableTimeRanges[i][1], disableTimeRanges[i - 1][1])];\n            disableTimeRanges.splice(i, 1);\n          }\n        }\n\n        return disableTimeRanges;\n      }\n      /*\n       *  Filter freeform input\n       */\n\n    }, {\n      key: "_disableTextInputHandler",\n      value: function _disableTextInputHandler(e) {\n        switch (e.keyCode) {\n          case 13: // return\n\n          case 9:\n            //tab\n            return;\n\n          default:\n            e.preventDefault();\n        }\n      }\n    }, {\n      key: "_int2duration",\n      value: function _int2duration(seconds, step) {\n        seconds = Math.abs(seconds);\n        var minutes = Math.round(seconds / 60),\n            duration = [],\n            hours,\n            mins;\n\n        if (minutes < 60) {\n          // Only show (x mins) under 1 hour\n          duration = [minutes, this.settings.lang.mins];\n        } else {\n          hours = Math.floor(minutes / 60);\n          mins = minutes % 60; // Show decimal notation (eg: 1.5 hrs) for 30 minute steps\n\n          if (step == 30 && mins == 30) {\n            hours += this.settings.lang.decimal + 5;\n          }\n\n          duration.push(hours);\n          duration.push(hours == 1 ? this.settings.lang.hr : this.settings.lang.hrs); // Show remainder minutes notation (eg: 1 hr 15 mins) for non-30 minute steps\n          // and only if there are remainder minutes to show\n\n          if (step != 30 && mins) {\n            duration.push(mins);\n            duration.push(this.settings.lang.mins);\n          }\n        }\n\n        return duration.join(" ");\n      }\n    }, {\n      key: "_roundAndFormatTime",\n      value: function _roundAndFormatTime(seconds) {\n        // console.log(\'_roundAndFormatTime\')\n        seconds = this.settings.roundingFunction(seconds, this.settings);\n\n        if (seconds !== null) {\n          return this._int2time(seconds);\n        }\n      }\n    }, {\n      key: "_int2time",\n      value: function _int2time(timeInt) {\n        if (typeof timeInt != "number") {\n          return null;\n        }\n\n        var seconds = parseInt(timeInt % 60),\n            minutes = parseInt(timeInt / 60 % 60),\n            hours = parseInt(timeInt / (60 * 60) % 24);\n        var time = new Date(1970, 0, 2, hours, minutes, seconds, 0);\n\n        if (isNaN(time.getTime())) {\n          return null;\n        }\n\n        if (typeof this.settings.timeFormat === "function") {\n          return this.settings.timeFormat(time);\n        }\n\n        var output = "";\n        var hour, code;\n\n        for (var i = 0; i < this.settings.timeFormat.length; i++) {\n          code = this.settings.timeFormat.charAt(i);\n\n          switch (code) {\n            case "a":\n              output += time.getHours() > 11 ? this.settings.lang.pm : this.settings.lang.am;\n              break;\n\n            case "A":\n              output += time.getHours() > 11 ? this.settings.lang.PM : this.settings.lang.AM;\n              break;\n\n            case "g":\n              hour = time.getHours() % 12;\n              output += hour === 0 ? "12" : hour;\n              break;\n\n            case "G":\n              hour = time.getHours();\n              if (timeInt === ONE_DAY) hour = this.settings.show2400 ? 24 : 0;\n              output += hour;\n              break;\n\n            case "h":\n              hour = time.getHours() % 12;\n\n              if (hour !== 0 && hour < 10) {\n                hour = "0" + hour;\n              }\n\n              output += hour === 0 ? "12" : hour;\n              break;\n\n            case "H":\n              hour = time.getHours();\n              if (timeInt === ONE_DAY) hour = this.settings.show2400 ? 24 : 0;\n              output += hour > 9 ? hour : "0" + hour;\n              break;\n\n            case "i":\n              var minutes = time.getMinutes();\n              output += minutes > 9 ? minutes : "0" + minutes;\n              break;\n\n            case "s":\n              seconds = time.getSeconds();\n              output += seconds > 9 ? seconds : "0" + seconds;\n              break;\n\n            case "\\\\":\n              // escape character; add the next character and skip ahead\n              i++;\n              output += this.settings.timeFormat.charAt(i);\n              break;\n\n            default:\n              output += code;\n          }\n        }\n\n        return output;\n      }\n    }, {\n      key: "_setSelected",\n      value: function _setSelected() {\n        var list = this.list;\n        list.find("li").removeClass("ui-timepicker-selected");\n        var timeValue = this.anytime2int(this._getTimeValue());\n\n        if (timeValue === null) {\n          return;\n        }\n\n        var selected = this._findRow(timeValue);\n\n        if (selected) {\n          var selectedRect = selected.getBoundingClientRect();\n          var listRect = list.get(0).getBoundingClientRect();\n          var topDelta = selectedRect.top - listRect.top;\n\n          if (topDelta + selectedRect.height > listRect.height || topDelta < 0) {\n            var newScroll = list.scrollTop() + (selectedRect.top - listRect.top) - selectedRect.height;\n            list.scrollTop(newScroll);\n          }\n\n          var parsed = parseInt(selected.dataset.time);\n\n          if (this.settings.forceRoundTime || parsed === timeValue) {\n            selected.classList.add(\'ui-timepicker-selected\');\n          }\n        }\n      }\n    }, {\n      key: "_isFocused",\n      value: function _isFocused(el) {\n        return el === document.activeElement;\n      }\n    }, {\n      key: "_handleFormatValue",\n      value: function _handleFormatValue(e) {\n        if (e && e.detail == "timepicker") {\n          return;\n        }\n\n        this._formatValue(e);\n      }\n    }, {\n      key: "_formatValue",\n      value: function _formatValue(e, origin) {\n        if (this.targetEl.value === "") {\n          this._setTimeValue(null, origin);\n\n          return;\n        } // IE fires change event before blur\n\n\n        if (this._isFocused(this.targetEl) && (!e || e.type != "change")) {\n          return;\n        }\n\n        var settings = this.settings;\n        var seconds = this.anytime2int(this.targetEl.value);\n\n        if (seconds === null) {\n          var timeFormatErrorEvent = new CustomEvent(\'timeFormatError\', EVENT_DEFAULTS);\n          this.targetEl.dispatchEvent(timeFormatErrorEvent);\n          return;\n        }\n\n        var rangeError = this._isTimeRangeError(seconds, settings);\n\n        if (settings.forceRoundTime) {\n          var roundSeconds = settings.roundingFunction(seconds, settings);\n\n          if (roundSeconds != seconds) {\n            seconds = roundSeconds;\n            origin = null;\n          }\n        }\n\n        var prettyTime = this._int2time(seconds);\n\n        if (rangeError) {\n          this._setTimeValue(prettyTime);\n\n          var timeRangeErrorEvent = new CustomEvent(\'timeRangeError\', EVENT_DEFAULTS);\n          this.targetEl.dispatchEvent(timeRangeErrorEvent);\n        } else {\n          this._setTimeValue(prettyTime, origin);\n        }\n      }\n    }, {\n      key: "_isTimeRangeError",\n      value: function _isTimeRangeError(seconds, settings) {\n        // check that the time in within bounds\n        if (settings.minTime !== null && settings.maxTime !== null && (seconds < settings.minTime() || seconds > settings.maxTime())) {\n          return true;\n        } // check that time isn\'t within disabled time ranges\n\n\n        var _iterator = _createForOfIteratorHelper(settings.disableTimeRanges),\n            _step;\n\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var range = _step.value;\n\n            if (seconds >= range[0] && seconds < range[1]) {\n              return true;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n\n        return false;\n      }\n    }, {\n      key: "_generateNoneElement",\n      value: function _generateNoneElement(optionValue, useSelect) {\n        var label, className, value;\n\n        if (_typeof(optionValue) == "object") {\n          label = optionValue.label;\n          className = optionValue.className;\n          value = optionValue.value;\n        } else if (typeof optionValue == "string") {\n          label = optionValue;\n          value = "";\n        } else {\n          $.error("Invalid noneOption value");\n        }\n\n        var el;\n\n        if (useSelect) {\n          el = document.createElement("option");\n          el.value = value;\n        } else {\n          el = document.createElement("li");\n          el.dataset.time = String(value);\n        }\n\n        el.innerText = label;\n        el.classList.add(className);\n        return el;\n      }\n      /*\n       *  Time typeahead\n       */\n\n    }, {\n      key: "_handleKeyUp",\n      value: function _handleKeyUp(e) {\n        var _this2 = this;\n\n        if (!this.list || !Timepicker.isVisible(this.list) || this.settings.disableTextInput) {\n          return true;\n        }\n\n        if (e.type === "paste" || e.type === "cut") {\n          var handler = function handler() {\n            if (_this2.settings.typeaheadHighlight) {\n              _this2._setSelected();\n            } else {\n              _this2.list.hide();\n            }\n          };\n\n          setTimeout(handler, 0);\n          return;\n        }\n\n        switch (e.keyCode) {\n          case 96: // numpad numerals\n\n          case 97:\n          case 98:\n          case 99:\n          case 100:\n          case 101:\n          case 102:\n          case 103:\n          case 104:\n          case 105:\n          case 48: // numerals\n\n          case 49:\n          case 50:\n          case 51:\n          case 52:\n          case 53:\n          case 54:\n          case 55:\n          case 56:\n          case 57:\n          case 65: // a\n\n          case 77: // m\n\n          case 80: // p\n\n          case 186: // colon\n\n          case 8: // backspace\n\n          case 46:\n            // delete\n            if (this.settings.typeaheadHighlight) {\n              this._setSelected();\n            } else {\n              this.list.hide();\n            }\n\n            break;\n        }\n      }\n    }], [{\n      key: "extractAttrOptions",\n      value: function extractAttrOptions(element, keys) {\n        var output = {};\n\n        var _iterator2 = _createForOfIteratorHelper(keys),\n            _step2;\n\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var key = _step2.value;\n\n            if (key in element.dataset) {\n              output[key] = element.dataset[key];\n            }\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n\n        return output;\n      }\n    }, {\n      key: "isVisible",\n      value: function isVisible(elem) {\n        var el = elem[0];\n        return el.offsetWidth > 0 && el.offsetHeight > 0;\n      }\n    }, {\n      key: "hideAll",\n      value: function hideAll() {\n        var _iterator3 = _createForOfIteratorHelper(document.getElementsByClassName(\'ui-timepicker-input\')),\n            _step3;\n\n        try {\n          for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n            var el = _step3.value;\n            var tp = el.timepickerObj;\n\n            if (tp) {\n              tp.hideMe();\n            }\n          }\n        } catch (err) {\n          _iterator3.e(err);\n        } finally {\n          _iterator3.f();\n        }\n      }\n    }]);\n\n    return Timepicker;\n  }(); // IE9-11 polyfill for CustomEvent\n\n\n  (function () {\n    if (typeof window.CustomEvent === "function") return false;\n\n    function CustomEvent(event, params) {\n      if (!params) {\n        params = {};\n      }\n\n      params = Object.assign(EVENT_DEFAULTS, params);\n      var evt = document.createEvent(\'CustomEvent\');\n      evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);\n      return evt;\n    }\n\n    window.CustomEvent = CustomEvent;\n  })();\n\n  function _getNoneOptionItems(settings) {\n    if (!settings.noneOption) {\n      return [];\n    }\n\n    var noneOptions = _getNoneOptionItemsHelper(settings.noneOption);\n\n    if (Array.isArray(settings.noneOption)) {\n      return noneOptions;\n    } else {\n      return [noneOptions];\n    }\n  }\n\n  function _getNoneOptionItemsHelper(noneOption) {\n    if (Array.isArray(noneOption)) {\n      return noneOption.map(_getNoneOptionItemsHelper);\n    }\n\n    if (noneOption === true) {\n      return {\n        \'label\': \'None\',\n        \'value\': \'\'\n      };\n    }\n\n    if (_typeof(noneOption) === \'object\') {\n      return noneOption;\n    }\n\n    return {\n      \'label\': noneOption,\n      \'value\': \'\'\n    };\n  }\n\n  function _getDropdownTimes(tp) {\n    var _settings$minTime, _settings$maxTime;\n\n    var settings = tp.settings;\n    var start = (_settings$minTime = settings.minTime()) !== null && _settings$minTime !== void 0 ? _settings$minTime : 0;\n    var end = (_settings$maxTime = settings.maxTime()) !== null && _settings$maxTime !== void 0 ? _settings$maxTime : start + ONE_DAY - 1;\n\n    if (end < start) {\n      // make sure the end time is greater than start time, otherwise there will be no list to show\n      end += ONE_DAY;\n    }\n\n    if (end === ONE_DAY - 1 && typeof settings.timeFormat === \'string\' && settings.show2400) {\n      // show a 24:00 option when using military time\n      end = ONE_DAY;\n    }\n\n    var output = [];\n\n    for (var i = start, j = 0; i <= end; j++, i += settings.step(j) * 60) {\n      var timeInt = i;\n\n      var timeString = tp._int2time(timeInt);\n\n      var className = timeInt % ONE_DAY < ONE_DAY / 2 ? \'ui-timepicker-am\' : \'ui-timepicker-pm\';\n      var item = {\n        \'label\': timeString,\n        \'value\': moduloSeconds(timeInt, settings),\n        \'className\': className\n      };\n\n      if ((settings.minTime() !== null || settings.durationTime() !== null) && settings.showDuration) {\n        var _settings$durationTim;\n\n        var durStart = (_settings$durationTim = settings.durationTime()) !== null && _settings$durationTim !== void 0 ? _settings$durationTim : settings.minTime();\n\n        if (durStart > i) {\n          durStart -= ONE_DAY;\n        }\n\n        var durationString = tp._int2duration(i - durStart, settings.step());\n\n        item.duration = durationString;\n      }\n\n      var _iterator = _createForOfIteratorHelper(settings.disableTimeRanges),\n          _step;\n\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var range = _step.value;\n\n          if (timeInt % ONE_DAY >= range[0] && timeInt % ONE_DAY < range[1]) {\n            item.disabled = true;\n            break;\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n\n      output.push(item);\n    }\n\n    return output;\n  }\n\n  function _renderSelectItem(item) {\n    var el = document.createElement(\'option\');\n    el.value = item.value || item.label;\n\n    if (item.duration) {\n      el.appendChild(document.createTextNode(item.label + \' (\' + item.duration + \')\'));\n    } else {\n      el.appendChild(document.createTextNode(item.label));\n    }\n\n    if (item.disabled) {\n      el.disabled = true;\n    }\n\n    return el;\n  }\n\n  function _renderStandardItem(item) {\n    var el = document.createElement(\'li\');\n    el.dataset[\'time\'] = item.value;\n\n    if (item.className) {\n      el.classList.add(item.className);\n    }\n\n    el.className = item.className;\n    el.appendChild(document.createTextNode(item.label));\n\n    if (item.duration) {\n      var durationEl = document.createElement(\'span\');\n      durationEl.appendChild(document.createTextNode(\'(\' + item.duration + \')\'));\n      durationEl.classList.add(\'ui-timepicker-duration\');\n      el.appendChild(durationEl);\n    }\n\n    if (item.disabled) {\n      el.classList.add(\'ui-timepicker-disabled\');\n    }\n\n    return el;\n  }\n\n  function _renderStandardList(items) {\n    var list = document.createElement(\'ul\');\n    list.classList.add(\'ui-timepicker-list\');\n\n    var _iterator2 = _createForOfIteratorHelper(items),\n        _step2;\n\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var item = _step2.value;\n\n        var itemEl = _renderStandardItem(item);\n\n        list.appendChild(itemEl);\n      }\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n\n    var wrapper = document.createElement(\'div\');\n    wrapper.classList.add(\'ui-timepicker-wrapper\');\n    wrapper.tabIndex = -1;\n    wrapper.style.display = \'none\';\n    wrapper.style.position = \'absolute\';\n    wrapper.appendChild(list);\n    return wrapper;\n  }\n\n  function _renderSelectList(items, targetName) {\n    var el = document.createElement(\'select\');\n    el.classList.add(\'ui-timepicker-select\');\n\n    if (targetName) {\n      el.name = \'ui-timepicker-\' + targetName;\n    }\n\n    var _iterator3 = _createForOfIteratorHelper(items),\n        _step3;\n\n    try {\n      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n        var item = _step3.value;\n\n        var itemEl = _renderSelectItem(item);\n\n        el.appendChild(itemEl);\n      }\n    } catch (err) {\n      _iterator3.e(err);\n    } finally {\n      _iterator3.f();\n    }\n\n    return el;\n  }\n\n  function renderHtml(tp) {\n    var items = [].concat(_getNoneOptionItems(tp.settings), _getDropdownTimes(tp));\n    var el;\n\n    if (tp.settings.useSelect) {\n      el = _renderSelectList(items, tp.targetEl.name);\n    } else {\n      el = _renderStandardList(items);\n    }\n\n    if (tp.settings.className) {\n      var _iterator4 = _createForOfIteratorHelper(tp.settings.className.split(\' \')),\n          _step4;\n\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var token = _step4.value;\n          el.classList.add(token);\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n    }\n\n    if (tp.settings.showDuration && (tp.settings.minTime !== null || tp.settings.durationTime !== null)) {\n      el.classList.add("ui-timepicker-with-duration");\n      el.classList.add("ui-timepicker-step-" + tp.settings.step());\n    }\n\n    return el;\n  }\n\n  (function (factory) {\n    if (( false ? 0 : _typeof(exports)) === "object" && exports && ( false ? 0 : _typeof(module)) === "object" && module && module.exports === exports) {\n      // Browserify. Attach to jQuery module.\n      factory(__webpack_require__(/*! jquery */ "jquery"));\n    } else if (true) {\n      // AMD. Register as an anonymous module.\n      !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(/*! jquery */ "jquery")], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === \'function\' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n  })(function ($) {\n    var methods = {\n      init: function init(options) {\n        return this.each(function () {\n          var self = $(this);\n          var tp = new Timepicker(this, options);\n          var settings = tp.settings;\n          settings.lang;\n          this.timepickerObj = tp;\n          self.addClass("ui-timepicker-input");\n\n          if (settings.useSelect) {\n            _render(self);\n          } else {\n            self.prop("autocomplete", "off");\n\n            if (settings.showOn) {\n              for (var i in settings.showOn) {\n                self.on(settings.showOn[i] + ".timepicker", methods.show);\n              }\n            }\n\n            self.on("change.timepicker", tp._handleFormatValue);\n            self.on("keydown.timepicker", _keydownhandler);\n            self.on("keyup.timepicker", tp._handleKeyUp);\n\n            if (settings.disableTextInput) {\n              self.on("keydown.timepicker", tp._disableTextInputHandler);\n            }\n\n            self.on("cut.timepicker", tp._handleKeyUp);\n            self.on("paste.timepicker", tp._handleKeyUp);\n\n            tp._formatValue(null, "initial");\n          }\n        });\n      },\n      show: function show(e) {\n        var self = $(this);\n        var tp = self[0].timepickerObj;\n        var settings = tp.settings;\n\n        if (e) {\n          e.preventDefault();\n        }\n\n        if (settings.useSelect) {\n          tp.list.trigger(\'focus\');\n          return;\n        }\n\n        if (tp._hideKeyboard()) {\n          // block the keyboard on mobile devices\n          self.trigger(\'blur\');\n        }\n\n        var list = tp.list; // check if input is readonly\n\n        if (self.prop("readonly")) {\n          return;\n        } // check if list needs to be rendered\n\n\n        _render(self);\n\n        list = tp.list;\n\n        if (Timepicker.isVisible(list)) {\n          return;\n        }\n\n        if (self.is(\'input\')) {\n          tp.selectedValue = self.val();\n        }\n\n        tp._setSelected(); // make sure other pickers are hidden\n\n\n        Timepicker.hideAll();\n\n        if (typeof settings.listWidth == "number") {\n          list.width(self.outerWidth() * settings.listWidth);\n        } // position the dropdown relative to the input\n\n\n        list.show();\n        var listOffset = {};\n\n        if (settings.orientation.match(/r/)) {\n          // right-align the dropdown\n          listOffset.left = self.offset().left + self.outerWidth() - list.outerWidth() + parseInt(list.css("marginLeft").replace("px", ""), 10);\n        } else if (settings.orientation.match(/l/)) {\n          // left-align the dropdown\n          listOffset.left = self.offset().left + parseInt(list.css("marginLeft").replace("px", ""), 10);\n        } else if (settings.orientation.match(/c/)) {\n          // center-align the dropdown\n          listOffset.left = self.offset().left + (self.outerWidth() - list.outerWidth()) / 2 + parseInt(list.css("marginLeft").replace("px", ""), 10);\n        }\n\n        var verticalOrientation;\n\n        if (settings.orientation.match(/t/)) {\n          verticalOrientation = "t";\n        } else if (settings.orientation.match(/b/)) {\n          verticalOrientation = "b";\n        } else if (self.offset().top + self.outerHeight(true) + list.outerHeight() > $(window).height() + $(window).scrollTop()) {\n          verticalOrientation = "t";\n        } else {\n          verticalOrientation = "b";\n        }\n\n        if (verticalOrientation == "t") {\n          // position the dropdown on top\n          list.addClass("ui-timepicker-positioned-top");\n          listOffset.top = self.offset().top - list.outerHeight() + parseInt(list.css("marginTop").replace("px", ""), 10);\n        } else {\n          // put it under the input\n          list.removeClass("ui-timepicker-positioned-top");\n          listOffset.top = self.offset().top + self.outerHeight() + parseInt(list.css("marginTop").replace("px", ""), 10);\n        }\n\n        list.offset(listOffset); // position scrolling\n\n        var selected = list.find(".ui-timepicker-selected");\n\n        if (!selected.length) {\n          var timeInt = tp.anytime2int(tp._getTimeValue());\n\n          if (timeInt !== null) {\n            selected = $(tp._findRow(timeInt));\n          } else if (settings.scrollDefault()) {\n            selected = $(tp._findRow(settings.scrollDefault()));\n          }\n        } // if not found or disabled, intelligently find first selectable element\n\n\n        if (!selected.length || selected.hasClass("ui-timepicker-disabled")) {\n          selected = list.find("li:not(.ui-timepicker-disabled):first");\n        }\n\n        if (selected && selected.length) {\n          var topOffset = list.scrollTop() + selected.position().top - selected.outerHeight();\n          list.scrollTop(topOffset);\n        } else {\n          list.scrollTop(0);\n        } // prevent scroll propagation\n\n\n        if (settings.stopScrollPropagation) {\n          $(document).on("wheel.ui-timepicker", ".ui-timepicker-wrapper", function (e) {\n            e.preventDefault();\n            var currentScroll = $(this).scrollTop();\n            $(this).scrollTop(currentScroll + e.originalEvent.deltaY);\n          });\n        } // attach close handlers\n\n\n        $(document).on("mousedown.ui-timepicker", _closeHandler);\n        window.addEventListener(\'resize\', _closeHandler);\n\n        if (settings.closeOnScroll) {\n          $(settings.closeOnScroll).on("scroll.ui-timepicker", _closeHandler);\n        }\n\n        self.trigger("showTimepicker");\n        return this;\n      },\n      hide: function hide(e) {\n        var tp = this[0].timepickerObj;\n\n        if (tp) {\n          tp.hideMe();\n        }\n\n        Timepicker.hideAll();\n        return this;\n      },\n      option: function option(key, value) {\n        if (typeof key == "string" && typeof value == "undefined") {\n          var tp = this[0].timepickerObj;\n          return tp.settings[key];\n        }\n\n        return this.each(function () {\n          var self = $(this);\n          var tp = self[0].timepickerObj;\n          var settings = tp.settings;\n          var list = tp.list;\n\n          if (_typeof(key) == "object") {\n            settings = $.extend(settings, key);\n          } else if (typeof key == "string") {\n            settings[key] = value;\n          }\n\n          settings = tp.parseSettings(settings);\n          tp.settings = settings;\n\n          tp._formatValue({\n            type: "change"\n          }, "initial");\n\n          if (list) {\n            list.remove();\n            tp.list = null;\n          }\n\n          if (settings.useSelect) {\n            _render(self);\n          }\n        });\n      },\n      getSecondsFromMidnight: function getSecondsFromMidnight() {\n        var tp = this[0].timepickerObj;\n        return tp.anytime2int(tp._getTimeValue());\n      },\n      getTime: function getTime(relative_date) {\n        var tp = this[0].timepickerObj;\n\n        var time_string = tp._getTimeValue();\n\n        if (!time_string) {\n          return null;\n        }\n\n        var offset = tp.anytime2int(time_string);\n\n        if (offset === null) {\n          return null;\n        }\n\n        if (!relative_date) {\n          relative_date = new Date();\n        } // construct a Date from relative date, and offset\'s time\n\n\n        var time = new Date(relative_date);\n        time.setHours(offset / 3600);\n        time.setMinutes(offset % 3600 / 60);\n        time.setSeconds(offset % 60);\n        time.setMilliseconds(0);\n        return time;\n      },\n      isVisible: function isVisible() {\n        var tp = this[0].timepickerObj;\n        return !!(tp && tp.list && Timepicker.isVisible(tp.list));\n      },\n      setTime: function setTime(value) {\n        var tp = this[0].timepickerObj;\n        var settings = tp.settings;\n        var seconds = tp.anytime2int(value);\n\n        if (tp._isTimeRangeError(seconds, settings)) {\n          var timeRangeErrorEvent = new CustomEvent(\'timeRangeError\', EVENT_DEFAULTS);\n          tp.targetEl.dispatchEvent(timeRangeErrorEvent);\n        }\n\n        if (settings.forceRoundTime) {\n          var prettyTime = tp._roundAndFormatTime(seconds);\n        } else {\n          var prettyTime = tp._int2time(seconds);\n        }\n\n        if (value && prettyTime === null && settings.noneOption) {\n          prettyTime = value;\n        }\n\n        tp._setTimeValue(prettyTime, "initial");\n\n        tp._formatValue({\n          type: "change"\n        }, "initial");\n\n        if (tp && tp.list) {\n          tp._setSelected();\n        }\n\n        return this;\n      },\n      remove: function remove() {\n        var self = this; // check if this element is a timepicker\n\n        if (!self.hasClass("ui-timepicker-input")) {\n          return;\n        }\n\n        var tp = self[0].timepickerObj;\n        var settings = tp.settings;\n        self.removeAttr("autocomplete", "off");\n        self.removeClass("ui-timepicker-input");\n        self.removeData("timepicker-obj");\n        self.off(".timepicker"); // timepicker-list won\'t be present unless the user has interacted with this timepicker\n\n        if (tp.list) {\n          tp.list.remove();\n        }\n\n        if (settings.useSelect) {\n          self.show();\n        }\n\n        tp.list = null;\n        return this;\n      }\n    }; // private methods\n\n    function _render(self) {\n      var tp = self[0].timepickerObj;\n      var list = tp.list;\n      var settings = tp.settings;\n\n      if (list && list.length) {\n        list.remove();\n        tp.list = null;\n      }\n\n      var wrapped_list = $(renderHtml(tp));\n\n      if (settings.useSelect) {\n        list = wrapped_list;\n      } else {\n        list = wrapped_list.children(\'ul\');\n      }\n\n      wrapped_list.data("timepicker-input", self);\n      tp.list = wrapped_list;\n\n      if (settings.useSelect) {\n        if (self.val()) {\n          list.val(tp._roundAndFormatTime(tp.anytime2int(self.val())));\n        }\n\n        list.on("focus", function () {\n          $(this).data("timepicker-input").trigger("showTimepicker");\n        });\n        list.on("blur", function () {\n          $(this).data("timepicker-input").trigger("hideTimepicker");\n        });\n        list.on("change", function () {\n          tp._setTimeValue($(this).val(), "select");\n        });\n\n        tp._setTimeValue(list.val(), "initial");\n\n        self.hide().after(list);\n      } else {\n        var appendTo = settings.appendTo;\n\n        if (typeof appendTo === "string") {\n          appendTo = $(appendTo);\n        } else if (typeof appendTo === "function") {\n          appendTo = appendTo(self);\n        }\n\n        appendTo.append(wrapped_list);\n\n        tp._setSelected();\n\n        list.on("mousedown click", "li", function (e) {\n          // hack: temporarily disable the focus handler\n          // to deal with the fact that IE fires \'focus\'\n          // events asynchronously\n          self.off("focus.timepicker");\n          self.on("focus.timepicker-ie-hack", function () {\n            self.off("focus.timepicker-ie-hack");\n            self.on("focus.timepicker", methods.show);\n          });\n\n          if (!tp._hideKeyboard()) {\n            self[0].focus();\n          } // make sure only the clicked row is selected\n\n\n          list.find("li").removeClass("ui-timepicker-selected");\n          $(this).addClass("ui-timepicker-selected");\n\n          if (tp._selectValue()) {\n            self.trigger("hideTimepicker");\n            list.on("mouseup.timepicker click.timepicker", "li", function (e) {\n              list.off("mouseup.timepicker click.timepicker");\n              wrapped_list.hide();\n            });\n          }\n        });\n      }\n    } // event handler to decide whether to close timepicker\n\n\n    function _closeHandler(e) {\n      if (e.type == \'focus\' && e.target == window) {\n        // mobile Chrome fires focus events against window for some reason\n        return;\n      }\n\n      var target = $(e.target);\n\n      if (target.closest(".ui-timepicker-input").length || target.closest(".ui-timepicker-wrapper").length) {\n        // active timepicker was focused. ignore\n        return;\n      }\n\n      Timepicker.hideAll();\n      $(document).off(".ui-timepicker");\n      $(window).off(".ui-timepicker");\n    }\n    /*\n     *  Keyboard navigation via arrow keys\n     */\n\n\n    function _keydownhandler(e) {\n      var self = $(this);\n      var tp = self[0].timepickerObj;\n      var list = tp.list;\n\n      if (!list || !Timepicker.isVisible(list)) {\n        if (e.keyCode == 40) {\n          // show the list!\n          methods.show.call(self.get(0));\n          list = tp.list;\n\n          if (!tp._hideKeyboard()) {\n            self.trigger(\'focus\');\n          }\n        } else {\n          return true;\n        }\n      }\n\n      switch (e.keyCode) {\n        case 13:\n          // return\n          if (tp._selectValue()) {\n            tp._formatValue({\n              type: "change"\n            });\n\n            tp.hideMe();\n          }\n\n          e.preventDefault();\n          return false;\n\n        case 38:\n          // up\n          var selected = list.find(".ui-timepicker-selected");\n\n          if (!selected.length) {\n            list.find("li").each(function (i, obj) {\n              if ($(obj).position().top > 0) {\n                selected = $(obj);\n                return false;\n              }\n            });\n            selected.addClass("ui-timepicker-selected");\n          } else if (!selected.is(":first-child")) {\n            selected.removeClass("ui-timepicker-selected");\n            selected.prev().addClass("ui-timepicker-selected");\n\n            if (selected.prev().position().top < selected.outerHeight()) {\n              list.scrollTop(list.scrollTop() - selected.outerHeight());\n            }\n          }\n\n          return false;\n\n        case 40:\n          // down\n          selected = list.find(".ui-timepicker-selected");\n\n          if (selected.length === 0) {\n            list.find("li").each(function (i, obj) {\n              if ($(obj).position().top > 0) {\n                selected = $(obj);\n                return false;\n              }\n            });\n            selected.addClass("ui-timepicker-selected");\n          } else if (!selected.is(":last-child")) {\n            selected.removeClass("ui-timepicker-selected");\n            selected.next().addClass("ui-timepicker-selected");\n\n            if (selected.next().position().top + 2 * selected.outerHeight() > list.outerHeight()) {\n              list.scrollTop(list.scrollTop() + selected.outerHeight());\n            }\n          }\n\n          return false;\n\n        case 27:\n          // escape\n          list.find("li").removeClass("ui-timepicker-selected");\n          tp.hideMe();\n          break;\n\n        case 9:\n          //tab\n          tp.hideMe();\n          break;\n\n        default:\n          return true;\n      }\n    } // Plugin entry\n\n\n    $.fn.timepicker = function (method) {\n      if (!this.length) return this;\n\n      if (methods[method]) {\n        // check if this element is a timepicker\n        if (!this.hasClass("ui-timepicker-input")) {\n          return this;\n        }\n\n        return methods[method].apply(this, Array.prototype.slice.call(arguments, 1));\n      } else if (_typeof(method) === "object" || !method) {\n        return methods.init.apply(this, arguments);\n      } else {\n        $.error("Method " + method + " does not exist on jQuery.timepicker");\n      }\n    }; // Default plugin options.\n\n\n    $.fn.timepicker.defaults = DEFAULT_SETTINGS;\n  });\n\n}());\n\n\n//# sourceURL=webpack://Materialize/./node_modules/timepicker/jquery.timepicker.js?')},jquery:function(e){"use strict";e.exports=__WEBPACK_EXTERNAL_MODULE_jquery__}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={id:e,loaded:!1,exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.loaded=!0,t.exports}__webpack_require__.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(n,{a:n}),n},__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__webpack_require__.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e};var __webpack_exports__=__webpack_require__("./libs/jquery-timepicker/jquery-timepicker.js");return __webpack_exports__}()}));