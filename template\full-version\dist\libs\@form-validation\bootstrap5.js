!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./node_modules/@form-validation/core/lib/cjs/index.js":function(__unused_webpack_module,exports){eval("\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n/**\n * Implement Luhn validation algorithm\n * Credit to https://gist.github.com/ShirtlessKirk/2134376\n *\n * @see http://en.wikipedia.org/wiki/<PERSON>hn\n * @param {string} value\n * @returns {boolean}\n */\nfunction luhn(value) {\n    var length = value.length;\n    var prodArr = [\n        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],\n        [0, 2, 4, 6, 8, 1, 3, 5, 7, 9],\n    ];\n    var mul = 0;\n    var sum = 0;\n    while (length--) {\n        sum += prodArr[mul][parseInt(value.charAt(length), 10)];\n        mul = 1 - mul;\n    }\n    return sum % 10 === 0 && sum > 0;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * Implement modulus 11, 10 (ISO 7064) algorithm\n *\n * @param {string} value\n * @returns {boolean}\n */\nfunction mod11And10(value) {\n    var length = value.length;\n    var check = 5;\n    for (var i = 0; i < length; i++) {\n        check = ((((check || 10) * 2) % 11) + parseInt(value.charAt(i), 10)) % 10;\n    }\n    return check === 1;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * Implements Mod 37, 36 (ISO 7064) algorithm\n *\n * @param {string} value\n * @param {string} [alphabet]\n * @returns {boolean}\n */\nfunction mod37And36(value, alphabet) {\n    if (alphabet === void 0) { alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'; }\n    var length = value.length;\n    var modulus = alphabet.length;\n    var check = Math.floor(modulus / 2);\n    for (var i = 0; i < length; i++) {\n        check = ((((check || modulus) * 2) % (modulus + 1)) + alphabet.indexOf(value.charAt(i))) % modulus;\n    }\n    return check === 1;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nfunction transform(input) {\n    return input\n        .split('')\n        .map(function (c) {\n        var code = c.charCodeAt(0);\n        // 65, 66, ..., 90 are the char code of A, B, ..., Z\n        return code >= 65 && code <= 90\n            ? // Replace A, B, C, ..., Z with 10, 11, ..., 35\n                code - 55\n            : c;\n    })\n        .join('')\n        .split('')\n        .map(function (c) { return parseInt(c, 10); });\n}\nfunction mod97And10(input) {\n    var digits = transform(input);\n    var temp = 0;\n    var length = digits.length;\n    for (var i = 0; i < length - 1; ++i) {\n        temp = ((temp + digits[i]) * 10) % 97;\n    }\n    temp += digits[length - 1];\n    return temp % 97 === 1;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * Implement Verhoeff validation algorithm\n * Credit to Sergey Petushkov, 2014\n *\n * @see https://en.wikipedia.org/wiki/Verhoeff_algorithm\n * @param {string} value\n * @returns {boolean}\n */\nfunction verhoeff(value) {\n    // Multiplication table d\n    var d = [\n        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],\n        [1, 2, 3, 4, 0, 6, 7, 8, 9, 5],\n        [2, 3, 4, 0, 1, 7, 8, 9, 5, 6],\n        [3, 4, 0, 1, 2, 8, 9, 5, 6, 7],\n        [4, 0, 1, 2, 3, 9, 5, 6, 7, 8],\n        [5, 9, 8, 7, 6, 0, 4, 3, 2, 1],\n        [6, 5, 9, 8, 7, 1, 0, 4, 3, 2],\n        [7, 6, 5, 9, 8, 2, 1, 0, 4, 3],\n        [8, 7, 6, 5, 9, 3, 2, 1, 0, 4],\n        [9, 8, 7, 6, 5, 4, 3, 2, 1, 0],\n    ];\n    // Permutation table p\n    var p = [\n        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],\n        [1, 5, 7, 6, 2, 8, 3, 0, 9, 4],\n        [5, 8, 0, 3, 7, 9, 6, 1, 4, 2],\n        [8, 9, 1, 6, 0, 4, 3, 5, 2, 7],\n        [9, 4, 5, 3, 1, 2, 6, 8, 7, 0],\n        [4, 2, 8, 6, 5, 7, 3, 9, 0, 1],\n        [2, 7, 9, 3, 8, 0, 6, 4, 1, 5],\n        [7, 0, 4, 6, 9, 1, 3, 2, 5, 8],\n    ];\n    // Inverse table inv\n    var invertedArray = value.reverse();\n    var c = 0;\n    for (var i = 0; i < invertedArray.length; i++) {\n        c = d[c][p[i % 8][invertedArray[i]]];\n    }\n    return c === 0;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar index$1 = {\n    luhn: luhn,\n    mod11And10: mod11And10,\n    mod37And36: mod37And36,\n    mod97And10: mod97And10,\n    verhoeff: verhoeff,\n};\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * @param {HTMLElement} form The form element\n * @param {string} field The field name\n * @param {HTMLElement} element The field element\n * @param {HTMLElement[]} elements The list of elements which have the same name as `field`\n * @return {string}\n */\nfunction getFieldValue(form, field, element, elements) {\n    var type = (element.getAttribute('type') || '').toLowerCase();\n    var tagName = element.tagName.toLowerCase();\n    if (tagName === 'textarea') {\n        return element.value;\n    }\n    if (tagName === 'select') {\n        var select = element;\n        var index = select.selectedIndex;\n        return index >= 0 ? select.options.item(index).value : '';\n    }\n    if (tagName === 'input') {\n        if ('radio' === type || 'checkbox' === type) {\n            var checked = elements.filter(function (ele) { return ele.checked; }).length;\n            return checked === 0 ? '' : checked + '';\n        }\n        else {\n            return element.value;\n        }\n    }\n    return '';\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nfunction emitter() {\n    return {\n        fns: {},\n        clear: function () {\n            this.fns = {};\n        },\n        emit: function (event) {\n            var args = [];\n            for (var _i = 1; _i < arguments.length; _i++) {\n                args[_i - 1] = arguments[_i];\n            }\n            (this.fns[event] || []).map(function (handler) { return handler.apply(handler, args); });\n        },\n        off: function (event, func) {\n            if (this.fns[event]) {\n                var index = this.fns[event].indexOf(func);\n                if (index >= 0) {\n                    this.fns[event].splice(index, 1);\n                }\n            }\n        },\n        on: function (event, func) {\n            (this.fns[event] = this.fns[event] || []).push(func);\n        },\n    };\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nfunction filter() {\n    return {\n        filters: {},\n        add: function (name, func) {\n            (this.filters[name] = this.filters[name] || []).push(func);\n        },\n        clear: function () {\n            this.filters = {};\n        },\n        execute: function (name, defaultValue, args) {\n            if (!this.filters[name] || !this.filters[name].length) {\n                return defaultValue;\n            }\n            var result = defaultValue;\n            var filters = this.filters[name];\n            var count = filters.length;\n            for (var i = 0; i < count; i++) {\n                result = filters[i].apply(result, args);\n            }\n            return result;\n        },\n        remove: function (name, func) {\n            if (this.filters[name]) {\n                this.filters[name] = this.filters[name].filter(function (f) { return f !== func; });\n            }\n        },\n    };\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar Core = /** @class */ (function () {\n    function Core(form, fields) {\n        this.fields = {};\n        this.elements = {};\n        this.ee = emitter();\n        this.filter = filter();\n        this.plugins = {};\n        // Store the result of validation for each field\n        this.results = new Map();\n        this.validators = {};\n        this.form = form;\n        this.fields = fields;\n    }\n    Core.prototype.on = function (event, func) {\n        this.ee.on(event, func);\n        return this;\n    };\n    Core.prototype.off = function (event, func) {\n        this.ee.off(event, func);\n        return this;\n    };\n    Core.prototype.emit = function (event) {\n        var _a;\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        (_a = this.ee).emit.apply(_a, __spreadArray([event], args, false));\n        return this;\n    };\n    Core.prototype.registerPlugin = function (name, plugin) {\n        // Check if whether the plugin is registered\n        if (this.plugins[name]) {\n            throw new Error(\"The plguin \".concat(name, \" is registered\"));\n        }\n        // Install the plugin\n        plugin.setCore(this);\n        plugin.install();\n        this.plugins[name] = plugin;\n        return this;\n    };\n    Core.prototype.deregisterPlugin = function (name) {\n        var plugin = this.plugins[name];\n        if (plugin) {\n            plugin.uninstall();\n        }\n        delete this.plugins[name];\n        return this;\n    };\n    Core.prototype.enablePlugin = function (name) {\n        var plugin = this.plugins[name];\n        if (plugin) {\n            plugin.enable();\n        }\n        return this;\n    };\n    Core.prototype.disablePlugin = function (name) {\n        var plugin = this.plugins[name];\n        if (plugin) {\n            plugin.disable();\n        }\n        return this;\n    };\n    Core.prototype.isPluginEnabled = function (name) {\n        var plugin = this.plugins[name];\n        return plugin ? plugin.isPluginEnabled() : false;\n    };\n    Core.prototype.registerValidator = function (name, func) {\n        if (this.validators[name]) {\n            throw new Error(\"The validator \".concat(name, \" is registered\"));\n        }\n        this.validators[name] = func;\n        return this;\n    };\n    /**\n     * Add a filter\n     *\n     * @param {string} name The name of filter\n     * @param {Function} func The filter function\n     * @return {Core}\n     */\n    Core.prototype.registerFilter = function (name, func) {\n        this.filter.add(name, func);\n        return this;\n    };\n    /**\n     * Remove a filter\n     *\n     * @param {string} name The name of filter\n     * @param {Function} func The filter function\n     * @return {Core}\n     */\n    Core.prototype.deregisterFilter = function (name, func) {\n        this.filter.remove(name, func);\n        return this;\n    };\n    /**\n     * Execute a filter\n     *\n     * @param {string} name The name of filter\n     * @param {T} defaultValue The default value returns by the filter\n     * @param {array} args The filter arguments\n     * @returns {T}\n     */\n    Core.prototype.executeFilter = function (name, defaultValue, args) {\n        return this.filter.execute(name, defaultValue, args);\n    };\n    /**\n     * Add a field\n     *\n     * @param {string} field The field name\n     * @param {FieldOptions} options The field options. The options will be merged with the original validator rules\n     * if the field is already defined\n     * @return {Core}\n     */\n    Core.prototype.addField = function (field, options) {\n        var opts = Object.assign({}, {\n            selector: '',\n            validators: {},\n        }, options);\n        // Merge the options\n        this.fields[field] = this.fields[field]\n            ? {\n                selector: opts.selector || this.fields[field].selector,\n                validators: Object.assign({}, this.fields[field].validators, opts.validators),\n            }\n            : opts;\n        this.elements[field] = this.queryElements(field);\n        this.emit('core.field.added', {\n            elements: this.elements[field],\n            field: field,\n            options: this.fields[field],\n        });\n        return this;\n    };\n    /**\n     * Remove given field by name\n     *\n     * @param {string} field The field name\n     * @return {Core}\n     */\n    Core.prototype.removeField = function (field) {\n        if (!this.fields[field]) {\n            throw new Error(\"The field \".concat(field, \" validators are not defined. Please ensure the field is added first\"));\n        }\n        var elements = this.elements[field];\n        var options = this.fields[field];\n        delete this.elements[field];\n        delete this.fields[field];\n        this.emit('core.field.removed', {\n            elements: elements,\n            field: field,\n            options: options,\n        });\n        return this;\n    };\n    /**\n     * Validate all fields\n     *\n     * @return {Promise<string>}\n     */\n    Core.prototype.validate = function () {\n        var _this = this;\n        this.emit('core.form.validating', {\n            formValidation: this,\n        });\n        return this.filter.execute('validate-pre', Promise.resolve(), []).then(function () {\n            return Promise.all(Object.keys(_this.fields).map(function (field) { return _this.validateField(field); })).then(function (results) {\n                // `results` is an array of `Valid`, `Invalid` and `NotValidated`\n                switch (true) {\n                    case results.indexOf('Invalid') !== -1:\n                        _this.emit('core.form.invalid', {\n                            formValidation: _this,\n                        });\n                        return Promise.resolve('Invalid');\n                    case results.indexOf('NotValidated') !== -1:\n                        _this.emit('core.form.notvalidated', {\n                            formValidation: _this,\n                        });\n                        return Promise.resolve('NotValidated');\n                    default:\n                        _this.emit('core.form.valid', {\n                            formValidation: _this,\n                        });\n                        return Promise.resolve('Valid');\n                }\n            });\n        });\n    };\n    /**\n     * Validate a particular field\n     *\n     * @param {string} field The field name\n     * @return {Promise<string>}\n     */\n    Core.prototype.validateField = function (field) {\n        var _this = this;\n        // Stop validation process if the field is already validated\n        var result = this.results.get(field);\n        if (result === 'Valid' || result === 'Invalid') {\n            return Promise.resolve(result);\n        }\n        this.emit('core.field.validating', field);\n        var elements = this.elements[field];\n        if (elements.length === 0) {\n            this.emit('core.field.valid', field);\n            return Promise.resolve('Valid');\n        }\n        var type = elements[0].getAttribute('type');\n        if ('radio' === type || 'checkbox' === type || elements.length === 1) {\n            return this.validateElement(field, elements[0]);\n        }\n        else {\n            return Promise.all(elements.map(function (ele) { return _this.validateElement(field, ele); })).then(function (results) {\n                // `results` is an array of `Valid`, `Invalid` and `NotValidated`\n                switch (true) {\n                    case results.indexOf('Invalid') !== -1:\n                        _this.emit('core.field.invalid', field);\n                        _this.results.set(field, 'Invalid');\n                        return Promise.resolve('Invalid');\n                    case results.indexOf('NotValidated') !== -1:\n                        _this.emit('core.field.notvalidated', field);\n                        _this.results.delete(field);\n                        return Promise.resolve('NotValidated');\n                    default:\n                        _this.emit('core.field.valid', field);\n                        _this.results.set(field, 'Valid');\n                        return Promise.resolve('Valid');\n                }\n            });\n        }\n    };\n    /**\n     * Validate particular element\n     *\n     * @param {string} field The field name\n     * @param {HTMLElement} ele The field element\n     * @return {Promise<string>}\n     */\n    Core.prototype.validateElement = function (field, ele) {\n        var _this = this;\n        // Reset validation result\n        this.results.delete(field);\n        var elements = this.elements[field];\n        var ignored = this.filter.execute('element-ignored', false, [field, ele, elements]);\n        if (ignored) {\n            this.emit('core.element.ignored', {\n                element: ele,\n                elements: elements,\n                field: field,\n            });\n            return Promise.resolve('Ignored');\n        }\n        var validatorList = this.fields[field].validators;\n        this.emit('core.element.validating', {\n            element: ele,\n            elements: elements,\n            field: field,\n        });\n        var promises = Object.keys(validatorList).map(function (v) {\n            return function () { return _this.executeValidator(field, ele, v, validatorList[v]); };\n        });\n        return this.waterfall(promises)\n            .then(function (results) {\n            // `results` is an array of `Valid` or `Invalid`\n            var isValid = results.indexOf('Invalid') === -1;\n            _this.emit('core.element.validated', {\n                element: ele,\n                elements: elements,\n                field: field,\n                valid: isValid,\n            });\n            var type = ele.getAttribute('type');\n            if ('radio' === type || 'checkbox' === type || elements.length === 1) {\n                _this.emit(isValid ? 'core.field.valid' : 'core.field.invalid', field);\n            }\n            return Promise.resolve(isValid ? 'Valid' : 'Invalid');\n        })\n            .catch(function (reason) {\n            // reason is `NotValidated`\n            _this.emit('core.element.notvalidated', {\n                element: ele,\n                elements: elements,\n                field: field,\n            });\n            return Promise.resolve(reason);\n        });\n    };\n    /**\n     * Perform given validator on field\n     *\n     * @param {string} field The field name\n     * @param {HTMLElement} ele The field element\n     * @param {string} v The validator name\n     * @param {ValidatorOptions} opts The validator options\n     * @return {Promise<string>}\n     */\n    Core.prototype.executeValidator = function (field, ele, v, opts) {\n        var _this = this;\n        var elements = this.elements[field];\n        var name = this.filter.execute('validator-name', v, [v, field]);\n        opts.message = this.filter.execute('validator-message', opts.message, [this.locale, field, name]);\n        // Simply pass the validator if\n        // - it isn't defined yet\n        // - or the associated validator isn't enabled\n        if (!this.validators[name] || opts.enabled === false) {\n            this.emit('core.validator.validated', {\n                element: ele,\n                elements: elements,\n                field: field,\n                result: this.normalizeResult(field, name, { valid: true }),\n                validator: name,\n            });\n            return Promise.resolve('Valid');\n        }\n        var validator = this.validators[name];\n        // Get the field value\n        var value = this.getElementValue(field, ele, name);\n        var willValidate = this.filter.execute('field-should-validate', true, [field, ele, value, v]);\n        if (!willValidate) {\n            this.emit('core.validator.notvalidated', {\n                element: ele,\n                elements: elements,\n                field: field,\n                validator: v,\n            });\n            return Promise.resolve('NotValidated');\n        }\n        this.emit('core.validator.validating', {\n            element: ele,\n            elements: elements,\n            field: field,\n            validator: v,\n        });\n        // Perform validation\n        var result = validator().validate({\n            element: ele,\n            elements: elements,\n            field: field,\n            l10n: this.localization,\n            options: opts,\n            value: value,\n        });\n        // Check whether the result is a `Promise`\n        var isPromise = 'function' === typeof result['then'];\n        if (isPromise) {\n            return result.then(function (r) {\n                var data = _this.normalizeResult(field, v, r);\n                _this.emit('core.validator.validated', {\n                    element: ele,\n                    elements: elements,\n                    field: field,\n                    result: data,\n                    validator: v,\n                });\n                return data.valid ? 'Valid' : 'Invalid';\n            });\n        }\n        else {\n            var data = this.normalizeResult(field, v, result);\n            this.emit('core.validator.validated', {\n                element: ele,\n                elements: elements,\n                field: field,\n                result: data,\n                validator: v,\n            });\n            return Promise.resolve(data.valid ? 'Valid' : 'Invalid');\n        }\n    };\n    Core.prototype.getElementValue = function (field, ele, validator) {\n        var defaultValue = getFieldValue(this.form, field, ele, this.elements[field]);\n        return this.filter.execute('field-value', defaultValue, [defaultValue, field, ele, validator]);\n    };\n    // Some getter methods\n    Core.prototype.getElements = function (field) {\n        return this.elements[field];\n    };\n    Core.prototype.getFields = function () {\n        return this.fields;\n    };\n    Core.prototype.getFormElement = function () {\n        return this.form;\n    };\n    Core.prototype.getLocale = function () {\n        return this.locale;\n    };\n    Core.prototype.getPlugin = function (name) {\n        return this.plugins[name];\n    };\n    /**\n     * Update the field status\n     *\n     * @param {string} field The field name\n     * @param {string} status The new status\n     * @param {string} [validator] The validator name. If it isn't specified, all validators will be updated\n     * @return {Core}\n     */\n    Core.prototype.updateFieldStatus = function (field, status, validator) {\n        var _this = this;\n        var elements = this.elements[field];\n        var type = elements[0].getAttribute('type');\n        var list = 'radio' === type || 'checkbox' === type ? [elements[0]] : elements;\n        list.forEach(function (ele) { return _this.updateElementStatus(field, ele, status, validator); });\n        if (!validator) {\n            switch (status) {\n                case 'NotValidated':\n                    this.emit('core.field.notvalidated', field);\n                    this.results.delete(field);\n                    break;\n                case 'Validating':\n                    this.emit('core.field.validating', field);\n                    this.results.delete(field);\n                    break;\n                case 'Valid':\n                    this.emit('core.field.valid', field);\n                    this.results.set(field, 'Valid');\n                    break;\n                case 'Invalid':\n                    this.emit('core.field.invalid', field);\n                    this.results.set(field, 'Invalid');\n                    break;\n            }\n        }\n        else if (status === 'Invalid') {\n            // We need to mark the field as invalid because it doesn't pass the `validator`\n            this.emit('core.field.invalid', field);\n            this.results.set(field, 'Invalid');\n        }\n        return this;\n    };\n    /**\n     * Update the element status\n     *\n     * @param {string} field The field name\n     * @param {HTMLElement} ele The field element\n     * @param {string} status The new status\n     * @param {string} [validator] The validator name. If it isn't specified, all validators will be updated\n     * @return {Core}\n     */\n    Core.prototype.updateElementStatus = function (field, ele, status, validator) {\n        var _this = this;\n        var elements = this.elements[field];\n        var fieldValidators = this.fields[field].validators;\n        var validatorArr = validator ? [validator] : Object.keys(fieldValidators);\n        switch (status) {\n            case 'NotValidated':\n                validatorArr.forEach(function (v) {\n                    return _this.emit('core.validator.notvalidated', {\n                        element: ele,\n                        elements: elements,\n                        field: field,\n                        validator: v,\n                    });\n                });\n                this.emit('core.element.notvalidated', {\n                    element: ele,\n                    elements: elements,\n                    field: field,\n                });\n                break;\n            case 'Validating':\n                validatorArr.forEach(function (v) {\n                    return _this.emit('core.validator.validating', {\n                        element: ele,\n                        elements: elements,\n                        field: field,\n                        validator: v,\n                    });\n                });\n                this.emit('core.element.validating', {\n                    element: ele,\n                    elements: elements,\n                    field: field,\n                });\n                break;\n            case 'Valid':\n                validatorArr.forEach(function (v) {\n                    return _this.emit('core.validator.validated', {\n                        element: ele,\n                        elements: elements,\n                        field: field,\n                        result: {\n                            message: fieldValidators[v].message,\n                            valid: true,\n                        },\n                        validator: v,\n                    });\n                });\n                this.emit('core.element.validated', {\n                    element: ele,\n                    elements: elements,\n                    field: field,\n                    valid: true,\n                });\n                break;\n            case 'Invalid':\n                validatorArr.forEach(function (v) {\n                    return _this.emit('core.validator.validated', {\n                        element: ele,\n                        elements: elements,\n                        field: field,\n                        result: {\n                            message: fieldValidators[v].message,\n                            valid: false,\n                        },\n                        validator: v,\n                    });\n                });\n                this.emit('core.element.validated', {\n                    element: ele,\n                    elements: elements,\n                    field: field,\n                    valid: false,\n                });\n                break;\n        }\n        return this;\n    };\n    /**\n     * Reset the form. It also clears all the messages, hide the feedback icons, etc.\n     *\n     * @param {boolean} reset If true, the method resets field value to empty\n     * or remove `checked`, `selected` attributes\n     * @return {Core}\n     */\n    Core.prototype.resetForm = function (reset) {\n        var _this = this;\n        Object.keys(this.fields).forEach(function (field) { return _this.resetField(field, reset); });\n        this.emit('core.form.reset', {\n            formValidation: this,\n            reset: reset,\n        });\n        return this;\n    };\n    /**\n     * Reset the field. It also clears all the messages, hide the feedback icons, etc.\n     *\n     * @param {string} field The field name\n     * @param {boolean} reset If true, the method resets field value to empty\n     * or remove `checked`, `selected` attributes\n     * @return {Core}\n     */\n    Core.prototype.resetField = function (field, reset) {\n        // Reset the field element value if needed\n        if (reset) {\n            var elements = this.elements[field];\n            var type_1 = elements[0].getAttribute('type');\n            elements.forEach(function (ele) {\n                if ('radio' === type_1 || 'checkbox' === type_1) {\n                    ele.removeAttribute('selected');\n                    ele.removeAttribute('checked');\n                    ele.checked = false;\n                }\n                else {\n                    ele.setAttribute('value', '');\n                    if (ele instanceof HTMLInputElement || ele instanceof HTMLTextAreaElement) {\n                        ele.value = '';\n                    }\n                }\n            });\n        }\n        // Mark the field as not validated yet\n        this.updateFieldStatus(field, 'NotValidated');\n        this.emit('core.field.reset', {\n            field: field,\n            reset: reset,\n        });\n        return this;\n    };\n    /**\n     * Revalidate a particular field. It's useful when the field value is effected by third parties\n     * (for example, attach another UI library to the field).\n     * Since there isn't an automatic way for FormValidation to know when the field value is modified in those cases,\n     * we need to revalidate the field manually.\n     *\n     * @param {string} field The field name\n     * @return {Promise<string>}\n     */\n    Core.prototype.revalidateField = function (field) {\n        if (!this.fields[field]) {\n            return Promise.resolve('Ignored');\n        }\n        this.updateFieldStatus(field, 'NotValidated');\n        return this.validateField(field);\n    };\n    /**\n     * Disable particular validator for given field\n     *\n     * @param {string} field The field name\n     * @param {string} validator The validator name. If it isn't specified, all validators will be disabled\n     * @return {Core}\n     */\n    Core.prototype.disableValidator = function (field, validator) {\n        if (!this.fields[field]) {\n            return this;\n        }\n        var elements = this.elements[field];\n        this.toggleValidator(false, field, validator);\n        this.emit('core.validator.disabled', {\n            elements: elements,\n            field: field,\n            formValidation: this,\n            validator: validator,\n        });\n        return this;\n    };\n    /**\n     * Enable particular validator for given field\n     *\n     * @param {string} field The field name\n     * @param {string} validator The validator name. If it isn't specified, all validators will be enabled\n     * @return {Core}\n     */\n    Core.prototype.enableValidator = function (field, validator) {\n        if (!this.fields[field]) {\n            return this;\n        }\n        var elements = this.elements[field];\n        this.toggleValidator(true, field, validator);\n        this.emit('core.validator.enabled', {\n            elements: elements,\n            field: field,\n            formValidation: this,\n            validator: validator,\n        });\n        return this;\n    };\n    /**\n     * Update option of particular validator for given field\n     *\n     * @param {string} field The field name\n     * @param {string} validator The validator name\n     * @param {string} name The option's name\n     * @param {unknown} value The option's value\n     * @return {Core}\n     */\n    Core.prototype.updateValidatorOption = function (field, validator, name, value) {\n        if (this.fields[field] && this.fields[field].validators && this.fields[field].validators[validator]) {\n            this.fields[field].validators[validator][name] = value;\n        }\n        return this;\n    };\n    Core.prototype.setFieldOptions = function (field, options) {\n        this.fields[field] = options;\n        return this;\n    };\n    Core.prototype.destroy = function () {\n        var _this = this;\n        // Remove plugins and filters\n        Object.keys(this.plugins).forEach(function (id) { return _this.plugins[id].uninstall(); });\n        this.ee.clear();\n        this.filter.clear();\n        this.results.clear();\n        this.plugins = {};\n        return this;\n    };\n    Core.prototype.setLocale = function (locale, localization) {\n        this.locale = locale;\n        this.localization = localization;\n        return this;\n    };\n    Core.prototype.waterfall = function (promises) {\n        return promises.reduce(function (p, c) {\n            return p.then(function (res) {\n                return c().then(function (result) {\n                    res.push(result);\n                    return res;\n                });\n            });\n        }, Promise.resolve([]));\n    };\n    Core.prototype.queryElements = function (field) {\n        var selector = this.fields[field].selector\n            ? // Check if the selector is an ID selector which starts with `#`\n                '#' === this.fields[field].selector.charAt(0)\n                    ? \"[id=\\\"\".concat(this.fields[field].selector.substring(1), \"\\\"]\")\n                    : this.fields[field].selector\n            : \"[name=\\\"\".concat(field.replace(/\"/g, '\\\\\"'), \"\\\"]\");\n        return [].slice.call(this.form.querySelectorAll(selector));\n    };\n    Core.prototype.normalizeResult = function (field, validator, result) {\n        var opts = this.fields[field].validators[validator];\n        return Object.assign({}, result, {\n            message: result.message ||\n                (opts ? opts.message : '') ||\n                (this.localization && this.localization[validator] && this.localization[validator]['default']\n                    ? this.localization[validator]['default']\n                    : '') ||\n                \"The field \".concat(field, \" is not valid\"),\n        });\n    };\n    Core.prototype.toggleValidator = function (enabled, field, validator) {\n        var _this = this;\n        var validatorArr = this.fields[field].validators;\n        if (validator && validatorArr && validatorArr[validator]) {\n            this.fields[field].validators[validator].enabled = enabled;\n        }\n        else if (!validator) {\n            Object.keys(validatorArr).forEach(function (v) { return (_this.fields[field].validators[v].enabled = enabled); });\n        }\n        return this.updateFieldStatus(field, 'NotValidated', validator);\n    };\n    return Core;\n}());\nfunction formValidation(form, options) {\n    var opts = Object.assign({}, {\n        fields: {},\n        locale: 'en_US',\n        plugins: {},\n        init: function (_) { },\n    }, options);\n    var core = new Core(form, opts.fields);\n    core.setLocale(opts.locale, opts.localization);\n    // Register plugins\n    Object.keys(opts.plugins).forEach(function (name) { return core.registerPlugin(name, opts.plugins[name]); });\n    // It's the single point that users can do a particular task before adding fields\n    // Some initialization tasks must be done at that point\n    opts.init(core);\n    // and add fields\n    Object.keys(opts.fields).forEach(function (field) { return core.addField(field, opts.fields[field]); });\n    return core;\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar Plugin = /** @class */ (function () {\n    function Plugin(opts) {\n        this.opts = opts;\n        this.isEnabled = true;\n    }\n    Plugin.prototype.setCore = function (core) {\n        this.core = core;\n        return this;\n    };\n    Plugin.prototype.enable = function () {\n        this.isEnabled = true;\n        this.onEnabled();\n        return this;\n    };\n    Plugin.prototype.disable = function () {\n        this.isEnabled = false;\n        this.onDisabled();\n        return this;\n    };\n    Plugin.prototype.isPluginEnabled = function () {\n        return this.isEnabled;\n    };\n    Plugin.prototype.onEnabled = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n    Plugin.prototype.onDisabled = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n    Plugin.prototype.install = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n    Plugin.prototype.uninstall = function () { }; // eslint-disable-line @typescript-eslint/no-empty-function\n    return Plugin;\n}());\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * Execute a callback function\n *\n * @param {Function | string} functionName Can be\n * - name of global function\n * - name of namespace function (such as A.B.C)\n * - a function\n * @param {any[]} args The callback arguments\n * @return {any}\n */\nfunction call(functionName, args) {\n    if ('function' === typeof functionName) {\n        return functionName.apply(this, args);\n    }\n    else if ('string' === typeof functionName) {\n        // Node that it doesn't support node.js based environment because we are trying to access `window`\n        var name_1 = functionName;\n        if ('()' === name_1.substring(name_1.length - 2)) {\n            name_1 = name_1.substring(0, name_1.length - 2);\n        }\n        var ns = name_1.split('.');\n        var func = ns.pop();\n        var context_1 = window;\n        for (var _i = 0, ns_1 = ns; _i < ns_1.length; _i++) {\n            var t = ns_1[_i];\n            context_1 = context_1[t];\n        }\n        return typeof context_1[func] === 'undefined' ? null : context_1[func].apply(this, args);\n    }\n}\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar addClass = function (element, classes) {\n    classes.split(' ').forEach(function (clazz) {\n        if (element.classList) {\n            element.classList.add(clazz);\n        }\n        else if (\" \".concat(element.className, \" \").indexOf(\" \".concat(clazz, \" \"))) {\n            element.className += \" \".concat(clazz);\n        }\n    });\n};\nvar removeClass = function (element, classes) {\n    classes.split(' ').forEach(function (clazz) {\n        element.classList\n            ? element.classList.remove(clazz)\n            : (element.className = element.className.replace(clazz, ''));\n    });\n};\nvar classSet = function (element, classes) {\n    var adding = [];\n    var removing = [];\n    Object.keys(classes).forEach(function (clazz) {\n        if (clazz) {\n            classes[clazz] ? adding.push(clazz) : removing.push(clazz);\n        }\n    });\n    // Always remove before adding class because there might be a class which belong to both sets.\n    // For example, the element will have class `a` after calling\n    //  ```\n    //  classSet(element, {\n    //      'a a1 a2': true,\n    //      'a b1 b2': false\n    //  })\n    //  ```\n    removing.forEach(function (clazz) { return removeClass(element, clazz); });\n    adding.forEach(function (clazz) { return addClass(element, clazz); });\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar matches = function (element, selector) {\n    var nativeMatches = element.matches ||\n        element.webkitMatchesSelector ||\n        element['mozMatchesSelector'] ||\n        element['msMatchesSelector'];\n    if (nativeMatches) {\n        return nativeMatches.call(element, selector);\n    }\n    // In case `matchesselector` isn't supported (such as IE10)\n    // See http://caniuse.com/matchesselector\n    var nodes = [].slice.call(element.parentElement.querySelectorAll(selector));\n    return nodes.indexOf(element) >= 0;\n};\nvar closest = function (element, selector) {\n    var ele = element;\n    while (ele) {\n        if (matches(ele, selector)) {\n            break;\n        }\n        ele = ele.parentElement;\n    }\n    return ele;\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar generateString = function (length) {\n    return Array(length)\n        .fill('')\n        .map(function (v) { return Math.random().toString(36).charAt(2); })\n        .join('');\n};\nvar fetch = function (url, options) {\n    var toQuery = function (obj) {\n        return Object.keys(obj)\n            .map(function (k) { return \"\".concat(encodeURIComponent(k), \"=\").concat(encodeURIComponent(obj[k])); })\n            .join('&');\n    };\n    return new Promise(function (resolve, reject) {\n        var opts = Object.assign({}, {\n            crossDomain: false,\n            headers: {},\n            method: 'GET',\n            params: {},\n        }, options);\n        // Build the params for GET request\n        var params = Object.keys(opts.params)\n            .map(function (k) { return \"\".concat(encodeURIComponent(k), \"=\").concat(encodeURIComponent(opts.params[k])); })\n            .join('&');\n        var hasQuery = url.indexOf('?') > -1;\n        var requestUrl = 'GET' === opts.method ? \"\".concat(url).concat(hasQuery ? '&' : '?').concat(params) : url;\n        if (opts.crossDomain) {\n            // User is making cross domain request\n            var script_1 = document.createElement('script');\n            // In some very fast systems, the different `Date.now()` invocations can return the same value\n            // which leads to the issue where there are multiple remove validators are used, for example.\n            // Appending it with a generated random string can fix the value\n            var callback_1 = \"___FormValidationFetch_\".concat(generateString(12), \"___\");\n            window[callback_1] = function (data) {\n                delete window[callback_1];\n                resolve(data);\n            };\n            script_1.src = \"\".concat(requestUrl).concat(hasQuery ? '&' : '?', \"callback=\").concat(callback_1);\n            script_1.async = true;\n            script_1.addEventListener('load', function () {\n                script_1.parentNode.removeChild(script_1);\n            });\n            script_1.addEventListener('error', function () { return reject; });\n            document.head.appendChild(script_1);\n        }\n        else {\n            var request_1 = new XMLHttpRequest();\n            request_1.open(opts.method, requestUrl);\n            // Set the headers\n            request_1.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n            if ('POST' === opts.method) {\n                request_1.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n            }\n            Object.keys(opts.headers).forEach(function (k) { return request_1.setRequestHeader(k, opts.headers[k]); });\n            request_1.addEventListener('load', function () {\n                // Cannot use arrow function here due to the `this` scope\n                resolve(JSON.parse(this.responseText));\n            });\n            request_1.addEventListener('error', function () { return reject; });\n            // GET request will ignore the passed data here\n            request_1.send(toQuery(opts.params));\n        }\n    });\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * Format a string\n * It's used to format the error message\n * format('The field must between %s and %s', [10, 20]) = 'The field must between 10 and 20'\n *\n * @param {string} message\n * @param {string|string[]} parameters\n * @returns {string}\n */\nvar format = function (message, parameters) {\n    var params = Array.isArray(parameters) ? parameters : [parameters];\n    var output = message;\n    params.forEach(function (p) {\n        output = output.replace('%s', p);\n    });\n    return output;\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar hasClass = function (element, clazz) {\n    return element.classList\n        ? element.classList.contains(clazz)\n        : new RegExp(\"(^| )\".concat(clazz, \"( |$)\"), 'gi').test(element.className);\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\n/**\n * Validate a date\n *\n * @param {string} year The full year in 4 digits\n * @param {string} month The month number\n * @param {string} day The day number\n * @param {boolean} [notInFuture] If true, the date must not be in the future\n * @returns {boolean}\n */\nvar isValidDate = function (year, month, day, notInFuture) {\n    if (isNaN(year) || isNaN(month) || isNaN(day)) {\n        return false;\n    }\n    if (year < 1000 || year > 9999 || month <= 0 || month > 12) {\n        return false;\n    }\n    var numDays = [\n        31,\n        // Update the number of days in Feb of leap year\n        year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0) ? 29 : 28,\n        31,\n        30,\n        31,\n        30,\n        31,\n        31,\n        30,\n        31,\n        30,\n        31,\n    ];\n    // Check the day\n    if (day <= 0 || day > numDays[month - 1]) {\n        return false;\n    }\n    if (notInFuture === true) {\n        var currentDate = new Date();\n        var currentYear = currentDate.getFullYear();\n        var currentMonth = currentDate.getMonth();\n        var currentDay = currentDate.getDate();\n        return (year < currentYear ||\n            (year === currentYear && month - 1 < currentMonth) ||\n            (year === currentYear && month - 1 === currentMonth && day < currentDay));\n    }\n    return true;\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar removeUndefined = function (obj) {\n    return obj\n        ? Object.entries(obj).reduce(function (a, _a) {\n            var k = _a[0], v = _a[1];\n            return (v === undefined ? a : ((a[k] = v), a));\n        }, {})\n        : {};\n};\n\n/**\n * FormValidation (https://formvalidation.io)\n * The best validation library for JavaScript\n * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>\n */\nvar index = {\n    call: call,\n    classSet: classSet,\n    closest: closest,\n    fetch: fetch,\n    format: format,\n    hasClass: hasClass,\n    isValidDate: isValidDate,\n    removeUndefined: removeUndefined,\n};\n\nexports.Plugin = Plugin;\nexports.algorithms = index$1;\nexports.formValidation = formValidation;\nexports.utils = index;\n\n\n//# <AUTHOR> <EMAIL>\n */\n\n\n\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/index.js */ "./node_modules/@form-validation/core/lib/cjs/index.js");\n}\n\n\n//# <AUTHOR> <EMAIL>\n */\nvar classSet = core.utils.classSet, hasClass = core.utils.hasClass;\nvar Bootstrap5 = /** @class */ (function (_super) {\n    __extends(Bootstrap5, _super);\n    function Bootstrap5(opts) {\n        var _this = _super.call(this, Object.assign({}, {\n            eleInvalidClass: 'is-invalid',\n            eleValidClass: 'is-valid',\n            formClass: 'fv-plugins-bootstrap5',\n            rowInvalidClass: 'fv-plugins-bootstrap5-row-invalid',\n            rowPattern: /^(.*)(col|offset)(-(sm|md|lg|xl))*-[0-9]+(.*)$/,\n            rowSelector: '.row',\n            rowValidClass: 'fv-plugins-bootstrap5-row-valid',\n        }, opts)) || this;\n        _this.eleValidatedHandler = _this.handleElementValidated.bind(_this);\n        return _this;\n    }\n    Bootstrap5.prototype.install = function () {\n        _super.prototype.install.call(this);\n        this.core.on('core.element.validated', this.eleValidatedHandler);\n    };\n    Bootstrap5.prototype.uninstall = function () {\n        _super.prototype.uninstall.call(this);\n        this.core.off('core.element.validated', this.eleValidatedHandler);\n    };\n    Bootstrap5.prototype.handleElementValidated = function (e) {\n        var type = e.element.getAttribute('type');\n        // If we use more than 1 inline checkbox/radio, we need to add `is-invalid` for the `form-check` container\n        // so the error messages are displayed properly.\n        // The markup looks like as following:\n        //  <div class=\"form-check form-check-inline is-invalid\">\n        //      <input type=\"checkbox\" class=\"form-check-input is-invalid\" />\n        //      <label class=\"form-check-label\">...</label>\n        //  <div>\n        //  \x3c!-- Other inline checkboxes/radios go here --\x3e\n        //  \x3c!-- Then message element --\x3e\n        //  <div class=\"fv-plugins-message-container invalid-feedback\">...</div>\n        if (('checkbox' === type || 'radio' === type) &&\n            e.elements.length > 1 &&\n            hasClass(e.element, 'form-check-input')) {\n            var inputParent = e.element.parentElement;\n            if (hasClass(inputParent, 'form-check') && hasClass(inputParent, 'form-check-inline')) {\n                classSet(inputParent, {\n                    'is-invalid': !e.valid,\n                    'is-valid': e.valid,\n                });\n            }\n        }\n    };\n    Bootstrap5.prototype.onIconPlaced = function (e) {\n        // Disable the default icon of Bootstrap 5\n        classSet(e.element, {\n            'fv-plugins-icon-input': true,\n        });\n        // Adjust icon place if the field belongs to a `input-group`\n        var parent = e.element.parentElement;\n        if (hasClass(parent, 'input-group')) {\n            parent.parentElement.insertBefore(e.iconElement, parent.nextSibling);\n            if (e.element.nextElementSibling && hasClass(e.element.nextElementSibling, 'input-group-text')) {\n                classSet(e.iconElement, {\n                    'fv-plugins-icon-input-group': true,\n                });\n            }\n        }\n        var type = e.element.getAttribute('type');\n        if ('checkbox' === type || 'radio' === type) {\n            var grandParent = parent.parentElement;\n            // Place it after the container of checkbox/radio\n            if (hasClass(parent, 'form-check')) {\n                classSet(e.iconElement, {\n                    'fv-plugins-icon-check': true,\n                });\n                parent.parentElement.insertBefore(e.iconElement, parent.nextSibling);\n            }\n            else if (hasClass(parent.parentElement, 'form-check')) {\n                classSet(e.iconElement, {\n                    'fv-plugins-icon-check': true,\n                });\n                grandParent.parentElement.insertBefore(e.iconElement, grandParent.nextSibling);\n            }\n        }\n    };\n    Bootstrap5.prototype.onMessagePlaced = function (e) {\n        e.messageElement.classList.add('invalid-feedback');\n        // Check if the input is placed inside an `input-group` element\n        var inputParent = e.element.parentElement;\n        if (hasClass(inputParent, 'input-group')) {\n            // The markup looks like\n            //  <div class=\"input-group\">\n            //      <span class=\"input-group-text\">...</span>\n            //      <input type=\"text\" class=\"form-control\" />\n            //      \x3c!-- We will place the error message here, at the end of parent --\x3e\n            //  </div>\n            inputParent.appendChild(e.messageElement);\n            // Keep the border radius of the right corners\n            classSet(inputParent, {\n                'has-validation': true,\n            });\n            return;\n        }\n        var type = e.element.getAttribute('type');\n        if (('checkbox' === type || 'radio' === type) &&\n            hasClass(e.element, 'form-check-input') &&\n            hasClass(inputParent, 'form-check') &&\n            !hasClass(inputParent, 'form-check-inline')) {\n            // Place the message inside the `form-check` container of the last checkbox/radio\n            e.elements[e.elements.length - 1].parentElement.appendChild(e.messageElement);\n        }\n    };\n    return Bootstrap5;\n}(pluginFramework.Framework));\n\nexports.Bootstrap5 = Bootstrap5;\n\n\n//# <AUTHOR> <EMAIL>\n */\n\n\n\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/index.js */ "./node_modules/@form-validation/plugin-bootstrap5/lib/cjs/index.js");\n}\n\n\n//# <AUTHOR> <EMAIL>\n */\nvar classSet = core.utils.classSet, closest = core.utils.closest;\nvar Framework = /** @class */ (function (_super) {\n    __extends(Framework, _super);\n    function Framework(opts) {\n        var _this = _super.call(this, opts) || this;\n        _this.results = new Map();\n        _this.containers = new Map();\n        _this.opts = Object.assign({}, {\n            defaultMessageContainer: true,\n            eleInvalidClass: '',\n            eleValidClass: '',\n            rowClasses: '',\n            rowValidatingClass: '',\n        }, opts);\n        _this.elementIgnoredHandler = _this.onElementIgnored.bind(_this);\n        _this.elementValidatingHandler = _this.onElementValidating.bind(_this);\n        _this.elementValidatedHandler = _this.onElementValidated.bind(_this);\n        _this.elementNotValidatedHandler = _this.onElementNotValidated.bind(_this);\n        _this.iconPlacedHandler = _this.onIconPlaced.bind(_this);\n        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n        _this.messagePlacedHandler = _this.onMessagePlaced.bind(_this);\n        return _this;\n    }\n    Framework.prototype.install = function () {\n        var _a;\n        var _this = this;\n        classSet(this.core.getFormElement(), (_a = {},\n            _a[this.opts.formClass] = true,\n            _a['fv-plugins-framework'] = true,\n            _a));\n        this.core\n            .on('core.element.ignored', this.elementIgnoredHandler)\n            .on('core.element.validating', this.elementValidatingHandler)\n            .on('core.element.validated', this.elementValidatedHandler)\n            .on('core.element.notvalidated', this.elementNotValidatedHandler)\n            .on('plugins.icon.placed', this.iconPlacedHandler)\n            .on('core.field.added', this.fieldAddedHandler)\n            .on('core.field.removed', this.fieldRemovedHandler);\n        if (this.opts.defaultMessageContainer) {\n            this.core.registerPlugin(Framework.MESSAGE_PLUGIN, new pluginMessage.Message({\n                clazz: this.opts.messageClass,\n                container: function (field, element) {\n                    var selector = 'string' === typeof _this.opts.rowSelector\n                        ? _this.opts.rowSelector\n                        : _this.opts.rowSelector(field, element);\n                    var groupEle = closest(element, selector);\n                    return pluginMessage.Message.getClosestContainer(element, groupEle, _this.opts.rowPattern);\n                },\n            }));\n            this.core.on('plugins.message.placed', this.messagePlacedHandler);\n        }\n    };\n    Framework.prototype.uninstall = function () {\n        var _a;\n        this.results.clear();\n        this.containers.clear();\n        classSet(this.core.getFormElement(), (_a = {},\n            _a[this.opts.formClass] = false,\n            _a['fv-plugins-framework'] = false,\n            _a));\n        this.core\n            .off('core.element.ignored', this.elementIgnoredHandler)\n            .off('core.element.validating', this.elementValidatingHandler)\n            .off('core.element.validated', this.elementValidatedHandler)\n            .off('core.element.notvalidated', this.elementNotValidatedHandler)\n            .off('plugins.icon.placed', this.iconPlacedHandler)\n            .off('core.field.added', this.fieldAddedHandler)\n            .off('core.field.removed', this.fieldRemovedHandler);\n        if (this.opts.defaultMessageContainer) {\n            this.core.deregisterPlugin(Framework.MESSAGE_PLUGIN);\n            this.core.off('plugins.message.placed', this.messagePlacedHandler);\n        }\n    };\n    Framework.prototype.onEnabled = function () {\n        var _a;\n        classSet(this.core.getFormElement(), (_a = {},\n            _a[this.opts.formClass] = true,\n            _a));\n        if (this.opts.defaultMessageContainer) {\n            this.core.enablePlugin(Framework.MESSAGE_PLUGIN);\n        }\n    };\n    Framework.prototype.onDisabled = function () {\n        var _a;\n        classSet(this.core.getFormElement(), (_a = {},\n            _a[this.opts.formClass] = false,\n            _a));\n        if (this.opts.defaultMessageContainer) {\n            this.core.disablePlugin(Framework.MESSAGE_PLUGIN);\n        }\n    };\n    Framework.prototype.onIconPlaced = function (_e) { }; // eslint-disable-line @typescript-eslint/no-empty-function\n    Framework.prototype.onMessagePlaced = function (_e) { }; // eslint-disable-line @typescript-eslint/no-empty-function\n    Framework.prototype.onFieldAdded = function (e) {\n        var _this = this;\n        var elements = e.elements;\n        if (elements) {\n            elements.forEach(function (ele) {\n                var _a;\n                var groupEle = _this.containers.get(ele);\n                if (groupEle) {\n                    classSet(groupEle, (_a = {},\n                        _a[_this.opts.rowInvalidClass] = false,\n                        _a[_this.opts.rowValidatingClass] = false,\n                        _a[_this.opts.rowValidClass] = false,\n                        _a['fv-plugins-icon-container'] = false,\n                        _a));\n                    _this.containers.delete(ele);\n                }\n            });\n            this.prepareFieldContainer(e.field, elements);\n        }\n    };\n    Framework.prototype.onFieldRemoved = function (e) {\n        var _this = this;\n        e.elements.forEach(function (ele) {\n            var _a;\n            var groupEle = _this.containers.get(ele);\n            if (groupEle) {\n                classSet(groupEle, (_a = {},\n                    _a[_this.opts.rowInvalidClass] = false,\n                    _a[_this.opts.rowValidatingClass] = false,\n                    _a[_this.opts.rowValidClass] = false,\n                    _a));\n            }\n        });\n    };\n    Framework.prototype.prepareFieldContainer = function (field, elements) {\n        var _this = this;\n        if (elements.length) {\n            var type = elements[0].getAttribute('type');\n            if ('radio' === type || 'checkbox' === type) {\n                this.prepareElementContainer(field, elements[0]);\n            }\n            else {\n                elements.forEach(function (ele) { return _this.prepareElementContainer(field, ele); });\n            }\n        }\n    };\n    Framework.prototype.prepareElementContainer = function (field, element) {\n        var _a;\n        var selector = 'string' === typeof this.opts.rowSelector ? this.opts.rowSelector : this.opts.rowSelector(field, element);\n        var groupEle = closest(element, selector);\n        if (groupEle !== element) {\n            classSet(groupEle, (_a = {},\n                _a[this.opts.rowClasses] = true,\n                _a['fv-plugins-icon-container'] = true,\n                _a));\n            this.containers.set(element, groupEle);\n        }\n    };\n    Framework.prototype.onElementValidating = function (e) {\n        this.removeClasses(e.element, e.elements);\n    };\n    Framework.prototype.onElementNotValidated = function (e) {\n        this.removeClasses(e.element, e.elements);\n    };\n    Framework.prototype.onElementIgnored = function (e) {\n        this.removeClasses(e.element, e.elements);\n    };\n    Framework.prototype.removeClasses = function (element, elements) {\n        var _a;\n        var _this = this;\n        var type = element.getAttribute('type');\n        var ele = 'radio' === type || 'checkbox' === type ? elements[0] : element;\n        elements.forEach(function (ele) {\n            var _a;\n            classSet(ele, (_a = {},\n                _a[_this.opts.eleValidClass] = false,\n                _a[_this.opts.eleInvalidClass] = false,\n                _a));\n        });\n        var groupEle = this.containers.get(ele);\n        if (groupEle) {\n            classSet(groupEle, (_a = {},\n                _a[this.opts.rowInvalidClass] = false,\n                _a[this.opts.rowValidatingClass] = false,\n                _a[this.opts.rowValidClass] = false,\n                _a));\n        }\n    };\n    Framework.prototype.onElementValidated = function (e) {\n        var _a, _b;\n        var _this = this;\n        var elements = e.elements;\n        var type = e.element.getAttribute('type');\n        var element = 'radio' === type || 'checkbox' === type ? elements[0] : e.element;\n        // Set the valid or invalid class for all elements\n        elements.forEach(function (ele) {\n            var _a;\n            classSet(ele, (_a = {},\n                _a[_this.opts.eleValidClass] = e.valid,\n                _a[_this.opts.eleInvalidClass] = !e.valid,\n                _a));\n        });\n        var groupEle = this.containers.get(element);\n        if (groupEle) {\n            if (!e.valid) {\n                this.results.set(element, false);\n                classSet(groupEle, (_a = {},\n                    _a[this.opts.rowInvalidClass] = true,\n                    _a[this.opts.rowValidatingClass] = false,\n                    _a[this.opts.rowValidClass] = false,\n                    _a));\n            }\n            else {\n                this.results.delete(element);\n                // Maybe there're multiple fields belong to the same row\n                var isValid_1 = true;\n                this.containers.forEach(function (value, key) {\n                    if (value === groupEle && _this.results.get(key) === false) {\n                        isValid_1 = false;\n                    }\n                });\n                // If all field(s) belonging to the row are valid\n                if (isValid_1) {\n                    classSet(groupEle, (_b = {},\n                        _b[this.opts.rowInvalidClass] = false,\n                        _b[this.opts.rowValidatingClass] = false,\n                        _b[this.opts.rowValidClass] = true,\n                        _b));\n                }\n            }\n        }\n    };\n    Framework.MESSAGE_PLUGIN = '___frameworkMessage';\n    return Framework;\n}(core.Plugin));\n\nexports.Framework = Framework;\n\n\n//# <AUTHOR> <EMAIL>\n */\n\n\n\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/index.js */ "./node_modules/@form-validation/plugin-framework/lib/cjs/index.js");\n}\n\n\n//# <AUTHOR> <EMAIL>\n */\nvar classSet = core.utils.classSet;\nvar Message = /** @class */ (function (_super) {\n    __extends(Message, _super);\n    function Message(opts) {\n        var _this = _super.call(this, opts) || this;\n        _this.useDefaultContainer = false;\n        // Map the field element to message container\n        _this.messages = new Map();\n        // By default, we will display error messages at the bottom of form\n        _this.defaultContainer = document.createElement('div');\n        _this.useDefaultContainer = !opts || !opts.container;\n        _this.opts = Object.assign({}, {\n            container: function (_field, _element) { return _this.defaultContainer; },\n        }, opts);\n        _this.elementIgnoredHandler = _this.onElementIgnored.bind(_this);\n        _this.fieldAddedHandler = _this.onFieldAdded.bind(_this);\n        _this.fieldRemovedHandler = _this.onFieldRemoved.bind(_this);\n        _this.validatorValidatedHandler = _this.onValidatorValidated.bind(_this);\n        _this.validatorNotValidatedHandler = _this.onValidatorNotValidated.bind(_this);\n        return _this;\n    }\n    /**\n     * Determine the closest element that its class matches with given pattern.\n     * In popular cases, all the fields might follow the same markup, so that closest element\n     * can be used as message container.\n     *\n     * For example, if we use the Bootstrap framework then the field often be placed inside a\n     * `col-{size}-{numberOfColumns}` class, we can register the plugin as following:\n     * ```\n     *  formValidation(form, {\n     *      plugins: {\n     *          message: new Message({\n     *              container: function(field, element) {\n     *                  return Message.getClosestContainer(element, form, /^(.*)(col|offset)-(xs|sm|md|lg)-[0-9]+(.*)$/)\n     *              }\n     *          })\n     *      }\n     *  })\n     * ```\n     *\n     * @param element The field element\n     * @param upper The upper element, so we don't have to look for the entire page\n     * @param pattern The pattern\n     * @return {HTMLElement}\n     */\n    Message.getClosestContainer = function (element, upper, pattern) {\n        var ele = element;\n        while (ele) {\n            if (ele === upper) {\n                break;\n            }\n            ele = ele.parentElement;\n            if (pattern.test(ele.className)) {\n                break;\n            }\n        }\n        return ele;\n    };\n    Message.prototype.install = function () {\n        if (this.useDefaultContainer) {\n            this.core.getFormElement().appendChild(this.defaultContainer);\n        }\n        this.core\n            .on('core.element.ignored', this.elementIgnoredHandler)\n            .on('core.field.added', this.fieldAddedHandler)\n            .on('core.field.removed', this.fieldRemovedHandler)\n            .on('core.validator.validated', this.validatorValidatedHandler)\n            .on('core.validator.notvalidated', this.validatorNotValidatedHandler);\n    };\n    Message.prototype.uninstall = function () {\n        if (this.useDefaultContainer) {\n            this.core.getFormElement().removeChild(this.defaultContainer);\n        }\n        this.messages.forEach(function (message) { return message.parentNode.removeChild(message); });\n        this.messages.clear();\n        this.core\n            .off('core.element.ignored', this.elementIgnoredHandler)\n            .off('core.field.added', this.fieldAddedHandler)\n            .off('core.field.removed', this.fieldRemovedHandler)\n            .off('core.validator.validated', this.validatorValidatedHandler)\n            .off('core.validator.notvalidated', this.validatorNotValidatedHandler);\n    };\n    Message.prototype.onEnabled = function () {\n        this.messages.forEach(function (_element, message, _map) {\n            classSet(message, {\n                'fv-plugins-message-container--enabled': true,\n                'fv-plugins-message-container--disabled': false,\n            });\n        });\n    };\n    Message.prototype.onDisabled = function () {\n        this.messages.forEach(function (_element, message, _map) {\n            classSet(message, {\n                'fv-plugins-message-container--enabled': false,\n                'fv-plugins-message-container--disabled': true,\n            });\n        });\n    };\n    // Prepare message container for new added field\n    Message.prototype.onFieldAdded = function (e) {\n        var _this = this;\n        var elements = e.elements;\n        if (elements) {\n            elements.forEach(function (ele) {\n                var msg = _this.messages.get(ele);\n                if (msg) {\n                    msg.parentNode.removeChild(msg);\n                    _this.messages.delete(ele);\n                }\n            });\n            this.prepareFieldContainer(e.field, elements);\n        }\n    };\n    // When a field is removed, we remove all error messages that associates with the field\n    Message.prototype.onFieldRemoved = function (e) {\n        var _this = this;\n        if (!e.elements.length || !e.field) {\n            return;\n        }\n        var type = e.elements[0].getAttribute('type');\n        var elements = 'radio' === type || 'checkbox' === type ? [e.elements[0]] : e.elements;\n        elements.forEach(function (ele) {\n            if (_this.messages.has(ele)) {\n                var container = _this.messages.get(ele);\n                container.parentNode.removeChild(container);\n                _this.messages.delete(ele);\n            }\n        });\n    };\n    Message.prototype.prepareFieldContainer = function (field, elements) {\n        var _this = this;\n        if (elements.length) {\n            var type = elements[0].getAttribute('type');\n            if ('radio' === type || 'checkbox' === type) {\n                this.prepareElementContainer(field, elements[0], elements);\n            }\n            else {\n                elements.forEach(function (ele) { return _this.prepareElementContainer(field, ele, elements); });\n            }\n        }\n    };\n    Message.prototype.prepareElementContainer = function (field, element, elements) {\n        var container;\n        if ('string' === typeof this.opts.container) {\n            var selector = '#' === this.opts.container.charAt(0)\n                ? \"[id=\\\"\".concat(this.opts.container.substring(1), \"\\\"]\")\n                : this.opts.container;\n            container = this.core.getFormElement().querySelector(selector);\n        }\n        else {\n            container = this.opts.container(field, element);\n        }\n        var message = document.createElement('div');\n        container.appendChild(message);\n        classSet(message, {\n            'fv-plugins-message-container': true,\n            'fv-plugins-message-container--enabled': this.isEnabled,\n            'fv-plugins-message-container--disabled': !this.isEnabled,\n        });\n        this.core.emit('plugins.message.placed', {\n            element: element,\n            elements: elements,\n            field: field,\n            messageElement: message,\n        });\n        this.messages.set(element, message);\n    };\n    Message.prototype.getMessage = function (result) {\n        return typeof result.message === 'string' ? result.message : result.message[this.core.getLocale()];\n    };\n    Message.prototype.onValidatorValidated = function (e) {\n        var _a;\n        var elements = e.elements;\n        var type = e.element.getAttribute('type');\n        var element = ('radio' === type || 'checkbox' === type) && elements.length > 0 ? elements[0] : e.element;\n        if (this.messages.has(element)) {\n            var container = this.messages.get(element);\n            var messageEle = container.querySelector(\"[data-field=\\\"\".concat(e.field.replace(/\"/g, '\\\\\"'), \"\\\"][data-validator=\\\"\").concat(e.validator.replace(/\"/g, '\\\\\"'), \"\\\"]\"));\n            if (!messageEle && !e.result.valid) {\n                var ele = document.createElement('div');\n                ele.innerHTML = this.getMessage(e.result);\n                ele.setAttribute('data-field', e.field);\n                ele.setAttribute('data-validator', e.validator);\n                if (this.opts.clazz) {\n                    classSet(ele, (_a = {},\n                        _a[this.opts.clazz] = true,\n                        _a));\n                }\n                container.appendChild(ele);\n                this.core.emit('plugins.message.displayed', {\n                    element: e.element,\n                    field: e.field,\n                    message: e.result.message,\n                    messageElement: ele,\n                    meta: e.result.meta,\n                    validator: e.validator,\n                });\n            }\n            else if (messageEle && !e.result.valid) {\n                // The validator returns new message\n                messageEle.innerHTML = this.getMessage(e.result);\n                this.core.emit('plugins.message.displayed', {\n                    element: e.element,\n                    field: e.field,\n                    message: e.result.message,\n                    messageElement: messageEle,\n                    meta: e.result.meta,\n                    validator: e.validator,\n                });\n            }\n            else if (messageEle && e.result.valid) {\n                // Field is valid\n                container.removeChild(messageEle);\n            }\n        }\n    };\n    Message.prototype.onValidatorNotValidated = function (e) {\n        var elements = e.elements;\n        var type = e.element.getAttribute('type');\n        var element = 'radio' === type || 'checkbox' === type ? elements[0] : e.element;\n        if (this.messages.has(element)) {\n            var container = this.messages.get(element);\n            var messageEle = container.querySelector(\"[data-field=\\\"\".concat(e.field.replace(/\"/g, '\\\\\"'), \"\\\"][data-validator=\\\"\").concat(e.validator.replace(/\"/g, '\\\\\"'), \"\\\"]\"));\n            if (messageEle) {\n                container.removeChild(messageEle);\n            }\n        }\n    };\n    Message.prototype.onElementIgnored = function (e) {\n        var elements = e.elements;\n        var type = e.element.getAttribute('type');\n        var element = 'radio' === type || 'checkbox' === type ? elements[0] : e.element;\n        if (this.messages.has(element)) {\n            var container_1 = this.messages.get(element);\n            var messageElements = [].slice.call(container_1.querySelectorAll(\"[data-field=\\\"\".concat(e.field.replace(/\"/g, '\\\\\"'), \"\\\"]\")));\n            messageElements.forEach(function (messageEle) {\n                container_1.removeChild(messageEle);\n            });\n        }\n    };\n    return Message;\n}(core.Plugin));\n\nexports.Message = Message;\n\n\n//# <AUTHOR> <EMAIL>\n */\n\n\n\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/index.js */ "./node_modules/@form-validation/plugin-message/lib/cjs/index.js");\n}\n\n\n//# sourceURL=webpack://Materialize/./node_modules/@form-validation/plugin-message/lib/index.js?')},"./libs/@form-validation/bootstrap5.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bootstrap5: function() { return /* reexport safe */ _form_validation_plugin_bootstrap5__WEBPACK_IMPORTED_MODULE_0__.Bootstrap5; }\n/* harmony export */ });\n/* harmony import */ var _form_validation_plugin_bootstrap5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @form-validation/plugin-bootstrap5 */ "./node_modules/@form-validation/plugin-bootstrap5/lib/index.js");\n\ntry {\n  FormValidation.plugins.Bootstrap5 = _form_validation_plugin_bootstrap5__WEBPACK_IMPORTED_MODULE_0__.Bootstrap5;\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/@form-validation/bootstrap5.js?')}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/@form-validation/bootstrap5.js");return __webpack_exports__}()}));