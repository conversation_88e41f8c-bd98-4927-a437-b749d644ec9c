{
  "editor.wordWrap": "off",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
  },
  "files.trimFinalNewlines": true,
  "diffEditor.ignoreTrimWhitespace": true,
  "search.exclude": {
    "**/node_modules": true,
    "*.min.js": true,
    "*.min.css": true
  },
  // JS
  "javascript.updateImportsOnFileMove.enabled": "always",
  // JSON
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  // Extension: Prettier
  "prettier.requireConfig": true,
  "prettier.configPath": ".prettierrc.json",
  "prettier.ignorePath": ".prettierignore",
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // Extension: Stylelint
  "stylelint.packageManager": "pnpm",
  "stylelint.validate": [
    "scss"
  ],
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // Extension: Git
  "git.rebaseWhenSync": true,
  "git.enableSmartCommit": true,
  // Extension: ESLint
  "eslint.packageManager": "yarn",
  "eslint.format.enable": true,
  // "eslint.workingDirectories": [
  //   "src",
  //   "dev"
  // ],
  "eslint.options": {
    "overrideConfigFile": ".eslintrc.json"
  },
  "eslint.validate": [
    "vue",
    "html",
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact"
  ],
  // Extension: npm
  "npm.packageManager": "yarn",
}