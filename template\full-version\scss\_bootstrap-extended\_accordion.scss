// Accordions
// *******************************************************************************
.accordion {
  --#{$prefix}accordion-box-shadow: var(--#{$prefix}box-shadow-xs);
  --#{$prefix}accordion-active-box-shadow: var(--#{$prefix}box-shadow);
  --#{$prefix}accordion-active-bg: var(--#{$prefix}accordion-bg);
  --#{$prefix}accordion-btn-active-bg: var(--#{$prefix}accordion-active-bg);
  --#{$prefix}accordion-btn-focus-box-shadow: none;
  --#{$prefix}accordion-btn-focus-shadow-width: 0;

  .accordion-button {
    &::after {
      background: var(--#{$prefix}accordion-btn-color);
      mask-image: var(--#{$prefix}accordion-btn-icon);
      mask-repeat: no-repeat;
      mask-size: 100% 100%;
    }
    &:not(.collapsed) {
      &::after {
        mask-image: var(--#{$prefix}accordion-btn-active-icon);
      }
    }
  }

  &.accordion-without-arrow {
    .accordion-button::after {
      background: none;
    }
  }
  &:not(.accordion-custom-button):not(.accordion-arrow-left) {
    .accordion-item {
      &:not(:first-of-type) {
        border-block-start: var(--#{$prefix}accordion-border-width) solid var(--#{$prefix}paper-bg);
      }
      &.previous-active:not(:first-of-type, .active, .active + .accordion-item.previous-active) {
        border-block-start-color: var(--#{$prefix}border-color);
      }
      &.active + .accordion-item {
        @include border-top-radius(var(--#{$prefix}accordion-border-radius));
        &:not(:last-of-type, .active, .previous-active) {
          border-block-end-color: var(--#{$prefix}border-color);
        }
      }
      &.active ~ .accordion-item {
        &:not(:last-of-type) {
          border-block-end-color: var(--#{$prefix}border-color);
        }
      }
      &:has(~ .accordion-item.previous-active):not(:first-of-type) {
        border-block-start-color: var(--#{$prefix}border-color);
      }
      @include transition($accordion-transition);
      > .accordion-header .accordion-button {
        @include border-radius(var(--#{$prefix}accordion-inner-border-radius));
      }
      &.active,
      &.previous-active {
        margin-block-end: $spacer * .5;
      }
      &.active {
        @include border-radius(var(--#{$prefix}accordion-border-radius));
      }
      &.previous-active {
        @include border-bottom-radius(var(--#{$prefix}accordion-border-radius));
      }
    }
    &:not(:has(.accordion-item.active)) .accordion-item:not(:first-of-type) {
      border-block-start-color: var(--#{$prefix}border-color);
    }
  }
}

.accordion-item {
  box-shadow: var(--#{$prefix}accordion-box-shadow);
  &.active {
    background-color: var(--#{$prefix}accordion-active-bg);
    box-shadow: var(--#{$prefix}accordion-active-box-shadow);
  }
}

.accordion-header {
  line-height: $line-height-base;
  & + .accordion-collapse .accordion-body {
    padding-block-start: 0;
  }
}

// Accordion Popout Variant
.accordion-popout {
  .accordion-item {
    @include transition(margin .35s cubic-bezier(.25, .46, .45, .94));
    &:not(.active) {
      margin-inline: 1rem;
    }
  }
}

/* Accordion border radius */
.accordion-button {
  font-weight: inherit;
  &::after{
    margin-inline-end: initial;
    margin-inline-start: auto;
  }
  &:not(.collapsed) {
    background-color: var(--#{$prefix}accordion-btn-active-bg);
    box-shadow: inset 0 calc(-1 * var(--#{$prefix}accordion-btn-focus-shadow-width)) 0 var(--#{$prefix}accordion-border-color);
  }
}

/* arrow left */

.accordion-arrow-left {
  --#{$prefix}accordion-box-shadow: none;
  --#{$prefix}accordion-active-box-shadow: none;
  --#{$prefix}accordion-btn-padding-y: calc(#{$accordion-padding-y} + .0625rem);
  --#{$prefix}accordion-btn-padding-x: 0;
  --#{$prefix}accordion-body-padding-y: calc(#{$accordion-body-padding-y} + .0625rem);
  .accordion-item {
    &:not(:first-of-type) {
      border-block-start: $accordion-border-width solid var(--#{$prefix}accordion-border-color);
    }
  }

  .accordion-button {
    &::after {
      display: none;
    }
    &:not(.collapsed) {
      &::before {
        background: var(--#{$prefix}accordion-active-color);
        mask-image: var(--#{$prefix}accordion-btn-active-icon);
        transform: var(--#{$prefix}accordion-btn-icon-transform);
      }
    }
    &::before {
      background: var(--#{$prefix}accordion-btn-color);
      block-size: var(--#{$prefix}accordion-btn-icon-width);
      content: "";
      inline-size: var(--#{$prefix}accordion-btn-icon-width);
      margin-inline: 0 1.1rem;
      mask-image: var(--#{$prefix}accordion-btn-icon);
      mask-repeat: no-repeat;
      mask-size: 100% 100%;
      @include transition(var(--#{$prefix}accordion-btn-icon-transition));
    }
  }
  .accordion-header + .accordion-collapse .accordion-body {
    padding-inline-start: calc(#{$accordion-padding-x} + .0625rem);
  }
}

/* Accordion custom button */

.accordion-custom-button {
  --#{$prefix}accordion-box-shadow: none;
  --#{$prefix}accordion-active-box-shadow: none;
  --#{$prefix}accordion-btn-padding-y: .76178rem;
  --#{$prefix}accordion-border-width: #{$accordion-border-width};
  --#{$prefix}accordion-border-color: #{$border-color};
  --#{$prefix}accordion-btn-bg: #fafafa;
  --#{$prefix}accordion-btn-active-bg: #fafafa;
  --#{$prefix}accordion-btn-focus-box-shadow: #{$accordion-button-focus-box-shadow};
  --#{$prefix}accordion-btn-focus-shadow-width: #{$accordion-border-width};
  --#{$prefix}accordion-btn-icon: #{escape-svg($accordion-custom-button-icon)};
  --#{$prefix}accordion-btn-active-icon: #{escape-svg($accordion-custom-button-active-icon)};
  .accordion-item {
    .accordion-body {
      padding-block-start: $accordion-body-padding-y;
    }
    &.active {
      margin: 0;
      box-shadow: none;
      & .accordion-header {
        border-block-end: $accordion-border-width solid $accordion-border-color;
      }
      & + .accordion-item {
        @include border-top-radius(0);
      }
      &:not(:first-child) {
        @include border-top-radius(0);
      }
      &:not(:last-child) {
        @include border-bottom-radius(0);
      }
    }
  }
}

/* Generate contextual modifier classes for colorizing the alert */
@each $state in map-keys($theme-colors) {
  .accordion-header-#{$state} {
    --#{$prefix}accordion-active-color: var(--#{$prefix}#{$state});
  }
}

/* Dark Theme */

@if $enable-dark-mode {
  @include color-mode(dark) {
    .accordion {
      &:not([class*="accordion-border-background-"], [class*="accordion-border-solid-"], [class*="accordion-solid-"]) {
        --#{$prefix}accordion-btn-color: #{$accordion-button-color-dark};
      }
      &:not([class*="accordion-header-"], [class*="accordion-border-background-"], [class*="accordion-border-solid-"], [class*="accordion-solid-"]) {
        --#{$prefix}accordion-active-color: #{$accordion-button-active-color-dark};
      }
      &.accordion-custom-button {
        --#{$prefix}accordion-border-color: #{$border-color-dark};
        --#{$prefix}accordion-btn-bg: #{$gray-25-dark};
        --#{$prefix}accordion-btn-active-bg: #{$gray-25-dark};
      }
    }
    .accordion-custom-button {
      .accordion-button::after {
        --#{$prefix}accordion-btn-icon: #{escape-svg($accordion-custom-button-icon-dark)};
        --#{$prefix}accordion-btn-active-icon: #{escape-svg($accordion-custom-button-active-icon-dark)};
      }
    }
  }
}
