.shepherd-button {
  background: #3288e6;
  border: 0;
  border-radius: 3px;
  color: hsla(0, 0%, 100%, 0.75);
  cursor: pointer;
  margin-right: 0.5rem;
  padding: 0.5rem 1.5rem;
  transition: all 0.5s ease;
}

.shepherd-button:not(:disabled):hover {
  background: #196fcc;
  color: hsla(0, 0%, 100%, 0.75);
}

.shepherd-button.shepherd-button-secondary {
  background: #f1f2f3;
  color: rgba(0, 0, 0, 0.75);
}

.shepherd-button.shepherd-button-secondary:not(:disabled):hover {
  background: #d6d9db;
  color: rgba(0, 0, 0, 0.75);
}

.shepherd-button:disabled {
  cursor: not-allowed;
}

.shepherd-footer {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  display: flex;
  justify-content: flex-end;
  padding: 0 0.75rem 0.75rem;
}

.shepherd-footer .shepherd-button:last-child {
  margin-right: 0;
}

.shepherd-cancel-icon {
  background: transparent;
  border: none;
  color: hsla(0, 0%, 50%, 0.75);
  cursor: pointer;
  font-size: 2em;
  font-weight: 400;
  margin: 0;
  padding: 0;
  transition: color 0.5s ease;
}

.shepherd-cancel-icon:hover {
  color: rgba(0, 0, 0, 0.75);
}

.shepherd-has-title .shepherd-content .shepherd-cancel-icon {
  color: hsla(0, 0%, 50%, 0.75);
}

.shepherd-has-title .shepherd-content .shepherd-cancel-icon:hover {
  color: rgba(0, 0, 0, 0.75);
}

.shepherd-title {
  color: rgba(0, 0, 0, 0.75);
  display: flex;
  flex: 1 0 auto;
  font-size: 1rem;
  font-weight: 400;
  margin: 0;
  padding: 0;
}

.shepherd-header {
  align-items: center;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  display: flex;
  justify-content: flex-end;
  line-height: 2em;
  padding: 0.75rem 0.75rem 0;
}

.shepherd-has-title .shepherd-content .shepherd-header {
  background: #e6e6e6;
  padding: 1em;
}

.shepherd-text {
  color: rgba(0, 0, 0, 0.75);
  font-size: 1rem;
  line-height: 1.3em;
  padding: 0.75em;
}

.shepherd-text p {
  margin-top: 0;
}

.shepherd-text p:last-child {
  margin-bottom: 0;
}

.shepherd-content {
  border-radius: 5px;
  outline: none;
  padding: 0;
}

.shepherd-element {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  margin: 0;
  max-width: 400px;
  opacity: 0;
  outline: none;
  transition: opacity 0.3s, visibility 0.3s;
  visibility: hidden;
  width: 100%;
  z-index: 9999;
}

.shepherd-enabled.shepherd-element {
  opacity: 1;
  visibility: visible;
}

.shepherd-element[data-popper-reference-hidden]:not(.shepherd-centered) {
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.shepherd-element, .shepherd-element *, .shepherd-element :after, .shepherd-element :before {
  box-sizing: border-box;
}

.shepherd-arrow, .shepherd-arrow:before {
  height: 16px;
  position: absolute;
  width: 16px;
  z-index: -1;
}

.shepherd-arrow:before {
  background: #fff;
  content: "";
  transform: rotate(45deg);
}

.shepherd-element[data-popper-placement^=top] > .shepherd-arrow {
  bottom: -8px;
}

.shepherd-element[data-popper-placement^=bottom] > .shepherd-arrow {
  top: -8px;
}

.shepherd-element[data-popper-placement^=left] > .shepherd-arrow {
  right: -8px;
}

.shepherd-element[data-popper-placement^=right] > .shepherd-arrow {
  left: -8px;
}

.shepherd-element.shepherd-centered > .shepherd-arrow {
  opacity: 0;
}

.shepherd-element.shepherd-has-title[data-popper-placement^=bottom] > .shepherd-arrow:before {
  background-color: #e6e6e6;
}

.shepherd-target-click-disabled.shepherd-enabled.shepherd-target, .shepherd-target-click-disabled.shepherd-enabled.shepherd-target * {
  pointer-events: none;
}

.shepherd-modal-overlay-container {
  height: 0;
  left: 0;
  opacity: 0;
  overflow: hidden;
  pointer-events: none;
  position: fixed;
  top: 0;
  transition: all 0.3s ease-out, height 0s 0.3s, opacity 0.3s 0s;
  width: 100vw;
  z-index: 9997;
}

.shepherd-modal-overlay-container.shepherd-modal-is-visible {
  height: 100vh;
  opacity: 0.5;
  transform: translateZ(0);
  transition: all 0.3s ease-out, height 0s 0s, opacity 0.3s 0s;
}

.shepherd-modal-overlay-container.shepherd-modal-is-visible path {
  pointer-events: all;
}

.shepherd-element {
  padding: 0;
  border-width: 0;
  background: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow-lg);
  inset-inline-start: auto;
  border-radius: var(--bs-border-radius-xl);
  /* Ask before submit */
}
@media (max-width: 575.98px) {
  .shepherd-element {
    max-inline-size: 300px;
  }
}
.shepherd-element .shepherd-arrow::before {
  border-color: var(--bs-paper-bg) !important;
  background: var(--bs-paper-bg);
  border-block-end: 1px solid;
  border-inline-end: 1px solid;
}
.shepherd-element .shepherd-title,
.shepherd-element .shepherd-cancel-icon {
  color: var(--bs-heading-color);
}
.shepherd-element .shepherd-content {
  min-inline-size: 15rem;
}
.shepherd-element .shepherd-content .shepherd-header {
  background-color: var(--bs-paper-bg);
  border-radius: var(--bs-border-radius);
  padding-block: 1.25rem 0;
  padding-inline: 1.25rem;
}
.shepherd-element .shepherd-content .shepherd-header .shepherd-title {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.75rem;
}
.shepherd-element .shepherd-content .shepherd-header .shepherd-cancel-icon {
  color: var(--bs-secondary-color);
  font-size: 1.5rem;
}
.shepherd-element .shepherd-content .shepherd-text {
  color: var(--bs-body-color);
  font-size: 0.9375rem;
  padding-block: 1rem !important;
  padding-inline: 1.25rem !important;
}
.shepherd-element .shepherd-content .shepherd-footer {
  padding-block: 0 0.75rem;
  padding-inline: 0.75rem;
}
.shepherd-element .shepherd-content .shepherd-footer .shepherd-button {
  background: var(--bs-btn-bg);
  border-radius: var(--bs-border-radius);
  color: var(--bs-btn-color);
  padding-block: 0.4415rem;
  padding-inline: 0.969rem;
}
.shepherd-element .shepherd-content .shepherd-footer .shepherd-button:hover {
  background: var(--bs-btn-hover-bg);
  color: var(--bs-btn-hover-color);
}
.shepherd-element .shepherd-content .shepherd-footer .shepherd-button:not(:last-child) {
  margin-inline: 0 0.75rem;
}
.shepherd-element .shepherd-content .shepherd-footer .shepherd-button.btn-outline-secondary {
  border-radius: var(--bs-border-radius);
  border: 1px solid var(--bs-secondary);
}
.shepherd-element[data-popper-placement=bottom] {
  margin-block-start: 0.8rem !important;
}
.shepherd-element[data-popper-placement=bottom] .shepherd-arrow::before {
  border-color: var(--bs-paper-bg) !important;
  background-color: var(--bs-paper-bg) !important;
}
.shepherd-element[data-popper-placement=top] {
  margin-block-start: -0.8rem;
}
.shepherd-element[data-popper-placement=left] {
  margin-inline-start: -0.8rem;
}
.shepherd-element[data-popper-placement=left] .shepherd-arrow::before {
  border-block-end: 0;
  border-block-start: 1px solid;
}
.shepherd-element[data-popper-placement=right] {
  margin-inline-start: 0.8rem;
}
.shepherd-element[data-popper-placement=right] .shepherd-arrow::before {
  border-inline-end: 0;
  border-inline-start: 1px solid;
}

/* RTL */
:dir(rtl) .shepherd-element[data-popper-placement=left] {
  margin-inline-end: -0.8rem;
}
:dir(rtl) .shepherd-element[data-popper-placement=right] {
  margin-inline-end: 0.8rem;
}
