!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("jQuery"));else if("function"==typeof define&&define.amd)define(["jQuery"],e);else{var t="object"==typeof exports?e(require("jQuery")):e(n.jQuery);for(var i in t)("object"==typeof exports?exports:n)[i]=t[i]}}(self,(function(__WEBPACK_EXTERNAL_MODULE_jquery__){return function(){var __webpack_modules__={"./libs/typeahead-js/typeahead.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var typeahead_js_dist_typeahead_bundle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! typeahead.js/dist/typeahead.bundle */ "./node_modules/typeahead.js/dist/typeahead.bundle.js");\n/* harmony import */ var typeahead_js_dist_typeahead_bundle__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(typeahead_js_dist_typeahead_bundle__WEBPACK_IMPORTED_MODULE_0__);\n\n\n//# sourceURL=webpack://Materialize/./libs/typeahead-js/typeahead.js?')},"./node_modules/typeahead.js/dist/typeahead.bundle.js":function(module,exports,__webpack_require__){eval('var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*!\n * typeahead.js 0.11.1\n * https://github.com/twitter/typeahead.js\n * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT\n */\n\n(function(root, factory) {\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [ __webpack_require__(/*! jquery */ "jquery") ], __WEBPACK_AMD_DEFINE_RESULT__ = (function(a0) {\n            return root["Bloodhound"] = factory(a0);\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n})(this, function($) {\n    var _ = function() {\n        "use strict";\n        return {\n            isMsie: function() {\n                return /(msie|trident)/i.test(navigator.userAgent) ? navigator.userAgent.match(/(msie |rv:)(\\d+(.\\d+)?)/i)[2] : false;\n            },\n            isBlankString: function(str) {\n                return !str || /^\\s*$/.test(str);\n            },\n            escapeRegExChars: function(str) {\n                return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, "\\\\$&");\n            },\n            isString: function(obj) {\n                return typeof obj === "string";\n            },\n            isNumber: function(obj) {\n                return typeof obj === "number";\n            },\n            isArray: $.isArray,\n            isFunction: $.isFunction,\n            isObject: $.isPlainObject,\n            isUndefined: function(obj) {\n                return typeof obj === "undefined";\n            },\n            isElement: function(obj) {\n                return !!(obj && obj.nodeType === 1);\n            },\n            isJQuery: function(obj) {\n                return obj instanceof $;\n            },\n            toStr: function toStr(s) {\n                return _.isUndefined(s) || s === null ? "" : s + "";\n            },\n            bind: $.proxy,\n            each: function(collection, cb) {\n                $.each(collection, reverseArgs);\n                function reverseArgs(index, value) {\n                    return cb(value, index);\n                }\n            },\n            map: $.map,\n            filter: $.grep,\n            every: function(obj, test) {\n                var result = true;\n                if (!obj) {\n                    return result;\n                }\n                $.each(obj, function(key, val) {\n                    if (!(result = test.call(null, val, key, obj))) {\n                        return false;\n                    }\n                });\n                return !!result;\n            },\n            some: function(obj, test) {\n                var result = false;\n                if (!obj) {\n                    return result;\n                }\n                $.each(obj, function(key, val) {\n                    if (result = test.call(null, val, key, obj)) {\n                        return false;\n                    }\n                });\n                return !!result;\n            },\n            mixin: $.extend,\n            identity: function(x) {\n                return x;\n            },\n            clone: function(obj) {\n                return $.extend(true, {}, obj);\n            },\n            getIdGenerator: function() {\n                var counter = 0;\n                return function() {\n                    return counter++;\n                };\n            },\n            templatify: function templatify(obj) {\n                return $.isFunction(obj) ? obj : template;\n                function template() {\n                    return String(obj);\n                }\n            },\n            defer: function(fn) {\n                setTimeout(fn, 0);\n            },\n            debounce: function(func, wait, immediate) {\n                var timeout, result;\n                return function() {\n                    var context = this, args = arguments, later, callNow;\n                    later = function() {\n                        timeout = null;\n                        if (!immediate) {\n                            result = func.apply(context, args);\n                        }\n                    };\n                    callNow = immediate && !timeout;\n                    clearTimeout(timeout);\n                    timeout = setTimeout(later, wait);\n                    if (callNow) {\n                        result = func.apply(context, args);\n                    }\n                    return result;\n                };\n            },\n            throttle: function(func, wait) {\n                var context, args, timeout, result, previous, later;\n                previous = 0;\n                later = function() {\n                    previous = new Date();\n                    timeout = null;\n                    result = func.apply(context, args);\n                };\n                return function() {\n                    var now = new Date(), remaining = wait - (now - previous);\n                    context = this;\n                    args = arguments;\n                    if (remaining <= 0) {\n                        clearTimeout(timeout);\n                        timeout = null;\n                        previous = now;\n                        result = func.apply(context, args);\n                    } else if (!timeout) {\n                        timeout = setTimeout(later, remaining);\n                    }\n                    return result;\n                };\n            },\n            stringify: function(val) {\n                return _.isString(val) ? val : JSON.stringify(val);\n            },\n            noop: function() {}\n        };\n    }();\n    var VERSION = "0.11.1";\n    var tokenizers = function() {\n        "use strict";\n        return {\n            nonword: nonword,\n            whitespace: whitespace,\n            obj: {\n                nonword: getObjTokenizer(nonword),\n                whitespace: getObjTokenizer(whitespace)\n            }\n        };\n        function whitespace(str) {\n            str = _.toStr(str);\n            return str ? str.split(/\\s+/) : [];\n        }\n        function nonword(str) {\n            str = _.toStr(str);\n            return str ? str.split(/\\W+/) : [];\n        }\n        function getObjTokenizer(tokenizer) {\n            return function setKey(keys) {\n                keys = _.isArray(keys) ? keys : [].slice.call(arguments, 0);\n                return function tokenize(o) {\n                    var tokens = [];\n                    _.each(keys, function(k) {\n                        tokens = tokens.concat(tokenizer(_.toStr(o[k])));\n                    });\n                    return tokens;\n                };\n            };\n        }\n    }();\n    var LruCache = function() {\n        "use strict";\n        function LruCache(maxSize) {\n            this.maxSize = _.isNumber(maxSize) ? maxSize : 100;\n            this.reset();\n            if (this.maxSize <= 0) {\n                this.set = this.get = $.noop;\n            }\n        }\n        _.mixin(LruCache.prototype, {\n            set: function set(key, val) {\n                var tailItem = this.list.tail, node;\n                if (this.size >= this.maxSize) {\n                    this.list.remove(tailItem);\n                    delete this.hash[tailItem.key];\n                    this.size--;\n                }\n                if (node = this.hash[key]) {\n                    node.val = val;\n                    this.list.moveToFront(node);\n                } else {\n                    node = new Node(key, val);\n                    this.list.add(node);\n                    this.hash[key] = node;\n                    this.size++;\n                }\n            },\n            get: function get(key) {\n                var node = this.hash[key];\n                if (node) {\n                    this.list.moveToFront(node);\n                    return node.val;\n                }\n            },\n            reset: function reset() {\n                this.size = 0;\n                this.hash = {};\n                this.list = new List();\n            }\n        });\n        function List() {\n            this.head = this.tail = null;\n        }\n        _.mixin(List.prototype, {\n            add: function add(node) {\n                if (this.head) {\n                    node.next = this.head;\n                    this.head.prev = node;\n                }\n                this.head = node;\n                this.tail = this.tail || node;\n            },\n            remove: function remove(node) {\n                node.prev ? node.prev.next = node.next : this.head = node.next;\n                node.next ? node.next.prev = node.prev : this.tail = node.prev;\n            },\n            moveToFront: function(node) {\n                this.remove(node);\n                this.add(node);\n            }\n        });\n        function Node(key, val) {\n            this.key = key;\n            this.val = val;\n            this.prev = this.next = null;\n        }\n        return LruCache;\n    }();\n    var PersistentStorage = function() {\n        "use strict";\n        var LOCAL_STORAGE;\n        try {\n            LOCAL_STORAGE = window.localStorage;\n            LOCAL_STORAGE.setItem("~~~", "!");\n            LOCAL_STORAGE.removeItem("~~~");\n        } catch (err) {\n            LOCAL_STORAGE = null;\n        }\n        function PersistentStorage(namespace, override) {\n            this.prefix = [ "__", namespace, "__" ].join("");\n            this.ttlKey = "__ttl__";\n            this.keyMatcher = new RegExp("^" + _.escapeRegExChars(this.prefix));\n            this.ls = override || LOCAL_STORAGE;\n            !this.ls && this._noop();\n        }\n        _.mixin(PersistentStorage.prototype, {\n            _prefix: function(key) {\n                return this.prefix + key;\n            },\n            _ttlKey: function(key) {\n                return this._prefix(key) + this.ttlKey;\n            },\n            _noop: function() {\n                this.get = this.set = this.remove = this.clear = this.isExpired = _.noop;\n            },\n            _safeSet: function(key, val) {\n                try {\n                    this.ls.setItem(key, val);\n                } catch (err) {\n                    if (err.name === "QuotaExceededError") {\n                        this.clear();\n                        this._noop();\n                    }\n                }\n            },\n            get: function(key) {\n                if (this.isExpired(key)) {\n                    this.remove(key);\n                }\n                return decode(this.ls.getItem(this._prefix(key)));\n            },\n            set: function(key, val, ttl) {\n                if (_.isNumber(ttl)) {\n                    this._safeSet(this._ttlKey(key), encode(now() + ttl));\n                } else {\n                    this.ls.removeItem(this._ttlKey(key));\n                }\n                return this._safeSet(this._prefix(key), encode(val));\n            },\n            remove: function(key) {\n                this.ls.removeItem(this._ttlKey(key));\n                this.ls.removeItem(this._prefix(key));\n                return this;\n            },\n            clear: function() {\n                var i, keys = gatherMatchingKeys(this.keyMatcher);\n                for (i = keys.length; i--; ) {\n                    this.remove(keys[i]);\n                }\n                return this;\n            },\n            isExpired: function(key) {\n                var ttl = decode(this.ls.getItem(this._ttlKey(key)));\n                return _.isNumber(ttl) && now() > ttl ? true : false;\n            }\n        });\n        return PersistentStorage;\n        function now() {\n            return new Date().getTime();\n        }\n        function encode(val) {\n            return JSON.stringify(_.isUndefined(val) ? null : val);\n        }\n        function decode(val) {\n            return $.parseJSON(val);\n        }\n        function gatherMatchingKeys(keyMatcher) {\n            var i, key, keys = [], len = LOCAL_STORAGE.length;\n            for (i = 0; i < len; i++) {\n                if ((key = LOCAL_STORAGE.key(i)).match(keyMatcher)) {\n                    keys.push(key.replace(keyMatcher, ""));\n                }\n            }\n            return keys;\n        }\n    }();\n    var Transport = function() {\n        "use strict";\n        var pendingRequestsCount = 0, pendingRequests = {}, maxPendingRequests = 6, sharedCache = new LruCache(10);\n        function Transport(o) {\n            o = o || {};\n            this.cancelled = false;\n            this.lastReq = null;\n            this._send = o.transport;\n            this._get = o.limiter ? o.limiter(this._get) : this._get;\n            this._cache = o.cache === false ? new LruCache(0) : sharedCache;\n        }\n        Transport.setMaxPendingRequests = function setMaxPendingRequests(num) {\n            maxPendingRequests = num;\n        };\n        Transport.resetCache = function resetCache() {\n            sharedCache.reset();\n        };\n        _.mixin(Transport.prototype, {\n            _fingerprint: function fingerprint(o) {\n                o = o || {};\n                return o.url + o.type + $.param(o.data || {});\n            },\n            _get: function(o, cb) {\n                var that = this, fingerprint, jqXhr;\n                fingerprint = this._fingerprint(o);\n                if (this.cancelled || fingerprint !== this.lastReq) {\n                    return;\n                }\n                if (jqXhr = pendingRequests[fingerprint]) {\n                    jqXhr.done(done).fail(fail);\n                } else if (pendingRequestsCount < maxPendingRequests) {\n                    pendingRequestsCount++;\n                    pendingRequests[fingerprint] = this._send(o).done(done).fail(fail).always(always);\n                } else {\n                    this.onDeckRequestArgs = [].slice.call(arguments, 0);\n                }\n                function done(resp) {\n                    cb(null, resp);\n                    that._cache.set(fingerprint, resp);\n                }\n                function fail() {\n                    cb(true);\n                }\n                function always() {\n                    pendingRequestsCount--;\n                    delete pendingRequests[fingerprint];\n                    if (that.onDeckRequestArgs) {\n                        that._get.apply(that, that.onDeckRequestArgs);\n                        that.onDeckRequestArgs = null;\n                    }\n                }\n            },\n            get: function(o, cb) {\n                var resp, fingerprint;\n                cb = cb || $.noop;\n                o = _.isString(o) ? {\n                    url: o\n                } : o || {};\n                fingerprint = this._fingerprint(o);\n                this.cancelled = false;\n                this.lastReq = fingerprint;\n                if (resp = this._cache.get(fingerprint)) {\n                    cb(null, resp);\n                } else {\n                    this._get(o, cb);\n                }\n            },\n            cancel: function() {\n                this.cancelled = true;\n            }\n        });\n        return Transport;\n    }();\n    var SearchIndex = window.SearchIndex = function() {\n        "use strict";\n        var CHILDREN = "c", IDS = "i";\n        function SearchIndex(o) {\n            o = o || {};\n            if (!o.datumTokenizer || !o.queryTokenizer) {\n                $.error("datumTokenizer and queryTokenizer are both required");\n            }\n            this.identify = o.identify || _.stringify;\n            this.datumTokenizer = o.datumTokenizer;\n            this.queryTokenizer = o.queryTokenizer;\n            this.reset();\n        }\n        _.mixin(SearchIndex.prototype, {\n            bootstrap: function bootstrap(o) {\n                this.datums = o.datums;\n                this.trie = o.trie;\n            },\n            add: function(data) {\n                var that = this;\n                data = _.isArray(data) ? data : [ data ];\n                _.each(data, function(datum) {\n                    var id, tokens;\n                    that.datums[id = that.identify(datum)] = datum;\n                    tokens = normalizeTokens(that.datumTokenizer(datum));\n                    _.each(tokens, function(token) {\n                        var node, chars, ch;\n                        node = that.trie;\n                        chars = token.split("");\n                        while (ch = chars.shift()) {\n                            node = node[CHILDREN][ch] || (node[CHILDREN][ch] = newNode());\n                            node[IDS].push(id);\n                        }\n                    });\n                });\n            },\n            get: function get(ids) {\n                var that = this;\n                return _.map(ids, function(id) {\n                    return that.datums[id];\n                });\n            },\n            search: function search(query) {\n                var that = this, tokens, matches;\n                tokens = normalizeTokens(this.queryTokenizer(query));\n                _.each(tokens, function(token) {\n                    var node, chars, ch, ids;\n                    if (matches && matches.length === 0) {\n                        return false;\n                    }\n                    node = that.trie;\n                    chars = token.split("");\n                    while (node && (ch = chars.shift())) {\n                        node = node[CHILDREN][ch];\n                    }\n                    if (node && chars.length === 0) {\n                        ids = node[IDS].slice(0);\n                        matches = matches ? getIntersection(matches, ids) : ids;\n                    } else {\n                        matches = [];\n                        return false;\n                    }\n                });\n                return matches ? _.map(unique(matches), function(id) {\n                    return that.datums[id];\n                }) : [];\n            },\n            all: function all() {\n                var values = [];\n                for (var key in this.datums) {\n                    values.push(this.datums[key]);\n                }\n                return values;\n            },\n            reset: function reset() {\n                this.datums = {};\n                this.trie = newNode();\n            },\n            serialize: function serialize() {\n                return {\n                    datums: this.datums,\n                    trie: this.trie\n                };\n            }\n        });\n        return SearchIndex;\n        function normalizeTokens(tokens) {\n            tokens = _.filter(tokens, function(token) {\n                return !!token;\n            });\n            tokens = _.map(tokens, function(token) {\n                return token.toLowerCase();\n            });\n            return tokens;\n        }\n        function newNode() {\n            var node = {};\n            node[IDS] = [];\n            node[CHILDREN] = {};\n            return node;\n        }\n        function unique(array) {\n            var seen = {}, uniques = [];\n            for (var i = 0, len = array.length; i < len; i++) {\n                if (!seen[array[i]]) {\n                    seen[array[i]] = true;\n                    uniques.push(array[i]);\n                }\n            }\n            return uniques;\n        }\n        function getIntersection(arrayA, arrayB) {\n            var ai = 0, bi = 0, intersection = [];\n            arrayA = arrayA.sort();\n            arrayB = arrayB.sort();\n            var lenArrayA = arrayA.length, lenArrayB = arrayB.length;\n            while (ai < lenArrayA && bi < lenArrayB) {\n                if (arrayA[ai] < arrayB[bi]) {\n                    ai++;\n                } else if (arrayA[ai] > arrayB[bi]) {\n                    bi++;\n                } else {\n                    intersection.push(arrayA[ai]);\n                    ai++;\n                    bi++;\n                }\n            }\n            return intersection;\n        }\n    }();\n    var Prefetch = function() {\n        "use strict";\n        var keys;\n        keys = {\n            data: "data",\n            protocol: "protocol",\n            thumbprint: "thumbprint"\n        };\n        function Prefetch(o) {\n            this.url = o.url;\n            this.ttl = o.ttl;\n            this.cache = o.cache;\n            this.prepare = o.prepare;\n            this.transform = o.transform;\n            this.transport = o.transport;\n            this.thumbprint = o.thumbprint;\n            this.storage = new PersistentStorage(o.cacheKey);\n        }\n        _.mixin(Prefetch.prototype, {\n            _settings: function settings() {\n                return {\n                    url: this.url,\n                    type: "GET",\n                    dataType: "json"\n                };\n            },\n            store: function store(data) {\n                if (!this.cache) {\n                    return;\n                }\n                this.storage.set(keys.data, data, this.ttl);\n                this.storage.set(keys.protocol, location.protocol, this.ttl);\n                this.storage.set(keys.thumbprint, this.thumbprint, this.ttl);\n            },\n            fromCache: function fromCache() {\n                var stored = {}, isExpired;\n                if (!this.cache) {\n                    return null;\n                }\n                stored.data = this.storage.get(keys.data);\n                stored.protocol = this.storage.get(keys.protocol);\n                stored.thumbprint = this.storage.get(keys.thumbprint);\n                isExpired = stored.thumbprint !== this.thumbprint || stored.protocol !== location.protocol;\n                return stored.data && !isExpired ? stored.data : null;\n            },\n            fromNetwork: function(cb) {\n                var that = this, settings;\n                if (!cb) {\n                    return;\n                }\n                settings = this.prepare(this._settings());\n                this.transport(settings).fail(onError).done(onResponse);\n                function onError() {\n                    cb(true);\n                }\n                function onResponse(resp) {\n                    cb(null, that.transform(resp));\n                }\n            },\n            clear: function clear() {\n                this.storage.clear();\n                return this;\n            }\n        });\n        return Prefetch;\n    }();\n    var Remote = function() {\n        "use strict";\n        function Remote(o) {\n            this.url = o.url;\n            this.prepare = o.prepare;\n            this.transform = o.transform;\n            this.transport = new Transport({\n                cache: o.cache,\n                limiter: o.limiter,\n                transport: o.transport\n            });\n        }\n        _.mixin(Remote.prototype, {\n            _settings: function settings() {\n                return {\n                    url: this.url,\n                    type: "GET",\n                    dataType: "json"\n                };\n            },\n            get: function get(query, cb) {\n                var that = this, settings;\n                if (!cb) {\n                    return;\n                }\n                query = query || "";\n                settings = this.prepare(query, this._settings());\n                return this.transport.get(settings, onResponse);\n                function onResponse(err, resp) {\n                    err ? cb([]) : cb(that.transform(resp));\n                }\n            },\n            cancelLastRequest: function cancelLastRequest() {\n                this.transport.cancel();\n            }\n        });\n        return Remote;\n    }();\n    var oParser = function() {\n        "use strict";\n        return function parse(o) {\n            var defaults, sorter;\n            defaults = {\n                initialize: true,\n                identify: _.stringify,\n                datumTokenizer: null,\n                queryTokenizer: null,\n                sufficient: 5,\n                sorter: null,\n                local: [],\n                prefetch: null,\n                remote: null\n            };\n            o = _.mixin(defaults, o || {});\n            !o.datumTokenizer && $.error("datumTokenizer is required");\n            !o.queryTokenizer && $.error("queryTokenizer is required");\n            sorter = o.sorter;\n            o.sorter = sorter ? function(x) {\n                return x.sort(sorter);\n            } : _.identity;\n            o.local = _.isFunction(o.local) ? o.local() : o.local;\n            o.prefetch = parsePrefetch(o.prefetch);\n            o.remote = parseRemote(o.remote);\n            return o;\n        };\n        function parsePrefetch(o) {\n            var defaults;\n            if (!o) {\n                return null;\n            }\n            defaults = {\n                url: null,\n                ttl: 24 * 60 * 60 * 1e3,\n                cache: true,\n                cacheKey: null,\n                thumbprint: "",\n                prepare: _.identity,\n                transform: _.identity,\n                transport: null\n            };\n            o = _.isString(o) ? {\n                url: o\n            } : o;\n            o = _.mixin(defaults, o);\n            !o.url && $.error("prefetch requires url to be set");\n            o.transform = o.filter || o.transform;\n            o.cacheKey = o.cacheKey || o.url;\n            o.thumbprint = VERSION + o.thumbprint;\n            o.transport = o.transport ? callbackToDeferred(o.transport) : $.ajax;\n            return o;\n        }\n        function parseRemote(o) {\n            var defaults;\n            if (!o) {\n                return;\n            }\n            defaults = {\n                url: null,\n                cache: true,\n                prepare: null,\n                replace: null,\n                wildcard: null,\n                limiter: null,\n                rateLimitBy: "debounce",\n                rateLimitWait: 300,\n                transform: _.identity,\n                transport: null\n            };\n            o = _.isString(o) ? {\n                url: o\n            } : o;\n            o = _.mixin(defaults, o);\n            !o.url && $.error("remote requires url to be set");\n            o.transform = o.filter || o.transform;\n            o.prepare = toRemotePrepare(o);\n            o.limiter = toLimiter(o);\n            o.transport = o.transport ? callbackToDeferred(o.transport) : $.ajax;\n            delete o.replace;\n            delete o.wildcard;\n            delete o.rateLimitBy;\n            delete o.rateLimitWait;\n            return o;\n        }\n        function toRemotePrepare(o) {\n            var prepare, replace, wildcard;\n            prepare = o.prepare;\n            replace = o.replace;\n            wildcard = o.wildcard;\n            if (prepare) {\n                return prepare;\n            }\n            if (replace) {\n                prepare = prepareByReplace;\n            } else if (o.wildcard) {\n                prepare = prepareByWildcard;\n            } else {\n                prepare = idenityPrepare;\n            }\n            return prepare;\n            function prepareByReplace(query, settings) {\n                settings.url = replace(settings.url, query);\n                return settings;\n            }\n            function prepareByWildcard(query, settings) {\n                settings.url = settings.url.replace(wildcard, encodeURIComponent(query));\n                return settings;\n            }\n            function idenityPrepare(query, settings) {\n                return settings;\n            }\n        }\n        function toLimiter(o) {\n            var limiter, method, wait;\n            limiter = o.limiter;\n            method = o.rateLimitBy;\n            wait = o.rateLimitWait;\n            if (!limiter) {\n                limiter = /^throttle$/i.test(method) ? throttle(wait) : debounce(wait);\n            }\n            return limiter;\n            function debounce(wait) {\n                return function debounce(fn) {\n                    return _.debounce(fn, wait);\n                };\n            }\n            function throttle(wait) {\n                return function throttle(fn) {\n                    return _.throttle(fn, wait);\n                };\n            }\n        }\n        function callbackToDeferred(fn) {\n            return function wrapper(o) {\n                var deferred = $.Deferred();\n                fn(o, onSuccess, onError);\n                return deferred;\n                function onSuccess(resp) {\n                    _.defer(function() {\n                        deferred.resolve(resp);\n                    });\n                }\n                function onError(err) {\n                    _.defer(function() {\n                        deferred.reject(err);\n                    });\n                }\n            };\n        }\n    }();\n    var Bloodhound = function() {\n        "use strict";\n        var old;\n        old = window && window.Bloodhound;\n        function Bloodhound(o) {\n            o = oParser(o);\n            this.sorter = o.sorter;\n            this.identify = o.identify;\n            this.sufficient = o.sufficient;\n            this.local = o.local;\n            this.remote = o.remote ? new Remote(o.remote) : null;\n            this.prefetch = o.prefetch ? new Prefetch(o.prefetch) : null;\n            this.index = new SearchIndex({\n                identify: this.identify,\n                datumTokenizer: o.datumTokenizer,\n                queryTokenizer: o.queryTokenizer\n            });\n            o.initialize !== false && this.initialize();\n        }\n        Bloodhound.noConflict = function noConflict() {\n            window && (window.Bloodhound = old);\n            return Bloodhound;\n        };\n        Bloodhound.tokenizers = tokenizers;\n        _.mixin(Bloodhound.prototype, {\n            __ttAdapter: function ttAdapter() {\n                var that = this;\n                return this.remote ? withAsync : withoutAsync;\n                function withAsync(query, sync, async) {\n                    return that.search(query, sync, async);\n                }\n                function withoutAsync(query, sync) {\n                    return that.search(query, sync);\n                }\n            },\n            _loadPrefetch: function loadPrefetch() {\n                var that = this, deferred, serialized;\n                deferred = $.Deferred();\n                if (!this.prefetch) {\n                    deferred.resolve();\n                } else if (serialized = this.prefetch.fromCache()) {\n                    this.index.bootstrap(serialized);\n                    deferred.resolve();\n                } else {\n                    this.prefetch.fromNetwork(done);\n                }\n                return deferred.promise();\n                function done(err, data) {\n                    if (err) {\n                        return deferred.reject();\n                    }\n                    that.add(data);\n                    that.prefetch.store(that.index.serialize());\n                    deferred.resolve();\n                }\n            },\n            _initialize: function initialize() {\n                var that = this, deferred;\n                this.clear();\n                (this.initPromise = this._loadPrefetch()).done(addLocalToIndex);\n                return this.initPromise;\n                function addLocalToIndex() {\n                    that.add(that.local);\n                }\n            },\n            initialize: function initialize(force) {\n                return !this.initPromise || force ? this._initialize() : this.initPromise;\n            },\n            add: function add(data) {\n                this.index.add(data);\n                return this;\n            },\n            get: function get(ids) {\n                ids = _.isArray(ids) ? ids : [].slice.call(arguments);\n                return this.index.get(ids);\n            },\n            search: function search(query, sync, async) {\n                var that = this, local;\n                local = this.sorter(this.index.search(query));\n                sync(this.remote ? local.slice() : local);\n                if (this.remote && local.length < this.sufficient) {\n                    this.remote.get(query, processRemote);\n                } else if (this.remote) {\n                    this.remote.cancelLastRequest();\n                }\n                return this;\n                function processRemote(remote) {\n                    var nonDuplicates = [];\n                    _.each(remote, function(r) {\n                        !_.some(local, function(l) {\n                            return that.identify(r) === that.identify(l);\n                        }) && nonDuplicates.push(r);\n                    });\n                    async && async(nonDuplicates);\n                }\n            },\n            all: function all() {\n                return this.index.all();\n            },\n            clear: function clear() {\n                this.index.reset();\n                return this;\n            },\n            clearPrefetchCache: function clearPrefetchCache() {\n                this.prefetch && this.prefetch.clear();\n                return this;\n            },\n            clearRemoteCache: function clearRemoteCache() {\n                Transport.resetCache();\n                return this;\n            },\n            ttAdapter: function ttAdapter() {\n                return this.__ttAdapter();\n            }\n        });\n        return Bloodhound;\n    }();\n    return Bloodhound;\n});\n\n(function(root, factory) {\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [ __webpack_require__(/*! jquery */ "jquery") ], __WEBPACK_AMD_DEFINE_RESULT__ = (function(a0) {\n            return factory(a0);\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n})(this, function($) {\n    var _ = function() {\n        "use strict";\n        return {\n            isMsie: function() {\n                return /(msie|trident)/i.test(navigator.userAgent) ? navigator.userAgent.match(/(msie |rv:)(\\d+(.\\d+)?)/i)[2] : false;\n            },\n            isBlankString: function(str) {\n                return !str || /^\\s*$/.test(str);\n            },\n            escapeRegExChars: function(str) {\n                return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, "\\\\$&");\n            },\n            isString: function(obj) {\n                return typeof obj === "string";\n            },\n            isNumber: function(obj) {\n                return typeof obj === "number";\n            },\n            isArray: $.isArray,\n            isFunction: $.isFunction,\n            isObject: $.isPlainObject,\n            isUndefined: function(obj) {\n                return typeof obj === "undefined";\n            },\n            isElement: function(obj) {\n                return !!(obj && obj.nodeType === 1);\n            },\n            isJQuery: function(obj) {\n                return obj instanceof $;\n            },\n            toStr: function toStr(s) {\n                return _.isUndefined(s) || s === null ? "" : s + "";\n            },\n            bind: $.proxy,\n            each: function(collection, cb) {\n                $.each(collection, reverseArgs);\n                function reverseArgs(index, value) {\n                    return cb(value, index);\n                }\n            },\n            map: $.map,\n            filter: $.grep,\n            every: function(obj, test) {\n                var result = true;\n                if (!obj) {\n                    return result;\n                }\n                $.each(obj, function(key, val) {\n                    if (!(result = test.call(null, val, key, obj))) {\n                        return false;\n                    }\n                });\n                return !!result;\n            },\n            some: function(obj, test) {\n                var result = false;\n                if (!obj) {\n                    return result;\n                }\n                $.each(obj, function(key, val) {\n                    if (result = test.call(null, val, key, obj)) {\n                        return false;\n                    }\n                });\n                return !!result;\n            },\n            mixin: $.extend,\n            identity: function(x) {\n                return x;\n            },\n            clone: function(obj) {\n                return $.extend(true, {}, obj);\n            },\n            getIdGenerator: function() {\n                var counter = 0;\n                return function() {\n                    return counter++;\n                };\n            },\n            templatify: function templatify(obj) {\n                return $.isFunction(obj) ? obj : template;\n                function template() {\n                    return String(obj);\n                }\n            },\n            defer: function(fn) {\n                setTimeout(fn, 0);\n            },\n            debounce: function(func, wait, immediate) {\n                var timeout, result;\n                return function() {\n                    var context = this, args = arguments, later, callNow;\n                    later = function() {\n                        timeout = null;\n                        if (!immediate) {\n                            result = func.apply(context, args);\n                        }\n                    };\n                    callNow = immediate && !timeout;\n                    clearTimeout(timeout);\n                    timeout = setTimeout(later, wait);\n                    if (callNow) {\n                        result = func.apply(context, args);\n                    }\n                    return result;\n                };\n            },\n            throttle: function(func, wait) {\n                var context, args, timeout, result, previous, later;\n                previous = 0;\n                later = function() {\n                    previous = new Date();\n                    timeout = null;\n                    result = func.apply(context, args);\n                };\n                return function() {\n                    var now = new Date(), remaining = wait - (now - previous);\n                    context = this;\n                    args = arguments;\n                    if (remaining <= 0) {\n                        clearTimeout(timeout);\n                        timeout = null;\n                        previous = now;\n                        result = func.apply(context, args);\n                    } else if (!timeout) {\n                        timeout = setTimeout(later, remaining);\n                    }\n                    return result;\n                };\n            },\n            stringify: function(val) {\n                return _.isString(val) ? val : JSON.stringify(val);\n            },\n            noop: function() {}\n        };\n    }();\n    var WWW = function() {\n        "use strict";\n        var defaultClassNames = {\n            wrapper: "twitter-typeahead",\n            input: "tt-input",\n            hint: "tt-hint",\n            menu: "tt-menu",\n            dataset: "tt-dataset",\n            suggestion: "tt-suggestion",\n            selectable: "tt-selectable",\n            empty: "tt-empty",\n            open: "tt-open",\n            cursor: "tt-cursor",\n            highlight: "tt-highlight"\n        };\n        return build;\n        function build(o) {\n            var www, classes;\n            classes = _.mixin({}, defaultClassNames, o);\n            www = {\n                css: buildCss(),\n                classes: classes,\n                html: buildHtml(classes),\n                selectors: buildSelectors(classes)\n            };\n            return {\n                css: www.css,\n                html: www.html,\n                classes: www.classes,\n                selectors: www.selectors,\n                mixin: function(o) {\n                    _.mixin(o, www);\n                }\n            };\n        }\n        function buildHtml(c) {\n            return {\n                wrapper: \'<span class="\' + c.wrapper + \'"></span>\',\n                menu: \'<div class="\' + c.menu + \'"></div>\'\n            };\n        }\n        function buildSelectors(classes) {\n            var selectors = {};\n            _.each(classes, function(v, k) {\n                selectors[k] = "." + v;\n            });\n            return selectors;\n        }\n        function buildCss() {\n            var css = {\n                wrapper: {\n                    position: "relative",\n                    display: "inline-block"\n                },\n                hint: {\n                    position: "absolute",\n                    top: "0",\n                    left: "0",\n                    borderColor: "transparent",\n                    boxShadow: "none",\n                    opacity: "1"\n                },\n                input: {\n                    position: "relative",\n                    verticalAlign: "top",\n                    backgroundColor: "transparent"\n                },\n                inputWithNoHint: {\n                    position: "relative",\n                    verticalAlign: "top"\n                },\n                menu: {\n                    position: "absolute",\n                    top: "100%",\n                    left: "0",\n                    zIndex: "100",\n                    display: "none"\n                },\n                ltr: {\n                    left: "0",\n                    right: "auto"\n                },\n                rtl: {\n                    left: "auto",\n                    right: " 0"\n                }\n            };\n            if (_.isMsie()) {\n                _.mixin(css.input, {\n                    backgroundImage: "url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"\n                });\n            }\n            return css;\n        }\n    }();\n    var EventBus = function() {\n        "use strict";\n        var namespace, deprecationMap;\n        namespace = "typeahead:";\n        deprecationMap = {\n            render: "rendered",\n            cursorchange: "cursorchanged",\n            select: "selected",\n            autocomplete: "autocompleted"\n        };\n        function EventBus(o) {\n            if (!o || !o.el) {\n                $.error("EventBus initialized without el");\n            }\n            this.$el = $(o.el);\n        }\n        _.mixin(EventBus.prototype, {\n            _trigger: function(type, args) {\n                var $e;\n                $e = $.Event(namespace + type);\n                (args = args || []).unshift($e);\n                this.$el.trigger.apply(this.$el, args);\n                return $e;\n            },\n            before: function(type) {\n                var args, $e;\n                args = [].slice.call(arguments, 1);\n                $e = this._trigger("before" + type, args);\n                return $e.isDefaultPrevented();\n            },\n            trigger: function(type) {\n                var deprecatedType;\n                this._trigger(type, [].slice.call(arguments, 1));\n                if (deprecatedType = deprecationMap[type]) {\n                    this._trigger(deprecatedType, [].slice.call(arguments, 1));\n                }\n            }\n        });\n        return EventBus;\n    }();\n    var EventEmitter = function() {\n        "use strict";\n        var splitter = /\\s+/, nextTick = getNextTick();\n        return {\n            onSync: onSync,\n            onAsync: onAsync,\n            off: off,\n            trigger: trigger\n        };\n        function on(method, types, cb, context) {\n            var type;\n            if (!cb) {\n                return this;\n            }\n            types = types.split(splitter);\n            cb = context ? bindContext(cb, context) : cb;\n            this._callbacks = this._callbacks || {};\n            while (type = types.shift()) {\n                this._callbacks[type] = this._callbacks[type] || {\n                    sync: [],\n                    async: []\n                };\n                this._callbacks[type][method].push(cb);\n            }\n            return this;\n        }\n        function onAsync(types, cb, context) {\n            return on.call(this, "async", types, cb, context);\n        }\n        function onSync(types, cb, context) {\n            return on.call(this, "sync", types, cb, context);\n        }\n        function off(types) {\n            var type;\n            if (!this._callbacks) {\n                return this;\n            }\n            types = types.split(splitter);\n            while (type = types.shift()) {\n                delete this._callbacks[type];\n            }\n            return this;\n        }\n        function trigger(types) {\n            var type, callbacks, args, syncFlush, asyncFlush;\n            if (!this._callbacks) {\n                return this;\n            }\n            types = types.split(splitter);\n            args = [].slice.call(arguments, 1);\n            while ((type = types.shift()) && (callbacks = this._callbacks[type])) {\n                syncFlush = getFlush(callbacks.sync, this, [ type ].concat(args));\n                asyncFlush = getFlush(callbacks.async, this, [ type ].concat(args));\n                syncFlush() && nextTick(asyncFlush);\n            }\n            return this;\n        }\n        function getFlush(callbacks, context, args) {\n            return flush;\n            function flush() {\n                var cancelled;\n                for (var i = 0, len = callbacks.length; !cancelled && i < len; i += 1) {\n                    cancelled = callbacks[i].apply(context, args) === false;\n                }\n                return !cancelled;\n            }\n        }\n        function getNextTick() {\n            var nextTickFn;\n            if (window.setImmediate) {\n                nextTickFn = function nextTickSetImmediate(fn) {\n                    setImmediate(function() {\n                        fn();\n                    });\n                };\n            } else {\n                nextTickFn = function nextTickSetTimeout(fn) {\n                    setTimeout(function() {\n                        fn();\n                    }, 0);\n                };\n            }\n            return nextTickFn;\n        }\n        function bindContext(fn, context) {\n            return fn.bind ? fn.bind(context) : function() {\n                fn.apply(context, [].slice.call(arguments, 0));\n            };\n        }\n    }();\n    var highlight = function(doc) {\n        "use strict";\n        var defaults = {\n            node: null,\n            pattern: null,\n            tagName: "strong",\n            className: null,\n            wordsOnly: false,\n            caseSensitive: false\n        };\n        return function hightlight(o) {\n            var regex;\n            o = _.mixin({}, defaults, o);\n            if (!o.node || !o.pattern) {\n                return;\n            }\n            o.pattern = _.isArray(o.pattern) ? o.pattern : [ o.pattern ];\n            regex = getRegex(o.pattern, o.caseSensitive, o.wordsOnly);\n            traverse(o.node, hightlightTextNode);\n            function hightlightTextNode(textNode) {\n                var match, patternNode, wrapperNode;\n                if (match = regex.exec(textNode.data)) {\n                    wrapperNode = doc.createElement(o.tagName);\n                    o.className && (wrapperNode.className = o.className);\n                    patternNode = textNode.splitText(match.index);\n                    patternNode.splitText(match[0].length);\n                    wrapperNode.appendChild(patternNode.cloneNode(true));\n                    textNode.parentNode.replaceChild(wrapperNode, patternNode);\n                }\n                return !!match;\n            }\n            function traverse(el, hightlightTextNode) {\n                var childNode, TEXT_NODE_TYPE = 3;\n                for (var i = 0; i < el.childNodes.length; i++) {\n                    childNode = el.childNodes[i];\n                    if (childNode.nodeType === TEXT_NODE_TYPE) {\n                        i += hightlightTextNode(childNode) ? 1 : 0;\n                    } else {\n                        traverse(childNode, hightlightTextNode);\n                    }\n                }\n            }\n        };\n        function getRegex(patterns, caseSensitive, wordsOnly) {\n            var escapedPatterns = [], regexStr;\n            for (var i = 0, len = patterns.length; i < len; i++) {\n                escapedPatterns.push(_.escapeRegExChars(patterns[i]));\n            }\n            regexStr = wordsOnly ? "\\\\b(" + escapedPatterns.join("|") + ")\\\\b" : "(" + escapedPatterns.join("|") + ")";\n            return caseSensitive ? new RegExp(regexStr) : new RegExp(regexStr, "i");\n        }\n    }(window.document);\n    var Input = function() {\n        "use strict";\n        var specialKeyCodeMap;\n        specialKeyCodeMap = {\n            9: "tab",\n            27: "esc",\n            37: "left",\n            39: "right",\n            13: "enter",\n            38: "up",\n            40: "down"\n        };\n        function Input(o, www) {\n            o = o || {};\n            if (!o.input) {\n                $.error("input is missing");\n            }\n            www.mixin(this);\n            this.$hint = $(o.hint);\n            this.$input = $(o.input);\n            this.query = this.$input.val();\n            this.queryWhenFocused = this.hasFocus() ? this.query : null;\n            this.$overflowHelper = buildOverflowHelper(this.$input);\n            this._checkLanguageDirection();\n            if (this.$hint.length === 0) {\n                this.setHint = this.getHint = this.clearHint = this.clearHintIfInvalid = _.noop;\n            }\n        }\n        Input.normalizeQuery = function(str) {\n            return _.toStr(str).replace(/^\\s*/g, "").replace(/\\s{2,}/g, " ");\n        };\n        _.mixin(Input.prototype, EventEmitter, {\n            _onBlur: function onBlur() {\n                this.resetInputValue();\n                this.trigger("blurred");\n            },\n            _onFocus: function onFocus() {\n                this.queryWhenFocused = this.query;\n                this.trigger("focused");\n            },\n            _onKeydown: function onKeydown($e) {\n                var keyName = specialKeyCodeMap[$e.which || $e.keyCode];\n                this._managePreventDefault(keyName, $e);\n                if (keyName && this._shouldTrigger(keyName, $e)) {\n                    this.trigger(keyName + "Keyed", $e);\n                }\n            },\n            _onInput: function onInput() {\n                this._setQuery(this.getInputValue());\n                this.clearHintIfInvalid();\n                this._checkLanguageDirection();\n            },\n            _managePreventDefault: function managePreventDefault(keyName, $e) {\n                var preventDefault;\n                switch (keyName) {\n                  case "up":\n                  case "down":\n                    preventDefault = !withModifier($e);\n                    break;\n\n                  default:\n                    preventDefault = false;\n                }\n                preventDefault && $e.preventDefault();\n            },\n            _shouldTrigger: function shouldTrigger(keyName, $e) {\n                var trigger;\n                switch (keyName) {\n                  case "tab":\n                    trigger = !withModifier($e);\n                    break;\n\n                  default:\n                    trigger = true;\n                }\n                return trigger;\n            },\n            _checkLanguageDirection: function checkLanguageDirection() {\n                var dir = (this.$input.css("direction") || "ltr").toLowerCase();\n                if (this.dir !== dir) {\n                    this.dir = dir;\n                    this.$hint.attr("dir", dir);\n                    this.trigger("langDirChanged", dir);\n                }\n            },\n            _setQuery: function setQuery(val, silent) {\n                var areEquivalent, hasDifferentWhitespace;\n                areEquivalent = areQueriesEquivalent(val, this.query);\n                hasDifferentWhitespace = areEquivalent ? this.query.length !== val.length : false;\n                this.query = val;\n                if (!silent && !areEquivalent) {\n                    this.trigger("queryChanged", this.query);\n                } else if (!silent && hasDifferentWhitespace) {\n                    this.trigger("whitespaceChanged", this.query);\n                }\n            },\n            bind: function() {\n                var that = this, onBlur, onFocus, onKeydown, onInput;\n                onBlur = _.bind(this._onBlur, this);\n                onFocus = _.bind(this._onFocus, this);\n                onKeydown = _.bind(this._onKeydown, this);\n                onInput = _.bind(this._onInput, this);\n                this.$input.on("blur.tt", onBlur).on("focus.tt", onFocus).on("keydown.tt", onKeydown);\n                if (!_.isMsie() || _.isMsie() > 9) {\n                    this.$input.on("input.tt", onInput);\n                } else {\n                    this.$input.on("keydown.tt keypress.tt cut.tt paste.tt", function($e) {\n                        if (specialKeyCodeMap[$e.which || $e.keyCode]) {\n                            return;\n                        }\n                        _.defer(_.bind(that._onInput, that, $e));\n                    });\n                }\n                return this;\n            },\n            focus: function focus() {\n                this.$input.focus();\n            },\n            blur: function blur() {\n                this.$input.blur();\n            },\n            getLangDir: function getLangDir() {\n                return this.dir;\n            },\n            getQuery: function getQuery() {\n                return this.query || "";\n            },\n            setQuery: function setQuery(val, silent) {\n                this.setInputValue(val);\n                this._setQuery(val, silent);\n            },\n            hasQueryChangedSinceLastFocus: function hasQueryChangedSinceLastFocus() {\n                return this.query !== this.queryWhenFocused;\n            },\n            getInputValue: function getInputValue() {\n                return this.$input.val();\n            },\n            setInputValue: function setInputValue(value) {\n                this.$input.val(value);\n                this.clearHintIfInvalid();\n                this._checkLanguageDirection();\n            },\n            resetInputValue: function resetInputValue() {\n                this.setInputValue(this.query);\n            },\n            getHint: function getHint() {\n                return this.$hint.val();\n            },\n            setHint: function setHint(value) {\n                this.$hint.val(value);\n            },\n            clearHint: function clearHint() {\n                this.setHint("");\n            },\n            clearHintIfInvalid: function clearHintIfInvalid() {\n                var val, hint, valIsPrefixOfHint, isValid;\n                val = this.getInputValue();\n                hint = this.getHint();\n                valIsPrefixOfHint = val !== hint && hint.indexOf(val) === 0;\n                isValid = val !== "" && valIsPrefixOfHint && !this.hasOverflow();\n                !isValid && this.clearHint();\n            },\n            hasFocus: function hasFocus() {\n                return this.$input.is(":focus");\n            },\n            hasOverflow: function hasOverflow() {\n                var constraint = this.$input.width() - 2;\n                this.$overflowHelper.text(this.getInputValue());\n                return this.$overflowHelper.width() >= constraint;\n            },\n            isCursorAtEnd: function() {\n                var valueLength, selectionStart, range;\n                valueLength = this.$input.val().length;\n                selectionStart = this.$input[0].selectionStart;\n                if (_.isNumber(selectionStart)) {\n                    return selectionStart === valueLength;\n                } else if (document.selection) {\n                    range = document.selection.createRange();\n                    range.moveStart("character", -valueLength);\n                    return valueLength === range.text.length;\n                }\n                return true;\n            },\n            destroy: function destroy() {\n                this.$hint.off(".tt");\n                this.$input.off(".tt");\n                this.$overflowHelper.remove();\n                this.$hint = this.$input = this.$overflowHelper = $("<div>");\n            }\n        });\n        return Input;\n        function buildOverflowHelper($input) {\n            return $(\'<pre aria-hidden="true"></pre>\').css({\n                position: "absolute",\n                visibility: "hidden",\n                whiteSpace: "pre",\n                fontFamily: $input.css("font-family"),\n                fontSize: $input.css("font-size"),\n                fontStyle: $input.css("font-style"),\n                fontVariant: $input.css("font-variant"),\n                fontWeight: $input.css("font-weight"),\n                wordSpacing: $input.css("word-spacing"),\n                letterSpacing: $input.css("letter-spacing"),\n                textIndent: $input.css("text-indent"),\n                textRendering: $input.css("text-rendering"),\n                textTransform: $input.css("text-transform")\n            }).insertAfter($input);\n        }\n        function areQueriesEquivalent(a, b) {\n            return Input.normalizeQuery(a) === Input.normalizeQuery(b);\n        }\n        function withModifier($e) {\n            return $e.altKey || $e.ctrlKey || $e.metaKey || $e.shiftKey;\n        }\n    }();\n    var Dataset = function() {\n        "use strict";\n        var keys, nameGenerator;\n        keys = {\n            val: "tt-selectable-display",\n            obj: "tt-selectable-object"\n        };\n        nameGenerator = _.getIdGenerator();\n        function Dataset(o, www) {\n            o = o || {};\n            o.templates = o.templates || {};\n            o.templates.notFound = o.templates.notFound || o.templates.empty;\n            if (!o.source) {\n                $.error("missing source");\n            }\n            if (!o.node) {\n                $.error("missing node");\n            }\n            if (o.name && !isValidName(o.name)) {\n                $.error("invalid dataset name: " + o.name);\n            }\n            www.mixin(this);\n            this.highlight = !!o.highlight;\n            this.name = o.name || nameGenerator();\n            this.limit = o.limit || 5;\n            this.displayFn = getDisplayFn(o.display || o.displayKey);\n            this.templates = getTemplates(o.templates, this.displayFn);\n            this.source = o.source.__ttAdapter ? o.source.__ttAdapter() : o.source;\n            this.async = _.isUndefined(o.async) ? this.source.length > 2 : !!o.async;\n            this._resetLastSuggestion();\n            this.$el = $(o.node).addClass(this.classes.dataset).addClass(this.classes.dataset + "-" + this.name);\n        }\n        Dataset.extractData = function extractData(el) {\n            var $el = $(el);\n            if ($el.data(keys.obj)) {\n                return {\n                    val: $el.data(keys.val) || "",\n                    obj: $el.data(keys.obj) || null\n                };\n            }\n            return null;\n        };\n        _.mixin(Dataset.prototype, EventEmitter, {\n            _overwrite: function overwrite(query, suggestions) {\n                suggestions = suggestions || [];\n                if (suggestions.length) {\n                    this._renderSuggestions(query, suggestions);\n                } else if (this.async && this.templates.pending) {\n                    this._renderPending(query);\n                } else if (!this.async && this.templates.notFound) {\n                    this._renderNotFound(query);\n                } else {\n                    this._empty();\n                }\n                this.trigger("rendered", this.name, suggestions, false);\n            },\n            _append: function append(query, suggestions) {\n                suggestions = suggestions || [];\n                if (suggestions.length && this.$lastSuggestion.length) {\n                    this._appendSuggestions(query, suggestions);\n                } else if (suggestions.length) {\n                    this._renderSuggestions(query, suggestions);\n                } else if (!this.$lastSuggestion.length && this.templates.notFound) {\n                    this._renderNotFound(query);\n                }\n                this.trigger("rendered", this.name, suggestions, true);\n            },\n            _renderSuggestions: function renderSuggestions(query, suggestions) {\n                var $fragment;\n                $fragment = this._getSuggestionsFragment(query, suggestions);\n                this.$lastSuggestion = $fragment.children().last();\n                this.$el.html($fragment).prepend(this._getHeader(query, suggestions)).append(this._getFooter(query, suggestions));\n            },\n            _appendSuggestions: function appendSuggestions(query, suggestions) {\n                var $fragment, $lastSuggestion;\n                $fragment = this._getSuggestionsFragment(query, suggestions);\n                $lastSuggestion = $fragment.children().last();\n                this.$lastSuggestion.after($fragment);\n                this.$lastSuggestion = $lastSuggestion;\n            },\n            _renderPending: function renderPending(query) {\n                var template = this.templates.pending;\n                this._resetLastSuggestion();\n                template && this.$el.html(template({\n                    query: query,\n                    dataset: this.name\n                }));\n            },\n            _renderNotFound: function renderNotFound(query) {\n                var template = this.templates.notFound;\n                this._resetLastSuggestion();\n                template && this.$el.html(template({\n                    query: query,\n                    dataset: this.name\n                }));\n            },\n            _empty: function empty() {\n                this.$el.empty();\n                this._resetLastSuggestion();\n            },\n            _getSuggestionsFragment: function getSuggestionsFragment(query, suggestions) {\n                var that = this, fragment;\n                fragment = document.createDocumentFragment();\n                _.each(suggestions, function getSuggestionNode(suggestion) {\n                    var $el, context;\n                    context = that._injectQuery(query, suggestion);\n                    $el = $(that.templates.suggestion(context)).data(keys.obj, suggestion).data(keys.val, that.displayFn(suggestion)).addClass(that.classes.suggestion + " " + that.classes.selectable);\n                    fragment.appendChild($el[0]);\n                });\n                this.highlight && highlight({\n                    className: this.classes.highlight,\n                    node: fragment,\n                    pattern: query\n                });\n                return $(fragment);\n            },\n            _getFooter: function getFooter(query, suggestions) {\n                return this.templates.footer ? this.templates.footer({\n                    query: query,\n                    suggestions: suggestions,\n                    dataset: this.name\n                }) : null;\n            },\n            _getHeader: function getHeader(query, suggestions) {\n                return this.templates.header ? this.templates.header({\n                    query: query,\n                    suggestions: suggestions,\n                    dataset: this.name\n                }) : null;\n            },\n            _resetLastSuggestion: function resetLastSuggestion() {\n                this.$lastSuggestion = $();\n            },\n            _injectQuery: function injectQuery(query, obj) {\n                return _.isObject(obj) ? _.mixin({\n                    _query: query\n                }, obj) : obj;\n            },\n            update: function update(query) {\n                var that = this, canceled = false, syncCalled = false, rendered = 0;\n                this.cancel();\n                this.cancel = function cancel() {\n                    canceled = true;\n                    that.cancel = $.noop;\n                    that.async && that.trigger("asyncCanceled", query);\n                };\n                this.source(query, sync, async);\n                !syncCalled && sync([]);\n                function sync(suggestions) {\n                    if (syncCalled) {\n                        return;\n                    }\n                    syncCalled = true;\n                    suggestions = (suggestions || []).slice(0, that.limit);\n                    rendered = suggestions.length;\n                    that._overwrite(query, suggestions);\n                    if (rendered < that.limit && that.async) {\n                        that.trigger("asyncRequested", query);\n                    }\n                }\n                function async(suggestions) {\n                    suggestions = suggestions || [];\n                    if (!canceled && rendered < that.limit) {\n                        that.cancel = $.noop;\n                        rendered += suggestions.length;\n                        that._append(query, suggestions.slice(0, that.limit - rendered));\n                        that.async && that.trigger("asyncReceived", query);\n                    }\n                }\n            },\n            cancel: $.noop,\n            clear: function clear() {\n                this._empty();\n                this.cancel();\n                this.trigger("cleared");\n            },\n            isEmpty: function isEmpty() {\n                return this.$el.is(":empty");\n            },\n            destroy: function destroy() {\n                this.$el = $("<div>");\n            }\n        });\n        return Dataset;\n        function getDisplayFn(display) {\n            display = display || _.stringify;\n            return _.isFunction(display) ? display : displayFn;\n            function displayFn(obj) {\n                return obj[display];\n            }\n        }\n        function getTemplates(templates, displayFn) {\n            return {\n                notFound: templates.notFound && _.templatify(templates.notFound),\n                pending: templates.pending && _.templatify(templates.pending),\n                header: templates.header && _.templatify(templates.header),\n                footer: templates.footer && _.templatify(templates.footer),\n                suggestion: templates.suggestion || suggestionTemplate\n            };\n            function suggestionTemplate(context) {\n                return $("<div>").text(displayFn(context));\n            }\n        }\n        function isValidName(str) {\n            return /^[_a-zA-Z0-9-]+$/.test(str);\n        }\n    }();\n    var Menu = function() {\n        "use strict";\n        function Menu(o, www) {\n            var that = this;\n            o = o || {};\n            if (!o.node) {\n                $.error("node is required");\n            }\n            www.mixin(this);\n            this.$node = $(o.node);\n            this.query = null;\n            this.datasets = _.map(o.datasets, initializeDataset);\n            function initializeDataset(oDataset) {\n                var node = that.$node.find(oDataset.node).first();\n                oDataset.node = node.length ? node : $("<div>").appendTo(that.$node);\n                return new Dataset(oDataset, www);\n            }\n        }\n        _.mixin(Menu.prototype, EventEmitter, {\n            _onSelectableClick: function onSelectableClick($e) {\n                this.trigger("selectableClicked", $($e.currentTarget));\n            },\n            _onRendered: function onRendered(type, dataset, suggestions, async) {\n                this.$node.toggleClass(this.classes.empty, this._allDatasetsEmpty());\n                this.trigger("datasetRendered", dataset, suggestions, async);\n            },\n            _onCleared: function onCleared() {\n                this.$node.toggleClass(this.classes.empty, this._allDatasetsEmpty());\n                this.trigger("datasetCleared");\n            },\n            _propagate: function propagate() {\n                this.trigger.apply(this, arguments);\n            },\n            _allDatasetsEmpty: function allDatasetsEmpty() {\n                return _.every(this.datasets, isDatasetEmpty);\n                function isDatasetEmpty(dataset) {\n                    return dataset.isEmpty();\n                }\n            },\n            _getSelectables: function getSelectables() {\n                return this.$node.find(this.selectors.selectable);\n            },\n            _removeCursor: function _removeCursor() {\n                var $selectable = this.getActiveSelectable();\n                $selectable && $selectable.removeClass(this.classes.cursor);\n            },\n            _ensureVisible: function ensureVisible($el) {\n                var elTop, elBottom, nodeScrollTop, nodeHeight;\n                elTop = $el.position().top;\n                elBottom = elTop + $el.outerHeight(true);\n                nodeScrollTop = this.$node.scrollTop();\n                nodeHeight = this.$node.height() + parseInt(this.$node.css("paddingTop"), 10) + parseInt(this.$node.css("paddingBottom"), 10);\n                if (elTop < 0) {\n                    this.$node.scrollTop(nodeScrollTop + elTop);\n                } else if (nodeHeight < elBottom) {\n                    this.$node.scrollTop(nodeScrollTop + (elBottom - nodeHeight));\n                }\n            },\n            bind: function() {\n                var that = this, onSelectableClick;\n                onSelectableClick = _.bind(this._onSelectableClick, this);\n                this.$node.on("click.tt", this.selectors.selectable, onSelectableClick);\n                _.each(this.datasets, function(dataset) {\n                    dataset.onSync("asyncRequested", that._propagate, that).onSync("asyncCanceled", that._propagate, that).onSync("asyncReceived", that._propagate, that).onSync("rendered", that._onRendered, that).onSync("cleared", that._onCleared, that);\n                });\n                return this;\n            },\n            isOpen: function isOpen() {\n                return this.$node.hasClass(this.classes.open);\n            },\n            open: function open() {\n                this.$node.addClass(this.classes.open);\n            },\n            close: function close() {\n                this.$node.removeClass(this.classes.open);\n                this._removeCursor();\n            },\n            setLanguageDirection: function setLanguageDirection(dir) {\n                this.$node.attr("dir", dir);\n            },\n            selectableRelativeToCursor: function selectableRelativeToCursor(delta) {\n                var $selectables, $oldCursor, oldIndex, newIndex;\n                $oldCursor = this.getActiveSelectable();\n                $selectables = this._getSelectables();\n                oldIndex = $oldCursor ? $selectables.index($oldCursor) : -1;\n                newIndex = oldIndex + delta;\n                newIndex = (newIndex + 1) % ($selectables.length + 1) - 1;\n                newIndex = newIndex < -1 ? $selectables.length - 1 : newIndex;\n                return newIndex === -1 ? null : $selectables.eq(newIndex);\n            },\n            setCursor: function setCursor($selectable) {\n                this._removeCursor();\n                if ($selectable = $selectable && $selectable.first()) {\n                    $selectable.addClass(this.classes.cursor);\n                    this._ensureVisible($selectable);\n                }\n            },\n            getSelectableData: function getSelectableData($el) {\n                return $el && $el.length ? Dataset.extractData($el) : null;\n            },\n            getActiveSelectable: function getActiveSelectable() {\n                var $selectable = this._getSelectables().filter(this.selectors.cursor).first();\n                return $selectable.length ? $selectable : null;\n            },\n            getTopSelectable: function getTopSelectable() {\n                var $selectable = this._getSelectables().first();\n                return $selectable.length ? $selectable : null;\n            },\n            update: function update(query) {\n                var isValidUpdate = query !== this.query;\n                if (isValidUpdate) {\n                    this.query = query;\n                    _.each(this.datasets, updateDataset);\n                }\n                return isValidUpdate;\n                function updateDataset(dataset) {\n                    dataset.update(query);\n                }\n            },\n            empty: function empty() {\n                _.each(this.datasets, clearDataset);\n                this.query = null;\n                this.$node.addClass(this.classes.empty);\n                function clearDataset(dataset) {\n                    dataset.clear();\n                }\n            },\n            destroy: function destroy() {\n                this.$node.off(".tt");\n                this.$node = $("<div>");\n                _.each(this.datasets, destroyDataset);\n                function destroyDataset(dataset) {\n                    dataset.destroy();\n                }\n            }\n        });\n        return Menu;\n    }();\n    var DefaultMenu = function() {\n        "use strict";\n        var s = Menu.prototype;\n        function DefaultMenu() {\n            Menu.apply(this, [].slice.call(arguments, 0));\n        }\n        _.mixin(DefaultMenu.prototype, Menu.prototype, {\n            open: function open() {\n                !this._allDatasetsEmpty() && this._show();\n                return s.open.apply(this, [].slice.call(arguments, 0));\n            },\n            close: function close() {\n                this._hide();\n                return s.close.apply(this, [].slice.call(arguments, 0));\n            },\n            _onRendered: function onRendered() {\n                if (this._allDatasetsEmpty()) {\n                    this._hide();\n                } else {\n                    this.isOpen() && this._show();\n                }\n                return s._onRendered.apply(this, [].slice.call(arguments, 0));\n            },\n            _onCleared: function onCleared() {\n                if (this._allDatasetsEmpty()) {\n                    this._hide();\n                } else {\n                    this.isOpen() && this._show();\n                }\n                return s._onCleared.apply(this, [].slice.call(arguments, 0));\n            },\n            setLanguageDirection: function setLanguageDirection(dir) {\n                this.$node.css(dir === "ltr" ? this.css.ltr : this.css.rtl);\n                return s.setLanguageDirection.apply(this, [].slice.call(arguments, 0));\n            },\n            _hide: function hide() {\n                this.$node.hide();\n            },\n            _show: function show() {\n                this.$node.css("display", "block");\n            }\n        });\n        return DefaultMenu;\n    }();\n    var Typeahead = function() {\n        "use strict";\n        function Typeahead(o, www) {\n            var onFocused, onBlurred, onEnterKeyed, onTabKeyed, onEscKeyed, onUpKeyed, onDownKeyed, onLeftKeyed, onRightKeyed, onQueryChanged, onWhitespaceChanged;\n            o = o || {};\n            if (!o.input) {\n                $.error("missing input");\n            }\n            if (!o.menu) {\n                $.error("missing menu");\n            }\n            if (!o.eventBus) {\n                $.error("missing event bus");\n            }\n            www.mixin(this);\n            this.eventBus = o.eventBus;\n            this.minLength = _.isNumber(o.minLength) ? o.minLength : 1;\n            this.input = o.input;\n            this.menu = o.menu;\n            this.enabled = true;\n            this.active = false;\n            this.input.hasFocus() && this.activate();\n            this.dir = this.input.getLangDir();\n            this._hacks();\n            this.menu.bind().onSync("selectableClicked", this._onSelectableClicked, this).onSync("asyncRequested", this._onAsyncRequested, this).onSync("asyncCanceled", this._onAsyncCanceled, this).onSync("asyncReceived", this._onAsyncReceived, this).onSync("datasetRendered", this._onDatasetRendered, this).onSync("datasetCleared", this._onDatasetCleared, this);\n            onFocused = c(this, "activate", "open", "_onFocused");\n            onBlurred = c(this, "deactivate", "_onBlurred");\n            onEnterKeyed = c(this, "isActive", "isOpen", "_onEnterKeyed");\n            onTabKeyed = c(this, "isActive", "isOpen", "_onTabKeyed");\n            onEscKeyed = c(this, "isActive", "_onEscKeyed");\n            onUpKeyed = c(this, "isActive", "open", "_onUpKeyed");\n            onDownKeyed = c(this, "isActive", "open", "_onDownKeyed");\n            onLeftKeyed = c(this, "isActive", "isOpen", "_onLeftKeyed");\n            onRightKeyed = c(this, "isActive", "isOpen", "_onRightKeyed");\n            onQueryChanged = c(this, "_openIfActive", "_onQueryChanged");\n            onWhitespaceChanged = c(this, "_openIfActive", "_onWhitespaceChanged");\n            this.input.bind().onSync("focused", onFocused, this).onSync("blurred", onBlurred, this).onSync("enterKeyed", onEnterKeyed, this).onSync("tabKeyed", onTabKeyed, this).onSync("escKeyed", onEscKeyed, this).onSync("upKeyed", onUpKeyed, this).onSync("downKeyed", onDownKeyed, this).onSync("leftKeyed", onLeftKeyed, this).onSync("rightKeyed", onRightKeyed, this).onSync("queryChanged", onQueryChanged, this).onSync("whitespaceChanged", onWhitespaceChanged, this).onSync("langDirChanged", this._onLangDirChanged, this);\n        }\n        _.mixin(Typeahead.prototype, {\n            _hacks: function hacks() {\n                var $input, $menu;\n                $input = this.input.$input || $("<div>");\n                $menu = this.menu.$node || $("<div>");\n                $input.on("blur.tt", function($e) {\n                    var active, isActive, hasActive;\n                    active = document.activeElement;\n                    isActive = $menu.is(active);\n                    hasActive = $menu.has(active).length > 0;\n                    if (_.isMsie() && (isActive || hasActive)) {\n                        $e.preventDefault();\n                        $e.stopImmediatePropagation();\n                        _.defer(function() {\n                            $input.focus();\n                        });\n                    }\n                });\n                $menu.on("mousedown.tt", function($e) {\n                    $e.preventDefault();\n                });\n            },\n            _onSelectableClicked: function onSelectableClicked(type, $el) {\n                this.select($el);\n            },\n            _onDatasetCleared: function onDatasetCleared() {\n                this._updateHint();\n            },\n            _onDatasetRendered: function onDatasetRendered(type, dataset, suggestions, async) {\n                this._updateHint();\n                this.eventBus.trigger("render", suggestions, async, dataset);\n            },\n            _onAsyncRequested: function onAsyncRequested(type, dataset, query) {\n                this.eventBus.trigger("asyncrequest", query, dataset);\n            },\n            _onAsyncCanceled: function onAsyncCanceled(type, dataset, query) {\n                this.eventBus.trigger("asynccancel", query, dataset);\n            },\n            _onAsyncReceived: function onAsyncReceived(type, dataset, query) {\n                this.eventBus.trigger("asyncreceive", query, dataset);\n            },\n            _onFocused: function onFocused() {\n                this._minLengthMet() && this.menu.update(this.input.getQuery());\n            },\n            _onBlurred: function onBlurred() {\n                if (this.input.hasQueryChangedSinceLastFocus()) {\n                    this.eventBus.trigger("change", this.input.getQuery());\n                }\n            },\n            _onEnterKeyed: function onEnterKeyed(type, $e) {\n                var $selectable;\n                if ($selectable = this.menu.getActiveSelectable()) {\n                    this.select($selectable) && $e.preventDefault();\n                }\n            },\n            _onTabKeyed: function onTabKeyed(type, $e) {\n                var $selectable;\n                if ($selectable = this.menu.getActiveSelectable()) {\n                    this.select($selectable) && $e.preventDefault();\n                } else if ($selectable = this.menu.getTopSelectable()) {\n                    this.autocomplete($selectable) && $e.preventDefault();\n                }\n            },\n            _onEscKeyed: function onEscKeyed() {\n                this.close();\n            },\n            _onUpKeyed: function onUpKeyed() {\n                this.moveCursor(-1);\n            },\n            _onDownKeyed: function onDownKeyed() {\n                this.moveCursor(+1);\n            },\n            _onLeftKeyed: function onLeftKeyed() {\n                if (this.dir === "rtl" && this.input.isCursorAtEnd()) {\n                    this.autocomplete(this.menu.getTopSelectable());\n                }\n            },\n            _onRightKeyed: function onRightKeyed() {\n                if (this.dir === "ltr" && this.input.isCursorAtEnd()) {\n                    this.autocomplete(this.menu.getTopSelectable());\n                }\n            },\n            _onQueryChanged: function onQueryChanged(e, query) {\n                this._minLengthMet(query) ? this.menu.update(query) : this.menu.empty();\n            },\n            _onWhitespaceChanged: function onWhitespaceChanged() {\n                this._updateHint();\n            },\n            _onLangDirChanged: function onLangDirChanged(e, dir) {\n                if (this.dir !== dir) {\n                    this.dir = dir;\n                    this.menu.setLanguageDirection(dir);\n                }\n            },\n            _openIfActive: function openIfActive() {\n                this.isActive() && this.open();\n            },\n            _minLengthMet: function minLengthMet(query) {\n                query = _.isString(query) ? query : this.input.getQuery() || "";\n                return query.length >= this.minLength;\n            },\n            _updateHint: function updateHint() {\n                var $selectable, data, val, query, escapedQuery, frontMatchRegEx, match;\n                $selectable = this.menu.getTopSelectable();\n                data = this.menu.getSelectableData($selectable);\n                val = this.input.getInputValue();\n                if (data && !_.isBlankString(val) && !this.input.hasOverflow()) {\n                    query = Input.normalizeQuery(val);\n                    escapedQuery = _.escapeRegExChars(query);\n                    frontMatchRegEx = new RegExp("^(?:" + escapedQuery + ")(.+$)", "i");\n                    match = frontMatchRegEx.exec(data.val);\n                    match && this.input.setHint(val + match[1]);\n                } else {\n                    this.input.clearHint();\n                }\n            },\n            isEnabled: function isEnabled() {\n                return this.enabled;\n            },\n            enable: function enable() {\n                this.enabled = true;\n            },\n            disable: function disable() {\n                this.enabled = false;\n            },\n            isActive: function isActive() {\n                return this.active;\n            },\n            activate: function activate() {\n                if (this.isActive()) {\n                    return true;\n                } else if (!this.isEnabled() || this.eventBus.before("active")) {\n                    return false;\n                } else {\n                    this.active = true;\n                    this.eventBus.trigger("active");\n                    return true;\n                }\n            },\n            deactivate: function deactivate() {\n                if (!this.isActive()) {\n                    return true;\n                } else if (this.eventBus.before("idle")) {\n                    return false;\n                } else {\n                    this.active = false;\n                    this.close();\n                    this.eventBus.trigger("idle");\n                    return true;\n                }\n            },\n            isOpen: function isOpen() {\n                return this.menu.isOpen();\n            },\n            open: function open() {\n                if (!this.isOpen() && !this.eventBus.before("open")) {\n                    this.menu.open();\n                    this._updateHint();\n                    this.eventBus.trigger("open");\n                }\n                return this.isOpen();\n            },\n            close: function close() {\n                if (this.isOpen() && !this.eventBus.before("close")) {\n                    this.menu.close();\n                    this.input.clearHint();\n                    this.input.resetInputValue();\n                    this.eventBus.trigger("close");\n                }\n                return !this.isOpen();\n            },\n            setVal: function setVal(val) {\n                this.input.setQuery(_.toStr(val));\n            },\n            getVal: function getVal() {\n                return this.input.getQuery();\n            },\n            select: function select($selectable) {\n                var data = this.menu.getSelectableData($selectable);\n                if (data && !this.eventBus.before("select", data.obj)) {\n                    this.input.setQuery(data.val, true);\n                    this.eventBus.trigger("select", data.obj);\n                    this.close();\n                    return true;\n                }\n                return false;\n            },\n            autocomplete: function autocomplete($selectable) {\n                var query, data, isValid;\n                query = this.input.getQuery();\n                data = this.menu.getSelectableData($selectable);\n                isValid = data && query !== data.val;\n                if (isValid && !this.eventBus.before("autocomplete", data.obj)) {\n                    this.input.setQuery(data.val);\n                    this.eventBus.trigger("autocomplete", data.obj);\n                    return true;\n                }\n                return false;\n            },\n            moveCursor: function moveCursor(delta) {\n                var query, $candidate, data, payload, cancelMove;\n                query = this.input.getQuery();\n                $candidate = this.menu.selectableRelativeToCursor(delta);\n                data = this.menu.getSelectableData($candidate);\n                payload = data ? data.obj : null;\n                cancelMove = this._minLengthMet() && this.menu.update(query);\n                if (!cancelMove && !this.eventBus.before("cursorchange", payload)) {\n                    this.menu.setCursor($candidate);\n                    if (data) {\n                        this.input.setInputValue(data.val);\n                    } else {\n                        this.input.resetInputValue();\n                        this._updateHint();\n                    }\n                    this.eventBus.trigger("cursorchange", payload);\n                    return true;\n                }\n                return false;\n            },\n            destroy: function destroy() {\n                this.input.destroy();\n                this.menu.destroy();\n            }\n        });\n        return Typeahead;\n        function c(ctx) {\n            var methods = [].slice.call(arguments, 1);\n            return function() {\n                var args = [].slice.call(arguments);\n                _.each(methods, function(method) {\n                    return ctx[method].apply(ctx, args);\n                });\n            };\n        }\n    }();\n    (function() {\n        "use strict";\n        var old, keys, methods;\n        old = $.fn.typeahead;\n        keys = {\n            www: "tt-www",\n            attrs: "tt-attrs",\n            typeahead: "tt-typeahead"\n        };\n        methods = {\n            initialize: function initialize(o, datasets) {\n                var www;\n                datasets = _.isArray(datasets) ? datasets : [].slice.call(arguments, 1);\n                o = o || {};\n                www = WWW(o.classNames);\n                return this.each(attach);\n                function attach() {\n                    var $input, $wrapper, $hint, $menu, defaultHint, defaultMenu, eventBus, input, menu, typeahead, MenuConstructor;\n                    _.each(datasets, function(d) {\n                        d.highlight = !!o.highlight;\n                    });\n                    $input = $(this);\n                    $wrapper = $(www.html.wrapper);\n                    $hint = $elOrNull(o.hint);\n                    $menu = $elOrNull(o.menu);\n                    defaultHint = o.hint !== false && !$hint;\n                    defaultMenu = o.menu !== false && !$menu;\n                    defaultHint && ($hint = buildHintFromInput($input, www));\n                    defaultMenu && ($menu = $(www.html.menu).css(www.css.menu));\n                    $hint && $hint.val("");\n                    $input = prepInput($input, www);\n                    if (defaultHint || defaultMenu) {\n                        $wrapper.css(www.css.wrapper);\n                        $input.css(defaultHint ? www.css.input : www.css.inputWithNoHint);\n                        $input.wrap($wrapper).parent().prepend(defaultHint ? $hint : null).append(defaultMenu ? $menu : null);\n                    }\n                    MenuConstructor = defaultMenu ? DefaultMenu : Menu;\n                    eventBus = new EventBus({\n                        el: $input\n                    });\n                    input = new Input({\n                        hint: $hint,\n                        input: $input\n                    }, www);\n                    menu = new MenuConstructor({\n                        node: $menu,\n                        datasets: datasets\n                    }, www);\n                    typeahead = new Typeahead({\n                        input: input,\n                        menu: menu,\n                        eventBus: eventBus,\n                        minLength: o.minLength\n                    }, www);\n                    $input.data(keys.www, www);\n                    $input.data(keys.typeahead, typeahead);\n                }\n            },\n            isEnabled: function isEnabled() {\n                var enabled;\n                ttEach(this.first(), function(t) {\n                    enabled = t.isEnabled();\n                });\n                return enabled;\n            },\n            enable: function enable() {\n                ttEach(this, function(t) {\n                    t.enable();\n                });\n                return this;\n            },\n            disable: function disable() {\n                ttEach(this, function(t) {\n                    t.disable();\n                });\n                return this;\n            },\n            isActive: function isActive() {\n                var active;\n                ttEach(this.first(), function(t) {\n                    active = t.isActive();\n                });\n                return active;\n            },\n            activate: function activate() {\n                ttEach(this, function(t) {\n                    t.activate();\n                });\n                return this;\n            },\n            deactivate: function deactivate() {\n                ttEach(this, function(t) {\n                    t.deactivate();\n                });\n                return this;\n            },\n            isOpen: function isOpen() {\n                var open;\n                ttEach(this.first(), function(t) {\n                    open = t.isOpen();\n                });\n                return open;\n            },\n            open: function open() {\n                ttEach(this, function(t) {\n                    t.open();\n                });\n                return this;\n            },\n            close: function close() {\n                ttEach(this, function(t) {\n                    t.close();\n                });\n                return this;\n            },\n            select: function select(el) {\n                var success = false, $el = $(el);\n                ttEach(this.first(), function(t) {\n                    success = t.select($el);\n                });\n                return success;\n            },\n            autocomplete: function autocomplete(el) {\n                var success = false, $el = $(el);\n                ttEach(this.first(), function(t) {\n                    success = t.autocomplete($el);\n                });\n                return success;\n            },\n            moveCursor: function moveCursoe(delta) {\n                var success = false;\n                ttEach(this.first(), function(t) {\n                    success = t.moveCursor(delta);\n                });\n                return success;\n            },\n            val: function val(newVal) {\n                var query;\n                if (!arguments.length) {\n                    ttEach(this.first(), function(t) {\n                        query = t.getVal();\n                    });\n                    return query;\n                } else {\n                    ttEach(this, function(t) {\n                        t.setVal(newVal);\n                    });\n                    return this;\n                }\n            },\n            destroy: function destroy() {\n                ttEach(this, function(typeahead, $input) {\n                    revert($input);\n                    typeahead.destroy();\n                });\n                return this;\n            }\n        };\n        $.fn.typeahead = function(method) {\n            if (methods[method]) {\n                return methods[method].apply(this, [].slice.call(arguments, 1));\n            } else {\n                return methods.initialize.apply(this, arguments);\n            }\n        };\n        $.fn.typeahead.noConflict = function noConflict() {\n            $.fn.typeahead = old;\n            return this;\n        };\n        function ttEach($els, fn) {\n            $els.each(function() {\n                var $input = $(this), typeahead;\n                (typeahead = $input.data(keys.typeahead)) && fn(typeahead, $input);\n            });\n        }\n        function buildHintFromInput($input, www) {\n            return $input.clone().addClass(www.classes.hint).removeData().css(www.css.hint).css(getBackgroundStyles($input)).prop("readonly", true).removeAttr("id name placeholder required").attr({\n                autocomplete: "off",\n                spellcheck: "false",\n                tabindex: -1\n            });\n        }\n        function prepInput($input, www) {\n            $input.data(keys.attrs, {\n                dir: $input.attr("dir"),\n                autocomplete: $input.attr("autocomplete"),\n                spellcheck: $input.attr("spellcheck"),\n                style: $input.attr("style")\n            });\n            $input.addClass(www.classes.input).attr({\n                autocomplete: "off",\n                spellcheck: false\n            });\n            try {\n                !$input.attr("dir") && $input.attr("dir", "auto");\n            } catch (e) {}\n            return $input;\n        }\n        function getBackgroundStyles($el) {\n            return {\n                backgroundAttachment: $el.css("background-attachment"),\n                backgroundClip: $el.css("background-clip"),\n                backgroundColor: $el.css("background-color"),\n                backgroundImage: $el.css("background-image"),\n                backgroundOrigin: $el.css("background-origin"),\n                backgroundPosition: $el.css("background-position"),\n                backgroundRepeat: $el.css("background-repeat"),\n                backgroundSize: $el.css("background-size")\n            };\n        }\n        function revert($input) {\n            var www, $wrapper;\n            www = $input.data(keys.www);\n            $wrapper = $input.parent().filter(www.selectors.wrapper);\n            _.each($input.data(keys.attrs), function(val, key) {\n                _.isUndefined(val) ? $input.removeAttr(key) : $input.attr(key, val);\n            });\n            $input.removeData(keys.typeahead).removeData(keys.www).removeData(keys.attr).removeClass(www.classes.input);\n            if ($wrapper.length) {\n                $input.detach().insertAfter($wrapper);\n                $wrapper.remove();\n            }\n        }\n        function $elOrNull(obj) {\n            var isValid, $el;\n            isValid = _.isJQuery(obj) || _.isElement(obj);\n            $el = isValid ? $(obj).first() : [];\n            return $el.length ? $el : null;\n        }\n    })();\n});\n\n//# sourceURL=webpack://Materialize/./node_modules/typeahead.js/dist/typeahead.bundle.js?')},jquery:function(n){"use strict";n.exports=__WEBPACK_EXTERNAL_MODULE_jquery__}},__webpack_module_cache__={};function __webpack_require__(n){var e=__webpack_module_cache__[n];if(void 0!==e)return e.exports;var t=__webpack_module_cache__[n]={exports:{}};return __webpack_modules__[n].call(t.exports,t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return __webpack_require__.d(e,{a:e}),e},__webpack_require__.d=function(n,e){for(var t in e)__webpack_require__.o(e,t)&&!__webpack_require__.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},__webpack_require__.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},__webpack_require__.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/typeahead-js/typeahead.js");return __webpack_exports__}()}));