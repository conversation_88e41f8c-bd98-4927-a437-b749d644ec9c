@import "../../scss/_bootstrap-extended/include";
@import "datatables.net-buttons-bs5/css/buttons.bootstrap5";

div.dt-container {
  .dt-button-collection {
    inline-size: auto;
  }

  div.dropdown-menu.dt-button-collection,
  div.dt-button-collection .dt-button:not(.dt-btn-split-drop) {
    min-inline-size: 8rem;
  }
  .dt-buttons {
    &.btn-group {
      .btn:not([class*="btn-outline-"]) {
        border-color: transparent;
      }
      &:not(.btn-group-vertical) > .btn-group:not(:last-child) > .btn,
      &:not(.btn-group-vertical) > :not(.btn-check) + .btn {
        @include border-radius($border-radius !important);
      }
    }
  }
}
div.dt-button-info {
  background-color: var(--#{$prefix}paper-bg);
}
