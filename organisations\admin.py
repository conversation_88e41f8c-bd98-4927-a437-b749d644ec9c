from django.contrib import admin
from .models import Organisation, MembreOrganisation


class MembreOrganisationInline(admin.TabularInline):
    """Interface inline pour gérer les membres d'une organisation"""
    model = MembreOrganisation
    extra = 1
    fields = ('utilisateur', 'role', 'actif')


@admin.register(Organisation)
class OrganisationAdmin(admin.ModelAdmin):
    """Interface d'administration pour les organisations"""

    list_display = (
        'nom',
        'type_abonnement',
        'get_nombre_utilisateurs',
        'actif',
        'date_creation'
    )

    list_filter = (
        'type_abonnement',
        'actif',
        'date_creation'
    )

    search_fields = (
        'nom',
        'email',
        'telephone'
    )

    readonly_fields = (
        'date_creation',
        'date_modification',
        'get_nombre_utilisateurs'
    )

    fieldsets = (
        ('Informations générales', {
            'fields': ('nom', 'adresse', 'telephone', 'email')
        }),
        ('Abonnement', {
            'fields': ('type_abonnement', 'actif')
        }),
        ('Dates', {
            'fields': ('date_creation', 'date_modification'),
            'classes': ('collapse',)
        }),
    )

    inlines = [MembreOrganisationInline]

    def get_nombre_utilisateurs(self, obj):
        return obj.get_nombre_utilisateurs()
    get_nombre_utilisateurs.short_description = "Nombre d'utilisateurs"


@admin.register(MembreOrganisation)
class MembreOrganisationAdmin(admin.ModelAdmin):
    """Interface d'administration pour les membres d'organisation"""

    list_display = (
        'utilisateur',
        'organisation',
        'role',
        'actif',
        'date_ajout'
    )

    list_filter = (
        'role',
        'actif',
        'organisation',
        'date_ajout'
    )

    search_fields = (
        'utilisateur__username',
        'utilisateur__first_name',
        'utilisateur__last_name',
        'organisation__nom'
    )

    readonly_fields = ('date_ajout',)
