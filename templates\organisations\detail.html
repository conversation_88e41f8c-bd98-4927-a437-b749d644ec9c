{% extends 'base.html' %}
{% load static %}

{% block title %}{{ organisation.nom }}{% endblock %}

{% block breadcrumb %}
<div class="row">
  <div class="col-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Accueil</a>
        </li>
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Organisations</a>
        </li>
        <li class="breadcrumb-item active">{{ organisation.nom }}</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <!-- En-tête de page -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h4 class="fw-bold py-3 mb-2">
          <i class="ti ti-building me-2"></i>{{ organisation.nom }}
        </h4>
        <p class="text-muted">
          Détails et gestion de l'organisation
        </p>
      </div>
      <div>
        <a href="{% url 'organisations:modifier' organisation.pk %}" class="btn btn-primary me-2">
          <i class="ti ti-pencil me-1"></i>Modifier
        </a>
        <a href="{% url 'organisations:liste' %}" class="btn btn-outline-secondary">
          <i class="ti ti-arrow-left me-1"></i>Retour
        </a>
      </div>
    </div>

    <div class="row">
      <!-- Informations principales -->
      <div class="col-xl-8 col-lg-7 col-md-7">
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="ti ti-info-circle me-2"></i>Informations générales
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-sm-3 text-muted">
                <strong>Nom :</strong>
              </div>
              <div class="col-sm-9">
                {{ organisation.nom }}
              </div>
            </div>
            <hr class="my-3">
            <div class="row">
              <div class="col-sm-3 text-muted">
                <strong>Adresse :</strong>
              </div>
              <div class="col-sm-9">
                {{ organisation.adresse|linebreaks }}
              </div>
            </div>
            <hr class="my-3">
            <div class="row">
              <div class="col-sm-3 text-muted">
                <strong>Téléphone :</strong>
              </div>
              <div class="col-sm-9">
                <a href="tel:{{ organisation.telephone }}" class="text-decoration-none">
                  <i class="ti ti-phone me-1"></i>{{ organisation.telephone }}
                </a>
              </div>
            </div>
            <hr class="my-3">
            <div class="row">
              <div class="col-sm-3 text-muted">
                <strong>Email :</strong>
              </div>
              <div class="col-sm-9">
                <a href="mailto:{{ organisation.email }}" class="text-decoration-none">
                  <i class="ti ti-mail me-1"></i>{{ organisation.email }}
                </a>
              </div>
            </div>
            <hr class="my-3">
            <div class="row">
              <div class="col-sm-3 text-muted">
                <strong>Abonnement :</strong>
              </div>
              <div class="col-sm-9">
                {% if organisation.type_abonnement == 'PREMIUM' %}
                  <span class="badge bg-label-success">
                    <i class="ti ti-crown ti-xs me-1"></i>Premium
                  </span>
                {% else %}
                  <span class="badge bg-label-secondary">
                    <i class="ti ti-gift ti-xs me-1"></i>Gratuit
                  </span>
                {% endif %}
              </div>
            </div>
            <hr class="my-3">
            <div class="row">
              <div class="col-sm-3 text-muted">
                <strong>Statut :</strong>
              </div>
              <div class="col-sm-9">
                {% if organisation.actif %}
                  <span class="badge bg-label-success">
                    <i class="ti ti-check ti-xs me-1"></i>Active
                  </span>
                {% else %}
                  <span class="badge bg-label-danger">
                    <i class="ti ti-x ti-xs me-1"></i>Inactive
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Membres de l'organisation -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="ti ti-users me-2"></i>Membres de l'organisation
            </h5>
            <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterMembreModal">
              <i class="ti ti-user-plus me-1"></i>Ajouter un membre
            </button>
          </div>
          
          {% if membres %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Utilisateur</th>
                    <th>Rôle</th>
                    <th>Date d'ajout</th>
                    <th>Statut</th>
                    <th class="text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for membre in membres %}
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar avatar-sm me-3">
                            <span class="avatar-initial rounded bg-label-primary">
                              {{ membre.utilisateur.first_name|first|default:membre.utilisateur.username|first|upper }}
                            </span>
                          </div>
                          <div>
                            <h6 class="mb-0">
                              {{ membre.utilisateur.get_full_name|default:membre.utilisateur.username }}
                            </h6>
                            <small class="text-muted">{{ membre.utilisateur.email }}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-label-info">{{ membre.get_role_display }}</span>
                      </td>
                      <td>
                        <small class="text-muted">{{ membre.date_ajout|date:"d/m/Y" }}</small>
                      </td>
                      <td>
                        {% if membre.actif %}
                          <span class="badge bg-label-success">
                            <i class="ti ti-check ti-xs me-1"></i>Actif
                          </span>
                        {% else %}
                          <span class="badge bg-label-danger">
                            <i class="ti ti-x ti-xs me-1"></i>Inactif
                          </span>
                        {% endif %}
                      </td>
                      <td class="text-center">
                        <div class="dropdown">
                          <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="ti ti-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">
                              <i class="ti ti-pencil me-1"></i>Modifier le rôle
                            </a>
                            {% if membre.actif %}
                              <a class="dropdown-item" href="#">
                                <i class="ti ti-user-off me-1"></i>Désactiver
                              </a>
                            {% else %}
                              <a class="dropdown-item" href="#">
                                <i class="ti ti-user-check me-1"></i>Activer
                              </a>
                            {% endif %}
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-danger" href="#">
                              <i class="ti ti-trash me-1"></i>Retirer
                            </a>
                          </div>
                        </div>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="card-body text-center py-5">
              <div class="mb-4">
                <i class="ti ti-users ti-48px text-muted"></i>
              </div>
              <h6 class="mb-2">Aucun membre</h6>
              <p class="text-muted mb-4">
                Cette organisation n'a pas encore de membres.
              </p>
              <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterMembreModal">
                <i class="ti ti-user-plus me-1"></i>Ajouter le premier membre
              </button>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Sidebar avec statistiques -->
      <div class="col-xl-4 col-lg-5 col-md-5">
        <!-- Statistiques -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="ti ti-chart-bar me-2"></i>Statistiques
            </h5>
          </div>
          <div class="card-body">
            <div class="d-flex align-items-center mb-3">
              <div class="avatar me-3">
                <span class="avatar-initial rounded bg-label-primary">
                  <i class="ti ti-users ti-md"></i>
                </span>
              </div>
              <div class="flex-grow-1">
                <span class="fw-medium d-block">Utilisateurs</span>
                <small class="text-muted">{{ organisation.get_nombre_utilisateurs }} membre{{ organisation.get_nombre_utilisateurs|pluralize }}</small>
              </div>
              <div class="text-end">
                <h5 class="mb-0">{{ organisation.get_nombre_utilisateurs }}</h5>
              </div>
            </div>

            <div class="d-flex align-items-center mb-3">
              <div class="avatar me-3">
                <span class="avatar-initial rounded bg-label-success">
                  <i class="ti ti-calendar ti-md"></i>
                </span>
              </div>
              <div class="flex-grow-1">
                <span class="fw-medium d-block">Créée le</span>
                <small class="text-muted">{{ organisation.date_creation|date:"d F Y" }}</small>
              </div>
            </div>

            <div class="d-flex align-items-center">
              <div class="avatar me-3">
                <span class="avatar-initial rounded bg-label-info">
                  <i class="ti ti-edit ti-md"></i>
                </span>
              </div>
              <div class="flex-grow-1">
                <span class="fw-medium d-block">Modifiée le</span>
                <small class="text-muted">{{ organisation.date_modification|date:"d F Y" }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions rapides -->
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="ti ti-bolt me-2"></i>Actions rapides
            </h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <a href="{% url 'organisations:modifier' organisation.pk %}" class="btn btn-outline-primary">
                <i class="ti ti-pencil me-1"></i>Modifier l'organisation
              </a>
              <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#ajouterMembreModal">
                <i class="ti ti-user-plus me-1"></i>Ajouter un membre
              </button>
              <button class="btn btn-outline-secondary">
                <i class="ti ti-download me-1"></i>Exporter les données
              </button>
              <div class="dropdown-divider"></div>
              <a href="{% url 'organisations:supprimer' organisation.pk %}" class="btn btn-outline-danger">
                <i class="ti ti-trash me-1"></i>Supprimer l'organisation
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour ajouter un membre -->
<div class="modal fade" id="ajouterMembreModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="ti ti-user-plus me-2"></i>Ajouter un membre
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="text-muted mb-4">
          Cette fonctionnalité sera disponible dans une prochaine version.
        </p>
        <div class="alert alert-info" role="alert">
          <i class="ti ti-info-circle me-1"></i>
          Pour l'instant, les membres peuvent être ajoutés via l'interface d'administration Django.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Fermer</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}
