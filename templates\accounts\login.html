<!doctype html>
<html lang="fr" class="layout-navbar-fixed layout-menu-fixed layout-compact" dir="ltr" data-skin="default" data-bs-theme="light">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <title>Connexion - Hospital SaaS</title>
    <meta name="description" content="Connectez-vous à votre compte Hospital SaaS" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap" rel="stylesheet" />

    <link rel="stylesheet" href="/static/vendor/fonts/iconify-icons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/node-waves/node-waves.css" />
    <link rel="stylesheet" href="/static/vendor/css/core.css" />
    <link rel="stylesheet" href="/static/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- Helpers -->
    <script src="/static/js/helpers.js"></script>
    <script src="/static/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        
        <!-- Content wrapper -->
        <div class="content-wrapper">
          <!-- Content -->
          <div class="container-xxl flex-grow-1 container-p-y">
            
            <!-- Header -->
            <div class="row justify-content-center">
              <div class="col-xl-4 col-lg-5 col-md-6">
                <div class="text-center mb-4">
                  <a href="{% url 'accounts:accueil' %}" class="text-decoration-none">
                    <i class="ti ti-building-hospital ti-48px text-primary mb-3"></i>
                    <h2 class="fw-bold text-primary">Hospital SaaS</h2>
                  </a>
                  <h3 class="h4 mb-2">Connexion</h3>
                  <p class="text-muted">
                    Connectez-vous à votre organisation
                  </p>
                </div>
              </div>
            </div>

            <!-- Messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="row justify-content-center mb-4">
                  <div class="col-xl-4 col-lg-5 col-md-6">
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                      {{ message }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            {% endif %}

            <!-- Formulaire de connexion -->
            <div class="row justify-content-center">
              <div class="col-xl-4 col-lg-5 col-md-6">
                <div class="card">
                  <div class="card-body p-4">
                    <form method="post" novalidate>
                      {% csrf_token %}
                      
                      <!-- Erreurs générales -->
                      {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                          <h6 class="alert-heading">
                            <i class="ti ti-alert-circle me-1"></i>Erreur de connexion
                          </h6>
                          {% for error in form.non_field_errors %}
                            <div>{{ error }}</div>
                          {% endfor %}
                        </div>
                      {% endif %}

                      <!-- Email -->
                      <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                          {{ form.email.label }} <span class="text-danger">*</span>
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                          <div class="invalid-feedback d-block">
                            {% for error in form.email.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                        {% if form.email.help_text %}
                          <div class="form-text">{{ form.email.help_text }}</div>
                        {% endif %}
                      </div>

                      <!-- Mot de passe -->
                      <div class="mb-3">
                        <label for="{{ form.mot_de_passe.id_for_label }}" class="form-label">
                          {{ form.mot_de_passe.label }} <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-merge">
                          {{ form.mot_de_passe }}
                          <span class="input-group-text cursor-pointer" id="password-toggle">
                            <i class="ti ti-eye-off" id="password-icon"></i>
                          </span>
                        </div>
                        {% if form.mot_de_passe.errors %}
                          <div class="invalid-feedback d-block">
                            {% for error in form.mot_de_passe.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                      </div>

                      <!-- Se souvenir de moi -->
                      <div class="mb-3">
                        <div class="form-check">
                          {{ form.se_souvenir }}
                          <label class="form-check-label" for="{{ form.se_souvenir.id_for_label }}">
                            {{ form.se_souvenir.label }}
                          </label>
                        </div>
                        {% if form.se_souvenir.help_text %}
                          <div class="form-text">{{ form.se_souvenir.help_text }}</div>
                        {% endif %}
                      </div>

                      <!-- Bouton de connexion -->
                      <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary">
                          <i class="ti ti-login me-1"></i>Se connecter
                        </button>
                      </div>

                      <!-- Liens utiles -->
                      <div class="text-center">
                        <small class="text-muted">
                          Pas encore de compte ? 
                          <a href="{% url 'accounts:onboarding' %}" class="text-decoration-none">
                            Demander l'inscription
                          </a>
                        </small>
                      </div>
                    </form>
                  </div>
                </div>

                <!-- Informations supplémentaires -->
                <div class="card mt-4">
                  <div class="card-body text-center">
                    <h6 class="card-title">
                      <i class="ti ti-help me-2 text-primary"></i>Besoin d'aide ?
                    </h6>
                    <p class="card-text text-muted mb-3">
                      Problème de connexion ou compte non activé ?
                    </p>
                    <div class="d-flex justify-content-center gap-2">
                      <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                        <i class="ti ti-mail me-1"></i>Support
                      </a>
                      <a href="{% url 'accounts:accueil' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="ti ti-home me-1"></i>Accueil
                      </a>
                    </div>
                  </div>
                </div>

                <!-- Informations de sécurité -->
                <div class="alert alert-info mt-4" role="alert">
                  <h6 class="alert-heading">
                    <i class="ti ti-shield-check me-1"></i>Sécurité
                  </h6>
                  <ul class="mb-0 small">
                    <li>Vos données sont chiffrées et sécurisées</li>
                    <li>Chaque organisation a accès uniquement à ses données</li>
                    <li>Déconnexion automatique après inactivité</li>
                  </ul>
                </div>

              </div>
            </div>

          </div>
          <!-- / Content -->

          <!-- Footer -->
          <footer class="content-footer footer bg-footer-theme">
            <div class="container-xxl">
              <div class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                <div class="text-body">
                  © 2025 Hospital SaaS. Tous droits réservés.
                </div>
                <div class="d-none d-lg-inline-block">
                  <a href="#" class="footer-link me-4">Support</a>
                  <a href="#" class="footer-link me-4">Documentation</a>
                  <a href="#" class="footer-link">Contact</a>
                </div>
              </div>
            </div>
          </footer>
          <!-- / Footer -->

        </div>
        <!-- Content wrapper -->
      </div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <script src="/static/vendor/libs/jquery/jquery.js"></script>
    <script src="/static/vendor/libs/popper/popper.js"></script>
    <script src="/static/js/bootstrap.js"></script>
    <script src="/static/vendor/libs/node-waves/node-waves.js"></script>
    <script src="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="/static/vendor/libs/hammer/hammer.js"></script>
    <script src="/static/js/menu.js"></script>

    <!-- Main JS -->
    <script src="/static/js/main.js"></script>

    <!-- Page JS -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const passwordField = document.getElementById('{{ form.mot_de_passe.id_for_label }}');
        const passwordToggle = document.getElementById('password-toggle');
        const passwordIcon = document.getElementById('password-icon');
        
        if (passwordToggle && passwordField && passwordIcon) {
            passwordToggle.addEventListener('click', function() {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    passwordIcon.classList.remove('ti-eye-off');
                    passwordIcon.classList.add('ti-eye');
                } else {
                    passwordField.type = 'password';
                    passwordIcon.classList.remove('ti-eye');
                    passwordIcon.classList.add('ti-eye-off');
                }
            });
        }
        
        // Auto-focus sur le premier champ avec erreur
        const firstErrorField = document.querySelector('.is-invalid');
        if (firstErrorField) {
            firstErrorField.focus();
        }
        
        // Validation en temps réel de l'email
        const emailField = document.getElementById('{{ form.email.id_for_label }}');
        if (emailField) {
            emailField.addEventListener('blur', function() {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (this.value && !emailRegex.test(this.value)) {
                    this.classList.add('is-invalid');
                } else if (this.value) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        }
    });
    </script>
  </body>
</html>
