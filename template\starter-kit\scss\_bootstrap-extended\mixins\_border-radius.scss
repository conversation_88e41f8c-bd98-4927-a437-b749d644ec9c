// stylelint-disable property-disallowed-list

// Single side border-radius

@mixin border-end-radius($radius: $border-radius) {
  @if $enable-rounded {
    border-end-end-radius: valid-radius($radius);
    border-start-end-radius: valid-radius($radius);
  }
}

@mixin border-start-radius($radius: $border-radius) {
  @if $enable-rounded {
    border-end-start-radius: valid-radius($radius);
    border-start-start-radius: valid-radius($radius);
  }
}

@mixin border-top-start-radius($radius: $border-radius) {
  @if $enable-rounded {
    border-start-start-radius: valid-radius($radius);
  }
}

@mixin border-top-end-radius($radius: $border-radius) {
  @if $enable-rounded {
    border-start-end-radius: valid-radius($radius);
  }
}

@mixin border-bottom-end-radius($radius: $border-radius) {
  @if $enable-rounded {
    border-end-end-radius: valid-radius($radius);
  }
}

@mixin border-bottom-start-radius($radius: $border-radius) {
  @if $enable-rounded {
    border-end-start-radius: valid-radius($radius);
  }
}

// scss-docs-end border-radius-mixins
