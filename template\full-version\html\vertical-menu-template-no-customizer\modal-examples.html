<!doctype html>

<html
  lang="en"
  class="layout-navbar-fixed layout-menu-fixed layout-compact"
  dir="ltr"
  data-skin="default"
  data-bs-theme="light"
  data-assets-path="../../assets/"
  data-template="vertical-menu-template-no-customizer">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <meta name="robots" content="noindex, nofollow" />
    <title>Demo: Modals - UI elements | Materialize - Bootstrap Dashboard PRO</title>

    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"
      rel="stylesheet" />

    <link rel="stylesheet" href="../../assets/vendor/fonts/iconify-icons.css" />

    <!-- Core CSS -->
    <!-- build:css assets/vendor/css/theme.css -->

    <link rel="stylesheet" href="../../assets/vendor/libs/node-waves/node-waves.css" />

    <link rel="stylesheet" href="../../assets/vendor/css/core.css" />
    <link rel="stylesheet" href="../../assets/css/demo.css" />

    <!-- Vendors CSS -->

    <link rel="stylesheet" href="../../assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- endbuild -->

    <link rel="stylesheet" href="../../assets/vendor/libs/select2/select2.css" />
    <link rel="stylesheet" href="../../assets/vendor/libs/tagify/tagify.css" />
    <link rel="stylesheet" href="../../assets/vendor/libs/@form-validation/form-validation.css" />
    <link rel="stylesheet" href="../../assets/vendor/libs/bs-stepper/bs-stepper.css" />

    <!-- Page CSS -->

    <!-- Helpers -->
    <script src="../../assets/vendor/js/helpers.js"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Config: Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file. -->

    <script src="../../assets/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        <!-- Menu -->

        <aside id="layout-menu" class="layout-menu menu-vertical menu">
          <div class="app-brand demo">
            <a href="index.html" class="app-brand-link">
              <span class="app-brand-logo demo">
                <span class="text-primary">
                  <svg width="32" height="18" viewBox="0 0 38 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M30.0944 2.22569C29.0511 0.444187 26.7508 -0.172113 24.9566 0.849138C23.1623 1.87039 22.5536 4.14247 23.5969 5.92397L30.5368 17.7743C31.5801 19.5558 33.8804 20.1721 35.6746 19.1509C37.4689 18.1296 38.0776 15.8575 37.0343 14.076L30.0944 2.22569Z"
                      fill="currentColor" />
                    <path
                      d="M30.171 2.22569C29.1277 0.444187 26.8274 -0.172113 25.0332 0.849138C23.2389 1.87039 22.6302 4.14247 23.6735 5.92397L30.6134 17.7743C31.6567 19.5558 33.957 20.1721 35.7512 19.1509C37.5455 18.1296 38.1542 15.8575 37.1109 14.076L30.171 2.22569Z"
                      fill="url(#paint0_linear_2989_100980)"
                      fill-opacity="0.4" />
                    <path
                      d="M22.9676 2.22569C24.0109 0.444187 26.3112 -0.172113 28.1054 0.849138C29.8996 1.87039 30.5084 4.14247 29.4651 5.92397L22.5251 17.7743C21.4818 19.5558 19.1816 20.1721 17.3873 19.1509C15.5931 18.1296 14.9843 15.8575 16.0276 14.076L22.9676 2.22569Z"
                      fill="currentColor" />
                    <path
                      d="M14.9558 2.22569C13.9125 0.444187 11.6122 -0.172113 9.818 0.849138C8.02377 1.87039 7.41502 4.14247 8.45833 5.92397L15.3983 17.7743C16.4416 19.5558 18.7418 20.1721 20.5361 19.1509C22.3303 18.1296 22.9391 15.8575 21.8958 14.076L14.9558 2.22569Z"
                      fill="currentColor" />
                    <path
                      d="M14.9558 2.22569C13.9125 0.444187 11.6122 -0.172113 9.818 0.849138C8.02377 1.87039 7.41502 4.14247 8.45833 5.92397L15.3983 17.7743C16.4416 19.5558 18.7418 20.1721 20.5361 19.1509C22.3303 18.1296 22.9391 15.8575 21.8958 14.076L14.9558 2.22569Z"
                      fill="url(#paint1_linear_2989_100980)"
                      fill-opacity="0.4" />
                    <path
                      d="M7.82901 2.22569C8.87231 0.444187 11.1726 -0.172113 12.9668 0.849138C14.7611 1.87039 15.3698 4.14247 14.3265 5.92397L7.38656 17.7743C6.34325 19.5558 4.04298 20.1721 2.24875 19.1509C0.454514 18.1296 -0.154233 15.8575 0.88907 14.076L7.82901 2.22569Z"
                      fill="currentColor" />
                    <defs>
                      <linearGradient
                        id="paint0_linear_2989_100980"
                        x1="5.36642"
                        y1="0.849138"
                        x2="10.532"
                        y2="24.104"
                        gradientUnits="userSpaceOnUse">
                        <stop offset="0" stop-opacity="1" />
                        <stop offset="1" stop-opacity="0" />
                      </linearGradient>
                      <linearGradient
                        id="paint1_linear_2989_100980"
                        x1="5.19475"
                        y1="0.849139"
                        x2="10.3357"
                        y2="24.1155"
                        gradientUnits="userSpaceOnUse">
                        <stop offset="0" stop-opacity="1" />
                        <stop offset="1" stop-opacity="0" />
                      </linearGradient>
                    </defs>
                  </svg>
                </span>
              </span>
              <span class="app-brand-text demo menu-text fw-semibold ms-2">Materialize</span>
            </a>

            <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M8.47365 11.7183C8.11707 12.0749 8.11707 12.6531 8.47365 13.0097L12.071 16.607C12.4615 16.9975 12.4615 17.6305 12.071 18.021C11.6805 18.4115 11.0475 18.4115 10.657 18.021L5.83009 13.1941C5.37164 12.7356 5.37164 11.9924 5.83009 11.5339L10.657 6.707C11.0475 6.31653 11.6805 6.31653 12.071 6.707C12.4615 7.09747 12.4615 7.73053 12.071 8.121L8.47365 11.7183Z"
                  fill-opacity="0.9" />
                <path
                  d="M14.3584 11.8336C14.0654 12.1266 14.0654 12.6014 14.3584 12.8944L18.071 16.607C18.4615 16.9975 18.4615 17.6305 18.071 18.021C17.6805 18.4115 17.0475 18.4115 16.657 18.021L11.6819 13.0459C11.3053 12.6693 11.3053 12.0587 11.6819 11.6821L16.657 6.707C17.0475 6.31653 17.6805 6.31653 18.071 6.707C18.4615 7.09747 18.4615 7.73053 18.071 8.121L14.3584 11.8336Z"
                  fill-opacity="0.4" />
              </svg>
            </a>
          </div>

          <div class="menu-inner-shadow"></div>

          <ul class="menu-inner py-1">
            <!-- Dashboards -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-home-smile-line"></i>
                <div data-i18n="Dashboards">Dashboards</div>
                <div class="badge badge-center text-bg-danger rounded-pill ms-auto">5</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-ecommerce-dashboard.html" class="menu-link">
                    <div data-i18n="eCommerce">eCommerce</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="dashboards-crm.html" class="menu-link">
                    <div data-i18n="CRM">CRM</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="index.html" class="menu-link">
                    <div data-i18n="Analytics">Analytics</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-logistics-dashboard.html" class="menu-link">
                    <div data-i18n="Logistics">Logistics</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-academy-dashboard.html" class="menu-link">
                    <div data-i18n="Academy">Academy</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Layouts -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-layout-2-line"></i>
                <div data-i18n="Layouts">Layouts</div>
              </a>

              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="layouts-collapsed-menu.html" class="menu-link">
                    <div data-i18n="Collapsed menu">Collapsed menu</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-content-navbar.html" class="menu-link">
                    <div data-i18n="Content navbar">Content navbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-content-navbar-with-sidebar.html" class="menu-link">
                    <div data-i18n="Content nav + Sidebar">Content nav + Sidebar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="../horizontal-menu-template/" class="menu-link" target="_blank">
                    <div data-i18n="Horizontal">Horizontal</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-without-menu.html" class="menu-link">
                    <div data-i18n="Without menu">Without menu</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-without-navbar.html" class="menu-link">
                    <div data-i18n="Without navbar">Without navbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-fluid.html" class="menu-link">
                    <div data-i18n="Fluid">Fluid</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-container.html" class="menu-link">
                    <div data-i18n="Container">Container</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-blank.html" class="menu-link">
                    <div data-i18n="Blank">Blank</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Front Pages -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-file-copy-line"></i>
                <div data-i18n="Front Pages">Front Pages</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="../front-pages/landing-page.html" class="menu-link" target="_blank">
                    <div data-i18n="Landing">Landing</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="../front-pages/pricing-page.html" class="menu-link" target="_blank">
                    <div data-i18n="Pricing">Pricing</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="../front-pages/payment-page.html" class="menu-link" target="_blank">
                    <div data-i18n="Payment">Payment</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="../front-pages/checkout-page.html" class="menu-link" target="_blank">
                    <div data-i18n="Checkout">Checkout</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="../front-pages/help-center-landing.html" class="menu-link" target="_blank">
                    <div data-i18n="Help Center">Help Center</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Apps & Pages -->
            <li class="menu-header small mt-5">
              <span class="menu-header-text" data-i18n="Apps & Pages">Apps &amp; Pages</span>
            </li>
            <li class="menu-item">
              <a href="app-email.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-mail-open-line"></i>
                <div data-i18n="Email">Email</div>
              </a>
            </li>
            <li class="menu-item">
              <a href="app-chat.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-wechat-line"></i>
                <div data-i18n="Chat">Chat</div>
              </a>
            </li>
            <li class="menu-item">
              <a href="app-calendar.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-calendar-line"></i>
                <div data-i18n="Calendar">Calendar</div>
              </a>
            </li>
            <li class="menu-item">
              <a href="app-kanban.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-drag-drop-line"></i>
                <div data-i18n="Kanban">Kanban</div>
              </a>
            </li>
            <!-- e-commerce-app menu start -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-shopping-bag-3-line"></i>
                <div data-i18n="eCommerce">eCommerce</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-ecommerce-dashboard.html" class="menu-link">
                    <div data-i18n="Dashboard">Dashboard</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Products">Products</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="app-ecommerce-product-list.html" class="menu-link">
                        <div data-i18n="Product List">Product List</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-product-add.html" class="menu-link">
                        <div data-i18n="Add Product">Add Product</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-category-list.html" class="menu-link">
                        <div data-i18n="Category List">Category List</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Order">Order</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="app-ecommerce-order-list.html" class="menu-link">
                        <div data-i18n="Order List">Order List</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-order-details.html" class="menu-link">
                        <div data-i18n="Order Details">Order Details</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Customer">Customer</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="app-ecommerce-customer-all.html" class="menu-link">
                        <div data-i18n="All Customers">All Customers</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Customer Details">Customer Details</div>
                      </a>
                      <ul class="menu-sub">
                        <li class="menu-item">
                          <a href="app-ecommerce-customer-details-overview.html" class="menu-link">
                            <div data-i18n="Overview">Overview</div>
                          </a>
                        </li>
                        <li class="menu-item">
                          <a href="app-ecommerce-customer-details-security.html" class="menu-link">
                            <div data-i18n="Security">Security</div>
                          </a>
                        </li>
                        <li class="menu-item">
                          <a href="app-ecommerce-customer-details-billing.html" class="menu-link">
                            <div data-i18n="Address & Billing">Address & Billing</div>
                          </a>
                        </li>
                        <li class="menu-item">
                          <a href="app-ecommerce-customer-details-notifications.html" class="menu-link">
                            <div data-i18n="Notifications">Notifications</div>
                          </a>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="app-ecommerce-manage-reviews.html" class="menu-link">
                    <div data-i18n="Manage Reviews">Manage Reviews</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-ecommerce-referral.html" class="menu-link">
                    <div data-i18n="Referrals">Referrals</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Settings">Settings</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="app-ecommerce-settings-detail.html" class="menu-link">
                        <div data-i18n="Store Details">Store Details</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-settings-payments.html" class="menu-link">
                        <div data-i18n="Payments">Payments</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-settings-checkout.html" class="menu-link">
                        <div data-i18n="Checkout">Checkout</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-settings-shipping.html" class="menu-link">
                        <div data-i18n="Shipping & Delivery">Shipping & Delivery</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-settings-locations.html" class="menu-link">
                        <div data-i18n="Locations">Locations</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-ecommerce-settings-notifications.html" class="menu-link">
                        <div data-i18n="Notifications">Notifications</div>
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
            <!-- e-commerce-app menu end -->
            <!-- Academy menu start -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-graduation-cap-line"></i>
                <div data-i18n="Academy">Academy</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-academy-dashboard.html" class="menu-link">
                    <div data-i18n="Dashboard">Dashboard</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-academy-course.html" class="menu-link">
                    <div data-i18n="My Course">My Course</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-academy-course-details.html" class="menu-link">
                    <div data-i18n="Course Details">Course Details</div>
                  </a>
                </li>
              </ul>
            </li>
            <!-- Academy menu end -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-car-line"></i>
                <div data-i18n="Logistics">Logistics</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-logistics-dashboard.html" class="menu-link">
                    <div data-i18n="Dashboard">Dashboard</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-logistics-fleet.html" class="menu-link">
                    <div data-i18n="Fleet">Fleet</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-bill-line"></i>
                <div data-i18n="Invoice">Invoice</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-invoice-list.html" class="menu-link">
                    <div data-i18n="List">List</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-invoice-preview.html" class="menu-link">
                    <div data-i18n="Preview">Preview</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-invoice-edit.html" class="menu-link">
                    <div data-i18n="Edit">Edit</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-invoice-add.html" class="menu-link">
                    <div data-i18n="Add">Add</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-user-line"></i>
                <div data-i18n="Users">Users</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-user-list.html" class="menu-link">
                    <div data-i18n="List">List</div>
                  </a>
                </li>

                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="View">View</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="app-user-view-account.html" class="menu-link">
                        <div data-i18n="Account">Account</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-user-view-security.html" class="menu-link">
                        <div data-i18n="Security">Security</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-user-view-billing.html" class="menu-link">
                        <div data-i18n="Billing & Plans">Billing & Plans</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-user-view-notifications.html" class="menu-link">
                        <div data-i18n="Notifications">Notifications</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="app-user-view-connections.html" class="menu-link">
                        <div data-i18n="Connections">Connections</div>
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-lock-2-line"></i>
                <div data-i18n="Roles & Permissions">Roles & Permissions</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="app-access-roles.html" class="menu-link">
                    <div data-i18n="Roles">Roles</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="app-access-permission.html" class="menu-link">
                    <div data-i18n="Permission">Permission</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-layout-left-line"></i>
                <div data-i18n="Pages">Pages</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="User Profile">User Profile</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="pages-profile-user.html" class="menu-link">
                        <div data-i18n="Profile">Profile</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-profile-teams.html" class="menu-link">
                        <div data-i18n="Teams">Teams</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-profile-projects.html" class="menu-link">
                        <div data-i18n="Projects">Projects</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-profile-connections.html" class="menu-link">
                        <div data-i18n="Connections">Connections</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Account Settings">Account Settings</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="pages-account-settings-account.html" class="menu-link">
                        <div data-i18n="Account">Account</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-account-settings-security.html" class="menu-link">
                        <div data-i18n="Security">Security</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-account-settings-billing.html" class="menu-link">
                        <div data-i18n="Billing & Plans">Billing & Plans</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-account-settings-notifications.html" class="menu-link">
                        <div data-i18n="Notifications">Notifications</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-account-settings-connections.html" class="menu-link">
                        <div data-i18n="Connections">Connections</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="pages-faq.html" class="menu-link">
                    <div data-i18n="FAQ">FAQ</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="pages-pricing.html" class="menu-link">
                    <div data-i18n="Pricing">Pricing</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Misc">Misc</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="pages-misc-error.html" class="menu-link" target="_blank">
                        <div data-i18n="Error">Error</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-misc-under-maintenance.html" class="menu-link" target="_blank">
                        <div data-i18n="Under Maintenance">Under Maintenance</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-misc-comingsoon.html" class="menu-link" target="_blank">
                        <div data-i18n="Coming Soon">Coming Soon</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-misc-not-authorized.html" class="menu-link" target="_blank">
                        <div data-i18n="Not Authorized">Not Authorized</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="pages-misc-server-error.html" class="menu-link" target="_blank">
                        <div data-i18n="Server Error">Server Error</div>
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-shield-keyhole-line"></i>
                <div data-i18n="Authentications">Authentications</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Login">Login</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="auth-login-basic.html" class="menu-link" target="_blank">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-login-cover.html" class="menu-link" target="_blank">
                        <div data-i18n="Cover">Cover</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Register">Register</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="auth-register-basic.html" class="menu-link" target="_blank">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-register-cover.html" class="menu-link" target="_blank">
                        <div data-i18n="Cover">Cover</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-register-multisteps.html" class="menu-link" target="_blank">
                        <div data-i18n="Multi-steps">Multi-steps</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Verify Email">Verify Email</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="auth-verify-email-basic.html" class="menu-link" target="_blank">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-verify-email-cover.html" class="menu-link" target="_blank">
                        <div data-i18n="Cover">Cover</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Reset Password">Reset Password</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="auth-reset-password-basic.html" class="menu-link" target="_blank">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-reset-password-cover.html" class="menu-link" target="_blank">
                        <div data-i18n="Cover">Cover</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Forgot Password">Forgot Password</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="auth-forgot-password-basic.html" class="menu-link" target="_blank">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-forgot-password-cover.html" class="menu-link" target="_blank">
                        <div data-i18n="Cover">Cover</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Two Steps">Two Steps</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="auth-two-steps-basic.html" class="menu-link" target="_blank">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="auth-two-steps-cover.html" class="menu-link" target="_blank">
                        <div data-i18n="Cover">Cover</div>
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-git-commit-line"></i>
                <div data-i18n="Wizard Examples">Wizard Examples</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="wizard-ex-checkout.html" class="menu-link">
                    <div data-i18n="Checkout">Checkout</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="wizard-ex-property-listing.html" class="menu-link">
                    <div data-i18n="Property Listing">Property Listing</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="wizard-ex-create-deal.html" class="menu-link">
                    <div data-i18n="Create Deal">Create Deal</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item active">
              <a href="modal-examples.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-tv-2-line"></i>
                <div data-i18n="Modal Examples">Modal Examples</div>
              </a>
            </li>

            <!-- Components -->
            <li class="menu-header small mt-5">
              <span class="menu-header-text" data-i18n="Components">Components</span>
            </li>
            <!-- Cards -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-bank-card-2-line"></i>
                <div data-i18n="Cards">Cards</div>
                <div class="badge badge-center text-bg-primary rounded-pill ms-auto">6</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="cards-basic.html" class="menu-link">
                    <div data-i18n="Basic">Basic</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="cards-advance.html" class="menu-link">
                    <div data-i18n="Advance">Advance</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="cards-statistics.html" class="menu-link">
                    <div data-i18n="Statistics">Statistics</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="cards-analytics.html" class="menu-link">
                    <div data-i18n="Analytics">Analytics</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="cards-gamifications.html" class="menu-link">
                    <div data-i18n="Gamifications">Gamifications</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="cards-actions.html" class="menu-link">
                    <div data-i18n="Actions">Actions</div>
                  </a>
                </li>
              </ul>
            </li>
            <!-- User interface -->
            <li class="menu-item">
              <a href="javascript:void(0)" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-toggle-line"></i>
                <div data-i18n="User interface">User interface</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="ui-accordion.html" class="menu-link">
                    <div data-i18n="Accordion">Accordion</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-alerts.html" class="menu-link">
                    <div data-i18n="Alerts">Alerts</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-badges.html" class="menu-link">
                    <div data-i18n="Badges">Badges</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-buttons.html" class="menu-link">
                    <div data-i18n="Buttons">Buttons</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-carousel.html" class="menu-link">
                    <div data-i18n="Carousel">Carousel</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-collapse.html" class="menu-link">
                    <div data-i18n="Collapse">Collapse</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-dropdowns.html" class="menu-link">
                    <div data-i18n="Dropdowns">Dropdowns</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-footer.html" class="menu-link">
                    <div data-i18n="Footer">Footer</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-list-groups.html" class="menu-link">
                    <div data-i18n="List Groups">List Groups</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-modals.html" class="menu-link">
                    <div data-i18n="Modals">Modals</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-navbar.html" class="menu-link">
                    <div data-i18n="Navbar">Navbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-offcanvas.html" class="menu-link">
                    <div data-i18n="Offcanvas">Offcanvas</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-pagination-breadcrumbs.html" class="menu-link">
                    <div data-i18n="Pagination & Breadcrumbs">Pagination &amp; Breadcrumbs</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-progress.html" class="menu-link">
                    <div data-i18n="Progress">Progress</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-spinners.html" class="menu-link">
                    <div data-i18n="Spinners">Spinners</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-tabs-pills.html" class="menu-link">
                    <div data-i18n="Tabs & Pills">Tabs &amp; Pills</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-toasts.html" class="menu-link">
                    <div data-i18n="Toasts">Toasts</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-tooltips-popovers.html" class="menu-link">
                    <div data-i18n="Tooltips & Popovers">Tooltips &amp; Popovers</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-typography.html" class="menu-link">
                    <div data-i18n="Typography">Typography</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Extended components -->
            <li class="menu-item">
              <a href="javascript:void(0)" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-box-3-line"></i>
                <div data-i18n="Extended UI">Extended UI</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="extended-ui-avatar.html" class="menu-link">
                    <div data-i18n="Avatar">Avatar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-blockui.html" class="menu-link">
                    <div data-i18n="BlockUI">BlockUI</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-drag-and-drop.html" class="menu-link">
                    <div data-i18n="Drag & Drop">Drag &amp; Drop</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-media-player.html" class="menu-link">
                    <div data-i18n="Media Player">Media Player</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-perfect-scrollbar.html" class="menu-link">
                    <div data-i18n="Perfect Scrollbar">Perfect Scrollbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-star-ratings.html" class="menu-link">
                    <div data-i18n="Star Ratings">Star Ratings</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-sweetalert2.html" class="menu-link">
                    <div data-i18n="SweetAlert2">SweetAlert2</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-text-divider.html" class="menu-link">
                    <div data-i18n="Text Divider">Text Divider</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Timeline">Timeline</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="extended-ui-timeline-basic.html" class="menu-link">
                        <div data-i18n="Basic">Basic</div>
                      </a>
                    </li>
                    <li class="menu-item">
                      <a href="extended-ui-timeline-fullscreen.html" class="menu-link">
                        <div data-i18n="Fullscreen">Fullscreen</div>
                      </a>
                    </li>
                  </ul>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-tour.html" class="menu-link">
                    <div data-i18n="Tour">Tour</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-treeview.html" class="menu-link">
                    <div data-i18n="Treeview">Treeview</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-misc.html" class="menu-link">
                    <div data-i18n="Miscellaneous">Miscellaneous</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Icons -->
            <li class="menu-item">
              <a href="icons-ri.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-remixicon-line"></i>
                <div data-i18n="Icons">Icons</div>
              </a>
            </li>

            <!-- Forms & Tables -->
            <li class="menu-header small mt-5">
              <span class="menu-header-text" data-i18n="Forms & Tables">Forms &amp; Tables</span>
            </li>
            <!-- Forms -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-radio-button-line"></i>
                <div data-i18n="Form Elements">Form Elements</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="forms-basic-inputs.html" class="menu-link">
                    <div data-i18n="Basic Inputs">Basic Inputs</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-input-groups.html" class="menu-link">
                    <div data-i18n="Input groups">Input groups</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-custom-options.html" class="menu-link">
                    <div data-i18n="Custom Options">Custom Options</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-editors.html" class="menu-link">
                    <div data-i18n="Editors">Editors</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-file-upload.html" class="menu-link">
                    <div data-i18n="File Upload">File Upload</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-pickers.html" class="menu-link">
                    <div data-i18n="Pickers">Pickers</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-selects.html" class="menu-link">
                    <div data-i18n="Select & Tags">Select &amp; Tags</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-sliders.html" class="menu-link">
                    <div data-i18n="Sliders">Sliders</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-switches.html" class="menu-link">
                    <div data-i18n="Switches">Switches</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-extras.html" class="menu-link">
                    <div data-i18n="Extras">Extras</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-box-3-line"></i>
                <div data-i18n="Form Layouts">Form Layouts</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="form-layouts-vertical.html" class="menu-link">
                    <div data-i18n="Vertical Form">Vertical Form</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="form-layouts-horizontal.html" class="menu-link">
                    <div data-i18n="Horizontal Form">Horizontal Form</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="form-layouts-sticky.html" class="menu-link">
                    <div data-i18n="Sticky Actions">Sticky Actions</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-git-commit-line"></i>
                <div data-i18n="Form Wizard">Form Wizard</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="form-wizard-numbered.html" class="menu-link">
                    <div data-i18n="Numbered">Numbered</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="form-wizard-icons.html" class="menu-link">
                    <div data-i18n="Icons">Icons</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="form-validation.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-checkbox-multiple-line"></i>
                <div data-i18n="Form Validation">Form Validation</div>
              </a>
            </li>
            <!-- Tables -->
            <li class="menu-item">
              <a href="tables-basic.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-table-alt-line"></i>
                <div data-i18n="Tables">Tables</div>
              </a>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-grid-line"></i>
                <div data-i18n="Datatables">Datatables</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="tables-datatables-basic.html" class="menu-link">
                    <div data-i18n="Basic">Basic</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="tables-datatables-advanced.html" class="menu-link">
                    <div data-i18n="Advanced">Advanced</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="tables-datatables-extensions.html" class="menu-link">
                    <div data-i18n="Extensions">Extensions</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Charts & Maps -->
            <li class="menu-header small mt-5">
              <span class="menu-header-text" data-i18n="Charts & Maps">Charts &amp; Maps</span>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-bar-chart-2-line"></i>
                <div data-i18n="Charts">Charts</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="charts-apex.html" class="menu-link">
                    <div data-i18n="Apex Charts">Apex Charts</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="charts-chartjs.html" class="menu-link">
                    <div data-i18n="ChartJS">ChartJS</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="maps-leaflet.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-map-2-line"></i>
                <div data-i18n="Leaflet Maps">Leaflet Maps</div>
              </a>
            </li>

            <!-- Misc -->
            <li class="menu-header small mt-5">
              <span class="menu-header-text" data-i18n="Misc">Misc</span>
            </li>

            <!-- Multi Level Menu -->
            <li class="menu-item">
              <a href="javascript:void(0)" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-drag-drop-line"></i>
                <div data-i18n="Multi Level">Multi Level</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="javascript:void(0)" class="menu-link menu-toggle">
                    <div data-i18n="Level 2">Level 2</div>
                  </a>
                  <ul class="menu-sub">
                    <li class="menu-item">
                      <a href="javascript:void(0)" class="menu-link">
                        <div data-i18n="Level 3">Level 3</div>
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="https://pixinvent.ticksy.com/" target="_blank" class="menu-link">
                <i class="menu-icon icon-base ri ri-lifebuoy-line"></i>
                <div data-i18n="Support">Support</div>
              </a>
            </li>
            <li class="menu-item">
              <a
                href="https://demos.pixinvent.com/materialize-html-admin-template/documentation/"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-article-line"></i>
                <div data-i18n="Documentation">Documentation</div>
              </a>
            </li>
          </ul>
        </aside>

        <div class="menu-mobile-toggler d-xl-none rounded-1">
          <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large text-bg-secondary p-2 rounded-1">
            <i class="ri ri-menu-line icon-base"></i>
            <i class="ri ri-arrow-right-s-line icon-base"></i>
          </a>
        </div>
        <!-- / Menu -->

        <!-- Layout container -->
        <div class="layout-page">
          <!-- Navbar -->

          <nav
            class="layout-navbar container-xxl navbar-detached navbar navbar-expand-xl align-items-center bg-navbar-theme"
            id="layout-navbar">
            <div class="layout-menu-toggle navbar-nav align-items-xl-center me-4 me-xl-0 d-xl-none">
              <a class="nav-item nav-link px-0 me-xl-6" href="javascript:void(0)">
                <i class="icon-base ri ri-menu-line icon-22px"></i>
              </a>
            </div>

            <div class="navbar-nav-right d-flex align-items-center justify-content-end" id="navbar-collapse">
              <!-- Search -->
              <div class="navbar-nav align-items-center">
                <div class="nav-item navbar-search-wrapper mb-0">
                  <a class="nav-item nav-link search-toggler px-0" href="javascript:void(0);">
                    <span class="d-inline-block text-body-secondary fw-normal" id="autocomplete"></span>
                  </a>
                </div>
              </div>

              <!-- /Search -->

              <ul class="navbar-nav flex-row align-items-center ms-md-auto">
                <li class="nav-item dropdown-language dropdown me-sm-2 me-xl-0">
                  <a
                    class="nav-link dropdown-toggle hide-arrow btn btn-icon btn-text-secondary rounded-pill"
                    href="javascript:void(0);"
                    data-bs-toggle="dropdown">
                    <i class="icon-base ri ri-translate-2 icon-22px"></i>
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-language="en" data-text-direction="ltr">
                        <span>English</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-language="fr" data-text-direction="ltr">
                        <span>French</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-language="ar" data-text-direction="rtl">
                        <span>Arabic</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-language="de" data-text-direction="ltr">
                        <span>German</span>
                      </a>
                    </li>
                  </ul>
                </li>
                <!--/ Language -->

                <!-- Quick links -->
                <li class="nav-item dropdown-shortcuts navbar-dropdown dropdown me-sm-2 me-xl-0">
                  <a
                    class="nav-link dropdown-toggle hide-arrow btn btn-icon btn-text-secondary rounded-pill"
                    href="javascript:void(0);"
                    data-bs-toggle="dropdown"
                    data-bs-auto-close="outside"
                    aria-expanded="false">
                    <i class="icon-base ri ri-star-smile-line icon-22px"></i>
                  </a>
                  <div class="dropdown-menu dropdown-menu-end p-0">
                    <div class="dropdown-menu-header border-bottom">
                      <div class="dropdown-header d-flex align-items-center py-3">
                        <h6 class="mb-0 me-auto">Shortcuts</h6>
                        <a
                          href="javascript:void(0)"
                          class="btn btn-text-secondary rounded-pill btn-icon dropdown-shortcuts-add text-heading"
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          title="Add shortcuts">
                          <i class="icon-base ri ri-add-line text-heading"></i>
                        </a>
                      </div>
                    </div>
                    <div class="dropdown-shortcuts-list scrollable-container">
                      <div class="row row-bordered overflow-visible g-0">
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-calendar-line icon-26px text-heading"></i>
                          </span>
                          <a href="app-calendar.html" class="stretched-link">Calendar</a>
                          <small>Appointments</small>
                        </div>
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-file-text-line icon-26px text-heading"></i>
                          </span>
                          <a href="app-invoice-list.html" class="stretched-link">Invoice App</a>
                          <small>Manage Accounts</small>
                        </div>
                      </div>
                      <div class="row row-bordered overflow-visible g-0">
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-user-line icon-26px text-heading"></i>
                          </span>
                          <a href="app-user-list.html" class="stretched-link">User App</a>
                          <small>Manage Users</small>
                        </div>
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-computer-line icon-26px text-heading"></i>
                          </span>
                          <a href="app-access-roles.html" class="stretched-link">Role Management</a>
                          <small>Permission</small>
                        </div>
                      </div>
                      <div class="row row-bordered overflow-visible g-0">
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-pie-chart-2-line icon-26px text-heading"></i>
                          </span>
                          <a href="index.html" class="stretched-link">Dashboard</a>
                          <small>User Dashboard</small>
                        </div>
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-settings-4-line icon-26px text-heading"></i>
                          </span>
                          <a href="pages-account-settings-account.html" class="stretched-link">Setting</a>
                          <small>Account Settings</small>
                        </div>
                      </div>
                      <div class="row row-bordered overflow-visible g-0">
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-question-line icon-26px text-heading"></i>
                          </span>
                          <a href="pages-faq.html" class="stretched-link">FAQs</a>
                          <small>FAQs & Articles</small>
                        </div>
                        <div class="dropdown-shortcuts-item col">
                          <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                            <i class="icon-base ri ri-tv-2-line icon-26px text-heading"></i>
                          </span>
                          <a href="modal-examples.html" class="stretched-link">Modals</a>
                          <small>Useful Popups</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <!-- Quick links -->

                <!-- Notification -->
                <li class="nav-item dropdown-notifications navbar-dropdown dropdown me-4 me-xl-1">
                  <a
                    class="nav-link dropdown-toggle hide-arrow btn btn-icon btn-text-secondary rounded-pill"
                    href="javascript:void(0);"
                    data-bs-toggle="dropdown"
                    data-bs-auto-close="outside"
                    aria-expanded="false">
                    <i class="icon-base ri ri-notification-2-line icon-22px"></i>
                    <span
                      class="position-absolute top-0 start-50 translate-middle-y badge badge-dot bg-danger mt-2 border"></span>
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end py-0">
                    <li class="dropdown-menu-header border-bottom py-50">
                      <div class="dropdown-header d-flex align-items-center py-2">
                        <h6 class="mb-0 me-auto">Notification</h6>
                        <div class="d-flex align-items-center h6 mb-0">
                          <span class="badge rounded-pill bg-label-primary fs-xsmall me-2">8 New</span>
                          <a
                            href="javascript:void(0)"
                            class="dropdown-notifications-all p-2"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Mark all as read"
                            ><i class="icon-base ri ri-mail-open-line text-heading"></i
                          ></a>
                        </div>
                      </div>
                    </li>
                    <li class="dropdown-notifications-list scrollable-container">
                      <ul class="list-group list-group-flush">
                        <li class="list-group-item list-group-item-action dropdown-notifications-item">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <img src="../../assets/img/avatars/1.png" alt="avatar" class="rounded-circle" />
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">Congratulation Lettie 🎉</h6>
                              <small class="mb-1 d-block text-body">Won the monthly best seller gold badge</small>
                              <small class="text-body-secondary">1h ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <span class="avatar-initial rounded-circle bg-label-danger">CF</span>
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">Charles Franklin</h6>
                              <small class="mb-1 d-block text-body">Accepted your connection</small>
                              <small class="text-body-secondary">12hr ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item marked-as-read">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <img src="../../assets/img/avatars/2.png" alt="avatar" class="rounded-circle" />
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">New Message ✉️</h6>
                              <small class="mb-1 d-block text-body">You have new message from Natalie</small>
                              <small class="text-body-secondary">1h ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <span class="avatar-initial rounded-circle bg-label-success"
                                  ><i class="icon-base ri ri-shopping-cart-2-line icon-18px"></i
                                ></span>
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">Whoo! You have new order 🛒</h6>
                              <small class="mb-1 d-block text-body">ACME Inc. made new order $1,154</small>
                              <small class="text-body-secondary">1 day ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item marked-as-read">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <img src="../../assets/img/avatars/9.png" alt="avatar" class="rounded-circle" />
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">Application has been approved 🚀</h6>
                              <small class="mb-1 d-block text-body"
                                >Your ABC project application has been approved.</small
                              >
                              <small class="text-body-secondary">2 days ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item marked-as-read">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <span class="avatar-initial rounded-circle bg-label-success"
                                  ><i class="icon-base ri ri-pie-chart-2-line icon-18px"></i
                                ></span>
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">Monthly report is generated</h6>
                              <small class="mb-1 d-block text-body">July monthly financial report is generated </small>
                              <small class="text-body-secondary">3 days ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item marked-as-read">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <img src="../../assets/img/avatars/5.png" alt="avatar" class="rounded-circle" />
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">Send connection request</h6>
                              <small class="mb-1 d-block text-body">Peter sent you connection request</small>
                              <small class="text-body-secondary">4 days ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <img src="../../assets/img/avatars/6.png" alt="avatar" class="rounded-circle" />
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">New message from Jane</h6>
                              <small class="mb-1 d-block text-body">Your have new message from Jane</small>
                              <small class="text-body-secondary">5 days ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                        <li class="list-group-item list-group-item-action dropdown-notifications-item marked-as-read">
                          <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                              <div class="avatar">
                                <span class="avatar-initial rounded-circle bg-label-warning"
                                  ><i class="icon-base ri ri-error-warning-line icon-18px"></i
                                ></span>
                              </div>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="small mb-1">CPU is running high</h6>
                              <small class="mb-1 d-block text-body"
                                >CPU Utilization Percent is currently at 88.63%,</small
                              >
                              <small class="text-body-secondary">5 days ago</small>
                            </div>
                            <div class="flex-shrink-0 dropdown-notifications-actions">
                              <a href="javascript:void(0)" class="dropdown-notifications-read"
                                ><span class="badge badge-dot"></span
                              ></a>
                              <a href="javascript:void(0)" class="dropdown-notifications-archive"
                                ><span class="icon-base ri ri-close-line"></span
                              ></a>
                            </div>
                          </div>
                        </li>
                      </ul>
                    </li>
                    <li class="border-top">
                      <div class="d-grid p-4">
                        <a class="btn btn-primary btn-sm d-flex" href="javascript:void(0);">
                          <small class="align-middle">View all notifications</small>
                        </a>
                      </div>
                    </li>
                  </ul>
                </li>
                <!--/ Notification -->

                <!-- User -->
                <li class="nav-item navbar-dropdown dropdown-user dropdown">
                  <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                      <img src="../../assets/img/avatars/1.png" alt="avatar" class="rounded-circle" />
                    </div>
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end mt-3 py-2">
                    <li>
                      <a class="dropdown-item" href="pages-account-settings-account.html">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0 me-2">
                            <div class="avatar avatar-online">
                              <img
                                src="../../assets/img/avatars/1.png"
                                alt="alt"
                                class="w-px-40 h-auto rounded-circle" />
                            </div>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-0 small">John Doe</h6>
                            <small class="text-body-secondary">Admin</small>
                          </div>
                        </div>
                      </a>
                    </li>
                    <li>
                      <div class="dropdown-divider"></div>
                    </li>
                    <li>
                      <a class="dropdown-item" href="pages-profile-user.html">
                        <i class="icon-base ri ri-user-3-line icon-22px me-3"></i
                        ><span class="align-middle">My Profile</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="pages-account-settings-account.html">
                        <i class="icon-base ri ri-settings-4-line icon-22px me-3"></i
                        ><span class="align-middle">Settings</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="pages-account-settings-billing.html">
                        <span class="d-flex align-items-center align-middle">
                          <i class="flex-shrink-0 icon-base ri ri-file-text-line icon-22px me-3"></i>
                          <span class="flex-grow-1 align-middle">Billing Plan</span>
                          <span class="flex-shrink-0 badge badge-center rounded-pill bg-danger">4</span>
                        </span>
                      </a>
                    </li>
                    <li>
                      <div class="dropdown-divider"></div>
                    </li>
                    <li>
                      <a class="dropdown-item" href="pages-pricing.html">
                        <i class="icon-base ri ri-money-dollar-circle-line icon-22px me-3"></i
                        ><span class="align-middle">Pricing</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="pages-faq.html">
                        <i class="icon-base ri ri-question-line icon-22px me-3"></i
                        ><span class="align-middle">FAQ</span>
                      </a>
                    </li>
                    <li>
                      <div class="d-grid px-4 pt-2 pb-1">
                        <a class="btn btn-sm btn-danger d-flex" href="auth-login-cover.html" target="_blank">
                          <small class="align-middle">Logout</small>
                          <i class="icon-base ri ri-logout-box-r-line ms-2 icon-16px"></i>
                        </a>
                      </div>
                    </li>
                  </ul>
                </li>
                <!--/ User -->
              </ul>
            </div>
          </nav>

          <!-- / Navbar -->

          <!-- Content wrapper -->
          <div class="content-wrapper">
            <!-- Content -->
            <div class="container-xxl flex-grow-1 container-p-y">
              <div class="row g-6">
                <!--  Pricing -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-money-dollar-circle-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Pricing</h5>
                      <p>Elegant pricing options modal popup example, easy to use in any page.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#pricingModal">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Pricing -->

                <!--  Add New Credit Card -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-bank-card-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Add New Credit Card</h5>
                      <p>Quickly collect the credit card details, built in input mask and form validation support.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#addNewCCModal">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Add New Credit Card -->

                <!--  Add New Address -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-home-3-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Add New Address</h5>
                      <p>Ready to use form to collect user address data with validation and custom input support.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#addNewAddress">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Add New Address -->

                <!--  Refer & Earn -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-gift-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Refer & Earn</h5>
                      <p>Use Refer & Earn modal to encourage your exiting customers refer their friends & colleague.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#referAndEarn">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Refer & Earn -->

                <!--  Edit User -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-user-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Edit User</h5>
                      <p>Easily update the user data on the go, built in form validation and custom controls.</p>
                      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editUser">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Edit User -->

                <!--  Enable OTP -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-smartphone-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Enable OTP</h5>
                      <p>Use this modal to enhance your application security by enabling authentication with OTP.</p>
                      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#enableOTP">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Enable OTP -->

                <!--  Share Project -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-file-pdf-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Share Project</h5>
                      <p>Elegant Share Project options modal popup example, easy to use in any page.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#shareProject">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Share Project -->

                <!--  Create App -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-box-3-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Create App</h5>
                      <p>Provide application data with this form to create your app, easy to use in page.</p>
                      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createApp">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Create App -->

                <!--  Two Factor Auth -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="icon-base ri ri-lock-line icon-36px text-heading"></i>
                      <h5 class="mt-4">Two Factor Auth</h5>
                      <p>Enhance your application security by enabling two factor authentication.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#twoFactorAuth">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Two Factor Auth -->

                <!--  Payment providers -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="mb-4 icon-base ri ri-bank-card-line icon-36px text-heading"></i>
                      <h5>Payment providers</h5>
                      <p>Elegant payment options modal popup example, easy to use in any page.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#paymentProvider">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Payment providers -->

                <!--  Payment methods -->
                <div class="col-12 col-sm-6 col-lg-4">
                  <div class="card">
                    <div class="card-body text-center">
                      <i class="mb-4 icon-base ri ri-bank-card-2-line icon-36px text-heading"></i>
                      <h5>Add Payment Method</h5>
                      <p>Elegant payment methods modal popup example, easy to use in any page.</p>
                      <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#paymentMethods">
                        Show
                      </button>
                    </div>
                  </div>
                </div>
                <!--/  Payment methods -->
              </div>

              <!-- All Modals -->
              <!-- Pricing Modal -->
              <div class="modal fade" id="pricingModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-simple modal-pricing">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <!-- Pricing Plans -->
                      <div class="pb-6 rounded-top">
                        <h4 class="text-center mb-2">Pricing Plans</h4>
                        <p class="text-center mb-0">
                          All plans include 40+ advanced tools and features to boost your product. Choose the best plan
                          to fit your needs.
                        </p>
                        <div class="d-flex align-items-center justify-content-center flex-wrap gap-2 pt-12 pb-4">
                          <label class="switch switch-sm ms-sm-12 ps-sm-12 me-0">
                            <span class="switch-label fs-6 text-body">Monthly</span>
                            <input type="checkbox" class="switch-input price-duration-toggler" checked />
                            <span class="switch-toggle-slider">
                              <span class="switch-on"></span>
                              <span class="switch-off"></span>
                            </span>
                            <span class="switch-label fs-6 text-body">Annually</span>
                          </label>
                          <div class="mt-n5 ms-n10 ml-2 mb-10 d-none d-sm-flex align-items-center gap-2">
                            <i
                              class="icon-base ri ri-corner-left-down-fill icon-24px text-body-secondary scaleX-n1-rtl"></i>
                            <span class="badge badge-sm bg-label-primary rounded-pill mb-2">Save up to 10%</span>
                          </div>
                        </div>

                        <div class="row gy-3">
                          <!-- Basic -->
                          <div class="col-xl mb-md-0 mb-6">
                            <div class="card border shadow-none">
                              <div class="card-body pt-12">
                                <div class="mt-3 mb-5 text-center">
                                  <img
                                    src="../../assets/img/illustrations/pricing-basic.png"
                                    alt="Basic Image"
                                    height="100" />
                                </div>
                                <h4 class="card-title text-center text-capitalize mb-2">Basic</h4>
                                <p class="text-center mb-5">A simple start for everyone</p>
                                <div class="text-center h-px-50">
                                  <div class="d-flex justify-content-center">
                                    <sup class="h6 text-body pricing-currency mt-2 mb-0 me-1 fw-normal">$</sup>
                                    <h1 class="mb-0 text-primary">0</h1>
                                    <sub class="h6 text-body pricing-duration mt-auto mb-1">/month</sub>
                                  </div>
                                </div>

                                <ul class="list-group ps-6 my-5 pt-4">
                                  <li class="mb-4">100 responses a month</li>
                                  <li class="mb-4">Unlimited forms and surveys</li>
                                  <li class="mb-4">Unlimited fields</li>
                                  <li class="mb-4">Basic form creation tools</li>
                                  <li class="mb-0">Up to 2 subdomains</li>
                                </ul>

                                <button
                                  type="button"
                                  class="btn btn-outline-success d-grid w-100"
                                  data-bs-dismiss="modal">
                                  Your Current Plan
                                </button>
                              </div>
                            </div>
                          </div>

                          <!-- Standard -->
                          <div class="col-xl mb-md-0 mb-6">
                            <div class="card border-primary border shadow-none">
                              <div class="card-body position-relative pt-4">
                                <div class="position-absolute end-0 me-6 top-0 mt-6">
                                  <span class="badge bg-label-primary rounded-pill">Popular</span>
                                </div>
                                <div class="my-5 pt-6 text-center">
                                  <img
                                    src="../../assets/img/illustrations/pricing-standard.png"
                                    alt="Standard Image"
                                    height="100" />
                                </div>
                                <h4 class="card-title text-center text-capitalize mb-2">Standard</h4>
                                <p class="text-center mb-5">For small to medium businesses</p>
                                <div class="text-center h-px-50">
                                  <div class="d-flex justify-content-center">
                                    <sup class="h6 text-body pricing-currency mt-2 mb-0 me-1">$</sup>
                                    <h1 class="price-toggle price-yearly text-primary mb-0">7</h1>
                                    <h1 class="price-toggle price-monthly text-primary mb-0 d-none">9</h1>
                                    <sub class="h6 text-body pricing-duration mt-auto mb-1">/month</sub>
                                  </div>
                                  <small class="price-yearly price-yearly-toggle text-body-secondary"
                                    >USD 480 / year</small
                                  >
                                </div>

                                <ul class="list-group ps-6 my-5 pt-4">
                                  <li class="mb-4">Unlimited responses</li>
                                  <li class="mb-4">Unlimited forms and surveys</li>
                                  <li class="mb-4">Instagram profile page</li>
                                  <li class="mb-4">Google Docs integration</li>
                                  <li class="mb-0">Custom “Thank you” page</li>
                                </ul>

                                <button type="button" class="btn btn-primary d-grid w-100" data-bs-dismiss="modal">
                                  Upgrade
                                </button>
                              </div>
                            </div>
                          </div>

                          <!-- Enterprise -->
                          <div class="col-xl">
                            <div class="card border shadow-none">
                              <div class="card-body pt-12">
                                <div class="mt-3 mb-5 text-center">
                                  <img
                                    src="../../assets/img/illustrations/pricing-enterprise.png"
                                    alt="Enterprise Image"
                                    height="100" />
                                </div>
                                <h4 class="card-title text-center text-capitalize mb-2">Enterprise</h4>
                                <p class="text-center mb-5">Solution for big organizations</p>

                                <div class="text-center h-px-50">
                                  <div class="d-flex justify-content-center">
                                    <sup class="h6 text-body pricing-currency mt-2 mb-0 me-1">$</sup>
                                    <h1 class="price-toggle price-yearly text-primary mb-0">16</h1>
                                    <h1 class="price-toggle price-monthly text-primary mb-0 d-none">19</h1>
                                    <sub class="h6 text-body pricing-duration mt-auto mb-1">/month</sub>
                                  </div>
                                  <small class="price-yearly price-yearly-toggle text-body-secondary"
                                    >USD 960 / year</small
                                  >
                                </div>

                                <ul class="list-group ps-6 my-5 pt-4">
                                  <li class="mb-4">PayPal payments</li>
                                  <li class="mb-4">Logic Jumps</li>
                                  <li class="mb-4">File upload with 5GB storage</li>
                                  <li class="mb-4">Custom domain support</li>
                                  <li class="mb-0">Stripe integration</li>
                                </ul>

                                <button
                                  type="button"
                                  class="btn btn-outline-primary d-grid w-100"
                                  data-bs-dismiss="modal">
                                  Upgrade
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!--/ Pricing Plans -->
                      <div class="text-center">
                        <p>Still Not Convinced? Start with a 14-day FREE trial!</p>
                        <a href="javascript:void(0);" class="btn btn-primary">Start your trial</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Pricing Modal -->
              <script src="../../assets//js/pages-pricing.js"></script>
              <!-- Add New Credit Card Modal -->
              <div class="modal fade" id="addNewCCModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered1 modal-simple modal-add-new-cc">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Add New Card</h4>
                        <p>Add new card to complete payment</p>
                      </div>
                      <form id="addNewCCForm" class="row g-5" onsubmit="return false">
                        <div class="col-12 form-control-validation">
                          <div class="input-group input-group-merge">
                            <div class="form-floating form-floating-outline">
                              <input
                                id="modalAddCard"
                                name="modalAddCard"
                                class="form-control credit-card-mask"
                                type="text"
                                placeholder="1356 3215 6548 7898"
                                aria-describedby="modalAddCard2" />
                              <label for="modalAddCard">Card Number</label>
                            </div>
                            <span class="input-group-text cursor-pointer p-1" id="modalAddCard2"
                              ><span class="card-type"></span
                            ></span>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input type="text" id="modalAddCardName" class="form-control" placeholder="John Doe" />
                            <label for="modalAddCardName">Name</label>
                          </div>
                        </div>
                        <div class="col-6 col-md-3">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddCardExpiryDate"
                              class="form-control expiry-date-mask"
                              placeholder="MM/YY" />
                            <label for="modalAddCardExpiryDate">Expiry</label>
                          </div>
                        </div>
                        <div class="col-6 col-md-3">
                          <div class="input-group input-group-merge">
                            <div class="form-floating form-floating-outline">
                              <input
                                type="text"
                                id="modalAddCardCvv"
                                class="form-control cvv-code-mask"
                                maxlength="3"
                                placeholder="654" />
                              <label for="modalAddCardCvv" class="pe-1_5">CVV</label>
                            </div>
                            <span class="input-group-text cursor-pointer ps-0" id="modalAddCardCvv2"
                              ><i
                                class="icon-base ri ri-question-line"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Card Verification Value"></i
                            ></span>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="futureAddress" />
                            <label for="futureAddress" class="text-heading">Save card for future billing?</label>
                          </div>
                        </div>
                        <div class="col-12 d-flex flex-wrap justify-content-center gap-4 row-gap-4">
                          <button type="submit" class="btn btn-primary">Submit</button>
                          <button
                            type="reset"
                            class="btn btn-outline-secondary btn-reset"
                            data-bs-dismiss="modal"
                            aria-label="Close">
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Add New Credit Card Modal -->

              <!-- Add New Address Modal -->
              <div class="modal fade" id="addNewAddress" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-add-new-address">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="address-title mb-2">Add New Address</h4>
                        <p class="address-subtitle">Add new address for express delivery</p>
                      </div>
                      <form id="addNewAddressForm" class="row g-5" onsubmit="return false">
                        <div class="col-12 form-control-validation">
                          <div class="row g-5">
                            <div class="col-md mb-md-0">
                              <div class="form-check custom-option custom-option-basic">
                                <label class="form-check-label custom-option-content" for="customRadioHome">
                                  <input
                                    name="customRadioTemp"
                                    class="form-check-input"
                                    type="radio"
                                    value=""
                                    id="customRadioHome"
                                    checked />
                                  <span class="custom-option-header">
                                    <span class="h6 mb-0 d-flex align-items-center"
                                      ><i class="icon-base ri ri-home-smile-2-line icon-20px me-1"></i>Home</span
                                    >
                                  </span>
                                  <span class="custom-option-body">
                                    <small>Delivery time (9am – 9pm)</small>
                                  </span>
                                </label>
                              </div>
                            </div>
                            <div class="col-md mb-md-0">
                              <div class="form-check custom-option custom-option-basic">
                                <label class="form-check-label custom-option-content" for="customRadioOffice">
                                  <input
                                    name="customRadioTemp"
                                    class="form-check-input"
                                    type="radio"
                                    value=""
                                    id="customRadioOffice" />
                                  <span class="custom-option-header">
                                    <span class="h6 mb-0 d-flex align-items-center"
                                      ><i class="icon-base ri ri-building-line icon-20px me-1"></i>Office</span
                                    >
                                  </span>
                                  <span class="custom-option-body">
                                    <small>Delivery time (9am – 5pm) </small>
                                  </span>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-12 form-control-validation col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressFirstName"
                              name="modalAddressFirstName"
                              class="form-control"
                              placeholder="John" />
                            <label for="modalAddressFirstName">First Name</label>
                          </div>
                        </div>
                        <div class="col-12 form-control-validation col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressLastName"
                              name="modalAddressLastName"
                              class="form-control"
                              placeholder="Doe" />
                            <label for="modalAddressLastName">Last Name</label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-floating form-floating-outline">
                            <select
                              id="modalAddressCountry"
                              name="modalAddressCountry"
                              class="select2 form-select"
                              data-allow-clear="true">
                              <option value="">Select</option>
                              <option value="Australia">Australia</option>
                              <option value="Bangladesh">Bangladesh</option>
                              <option value="Belarus">Belarus</option>
                              <option value="Brazil">Brazil</option>
                              <option value="Canada">Canada</option>
                              <option value="China">China</option>
                              <option value="France">France</option>
                              <option value="Germany">Germany</option>
                              <option value="India">India</option>
                              <option value="Indonesia">Indonesia</option>
                              <option value="Israel">Israel</option>
                              <option value="Italy">Italy</option>
                              <option value="Japan">Japan</option>
                              <option value="Korea">Korea, Republic of</option>
                              <option value="Mexico">Mexico</option>
                              <option value="Philippines">Philippines</option>
                              <option value="Russia">Russian Federation</option>
                              <option value="South Africa">South Africa</option>
                              <option value="Thailand">Thailand</option>
                              <option value="Turkey">Turkey</option>
                              <option value="Ukraine">Ukraine</option>
                              <option value="United Arab Emirates">United Arab Emirates</option>
                              <option value="United Kingdom">United Kingdom</option>
                              <option value="United States">United States</option>
                            </select>
                            <label for="modalAddressCountry">Country</label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressAddress1"
                              name="modalAddressAddress1"
                              class="form-control"
                              placeholder="12, Business Park" />
                            <label for="modalAddressAddress1">Address Line 1</label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressAddress2"
                              name="modalAddressAddress2"
                              class="form-control"
                              placeholder="Mall Road" />
                            <label for="modalAddressAddress2">Address Line 2</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressLandmark"
                              name="modalAddressLandmark"
                              class="form-control"
                              placeholder="Nr. Hard Rock Cafe" />
                            <label for="modalAddressLandmark">Landmark</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressCity"
                              name="modalAddressCity"
                              class="form-control"
                              placeholder="Los Angeles" />
                            <label for="modalAddressCity">City</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressState"
                              name="modalAddressState"
                              class="form-control"
                              placeholder="California" />
                            <label for="modalAddressLandmark">State</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalAddressZipCode"
                              name="modalAddressZipCode"
                              class="form-control"
                              placeholder="99950" />
                            <label for="modalAddressZipCode">Zip Code</label>
                          </div>
                        </div>
                        <div class="col-12 mt-6">
                          <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="billingAddress" />
                            <label for="billingAddress">Use as a billing address?</label>
                          </div>
                        </div>
                        <div class="col-12 mt-6 d-flex flex-wrap justify-content-center gap-4 row-gap-4">
                          <button type="submit" class="btn btn-primary">Submit</button>
                          <button
                            type="reset"
                            class="btn btn-outline-secondary"
                            data-bs-dismiss="modal"
                            aria-label="Close">
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Add New Address Modal -->

              <!-- Refer & Earn Modal -->
              <div class="modal fade" id="referAndEarn" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-refer-and-earn">
                  <div class="modal-content">
                    <div class="modal-body pt-4 pt-md-0 px-0 pb-md-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Refer & Earn</h4>
                        <p class="text-center mb-6">
                          Invite your friend to Materialize, if they sign up, you and your friend will get 30 days free
                          trial.
                        </p>
                      </div>
                      <div class="row py-6">
                        <div class="col-12 col-lg-4 px-6">
                          <div class="d-flex justify-content-center mb-4">
                            <div class="modal-refer-and-earn-step bg-label-primary">
                              <i class="icon-base ri ri-send-plane-2-line icon-40px"></i>
                            </div>
                          </div>
                          <div class="text-center">
                            <h6 class="mb-2">Send Invitation 🤟🏻</h6>
                            <p class="mb-lg-0">Send your referral link to your friend</p>
                          </div>
                        </div>
                        <div class="col-12 col-lg-4 px-6">
                          <div class="d-flex justify-content-center mb-4">
                            <div class="modal-refer-and-earn-step bg-label-primary">
                              <i class="icon-base ri ri-pages-line icon-40px"></i>
                            </div>
                          </div>
                          <div class="text-center">
                            <h6 class="mb-2">Registration 👩🏻‍💻</h6>
                            <p class="mb-lg-0">Let them register to our services</p>
                          </div>
                        </div>
                        <div class="col-12 col-lg-4 px-6">
                          <div class="d-flex justify-content-center mb-4">
                            <div class="modal-refer-and-earn-step bg-label-primary">
                              <i class="icon-base ri ri-gift-line icon-40px"></i>
                            </div>
                          </div>
                          <div class="text-center">
                            <h6 class="mb-2">Free Trial 🎉</h6>
                            <p class="mb-0">Your friend will get 30 days free trial</p>
                          </div>
                        </div>
                      </div>
                      <hr class="my-6" />
                      <h5 class="mb-5">Invite your friends</h5>
                      <form class="row g-4" onsubmit="return false">
                        <div class="col-lg-10">
                          <label class="mb-2" for="modalRnFEmail"
                            >Enter your friend’s email address and invite them to join Materialize 😍</label
                          >
                          <input
                            type="text"
                            id="modalRnFEmail"
                            class="form-control form-control-sm"
                            placeholder="<EMAIL>"
                            aria-label="<EMAIL>" />
                        </div>
                        <div class="col-lg-2 d-flex align-items-end">
                          <button type="button" class="btn btn-primary">Submit</button>
                        </div>
                      </form>
                      <h5 class="mt-6 mb-5">Share the referral link</h5>
                      <form class="row g-4" onsubmit="return false">
                        <div class="col-lg-9">
                          <label class="mb-2" for="modalRnFLink"
                            >You can also copy and send it or share it on your social media. 🥳</label
                          >
                          <div class="input-group input-group-sm input-group-merge">
                            <input type="text" id="modalRnFLink" class="form-control" value="https://pixinvent.com" />
                            <span class="input-group-text text-primary cursor-pointer text-uppercase" id="basic-addon33"
                              >Copy link</span
                            >
                          </div>
                        </div>
                        <div class="col-lg-3 d-flex align-items-end">
                          <div class="btn-social">
                            <button type="button" class="btn btn-icon btn-facebook">
                              <i class="icon-base ri ri-facebook-circle-line icon-22px"></i>
                            </button>
                            <button type="button" class="btn btn-icon btn-twitter">
                              <i class="icon-base ri ri-twitter-line icon-22px"></i>
                            </button>
                            <button type="button" class="btn btn-icon btn-linkedin">
                              <i class="icon-base ri ri-linkedin-line icon-22px"></i>
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Refer & Earn Modal -->

              <!-- Edit User Modal -->
              <div class="modal fade" id="editUser" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-edit-user">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Edit User Information</h4>
                        <p class="mb-6">Updating user details will receive a privacy audit.</p>
                      </div>
                      <form id="editUserForm" class="row g-5" onsubmit="return false">
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalEditUserFirstName"
                              name="modalEditUserFirstName"
                              class="form-control"
                              value="Oliver"
                              placeholder="Oliver" />
                            <label for="modalEditUserFirstName">First Name</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalEditUserLastName"
                              name="modalEditUserLastName"
                              class="form-control"
                              value="Queen"
                              placeholder="Queen" />
                            <label for="modalEditUserLastName">Last Name</label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalEditUserName"
                              name="modalEditUserName"
                              class="form-control"
                              value="oliver.queen"
                              placeholder="oliver.queen" />
                            <label for="modalEditUserName">Username</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalEditUserEmail"
                              name="modalEditUserEmail"
                              class="form-control"
                              value="<EMAIL>"
                              placeholder="<EMAIL>" />
                            <label for="modalEditUserEmail">Email</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <select
                              id="modalEditUserStatus"
                              name="modalEditUserStatus"
                              class="form-select"
                              aria-label="Default select example">
                              <option value="1" selected>Active</option>
                              <option value="2">Inactive</option>
                              <option value="3">Suspended</option>
                            </select>
                            <label for="modalEditUserStatus">Status</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              type="text"
                              id="modalEditTaxID"
                              name="modalEditTaxID"
                              class="form-control modal-edit-tax-id"
                              placeholder="123 456 7890" />
                            <label for="modalEditTaxID">Tax ID</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="input-group input-group-merge">
                            <span class="input-group-text">US (+1)</span>
                            <div class="form-floating form-floating-outline">
                              <input
                                type="text"
                                id="modalEditUserPhone"
                                name="modalEditUserPhone"
                                class="form-control phone-number-mask"
                                value="****** 933 4422"
                                placeholder="****** 933 4422" />
                              <label for="modalEditUserPhone">Phone Number</label>
                            </div>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input
                              id="modalEditUserLanguage"
                              name="modalEditUserLanguage"
                              class="form-control h-auto"
                              placeholder="select technologies"
                              value="English" />
                            <label for="modalEditUserLanguage">Custom List Suggestions</label>
                          </div>
                        </div>
                        <div class="col-12 col-md-6">
                          <div class="form-floating form-floating-outline">
                            <select
                              id="modalEditUserCountry"
                              name="modalEditUserCountry"
                              class="select2 form-select"
                              data-allow-clear="true">
                              <option value="">Select</option>
                              <option value="Australia">Australia</option>
                              <option value="Bangladesh">Bangladesh</option>
                              <option value="Belarus">Belarus</option>
                              <option value="Brazil">Brazil</option>
                              <option value="Canada">Canada</option>
                              <option value="China">China</option>
                              <option value="France">France</option>
                              <option value="Germany">Germany</option>
                              <option value="India" selected>India</option>
                              <option value="Indonesia">Indonesia</option>
                              <option value="Israel">Israel</option>
                              <option value="Italy">Italy</option>
                              <option value="Japan">Japan</option>
                              <option value="Korea">Korea, Republic of</option>
                              <option value="Mexico">Mexico</option>
                              <option value="Philippines">Philippines</option>
                              <option value="Russia">Russian Federation</option>
                              <option value="South Africa">South Africa</option>
                              <option value="Thailand">Thailand</option>
                              <option value="Turkey">Turkey</option>
                              <option value="Ukraine">Ukraine</option>
                              <option value="United Arab Emirates">United Arab Emirates</option>
                              <option value="United Kingdom">United Kingdom</option>
                              <option value="United States">United States</option>
                            </select>
                            <label for="modalEditUserCountry">Country</label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="editBillingAddress" />
                            <label for="editBillingAddress" class="text-heading">Use as a billing address?</label>
                          </div>
                        </div>
                        <div class="col-12 text-center">
                          <button type="submit" class="btn btn-primary me-3">Submit</button>
                          <button
                            type="reset"
                            class="btn btn-outline-secondary"
                            data-bs-dismiss="modal"
                            aria-label="Close">
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Edit User Modal -->

              <!-- Enable OTP Modal -->
              <div class="modal fade" id="enableOTP" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-simple modal-enable-otp modal-dialog-centered">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Enable One Time Password</h4>
                        <p>Verify Your Mobile Number for SMS</p>
                      </div>
                      <p class="mb-5">
                        Enter your mobile phone number with country code and we will send you a verification code.
                      </p>
                      <form id="enableOTPForm" class="row g-5" onsubmit="return false">
                        <div class="col-12 form-control-validation">
                          <div class="input-group input-group-merge">
                            <span class="input-group-text">US (+1)</span>
                            <div class="form-floating form-floating-outline">
                              <input
                                type="text"
                                id="modalEnableOTPPhone"
                                name="modalEnableOTPPhone"
                                class="form-control phone-number-otp-mask"
                                placeholder="************" />
                              <label for="modalEnableOTPPhone">Phone Number</label>
                            </div>
                          </div>
                        </div>
                        <div class="col-12 d-flex flex-wrap justify-content-center gap-4 row-gap-4">
                          <button type="submit" class="btn btn-primary">Submit</button>
                          <button
                            type="reset"
                            class="btn btn-outline-secondary"
                            data-bs-dismiss="modal"
                            aria-label="Close">
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Enable OTP Modal -->

              <!-- Share Project Modal -->
              <div class="modal fade" id="shareProject" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-enable-otp modal-dialog-centered">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center">
                        <h4 class="mb-2">Share Project</h4>
                        <p class="mb-6">Share project with a team member</p>
                      </div>
                    </div>
                    <div class="mb-6">
                      <div class="form-floating form-floating-outline">
                        <select
                          id="select2Basic"
                          class="select2 form-select share-project-select"
                          data-allow-clear="true">
                          <option data-name="Adelaide Nichols" data-image="img/avatars/20.png" selected="selected">
                            Adelaide Nichols
                          </option>
                          <option data-name="Julian Murphy" data-image="img/avatars/9.png">Julian Murphy</option>
                          <option data-name="Sophie Gilbert" data-image="img/avatars/10.png">Sophie Gilbert</option>
                          <option data-name="Marvin Wheeler" data-image="img/avatars/17.png">Marvin Wheeler</option>
                        </select>
                        <label for="select2Basic">Add Members</label>
                      </div>
                    </div>
                    <h6>8 Members</h6>
                    <ul class="p-0 m-0">
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/1.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Lester Palmer</p>
                            <p class="small mb-0">pe&#64;vogeiz.net</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can Edit</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/2.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Mattie Blair</p>
                            <p class="small mb-0">peromak&#64;zukedohik.gov</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Owner</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/3.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Marvin Wheeler</p>
                            <p class="small mb-0">rumet&#64;jujpejah.net</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can Edit</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/4.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Nannie Ford</p>
                            <p class="small mb-0">negza&#64;nuv.io</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can Comment</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/5.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Julian Murphy</p>
                            <p class="small mb-0">lunebame&#64;umdomgu.net</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can View</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/6.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Sophie Gilbert</p>
                            <p class="small mb-0">ha&#64;sugit.gov</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can View</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap mb-4">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/7.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Chris Watkins</p>
                            <p class="small mb-0">zokap&#64;mak.org</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can Comment</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                      <li class="d-flex flex-wrap">
                        <div class="avatar me-4">
                          <img src="../../assets/img/avatars/8.png" alt="avatar" class="rounded-circle" />
                        </div>
                        <div class="d-flex justify-content-between flex-grow-1">
                          <div class="me-2">
                            <p class="mb-0 text-heading">Adelaide Nichols</p>
                            <p class="small mb-0">ujinomu&#64;jigo.com</p>
                          </div>
                          <div class="dropdown">
                            <button
                              type="button"
                              class="btn btn-text-secondary dropdown-toggle p-2 text-secondary"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                              <span class="me-2 d-none d-sm-inline-block">Can Edit</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div class="d-flex align-items-center flex-wrap mt-6">
                      <i
                        class="icon-base ri ri-group-line icon-20px text-heading me-2 align-self-start align-self-md-center"></i>
                      <div class="d-flex justify-content-between flex-grow-1 align-items-center flex-wrap gap-3">
                        <h6 class="mb-0">Public to Materialize - Pixinvent</h6>
                        <button class="btn btn-primary">
                          <i class="icon-base ri ri-link-m me-1_5"></i>Copy Project Link
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Share Project Modal -->

              <!-- Create App Modal -->
              <div class="modal fade" id="createApp" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-dialog-centered modal-simple modal-upgrade-plan">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center">
                        <h4 class="mb-2">Create App</h4>
                        <p class="mb-6">Provide data with this form to create your app.</p>
                      </div>
                      <!-- Property Listing Wizard -->
                      <div id="wizard-create-app" class="bs-stepper vertical wizard-vertical-icons mt-2 shadow-none">
                        <div class="bs-stepper-header border-0 p-1">
                          <div class="step" data-target="#details">
                            <button type="button" class="step-trigger">
                              <span class="avatar">
                                <span class="avatar-initial rounded-3">
                                  <i class="icon-base ri ri-file-text-line icon-24px"></i>
                                </span>
                              </span>
                              <span class="bs-stepper-label flex-column align-items-start gap-1 ms-4">
                                <span class="bs-stepper-title text-uppercase">Details</span>
                                <small class="bs-stepper-subtitle">Enter Details</small>
                              </span>
                            </button>
                          </div>
                          <div class="step" data-target="#frameworks">
                            <button type="button" class="step-trigger">
                              <span class="avatar">
                                <span class="avatar-initial rounded-3">
                                  <i class="icon-base ri ri-star-smile-line icon-24px"></i>
                                </span>
                              </span>
                              <span class="bs-stepper-label flex-column align-items-start gap-1 ms-4">
                                <span class="bs-stepper-title text-uppercase">Frameworks</span>
                                <small class="bs-stepper-subtitle">Select Framework</small>
                              </span>
                            </button>
                          </div>
                          <div class="step" data-target="#database">
                            <button type="button" class="step-trigger">
                              <span class="avatar">
                                <span class="avatar-initial rounded-3">
                                  <i class="icon-base ri ri-pie-chart-2-line icon-24px"></i>
                                </span>
                              </span>
                              <span class="bs-stepper-label flex-column align-items-start gap-1 ms-4">
                                <span class="bs-stepper-title text-uppercase">Database</span>
                                <small class="bs-stepper-subtitle">Select Database</small>
                              </span>
                            </button>
                          </div>
                          <div class="step" data-target="#billing">
                            <button type="button" class="step-trigger">
                              <span class="avatar">
                                <span class="avatar-initial rounded-3">
                                  <i class="icon-base ri ri-bank-card-line icon-24px"></i>
                                </span>
                              </span>
                              <span class="bs-stepper-label flex-column align-items-start gap-1 ms-4">
                                <span class="bs-stepper-title text-uppercase">Billing</span>
                                <small class="bs-stepper-subtitle">Payment Details</small>
                              </span>
                            </button>
                          </div>
                          <div class="step" data-target="#submit">
                            <button type="button" class="step-trigger">
                              <span class="avatar">
                                <span class="avatar-initial rounded-3">
                                  <i class="icon-base ri ri-check-double-line icon-24px"></i>
                                </span>
                              </span>
                              <span class="bs-stepper-label flex-column align-items-start gap-1 ms-4">
                                <span class="bs-stepper-title text-uppercase">Submit</span>
                                <small class="bs-stepper-subtitle">Submit</small>
                              </span>
                            </button>
                          </div>
                        </div>
                        <div class="bs-stepper-content p-1">
                          <form onsubmit="return false">
                            <!-- Details -->
                            <div id="details" class="content pt-4 pt-lg-0">
                              <div class="form-floating form-floating-outline mb-6">
                                <input
                                  type="text"
                                  class="form-control form-control-lg"
                                  id="modalAppName"
                                  placeholder="Application Name" />
                                <label for="modalAppName">Application Name</label>
                              </div>
                              <h5>Category</h5>
                              <ul class="p-0 m-0">
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md bg-label-info d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-bar-chart-box-line icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">CRM Application</h6>
                                      <small>Scales with any business</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input name="details-radio" class="form-check-input" type="radio" value="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md bg-label-success d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-shopping-cart-line icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">eCommerce Platforms</h6>
                                      <small>Grow Your Business With App</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input
                                          name="details-radio"
                                          class="form-check-input"
                                          type="radio"
                                          value=""
                                          checked="checked" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-center">
                                  <div
                                    class="avatar avatar-md bg-label-danger d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-video-upload-line icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">Online Learning platform</h6>
                                      <small>Start learning today</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input name="details-radio" class="form-check-input" type="radio" value="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                              </ul>
                              <div class="col-12 d-flex justify-content-between mt-6">
                                <button class="btn btn-outline-secondary btn-prev" disabled>
                                  <i class="icon-base ri ri-arrow-left-line icon-16px"></i>
                                  <span class="align-middle d-sm-block d-none ms-2">Previous</span>
                                </button>
                                <button class="btn btn-primary btn-next">
                                  <span class="align-middle d-sm-block d-none me-2">Next</span>
                                  <i class="icon-base ri ri-arrow-right-line icon-16px"></i>
                                </button>
                              </div>
                            </div>

                            <!-- Frameworks -->
                            <div id="frameworks" class="content pt-4 pt-lg-0">
                              <h5>Select Framework</h5>
                              <ul class="p-0 m-0">
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md bg-label-info d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-reactjs-line icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">React Native</h6>
                                      <small>Create truly native apps</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input name="frameworks-radio" class="form-check-input" type="radio" value="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md bg-label-danger d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-angularjs-fill icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">Angular</h6>
                                      <small>Most suited for your application</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input
                                          name="frameworks-radio"
                                          class="form-check-input"
                                          type="radio"
                                          value=""
                                          checked="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md bg-label-warning d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-html5-line icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">HTML</h6>
                                      <small>Progressive Framework</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input
                                          name="frameworks-radio"
                                          class="form-check-input"
                                          type="radio"
                                          value=""
                                          checked="checked" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-start">
                                  <div
                                    class="avatar avatar-md bg-label-success d-flex align-items-center justify-content-center flex-shrink-0 me-4 rounded-3">
                                    <i class="icon-base ri ri-vuejs-fill icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">VueJs</h6>
                                      <small>JS web frameworks</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input name="frameworks-radio" class="form-check-input" type="radio" value="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                              </ul>

                              <div class="col-12 d-flex justify-content-between mt-6">
                                <button class="btn btn-outline-secondary btn-prev">
                                  <i class="icon-base ri ri-arrow-left-line icon-16px"></i>
                                  <span class="align-middle d-sm-block d-none ms-2">Previous</span>
                                </button>
                                <button class="btn btn-primary btn-next">
                                  <span class="align-middle d-sm-block d-none me-2">Next</span>
                                  <i class="icon-base ri ri-arrow-right-line icon-16px"></i>
                                </button>
                              </div>
                            </div>

                            <!-- Database -->
                            <div id="database" class="content pt-4 pt-lg-0">
                              <div class="form-floating form-floating-outline mb-6">
                                <input
                                  type="text"
                                  class="form-control form-control-lg"
                                  id="modalAppDatabaseName"
                                  placeholder="Database Name" />
                                <label for="modalAppDatabaseName">Database Name</label>
                              </div>
                              <h5>Select Database Engine</h5>
                              <ul class="p-0 m-0">
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md d-flex align-items-center justify-content-center flex-shrink-0 bg-label-danger me-4 rounded-3">
                                    <i class="icon-base ri ri-fire-fill icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">Firebase</h6>
                                      <small>Cloud Firestone</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input name="database-radio" class="form-check-input" type="radio" value="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-center mb-4">
                                  <div
                                    class="avatar avatar-md d-flex align-items-center justify-content-center flex-shrink-0 bg-label-warning me-4 rounded-3">
                                    <i class="icon-base ri ri-amazon-line icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">AWS</h6>
                                      <small>Amazon Fast NoSQL Database</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input
                                          name="database-radio"
                                          class="form-check-input"
                                          type="radio"
                                          value=""
                                          checked="checked" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                                <li class="d-flex align-items-start">
                                  <div
                                    class="avatar avatar-md d-flex align-items-center justify-content-center flex-shrink-0 bg-label-info me-4 rounded-3">
                                    <i class="icon-base ri ri-database-2-fill icon-30px"></i>
                                  </div>
                                  <div class="d-flex justify-content-between w-100">
                                    <div class="me-2">
                                      <h6 class="mb-0">MySQL</h6>
                                      <small>Basic MySQL database</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                      <div class="form-check form-check-inline me-0">
                                        <input name="database-radio" class="form-check-input" type="radio" value="" />
                                      </div>
                                    </div>
                                  </div>
                                </li>
                              </ul>
                              <div class="col-12 d-flex justify-content-between mt-6">
                                <button class="btn btn-outline-secondary btn-prev">
                                  <i class="icon-base ri ri-arrow-left-line icon-16px"></i>
                                  <span class="align-middle d-sm-block d-none ms-2">Previous</span>
                                </button>
                                <button class="btn btn-primary btn-next">
                                  <span class="align-middle d-sm-block d-none me-2">Next</span>
                                  <i class="icon-base ri ri-arrow-right-line icon-16px"></i>
                                </button>
                              </div>
                            </div>

                            <!-- billing -->
                            <div id="billing" class="content">
                              <div id="AppNewCCForm" class="row g-5 pt-4 pt-lg-6 mb-6" onsubmit="return false">
                                <div class="col-12">
                                  <div class="input-group input-group-merge">
                                    <div class="form-floating form-floating-outline">
                                      <input
                                        class="form-control app-credit-card-mask"
                                        id="modalAppAddCardNumber"
                                        type="text"
                                        placeholder="1356 3215 6548 7898"
                                        aria-describedby="modalAppAddCard" />
                                      <label for="modalAppAddCardNumber">Card Number</label>
                                    </div>
                                    <span class="input-group-text cursor-pointer p-1" id="modalAppAddCard"
                                      ><span class="app-card-type"></span
                                    ></span>
                                  </div>
                                </div>
                                <div class="col-12 col-lg-6">
                                  <div class="form-floating form-floating-outline">
                                    <input
                                      type="text"
                                      class="form-control"
                                      id="modalAppAddCardName"
                                      placeholder="John Doe" />
                                    <label for="modalAppAddCardName">Name on Card</label>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3">
                                  <div class="form-floating form-floating-outline">
                                    <input
                                      type="text"
                                      class="form-control app-expiry-date-mask"
                                      id="modalAppAddCardDate"
                                      placeholder="MM/YY" />
                                    <label for="modalAppAddCardDate">Expiry</label>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3">
                                  <div class="input-group input-group-merge">
                                    <div class="form-floating form-floating-outline">
                                      <input
                                        type="text"
                                        id="modalAppAddCardCvv"
                                        class="form-control app-cvv-code-mask pe-2"
                                        maxlength="3"
                                        placeholder="654" />
                                      <label for="modalAppAddCardCvv" class="pe-1_5">CVV</label>
                                    </div>
                                    <span class="input-group-text cursor-pointer ps-0" id="modalAppAddCardCvv2"
                                      ><i
                                        class="icon-base ri ri-question-line"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="top"
                                        title="Card Verification Value"></i
                                    ></span>
                                  </div>
                                </div>
                                <div class="col-12">
                                  <div class="form-check form-switch">
                                    <input type="checkbox" class="form-check-input" id="appFutureAddress" checked />
                                    <label for="appFutureAddress" class="text-heading"
                                      >Save card for future billing?</label
                                    >
                                  </div>
                                </div>
                              </div>
                              <div class="col-12 d-flex justify-content-between mt-6">
                                <button class="btn btn-outline-secondary btn-prev">
                                  <i class="icon-base ri ri-arrow-left-line icon-16px"></i>
                                  <span class="align-middle d-sm-block d-none ms-2">Previous</span>
                                </button>
                                <button class="btn btn-primary btn-next">
                                  <span class="align-middle d-sm-block d-none me-2">Next</span>
                                  <i class="icon-base ri ri-arrow-right-line icon-16px"></i>
                                </button>
                              </div>
                            </div>

                            <!-- submit -->
                            <div id="submit" class="content text-center pt-4 pt-lg-0">
                              <h5 class="mb-1 mt-4">Submit</h5>
                              <p class="small">Submit to kick start your project.</p>
                              <!-- image -->
                              <img
                                src="../../assets/img/illustrations/create-app-modal-illustration-light.png"
                                alt="Create App img"
                                width="265"
                                class="img-fluid"
                                data-app-light-img="illustrations/create-app-modal-illustration-light.png"
                                data-app-dark-img="illustrations/create-app-modal-illustration-dark.png" />
                              <div class="col-12 d-flex justify-content-between mt-4 pt-2">
                                <button class="btn btn-outline-secondary btn-prev">
                                  <i class="icon-base ri ri-arrow-left-line icon-16px"></i>
                                  <span class="align-middle d-none d-sm-block ms-2">Previous</span>
                                </button>
                                <button class="btn btn-success btn-submit">
                                  <span class="align-middle d-none d-sm-block me-2">Submit</span
                                  ><i class="icon-base ri ri-check-line icon-18px"></i>
                                </button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>
                    <!--/ Property Listing Wizard -->
                  </div>
                </div>
              </div>
              <!--/ Create App Modal -->

              <!-- Two Factor Auth Modal -->

              <div class="modal fade" id="twoFactorAuth" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered modal-simple">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Select Authentication Method</h4>
                        <p>You also need to select a method by which the proxy authenticates to the directory serve.</p>
                      </div>
                      <div class="row pt-1">
                        <div class="col-12 mb-6">
                          <div class="form-check custom-option custom-option-basic">
                            <label
                              class="form-check-label custom-option-content"
                              for="customRadioTemp1"
                              data-bs-target="#twoFactorAuthOne"
                              data-bs-toggle="modal">
                              <input
                                name="customRadioTemp"
                                class="form-check-input"
                                type="radio"
                                value=""
                                id="customRadioTemp1"
                                checked />
                              <span class="custom-option-header">
                                <span class="h6 mb-0 d-flex align-items-center"
                                  ><i class="icon-base ri ri-settings-3-line icon-20px me-1"></i>Authenticator
                                  Apps</span
                                >
                              </span>
                              <span class="custom-option-body">
                                <small
                                  >Get code from an app like Google Authenticator or Microsoft Authenticator.</small
                                >
                              </span>
                            </label>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="form-check custom-option custom-option-basic">
                            <label
                              class="form-check-label custom-option-content"
                              for="customRadioTemp2"
                              data-bs-target="#twoFactorAuthTwo"
                              data-bs-toggle="modal">
                              <input
                                name="customRadioTemp"
                                class="form-check-input"
                                type="radio"
                                value=""
                                id="customRadioTemp2" />
                              <span class="custom-option-header">
                                <span class="h6 mb-0 d-flex align-items-center"
                                  ><i class="icon-base ri ri-wechat-line icon-20px me-1"></i>SMS</span
                                >
                              </span>
                              <span class="custom-option-body">
                                <small>We will send a code via SMS if you need to use your backup login method.</small>
                              </span>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Modal Authentication App -->
              <div class="modal fade" id="twoFactorAuthOne" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered modal-simple">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-0">Add Authenticator App</h4>
                      </div>
                      <h5 class="mb-2 text-break">Authenticator Apps</h5>
                      <p class="mb-6">
                        Using an authenticator app like Google Authenticator, Microsoft Authenticator, Authy, or
                        1Password, scan the QR code. It will generate a 6-digit code for you to enter below.
                      </p>
                      <div class="text-center mb-6">
                        <img src="../../assets/img/icons/misc/authentication-QR.png" alt="QR Code" width="150" />
                      </div>
                      <div class="alert alert-warning alert-dismissible mb-4" role="alert">
                        <h5 class="alert-heading mb-1 text-break">ASDLKNASDA9AHS678dGhASD78AB</h5>
                        <p class="mb-0">If you're having trouble using the QR code, select manual entry on your app</p>
                      </div>
                      <div class="form-floating form-floating-outline mb-6">
                        <input
                          type="email"
                          class="form-control"
                          id="twoFactorAuthInput"
                          placeholder="Enter Authentication Code" />
                        <label for="twoFactorAuthInput">Enter Authentication Code</label>
                      </div>
                      <div class="col-12 d-flex flex-wrap justify-content-start justify-content-sm-end gap-4 row-gap-4">
                        <button
                          type="button"
                          class="btn btn-outline-secondary me-3"
                          data-bs-toggle="modal"
                          data-bs-target="#twoFactorAuth">
                          <span class="align-middle">cancel</span>
                        </button>
                        <button type="button" class="btn btn-success" data-bs-dismiss="modal" aria-label="Close">
                          <span class="align-middle">submit</span
                          ><i class="icon-base ri ri-check-line icon-sm ms-1"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Modal Authentication via SMS -->
              <div class="modal fade" id="twoFactorAuthTwo" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered modal-simple">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <h5 class="mb-2">Verify Your Mobile Number for SMS</h5>
                      <p class="mb-6 text-truncate">
                        Enter your mobile phone number with country code, and we will send you a verification code.
                      </p>
                      <div class="form-floating form-floating-outline mb-6">
                        <input type="text" class="form-control" id="twoFactorAuthInputSms" placeholder="Phone Number" />
                        <label for="twoFactorAuthInputSms">Phone Number</label>
                      </div>
                      <div class="col-12 d-flex flex-wrap justify-content-start justify-content-sm-end gap-4 row-gap-4">
                        <button
                          type="button"
                          class="btn btn-outline-secondary"
                          data-bs-toggle="modal"
                          data-bs-target="#twoFactorAuth">
                          <span class="align-middle">cancel</span>
                        </button>
                        <button type="button" class="btn btn-success" data-bs-dismiss="modal" aria-label="Close">
                          <span class="align-middle">submit</span><i class="icon-base ri ri-check-line ms-1"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--/ Two Factor Auth Modal -->
              <script>
                // Check selected custom option
                window.Helpers.initCustomOptionCheck();
              </script>

              <!-- Payment provider modal -->
              <div class="modal fade" id="paymentProvider" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-enable-otp modal-dialog-centered">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Select payment providers</h4>
                        <p>Third-party payment providers</p>
                      </div>

                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom py-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0 me-12">Adyen</h6>
                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/master-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="master-card"
                            data-app-light-img="icons/payments/master-light.png"
                            data-app-dark-img="icons/payments/master-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/dc-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="diners-club-card"
                            data-app-light-img="icons/payments/dc-light.png"
                            data-app-dark-img="icons/payments/dc-dark.png" />
                        </div>
                      </div>
                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom pb-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0">2Checkout</h6>
                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/dc-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="diners-club-card"
                            data-app-light-img="icons/payments/dc-light.png"
                            data-app-dark-img="icons/payments/dc-dark.png" />
                        </div>
                      </div>
                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom pb-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0">Airpay</h6>
                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/master-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="master-card"
                            data-app-light-img="icons/payments/master-light.png"
                            data-app-dark-img="icons/payments/master-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                        </div>
                      </div>
                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom pb-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0">Authorize.net</h6>

                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/dc-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="diners-club-card"
                            data-app-light-img="icons/payments/dc-light.png"
                            data-app-dark-img="icons/payments/dc-dark.png" />
                        </div>
                      </div>
                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom pb-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0">Bambora</h6>

                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/master-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="master-card"
                            data-app-light-img="icons/payments/master-light.png"
                            data-app-dark-img="icons/payments/master-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                        </div>
                      </div>
                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom pb-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0">Cayan</h6>

                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/master-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="master-card"
                            data-app-light-img="icons/payments/master-light.png"
                            data-app-dark-img="icons/payments/master-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/dc-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="diners-club-card"
                            data-app-light-img="icons/payments/dc-light.png"
                            data-app-dark-img="icons/payments/dc-dark.png" />
                        </div>
                      </div>
                      <div
                        class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between border-bottom pb-4 mb-4">
                        <h6 class="m-0 mb-2 mb-sm-0">Chase Paymentech (Orbital)</h6>

                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/dc-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="diners-club-card"
                            data-app-light-img="icons/payments/dc-light.png"
                            data-app-dark-img="icons/payments/dc-dark.png" />
                        </div>
                      </div>
                      <div class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between">
                        <h6 class="m-0 mb-2 mb-sm-0">Checkout.com</h6>
                        <div class="d-flex flex-wrap gap-4">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />
                          <img
                            src="../../assets/img/icons/payments/master-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="master-card"
                            data-app-light-img="icons/payments/master-light.png"
                            data-app-dark-img="icons/payments/master-dark.png" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- / Payment provider modal -->

              <!-- Payment Methods modal -->
              <div class="modal fade" id="paymentMethods" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-simple modal-enable-otp modal-dialog-centered">
                  <div class="modal-content">
                    <div class="modal-body p-0">
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      <div class="text-center mb-6">
                        <h4 class="mb-2">Select payment methods</h4>
                        <p>Supported payment methods</p>
                      </div>

                      <div class="d-flex justify-content-between align-items-center border-bottom py-4 mb-4">
                        <div class="d-flex gap-4 align-items-center">
                          <img
                            src="../../assets/img/icons/payments/visa-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="visa-card"
                            data-app-light-img="icons/payments/visa-light.png"
                            data-app-dark-img="icons/payments/visa-dark.png" />

                          <h6 class="m-0">Visa</h6>
                        </div>
                        <p class="m-0 d-none d-sm-block">Credit Card</p>
                      </div>
                      <div class="d-flex justify-content-sm-between align-items-center border-bottom pb-4 mb-4">
                        <div class="d-flex gap-4 align-items-center">
                          <img
                            src="../../assets/img/icons/payments/ae-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="american-express-card"
                            data-app-light-img="icons/payments/ae-light.png"
                            data-app-dark-img="icons/payments/ae-dark.png" />

                          <h6 class="m-0">American Express</h6>
                        </div>
                        <p class="m-0 d-none d-sm-block">Credit Card</p>
                      </div>
                      <div class="d-flex justify-content-between align-items-center border-bottom pb-4 mb-4">
                        <div class="d-flex gap-4 align-items-center">
                          <img
                            src="../../assets/img/icons/payments/master-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="master-card"
                            data-app-light-img="icons/payments/master-light.png"
                            data-app-dark-img="icons/payments/master-dark.png" />

                          <h6 class="m-0">Mastercard</h6>
                        </div>
                        <p class="m-0 d-none d-sm-block">Credit Card</p>
                      </div>
                      <div class="d-flex justify-content-between align-items-center border-bottom pb-4 mb-4">
                        <div class="d-flex gap-4 align-items-center">
                          <img
                            src="../../assets/img/icons/payments/jcb-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="jcb-card"
                            data-app-light-img="icons/payments/jcb-light.png"
                            data-app-dark-img="icons/payments/jcb-dark.png" />
                          <h6 class="m-0">JCB</h6>
                        </div>
                        <p class="m-0 d-none d-sm-block">Credit Card</p>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex gap-4 align-items-center">
                          <img
                            src="../../assets/img/icons/payments/dc-light.png"
                            class="img-fluid w-px-50 h-px-30"
                            alt="diners-club-card"
                            data-app-light-img="icons/payments/dc-light.png"
                            data-app-dark-img="icons/payments/dc-dark.png" />
                          <h6 class="m-0">Diners Club</h6>
                        </div>
                        <p class="m-0 d-none d-sm-block">Credit Card</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- / Payment Methods modal -->
            </div>
            <!-- / Content -->

            <!-- Footer -->
            <footer class="content-footer footer bg-footer-theme">
              <div class="container-xxl">
                <div
                  class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                  <div class="mb-2 mb-md-0">
                    &#169;
                    <script>
                      document.write(new Date().getFullYear());
                    </script>
                    , made with ❤️ by
                    <a href="https://pixinvent.com" target="_blank" class="footer-link fw-medium">Pixinvent</a>
                  </div>
                  <div class="d-none d-lg-inline-block">
                    <a href="https://themeforest.net/licenses/standard" class="footer-link me-4" target="_blank"
                      >License</a
                    >

                    <a href="https://themeforest.net/user/pixinvent/portfolio" target="_blank" class="footer-link me-4"
                      >More Themes</a
                    >
                    <a
                      href="https://demos.pixinvent.com/materialize-html-admin-template/documentation/"
                      target="_blank"
                      class="footer-link me-4"
                      >Documentation</a
                    >

                    <a href="https://pixinvent.ticksy.com/" target="_blank" class="footer-link d-none d-sm-inline-block"
                      >Support</a
                    >
                  </div>
                </div>
              </div>
            </footer>
            <!-- / Footer -->

            <div class="content-backdrop fade"></div>
          </div>
          <!-- Content wrapper -->
        </div>
        <!-- / Layout page -->
      </div>

      <!-- Overlay -->
      <div class="layout-overlay layout-menu-toggle"></div>

      <!-- Drag Target Area To SlideIn Menu On Small Screens -->
      <div class="drag-target"></div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->

    <!-- build:js assets/vendor/js/theme.js  -->

    <script src="../../assets/vendor/libs/jquery/jquery.js"></script>

    <script src="../../assets/vendor/libs/popper/popper.js"></script>
    <script src="../../assets/vendor/js/bootstrap.js"></script>
    <script src="../../assets/vendor/libs/node-waves/node-waves.js"></script>

    <script src="../../assets/vendor/libs/@algolia/autocomplete-js.js"></script>

    <script src="../../assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>

    <script src="../../assets/vendor/libs/hammer/hammer.js"></script>

    <script src="../../assets/vendor/libs/i18n/i18n.js"></script>

    <script src="../../assets/vendor/js/menu.js"></script>

    <!-- endbuild -->

    <!-- Vendors JS -->
    <script src="../../assets/vendor/libs/cleave-zen/cleave-zen.js"></script>
    <script src="../../assets/vendor/libs/select2/select2.js"></script>
    <script src="../../assets/vendor/libs/tagify/tagify.js"></script>
    <script src="../../assets/vendor/libs/@form-validation/popular.js"></script>
    <script src="../../assets/vendor/libs/@form-validation/bootstrap5.js"></script>
    <script src="../../assets/vendor/libs/@form-validation/auto-focus.js"></script>
    <script src="../../assets/vendor/libs/bs-stepper/bs-stepper.js"></script>

    <!-- Main JS -->

    <script src="../../assets/js/main.js"></script>

    <!-- Page JS -->
    <script src="../../assets/js/pages-pricing.js"></script>
    <script src="../../assets/js/modal-create-app.js"></script>
    <script src="../../assets/js/modal-add-new-cc.js"></script>
    <script src="../../assets/js/modal-add-new-address.js"></script>
    <script src="../../assets/js/modal-edit-user.js"></script>
    <script src="../../assets/js/modal-enable-otp.js"></script>
    <script src="../../assets/js/modal-share-project.js"></script>
    <script src="../../assets/js/modal-two-factor-auth.js"></script>
  </body>
</html>
