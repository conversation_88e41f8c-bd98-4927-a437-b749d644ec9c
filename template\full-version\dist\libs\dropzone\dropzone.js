!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var r in t)("object"==typeof exports?exports:e)[r]=t[r]}}(self,(function(){return function(){var __webpack_modules__={"./libs/dropzone/dropzone.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dropzone: function() { return /* reexport default from dynamic */ dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dropzone/dist/dropzone */ \"./node_modules/dropzone/dist/dropzone.js\");\n/* harmony import */ var dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0__);\n\n(dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default().autoDiscover) = false;\n\n// File upload progress animation\n(dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default().prototype).uploadFiles = function (files) {\n  var minSteps = 6;\n  var maxSteps = 60;\n  var timeBetweenSteps = 100;\n  var bytesPerStep = 100000;\n  var isUploadSuccess = true;\n  var self = this;\n  for (var i = 0; i < files.length; i++) {\n    var file = files[i];\n    var totalSteps = Math.round(Math.min(maxSteps, Math.max(minSteps, file.size / bytesPerStep)));\n    for (var step = 0; step < totalSteps; step++) {\n      var duration = timeBetweenSteps * (step + 1);\n      setTimeout(function (file, totalSteps, step) {\n        return function () {\n          file.upload = {\n            progress: 100 * (step + 1) / totalSteps,\n            total: file.size,\n            bytesSent: (step + 1) * file.size / totalSteps\n          };\n          self.emit('uploadprogress', file, file.upload.progress, file.upload.bytesSent);\n          if (file.upload.progress === 100) {\n            if (isUploadSuccess) {\n              file.status = (dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default().SUCCESS);\n              self.emit('success', file, 'success', null);\n            } else {\n              file.status = (dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default().ERROR);\n              self.emit('error', file, 'Some upload error', null);\n            }\n            self.emit('complete', file);\n            self.processQueue();\n          }\n        };\n      }(file, totalSteps, step), duration);\n    }\n  }\n};\ntry {\n  window.Dropzone = (dropzone_dist_dropzone__WEBPACK_IMPORTED_MODULE_0___default());\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/dropzone/dropzone.js?")},"./node_modules/dropzone/dist/dropzone.js":function(module){eval("(function webpackUniversalModuleDefinition(root, factory) {\n\tif(true)\n\t\tmodule.exports = factory();\n\telse { var i, a; }\n})(self, function() {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 3099:\n/***/ (function(module) {\n\nmodule.exports = function (it) {\n  if (typeof it != 'function') {\n    throw TypeError(String(it) + ' is not a function');\n  } return it;\n};\n\n\n/***/ }),\n\n/***/ 6077:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_703__) {\n\nvar isObject = __nested_webpack_require_703__(111);\n\nmodule.exports = function (it) {\n  if (!isObject(it) && it !== null) {\n    throw TypeError(\"Can't set \" + String(it) + ' as a prototype');\n  } return it;\n};\n\n\n/***/ }),\n\n/***/ 1223:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_1001__) {\n\nvar wellKnownSymbol = __nested_webpack_require_1001__(5112);\nvar create = __nested_webpack_require_1001__(30);\nvar definePropertyModule = __nested_webpack_require_1001__(3070);\n\nvar UNSCOPABLES = wellKnownSymbol('unscopables');\nvar ArrayPrototype = Array.prototype;\n\n// Array.prototype[@@unscopables]\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\nif (ArrayPrototype[UNSCOPABLES] == undefined) {\n  definePropertyModule.f(ArrayPrototype, UNSCOPABLES, {\n    configurable: true,\n    value: create(null)\n  });\n}\n\n// add a key to Array.prototype[@@unscopables]\nmodule.exports = function (key) {\n  ArrayPrototype[UNSCOPABLES][key] = true;\n};\n\n\n/***/ }),\n\n/***/ 1530:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_1715__) {\n\n\"use strict\";\n\nvar charAt = __nested_webpack_require_1715__(8710).charAt;\n\n// `AdvanceStringIndex` abstract operation\n// https://tc39.es/ecma262/#sec-advancestringindex\nmodule.exports = function (S, index, unicode) {\n  return index + (unicode ? charAt(S, index).length : 1);\n};\n\n\n/***/ }),\n\n/***/ 5787:\n/***/ (function(module) {\n\nmodule.exports = function (it, Constructor, name) {\n  if (!(it instanceof Constructor)) {\n    throw TypeError('Incorrect ' + (name ? name + ' ' : '') + 'invocation');\n  } return it;\n};\n\n\n/***/ }),\n\n/***/ 9670:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_2317__) {\n\nvar isObject = __nested_webpack_require_2317__(111);\n\nmodule.exports = function (it) {\n  if (!isObject(it)) {\n    throw TypeError(String(it) + ' is not an object');\n  } return it;\n};\n\n\n/***/ }),\n\n/***/ 4019:\n/***/ (function(module) {\n\nmodule.exports = typeof ArrayBuffer !== 'undefined' && typeof DataView !== 'undefined';\n\n\n/***/ }),\n\n/***/ 260:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_2726__) {\n\n\"use strict\";\n\nvar NATIVE_ARRAY_BUFFER = __nested_webpack_require_2726__(4019);\nvar DESCRIPTORS = __nested_webpack_require_2726__(9781);\nvar global = __nested_webpack_require_2726__(7854);\nvar isObject = __nested_webpack_require_2726__(111);\nvar has = __nested_webpack_require_2726__(6656);\nvar classof = __nested_webpack_require_2726__(648);\nvar createNonEnumerableProperty = __nested_webpack_require_2726__(8880);\nvar redefine = __nested_webpack_require_2726__(1320);\nvar defineProperty = __nested_webpack_require_2726__(3070).f;\nvar getPrototypeOf = __nested_webpack_require_2726__(9518);\nvar setPrototypeOf = __nested_webpack_require_2726__(7674);\nvar wellKnownSymbol = __nested_webpack_require_2726__(5112);\nvar uid = __nested_webpack_require_2726__(9711);\n\nvar Int8Array = global.Int8Array;\nvar Int8ArrayPrototype = Int8Array && Int8Array.prototype;\nvar Uint8ClampedArray = global.Uint8ClampedArray;\nvar Uint8ClampedArrayPrototype = Uint8ClampedArray && Uint8ClampedArray.prototype;\nvar TypedArray = Int8Array && getPrototypeOf(Int8Array);\nvar TypedArrayPrototype = Int8ArrayPrototype && getPrototypeOf(Int8ArrayPrototype);\nvar ObjectPrototype = Object.prototype;\nvar isPrototypeOf = ObjectPrototype.isPrototypeOf;\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar TYPED_ARRAY_TAG = uid('TYPED_ARRAY_TAG');\n// Fixing native typed arrays in Opera Presto crashes the browser, see #595\nvar NATIVE_ARRAY_BUFFER_VIEWS = NATIVE_ARRAY_BUFFER && !!setPrototypeOf && classof(global.opera) !== 'Opera';\nvar TYPED_ARRAY_TAG_REQIRED = false;\nvar NAME;\n\nvar TypedArrayConstructorsList = {\n  Int8Array: 1,\n  Uint8Array: 1,\n  Uint8ClampedArray: 1,\n  Int16Array: 2,\n  Uint16Array: 2,\n  Int32Array: 4,\n  Uint32Array: 4,\n  Float32Array: 4,\n  Float64Array: 8\n};\n\nvar BigIntArrayConstructorsList = {\n  BigInt64Array: 8,\n  BigUint64Array: 8\n};\n\nvar isView = function isView(it) {\n  if (!isObject(it)) return false;\n  var klass = classof(it);\n  return klass === 'DataView'\n    || has(TypedArrayConstructorsList, klass)\n    || has(BigIntArrayConstructorsList, klass);\n};\n\nvar isTypedArray = function (it) {\n  if (!isObject(it)) return false;\n  var klass = classof(it);\n  return has(TypedArrayConstructorsList, klass)\n    || has(BigIntArrayConstructorsList, klass);\n};\n\nvar aTypedArray = function (it) {\n  if (isTypedArray(it)) return it;\n  throw TypeError('Target is not a typed array');\n};\n\nvar aTypedArrayConstructor = function (C) {\n  if (setPrototypeOf) {\n    if (isPrototypeOf.call(TypedArray, C)) return C;\n  } else for (var ARRAY in TypedArrayConstructorsList) if (has(TypedArrayConstructorsList, NAME)) {\n    var TypedArrayConstructor = global[ARRAY];\n    if (TypedArrayConstructor && (C === TypedArrayConstructor || isPrototypeOf.call(TypedArrayConstructor, C))) {\n      return C;\n    }\n  } throw TypeError('Target is not a typed array constructor');\n};\n\nvar exportTypedArrayMethod = function (KEY, property, forced) {\n  if (!DESCRIPTORS) return;\n  if (forced) for (var ARRAY in TypedArrayConstructorsList) {\n    var TypedArrayConstructor = global[ARRAY];\n    if (TypedArrayConstructor && has(TypedArrayConstructor.prototype, KEY)) {\n      delete TypedArrayConstructor.prototype[KEY];\n    }\n  }\n  if (!TypedArrayPrototype[KEY] || forced) {\n    redefine(TypedArrayPrototype, KEY, forced ? property\n      : NATIVE_ARRAY_BUFFER_VIEWS && Int8ArrayPrototype[KEY] || property);\n  }\n};\n\nvar exportTypedArrayStaticMethod = function (KEY, property, forced) {\n  var ARRAY, TypedArrayConstructor;\n  if (!DESCRIPTORS) return;\n  if (setPrototypeOf) {\n    if (forced) for (ARRAY in TypedArrayConstructorsList) {\n      TypedArrayConstructor = global[ARRAY];\n      if (TypedArrayConstructor && has(TypedArrayConstructor, KEY)) {\n        delete TypedArrayConstructor[KEY];\n      }\n    }\n    if (!TypedArray[KEY] || forced) {\n      // V8 ~ Chrome 49-50 `%TypedArray%` methods are non-writable non-configurable\n      try {\n        return redefine(TypedArray, KEY, forced ? property : NATIVE_ARRAY_BUFFER_VIEWS && Int8Array[KEY] || property);\n      } catch (error) { /* empty */ }\n    } else return;\n  }\n  for (ARRAY in TypedArrayConstructorsList) {\n    TypedArrayConstructor = global[ARRAY];\n    if (TypedArrayConstructor && (!TypedArrayConstructor[KEY] || forced)) {\n      redefine(TypedArrayConstructor, KEY, property);\n    }\n  }\n};\n\nfor (NAME in TypedArrayConstructorsList) {\n  if (!global[NAME]) NATIVE_ARRAY_BUFFER_VIEWS = false;\n}\n\n// WebKit bug - typed arrays constructors prototype is Object.prototype\nif (!NATIVE_ARRAY_BUFFER_VIEWS || typeof TypedArray != 'function' || TypedArray === Function.prototype) {\n  // eslint-disable-next-line no-shadow -- safe\n  TypedArray = function TypedArray() {\n    throw TypeError('Incorrect invocation');\n  };\n  if (NATIVE_ARRAY_BUFFER_VIEWS) for (NAME in TypedArrayConstructorsList) {\n    if (global[NAME]) setPrototypeOf(global[NAME], TypedArray);\n  }\n}\n\nif (!NATIVE_ARRAY_BUFFER_VIEWS || !TypedArrayPrototype || TypedArrayPrototype === ObjectPrototype) {\n  TypedArrayPrototype = TypedArray.prototype;\n  if (NATIVE_ARRAY_BUFFER_VIEWS) for (NAME in TypedArrayConstructorsList) {\n    if (global[NAME]) setPrototypeOf(global[NAME].prototype, TypedArrayPrototype);\n  }\n}\n\n// WebKit bug - one more object in Uint8ClampedArray prototype chain\nif (NATIVE_ARRAY_BUFFER_VIEWS && getPrototypeOf(Uint8ClampedArrayPrototype) !== TypedArrayPrototype) {\n  setPrototypeOf(Uint8ClampedArrayPrototype, TypedArrayPrototype);\n}\n\nif (DESCRIPTORS && !has(TypedArrayPrototype, TO_STRING_TAG)) {\n  TYPED_ARRAY_TAG_REQIRED = true;\n  defineProperty(TypedArrayPrototype, TO_STRING_TAG, { get: function () {\n    return isObject(this) ? this[TYPED_ARRAY_TAG] : undefined;\n  } });\n  for (NAME in TypedArrayConstructorsList) if (global[NAME]) {\n    createNonEnumerableProperty(global[NAME], TYPED_ARRAY_TAG, NAME);\n  }\n}\n\nmodule.exports = {\n  NATIVE_ARRAY_BUFFER_VIEWS: NATIVE_ARRAY_BUFFER_VIEWS,\n  TYPED_ARRAY_TAG: TYPED_ARRAY_TAG_REQIRED && TYPED_ARRAY_TAG,\n  aTypedArray: aTypedArray,\n  aTypedArrayConstructor: aTypedArrayConstructor,\n  exportTypedArrayMethod: exportTypedArrayMethod,\n  exportTypedArrayStaticMethod: exportTypedArrayStaticMethod,\n  isView: isView,\n  isTypedArray: isTypedArray,\n  TypedArray: TypedArray,\n  TypedArrayPrototype: TypedArrayPrototype\n};\n\n\n/***/ }),\n\n/***/ 3331:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_8934__) {\n\n\"use strict\";\n\nvar global = __nested_webpack_require_8934__(7854);\nvar DESCRIPTORS = __nested_webpack_require_8934__(9781);\nvar NATIVE_ARRAY_BUFFER = __nested_webpack_require_8934__(4019);\nvar createNonEnumerableProperty = __nested_webpack_require_8934__(8880);\nvar redefineAll = __nested_webpack_require_8934__(2248);\nvar fails = __nested_webpack_require_8934__(7293);\nvar anInstance = __nested_webpack_require_8934__(5787);\nvar toInteger = __nested_webpack_require_8934__(9958);\nvar toLength = __nested_webpack_require_8934__(7466);\nvar toIndex = __nested_webpack_require_8934__(7067);\nvar IEEE754 = __nested_webpack_require_8934__(1179);\nvar getPrototypeOf = __nested_webpack_require_8934__(9518);\nvar setPrototypeOf = __nested_webpack_require_8934__(7674);\nvar getOwnPropertyNames = __nested_webpack_require_8934__(8006).f;\nvar defineProperty = __nested_webpack_require_8934__(3070).f;\nvar arrayFill = __nested_webpack_require_8934__(1285);\nvar setToStringTag = __nested_webpack_require_8934__(8003);\nvar InternalStateModule = __nested_webpack_require_8934__(9909);\n\nvar getInternalState = InternalStateModule.get;\nvar setInternalState = InternalStateModule.set;\nvar ARRAY_BUFFER = 'ArrayBuffer';\nvar DATA_VIEW = 'DataView';\nvar PROTOTYPE = 'prototype';\nvar WRONG_LENGTH = 'Wrong length';\nvar WRONG_INDEX = 'Wrong index';\nvar NativeArrayBuffer = global[ARRAY_BUFFER];\nvar $ArrayBuffer = NativeArrayBuffer;\nvar $DataView = global[DATA_VIEW];\nvar $DataViewPrototype = $DataView && $DataView[PROTOTYPE];\nvar ObjectPrototype = Object.prototype;\nvar RangeError = global.RangeError;\n\nvar packIEEE754 = IEEE754.pack;\nvar unpackIEEE754 = IEEE754.unpack;\n\nvar packInt8 = function (number) {\n  return [number & 0xFF];\n};\n\nvar packInt16 = function (number) {\n  return [number & 0xFF, number >> 8 & 0xFF];\n};\n\nvar packInt32 = function (number) {\n  return [number & 0xFF, number >> 8 & 0xFF, number >> 16 & 0xFF, number >> 24 & 0xFF];\n};\n\nvar unpackInt32 = function (buffer) {\n  return buffer[3] << 24 | buffer[2] << 16 | buffer[1] << 8 | buffer[0];\n};\n\nvar packFloat32 = function (number) {\n  return packIEEE754(number, 23, 4);\n};\n\nvar packFloat64 = function (number) {\n  return packIEEE754(number, 52, 8);\n};\n\nvar addGetter = function (Constructor, key) {\n  defineProperty(Constructor[PROTOTYPE], key, { get: function () { return getInternalState(this)[key]; } });\n};\n\nvar get = function (view, count, index, isLittleEndian) {\n  var intIndex = toIndex(index);\n  var store = getInternalState(view);\n  if (intIndex + count > store.byteLength) throw RangeError(WRONG_INDEX);\n  var bytes = getInternalState(store.buffer).bytes;\n  var start = intIndex + store.byteOffset;\n  var pack = bytes.slice(start, start + count);\n  return isLittleEndian ? pack : pack.reverse();\n};\n\nvar set = function (view, count, index, conversion, value, isLittleEndian) {\n  var intIndex = toIndex(index);\n  var store = getInternalState(view);\n  if (intIndex + count > store.byteLength) throw RangeError(WRONG_INDEX);\n  var bytes = getInternalState(store.buffer).bytes;\n  var start = intIndex + store.byteOffset;\n  var pack = conversion(+value);\n  for (var i = 0; i < count; i++) bytes[start + i] = pack[isLittleEndian ? i : count - i - 1];\n};\n\nif (!NATIVE_ARRAY_BUFFER) {\n  $ArrayBuffer = function ArrayBuffer(length) {\n    anInstance(this, $ArrayBuffer, ARRAY_BUFFER);\n    var byteLength = toIndex(length);\n    setInternalState(this, {\n      bytes: arrayFill.call(new Array(byteLength), 0),\n      byteLength: byteLength\n    });\n    if (!DESCRIPTORS) this.byteLength = byteLength;\n  };\n\n  $DataView = function DataView(buffer, byteOffset, byteLength) {\n    anInstance(this, $DataView, DATA_VIEW);\n    anInstance(buffer, $ArrayBuffer, DATA_VIEW);\n    var bufferLength = getInternalState(buffer).byteLength;\n    var offset = toInteger(byteOffset);\n    if (offset < 0 || offset > bufferLength) throw RangeError('Wrong offset');\n    byteLength = byteLength === undefined ? bufferLength - offset : toLength(byteLength);\n    if (offset + byteLength > bufferLength) throw RangeError(WRONG_LENGTH);\n    setInternalState(this, {\n      buffer: buffer,\n      byteLength: byteLength,\n      byteOffset: offset\n    });\n    if (!DESCRIPTORS) {\n      this.buffer = buffer;\n      this.byteLength = byteLength;\n      this.byteOffset = offset;\n    }\n  };\n\n  if (DESCRIPTORS) {\n    addGetter($ArrayBuffer, 'byteLength');\n    addGetter($DataView, 'buffer');\n    addGetter($DataView, 'byteLength');\n    addGetter($DataView, 'byteOffset');\n  }\n\n  redefineAll($DataView[PROTOTYPE], {\n    getInt8: function getInt8(byteOffset) {\n      return get(this, 1, byteOffset)[0] << 24 >> 24;\n    },\n    getUint8: function getUint8(byteOffset) {\n      return get(this, 1, byteOffset)[0];\n    },\n    getInt16: function getInt16(byteOffset /* , littleEndian */) {\n      var bytes = get(this, 2, byteOffset, arguments.length > 1 ? arguments[1] : undefined);\n      return (bytes[1] << 8 | bytes[0]) << 16 >> 16;\n    },\n    getUint16: function getUint16(byteOffset /* , littleEndian */) {\n      var bytes = get(this, 2, byteOffset, arguments.length > 1 ? arguments[1] : undefined);\n      return bytes[1] << 8 | bytes[0];\n    },\n    getInt32: function getInt32(byteOffset /* , littleEndian */) {\n      return unpackInt32(get(this, 4, byteOffset, arguments.length > 1 ? arguments[1] : undefined));\n    },\n    getUint32: function getUint32(byteOffset /* , littleEndian */) {\n      return unpackInt32(get(this, 4, byteOffset, arguments.length > 1 ? arguments[1] : undefined)) >>> 0;\n    },\n    getFloat32: function getFloat32(byteOffset /* , littleEndian */) {\n      return unpackIEEE754(get(this, 4, byteOffset, arguments.length > 1 ? arguments[1] : undefined), 23);\n    },\n    getFloat64: function getFloat64(byteOffset /* , littleEndian */) {\n      return unpackIEEE754(get(this, 8, byteOffset, arguments.length > 1 ? arguments[1] : undefined), 52);\n    },\n    setInt8: function setInt8(byteOffset, value) {\n      set(this, 1, byteOffset, packInt8, value);\n    },\n    setUint8: function setUint8(byteOffset, value) {\n      set(this, 1, byteOffset, packInt8, value);\n    },\n    setInt16: function setInt16(byteOffset, value /* , littleEndian */) {\n      set(this, 2, byteOffset, packInt16, value, arguments.length > 2 ? arguments[2] : undefined);\n    },\n    setUint16: function setUint16(byteOffset, value /* , littleEndian */) {\n      set(this, 2, byteOffset, packInt16, value, arguments.length > 2 ? arguments[2] : undefined);\n    },\n    setInt32: function setInt32(byteOffset, value /* , littleEndian */) {\n      set(this, 4, byteOffset, packInt32, value, arguments.length > 2 ? arguments[2] : undefined);\n    },\n    setUint32: function setUint32(byteOffset, value /* , littleEndian */) {\n      set(this, 4, byteOffset, packInt32, value, arguments.length > 2 ? arguments[2] : undefined);\n    },\n    setFloat32: function setFloat32(byteOffset, value /* , littleEndian */) {\n      set(this, 4, byteOffset, packFloat32, value, arguments.length > 2 ? arguments[2] : undefined);\n    },\n    setFloat64: function setFloat64(byteOffset, value /* , littleEndian */) {\n      set(this, 8, byteOffset, packFloat64, value, arguments.length > 2 ? arguments[2] : undefined);\n    }\n  });\n} else {\n  /* eslint-disable no-new -- required for testing */\n  if (!fails(function () {\n    NativeArrayBuffer(1);\n  }) || !fails(function () {\n    new NativeArrayBuffer(-1);\n  }) || fails(function () {\n    new NativeArrayBuffer();\n    new NativeArrayBuffer(1.5);\n    new NativeArrayBuffer(NaN);\n    return NativeArrayBuffer.name != ARRAY_BUFFER;\n  })) {\n  /* eslint-enable no-new -- required for testing */\n    $ArrayBuffer = function ArrayBuffer(length) {\n      anInstance(this, $ArrayBuffer);\n      return new NativeArrayBuffer(toIndex(length));\n    };\n    var ArrayBufferPrototype = $ArrayBuffer[PROTOTYPE] = NativeArrayBuffer[PROTOTYPE];\n    for (var keys = getOwnPropertyNames(NativeArrayBuffer), j = 0, key; keys.length > j;) {\n      if (!((key = keys[j++]) in $ArrayBuffer)) {\n        createNonEnumerableProperty($ArrayBuffer, key, NativeArrayBuffer[key]);\n      }\n    }\n    ArrayBufferPrototype.constructor = $ArrayBuffer;\n  }\n\n  // WebKit bug - the same parent prototype for typed arrays and data view\n  if (setPrototypeOf && getPrototypeOf($DataViewPrototype) !== ObjectPrototype) {\n    setPrototypeOf($DataViewPrototype, ObjectPrototype);\n  }\n\n  // iOS Safari 7.x bug\n  var testView = new $DataView(new $ArrayBuffer(2));\n  var nativeSetInt8 = $DataViewPrototype.setInt8;\n  testView.setInt8(0, 2147483648);\n  testView.setInt8(1, 2147483649);\n  if (testView.getInt8(0) || !testView.getInt8(1)) redefineAll($DataViewPrototype, {\n    setInt8: function setInt8(byteOffset, value) {\n      nativeSetInt8.call(this, byteOffset, value << 24 >> 24);\n    },\n    setUint8: function setUint8(byteOffset, value) {\n      nativeSetInt8.call(this, byteOffset, value << 24 >> 24);\n    }\n  }, { unsafe: true });\n}\n\nsetToStringTag($ArrayBuffer, ARRAY_BUFFER);\nsetToStringTag($DataView, DATA_VIEW);\n\nmodule.exports = {\n  ArrayBuffer: $ArrayBuffer,\n  DataView: $DataView\n};\n\n\n/***/ }),\n\n/***/ 1048:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_17881__) {\n\n\"use strict\";\n\nvar toObject = __nested_webpack_require_17881__(7908);\nvar toAbsoluteIndex = __nested_webpack_require_17881__(1400);\nvar toLength = __nested_webpack_require_17881__(7466);\n\nvar min = Math.min;\n\n// `Array.prototype.copyWithin` method implementation\n// https://tc39.es/ecma262/#sec-array.prototype.copywithin\nmodule.exports = [].copyWithin || function copyWithin(target /* = 0 */, start /* = 0, end = @length */) {\n  var O = toObject(this);\n  var len = toLength(O.length);\n  var to = toAbsoluteIndex(target, len);\n  var from = toAbsoluteIndex(start, len);\n  var end = arguments.length > 2 ? arguments[2] : undefined;\n  var count = min((end === undefined ? len : toAbsoluteIndex(end, len)) - from, len - to);\n  var inc = 1;\n  if (from < to && to < from + count) {\n    inc = -1;\n    from += count - 1;\n    to += count - 1;\n  }\n  while (count-- > 0) {\n    if (from in O) O[to] = O[from];\n    else delete O[to];\n    to += inc;\n    from += inc;\n  } return O;\n};\n\n\n/***/ }),\n\n/***/ 1285:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_18911__) {\n\n\"use strict\";\n\nvar toObject = __nested_webpack_require_18911__(7908);\nvar toAbsoluteIndex = __nested_webpack_require_18911__(1400);\nvar toLength = __nested_webpack_require_18911__(7466);\n\n// `Array.prototype.fill` method implementation\n// https://tc39.es/ecma262/#sec-array.prototype.fill\nmodule.exports = function fill(value /* , start = 0, end = @length */) {\n  var O = toObject(this);\n  var length = toLength(O.length);\n  var argumentsLength = arguments.length;\n  var index = toAbsoluteIndex(argumentsLength > 1 ? arguments[1] : undefined, length);\n  var end = argumentsLength > 2 ? arguments[2] : undefined;\n  var endPos = end === undefined ? length : toAbsoluteIndex(end, length);\n  while (endPos > index) O[index++] = value;\n  return O;\n};\n\n\n/***/ }),\n\n/***/ 8533:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_19717__) {\n\n\"use strict\";\n\nvar $forEach = __nested_webpack_require_19717__(2092).forEach;\nvar arrayMethodIsStrict = __nested_webpack_require_19717__(9341);\n\nvar STRICT_METHOD = arrayMethodIsStrict('forEach');\n\n// `Array.prototype.forEach` method implementation\n// https://tc39.es/ecma262/#sec-array.prototype.foreach\nmodule.exports = !STRICT_METHOD ? function forEach(callbackfn /* , thisArg */) {\n  return $forEach(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n} : [].forEach;\n\n\n/***/ }),\n\n/***/ 8457:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_20278__) {\n\n\"use strict\";\n\nvar bind = __nested_webpack_require_20278__(9974);\nvar toObject = __nested_webpack_require_20278__(7908);\nvar callWithSafeIterationClosing = __nested_webpack_require_20278__(3411);\nvar isArrayIteratorMethod = __nested_webpack_require_20278__(7659);\nvar toLength = __nested_webpack_require_20278__(7466);\nvar createProperty = __nested_webpack_require_20278__(6135);\nvar getIteratorMethod = __nested_webpack_require_20278__(1246);\n\n// `Array.from` method implementation\n// https://tc39.es/ecma262/#sec-array.from\nmodule.exports = function from(arrayLike /* , mapfn = undefined, thisArg = undefined */) {\n  var O = toObject(arrayLike);\n  var C = typeof this == 'function' ? this : Array;\n  var argumentsLength = arguments.length;\n  var mapfn = argumentsLength > 1 ? arguments[1] : undefined;\n  var mapping = mapfn !== undefined;\n  var iteratorMethod = getIteratorMethod(O);\n  var index = 0;\n  var length, result, step, iterator, next, value;\n  if (mapping) mapfn = bind(mapfn, argumentsLength > 2 ? arguments[2] : undefined, 2);\n  // if the target is not iterable or it's an array with the default iterator - use a simple case\n  if (iteratorMethod != undefined && !(C == Array && isArrayIteratorMethod(iteratorMethod))) {\n    iterator = iteratorMethod.call(O);\n    next = iterator.next;\n    result = new C();\n    for (;!(step = next.call(iterator)).done; index++) {\n      value = mapping ? callWithSafeIterationClosing(iterator, mapfn, [step.value, index], true) : step.value;\n      createProperty(result, index, value);\n    }\n  } else {\n    length = toLength(O.length);\n    result = new C(length);\n    for (;length > index; index++) {\n      value = mapping ? mapfn(O[index], index) : O[index];\n      createProperty(result, index, value);\n    }\n  }\n  result.length = index;\n  return result;\n};\n\n\n/***/ }),\n\n/***/ 1318:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_22092__) {\n\nvar toIndexedObject = __nested_webpack_require_22092__(5656);\nvar toLength = __nested_webpack_require_22092__(7466);\nvar toAbsoluteIndex = __nested_webpack_require_22092__(1400);\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = toLength(O.length);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n\n\n/***/ }),\n\n/***/ 2092:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_23446__) {\n\nvar bind = __nested_webpack_require_23446__(9974);\nvar IndexedObject = __nested_webpack_require_23446__(8361);\nvar toObject = __nested_webpack_require_23446__(7908);\nvar toLength = __nested_webpack_require_23446__(7466);\nvar arraySpeciesCreate = __nested_webpack_require_23446__(5417);\n\nvar push = [].push;\n\n// `Array.prototype.{ forEach, map, filter, some, every, find, findIndex, filterOut }` methods implementation\nvar createMethod = function (TYPE) {\n  var IS_MAP = TYPE == 1;\n  var IS_FILTER = TYPE == 2;\n  var IS_SOME = TYPE == 3;\n  var IS_EVERY = TYPE == 4;\n  var IS_FIND_INDEX = TYPE == 6;\n  var IS_FILTER_OUT = TYPE == 7;\n  var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;\n  return function ($this, callbackfn, that, specificCreate) {\n    var O = toObject($this);\n    var self = IndexedObject(O);\n    var boundFunction = bind(callbackfn, that, 3);\n    var length = toLength(self.length);\n    var index = 0;\n    var create = specificCreate || arraySpeciesCreate;\n    var target = IS_MAP ? create($this, length) : IS_FILTER || IS_FILTER_OUT ? create($this, 0) : undefined;\n    var value, result;\n    for (;length > index; index++) if (NO_HOLES || index in self) {\n      value = self[index];\n      result = boundFunction(value, index, O);\n      if (TYPE) {\n        if (IS_MAP) target[index] = result; // map\n        else if (result) switch (TYPE) {\n          case 3: return true;              // some\n          case 5: return value;             // find\n          case 6: return index;             // findIndex\n          case 2: push.call(target, value); // filter\n        } else switch (TYPE) {\n          case 4: return false;             // every\n          case 7: push.call(target, value); // filterOut\n        }\n      }\n    }\n    return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.forEach` method\n  // https://tc39.es/ecma262/#sec-array.prototype.foreach\n  forEach: createMethod(0),\n  // `Array.prototype.map` method\n  // https://tc39.es/ecma262/#sec-array.prototype.map\n  map: createMethod(1),\n  // `Array.prototype.filter` method\n  // https://tc39.es/ecma262/#sec-array.prototype.filter\n  filter: createMethod(2),\n  // `Array.prototype.some` method\n  // https://tc39.es/ecma262/#sec-array.prototype.some\n  some: createMethod(3),\n  // `Array.prototype.every` method\n  // https://tc39.es/ecma262/#sec-array.prototype.every\n  every: createMethod(4),\n  // `Array.prototype.find` method\n  // https://tc39.es/ecma262/#sec-array.prototype.find\n  find: createMethod(5),\n  // `Array.prototype.findIndex` method\n  // https://tc39.es/ecma262/#sec-array.prototype.findIndex\n  findIndex: createMethod(6),\n  // `Array.prototype.filterOut` method\n  // https://github.com/tc39/proposal-array-filtering\n  filterOut: createMethod(7)\n};\n\n\n/***/ }),\n\n/***/ 6583:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_26271__) {\n\n\"use strict\";\n\nvar toIndexedObject = __nested_webpack_require_26271__(5656);\nvar toInteger = __nested_webpack_require_26271__(9958);\nvar toLength = __nested_webpack_require_26271__(7466);\nvar arrayMethodIsStrict = __nested_webpack_require_26271__(9341);\n\nvar min = Math.min;\nvar nativeLastIndexOf = [].lastIndexOf;\nvar NEGATIVE_ZERO = !!nativeLastIndexOf && 1 / [1].lastIndexOf(1, -0) < 0;\nvar STRICT_METHOD = arrayMethodIsStrict('lastIndexOf');\nvar FORCED = NEGATIVE_ZERO || !STRICT_METHOD;\n\n// `Array.prototype.lastIndexOf` method implementation\n// https://tc39.es/ecma262/#sec-array.prototype.lastindexof\nmodule.exports = FORCED ? function lastIndexOf(searchElement /* , fromIndex = @[*-1] */) {\n  // convert -0 to +0\n  if (NEGATIVE_ZERO) return nativeLastIndexOf.apply(this, arguments) || 0;\n  var O = toIndexedObject(this);\n  var length = toLength(O.length);\n  var index = length - 1;\n  if (arguments.length > 1) index = min(index, toInteger(arguments[1]));\n  if (index < 0) index = length + index;\n  for (;index >= 0; index--) if (index in O && O[index] === searchElement) return index || 0;\n  return -1;\n} : nativeLastIndexOf;\n\n\n/***/ }),\n\n/***/ 1194:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_27452__) {\n\nvar fails = __nested_webpack_require_27452__(7293);\nvar wellKnownSymbol = __nested_webpack_require_27452__(5112);\nvar V8_VERSION = __nested_webpack_require_27452__(7392);\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (METHOD_NAME) {\n  // We can't use this feature detection in V8 since it causes\n  // deoptimization and serious performance degradation\n  // https://github.com/zloirock/core-js/issues/677\n  return V8_VERSION >= 51 || !fails(function () {\n    var array = [];\n    var constructor = array.constructor = {};\n    constructor[SPECIES] = function () {\n      return { foo: 1 };\n    };\n    return array[METHOD_NAME](Boolean).foo !== 1;\n  });\n};\n\n\n/***/ }),\n\n/***/ 9341:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_28188__) {\n\n\"use strict\";\n\nvar fails = __nested_webpack_require_28188__(7293);\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n\n\n/***/ }),\n\n/***/ 3671:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_28629__) {\n\nvar aFunction = __nested_webpack_require_28629__(3099);\nvar toObject = __nested_webpack_require_28629__(7908);\nvar IndexedObject = __nested_webpack_require_28629__(8361);\nvar toLength = __nested_webpack_require_28629__(7466);\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n\n\n/***/ }),\n\n/***/ 5417:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_30017__) {\n\nvar isObject = __nested_webpack_require_30017__(111);\nvar isArray = __nested_webpack_require_30017__(3157);\nvar wellKnownSymbol = __nested_webpack_require_30017__(5112);\n\nvar SPECIES = wellKnownSymbol('species');\n\n// `ArraySpeciesCreate` abstract operation\n// https://tc39.es/ecma262/#sec-arrayspeciescreate\nmodule.exports = function (originalArray, length) {\n  var C;\n  if (isArray(originalArray)) {\n    C = originalArray.constructor;\n    // cross-realm fallback\n    if (typeof C == 'function' && (C === Array || isArray(C.prototype))) C = undefined;\n    else if (isObject(C)) {\n      C = C[SPECIES];\n      if (C === null) C = undefined;\n    }\n  } return new (C === undefined ? Array : C)(length === 0 ? 0 : length);\n};\n\n\n/***/ }),\n\n/***/ 3411:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_30798__) {\n\nvar anObject = __nested_webpack_require_30798__(9670);\nvar iteratorClose = __nested_webpack_require_30798__(9212);\n\n// call something on iterator step with safe closing on error\nmodule.exports = function (iterator, fn, value, ENTRIES) {\n  try {\n    return ENTRIES ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch (error) {\n    iteratorClose(iterator);\n    throw error;\n  }\n};\n\n\n/***/ }),\n\n/***/ 7072:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_31303__) {\n\nvar wellKnownSymbol = __nested_webpack_require_31303__(5112);\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var called = 0;\n  var iteratorWithReturn = {\n    next: function () {\n      return { done: !!called++ };\n    },\n    'return': function () {\n      SAFE_CLOSING = true;\n    }\n  };\n  iteratorWithReturn[ITERATOR] = function () {\n    return this;\n  };\n  // eslint-disable-next-line no-throw-literal -- required for testing\n  Array.from(iteratorWithReturn, function () { throw 2; });\n} catch (error) { /* empty */ }\n\nmodule.exports = function (exec, SKIP_CLOSING) {\n  if (!SKIP_CLOSING && !SAFE_CLOSING) return false;\n  var ITERATION_SUPPORT = false;\n  try {\n    var object = {};\n    object[ITERATOR] = function () {\n      return {\n        next: function () {\n          return { done: ITERATION_SUPPORT = true };\n        }\n      };\n    };\n    exec(object);\n  } catch (error) { /* empty */ }\n  return ITERATION_SUPPORT;\n};\n\n\n/***/ }),\n\n/***/ 4326:\n/***/ (function(module) {\n\nvar toString = {}.toString;\n\nmodule.exports = function (it) {\n  return toString.call(it).slice(8, -1);\n};\n\n\n/***/ }),\n\n/***/ 648:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_32503__) {\n\nvar TO_STRING_TAG_SUPPORT = __nested_webpack_require_32503__(1694);\nvar classofRaw = __nested_webpack_require_32503__(4326);\nvar wellKnownSymbol = __nested_webpack_require_32503__(5112);\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : result;\n};\n\n\n/***/ }),\n\n/***/ 9920:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_33565__) {\n\nvar has = __nested_webpack_require_33565__(6656);\nvar ownKeys = __nested_webpack_require_33565__(3887);\nvar getOwnPropertyDescriptorModule = __nested_webpack_require_33565__(1236);\nvar definePropertyModule = __nested_webpack_require_33565__(3070);\n\nmodule.exports = function (target, source) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!has(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n  }\n};\n\n\n/***/ }),\n\n/***/ 8544:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_34217__) {\n\nvar fails = __nested_webpack_require_34217__(7293);\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n\n\n/***/ }),\n\n/***/ 4994:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_34520__) {\n\n\"use strict\";\n\nvar IteratorPrototype = __nested_webpack_require_34520__(3383).IteratorPrototype;\nvar create = __nested_webpack_require_34520__(30);\nvar createPropertyDescriptor = __nested_webpack_require_34520__(9114);\nvar setToStringTag = __nested_webpack_require_34520__(8003);\nvar Iterators = __nested_webpack_require_34520__(7497);\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (IteratorConstructor, NAME, next) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(1, next) });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};\n\n\n/***/ }),\n\n/***/ 8880:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_35290__) {\n\nvar DESCRIPTORS = __nested_webpack_require_35290__(9781);\nvar definePropertyModule = __nested_webpack_require_35290__(3070);\nvar createPropertyDescriptor = __nested_webpack_require_35290__(9114);\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n\n\n/***/ }),\n\n/***/ 9114:\n/***/ (function(module) {\n\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n\n\n/***/ }),\n\n/***/ 6135:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_35996__) {\n\n\"use strict\";\n\nvar toPrimitive = __nested_webpack_require_35996__(7593);\nvar definePropertyModule = __nested_webpack_require_35996__(3070);\nvar createPropertyDescriptor = __nested_webpack_require_35996__(9114);\n\nmodule.exports = function (object, key, value) {\n  var propertyKey = toPrimitive(key);\n  if (propertyKey in object) definePropertyModule.f(object, propertyKey, createPropertyDescriptor(0, value));\n  else object[propertyKey] = value;\n};\n\n\n/***/ }),\n\n/***/ 654:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_36503__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_36503__(2109);\nvar createIteratorConstructor = __nested_webpack_require_36503__(4994);\nvar getPrototypeOf = __nested_webpack_require_36503__(9518);\nvar setPrototypeOf = __nested_webpack_require_36503__(7674);\nvar setToStringTag = __nested_webpack_require_36503__(8003);\nvar createNonEnumerableProperty = __nested_webpack_require_36503__(8880);\nvar redefine = __nested_webpack_require_36503__(1320);\nvar wellKnownSymbol = __nested_webpack_require_36503__(5112);\nvar IS_PURE = __nested_webpack_require_36503__(1913);\nvar Iterators = __nested_webpack_require_36503__(7497);\nvar IteratorsCore = __nested_webpack_require_36503__(3383);\n\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND in IterablePrototype) return IterablePrototype[KIND];\n    switch (KIND) {\n      case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };\n      case VALUES: return function values() { return new IteratorConstructor(this, KIND); };\n      case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };\n    } return function () { return new IteratorConstructor(this); };\n  };\n\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR]\n    || IterablePrototype['@@iterator']\n    || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME == 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (IteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (typeof CurrentIteratorPrototype[ITERATOR] != 'function') {\n          createNonEnumerableProperty(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if (DEFAULT == VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    INCORRECT_VALUES_NAME = true;\n    defaultIterator = function values() { return nativeIterator.call(this); };\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    createNonEnumerableProperty(IterablePrototype, ITERATOR, defaultIterator);\n  }\n  Iterators[NAME] = defaultIterator;\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        redefine(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);\n  }\n\n  return methods;\n};\n\n\n/***/ }),\n\n/***/ 9781:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_40475__) {\n\nvar fails = __nested_webpack_require_40475__(7293);\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] != 7;\n});\n\n\n/***/ }),\n\n/***/ 317:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_40795__) {\n\nvar global = __nested_webpack_require_40795__(7854);\nvar isObject = __nested_webpack_require_40795__(111);\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n\n\n/***/ }),\n\n/***/ 8324:\n/***/ (function(module) {\n\n// iterable DOM collections\n// flag - `iterable` interface - 'entries', 'keys', 'values', 'forEach' methods\nmodule.exports = {\n  CSSRuleList: 0,\n  CSSStyleDeclaration: 0,\n  CSSValueList: 0,\n  ClientRectList: 0,\n  DOMRectList: 0,\n  DOMStringList: 0,\n  DOMTokenList: 1,\n  DataTransferItemList: 0,\n  FileList: 0,\n  HTMLAllCollection: 0,\n  HTMLCollection: 0,\n  HTMLFormElement: 0,\n  HTMLSelectElement: 0,\n  MediaList: 0,\n  MimeTypeArray: 0,\n  NamedNodeMap: 0,\n  NodeList: 1,\n  PaintRequestList: 0,\n  Plugin: 0,\n  PluginArray: 0,\n  SVGLengthList: 0,\n  SVGNumberList: 0,\n  SVGPathSegList: 0,\n  SVGPointList: 0,\n  SVGStringList: 0,\n  SVGTransformList: 0,\n  SourceBufferList: 0,\n  StyleSheetList: 0,\n  TextTrackCueList: 0,\n  TextTrackList: 0,\n  TouchList: 0\n};\n\n\n/***/ }),\n\n/***/ 8113:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_42025__) {\n\nvar getBuiltIn = __nested_webpack_require_42025__(5005);\n\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';\n\n\n/***/ }),\n\n/***/ 7392:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_42230__) {\n\nvar global = __nested_webpack_require_42230__(7854);\nvar userAgent = __nested_webpack_require_42230__(8113);\n\nvar process = global.process;\nvar versions = process && process.versions;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  version = match[0] + match[1];\n} else if (userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = match[1];\n  }\n}\n\nmodule.exports = version && +version;\n\n\n/***/ }),\n\n/***/ 748:\n/***/ (function(module) {\n\n// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n\n\n/***/ }),\n\n/***/ 2109:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_43065__) {\n\nvar global = __nested_webpack_require_43065__(7854);\nvar getOwnPropertyDescriptor = __nested_webpack_require_43065__(1236).f;\nvar createNonEnumerableProperty = __nested_webpack_require_43065__(8880);\nvar redefine = __nested_webpack_require_43065__(1320);\nvar setGlobal = __nested_webpack_require_43065__(3505);\nvar copyConstructorProperties = __nested_webpack_require_43065__(9920);\nvar isForced = __nested_webpack_require_43065__(4705);\n\n/*\n  options.target      - name of the target object\n  options.global      - target is the global object\n  options.stat        - export as static methods of target\n  options.proto       - export as prototype methods of target\n  options.real        - real prototype method for the `pure` version\n  options.forced      - export even if the native feature is available\n  options.bind        - bind methods to the target, required for the `pure` version\n  options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe      - use the simple assignment of property instead of delete + defineProperty\n  options.sham        - add a flag to not completely full polyfills\n  options.enumerable  - export as enumerable property\n  options.noTargetGet - prevent calling a getter on target\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || setGlobal(TARGET, {});\n  } else {\n    target = (global[TARGET] || {}).prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.noTargetGet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty === typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    // extend global\n    redefine(target, key, sourceProperty, options);\n  }\n};\n\n\n/***/ }),\n\n/***/ 7293:\n/***/ (function(module) {\n\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n\n\n/***/ }),\n\n/***/ 7007:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_45726__) {\n\n\"use strict\";\n\n// TODO: Remove from `core-js@4` since it's moved to entry points\n__nested_webpack_require_45726__(4916);\nvar redefine = __nested_webpack_require_45726__(1320);\nvar fails = __nested_webpack_require_45726__(7293);\nvar wellKnownSymbol = __nested_webpack_require_45726__(5112);\nvar regexpExec = __nested_webpack_require_45726__(2261);\nvar createNonEnumerableProperty = __nested_webpack_require_45726__(8880);\n\nvar SPECIES = wellKnownSymbol('species');\n\nvar REPLACE_SUPPORTS_NAMED_GROUPS = !fails(function () {\n  // #replace needs built-in support for named groups.\n  // #match works fine because it just return the exec results, even if it has\n  // a \"grops\" property.\n  var re = /./;\n  re.exec = function () {\n    var result = [];\n    result.groups = { a: '7' };\n    return result;\n  };\n  return ''.replace(re, '$<a>') !== '7';\n});\n\n// IE <= 11 replaces $0 with the whole match, as if it was $&\n// https://stackoverflow.com/questions/6024666/getting-ie-to-replace-a-regex-with-the-literal-string-0\nvar REPLACE_KEEPS_$0 = (function () {\n  return 'a'.replace(/./, '$0') === '$0';\n})();\n\nvar REPLACE = wellKnownSymbol('replace');\n// Safari <= 13.0.3(?) substitutes nth capture where n>m with an empty string\nvar REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE = (function () {\n  if (/./[REPLACE]) {\n    return /./[REPLACE]('a', '$0') === '';\n  }\n  return false;\n})();\n\n// Chrome 51 has a buggy \"split\" implementation when RegExp#exec !== nativeExec\n// Weex JS has frozen built-in prototypes, so use try / catch wrapper\nvar SPLIT_WORKS_WITH_OVERWRITTEN_EXEC = !fails(function () {\n  // eslint-disable-next-line regexp/no-empty-group -- required for testing\n  var re = /(?:)/;\n  var originalExec = re.exec;\n  re.exec = function () { return originalExec.apply(this, arguments); };\n  var result = 'ab'.split(re);\n  return result.length !== 2 || result[0] !== 'a' || result[1] !== 'b';\n});\n\nmodule.exports = function (KEY, length, exec, sham) {\n  var SYMBOL = wellKnownSymbol(KEY);\n\n  var DELEGATES_TO_SYMBOL = !fails(function () {\n    // String methods call symbol-named RegEp methods\n    var O = {};\n    O[SYMBOL] = function () { return 7; };\n    return ''[KEY](O) != 7;\n  });\n\n  var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {\n    // Symbol-named RegExp methods call .exec\n    var execCalled = false;\n    var re = /a/;\n\n    if (KEY === 'split') {\n      // We can't use real regex here since it causes deoptimization\n      // and serious performance degradation in V8\n      // https://github.com/zloirock/core-js/issues/306\n      re = {};\n      // RegExp[@@split] doesn't call the regex's exec method, but first creates\n      // a new one. We need to return the patched regex when creating the new one.\n      re.constructor = {};\n      re.constructor[SPECIES] = function () { return re; };\n      re.flags = '';\n      re[SYMBOL] = /./[SYMBOL];\n    }\n\n    re.exec = function () { execCalled = true; return null; };\n\n    re[SYMBOL]('');\n    return !execCalled;\n  });\n\n  if (\n    !DELEGATES_TO_SYMBOL ||\n    !DELEGATES_TO_EXEC ||\n    (KEY === 'replace' && !(\n      REPLACE_SUPPORTS_NAMED_GROUPS &&\n      REPLACE_KEEPS_$0 &&\n      !REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE\n    )) ||\n    (KEY === 'split' && !SPLIT_WORKS_WITH_OVERWRITTEN_EXEC)\n  ) {\n    var nativeRegExpMethod = /./[SYMBOL];\n    var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {\n      if (regexp.exec === regexpExec) {\n        if (DELEGATES_TO_SYMBOL && !forceStringMethod) {\n          // The native String method already delegates to @@method (this\n          // polyfilled function), leasing to infinite recursion.\n          // We avoid it by directly calling the native @@method method.\n          return { done: true, value: nativeRegExpMethod.call(regexp, str, arg2) };\n        }\n        return { done: true, value: nativeMethod.call(str, regexp, arg2) };\n      }\n      return { done: false };\n    }, {\n      REPLACE_KEEPS_$0: REPLACE_KEEPS_$0,\n      REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE: REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE\n    });\n    var stringMethod = methods[0];\n    var regexMethod = methods[1];\n\n    redefine(String.prototype, KEY, stringMethod);\n    redefine(RegExp.prototype, SYMBOL, length == 2\n      // 21.2.5.8 RegExp.prototype[@@replace](string, replaceValue)\n      // 21.2.5.11 RegExp.prototype[@@split](string, limit)\n      ? function (string, arg) { return regexMethod.call(string, this, arg); }\n      // 21.2.5.6 RegExp.prototype[@@match](string)\n      // 21.2.5.9 RegExp.prototype[@@search](string)\n      : function (string) { return regexMethod.call(string, this); }\n    );\n  }\n\n  if (sham) createNonEnumerableProperty(RegExp.prototype[SYMBOL], 'sham', true);\n};\n\n\n/***/ }),\n\n/***/ 9974:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_50488__) {\n\nvar aFunction = __nested_webpack_require_50488__(3099);\n\n// optional / simple context binding\nmodule.exports = function (fn, that, length) {\n  aFunction(fn);\n  if (that === undefined) return fn;\n  switch (length) {\n    case 0: return function () {\n      return fn.call(that);\n    };\n    case 1: return function (a) {\n      return fn.call(that, a);\n    };\n    case 2: return function (a, b) {\n      return fn.call(that, a, b);\n    };\n    case 3: return function (a, b, c) {\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n\n\n/***/ }),\n\n/***/ 5005:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_51177__) {\n\nvar path = __nested_webpack_require_51177__(857);\nvar global = __nested_webpack_require_51177__(7854);\n\nvar aFunction = function (variable) {\n  return typeof variable == 'function' ? variable : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global[namespace])\n    : path[namespace] && path[namespace][method] || global[namespace] && global[namespace][method];\n};\n\n\n/***/ }),\n\n/***/ 1246:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_51701__) {\n\nvar classof = __nested_webpack_require_51701__(648);\nvar Iterators = __nested_webpack_require_51701__(7497);\nvar wellKnownSymbol = __nested_webpack_require_51701__(5112);\n\nvar ITERATOR = wellKnownSymbol('iterator');\n\nmodule.exports = function (it) {\n  if (it != undefined) return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n\n\n/***/ }),\n\n/***/ 8554:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_52112__) {\n\nvar anObject = __nested_webpack_require_52112__(9670);\nvar getIteratorMethod = __nested_webpack_require_52112__(1246);\n\nmodule.exports = function (it) {\n  var iteratorMethod = getIteratorMethod(it);\n  if (typeof iteratorMethod != 'function') {\n    throw TypeError(String(it) + ' is not iterable');\n  } return anObject(iteratorMethod.call(it));\n};\n\n\n/***/ }),\n\n/***/ 647:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_52531__) {\n\nvar toObject = __nested_webpack_require_52531__(7908);\n\nvar floor = Math.floor;\nvar replace = ''.replace;\nvar SUBSTITUTION_SYMBOLS = /\\$([$&'`]|\\d\\d?|<[^>]*>)/g;\nvar SUBSTITUTION_SYMBOLS_NO_NAMED = /\\$([$&'`]|\\d\\d?)/g;\n\n// https://tc39.es/ecma262/#sec-getsubstitution\nmodule.exports = function (matched, str, position, captures, namedCaptures, replacement) {\n  var tailPos = position + matched.length;\n  var m = captures.length;\n  var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;\n  if (namedCaptures !== undefined) {\n    namedCaptures = toObject(namedCaptures);\n    symbols = SUBSTITUTION_SYMBOLS;\n  }\n  return replace.call(replacement, symbols, function (match, ch) {\n    var capture;\n    switch (ch.charAt(0)) {\n      case '$': return '$';\n      case '&': return matched;\n      case '`': return str.slice(0, position);\n      case \"'\": return str.slice(tailPos);\n      case '<':\n        capture = namedCaptures[ch.slice(1, -1)];\n        break;\n      default: // \\d\\d?\n        var n = +ch;\n        if (n === 0) return match;\n        if (n > m) {\n          var f = floor(n / 10);\n          if (f === 0) return match;\n          if (f <= m) return captures[f - 1] === undefined ? ch.charAt(1) : captures[f - 1] + ch.charAt(1);\n          return match;\n        }\n        capture = captures[n - 1];\n    }\n    return capture === undefined ? '' : capture;\n  });\n};\n\n\n/***/ }),\n\n/***/ 7854:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_53970__) {\n\nvar check = function (it) {\n  return it && it.Math == Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  /* global globalThis -- safe */\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  check(typeof self == 'object' && self) ||\n  check(typeof __nested_webpack_require_53970__.g == 'object' && __nested_webpack_require_53970__.g) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n\n\n/***/ }),\n\n/***/ 6656:\n/***/ (function(module) {\n\nvar hasOwnProperty = {}.hasOwnProperty;\n\nmodule.exports = function (it, key) {\n  return hasOwnProperty.call(it, key);\n};\n\n\n/***/ }),\n\n/***/ 3501:\n/***/ (function(module) {\n\nmodule.exports = {};\n\n\n/***/ }),\n\n/***/ 490:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_54852__) {\n\nvar getBuiltIn = __nested_webpack_require_54852__(5005);\n\nmodule.exports = getBuiltIn('document', 'documentElement');\n\n\n/***/ }),\n\n/***/ 4664:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_55056__) {\n\nvar DESCRIPTORS = __nested_webpack_require_55056__(9781);\nvar fails = __nested_webpack_require_55056__(7293);\nvar createElement = __nested_webpack_require_55056__(317);\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a != 7;\n});\n\n\n/***/ }),\n\n/***/ 1179:\n/***/ (function(module) {\n\n// IEEE754 conversions based on https://github.com/feross/ieee754\nvar abs = Math.abs;\nvar pow = Math.pow;\nvar floor = Math.floor;\nvar log = Math.log;\nvar LN2 = Math.LN2;\n\nvar pack = function (number, mantissaLength, bytes) {\n  var buffer = new Array(bytes);\n  var exponentLength = bytes * 8 - mantissaLength - 1;\n  var eMax = (1 << exponentLength) - 1;\n  var eBias = eMax >> 1;\n  var rt = mantissaLength === 23 ? pow(2, -24) - pow(2, -77) : 0;\n  var sign = number < 0 || number === 0 && 1 / number < 0 ? 1 : 0;\n  var index = 0;\n  var exponent, mantissa, c;\n  number = abs(number);\n  // eslint-disable-next-line no-self-compare -- NaN check\n  if (number != number || number === Infinity) {\n    // eslint-disable-next-line no-self-compare -- NaN check\n    mantissa = number != number ? 1 : 0;\n    exponent = eMax;\n  } else {\n    exponent = floor(log(number) / LN2);\n    if (number * (c = pow(2, -exponent)) < 1) {\n      exponent--;\n      c *= 2;\n    }\n    if (exponent + eBias >= 1) {\n      number += rt / c;\n    } else {\n      number += rt * pow(2, 1 - eBias);\n    }\n    if (number * c >= 2) {\n      exponent++;\n      c /= 2;\n    }\n    if (exponent + eBias >= eMax) {\n      mantissa = 0;\n      exponent = eMax;\n    } else if (exponent + eBias >= 1) {\n      mantissa = (number * c - 1) * pow(2, mantissaLength);\n      exponent = exponent + eBias;\n    } else {\n      mantissa = number * pow(2, eBias - 1) * pow(2, mantissaLength);\n      exponent = 0;\n    }\n  }\n  for (; mantissaLength >= 8; buffer[index++] = mantissa & 255, mantissa /= 256, mantissaLength -= 8);\n  exponent = exponent << mantissaLength | mantissa;\n  exponentLength += mantissaLength;\n  for (; exponentLength > 0; buffer[index++] = exponent & 255, exponent /= 256, exponentLength -= 8);\n  buffer[--index] |= sign * 128;\n  return buffer;\n};\n\nvar unpack = function (buffer, mantissaLength) {\n  var bytes = buffer.length;\n  var exponentLength = bytes * 8 - mantissaLength - 1;\n  var eMax = (1 << exponentLength) - 1;\n  var eBias = eMax >> 1;\n  var nBits = exponentLength - 7;\n  var index = bytes - 1;\n  var sign = buffer[index--];\n  var exponent = sign & 127;\n  var mantissa;\n  sign >>= 7;\n  for (; nBits > 0; exponent = exponent * 256 + buffer[index], index--, nBits -= 8);\n  mantissa = exponent & (1 << -nBits) - 1;\n  exponent >>= -nBits;\n  nBits += mantissaLength;\n  for (; nBits > 0; mantissa = mantissa * 256 + buffer[index], index--, nBits -= 8);\n  if (exponent === 0) {\n    exponent = 1 - eBias;\n  } else if (exponent === eMax) {\n    return mantissa ? NaN : sign ? -Infinity : Infinity;\n  } else {\n    mantissa = mantissa + pow(2, mantissaLength);\n    exponent = exponent - eBias;\n  } return (sign ? -1 : 1) * mantissa * pow(2, exponent - mantissaLength);\n};\n\nmodule.exports = {\n  pack: pack,\n  unpack: unpack\n};\n\n\n/***/ }),\n\n/***/ 8361:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_58329__) {\n\nvar fails = __nested_webpack_require_58329__(7293);\nvar classof = __nested_webpack_require_58329__(4326);\n\nvar split = ''.split;\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split.call(it, '') : Object(it);\n} : Object;\n\n\n/***/ }),\n\n/***/ 9587:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_58925__) {\n\nvar isObject = __nested_webpack_require_58925__(111);\nvar setPrototypeOf = __nested_webpack_require_58925__(7674);\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    typeof (NewTarget = dummy.constructor) == 'function' &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n\n\n/***/ }),\n\n/***/ 2788:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_59686__) {\n\nvar store = __nested_webpack_require_59686__(5465);\n\nvar functionToString = Function.toString;\n\n// this helper broken in `3.4.1-3.4.4`, so we can't use `shared` helper\nif (typeof store.inspectSource != 'function') {\n  store.inspectSource = function (it) {\n    return functionToString.call(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n\n\n/***/ }),\n\n/***/ 9909:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_60112__) {\n\nvar NATIVE_WEAK_MAP = __nested_webpack_require_60112__(8536);\nvar global = __nested_webpack_require_60112__(7854);\nvar isObject = __nested_webpack_require_60112__(111);\nvar createNonEnumerableProperty = __nested_webpack_require_60112__(8880);\nvar objectHas = __nested_webpack_require_60112__(6656);\nvar shared = __nested_webpack_require_60112__(5465);\nvar sharedKey = __nested_webpack_require_60112__(6200);\nvar hiddenKeys = __nested_webpack_require_60112__(3501);\n\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP) {\n  var store = shared.state || (shared.state = new WeakMap());\n  var wmget = store.get;\n  var wmhas = store.has;\n  var wmset = store.set;\n  set = function (it, metadata) {\n    metadata.facade = it;\n    wmset.call(store, it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return wmget.call(store, it) || {};\n  };\n  has = function (it) {\n    return wmhas.call(store, it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return objectHas(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return objectHas(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n\n\n/***/ }),\n\n/***/ 7659:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_61804__) {\n\nvar wellKnownSymbol = __nested_webpack_require_61804__(5112);\nvar Iterators = __nested_webpack_require_61804__(7497);\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayPrototype = Array.prototype;\n\n// check on default Array iterator\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);\n};\n\n\n/***/ }),\n\n/***/ 3157:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_62240__) {\n\nvar classof = __nested_webpack_require_62240__(4326);\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\nmodule.exports = Array.isArray || function isArray(arg) {\n  return classof(arg) == 'Array';\n};\n\n\n/***/ }),\n\n/***/ 4705:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_62548__) {\n\nvar fails = __nested_webpack_require_62548__(7293);\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true\n    : value == NATIVE ? false\n    : typeof detection == 'function' ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n\n\n/***/ }),\n\n/***/ 111:\n/***/ (function(module) {\n\nmodule.exports = function (it) {\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n\n\n/***/ }),\n\n/***/ 1913:\n/***/ (function(module) {\n\nmodule.exports = false;\n\n\n/***/ }),\n\n/***/ 7850:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_63453__) {\n\nvar isObject = __nested_webpack_require_63453__(111);\nvar classof = __nested_webpack_require_63453__(4326);\nvar wellKnownSymbol = __nested_webpack_require_63453__(5112);\n\nvar MATCH = wellKnownSymbol('match');\n\n// `IsRegExp` abstract operation\n// https://tc39.es/ecma262/#sec-isregexp\nmodule.exports = function (it) {\n  var isRegExp;\n  return isObject(it) && ((isRegExp = it[MATCH]) !== undefined ? !!isRegExp : classof(it) == 'RegExp');\n};\n\n\n/***/ }),\n\n/***/ 9212:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_63953__) {\n\nvar anObject = __nested_webpack_require_63953__(9670);\n\nmodule.exports = function (iterator) {\n  var returnMethod = iterator['return'];\n  if (returnMethod !== undefined) {\n    return anObject(returnMethod.call(iterator)).value;\n  }\n};\n\n\n/***/ }),\n\n/***/ 3383:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_64274__) {\n\n\"use strict\";\n\nvar fails = __nested_webpack_require_64274__(7293);\nvar getPrototypeOf = __nested_webpack_require_64274__(9518);\nvar createNonEnumerableProperty = __nested_webpack_require_64274__(8880);\nvar has = __nested_webpack_require_64274__(6656);\nvar wellKnownSymbol = __nested_webpack_require_64274__(5112);\nvar IS_PURE = __nested_webpack_require_64274__(1913);\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\nvar returnThis = function () { return this; };\n\n// `%IteratorPrototype%` object\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;\n  else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\n\nvar NEW_ITERATOR_PROTOTYPE = IteratorPrototype == undefined || fails(function () {\n  var test = {};\n  // FF44- legacy iterators case\n  return IteratorPrototype[ITERATOR].call(test) !== test;\n});\n\nif (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};\n\n// 25.1.2.1.1 %IteratorPrototype%[@@iterator]()\nif ((!IS_PURE || NEW_ITERATOR_PROTOTYPE) && !has(IteratorPrototype, ITERATOR)) {\n  createNonEnumerableProperty(IteratorPrototype, ITERATOR, returnThis);\n}\n\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};\n\n\n/***/ }),\n\n/***/ 7497:\n/***/ (function(module) {\n\nmodule.exports = {};\n\n\n/***/ }),\n\n/***/ 133:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_65970__) {\n\nvar fails = __nested_webpack_require_65970__(7293);\n\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  // Chrome 38 Symbol has incorrect toString conversion\n  /* global Symbol -- required for testing */\n  return !String(Symbol());\n});\n\n\n/***/ }),\n\n/***/ 590:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_66314__) {\n\nvar fails = __nested_webpack_require_66314__(7293);\nvar wellKnownSymbol = __nested_webpack_require_66314__(5112);\nvar IS_PURE = __nested_webpack_require_66314__(1913);\n\nvar ITERATOR = wellKnownSymbol('iterator');\n\nmodule.exports = !fails(function () {\n  var url = new URL('b?a=1&b=2&c=3', 'http://a');\n  var searchParams = url.searchParams;\n  var result = '';\n  url.pathname = 'c%20d';\n  searchParams.forEach(function (value, key) {\n    searchParams['delete']('b');\n    result += key + value;\n  });\n  return (IS_PURE && !url.toJSON)\n    || !searchParams.sort\n    || url.href !== 'http://a/c%20d?a=1&c=3'\n    || searchParams.get('c') !== '3'\n    || String(new URLSearchParams('?a=1')) !== 'a=1'\n    || !searchParams[ITERATOR]\n    // throws in Edge\n    || new URL('https://a@b').username !== 'a'\n    || new URLSearchParams(new URLSearchParams('a=b')).get('a') !== 'b'\n    // not punycoded in Edge\n    || new URL('http://тест').host !== 'xn--e1aybc'\n    // not escaped in Chrome 62-\n    || new URL('http://a#б').hash !== '#%D0%B1'\n    // fails in Chrome 66-\n    || result !== 'a1c3'\n    // throws in Safari\n    || new URL('http://x', undefined).host !== 'x';\n});\n\n\n/***/ }),\n\n/***/ 8536:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_67534__) {\n\nvar global = __nested_webpack_require_67534__(7854);\nvar inspectSource = __nested_webpack_require_67534__(2788);\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = typeof WeakMap === 'function' && /native code/.test(inspectSource(WeakMap));\n\n\n/***/ }),\n\n/***/ 1574:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_67846__) {\n\n\"use strict\";\n\nvar DESCRIPTORS = __nested_webpack_require_67846__(9781);\nvar fails = __nested_webpack_require_67846__(7293);\nvar objectKeys = __nested_webpack_require_67846__(1956);\nvar getOwnPropertySymbolsModule = __nested_webpack_require_67846__(5181);\nvar propertyIsEnumerableModule = __nested_webpack_require_67846__(5296);\nvar toObject = __nested_webpack_require_67846__(7908);\nvar IndexedObject = __nested_webpack_require_67846__(8361);\n\nvar nativeAssign = Object.assign;\nvar defineProperty = Object.defineProperty;\n\n// `Object.assign` method\n// https://tc39.es/ecma262/#sec-object.assign\nmodule.exports = !nativeAssign || fails(function () {\n  // should have correct order of operations (Edge bug)\n  if (DESCRIPTORS && nativeAssign({ b: 1 }, nativeAssign(defineProperty({}, 'a', {\n    enumerable: true,\n    get: function () {\n      defineProperty(this, 'b', {\n        value: 3,\n        enumerable: false\n      });\n    }\n  }), { b: 2 })).b !== 1) return true;\n  // should work with symbols and should have deterministic property order (V8 bug)\n  var A = {};\n  var B = {};\n  /* global Symbol -- required for testing */\n  var symbol = Symbol();\n  var alphabet = 'abcdefghijklmnopqrst';\n  A[symbol] = 7;\n  alphabet.split('').forEach(function (chr) { B[chr] = chr; });\n  return nativeAssign({}, A)[symbol] != 7 || objectKeys(nativeAssign({}, B)).join('') != alphabet;\n}) ? function assign(target, source) { // eslint-disable-line no-unused-vars -- required for `.length`\n  var T = toObject(target);\n  var argumentsLength = arguments.length;\n  var index = 1;\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  var propertyIsEnumerable = propertyIsEnumerableModule.f;\n  while (argumentsLength > index) {\n    var S = IndexedObject(arguments[index++]);\n    var keys = getOwnPropertySymbols ? objectKeys(S).concat(getOwnPropertySymbols(S)) : objectKeys(S);\n    var length = keys.length;\n    var j = 0;\n    var key;\n    while (length > j) {\n      key = keys[j++];\n      if (!DESCRIPTORS || propertyIsEnumerable.call(S, key)) T[key] = S[key];\n    }\n  } return T;\n} : nativeAssign;\n\n\n/***/ }),\n\n/***/ 30:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_69941__) {\n\nvar anObject = __nested_webpack_require_69941__(9670);\nvar defineProperties = __nested_webpack_require_69941__(6048);\nvar enumBugKeys = __nested_webpack_require_69941__(748);\nvar hiddenKeys = __nested_webpack_require_69941__(3501);\nvar html = __nested_webpack_require_69941__(490);\nvar documentCreateElement = __nested_webpack_require_69941__(317);\nvar sharedKey = __nested_webpack_require_69941__(6200);\n\nvar GT = '>';\nvar LT = '<';\nvar PROTOTYPE = 'prototype';\nvar SCRIPT = 'script';\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar EmptyConstructor = function () { /* empty */ };\n\nvar scriptTag = function (content) {\n  return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n};\n\n// Create object with fake `null` prototype: use ActiveX Object with cleared prototype\nvar NullProtoObjectViaActiveX = function (activeXDocument) {\n  activeXDocument.write(scriptTag(''));\n  activeXDocument.close();\n  var temp = activeXDocument.parentWindow.Object;\n  activeXDocument = null; // avoid memory leak\n  return temp;\n};\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar NullProtoObjectViaIFrame = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var JS = 'java' + SCRIPT + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  // https://github.com/zloirock/core-js/issues/475\n  iframe.src = String(JS);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(scriptTag('document.F=Object'));\n  iframeDocument.close();\n  return iframeDocument.F;\n};\n\n// Check for document.domain and active x support\n// No need to use active x approach when document.domain is not set\n// see https://github.com/es-shims/es5-shim/issues/150\n// variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n// avoid IE GC bug\nvar activeXDocument;\nvar NullProtoObject = function () {\n  try {\n    /* global ActiveXObject -- old IE */\n    activeXDocument = document.domain && new ActiveXObject('htmlfile');\n  } catch (error) { /* ignore */ }\n  NullProtoObject = activeXDocument ? NullProtoObjectViaActiveX(activeXDocument) : NullProtoObjectViaIFrame();\n  var length = enumBugKeys.length;\n  while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n  return NullProtoObject();\n};\n\nhiddenKeys[IE_PROTO] = true;\n\n// `Object.create` method\n// https://tc39.es/ecma262/#sec-object.create\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    EmptyConstructor[PROTOTYPE] = anObject(O);\n    result = new EmptyConstructor();\n    EmptyConstructor[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = NullProtoObject();\n  return Properties === undefined ? result : defineProperties(result, Properties);\n};\n\n\n/***/ }),\n\n/***/ 6048:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_72835__) {\n\nvar DESCRIPTORS = __nested_webpack_require_72835__(9781);\nvar definePropertyModule = __nested_webpack_require_72835__(3070);\nvar anObject = __nested_webpack_require_72835__(9670);\nvar objectKeys = __nested_webpack_require_72835__(1956);\n\n// `Object.defineProperties` method\n// https://tc39.es/ecma262/#sec-object.defineproperties\nmodule.exports = DESCRIPTORS ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], Properties[key]);\n  return O;\n};\n\n\n/***/ }),\n\n/***/ 3070:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_73525__) {\n\nvar DESCRIPTORS = __nested_webpack_require_73525__(9781);\nvar IE8_DOM_DEFINE = __nested_webpack_require_73525__(4664);\nvar anObject = __nested_webpack_require_73525__(9670);\nvar toPrimitive = __nested_webpack_require_73525__(7593);\n\nvar nativeDefineProperty = Object.defineProperty;\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? nativeDefineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return nativeDefineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n\n\n/***/ }),\n\n/***/ 1236:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_74380__) {\n\nvar DESCRIPTORS = __nested_webpack_require_74380__(9781);\nvar propertyIsEnumerableModule = __nested_webpack_require_74380__(5296);\nvar createPropertyDescriptor = __nested_webpack_require_74380__(9114);\nvar toIndexedObject = __nested_webpack_require_74380__(5656);\nvar toPrimitive = __nested_webpack_require_74380__(7593);\nvar has = __nested_webpack_require_74380__(6656);\nvar IE8_DOM_DEFINE = __nested_webpack_require_74380__(4664);\n\nvar nativeGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? nativeGetOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPrimitive(P, true);\n  if (IE8_DOM_DEFINE) try {\n    return nativeGetOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (has(O, P)) return createPropertyDescriptor(!propertyIsEnumerableModule.f.call(O, P), O[P]);\n};\n\n\n/***/ }),\n\n/***/ 8006:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_75368__) {\n\nvar internalObjectKeys = __nested_webpack_require_75368__(6324);\nvar enumBugKeys = __nested_webpack_require_75368__(748);\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n\n\n/***/ }),\n\n/***/ 5181:\n/***/ (function(__unused_webpack_module, exports) {\n\nexports.f = Object.getOwnPropertySymbols;\n\n\n/***/ }),\n\n/***/ 9518:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_75966__) {\n\nvar has = __nested_webpack_require_75966__(6656);\nvar toObject = __nested_webpack_require_75966__(7908);\nvar sharedKey = __nested_webpack_require_75966__(6200);\nvar CORRECT_PROTOTYPE_GETTER = __nested_webpack_require_75966__(8544);\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar ObjectPrototype = Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? Object.getPrototypeOf : function (O) {\n  O = toObject(O);\n  if (has(O, IE_PROTO)) return O[IE_PROTO];\n  if (typeof O.constructor == 'function' && O instanceof O.constructor) {\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectPrototype : null;\n};\n\n\n/***/ }),\n\n/***/ 6324:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_76729__) {\n\nvar has = __nested_webpack_require_76729__(6656);\nvar toIndexedObject = __nested_webpack_require_76729__(5656);\nvar indexOf = __nested_webpack_require_76729__(1318).indexOf;\nvar hiddenKeys = __nested_webpack_require_76729__(3501);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !has(hiddenKeys, key) && has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (has(O, key = names[i++])) {\n    ~indexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n\n\n/***/ }),\n\n/***/ 1956:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_77369__) {\n\nvar internalObjectKeys = __nested_webpack_require_77369__(6324);\nvar enumBugKeys = __nested_webpack_require_77369__(748);\n\n// `Object.keys` method\n// https://tc39.es/ecma262/#sec-object.keys\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n\n\n/***/ }),\n\n/***/ 5296:\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\nvar nativePropertyIsEnumerable = {}.propertyIsEnumerable;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !nativePropertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : nativePropertyIsEnumerable;\n\n\n/***/ }),\n\n/***/ 7674:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_78394__) {\n\n/* eslint-disable no-proto -- safe */\nvar anObject = __nested_webpack_require_78394__(9670);\nvar aPossiblePrototype = __nested_webpack_require_78394__(6077);\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = Object.getOwnPropertyDescriptor(Object.prototype, '__proto__').set;\n    setter.call(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter.call(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n\n\n/***/ }),\n\n/***/ 288:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_79325__) {\n\n\"use strict\";\n\nvar TO_STRING_TAG_SUPPORT = __nested_webpack_require_79325__(1694);\nvar classof = __nested_webpack_require_79325__(648);\n\n// `Object.prototype.toString` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.tostring\nmodule.exports = TO_STRING_TAG_SUPPORT ? {}.toString : function toString() {\n  return '[object ' + classof(this) + ']';\n};\n\n\n/***/ }),\n\n/***/ 3887:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_79769__) {\n\nvar getBuiltIn = __nested_webpack_require_79769__(5005);\nvar getOwnPropertyNamesModule = __nested_webpack_require_79769__(8006);\nvar getOwnPropertySymbolsModule = __nested_webpack_require_79769__(5181);\nvar anObject = __nested_webpack_require_79769__(9670);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? keys.concat(getOwnPropertySymbols(it)) : keys;\n};\n\n\n/***/ }),\n\n/***/ 857:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_80406__) {\n\nvar global = __nested_webpack_require_80406__(7854);\n\nmodule.exports = global;\n\n\n/***/ }),\n\n/***/ 2248:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_80571__) {\n\nvar redefine = __nested_webpack_require_80571__(1320);\n\nmodule.exports = function (target, src, options) {\n  for (var key in src) redefine(target, key, src[key], options);\n  return target;\n};\n\n\n/***/ }),\n\n/***/ 1320:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_80849__) {\n\nvar global = __nested_webpack_require_80849__(7854);\nvar createNonEnumerableProperty = __nested_webpack_require_80849__(8880);\nvar has = __nested_webpack_require_80849__(6656);\nvar setGlobal = __nested_webpack_require_80849__(3505);\nvar inspectSource = __nested_webpack_require_80849__(2788);\nvar InternalStateModule = __nested_webpack_require_80849__(9909);\n\nvar getInternalState = InternalStateModule.get;\nvar enforceInternalState = InternalStateModule.enforce;\nvar TEMPLATE = String(String).split('String');\n\n(module.exports = function (O, key, value, options) {\n  var unsafe = options ? !!options.unsafe : false;\n  var simple = options ? !!options.enumerable : false;\n  var noTargetGet = options ? !!options.noTargetGet : false;\n  var state;\n  if (typeof value == 'function') {\n    if (typeof key == 'string' && !has(value, 'name')) {\n      createNonEnumerableProperty(value, 'name', key);\n    }\n    state = enforceInternalState(value);\n    if (!state.source) {\n      state.source = TEMPLATE.join(typeof key == 'string' ? key : '');\n    }\n  }\n  if (O === global) {\n    if (simple) O[key] = value;\n    else setGlobal(key, value);\n    return;\n  } else if (!unsafe) {\n    delete O[key];\n  } else if (!noTargetGet && O[key]) {\n    simple = true;\n  }\n  if (simple) O[key] = value;\n  else createNonEnumerableProperty(O, key, value);\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, 'toString', function toString() {\n  return typeof this == 'function' && getInternalState(this).source || inspectSource(this);\n});\n\n\n/***/ }),\n\n/***/ 7651:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_82466__) {\n\nvar classof = __nested_webpack_require_82466__(4326);\nvar regexpExec = __nested_webpack_require_82466__(2261);\n\n// `RegExpExec` abstract operation\n// https://tc39.es/ecma262/#sec-regexpexec\nmodule.exports = function (R, S) {\n  var exec = R.exec;\n  if (typeof exec === 'function') {\n    var result = exec.call(R, S);\n    if (typeof result !== 'object') {\n      throw TypeError('RegExp exec method returned something other than an Object or null');\n    }\n    return result;\n  }\n\n  if (classof(R) !== 'RegExp') {\n    throw TypeError('RegExp#exec called on incompatible receiver');\n  }\n\n  return regexpExec.call(R, S);\n};\n\n\n\n/***/ }),\n\n/***/ 2261:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_83158__) {\n\n\"use strict\";\n\nvar regexpFlags = __nested_webpack_require_83158__(7066);\nvar stickyHelpers = __nested_webpack_require_83158__(2999);\n\nvar nativeExec = RegExp.prototype.exec;\n// This always refers to the native implementation, because the\n// String#replace polyfill uses ./fix-regexp-well-known-symbol-logic.js,\n// which loads this file before patching the method.\nvar nativeReplace = String.prototype.replace;\n\nvar patchedExec = nativeExec;\n\nvar UPDATES_LAST_INDEX_WRONG = (function () {\n  var re1 = /a/;\n  var re2 = /b*/g;\n  nativeExec.call(re1, 'a');\n  nativeExec.call(re2, 'a');\n  return re1.lastIndex !== 0 || re2.lastIndex !== 0;\n})();\n\nvar UNSUPPORTED_Y = stickyHelpers.UNSUPPORTED_Y || stickyHelpers.BROKEN_CARET;\n\n// nonparticipating capturing group, copied from es5-shim's String#split patch.\n// eslint-disable-next-line regexp/no-assertion-capturing-group, regexp/no-empty-group -- required for testing\nvar NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;\n\nvar PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED || UNSUPPORTED_Y;\n\nif (PATCH) {\n  patchedExec = function exec(str) {\n    var re = this;\n    var lastIndex, reCopy, match, i;\n    var sticky = UNSUPPORTED_Y && re.sticky;\n    var flags = regexpFlags.call(re);\n    var source = re.source;\n    var charsAdded = 0;\n    var strCopy = str;\n\n    if (sticky) {\n      flags = flags.replace('y', '');\n      if (flags.indexOf('g') === -1) {\n        flags += 'g';\n      }\n\n      strCopy = String(str).slice(re.lastIndex);\n      // Support anchored sticky behavior.\n      if (re.lastIndex > 0 && (!re.multiline || re.multiline && str[re.lastIndex - 1] !== '\\n')) {\n        source = '(?: ' + source + ')';\n        strCopy = ' ' + strCopy;\n        charsAdded++;\n      }\n      // ^(? + rx + ) is needed, in combination with some str slicing, to\n      // simulate the 'y' flag.\n      reCopy = new RegExp('^(?:' + source + ')', flags);\n    }\n\n    if (NPCG_INCLUDED) {\n      reCopy = new RegExp('^' + source + '$(?!\\\\s)', flags);\n    }\n    if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;\n\n    match = nativeExec.call(sticky ? reCopy : re, strCopy);\n\n    if (sticky) {\n      if (match) {\n        match.input = match.input.slice(charsAdded);\n        match[0] = match[0].slice(charsAdded);\n        match.index = re.lastIndex;\n        re.lastIndex += match[0].length;\n      } else re.lastIndex = 0;\n    } else if (UPDATES_LAST_INDEX_WRONG && match) {\n      re.lastIndex = re.global ? match.index + match[0].length : lastIndex;\n    }\n    if (NPCG_INCLUDED && match && match.length > 1) {\n      // Fix browsers whose `exec` methods don't consistently return `undefined`\n      // for NPCG, like IE8. NOTE: This doesn' work for /(.?)?/\n      nativeReplace.call(match[0], reCopy, function () {\n        for (i = 1; i < arguments.length - 2; i++) {\n          if (arguments[i] === undefined) match[i] = undefined;\n        }\n      });\n    }\n\n    return match;\n  };\n}\n\nmodule.exports = patchedExec;\n\n\n/***/ }),\n\n/***/ 7066:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_86176__) {\n\n\"use strict\";\n\nvar anObject = __nested_webpack_require_86176__(9670);\n\n// `RegExp.prototype.flags` getter implementation\n// https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\nmodule.exports = function () {\n  var that = anObject(this);\n  var result = '';\n  if (that.global) result += 'g';\n  if (that.ignoreCase) result += 'i';\n  if (that.multiline) result += 'm';\n  if (that.dotAll) result += 's';\n  if (that.unicode) result += 'u';\n  if (that.sticky) result += 'y';\n  return result;\n};\n\n\n/***/ }),\n\n/***/ 2999:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_86753__) {\n\n\"use strict\";\n\n\nvar fails = __nested_webpack_require_86753__(7293);\n\n// babel-minify transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError,\n// so we use an intermediate function.\nfunction RE(s, f) {\n  return RegExp(s, f);\n}\n\nexports.UNSUPPORTED_Y = fails(function () {\n  // babel-minify transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError\n  var re = RE('a', 'y');\n  re.lastIndex = 2;\n  return re.exec('abcd') != null;\n});\n\nexports.BROKEN_CARET = fails(function () {\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=773687\n  var re = RE('^r', 'gy');\n  re.lastIndex = 2;\n  return re.exec('str') != null;\n});\n\n\n/***/ }),\n\n/***/ 4488:\n/***/ (function(module) {\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};\n\n\n/***/ }),\n\n/***/ 3505:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_87737__) {\n\nvar global = __nested_webpack_require_87737__(7854);\nvar createNonEnumerableProperty = __nested_webpack_require_87737__(8880);\n\nmodule.exports = function (key, value) {\n  try {\n    createNonEnumerableProperty(global, key, value);\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n\n\n/***/ }),\n\n/***/ 6340:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_88106__) {\n\n\"use strict\";\n\nvar getBuiltIn = __nested_webpack_require_88106__(5005);\nvar definePropertyModule = __nested_webpack_require_88106__(3070);\nvar wellKnownSymbol = __nested_webpack_require_88106__(5112);\nvar DESCRIPTORS = __nested_webpack_require_88106__(9781);\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (CONSTRUCTOR_NAME) {\n  var Constructor = getBuiltIn(CONSTRUCTOR_NAME);\n  var defineProperty = definePropertyModule.f;\n\n  if (DESCRIPTORS && Constructor && !Constructor[SPECIES]) {\n    defineProperty(Constructor, SPECIES, {\n      configurable: true,\n      get: function () { return this; }\n    });\n  }\n};\n\n\n/***/ }),\n\n/***/ 8003:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_88786__) {\n\nvar defineProperty = __nested_webpack_require_88786__(3070).f;\nvar has = __nested_webpack_require_88786__(6656);\nvar wellKnownSymbol = __nested_webpack_require_88786__(5112);\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (it, TAG, STATIC) {\n  if (it && !has(it = STATIC ? it : it.prototype, TO_STRING_TAG)) {\n    defineProperty(it, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n\n\n/***/ }),\n\n/***/ 6200:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_89271__) {\n\nvar shared = __nested_webpack_require_89271__(2309);\nvar uid = __nested_webpack_require_89271__(9711);\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n\n\n/***/ }),\n\n/***/ 5465:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_89559__) {\n\nvar global = __nested_webpack_require_89559__(7854);\nvar setGlobal = __nested_webpack_require_89559__(3505);\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\n\nmodule.exports = store;\n\n\n/***/ }),\n\n/***/ 2309:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_89855__) {\n\nvar IS_PURE = __nested_webpack_require_89855__(1913);\nvar store = __nested_webpack_require_89855__(5465);\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.9.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2021 Denis Pushkarev (zloirock.ru)'\n});\n\n\n/***/ }),\n\n/***/ 6707:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_90288__) {\n\nvar anObject = __nested_webpack_require_90288__(9670);\nvar aFunction = __nested_webpack_require_90288__(3099);\nvar wellKnownSymbol = __nested_webpack_require_90288__(5112);\n\nvar SPECIES = wellKnownSymbol('species');\n\n// `SpeciesConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-speciesconstructor\nmodule.exports = function (O, defaultConstructor) {\n  var C = anObject(O).constructor;\n  var S;\n  return C === undefined || (S = anObject(C)[SPECIES]) == undefined ? defaultConstructor : aFunction(S);\n};\n\n\n/***/ }),\n\n/***/ 8710:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_90863__) {\n\nvar toInteger = __nested_webpack_require_90863__(9958);\nvar requireObjectCoercible = __nested_webpack_require_90863__(4488);\n\n// `String.prototype.{ codePointAt, at }` methods implementation\nvar createMethod = function (CONVERT_TO_STRING) {\n  return function ($this, pos) {\n    var S = String(requireObjectCoercible($this));\n    var position = toInteger(pos);\n    var size = S.length;\n    var first, second;\n    if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;\n    first = S.charCodeAt(position);\n    return first < 0xD800 || first > 0xDBFF || position + 1 === size\n      || (second = S.charCodeAt(position + 1)) < 0xDC00 || second > 0xDFFF\n        ? CONVERT_TO_STRING ? S.charAt(position) : first\n        : CONVERT_TO_STRING ? S.slice(position, position + 2) : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.codePointAt` method\n  // https://tc39.es/ecma262/#sec-string.prototype.codepointat\n  codeAt: createMethod(false),\n  // `String.prototype.at` method\n  // https://github.com/mathiasbynens/String.prototype.at\n  charAt: createMethod(true)\n};\n\n\n/***/ }),\n\n/***/ 3197:\n/***/ (function(module) {\n\n\"use strict\";\n\n// based on https://github.com/bestiejs/punycode.js/blob/master/punycode.js\nvar maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\nvar base = 36;\nvar tMin = 1;\nvar tMax = 26;\nvar skew = 38;\nvar damp = 700;\nvar initialBias = 72;\nvar initialN = 128; // 0x80\nvar delimiter = '-'; // '\\x2D'\nvar regexNonASCII = /[^\\0-\\u007E]/; // non-ASCII chars\nvar regexSeparators = /[.\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\nvar OVERFLOW_ERROR = 'Overflow: input needs wider integers to process';\nvar baseMinusTMin = base - tMin;\nvar floor = Math.floor;\nvar stringFromCharCode = String.fromCharCode;\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n */\nvar ucs2decode = function (string) {\n  var output = [];\n  var counter = 0;\n  var length = string.length;\n  while (counter < length) {\n    var value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      // It's a high surrogate, and there is a next character.\n      var extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        // It's an unmatched surrogate; only append this code unit, in case the\n        // next code unit is the high surrogate of a surrogate pair.\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n */\nvar digitToBasic = function (digit) {\n  //  0..25 map to ASCII a..z or A..Z\n  // 26..35 map to ASCII 0..9\n  return digit + 22 + 75 * (digit < 26);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n */\nvar adapt = function (delta, numPoints, firstTime) {\n  var k = 0;\n  delta = firstTime ? floor(delta / damp) : delta >> 1;\n  delta += floor(delta / numPoints);\n  for (; delta > baseMinusTMin * tMax >> 1; k += base) {\n    delta = floor(delta / baseMinusTMin);\n  }\n  return floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n */\n// eslint-disable-next-line max-statements -- TODO\nvar encode = function (input) {\n  var output = [];\n\n  // Convert the input in UCS-2 to an array of Unicode code points.\n  input = ucs2decode(input);\n\n  // Cache the length.\n  var inputLength = input.length;\n\n  // Initialize the state.\n  var n = initialN;\n  var delta = 0;\n  var bias = initialBias;\n  var i, currentValue;\n\n  // Handle the basic code points.\n  for (i = 0; i < input.length; i++) {\n    currentValue = input[i];\n    if (currentValue < 0x80) {\n      output.push(stringFromCharCode(currentValue));\n    }\n  }\n\n  var basicLength = output.length; // number of basic code points.\n  var handledCPCount = basicLength; // number of code points that have been handled;\n\n  // Finish the basic string with a delimiter unless it's empty.\n  if (basicLength) {\n    output.push(delimiter);\n  }\n\n  // Main encoding loop:\n  while (handledCPCount < inputLength) {\n    // All non-basic code points < n have been handled already. Find the next larger one:\n    var m = maxInt;\n    for (i = 0; i < input.length; i++) {\n      currentValue = input[i];\n      if (currentValue >= n && currentValue < m) {\n        m = currentValue;\n      }\n    }\n\n    // Increase `delta` enough to advance the decoder's <n,i> state to <m,0>, but guard against overflow.\n    var handledCPCountPlusOne = handledCPCount + 1;\n    if (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n      throw RangeError(OVERFLOW_ERROR);\n    }\n\n    delta += (m - n) * handledCPCountPlusOne;\n    n = m;\n\n    for (i = 0; i < input.length; i++) {\n      currentValue = input[i];\n      if (currentValue < n && ++delta > maxInt) {\n        throw RangeError(OVERFLOW_ERROR);\n      }\n      if (currentValue == n) {\n        // Represent delta as a generalized variable-length integer.\n        var q = delta;\n        for (var k = base; /* no condition */; k += base) {\n          var t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n          if (q < t) break;\n          var qMinusT = q - t;\n          var baseMinusT = base - t;\n          output.push(stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT)));\n          q = floor(qMinusT / baseMinusT);\n        }\n\n        output.push(stringFromCharCode(digitToBasic(q)));\n        bias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n        delta = 0;\n        ++handledCPCount;\n      }\n    }\n\n    ++delta;\n    ++n;\n  }\n  return output.join('');\n};\n\nmodule.exports = function (input) {\n  var encoded = [];\n  var labels = input.toLowerCase().replace(regexSeparators, '\\u002E').split('.');\n  var i, label;\n  for (i = 0; i < labels.length; i++) {\n    label = labels[i];\n    encoded.push(regexNonASCII.test(label) ? 'xn--' + encode(label) : label);\n  }\n  return encoded.join('.');\n};\n\n\n/***/ }),\n\n/***/ 6091:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_97360__) {\n\nvar fails = __nested_webpack_require_97360__(7293);\nvar whitespaces = __nested_webpack_require_97360__(1361);\n\nvar non = '\\u200B\\u0085\\u180E';\n\n// check that a method works with the correct list\n// of whitespaces and has a correct name\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    return !!whitespaces[METHOD_NAME]() || non[METHOD_NAME]() != non || whitespaces[METHOD_NAME].name !== METHOD_NAME;\n  });\n};\n\n\n/***/ }),\n\n/***/ 3111:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_97868__) {\n\nvar requireObjectCoercible = __nested_webpack_require_97868__(4488);\nvar whitespaces = __nested_webpack_require_97868__(1361);\n\nvar whitespace = '[' + whitespaces + ']';\nvar ltrim = RegExp('^' + whitespace + whitespace + '*');\nvar rtrim = RegExp(whitespace + whitespace + '*$');\n\n// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\nvar createMethod = function (TYPE) {\n  return function ($this) {\n    var string = String(requireObjectCoercible($this));\n    if (TYPE & 1) string = string.replace(ltrim, '');\n    if (TYPE & 2) string = string.replace(rtrim, '');\n    return string;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.{ trimLeft, trimStart }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n  start: createMethod(1),\n  // `String.prototype.{ trimRight, trimEnd }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimend\n  end: createMethod(2),\n  // `String.prototype.trim` method\n  // https://tc39.es/ecma262/#sec-string.prototype.trim\n  trim: createMethod(3)\n};\n\n\n/***/ }),\n\n/***/ 1400:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_98992__) {\n\nvar toInteger = __nested_webpack_require_98992__(9958);\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toInteger(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n\n\n/***/ }),\n\n/***/ 7067:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_99521__) {\n\nvar toInteger = __nested_webpack_require_99521__(9958);\nvar toLength = __nested_webpack_require_99521__(7466);\n\n// `ToIndex` abstract operation\n// https://tc39.es/ecma262/#sec-toindex\nmodule.exports = function (it) {\n  if (it === undefined) return 0;\n  var number = toInteger(it);\n  var length = toLength(number);\n  if (number !== length) throw RangeError('Wrong length or index');\n  return length;\n};\n\n\n/***/ }),\n\n/***/ 5656:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_99996__) {\n\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = __nested_webpack_require_99996__(8361);\nvar requireObjectCoercible = __nested_webpack_require_99996__(4488);\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n\n\n/***/ }),\n\n/***/ 9958:\n/***/ (function(module) {\n\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `ToInteger` abstract operation\n// https://tc39.es/ecma262/#sec-tointeger\nmodule.exports = function (argument) {\n  return isNaN(argument = +argument) ? 0 : (argument > 0 ? floor : ceil)(argument);\n};\n\n\n/***/ }),\n\n/***/ 7466:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_100645__) {\n\nvar toInteger = __nested_webpack_require_100645__(9958);\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  return argument > 0 ? min(toInteger(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n\n\n/***/ }),\n\n/***/ 7908:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_101032__) {\n\nvar requireObjectCoercible = __nested_webpack_require_101032__(4488);\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};\n\n\n/***/ }),\n\n/***/ 4590:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_101355__) {\n\nvar toPositiveInteger = __nested_webpack_require_101355__(3002);\n\nmodule.exports = function (it, BYTES) {\n  var offset = toPositiveInteger(it);\n  if (offset % BYTES) throw RangeError('Wrong offset');\n  return offset;\n};\n\n\n/***/ }),\n\n/***/ 3002:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_101660__) {\n\nvar toInteger = __nested_webpack_require_101660__(9958);\n\nmodule.exports = function (it) {\n  var result = toInteger(it);\n  if (result < 0) throw RangeError(\"The argument can't be less than 0\");\n  return result;\n};\n\n\n/***/ }),\n\n/***/ 7593:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_101959__) {\n\nvar isObject = __nested_webpack_require_101959__(111);\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function (input, PREFERRED_STRING) {\n  if (!isObject(input)) return input;\n  var fn, val;\n  if (PREFERRED_STRING && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;\n  if (typeof (fn = input.valueOf) == 'function' && !isObject(val = fn.call(input))) return val;\n  if (!PREFERRED_STRING && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n\n\n/***/ }),\n\n/***/ 1694:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_102821__) {\n\nvar wellKnownSymbol = __nested_webpack_require_102821__(5112);\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n\n\n/***/ }),\n\n/***/ 9843:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_103114__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_103114__(2109);\nvar global = __nested_webpack_require_103114__(7854);\nvar DESCRIPTORS = __nested_webpack_require_103114__(9781);\nvar TYPED_ARRAYS_CONSTRUCTORS_REQUIRES_WRAPPERS = __nested_webpack_require_103114__(3832);\nvar ArrayBufferViewCore = __nested_webpack_require_103114__(260);\nvar ArrayBufferModule = __nested_webpack_require_103114__(3331);\nvar anInstance = __nested_webpack_require_103114__(5787);\nvar createPropertyDescriptor = __nested_webpack_require_103114__(9114);\nvar createNonEnumerableProperty = __nested_webpack_require_103114__(8880);\nvar toLength = __nested_webpack_require_103114__(7466);\nvar toIndex = __nested_webpack_require_103114__(7067);\nvar toOffset = __nested_webpack_require_103114__(4590);\nvar toPrimitive = __nested_webpack_require_103114__(7593);\nvar has = __nested_webpack_require_103114__(6656);\nvar classof = __nested_webpack_require_103114__(648);\nvar isObject = __nested_webpack_require_103114__(111);\nvar create = __nested_webpack_require_103114__(30);\nvar setPrototypeOf = __nested_webpack_require_103114__(7674);\nvar getOwnPropertyNames = __nested_webpack_require_103114__(8006).f;\nvar typedArrayFrom = __nested_webpack_require_103114__(7321);\nvar forEach = __nested_webpack_require_103114__(2092).forEach;\nvar setSpecies = __nested_webpack_require_103114__(6340);\nvar definePropertyModule = __nested_webpack_require_103114__(3070);\nvar getOwnPropertyDescriptorModule = __nested_webpack_require_103114__(1236);\nvar InternalStateModule = __nested_webpack_require_103114__(9909);\nvar inheritIfRequired = __nested_webpack_require_103114__(9587);\n\nvar getInternalState = InternalStateModule.get;\nvar setInternalState = InternalStateModule.set;\nvar nativeDefineProperty = definePropertyModule.f;\nvar nativeGetOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\nvar round = Math.round;\nvar RangeError = global.RangeError;\nvar ArrayBuffer = ArrayBufferModule.ArrayBuffer;\nvar DataView = ArrayBufferModule.DataView;\nvar NATIVE_ARRAY_BUFFER_VIEWS = ArrayBufferViewCore.NATIVE_ARRAY_BUFFER_VIEWS;\nvar TYPED_ARRAY_TAG = ArrayBufferViewCore.TYPED_ARRAY_TAG;\nvar TypedArray = ArrayBufferViewCore.TypedArray;\nvar TypedArrayPrototype = ArrayBufferViewCore.TypedArrayPrototype;\nvar aTypedArrayConstructor = ArrayBufferViewCore.aTypedArrayConstructor;\nvar isTypedArray = ArrayBufferViewCore.isTypedArray;\nvar BYTES_PER_ELEMENT = 'BYTES_PER_ELEMENT';\nvar WRONG_LENGTH = 'Wrong length';\n\nvar fromList = function (C, list) {\n  var index = 0;\n  var length = list.length;\n  var result = new (aTypedArrayConstructor(C))(length);\n  while (length > index) result[index] = list[index++];\n  return result;\n};\n\nvar addGetter = function (it, key) {\n  nativeDefineProperty(it, key, { get: function () {\n    return getInternalState(this)[key];\n  } });\n};\n\nvar isArrayBuffer = function (it) {\n  var klass;\n  return it instanceof ArrayBuffer || (klass = classof(it)) == 'ArrayBuffer' || klass == 'SharedArrayBuffer';\n};\n\nvar isTypedArrayIndex = function (target, key) {\n  return isTypedArray(target)\n    && typeof key != 'symbol'\n    && key in target\n    && String(+key) == String(key);\n};\n\nvar wrappedGetOwnPropertyDescriptor = function getOwnPropertyDescriptor(target, key) {\n  return isTypedArrayIndex(target, key = toPrimitive(key, true))\n    ? createPropertyDescriptor(2, target[key])\n    : nativeGetOwnPropertyDescriptor(target, key);\n};\n\nvar wrappedDefineProperty = function defineProperty(target, key, descriptor) {\n  if (isTypedArrayIndex(target, key = toPrimitive(key, true))\n    && isObject(descriptor)\n    && has(descriptor, 'value')\n    && !has(descriptor, 'get')\n    && !has(descriptor, 'set')\n    // TODO: add validation descriptor w/o calling accessors\n    && !descriptor.configurable\n    && (!has(descriptor, 'writable') || descriptor.writable)\n    && (!has(descriptor, 'enumerable') || descriptor.enumerable)\n  ) {\n    target[key] = descriptor.value;\n    return target;\n  } return nativeDefineProperty(target, key, descriptor);\n};\n\nif (DESCRIPTORS) {\n  if (!NATIVE_ARRAY_BUFFER_VIEWS) {\n    getOwnPropertyDescriptorModule.f = wrappedGetOwnPropertyDescriptor;\n    definePropertyModule.f = wrappedDefineProperty;\n    addGetter(TypedArrayPrototype, 'buffer');\n    addGetter(TypedArrayPrototype, 'byteOffset');\n    addGetter(TypedArrayPrototype, 'byteLength');\n    addGetter(TypedArrayPrototype, 'length');\n  }\n\n  $({ target: 'Object', stat: true, forced: !NATIVE_ARRAY_BUFFER_VIEWS }, {\n    getOwnPropertyDescriptor: wrappedGetOwnPropertyDescriptor,\n    defineProperty: wrappedDefineProperty\n  });\n\n  module.exports = function (TYPE, wrapper, CLAMPED) {\n    var BYTES = TYPE.match(/\\d+$/)[0] / 8;\n    var CONSTRUCTOR_NAME = TYPE + (CLAMPED ? 'Clamped' : '') + 'Array';\n    var GETTER = 'get' + TYPE;\n    var SETTER = 'set' + TYPE;\n    var NativeTypedArrayConstructor = global[CONSTRUCTOR_NAME];\n    var TypedArrayConstructor = NativeTypedArrayConstructor;\n    var TypedArrayConstructorPrototype = TypedArrayConstructor && TypedArrayConstructor.prototype;\n    var exported = {};\n\n    var getter = function (that, index) {\n      var data = getInternalState(that);\n      return data.view[GETTER](index * BYTES + data.byteOffset, true);\n    };\n\n    var setter = function (that, index, value) {\n      var data = getInternalState(that);\n      if (CLAMPED) value = (value = round(value)) < 0 ? 0 : value > 0xFF ? 0xFF : value & 0xFF;\n      data.view[SETTER](index * BYTES + data.byteOffset, value, true);\n    };\n\n    var addElement = function (that, index) {\n      nativeDefineProperty(that, index, {\n        get: function () {\n          return getter(this, index);\n        },\n        set: function (value) {\n          return setter(this, index, value);\n        },\n        enumerable: true\n      });\n    };\n\n    if (!NATIVE_ARRAY_BUFFER_VIEWS) {\n      TypedArrayConstructor = wrapper(function (that, data, offset, $length) {\n        anInstance(that, TypedArrayConstructor, CONSTRUCTOR_NAME);\n        var index = 0;\n        var byteOffset = 0;\n        var buffer, byteLength, length;\n        if (!isObject(data)) {\n          length = toIndex(data);\n          byteLength = length * BYTES;\n          buffer = new ArrayBuffer(byteLength);\n        } else if (isArrayBuffer(data)) {\n          buffer = data;\n          byteOffset = toOffset(offset, BYTES);\n          var $len = data.byteLength;\n          if ($length === undefined) {\n            if ($len % BYTES) throw RangeError(WRONG_LENGTH);\n            byteLength = $len - byteOffset;\n            if (byteLength < 0) throw RangeError(WRONG_LENGTH);\n          } else {\n            byteLength = toLength($length) * BYTES;\n            if (byteLength + byteOffset > $len) throw RangeError(WRONG_LENGTH);\n          }\n          length = byteLength / BYTES;\n        } else if (isTypedArray(data)) {\n          return fromList(TypedArrayConstructor, data);\n        } else {\n          return typedArrayFrom.call(TypedArrayConstructor, data);\n        }\n        setInternalState(that, {\n          buffer: buffer,\n          byteOffset: byteOffset,\n          byteLength: byteLength,\n          length: length,\n          view: new DataView(buffer)\n        });\n        while (index < length) addElement(that, index++);\n      });\n\n      if (setPrototypeOf) setPrototypeOf(TypedArrayConstructor, TypedArray);\n      TypedArrayConstructorPrototype = TypedArrayConstructor.prototype = create(TypedArrayPrototype);\n    } else if (TYPED_ARRAYS_CONSTRUCTORS_REQUIRES_WRAPPERS) {\n      TypedArrayConstructor = wrapper(function (dummy, data, typedArrayOffset, $length) {\n        anInstance(dummy, TypedArrayConstructor, CONSTRUCTOR_NAME);\n        return inheritIfRequired(function () {\n          if (!isObject(data)) return new NativeTypedArrayConstructor(toIndex(data));\n          if (isArrayBuffer(data)) return $length !== undefined\n            ? new NativeTypedArrayConstructor(data, toOffset(typedArrayOffset, BYTES), $length)\n            : typedArrayOffset !== undefined\n              ? new NativeTypedArrayConstructor(data, toOffset(typedArrayOffset, BYTES))\n              : new NativeTypedArrayConstructor(data);\n          if (isTypedArray(data)) return fromList(TypedArrayConstructor, data);\n          return typedArrayFrom.call(TypedArrayConstructor, data);\n        }(), dummy, TypedArrayConstructor);\n      });\n\n      if (setPrototypeOf) setPrototypeOf(TypedArrayConstructor, TypedArray);\n      forEach(getOwnPropertyNames(NativeTypedArrayConstructor), function (key) {\n        if (!(key in TypedArrayConstructor)) {\n          createNonEnumerableProperty(TypedArrayConstructor, key, NativeTypedArrayConstructor[key]);\n        }\n      });\n      TypedArrayConstructor.prototype = TypedArrayConstructorPrototype;\n    }\n\n    if (TypedArrayConstructorPrototype.constructor !== TypedArrayConstructor) {\n      createNonEnumerableProperty(TypedArrayConstructorPrototype, 'constructor', TypedArrayConstructor);\n    }\n\n    if (TYPED_ARRAY_TAG) {\n      createNonEnumerableProperty(TypedArrayConstructorPrototype, TYPED_ARRAY_TAG, CONSTRUCTOR_NAME);\n    }\n\n    exported[CONSTRUCTOR_NAME] = TypedArrayConstructor;\n\n    $({\n      global: true, forced: TypedArrayConstructor != NativeTypedArrayConstructor, sham: !NATIVE_ARRAY_BUFFER_VIEWS\n    }, exported);\n\n    if (!(BYTES_PER_ELEMENT in TypedArrayConstructor)) {\n      createNonEnumerableProperty(TypedArrayConstructor, BYTES_PER_ELEMENT, BYTES);\n    }\n\n    if (!(BYTES_PER_ELEMENT in TypedArrayConstructorPrototype)) {\n      createNonEnumerableProperty(TypedArrayConstructorPrototype, BYTES_PER_ELEMENT, BYTES);\n    }\n\n    setSpecies(CONSTRUCTOR_NAME);\n  };\n} else module.exports = function () { /* empty */ };\n\n\n/***/ }),\n\n/***/ 3832:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_112503__) {\n\n/* eslint-disable no-new -- required for testing */\nvar global = __nested_webpack_require_112503__(7854);\nvar fails = __nested_webpack_require_112503__(7293);\nvar checkCorrectnessOfIteration = __nested_webpack_require_112503__(7072);\nvar NATIVE_ARRAY_BUFFER_VIEWS = __nested_webpack_require_112503__(260).NATIVE_ARRAY_BUFFER_VIEWS;\n\nvar ArrayBuffer = global.ArrayBuffer;\nvar Int8Array = global.Int8Array;\n\nmodule.exports = !NATIVE_ARRAY_BUFFER_VIEWS || !fails(function () {\n  Int8Array(1);\n}) || !fails(function () {\n  new Int8Array(-1);\n}) || !checkCorrectnessOfIteration(function (iterable) {\n  new Int8Array();\n  new Int8Array(null);\n  new Int8Array(1.5);\n  new Int8Array(iterable);\n}, true) || fails(function () {\n  // Safari (11+) bug - a reason why even Safari 13 should load a typed array polyfill\n  return new Int8Array(new ArrayBuffer(2), 1, undefined).length !== 1;\n});\n\n\n/***/ }),\n\n/***/ 3074:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_113426__) {\n\nvar aTypedArrayConstructor = __nested_webpack_require_113426__(260).aTypedArrayConstructor;\nvar speciesConstructor = __nested_webpack_require_113426__(6707);\n\nmodule.exports = function (instance, list) {\n  var C = speciesConstructor(instance, instance.constructor);\n  var index = 0;\n  var length = list.length;\n  var result = new (aTypedArrayConstructor(C))(length);\n  while (length > index) result[index] = list[index++];\n  return result;\n};\n\n\n/***/ }),\n\n/***/ 7321:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_113940__) {\n\nvar toObject = __nested_webpack_require_113940__(7908);\nvar toLength = __nested_webpack_require_113940__(7466);\nvar getIteratorMethod = __nested_webpack_require_113940__(1246);\nvar isArrayIteratorMethod = __nested_webpack_require_113940__(7659);\nvar bind = __nested_webpack_require_113940__(9974);\nvar aTypedArrayConstructor = __nested_webpack_require_113940__(260).aTypedArrayConstructor;\n\nmodule.exports = function from(source /* , mapfn, thisArg */) {\n  var O = toObject(source);\n  var argumentsLength = arguments.length;\n  var mapfn = argumentsLength > 1 ? arguments[1] : undefined;\n  var mapping = mapfn !== undefined;\n  var iteratorMethod = getIteratorMethod(O);\n  var i, length, result, step, iterator, next;\n  if (iteratorMethod != undefined && !isArrayIteratorMethod(iteratorMethod)) {\n    iterator = iteratorMethod.call(O);\n    next = iterator.next;\n    O = [];\n    while (!(step = next.call(iterator)).done) {\n      O.push(step.value);\n    }\n  }\n  if (mapping && argumentsLength > 2) {\n    mapfn = bind(mapfn, arguments[2], 2);\n  }\n  length = toLength(O.length);\n  result = new (aTypedArrayConstructor(this))(length);\n  for (i = 0; length > i; i++) {\n    result[i] = mapping ? mapfn(O[i], i) : O[i];\n  }\n  return result;\n};\n\n\n/***/ }),\n\n/***/ 9711:\n/***/ (function(module) {\n\nvar id = 0;\nvar postfix = Math.random();\n\nmodule.exports = function (key) {\n  return 'Symbol(' + String(key === undefined ? '' : key) + ')_' + (++id + postfix).toString(36);\n};\n\n\n/***/ }),\n\n/***/ 3307:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_115419__) {\n\nvar NATIVE_SYMBOL = __nested_webpack_require_115419__(133);\n\nmodule.exports = NATIVE_SYMBOL\n  /* global Symbol -- safe */\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n\n\n/***/ }),\n\n/***/ 5112:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_115685__) {\n\nvar global = __nested_webpack_require_115685__(7854);\nvar shared = __nested_webpack_require_115685__(2309);\nvar has = __nested_webpack_require_115685__(6656);\nvar uid = __nested_webpack_require_115685__(9711);\nvar NATIVE_SYMBOL = __nested_webpack_require_115685__(133);\nvar USE_SYMBOL_AS_UID = __nested_webpack_require_115685__(3307);\n\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!has(WellKnownSymbolsStore, name)) {\n    if (NATIVE_SYMBOL && has(Symbol, name)) WellKnownSymbolsStore[name] = Symbol[name];\n    else WellKnownSymbolsStore[name] = createWellKnownSymbol('Symbol.' + name);\n  } return WellKnownSymbolsStore[name];\n};\n\n\n/***/ }),\n\n/***/ 1361:\n/***/ (function(module) {\n\n// a string of all valid unicode whitespaces\nmodule.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' +\n  '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n\n\n/***/ }),\n\n/***/ 8264:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_116785__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_116785__(2109);\nvar global = __nested_webpack_require_116785__(7854);\nvar arrayBufferModule = __nested_webpack_require_116785__(3331);\nvar setSpecies = __nested_webpack_require_116785__(6340);\n\nvar ARRAY_BUFFER = 'ArrayBuffer';\nvar ArrayBuffer = arrayBufferModule[ARRAY_BUFFER];\nvar NativeArrayBuffer = global[ARRAY_BUFFER];\n\n// `ArrayBuffer` constructor\n// https://tc39.es/ecma262/#sec-arraybuffer-constructor\n$({ global: true, forced: NativeArrayBuffer !== ArrayBuffer }, {\n  ArrayBuffer: ArrayBuffer\n});\n\nsetSpecies(ARRAY_BUFFER);\n\n\n/***/ }),\n\n/***/ 2222:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_117427__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_117427__(2109);\nvar fails = __nested_webpack_require_117427__(7293);\nvar isArray = __nested_webpack_require_117427__(3157);\nvar isObject = __nested_webpack_require_117427__(111);\nvar toObject = __nested_webpack_require_117427__(7908);\nvar toLength = __nested_webpack_require_117427__(7466);\nvar createProperty = __nested_webpack_require_117427__(6135);\nvar arraySpeciesCreate = __nested_webpack_require_117427__(5417);\nvar arrayMethodHasSpeciesSupport = __nested_webpack_require_117427__(1194);\nvar wellKnownSymbol = __nested_webpack_require_117427__(5112);\nvar V8_VERSION = __nested_webpack_require_117427__(7392);\n\nvar IS_CONCAT_SPREADABLE = wellKnownSymbol('isConcatSpreadable');\nvar MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFF;\nvar MAXIMUM_ALLOWED_INDEX_EXCEEDED = 'Maximum allowed index exceeded';\n\n// We can't use this feature detection in V8 since it causes\n// deoptimization and serious performance degradation\n// https://github.com/zloirock/core-js/issues/679\nvar IS_CONCAT_SPREADABLE_SUPPORT = V8_VERSION >= 51 || !fails(function () {\n  var array = [];\n  array[IS_CONCAT_SPREADABLE] = false;\n  return array.concat()[0] !== array;\n});\n\nvar SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('concat');\n\nvar isConcatSpreadable = function (O) {\n  if (!isObject(O)) return false;\n  var spreadable = O[IS_CONCAT_SPREADABLE];\n  return spreadable !== undefined ? !!spreadable : isArray(O);\n};\n\nvar FORCED = !IS_CONCAT_SPREADABLE_SUPPORT || !SPECIES_SUPPORT;\n\n// `Array.prototype.concat` method\n// https://tc39.es/ecma262/#sec-array.prototype.concat\n// with adding support of @@isConcatSpreadable and @@species\n$({ target: 'Array', proto: true, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  concat: function concat(arg) {\n    var O = toObject(this);\n    var A = arraySpeciesCreate(O, 0);\n    var n = 0;\n    var i, k, length, len, E;\n    for (i = -1, length = arguments.length; i < length; i++) {\n      E = i === -1 ? O : arguments[i];\n      if (isConcatSpreadable(E)) {\n        len = toLength(E.length);\n        if (n + len > MAX_SAFE_INTEGER) throw TypeError(MAXIMUM_ALLOWED_INDEX_EXCEEDED);\n        for (k = 0; k < len; k++, n++) if (k in E) createProperty(A, n, E[k]);\n      } else {\n        if (n >= MAX_SAFE_INTEGER) throw TypeError(MAXIMUM_ALLOWED_INDEX_EXCEEDED);\n        createProperty(A, n++, E);\n      }\n    }\n    A.length = n;\n    return A;\n  }\n});\n\n\n/***/ }),\n\n/***/ 7327:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_119834__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_119834__(2109);\nvar $filter = __nested_webpack_require_119834__(2092).filter;\nvar arrayMethodHasSpeciesSupport = __nested_webpack_require_119834__(1194);\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('filter');\n\n// `Array.prototype.filter` method\n// https://tc39.es/ecma262/#sec-array.prototype.filter\n// with adding support of @@species\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  filter: function filter(callbackfn /* , thisArg */) {\n    return $filter(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n\n/***/ }),\n\n/***/ 2772:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_120523__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_120523__(2109);\nvar $indexOf = __nested_webpack_require_120523__(1318).indexOf;\nvar arrayMethodIsStrict = __nested_webpack_require_120523__(9341);\n\nvar nativeIndexOf = [].indexOf;\n\nvar NEGATIVE_ZERO = !!nativeIndexOf && 1 / [1].indexOf(1, -0) < 0;\nvar STRICT_METHOD = arrayMethodIsStrict('indexOf');\n\n// `Array.prototype.indexOf` method\n// https://tc39.es/ecma262/#sec-array.prototype.indexof\n$({ target: 'Array', proto: true, forced: NEGATIVE_ZERO || !STRICT_METHOD }, {\n  indexOf: function indexOf(searchElement /* , fromIndex = 0 */) {\n    return NEGATIVE_ZERO\n      // convert -0 to +0\n      ? nativeIndexOf.apply(this, arguments) || 0\n      : $indexOf(this, searchElement, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n\n/***/ }),\n\n/***/ 6992:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_121364__) {\n\n\"use strict\";\n\nvar toIndexedObject = __nested_webpack_require_121364__(5656);\nvar addToUnscopables = __nested_webpack_require_121364__(1223);\nvar Iterators = __nested_webpack_require_121364__(7497);\nvar InternalStateModule = __nested_webpack_require_121364__(9909);\nvar defineIterator = __nested_webpack_require_121364__(654);\n\nvar ARRAY_ITERATOR = 'Array Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);\n\n// `Array.prototype.entries` method\n// https://tc39.es/ecma262/#sec-array.prototype.entries\n// `Array.prototype.keys` method\n// https://tc39.es/ecma262/#sec-array.prototype.keys\n// `Array.prototype.values` method\n// https://tc39.es/ecma262/#sec-array.prototype.values\n// `Array.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-array.prototype-@@iterator\n// `CreateArrayIterator` internal method\n// https://tc39.es/ecma262/#sec-createarrayiterator\nmodule.exports = defineIterator(Array, 'Array', function (iterated, kind) {\n  setInternalState(this, {\n    type: ARRAY_ITERATOR,\n    target: toIndexedObject(iterated), // target\n    index: 0,                          // next index\n    kind: kind                         // kind\n  });\n// `%ArrayIteratorPrototype%.next` method\n// https://tc39.es/ecma262/#sec-%arrayiteratorprototype%.next\n}, function () {\n  var state = getInternalState(this);\n  var target = state.target;\n  var kind = state.kind;\n  var index = state.index++;\n  if (!target || index >= target.length) {\n    state.target = undefined;\n    return { value: undefined, done: true };\n  }\n  if (kind == 'keys') return { value: index, done: false };\n  if (kind == 'values') return { value: target[index], done: false };\n  return { value: [index, target[index]], done: false };\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values%\n// https://tc39.es/ecma262/#sec-createunmappedargumentsobject\n// https://tc39.es/ecma262/#sec-createmappedargumentsobject\nIterators.Arguments = Iterators.Array;\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n\n/***/ }),\n\n/***/ 1249:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_123565__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_123565__(2109);\nvar $map = __nested_webpack_require_123565__(2092).map;\nvar arrayMethodHasSpeciesSupport = __nested_webpack_require_123565__(1194);\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('map');\n\n// `Array.prototype.map` method\n// https://tc39.es/ecma262/#sec-array.prototype.map\n// with adding support of @@species\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  map: function map(callbackfn /* , thisArg */) {\n    return $map(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n\n/***/ }),\n\n/***/ 7042:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_124230__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_124230__(2109);\nvar isObject = __nested_webpack_require_124230__(111);\nvar isArray = __nested_webpack_require_124230__(3157);\nvar toAbsoluteIndex = __nested_webpack_require_124230__(1400);\nvar toLength = __nested_webpack_require_124230__(7466);\nvar toIndexedObject = __nested_webpack_require_124230__(5656);\nvar createProperty = __nested_webpack_require_124230__(6135);\nvar wellKnownSymbol = __nested_webpack_require_124230__(5112);\nvar arrayMethodHasSpeciesSupport = __nested_webpack_require_124230__(1194);\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('slice');\n\nvar SPECIES = wellKnownSymbol('species');\nvar nativeSlice = [].slice;\nvar max = Math.max;\n\n// `Array.prototype.slice` method\n// https://tc39.es/ecma262/#sec-array.prototype.slice\n// fallback for not array-like ES3 strings and DOM objects\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  slice: function slice(start, end) {\n    var O = toIndexedObject(this);\n    var length = toLength(O.length);\n    var k = toAbsoluteIndex(start, length);\n    var fin = toAbsoluteIndex(end === undefined ? length : end, length);\n    // inline `ArraySpeciesCreate` for usage native `Array#slice` where it's possible\n    var Constructor, result, n;\n    if (isArray(O)) {\n      Constructor = O.constructor;\n      // cross-realm fallback\n      if (typeof Constructor == 'function' && (Constructor === Array || isArray(Constructor.prototype))) {\n        Constructor = undefined;\n      } else if (isObject(Constructor)) {\n        Constructor = Constructor[SPECIES];\n        if (Constructor === null) Constructor = undefined;\n      }\n      if (Constructor === Array || Constructor === undefined) {\n        return nativeSlice.call(O, k, fin);\n      }\n    }\n    result = new (Constructor === undefined ? Array : Constructor)(max(fin - k, 0));\n    for (n = 0; k < fin; k++, n++) if (k in O) createProperty(result, n, O[k]);\n    result.length = n;\n    return result;\n  }\n});\n\n\n/***/ }),\n\n/***/ 561:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_126211__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_126211__(2109);\nvar toAbsoluteIndex = __nested_webpack_require_126211__(1400);\nvar toInteger = __nested_webpack_require_126211__(9958);\nvar toLength = __nested_webpack_require_126211__(7466);\nvar toObject = __nested_webpack_require_126211__(7908);\nvar arraySpeciesCreate = __nested_webpack_require_126211__(5417);\nvar createProperty = __nested_webpack_require_126211__(6135);\nvar arrayMethodHasSpeciesSupport = __nested_webpack_require_126211__(1194);\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('splice');\n\nvar max = Math.max;\nvar min = Math.min;\nvar MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFF;\nvar MAXIMUM_ALLOWED_LENGTH_EXCEEDED = 'Maximum allowed length exceeded';\n\n// `Array.prototype.splice` method\n// https://tc39.es/ecma262/#sec-array.prototype.splice\n// with adding support of @@species\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  splice: function splice(start, deleteCount /* , ...items */) {\n    var O = toObject(this);\n    var len = toLength(O.length);\n    var actualStart = toAbsoluteIndex(start, len);\n    var argumentsLength = arguments.length;\n    var insertCount, actualDeleteCount, A, k, from, to;\n    if (argumentsLength === 0) {\n      insertCount = actualDeleteCount = 0;\n    } else if (argumentsLength === 1) {\n      insertCount = 0;\n      actualDeleteCount = len - actualStart;\n    } else {\n      insertCount = argumentsLength - 2;\n      actualDeleteCount = min(max(toInteger(deleteCount), 0), len - actualStart);\n    }\n    if (len + insertCount - actualDeleteCount > MAX_SAFE_INTEGER) {\n      throw TypeError(MAXIMUM_ALLOWED_LENGTH_EXCEEDED);\n    }\n    A = arraySpeciesCreate(O, actualDeleteCount);\n    for (k = 0; k < actualDeleteCount; k++) {\n      from = actualStart + k;\n      if (from in O) createProperty(A, k, O[from]);\n    }\n    A.length = actualDeleteCount;\n    if (insertCount < actualDeleteCount) {\n      for (k = actualStart; k < len - actualDeleteCount; k++) {\n        from = k + actualDeleteCount;\n        to = k + insertCount;\n        if (from in O) O[to] = O[from];\n        else delete O[to];\n      }\n      for (k = len; k > len - actualDeleteCount + insertCount; k--) delete O[k - 1];\n    } else if (insertCount > actualDeleteCount) {\n      for (k = len - actualDeleteCount; k > actualStart; k--) {\n        from = k + actualDeleteCount - 1;\n        to = k + insertCount - 1;\n        if (from in O) O[to] = O[from];\n        else delete O[to];\n      }\n    }\n    for (k = 0; k < insertCount; k++) {\n      O[k + actualStart] = arguments[k + 2];\n    }\n    O.length = len - actualDeleteCount + insertCount;\n    return A;\n  }\n});\n\n\n/***/ }),\n\n/***/ 8309:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_128857__) {\n\nvar DESCRIPTORS = __nested_webpack_require_128857__(9781);\nvar defineProperty = __nested_webpack_require_128857__(3070).f;\n\nvar FunctionPrototype = Function.prototype;\nvar FunctionPrototypeToString = FunctionPrototype.toString;\nvar nameRE = /^\\s*function ([^ (]*)/;\nvar NAME = 'name';\n\n// Function instances `.name` property\n// https://tc39.es/ecma262/#sec-function-instances-name\nif (DESCRIPTORS && !(NAME in FunctionPrototype)) {\n  defineProperty(FunctionPrototype, NAME, {\n    configurable: true,\n    get: function () {\n      try {\n        return FunctionPrototypeToString.call(this).match(nameRE)[1];\n      } catch (error) {\n        return '';\n      }\n    }\n  });\n}\n\n\n/***/ }),\n\n/***/ 489:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_129614__) {\n\nvar $ = __nested_webpack_require_129614__(2109);\nvar fails = __nested_webpack_require_129614__(7293);\nvar toObject = __nested_webpack_require_129614__(7908);\nvar nativeGetPrototypeOf = __nested_webpack_require_129614__(9518);\nvar CORRECT_PROTOTYPE_GETTER = __nested_webpack_require_129614__(8544);\n\nvar FAILS_ON_PRIMITIVES = fails(function () { nativeGetPrototypeOf(1); });\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\n$({ target: 'Object', stat: true, forced: FAILS_ON_PRIMITIVES, sham: !CORRECT_PROTOTYPE_GETTER }, {\n  getPrototypeOf: function getPrototypeOf(it) {\n    return nativeGetPrototypeOf(toObject(it));\n  }\n});\n\n\n\n/***/ }),\n\n/***/ 1539:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_130327__) {\n\nvar TO_STRING_TAG_SUPPORT = __nested_webpack_require_130327__(1694);\nvar redefine = __nested_webpack_require_130327__(1320);\nvar toString = __nested_webpack_require_130327__(288);\n\n// `Object.prototype.toString` method\n// https://tc39.es/ecma262/#sec-object.prototype.tostring\nif (!TO_STRING_TAG_SUPPORT) {\n  redefine(Object.prototype, 'toString', toString, { unsafe: true });\n}\n\n\n/***/ }),\n\n/***/ 4916:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_130780__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_130780__(2109);\nvar exec = __nested_webpack_require_130780__(2261);\n\n// `RegExp.prototype.exec` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.exec\n$({ target: 'RegExp', proto: true, forced: /./.exec !== exec }, {\n  exec: exec\n});\n\n\n/***/ }),\n\n/***/ 9714:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_131156__) {\n\n\"use strict\";\n\nvar redefine = __nested_webpack_require_131156__(1320);\nvar anObject = __nested_webpack_require_131156__(9670);\nvar fails = __nested_webpack_require_131156__(7293);\nvar flags = __nested_webpack_require_131156__(7066);\n\nvar TO_STRING = 'toString';\nvar RegExpPrototype = RegExp.prototype;\nvar nativeToString = RegExpPrototype[TO_STRING];\n\nvar NOT_GENERIC = fails(function () { return nativeToString.call({ source: 'a', flags: 'b' }) != '/a/b'; });\n// FF44- RegExp#toString has a wrong name\nvar INCORRECT_NAME = nativeToString.name != TO_STRING;\n\n// `RegExp.prototype.toString` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.tostring\nif (NOT_GENERIC || INCORRECT_NAME) {\n  redefine(RegExp.prototype, TO_STRING, function toString() {\n    var R = anObject(this);\n    var p = String(R.source);\n    var rf = R.flags;\n    var f = String(rf === undefined && R instanceof RegExp && !('flags' in RegExpPrototype) ? flags.call(R) : rf);\n    return '/' + p + '/' + f;\n  }, { unsafe: true });\n}\n\n\n/***/ }),\n\n/***/ 8783:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_132221__) {\n\n\"use strict\";\n\nvar charAt = __nested_webpack_require_132221__(8710).charAt;\nvar InternalStateModule = __nested_webpack_require_132221__(9909);\nvar defineIterator = __nested_webpack_require_132221__(654);\n\nvar STRING_ITERATOR = 'String Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(STRING_ITERATOR);\n\n// `String.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-string.prototype-@@iterator\ndefineIterator(String, 'String', function (iterated) {\n  setInternalState(this, {\n    type: STRING_ITERATOR,\n    string: String(iterated),\n    index: 0\n  });\n// `%StringIteratorPrototype%.next` method\n// https://tc39.es/ecma262/#sec-%stringiteratorprototype%.next\n}, function next() {\n  var state = getInternalState(this);\n  var string = state.string;\n  var index = state.index;\n  var point;\n  if (index >= string.length) return { value: undefined, done: true };\n  point = charAt(string, index);\n  state.index += point.length;\n  return { value: point, done: false };\n});\n\n\n/***/ }),\n\n/***/ 4723:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_133333__) {\n\n\"use strict\";\n\nvar fixRegExpWellKnownSymbolLogic = __nested_webpack_require_133333__(7007);\nvar anObject = __nested_webpack_require_133333__(9670);\nvar toLength = __nested_webpack_require_133333__(7466);\nvar requireObjectCoercible = __nested_webpack_require_133333__(4488);\nvar advanceStringIndex = __nested_webpack_require_133333__(1530);\nvar regExpExec = __nested_webpack_require_133333__(7651);\n\n// @@match logic\nfixRegExpWellKnownSymbolLogic('match', 1, function (MATCH, nativeMatch, maybeCallNative) {\n  return [\n    // `String.prototype.match` method\n    // https://tc39.es/ecma262/#sec-string.prototype.match\n    function match(regexp) {\n      var O = requireObjectCoercible(this);\n      var matcher = regexp == undefined ? undefined : regexp[MATCH];\n      return matcher !== undefined ? matcher.call(regexp, O) : new RegExp(regexp)[MATCH](String(O));\n    },\n    // `RegExp.prototype[@@match]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@match\n    function (regexp) {\n      var res = maybeCallNative(nativeMatch, regexp, this);\n      if (res.done) return res.value;\n\n      var rx = anObject(regexp);\n      var S = String(this);\n\n      if (!rx.global) return regExpExec(rx, S);\n\n      var fullUnicode = rx.unicode;\n      rx.lastIndex = 0;\n      var A = [];\n      var n = 0;\n      var result;\n      while ((result = regExpExec(rx, S)) !== null) {\n        var matchStr = String(result[0]);\n        A[n] = matchStr;\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n        n++;\n      }\n      return n === 0 ? null : A;\n    }\n  ];\n});\n\n\n/***/ }),\n\n/***/ 5306:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_134975__) {\n\n\"use strict\";\n\nvar fixRegExpWellKnownSymbolLogic = __nested_webpack_require_134975__(7007);\nvar anObject = __nested_webpack_require_134975__(9670);\nvar toLength = __nested_webpack_require_134975__(7466);\nvar toInteger = __nested_webpack_require_134975__(9958);\nvar requireObjectCoercible = __nested_webpack_require_134975__(4488);\nvar advanceStringIndex = __nested_webpack_require_134975__(1530);\nvar getSubstitution = __nested_webpack_require_134975__(647);\nvar regExpExec = __nested_webpack_require_134975__(7651);\n\nvar max = Math.max;\nvar min = Math.min;\n\nvar maybeToString = function (it) {\n  return it === undefined ? it : String(it);\n};\n\n// @@replace logic\nfixRegExpWellKnownSymbolLogic('replace', 2, function (REPLACE, nativeReplace, maybeCallNative, reason) {\n  var REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE = reason.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE;\n  var REPLACE_KEEPS_$0 = reason.REPLACE_KEEPS_$0;\n  var UNSAFE_SUBSTITUTE = REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE ? '$' : '$0';\n\n  return [\n    // `String.prototype.replace` method\n    // https://tc39.es/ecma262/#sec-string.prototype.replace\n    function replace(searchValue, replaceValue) {\n      var O = requireObjectCoercible(this);\n      var replacer = searchValue == undefined ? undefined : searchValue[REPLACE];\n      return replacer !== undefined\n        ? replacer.call(searchValue, O, replaceValue)\n        : nativeReplace.call(String(O), searchValue, replaceValue);\n    },\n    // `RegExp.prototype[@@replace]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@replace\n    function (regexp, replaceValue) {\n      if (\n        (!REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE && REPLACE_KEEPS_$0) ||\n        (typeof replaceValue === 'string' && replaceValue.indexOf(UNSAFE_SUBSTITUTE) === -1)\n      ) {\n        var res = maybeCallNative(nativeReplace, regexp, this, replaceValue);\n        if (res.done) return res.value;\n      }\n\n      var rx = anObject(regexp);\n      var S = String(this);\n\n      var functionalReplace = typeof replaceValue === 'function';\n      if (!functionalReplace) replaceValue = String(replaceValue);\n\n      var global = rx.global;\n      if (global) {\n        var fullUnicode = rx.unicode;\n        rx.lastIndex = 0;\n      }\n      var results = [];\n      while (true) {\n        var result = regExpExec(rx, S);\n        if (result === null) break;\n\n        results.push(result);\n        if (!global) break;\n\n        var matchStr = String(result[0]);\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n      }\n\n      var accumulatedResult = '';\n      var nextSourcePosition = 0;\n      for (var i = 0; i < results.length; i++) {\n        result = results[i];\n\n        var matched = String(result[0]);\n        var position = max(min(toInteger(result.index), S.length), 0);\n        var captures = [];\n        // NOTE: This is equivalent to\n        //   captures = result.slice(1).map(maybeToString)\n        // but for some reason `nativeSlice.call(result, 1, result.length)` (called in\n        // the slice polyfill when slicing native arrays) \"doesn't work\" in safari 9 and\n        // causes a crash (https://pastebin.com/N21QzeQA) when trying to debug it.\n        for (var j = 1; j < result.length; j++) captures.push(maybeToString(result[j]));\n        var namedCaptures = result.groups;\n        if (functionalReplace) {\n          var replacerArgs = [matched].concat(captures, position, S);\n          if (namedCaptures !== undefined) replacerArgs.push(namedCaptures);\n          var replacement = String(replaceValue.apply(undefined, replacerArgs));\n        } else {\n          replacement = getSubstitution(matched, S, position, captures, namedCaptures, replaceValue);\n        }\n        if (position >= nextSourcePosition) {\n          accumulatedResult += S.slice(nextSourcePosition, position) + replacement;\n          nextSourcePosition = position + matched.length;\n        }\n      }\n      return accumulatedResult + S.slice(nextSourcePosition);\n    }\n  ];\n});\n\n\n/***/ }),\n\n/***/ 3123:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_139010__) {\n\n\"use strict\";\n\nvar fixRegExpWellKnownSymbolLogic = __nested_webpack_require_139010__(7007);\nvar isRegExp = __nested_webpack_require_139010__(7850);\nvar anObject = __nested_webpack_require_139010__(9670);\nvar requireObjectCoercible = __nested_webpack_require_139010__(4488);\nvar speciesConstructor = __nested_webpack_require_139010__(6707);\nvar advanceStringIndex = __nested_webpack_require_139010__(1530);\nvar toLength = __nested_webpack_require_139010__(7466);\nvar callRegExpExec = __nested_webpack_require_139010__(7651);\nvar regexpExec = __nested_webpack_require_139010__(2261);\nvar fails = __nested_webpack_require_139010__(7293);\n\nvar arrayPush = [].push;\nvar min = Math.min;\nvar MAX_UINT32 = 0xFFFFFFFF;\n\n// babel-minify transpiles RegExp('x', 'y') -> /x/y and it causes SyntaxError\nvar SUPPORTS_Y = !fails(function () { return !RegExp(MAX_UINT32, 'y'); });\n\n// @@split logic\nfixRegExpWellKnownSymbolLogic('split', 2, function (SPLIT, nativeSplit, maybeCallNative) {\n  var internalSplit;\n  if (\n    'abbc'.split(/(b)*/)[1] == 'c' ||\n    // eslint-disable-next-line regexp/no-empty-group -- required for testing\n    'test'.split(/(?:)/, -1).length != 4 ||\n    'ab'.split(/(?:ab)*/).length != 2 ||\n    '.'.split(/(.?)(.?)/).length != 4 ||\n    // eslint-disable-next-line regexp/no-assertion-capturing-group, regexp/no-empty-group -- required for testing\n    '.'.split(/()()/).length > 1 ||\n    ''.split(/.?/).length\n  ) {\n    // based on es5-shim implementation, need to rework it\n    internalSplit = function (separator, limit) {\n      var string = String(requireObjectCoercible(this));\n      var lim = limit === undefined ? MAX_UINT32 : limit >>> 0;\n      if (lim === 0) return [];\n      if (separator === undefined) return [string];\n      // If `separator` is not a regex, use native split\n      if (!isRegExp(separator)) {\n        return nativeSplit.call(string, separator, lim);\n      }\n      var output = [];\n      var flags = (separator.ignoreCase ? 'i' : '') +\n                  (separator.multiline ? 'm' : '') +\n                  (separator.unicode ? 'u' : '') +\n                  (separator.sticky ? 'y' : '');\n      var lastLastIndex = 0;\n      // Make `global` and avoid `lastIndex` issues by working with a copy\n      var separatorCopy = new RegExp(separator.source, flags + 'g');\n      var match, lastIndex, lastLength;\n      while (match = regexpExec.call(separatorCopy, string)) {\n        lastIndex = separatorCopy.lastIndex;\n        if (lastIndex > lastLastIndex) {\n          output.push(string.slice(lastLastIndex, match.index));\n          if (match.length > 1 && match.index < string.length) arrayPush.apply(output, match.slice(1));\n          lastLength = match[0].length;\n          lastLastIndex = lastIndex;\n          if (output.length >= lim) break;\n        }\n        if (separatorCopy.lastIndex === match.index) separatorCopy.lastIndex++; // Avoid an infinite loop\n      }\n      if (lastLastIndex === string.length) {\n        if (lastLength || !separatorCopy.test('')) output.push('');\n      } else output.push(string.slice(lastLastIndex));\n      return output.length > lim ? output.slice(0, lim) : output;\n    };\n  // Chakra, V8\n  } else if ('0'.split(undefined, 0).length) {\n    internalSplit = function (separator, limit) {\n      return separator === undefined && limit === 0 ? [] : nativeSplit.call(this, separator, limit);\n    };\n  } else internalSplit = nativeSplit;\n\n  return [\n    // `String.prototype.split` method\n    // https://tc39.es/ecma262/#sec-string.prototype.split\n    function split(separator, limit) {\n      var O = requireObjectCoercible(this);\n      var splitter = separator == undefined ? undefined : separator[SPLIT];\n      return splitter !== undefined\n        ? splitter.call(separator, O, limit)\n        : internalSplit.call(String(O), separator, limit);\n    },\n    // `RegExp.prototype[@@split]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@split\n    //\n    // NOTE: This cannot be properly polyfilled in engines that don't support\n    // the 'y' flag.\n    function (regexp, limit) {\n      var res = maybeCallNative(internalSplit, regexp, this, limit, internalSplit !== nativeSplit);\n      if (res.done) return res.value;\n\n      var rx = anObject(regexp);\n      var S = String(this);\n      var C = speciesConstructor(rx, RegExp);\n\n      var unicodeMatching = rx.unicode;\n      var flags = (rx.ignoreCase ? 'i' : '') +\n                  (rx.multiline ? 'm' : '') +\n                  (rx.unicode ? 'u' : '') +\n                  (SUPPORTS_Y ? 'y' : 'g');\n\n      // ^(? + rx + ) is needed, in combination with some S slicing, to\n      // simulate the 'y' flag.\n      var splitter = new C(SUPPORTS_Y ? rx : '^(?:' + rx.source + ')', flags);\n      var lim = limit === undefined ? MAX_UINT32 : limit >>> 0;\n      if (lim === 0) return [];\n      if (S.length === 0) return callRegExpExec(splitter, S) === null ? [S] : [];\n      var p = 0;\n      var q = 0;\n      var A = [];\n      while (q < S.length) {\n        splitter.lastIndex = SUPPORTS_Y ? q : 0;\n        var z = callRegExpExec(splitter, SUPPORTS_Y ? S : S.slice(q));\n        var e;\n        if (\n          z === null ||\n          (e = min(toLength(splitter.lastIndex + (SUPPORTS_Y ? 0 : q)), S.length)) === p\n        ) {\n          q = advanceStringIndex(S, q, unicodeMatching);\n        } else {\n          A.push(S.slice(p, q));\n          if (A.length === lim) return A;\n          for (var i = 1; i <= z.length - 1; i++) {\n            A.push(z[i]);\n            if (A.length === lim) return A;\n          }\n          q = p = e;\n        }\n      }\n      A.push(S.slice(p));\n      return A;\n    }\n  ];\n}, !SUPPORTS_Y);\n\n\n/***/ }),\n\n/***/ 3210:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_144619__) {\n\n\"use strict\";\n\nvar $ = __nested_webpack_require_144619__(2109);\nvar $trim = __nested_webpack_require_144619__(3111).trim;\nvar forcedStringTrimMethod = __nested_webpack_require_144619__(6091);\n\n// `String.prototype.trim` method\n// https://tc39.es/ecma262/#sec-string.prototype.trim\n$({ target: 'String', proto: true, forced: forcedStringTrimMethod('trim') }, {\n  trim: function trim() {\n    return $trim(this);\n  }\n});\n\n\n/***/ }),\n\n/***/ 2990:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_145111__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_145111__(260);\nvar $copyWithin = __nested_webpack_require_145111__(1048);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.copyWithin` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.copywithin\nexportTypedArrayMethod('copyWithin', function copyWithin(target, start /* , end */) {\n  return $copyWithin.call(aTypedArray(this), target, start, arguments.length > 2 ? arguments[2] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 8927:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_145777__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_145777__(260);\nvar $every = __nested_webpack_require_145777__(2092).every;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.every` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.every\nexportTypedArrayMethod('every', function every(callbackfn /* , thisArg */) {\n  return $every(aTypedArray(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 3105:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_146412__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_146412__(260);\nvar $fill = __nested_webpack_require_146412__(1285);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.fill` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.fill\n// eslint-disable-next-line no-unused-vars -- required for `.length`\nexportTypedArrayMethod('fill', function fill(value /* , start, end */) {\n  return $fill.apply(aTypedArray(this), arguments);\n});\n\n\n/***/ }),\n\n/***/ 5035:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_147058__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_147058__(260);\nvar $filter = __nested_webpack_require_147058__(2092).filter;\nvar fromSpeciesAndList = __nested_webpack_require_147058__(3074);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.filter` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.filter\nexportTypedArrayMethod('filter', function filter(callbackfn /* , thisArg */) {\n  var list = $filter(aTypedArray(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  return fromSpeciesAndList(this, list);\n});\n\n\n/***/ }),\n\n/***/ 7174:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_147797__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_147797__(260);\nvar $findIndex = __nested_webpack_require_147797__(2092).findIndex;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.findIndex` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.findindex\nexportTypedArrayMethod('findIndex', function findIndex(predicate /* , thisArg */) {\n  return $findIndex(aTypedArray(this), predicate, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 4345:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_148458__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_148458__(260);\nvar $find = __nested_webpack_require_148458__(2092).find;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.find` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.find\nexportTypedArrayMethod('find', function find(predicate /* , thisArg */) {\n  return $find(aTypedArray(this), predicate, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 2846:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_149084__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_149084__(260);\nvar $forEach = __nested_webpack_require_149084__(2092).forEach;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.forEach` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.foreach\nexportTypedArrayMethod('forEach', function forEach(callbackfn /* , thisArg */) {\n  $forEach(aTypedArray(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 4731:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_149726__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_149726__(260);\nvar $includes = __nested_webpack_require_149726__(1318).includes;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.includes` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.includes\nexportTypedArrayMethod('includes', function includes(searchElement /* , fromIndex */) {\n  return $includes(aTypedArray(this), searchElement, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 7209:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_150390__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_150390__(260);\nvar $indexOf = __nested_webpack_require_150390__(1318).indexOf;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.indexOf` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.indexof\nexportTypedArrayMethod('indexOf', function indexOf(searchElement /* , fromIndex */) {\n  return $indexOf(aTypedArray(this), searchElement, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 6319:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_151047__) {\n\n\"use strict\";\n\nvar global = __nested_webpack_require_151047__(7854);\nvar ArrayBufferViewCore = __nested_webpack_require_151047__(260);\nvar ArrayIterators = __nested_webpack_require_151047__(6992);\nvar wellKnownSymbol = __nested_webpack_require_151047__(5112);\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar Uint8Array = global.Uint8Array;\nvar arrayValues = ArrayIterators.values;\nvar arrayKeys = ArrayIterators.keys;\nvar arrayEntries = ArrayIterators.entries;\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar nativeTypedArrayIterator = Uint8Array && Uint8Array.prototype[ITERATOR];\n\nvar CORRECT_ITER_NAME = !!nativeTypedArrayIterator\n  && (nativeTypedArrayIterator.name == 'values' || nativeTypedArrayIterator.name == undefined);\n\nvar typedArrayValues = function values() {\n  return arrayValues.call(aTypedArray(this));\n};\n\n// `%TypedArray%.prototype.entries` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.entries\nexportTypedArrayMethod('entries', function entries() {\n  return arrayEntries.call(aTypedArray(this));\n});\n// `%TypedArray%.prototype.keys` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.keys\nexportTypedArrayMethod('keys', function keys() {\n  return arrayKeys.call(aTypedArray(this));\n});\n// `%TypedArray%.prototype.values` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.values\nexportTypedArrayMethod('values', typedArrayValues, !CORRECT_ITER_NAME);\n// `%TypedArray%.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype-@@iterator\nexportTypedArrayMethod(ITERATOR, typedArrayValues, !CORRECT_ITER_NAME);\n\n\n/***/ }),\n\n/***/ 8867:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_152782__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_152782__(260);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar $join = [].join;\n\n// `%TypedArray%.prototype.join` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.join\n// eslint-disable-next-line no-unused-vars -- required for `.length`\nexportTypedArrayMethod('join', function join(separator) {\n  return $join.apply(aTypedArray(this), arguments);\n});\n\n\n/***/ }),\n\n/***/ 7789:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_153395__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_153395__(260);\nvar $lastIndexOf = __nested_webpack_require_153395__(6583);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.lastIndexOf` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.lastindexof\n// eslint-disable-next-line no-unused-vars -- required for `.length`\nexportTypedArrayMethod('lastIndexOf', function lastIndexOf(searchElement /* , fromIndex */) {\n  return $lastIndexOf.apply(aTypedArray(this), arguments);\n});\n\n\n/***/ }),\n\n/***/ 3739:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_154090__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_154090__(260);\nvar $map = __nested_webpack_require_154090__(2092).map;\nvar speciesConstructor = __nested_webpack_require_154090__(6707);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar aTypedArrayConstructor = ArrayBufferViewCore.aTypedArrayConstructor;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.map` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.map\nexportTypedArrayMethod('map', function map(mapfn /* , thisArg */) {\n  return $map(aTypedArray(this), mapfn, arguments.length > 1 ? arguments[1] : undefined, function (O, length) {\n    return new (aTypedArrayConstructor(speciesConstructor(O, O.constructor)))(length);\n  });\n});\n\n\n/***/ }),\n\n/***/ 4483:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_154941__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_154941__(260);\nvar $reduceRight = __nested_webpack_require_154941__(3671).right;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.reduceRicht` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.reduceright\nexportTypedArrayMethod('reduceRight', function reduceRight(callbackfn /* , initialValue */) {\n  return $reduceRight(aTypedArray(this), callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 9368:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_155635__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_155635__(260);\nvar $reduce = __nested_webpack_require_155635__(3671).left;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.reduce\nexportTypedArrayMethod('reduce', function reduce(callbackfn /* , initialValue */) {\n  return $reduce(aTypedArray(this), callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 2056:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_156298__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_156298__(260);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar floor = Math.floor;\n\n// `%TypedArray%.prototype.reverse` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.reverse\nexportTypedArrayMethod('reverse', function reverse() {\n  var that = this;\n  var length = aTypedArray(that).length;\n  var middle = floor(length / 2);\n  var index = 0;\n  var value;\n  while (index < middle) {\n    value = that[index];\n    that[index++] = that[--length];\n    that[length] = value;\n  } return that;\n});\n\n\n/***/ }),\n\n/***/ 3462:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_157051__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_157051__(260);\nvar toLength = __nested_webpack_require_157051__(7466);\nvar toOffset = __nested_webpack_require_157051__(4590);\nvar toObject = __nested_webpack_require_157051__(7908);\nvar fails = __nested_webpack_require_157051__(7293);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\nvar FORCED = fails(function () {\n  /* global Int8Array -- safe */\n  new Int8Array(1).set({});\n});\n\n// `%TypedArray%.prototype.set` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.set\nexportTypedArrayMethod('set', function set(arrayLike /* , offset */) {\n  aTypedArray(this);\n  var offset = toOffset(arguments.length > 1 ? arguments[1] : undefined, 1);\n  var length = this.length;\n  var src = toObject(arrayLike);\n  var len = toLength(src.length);\n  var index = 0;\n  if (len + offset > length) throw RangeError('Wrong length');\n  while (index < len) this[offset + index] = src[index++];\n}, FORCED);\n\n\n/***/ }),\n\n/***/ 678:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_158136__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_158136__(260);\nvar speciesConstructor = __nested_webpack_require_158136__(6707);\nvar fails = __nested_webpack_require_158136__(7293);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar aTypedArrayConstructor = ArrayBufferViewCore.aTypedArrayConstructor;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar $slice = [].slice;\n\nvar FORCED = fails(function () {\n  /* global Int8Array -- safe */\n  new Int8Array(1).slice();\n});\n\n// `%TypedArray%.prototype.slice` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.slice\nexportTypedArrayMethod('slice', function slice(start, end) {\n  var list = $slice.call(aTypedArray(this), start, end);\n  var C = speciesConstructor(this, this.constructor);\n  var index = 0;\n  var length = list.length;\n  var result = new (aTypedArrayConstructor(C))(length);\n  while (length > index) result[index] = list[index++];\n  return result;\n}, FORCED);\n\n\n/***/ }),\n\n/***/ 7462:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_159191__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_159191__(260);\nvar $some = __nested_webpack_require_159191__(2092).some;\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.some` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.some\nexportTypedArrayMethod('some', function some(callbackfn /* , thisArg */) {\n  return $some(aTypedArray(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n});\n\n\n/***/ }),\n\n/***/ 3824:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_159819__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_159819__(260);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar $sort = [].sort;\n\n// `%TypedArray%.prototype.sort` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.sort\nexportTypedArrayMethod('sort', function sort(comparefn) {\n  return $sort.call(aTypedArray(this), comparefn);\n});\n\n\n/***/ }),\n\n/***/ 5021:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_160362__) {\n\n\"use strict\";\n\nvar ArrayBufferViewCore = __nested_webpack_require_160362__(260);\nvar toLength = __nested_webpack_require_160362__(7466);\nvar toAbsoluteIndex = __nested_webpack_require_160362__(1400);\nvar speciesConstructor = __nested_webpack_require_160362__(6707);\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\n// `%TypedArray%.prototype.subarray` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.subarray\nexportTypedArrayMethod('subarray', function subarray(begin, end) {\n  var O = aTypedArray(this);\n  var length = O.length;\n  var beginIndex = toAbsoluteIndex(begin, length);\n  return new (speciesConstructor(O, O.constructor))(\n    O.buffer,\n    O.byteOffset + beginIndex * O.BYTES_PER_ELEMENT,\n    toLength((end === undefined ? length : toAbsoluteIndex(end, length)) - beginIndex)\n  );\n});\n\n\n/***/ }),\n\n/***/ 2974:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_161310__) {\n\n\"use strict\";\n\nvar global = __nested_webpack_require_161310__(7854);\nvar ArrayBufferViewCore = __nested_webpack_require_161310__(260);\nvar fails = __nested_webpack_require_161310__(7293);\n\nvar Int8Array = global.Int8Array;\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar $toLocaleString = [].toLocaleString;\nvar $slice = [].slice;\n\n// iOS Safari 6.x fails here\nvar TO_LOCALE_STRING_BUG = !!Int8Array && fails(function () {\n  $toLocaleString.call(new Int8Array(1));\n});\n\nvar FORCED = fails(function () {\n  return [1, 2].toLocaleString() != new Int8Array([1, 2]).toLocaleString();\n}) || !fails(function () {\n  Int8Array.prototype.toLocaleString.call([1, 2]);\n});\n\n// `%TypedArray%.prototype.toLocaleString` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.tolocalestring\nexportTypedArrayMethod('toLocaleString', function toLocaleString() {\n  return $toLocaleString.apply(TO_LOCALE_STRING_BUG ? $slice.call(aTypedArray(this)) : aTypedArray(this), arguments);\n}, FORCED);\n\n\n/***/ }),\n\n/***/ 5016:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_162445__) {\n\n\"use strict\";\n\nvar exportTypedArrayMethod = __nested_webpack_require_162445__(260).exportTypedArrayMethod;\nvar fails = __nested_webpack_require_162445__(7293);\nvar global = __nested_webpack_require_162445__(7854);\n\nvar Uint8Array = global.Uint8Array;\nvar Uint8ArrayPrototype = Uint8Array && Uint8Array.prototype || {};\nvar arrayToString = [].toString;\nvar arrayJoin = [].join;\n\nif (fails(function () { arrayToString.call({}); })) {\n  arrayToString = function toString() {\n    return arrayJoin.call(this);\n  };\n}\n\nvar IS_NOT_ARRAY_METHOD = Uint8ArrayPrototype.toString != arrayToString;\n\n// `%TypedArray%.prototype.toString` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.tostring\nexportTypedArrayMethod('toString', arrayToString, IS_NOT_ARRAY_METHOD);\n\n\n/***/ }),\n\n/***/ 2472:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_163286__) {\n\nvar createTypedArrayConstructor = __nested_webpack_require_163286__(9843);\n\n// `Uint8Array` constructor\n// https://tc39.es/ecma262/#sec-typedarray-objects\ncreateTypedArrayConstructor('Uint8', function (init) {\n  return function Uint8Array(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n});\n\n\n/***/ }),\n\n/***/ 4747:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_163713__) {\n\nvar global = __nested_webpack_require_163713__(7854);\nvar DOMIterables = __nested_webpack_require_163713__(8324);\nvar forEach = __nested_webpack_require_163713__(8533);\nvar createNonEnumerableProperty = __nested_webpack_require_163713__(8880);\n\nfor (var COLLECTION_NAME in DOMIterables) {\n  var Collection = global[COLLECTION_NAME];\n  var CollectionPrototype = Collection && Collection.prototype;\n  // some Chrome versions have non-configurable methods on DOMTokenList\n  if (CollectionPrototype && CollectionPrototype.forEach !== forEach) try {\n    createNonEnumerableProperty(CollectionPrototype, 'forEach', forEach);\n  } catch (error) {\n    CollectionPrototype.forEach = forEach;\n  }\n}\n\n\n/***/ }),\n\n/***/ 3948:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_164461__) {\n\nvar global = __nested_webpack_require_164461__(7854);\nvar DOMIterables = __nested_webpack_require_164461__(8324);\nvar ArrayIteratorMethods = __nested_webpack_require_164461__(6992);\nvar createNonEnumerableProperty = __nested_webpack_require_164461__(8880);\nvar wellKnownSymbol = __nested_webpack_require_164461__(5112);\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar ArrayValues = ArrayIteratorMethods.values;\n\nfor (var COLLECTION_NAME in DOMIterables) {\n  var Collection = global[COLLECTION_NAME];\n  var CollectionPrototype = Collection && Collection.prototype;\n  if (CollectionPrototype) {\n    // some Chrome versions have non-configurable methods on DOMTokenList\n    if (CollectionPrototype[ITERATOR] !== ArrayValues) try {\n      createNonEnumerableProperty(CollectionPrototype, ITERATOR, ArrayValues);\n    } catch (error) {\n      CollectionPrototype[ITERATOR] = ArrayValues;\n    }\n    if (!CollectionPrototype[TO_STRING_TAG]) {\n      createNonEnumerableProperty(CollectionPrototype, TO_STRING_TAG, COLLECTION_NAME);\n    }\n    if (DOMIterables[COLLECTION_NAME]) for (var METHOD_NAME in ArrayIteratorMethods) {\n      // some Chrome versions have non-configurable methods on DOMTokenList\n      if (CollectionPrototype[METHOD_NAME] !== ArrayIteratorMethods[METHOD_NAME]) try {\n        createNonEnumerableProperty(CollectionPrototype, METHOD_NAME, ArrayIteratorMethods[METHOD_NAME]);\n      } catch (error) {\n        CollectionPrototype[METHOD_NAME] = ArrayIteratorMethods[METHOD_NAME];\n      }\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 1637:\n/***/ (function(module, __unused_webpack_exports, __nested_webpack_require_166049__) {\n\n\"use strict\";\n\n// TODO: in core-js@4, move /modules/ dependencies to public entries for better optimization by tools like `preset-env`\n__nested_webpack_require_166049__(6992);\nvar $ = __nested_webpack_require_166049__(2109);\nvar getBuiltIn = __nested_webpack_require_166049__(5005);\nvar USE_NATIVE_URL = __nested_webpack_require_166049__(590);\nvar redefine = __nested_webpack_require_166049__(1320);\nvar redefineAll = __nested_webpack_require_166049__(2248);\nvar setToStringTag = __nested_webpack_require_166049__(8003);\nvar createIteratorConstructor = __nested_webpack_require_166049__(4994);\nvar InternalStateModule = __nested_webpack_require_166049__(9909);\nvar anInstance = __nested_webpack_require_166049__(5787);\nvar hasOwn = __nested_webpack_require_166049__(6656);\nvar bind = __nested_webpack_require_166049__(9974);\nvar classof = __nested_webpack_require_166049__(648);\nvar anObject = __nested_webpack_require_166049__(9670);\nvar isObject = __nested_webpack_require_166049__(111);\nvar create = __nested_webpack_require_166049__(30);\nvar createPropertyDescriptor = __nested_webpack_require_166049__(9114);\nvar getIterator = __nested_webpack_require_166049__(8554);\nvar getIteratorMethod = __nested_webpack_require_166049__(1246);\nvar wellKnownSymbol = __nested_webpack_require_166049__(5112);\n\nvar $fetch = getBuiltIn('fetch');\nvar Headers = getBuiltIn('Headers');\nvar ITERATOR = wellKnownSymbol('iterator');\nvar URL_SEARCH_PARAMS = 'URLSearchParams';\nvar URL_SEARCH_PARAMS_ITERATOR = URL_SEARCH_PARAMS + 'Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalParamsState = InternalStateModule.getterFor(URL_SEARCH_PARAMS);\nvar getInternalIteratorState = InternalStateModule.getterFor(URL_SEARCH_PARAMS_ITERATOR);\n\nvar plus = /\\+/g;\nvar sequences = Array(4);\n\nvar percentSequence = function (bytes) {\n  return sequences[bytes - 1] || (sequences[bytes - 1] = RegExp('((?:%[\\\\da-f]{2}){' + bytes + '})', 'gi'));\n};\n\nvar percentDecode = function (sequence) {\n  try {\n    return decodeURIComponent(sequence);\n  } catch (error) {\n    return sequence;\n  }\n};\n\nvar deserialize = function (it) {\n  var result = it.replace(plus, ' ');\n  var bytes = 4;\n  try {\n    return decodeURIComponent(result);\n  } catch (error) {\n    while (bytes) {\n      result = result.replace(percentSequence(bytes--), percentDecode);\n    }\n    return result;\n  }\n};\n\nvar find = /[!'()~]|%20/g;\n\nvar replace = {\n  '!': '%21',\n  \"'\": '%27',\n  '(': '%28',\n  ')': '%29',\n  '~': '%7E',\n  '%20': '+'\n};\n\nvar replacer = function (match) {\n  return replace[match];\n};\n\nvar serialize = function (it) {\n  return encodeURIComponent(it).replace(find, replacer);\n};\n\nvar parseSearchParams = function (result, query) {\n  if (query) {\n    var attributes = query.split('&');\n    var index = 0;\n    var attribute, entry;\n    while (index < attributes.length) {\n      attribute = attributes[index++];\n      if (attribute.length) {\n        entry = attribute.split('=');\n        result.push({\n          key: deserialize(entry.shift()),\n          value: deserialize(entry.join('='))\n        });\n      }\n    }\n  }\n};\n\nvar updateSearchParams = function (query) {\n  this.entries.length = 0;\n  parseSearchParams(this.entries, query);\n};\n\nvar validateArgumentsLength = function (passed, required) {\n  if (passed < required) throw TypeError('Not enough arguments');\n};\n\nvar URLSearchParamsIterator = createIteratorConstructor(function Iterator(params, kind) {\n  setInternalState(this, {\n    type: URL_SEARCH_PARAMS_ITERATOR,\n    iterator: getIterator(getInternalParamsState(params).entries),\n    kind: kind\n  });\n}, 'Iterator', function next() {\n  var state = getInternalIteratorState(this);\n  var kind = state.kind;\n  var step = state.iterator.next();\n  var entry = step.value;\n  if (!step.done) {\n    step.value = kind === 'keys' ? entry.key : kind === 'values' ? entry.value : [entry.key, entry.value];\n  } return step;\n});\n\n// `URLSearchParams` constructor\n// https://url.spec.whatwg.org/#interface-urlsearchparams\nvar URLSearchParamsConstructor = function URLSearchParams(/* init */) {\n  anInstance(this, URLSearchParamsConstructor, URL_SEARCH_PARAMS);\n  var init = arguments.length > 0 ? arguments[0] : undefined;\n  var that = this;\n  var entries = [];\n  var iteratorMethod, iterator, next, step, entryIterator, entryNext, first, second, key;\n\n  setInternalState(that, {\n    type: URL_SEARCH_PARAMS,\n    entries: entries,\n    updateURL: function () { /* empty */ },\n    updateSearchParams: updateSearchParams\n  });\n\n  if (init !== undefined) {\n    if (isObject(init)) {\n      iteratorMethod = getIteratorMethod(init);\n      if (typeof iteratorMethod === 'function') {\n        iterator = iteratorMethod.call(init);\n        next = iterator.next;\n        while (!(step = next.call(iterator)).done) {\n          entryIterator = getIterator(anObject(step.value));\n          entryNext = entryIterator.next;\n          if (\n            (first = entryNext.call(entryIterator)).done ||\n            (second = entryNext.call(entryIterator)).done ||\n            !entryNext.call(entryIterator).done\n          ) throw TypeError('Expected sequence with length 2');\n          entries.push({ key: first.value + '', value: second.value + '' });\n        }\n      } else for (key in init) if (hasOwn(init, key)) entries.push({ key: key, value: init[key] + '' });\n    } else {\n      parseSearchParams(entries, typeof init === 'string' ? init.charAt(0) === '?' ? init.slice(1) : init : init + '');\n    }\n  }\n};\n\nvar URLSearchParamsPrototype = URLSearchParamsConstructor.prototype;\n\nredefineAll(URLSearchParamsPrototype, {\n  // `URLSearchParams.prototype.append` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-append\n  append: function append(name, value) {\n    validateArgumentsLength(arguments.length, 2);\n    var state = getInternalParamsState(this);\n    state.entries.push({ key: name + '', value: value + '' });\n    state.updateURL();\n  },\n  // `URLSearchParams.prototype.delete` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-delete\n  'delete': function (name) {\n    validateArgumentsLength(arguments.length, 1);\n    var state = getInternalParamsState(this);\n    var entries = state.entries;\n    var key = name + '';\n    var index = 0;\n    while (index < entries.length) {\n      if (entries[index].key === key) entries.splice(index, 1);\n      else index++;\n    }\n    state.updateURL();\n  },\n  // `URLSearchParams.prototype.get` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-get\n  get: function get(name) {\n    validateArgumentsLength(arguments.length, 1);\n    var entries = getInternalParamsState(this).entries;\n    var key = name + '';\n    var index = 0;\n    for (; index < entries.length; index++) {\n      if (entries[index].key === key) return entries[index].value;\n    }\n    return null;\n  },\n  // `URLSearchParams.prototype.getAll` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-getall\n  getAll: function getAll(name) {\n    validateArgumentsLength(arguments.length, 1);\n    var entries = getInternalParamsState(this).entries;\n    var key = name + '';\n    var result = [];\n    var index = 0;\n    for (; index < entries.length; index++) {\n      if (entries[index].key === key) result.push(entries[index].value);\n    }\n    return result;\n  },\n  // `URLSearchParams.prototype.has` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-has\n  has: function has(name) {\n    validateArgumentsLength(arguments.length, 1);\n    var entries = getInternalParamsState(this).entries;\n    var key = name + '';\n    var index = 0;\n    while (index < entries.length) {\n      if (entries[index++].key === key) return true;\n    }\n    return false;\n  },\n  // `URLSearchParams.prototype.set` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-set\n  set: function set(name, value) {\n    validateArgumentsLength(arguments.length, 1);\n    var state = getInternalParamsState(this);\n    var entries = state.entries;\n    var found = false;\n    var key = name + '';\n    var val = value + '';\n    var index = 0;\n    var entry;\n    for (; index < entries.length; index++) {\n      entry = entries[index];\n      if (entry.key === key) {\n        if (found) entries.splice(index--, 1);\n        else {\n          found = true;\n          entry.value = val;\n        }\n      }\n    }\n    if (!found) entries.push({ key: key, value: val });\n    state.updateURL();\n  },\n  // `URLSearchParams.prototype.sort` method\n  // https://url.spec.whatwg.org/#dom-urlsearchparams-sort\n  sort: function sort() {\n    var state = getInternalParamsState(this);\n    var entries = state.entries;\n    // Array#sort is not stable in some engines\n    var slice = entries.slice();\n    var entry, entriesIndex, sliceIndex;\n    entries.length = 0;\n    for (sliceIndex = 0; sliceIndex < slice.length; sliceIndex++) {\n      entry = slice[sliceIndex];\n      for (entriesIndex = 0; entriesIndex < sliceIndex; entriesIndex++) {\n        if (entries[entriesIndex].key > entry.key) {\n          entries.splice(entriesIndex, 0, entry);\n          break;\n        }\n      }\n      if (entriesIndex === sliceIndex) entries.push(entry);\n    }\n    state.updateURL();\n  },\n  // `URLSearchParams.prototype.forEach` method\n  forEach: function forEach(callback /* , thisArg */) {\n    var entries = getInternalParamsState(this).entries;\n    var boundFunction = bind(callback, arguments.length > 1 ? arguments[1] : undefined, 3);\n    var index = 0;\n    var entry;\n    while (index < entries.length) {\n      entry = entries[index++];\n      boundFunction(entry.value, entry.key, this);\n    }\n  },\n  // `URLSearchParams.prototype.keys` method\n  keys: function keys() {\n    return new URLSearchParamsIterator(this, 'keys');\n  },\n  // `URLSearchParams.prototype.values` method\n  values: function values() {\n    return new URLSearchParamsIterator(this, 'values');\n  },\n  // `URLSearchParams.prototype.entries` method\n  entries: function entries() {\n    return new URLSearchParamsIterator(this, 'entries');\n  }\n}, { enumerable: true });\n\n// `URLSearchParams.prototype[@@iterator]` method\nredefine(URLSearchParamsPrototype, ITERATOR, URLSearchParamsPrototype.entries);\n\n// `URLSearchParams.prototype.toString` method\n// https://url.spec.whatwg.org/#urlsearchparams-stringification-behavior\nredefine(URLSearchParamsPrototype, 'toString', function toString() {\n  var entries = getInternalParamsState(this).entries;\n  var result = [];\n  var index = 0;\n  var entry;\n  while (index < entries.length) {\n    entry = entries[index++];\n    result.push(serialize(entry.key) + '=' + serialize(entry.value));\n  } return result.join('&');\n}, { enumerable: true });\n\nsetToStringTag(URLSearchParamsConstructor, URL_SEARCH_PARAMS);\n\n$({ global: true, forced: !USE_NATIVE_URL }, {\n  URLSearchParams: URLSearchParamsConstructor\n});\n\n// Wrap `fetch` for correct work with polyfilled `URLSearchParams`\n// https://github.com/zloirock/core-js/issues/674\nif (!USE_NATIVE_URL && typeof $fetch == 'function' && typeof Headers == 'function') {\n  $({ global: true, enumerable: true, forced: true }, {\n    fetch: function fetch(input /* , init */) {\n      var args = [input];\n      var init, body, headers;\n      if (arguments.length > 1) {\n        init = arguments[1];\n        if (isObject(init)) {\n          body = init.body;\n          if (classof(body) === URL_SEARCH_PARAMS) {\n            headers = init.headers ? new Headers(init.headers) : new Headers();\n            if (!headers.has('content-type')) {\n              headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n            }\n            init = create(init, {\n              body: createPropertyDescriptor(0, String(body)),\n              headers: createPropertyDescriptor(0, headers)\n            });\n          }\n        }\n        args.push(init);\n      } return $fetch.apply(this, args);\n    }\n  });\n}\n\nmodule.exports = {\n  URLSearchParams: URLSearchParamsConstructor,\n  getState: getInternalParamsState\n};\n\n\n/***/ }),\n\n/***/ 285:\n/***/ (function(__unused_webpack_module, __unused_webpack_exports, __nested_webpack_require_177789__) {\n\n\"use strict\";\n\n// TODO: in core-js@4, move /modules/ dependencies to public entries for better optimization by tools like `preset-env`\n__nested_webpack_require_177789__(8783);\nvar $ = __nested_webpack_require_177789__(2109);\nvar DESCRIPTORS = __nested_webpack_require_177789__(9781);\nvar USE_NATIVE_URL = __nested_webpack_require_177789__(590);\nvar global = __nested_webpack_require_177789__(7854);\nvar defineProperties = __nested_webpack_require_177789__(6048);\nvar redefine = __nested_webpack_require_177789__(1320);\nvar anInstance = __nested_webpack_require_177789__(5787);\nvar has = __nested_webpack_require_177789__(6656);\nvar assign = __nested_webpack_require_177789__(1574);\nvar arrayFrom = __nested_webpack_require_177789__(8457);\nvar codeAt = __nested_webpack_require_177789__(8710).codeAt;\nvar toASCII = __nested_webpack_require_177789__(3197);\nvar setToStringTag = __nested_webpack_require_177789__(8003);\nvar URLSearchParamsModule = __nested_webpack_require_177789__(1637);\nvar InternalStateModule = __nested_webpack_require_177789__(9909);\n\nvar NativeURL = global.URL;\nvar URLSearchParams = URLSearchParamsModule.URLSearchParams;\nvar getInternalSearchParamsState = URLSearchParamsModule.getState;\nvar setInternalState = InternalStateModule.set;\nvar getInternalURLState = InternalStateModule.getterFor('URL');\nvar floor = Math.floor;\nvar pow = Math.pow;\n\nvar INVALID_AUTHORITY = 'Invalid authority';\nvar INVALID_SCHEME = 'Invalid scheme';\nvar INVALID_HOST = 'Invalid host';\nvar INVALID_PORT = 'Invalid port';\n\nvar ALPHA = /[A-Za-z]/;\nvar ALPHANUMERIC = /[\\d+-.A-Za-z]/;\nvar DIGIT = /\\d/;\nvar HEX_START = /^(0x|0X)/;\nvar OCT = /^[0-7]+$/;\nvar DEC = /^\\d+$/;\nvar HEX = /^[\\dA-Fa-f]+$/;\n/* eslint-disable no-control-regex -- safe */\nvar FORBIDDEN_HOST_CODE_POINT = /[\\u0000\\t\\u000A\\u000D #%/:?@[\\\\]]/;\nvar FORBIDDEN_HOST_CODE_POINT_EXCLUDING_PERCENT = /[\\u0000\\t\\u000A\\u000D #/:?@[\\\\]]/;\nvar LEADING_AND_TRAILING_C0_CONTROL_OR_SPACE = /^[\\u0000-\\u001F ]+|[\\u0000-\\u001F ]+$/g;\nvar TAB_AND_NEW_LINE = /[\\t\\u000A\\u000D]/g;\n/* eslint-enable no-control-regex -- safe */\nvar EOF;\n\nvar parseHost = function (url, input) {\n  var result, codePoints, index;\n  if (input.charAt(0) == '[') {\n    if (input.charAt(input.length - 1) != ']') return INVALID_HOST;\n    result = parseIPv6(input.slice(1, -1));\n    if (!result) return INVALID_HOST;\n    url.host = result;\n  // opaque host\n  } else if (!isSpecial(url)) {\n    if (FORBIDDEN_HOST_CODE_POINT_EXCLUDING_PERCENT.test(input)) return INVALID_HOST;\n    result = '';\n    codePoints = arrayFrom(input);\n    for (index = 0; index < codePoints.length; index++) {\n      result += percentEncode(codePoints[index], C0ControlPercentEncodeSet);\n    }\n    url.host = result;\n  } else {\n    input = toASCII(input);\n    if (FORBIDDEN_HOST_CODE_POINT.test(input)) return INVALID_HOST;\n    result = parseIPv4(input);\n    if (result === null) return INVALID_HOST;\n    url.host = result;\n  }\n};\n\nvar parseIPv4 = function (input) {\n  var parts = input.split('.');\n  var partsLength, numbers, index, part, radix, number, ipv4;\n  if (parts.length && parts[parts.length - 1] == '') {\n    parts.pop();\n  }\n  partsLength = parts.length;\n  if (partsLength > 4) return input;\n  numbers = [];\n  for (index = 0; index < partsLength; index++) {\n    part = parts[index];\n    if (part == '') return input;\n    radix = 10;\n    if (part.length > 1 && part.charAt(0) == '0') {\n      radix = HEX_START.test(part) ? 16 : 8;\n      part = part.slice(radix == 8 ? 1 : 2);\n    }\n    if (part === '') {\n      number = 0;\n    } else {\n      if (!(radix == 10 ? DEC : radix == 8 ? OCT : HEX).test(part)) return input;\n      number = parseInt(part, radix);\n    }\n    numbers.push(number);\n  }\n  for (index = 0; index < partsLength; index++) {\n    number = numbers[index];\n    if (index == partsLength - 1) {\n      if (number >= pow(256, 5 - partsLength)) return null;\n    } else if (number > 255) return null;\n  }\n  ipv4 = numbers.pop();\n  for (index = 0; index < numbers.length; index++) {\n    ipv4 += numbers[index] * pow(256, 3 - index);\n  }\n  return ipv4;\n};\n\n// eslint-disable-next-line max-statements -- TODO\nvar parseIPv6 = function (input) {\n  var address = [0, 0, 0, 0, 0, 0, 0, 0];\n  var pieceIndex = 0;\n  var compress = null;\n  var pointer = 0;\n  var value, length, numbersSeen, ipv4Piece, number, swaps, swap;\n\n  var char = function () {\n    return input.charAt(pointer);\n  };\n\n  if (char() == ':') {\n    if (input.charAt(1) != ':') return;\n    pointer += 2;\n    pieceIndex++;\n    compress = pieceIndex;\n  }\n  while (char()) {\n    if (pieceIndex == 8) return;\n    if (char() == ':') {\n      if (compress !== null) return;\n      pointer++;\n      pieceIndex++;\n      compress = pieceIndex;\n      continue;\n    }\n    value = length = 0;\n    while (length < 4 && HEX.test(char())) {\n      value = value * 16 + parseInt(char(), 16);\n      pointer++;\n      length++;\n    }\n    if (char() == '.') {\n      if (length == 0) return;\n      pointer -= length;\n      if (pieceIndex > 6) return;\n      numbersSeen = 0;\n      while (char()) {\n        ipv4Piece = null;\n        if (numbersSeen > 0) {\n          if (char() == '.' && numbersSeen < 4) pointer++;\n          else return;\n        }\n        if (!DIGIT.test(char())) return;\n        while (DIGIT.test(char())) {\n          number = parseInt(char(), 10);\n          if (ipv4Piece === null) ipv4Piece = number;\n          else if (ipv4Piece == 0) return;\n          else ipv4Piece = ipv4Piece * 10 + number;\n          if (ipv4Piece > 255) return;\n          pointer++;\n        }\n        address[pieceIndex] = address[pieceIndex] * 256 + ipv4Piece;\n        numbersSeen++;\n        if (numbersSeen == 2 || numbersSeen == 4) pieceIndex++;\n      }\n      if (numbersSeen != 4) return;\n      break;\n    } else if (char() == ':') {\n      pointer++;\n      if (!char()) return;\n    } else if (char()) return;\n    address[pieceIndex++] = value;\n  }\n  if (compress !== null) {\n    swaps = pieceIndex - compress;\n    pieceIndex = 7;\n    while (pieceIndex != 0 && swaps > 0) {\n      swap = address[pieceIndex];\n      address[pieceIndex--] = address[compress + swaps - 1];\n      address[compress + --swaps] = swap;\n    }\n  } else if (pieceIndex != 8) return;\n  return address;\n};\n\nvar findLongestZeroSequence = function (ipv6) {\n  var maxIndex = null;\n  var maxLength = 1;\n  var currStart = null;\n  var currLength = 0;\n  var index = 0;\n  for (; index < 8; index++) {\n    if (ipv6[index] !== 0) {\n      if (currLength > maxLength) {\n        maxIndex = currStart;\n        maxLength = currLength;\n      }\n      currStart = null;\n      currLength = 0;\n    } else {\n      if (currStart === null) currStart = index;\n      ++currLength;\n    }\n  }\n  if (currLength > maxLength) {\n    maxIndex = currStart;\n    maxLength = currLength;\n  }\n  return maxIndex;\n};\n\nvar serializeHost = function (host) {\n  var result, index, compress, ignore0;\n  // ipv4\n  if (typeof host == 'number') {\n    result = [];\n    for (index = 0; index < 4; index++) {\n      result.unshift(host % 256);\n      host = floor(host / 256);\n    } return result.join('.');\n  // ipv6\n  } else if (typeof host == 'object') {\n    result = '';\n    compress = findLongestZeroSequence(host);\n    for (index = 0; index < 8; index++) {\n      if (ignore0 && host[index] === 0) continue;\n      if (ignore0) ignore0 = false;\n      if (compress === index) {\n        result += index ? ':' : '::';\n        ignore0 = true;\n      } else {\n        result += host[index].toString(16);\n        if (index < 7) result += ':';\n      }\n    }\n    return '[' + result + ']';\n  } return host;\n};\n\nvar C0ControlPercentEncodeSet = {};\nvar fragmentPercentEncodeSet = assign({}, C0ControlPercentEncodeSet, {\n  ' ': 1, '\"': 1, '<': 1, '>': 1, '`': 1\n});\nvar pathPercentEncodeSet = assign({}, fragmentPercentEncodeSet, {\n  '#': 1, '?': 1, '{': 1, '}': 1\n});\nvar userinfoPercentEncodeSet = assign({}, pathPercentEncodeSet, {\n  '/': 1, ':': 1, ';': 1, '=': 1, '@': 1, '[': 1, '\\\\': 1, ']': 1, '^': 1, '|': 1\n});\n\nvar percentEncode = function (char, set) {\n  var code = codeAt(char, 0);\n  return code > 0x20 && code < 0x7F && !has(set, char) ? char : encodeURIComponent(char);\n};\n\nvar specialSchemes = {\n  ftp: 21,\n  file: null,\n  http: 80,\n  https: 443,\n  ws: 80,\n  wss: 443\n};\n\nvar isSpecial = function (url) {\n  return has(specialSchemes, url.scheme);\n};\n\nvar includesCredentials = function (url) {\n  return url.username != '' || url.password != '';\n};\n\nvar cannotHaveUsernamePasswordPort = function (url) {\n  return !url.host || url.cannotBeABaseURL || url.scheme == 'file';\n};\n\nvar isWindowsDriveLetter = function (string, normalized) {\n  var second;\n  return string.length == 2 && ALPHA.test(string.charAt(0))\n    && ((second = string.charAt(1)) == ':' || (!normalized && second == '|'));\n};\n\nvar startsWithWindowsDriveLetter = function (string) {\n  var third;\n  return string.length > 1 && isWindowsDriveLetter(string.slice(0, 2)) && (\n    string.length == 2 ||\n    ((third = string.charAt(2)) === '/' || third === '\\\\' || third === '?' || third === '#')\n  );\n};\n\nvar shortenURLsPath = function (url) {\n  var path = url.path;\n  var pathSize = path.length;\n  if (pathSize && (url.scheme != 'file' || pathSize != 1 || !isWindowsDriveLetter(path[0], true))) {\n    path.pop();\n  }\n};\n\nvar isSingleDot = function (segment) {\n  return segment === '.' || segment.toLowerCase() === '%2e';\n};\n\nvar isDoubleDot = function (segment) {\n  segment = segment.toLowerCase();\n  return segment === '..' || segment === '%2e.' || segment === '.%2e' || segment === '%2e%2e';\n};\n\n// States:\nvar SCHEME_START = {};\nvar SCHEME = {};\nvar NO_SCHEME = {};\nvar SPECIAL_RELATIVE_OR_AUTHORITY = {};\nvar PATH_OR_AUTHORITY = {};\nvar RELATIVE = {};\nvar RELATIVE_SLASH = {};\nvar SPECIAL_AUTHORITY_SLASHES = {};\nvar SPECIAL_AUTHORITY_IGNORE_SLASHES = {};\nvar AUTHORITY = {};\nvar HOST = {};\nvar HOSTNAME = {};\nvar PORT = {};\nvar FILE = {};\nvar FILE_SLASH = {};\nvar FILE_HOST = {};\nvar PATH_START = {};\nvar PATH = {};\nvar CANNOT_BE_A_BASE_URL_PATH = {};\nvar QUERY = {};\nvar FRAGMENT = {};\n\n// eslint-disable-next-line max-statements -- TODO\nvar parseURL = function (url, input, stateOverride, base) {\n  var state = stateOverride || SCHEME_START;\n  var pointer = 0;\n  var buffer = '';\n  var seenAt = false;\n  var seenBracket = false;\n  var seenPasswordToken = false;\n  var codePoints, char, bufferCodePoints, failure;\n\n  if (!stateOverride) {\n    url.scheme = '';\n    url.username = '';\n    url.password = '';\n    url.host = null;\n    url.port = null;\n    url.path = [];\n    url.query = null;\n    url.fragment = null;\n    url.cannotBeABaseURL = false;\n    input = input.replace(LEADING_AND_TRAILING_C0_CONTROL_OR_SPACE, '');\n  }\n\n  input = input.replace(TAB_AND_NEW_LINE, '');\n\n  codePoints = arrayFrom(input);\n\n  while (pointer <= codePoints.length) {\n    char = codePoints[pointer];\n    switch (state) {\n      case SCHEME_START:\n        if (char && ALPHA.test(char)) {\n          buffer += char.toLowerCase();\n          state = SCHEME;\n        } else if (!stateOverride) {\n          state = NO_SCHEME;\n          continue;\n        } else return INVALID_SCHEME;\n        break;\n\n      case SCHEME:\n        if (char && (ALPHANUMERIC.test(char) || char == '+' || char == '-' || char == '.')) {\n          buffer += char.toLowerCase();\n        } else if (char == ':') {\n          if (stateOverride && (\n            (isSpecial(url) != has(specialSchemes, buffer)) ||\n            (buffer == 'file' && (includesCredentials(url) || url.port !== null)) ||\n            (url.scheme == 'file' && !url.host)\n          )) return;\n          url.scheme = buffer;\n          if (stateOverride) {\n            if (isSpecial(url) && specialSchemes[url.scheme] == url.port) url.port = null;\n            return;\n          }\n          buffer = '';\n          if (url.scheme == 'file') {\n            state = FILE;\n          } else if (isSpecial(url) && base && base.scheme == url.scheme) {\n            state = SPECIAL_RELATIVE_OR_AUTHORITY;\n          } else if (isSpecial(url)) {\n            state = SPECIAL_AUTHORITY_SLASHES;\n          } else if (codePoints[pointer + 1] == '/') {\n            state = PATH_OR_AUTHORITY;\n            pointer++;\n          } else {\n            url.cannotBeABaseURL = true;\n            url.path.push('');\n            state = CANNOT_BE_A_BASE_URL_PATH;\n          }\n        } else if (!stateOverride) {\n          buffer = '';\n          state = NO_SCHEME;\n          pointer = 0;\n          continue;\n        } else return INVALID_SCHEME;\n        break;\n\n      case NO_SCHEME:\n        if (!base || (base.cannotBeABaseURL && char != '#')) return INVALID_SCHEME;\n        if (base.cannotBeABaseURL && char == '#') {\n          url.scheme = base.scheme;\n          url.path = base.path.slice();\n          url.query = base.query;\n          url.fragment = '';\n          url.cannotBeABaseURL = true;\n          state = FRAGMENT;\n          break;\n        }\n        state = base.scheme == 'file' ? FILE : RELATIVE;\n        continue;\n\n      case SPECIAL_RELATIVE_OR_AUTHORITY:\n        if (char == '/' && codePoints[pointer + 1] == '/') {\n          state = SPECIAL_AUTHORITY_IGNORE_SLASHES;\n          pointer++;\n        } else {\n          state = RELATIVE;\n          continue;\n        } break;\n\n      case PATH_OR_AUTHORITY:\n        if (char == '/') {\n          state = AUTHORITY;\n          break;\n        } else {\n          state = PATH;\n          continue;\n        }\n\n      case RELATIVE:\n        url.scheme = base.scheme;\n        if (char == EOF) {\n          url.username = base.username;\n          url.password = base.password;\n          url.host = base.host;\n          url.port = base.port;\n          url.path = base.path.slice();\n          url.query = base.query;\n        } else if (char == '/' || (char == '\\\\' && isSpecial(url))) {\n          state = RELATIVE_SLASH;\n        } else if (char == '?') {\n          url.username = base.username;\n          url.password = base.password;\n          url.host = base.host;\n          url.port = base.port;\n          url.path = base.path.slice();\n          url.query = '';\n          state = QUERY;\n        } else if (char == '#') {\n          url.username = base.username;\n          url.password = base.password;\n          url.host = base.host;\n          url.port = base.port;\n          url.path = base.path.slice();\n          url.query = base.query;\n          url.fragment = '';\n          state = FRAGMENT;\n        } else {\n          url.username = base.username;\n          url.password = base.password;\n          url.host = base.host;\n          url.port = base.port;\n          url.path = base.path.slice();\n          url.path.pop();\n          state = PATH;\n          continue;\n        } break;\n\n      case RELATIVE_SLASH:\n        if (isSpecial(url) && (char == '/' || char == '\\\\')) {\n          state = SPECIAL_AUTHORITY_IGNORE_SLASHES;\n        } else if (char == '/') {\n          state = AUTHORITY;\n        } else {\n          url.username = base.username;\n          url.password = base.password;\n          url.host = base.host;\n          url.port = base.port;\n          state = PATH;\n          continue;\n        } break;\n\n      case SPECIAL_AUTHORITY_SLASHES:\n        state = SPECIAL_AUTHORITY_IGNORE_SLASHES;\n        if (char != '/' || buffer.charAt(pointer + 1) != '/') continue;\n        pointer++;\n        break;\n\n      case SPECIAL_AUTHORITY_IGNORE_SLASHES:\n        if (char != '/' && char != '\\\\') {\n          state = AUTHORITY;\n          continue;\n        } break;\n\n      case AUTHORITY:\n        if (char == '@') {\n          if (seenAt) buffer = '%40' + buffer;\n          seenAt = true;\n          bufferCodePoints = arrayFrom(buffer);\n          for (var i = 0; i < bufferCodePoints.length; i++) {\n            var codePoint = bufferCodePoints[i];\n            if (codePoint == ':' && !seenPasswordToken) {\n              seenPasswordToken = true;\n              continue;\n            }\n            var encodedCodePoints = percentEncode(codePoint, userinfoPercentEncodeSet);\n            if (seenPasswordToken) url.password += encodedCodePoints;\n            else url.username += encodedCodePoints;\n          }\n          buffer = '';\n        } else if (\n          char == EOF || char == '/' || char == '?' || char == '#' ||\n          (char == '\\\\' && isSpecial(url))\n        ) {\n          if (seenAt && buffer == '') return INVALID_AUTHORITY;\n          pointer -= arrayFrom(buffer).length + 1;\n          buffer = '';\n          state = HOST;\n        } else buffer += char;\n        break;\n\n      case HOST:\n      case HOSTNAME:\n        if (stateOverride && url.scheme == 'file') {\n          state = FILE_HOST;\n          continue;\n        } else if (char == ':' && !seenBracket) {\n          if (buffer == '') return INVALID_HOST;\n          failure = parseHost(url, buffer);\n          if (failure) return failure;\n          buffer = '';\n          state = PORT;\n          if (stateOverride == HOSTNAME) return;\n        } else if (\n          char == EOF || char == '/' || char == '?' || char == '#' ||\n          (char == '\\\\' && isSpecial(url))\n        ) {\n          if (isSpecial(url) && buffer == '') return INVALID_HOST;\n          if (stateOverride && buffer == '' && (includesCredentials(url) || url.port !== null)) return;\n          failure = parseHost(url, buffer);\n          if (failure) return failure;\n          buffer = '';\n          state = PATH_START;\n          if (stateOverride) return;\n          continue;\n        } else {\n          if (char == '[') seenBracket = true;\n          else if (char == ']') seenBracket = false;\n          buffer += char;\n        } break;\n\n      case PORT:\n        if (DIGIT.test(char)) {\n          buffer += char;\n        } else if (\n          char == EOF || char == '/' || char == '?' || char == '#' ||\n          (char == '\\\\' && isSpecial(url)) ||\n          stateOverride\n        ) {\n          if (buffer != '') {\n            var port = parseInt(buffer, 10);\n            if (port > 0xFFFF) return INVALID_PORT;\n            url.port = (isSpecial(url) && port === specialSchemes[url.scheme]) ? null : port;\n            buffer = '';\n          }\n          if (stateOverride) return;\n          state = PATH_START;\n          continue;\n        } else return INVALID_PORT;\n        break;\n\n      case FILE:\n        url.scheme = 'file';\n        if (char == '/' || char == '\\\\') state = FILE_SLASH;\n        else if (base && base.scheme == 'file') {\n          if (char == EOF) {\n            url.host = base.host;\n            url.path = base.path.slice();\n            url.query = base.query;\n          } else if (char == '?') {\n            url.host = base.host;\n            url.path = base.path.slice();\n            url.query = '';\n            state = QUERY;\n          } else if (char == '#') {\n            url.host = base.host;\n            url.path = base.path.slice();\n            url.query = base.query;\n            url.fragment = '';\n            state = FRAGMENT;\n          } else {\n            if (!startsWithWindowsDriveLetter(codePoints.slice(pointer).join(''))) {\n              url.host = base.host;\n              url.path = base.path.slice();\n              shortenURLsPath(url);\n            }\n            state = PATH;\n            continue;\n          }\n        } else {\n          state = PATH;\n          continue;\n        } break;\n\n      case FILE_SLASH:\n        if (char == '/' || char == '\\\\') {\n          state = FILE_HOST;\n          break;\n        }\n        if (base && base.scheme == 'file' && !startsWithWindowsDriveLetter(codePoints.slice(pointer).join(''))) {\n          if (isWindowsDriveLetter(base.path[0], true)) url.path.push(base.path[0]);\n          else url.host = base.host;\n        }\n        state = PATH;\n        continue;\n\n      case FILE_HOST:\n        if (char == EOF || char == '/' || char == '\\\\' || char == '?' || char == '#') {\n          if (!stateOverride && isWindowsDriveLetter(buffer)) {\n            state = PATH;\n          } else if (buffer == '') {\n            url.host = '';\n            if (stateOverride) return;\n            state = PATH_START;\n          } else {\n            failure = parseHost(url, buffer);\n            if (failure) return failure;\n            if (url.host == 'localhost') url.host = '';\n            if (stateOverride) return;\n            buffer = '';\n            state = PATH_START;\n          } continue;\n        } else buffer += char;\n        break;\n\n      case PATH_START:\n        if (isSpecial(url)) {\n          state = PATH;\n          if (char != '/' && char != '\\\\') continue;\n        } else if (!stateOverride && char == '?') {\n          url.query = '';\n          state = QUERY;\n        } else if (!stateOverride && char == '#') {\n          url.fragment = '';\n          state = FRAGMENT;\n        } else if (char != EOF) {\n          state = PATH;\n          if (char != '/') continue;\n        } break;\n\n      case PATH:\n        if (\n          char == EOF || char == '/' ||\n          (char == '\\\\' && isSpecial(url)) ||\n          (!stateOverride && (char == '?' || char == '#'))\n        ) {\n          if (isDoubleDot(buffer)) {\n            shortenURLsPath(url);\n            if (char != '/' && !(char == '\\\\' && isSpecial(url))) {\n              url.path.push('');\n            }\n          } else if (isSingleDot(buffer)) {\n            if (char != '/' && !(char == '\\\\' && isSpecial(url))) {\n              url.path.push('');\n            }\n          } else {\n            if (url.scheme == 'file' && !url.path.length && isWindowsDriveLetter(buffer)) {\n              if (url.host) url.host = '';\n              buffer = buffer.charAt(0) + ':'; // normalize windows drive letter\n            }\n            url.path.push(buffer);\n          }\n          buffer = '';\n          if (url.scheme == 'file' && (char == EOF || char == '?' || char == '#')) {\n            while (url.path.length > 1 && url.path[0] === '') {\n              url.path.shift();\n            }\n          }\n          if (char == '?') {\n            url.query = '';\n            state = QUERY;\n          } else if (char == '#') {\n            url.fragment = '';\n            state = FRAGMENT;\n          }\n        } else {\n          buffer += percentEncode(char, pathPercentEncodeSet);\n        } break;\n\n      case CANNOT_BE_A_BASE_URL_PATH:\n        if (char == '?') {\n          url.query = '';\n          state = QUERY;\n        } else if (char == '#') {\n          url.fragment = '';\n          state = FRAGMENT;\n        } else if (char != EOF) {\n          url.path[0] += percentEncode(char, C0ControlPercentEncodeSet);\n        } break;\n\n      case QUERY:\n        if (!stateOverride && char == '#') {\n          url.fragment = '';\n          state = FRAGMENT;\n        } else if (char != EOF) {\n          if (char == \"'\" && isSpecial(url)) url.query += '%27';\n          else if (char == '#') url.query += '%23';\n          else url.query += percentEncode(char, C0ControlPercentEncodeSet);\n        } break;\n\n      case FRAGMENT:\n        if (char != EOF) url.fragment += percentEncode(char, fragmentPercentEncodeSet);\n        break;\n    }\n\n    pointer++;\n  }\n};\n\n// `URL` constructor\n// https://url.spec.whatwg.org/#url-class\nvar URLConstructor = function URL(url /* , base */) {\n  var that = anInstance(this, URLConstructor, 'URL');\n  var base = arguments.length > 1 ? arguments[1] : undefined;\n  var urlString = String(url);\n  var state = setInternalState(that, { type: 'URL' });\n  var baseState, failure;\n  if (base !== undefined) {\n    if (base instanceof URLConstructor) baseState = getInternalURLState(base);\n    else {\n      failure = parseURL(baseState = {}, String(base));\n      if (failure) throw TypeError(failure);\n    }\n  }\n  failure = parseURL(state, urlString, null, baseState);\n  if (failure) throw TypeError(failure);\n  var searchParams = state.searchParams = new URLSearchParams();\n  var searchParamsState = getInternalSearchParamsState(searchParams);\n  searchParamsState.updateSearchParams(state.query);\n  searchParamsState.updateURL = function () {\n    state.query = String(searchParams) || null;\n  };\n  if (!DESCRIPTORS) {\n    that.href = serializeURL.call(that);\n    that.origin = getOrigin.call(that);\n    that.protocol = getProtocol.call(that);\n    that.username = getUsername.call(that);\n    that.password = getPassword.call(that);\n    that.host = getHost.call(that);\n    that.hostname = getHostname.call(that);\n    that.port = getPort.call(that);\n    that.pathname = getPathname.call(that);\n    that.search = getSearch.call(that);\n    that.searchParams = getSearchParams.call(that);\n    that.hash = getHash.call(that);\n  }\n};\n\nvar URLPrototype = URLConstructor.prototype;\n\nvar serializeURL = function () {\n  var url = getInternalURLState(this);\n  var scheme = url.scheme;\n  var username = url.username;\n  var password = url.password;\n  var host = url.host;\n  var port = url.port;\n  var path = url.path;\n  var query = url.query;\n  var fragment = url.fragment;\n  var output = scheme + ':';\n  if (host !== null) {\n    output += '//';\n    if (includesCredentials(url)) {\n      output += username + (password ? ':' + password : '') + '@';\n    }\n    output += serializeHost(host);\n    if (port !== null) output += ':' + port;\n  } else if (scheme == 'file') output += '//';\n  output += url.cannotBeABaseURL ? path[0] : path.length ? '/' + path.join('/') : '';\n  if (query !== null) output += '?' + query;\n  if (fragment !== null) output += '#' + fragment;\n  return output;\n};\n\nvar getOrigin = function () {\n  var url = getInternalURLState(this);\n  var scheme = url.scheme;\n  var port = url.port;\n  if (scheme == 'blob') try {\n    return new URL(scheme.path[0]).origin;\n  } catch (error) {\n    return 'null';\n  }\n  if (scheme == 'file' || !isSpecial(url)) return 'null';\n  return scheme + '://' + serializeHost(url.host) + (port !== null ? ':' + port : '');\n};\n\nvar getProtocol = function () {\n  return getInternalURLState(this).scheme + ':';\n};\n\nvar getUsername = function () {\n  return getInternalURLState(this).username;\n};\n\nvar getPassword = function () {\n  return getInternalURLState(this).password;\n};\n\nvar getHost = function () {\n  var url = getInternalURLState(this);\n  var host = url.host;\n  var port = url.port;\n  return host === null ? ''\n    : port === null ? serializeHost(host)\n    : serializeHost(host) + ':' + port;\n};\n\nvar getHostname = function () {\n  var host = getInternalURLState(this).host;\n  return host === null ? '' : serializeHost(host);\n};\n\nvar getPort = function () {\n  var port = getInternalURLState(this).port;\n  return port === null ? '' : String(port);\n};\n\nvar getPathname = function () {\n  var url = getInternalURLState(this);\n  var path = url.path;\n  return url.cannotBeABaseURL ? path[0] : path.length ? '/' + path.join('/') : '';\n};\n\nvar getSearch = function () {\n  var query = getInternalURLState(this).query;\n  return query ? '?' + query : '';\n};\n\nvar getSearchParams = function () {\n  return getInternalURLState(this).searchParams;\n};\n\nvar getHash = function () {\n  var fragment = getInternalURLState(this).fragment;\n  return fragment ? '#' + fragment : '';\n};\n\nvar accessorDescriptor = function (getter, setter) {\n  return { get: getter, set: setter, configurable: true, enumerable: true };\n};\n\nif (DESCRIPTORS) {\n  defineProperties(URLPrototype, {\n    // `URL.prototype.href` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-href\n    href: accessorDescriptor(serializeURL, function (href) {\n      var url = getInternalURLState(this);\n      var urlString = String(href);\n      var failure = parseURL(url, urlString);\n      if (failure) throw TypeError(failure);\n      getInternalSearchParamsState(url.searchParams).updateSearchParams(url.query);\n    }),\n    // `URL.prototype.origin` getter\n    // https://url.spec.whatwg.org/#dom-url-origin\n    origin: accessorDescriptor(getOrigin),\n    // `URL.prototype.protocol` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-protocol\n    protocol: accessorDescriptor(getProtocol, function (protocol) {\n      var url = getInternalURLState(this);\n      parseURL(url, String(protocol) + ':', SCHEME_START);\n    }),\n    // `URL.prototype.username` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-username\n    username: accessorDescriptor(getUsername, function (username) {\n      var url = getInternalURLState(this);\n      var codePoints = arrayFrom(String(username));\n      if (cannotHaveUsernamePasswordPort(url)) return;\n      url.username = '';\n      for (var i = 0; i < codePoints.length; i++) {\n        url.username += percentEncode(codePoints[i], userinfoPercentEncodeSet);\n      }\n    }),\n    // `URL.prototype.password` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-password\n    password: accessorDescriptor(getPassword, function (password) {\n      var url = getInternalURLState(this);\n      var codePoints = arrayFrom(String(password));\n      if (cannotHaveUsernamePasswordPort(url)) return;\n      url.password = '';\n      for (var i = 0; i < codePoints.length; i++) {\n        url.password += percentEncode(codePoints[i], userinfoPercentEncodeSet);\n      }\n    }),\n    // `URL.prototype.host` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-host\n    host: accessorDescriptor(getHost, function (host) {\n      var url = getInternalURLState(this);\n      if (url.cannotBeABaseURL) return;\n      parseURL(url, String(host), HOST);\n    }),\n    // `URL.prototype.hostname` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-hostname\n    hostname: accessorDescriptor(getHostname, function (hostname) {\n      var url = getInternalURLState(this);\n      if (url.cannotBeABaseURL) return;\n      parseURL(url, String(hostname), HOSTNAME);\n    }),\n    // `URL.prototype.port` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-port\n    port: accessorDescriptor(getPort, function (port) {\n      var url = getInternalURLState(this);\n      if (cannotHaveUsernamePasswordPort(url)) return;\n      port = String(port);\n      if (port == '') url.port = null;\n      else parseURL(url, port, PORT);\n    }),\n    // `URL.prototype.pathname` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-pathname\n    pathname: accessorDescriptor(getPathname, function (pathname) {\n      var url = getInternalURLState(this);\n      if (url.cannotBeABaseURL) return;\n      url.path = [];\n      parseURL(url, pathname + '', PATH_START);\n    }),\n    // `URL.prototype.search` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-search\n    search: accessorDescriptor(getSearch, function (search) {\n      var url = getInternalURLState(this);\n      search = String(search);\n      if (search == '') {\n        url.query = null;\n      } else {\n        if ('?' == search.charAt(0)) search = search.slice(1);\n        url.query = '';\n        parseURL(url, search, QUERY);\n      }\n      getInternalSearchParamsState(url.searchParams).updateSearchParams(url.query);\n    }),\n    // `URL.prototype.searchParams` getter\n    // https://url.spec.whatwg.org/#dom-url-searchparams\n    searchParams: accessorDescriptor(getSearchParams),\n    // `URL.prototype.hash` accessors pair\n    // https://url.spec.whatwg.org/#dom-url-hash\n    hash: accessorDescriptor(getHash, function (hash) {\n      var url = getInternalURLState(this);\n      hash = String(hash);\n      if (hash == '') {\n        url.fragment = null;\n        return;\n      }\n      if ('#' == hash.charAt(0)) hash = hash.slice(1);\n      url.fragment = '';\n      parseURL(url, hash, FRAGMENT);\n    })\n  });\n}\n\n// `URL.prototype.toJSON` method\n// https://url.spec.whatwg.org/#dom-url-tojson\nredefine(URLPrototype, 'toJSON', function toJSON() {\n  return serializeURL.call(this);\n}, { enumerable: true });\n\n// `URL.prototype.toString` method\n// https://url.spec.whatwg.org/#URL-stringification-behavior\nredefine(URLPrototype, 'toString', function toString() {\n  return serializeURL.call(this);\n}, { enumerable: true });\n\nif (NativeURL) {\n  var nativeCreateObjectURL = NativeURL.createObjectURL;\n  var nativeRevokeObjectURL = NativeURL.revokeObjectURL;\n  // `URL.createObjectURL` method\n  // https://developer.mozilla.org/en-US/docs/Web/API/URL/createObjectURL\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  if (nativeCreateObjectURL) redefine(URLConstructor, 'createObjectURL', function createObjectURL(blob) {\n    return nativeCreateObjectURL.apply(NativeURL, arguments);\n  });\n  // `URL.revokeObjectURL` method\n  // https://developer.mozilla.org/en-US/docs/Web/API/URL/revokeObjectURL\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  if (nativeRevokeObjectURL) redefine(URLConstructor, 'revokeObjectURL', function revokeObjectURL(url) {\n    return nativeRevokeObjectURL.apply(NativeURL, arguments);\n  });\n}\n\nsetToStringTag(URLConstructor, 'URL');\n\n$({ global: true, forced: !USE_NATIVE_URL, sham: !DESCRIPTORS }, {\n  URL: URLConstructor\n});\n\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __nested_webpack_require_210484__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(__webpack_module_cache__[moduleId]) {\n/******/ \t\t\treturn __webpack_module_cache__[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __nested_webpack_require_210484__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__nested_webpack_require_210484__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__nested_webpack_require_210484__.o(definition, key) && !__nested_webpack_require_210484__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/global */\n/******/ \t!function() {\n/******/ \t\t__nested_webpack_require_210484__.g = (function() {\n/******/ \t\t\tif (typeof globalThis === 'object') return globalThis;\n/******/ \t\t\ttry {\n/******/ \t\t\t\treturn this || new Function('return this')();\n/******/ \t\t\t} catch (e) {\n/******/ \t\t\t\tif (typeof window === 'object') return window;\n/******/ \t\t\t}\n/******/ \t\t})();\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__nested_webpack_require_210484__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__nested_webpack_require_210484__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __nested_webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be in strict mode.\n!function() {\n\"use strict\";\n// ESM COMPAT FLAG\n__nested_webpack_require_210484__.r(__nested_webpack_exports__);\n\n// EXPORTS\n__nested_webpack_require_210484__.d(__nested_webpack_exports__, {\n  \"Dropzone\": function() { return /* reexport */ Dropzone; },\n  \"default\": function() { return /* binding */ dropzone_dist; }\n});\n\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js\nvar es_array_concat = __nested_webpack_require_210484__(2222);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js\nvar es_array_filter = __nested_webpack_require_210484__(7327);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.index-of.js\nvar es_array_index_of = __nested_webpack_require_210484__(2772);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.iterator.js\nvar es_array_iterator = __nested_webpack_require_210484__(6992);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js\nvar es_array_map = __nested_webpack_require_210484__(1249);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js\nvar es_array_slice = __nested_webpack_require_210484__(7042);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js\nvar es_array_splice = __nested_webpack_require_210484__(561);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array-buffer.constructor.js\nvar es_array_buffer_constructor = __nested_webpack_require_210484__(8264);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js\nvar es_function_name = __nested_webpack_require_210484__(8309);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-prototype-of.js\nvar es_object_get_prototype_of = __nested_webpack_require_210484__(489);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js\nvar es_object_to_string = __nested_webpack_require_210484__(1539);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js\nvar es_regexp_exec = __nested_webpack_require_210484__(4916);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js\nvar es_regexp_to_string = __nested_webpack_require_210484__(9714);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js\nvar es_string_iterator = __nested_webpack_require_210484__(8783);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.match.js\nvar es_string_match = __nested_webpack_require_210484__(4723);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js\nvar es_string_replace = __nested_webpack_require_210484__(5306);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.split.js\nvar es_string_split = __nested_webpack_require_210484__(3123);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.trim.js\nvar es_string_trim = __nested_webpack_require_210484__(3210);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.uint8-array.js\nvar es_typed_array_uint8_array = __nested_webpack_require_210484__(2472);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.copy-within.js\nvar es_typed_array_copy_within = __nested_webpack_require_210484__(2990);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.every.js\nvar es_typed_array_every = __nested_webpack_require_210484__(8927);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.fill.js\nvar es_typed_array_fill = __nested_webpack_require_210484__(3105);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.filter.js\nvar es_typed_array_filter = __nested_webpack_require_210484__(5035);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.find.js\nvar es_typed_array_find = __nested_webpack_require_210484__(4345);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.find-index.js\nvar es_typed_array_find_index = __nested_webpack_require_210484__(7174);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.for-each.js\nvar es_typed_array_for_each = __nested_webpack_require_210484__(2846);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.includes.js\nvar es_typed_array_includes = __nested_webpack_require_210484__(4731);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.index-of.js\nvar es_typed_array_index_of = __nested_webpack_require_210484__(7209);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.iterator.js\nvar es_typed_array_iterator = __nested_webpack_require_210484__(6319);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.join.js\nvar es_typed_array_join = __nested_webpack_require_210484__(8867);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.last-index-of.js\nvar es_typed_array_last_index_of = __nested_webpack_require_210484__(7789);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.map.js\nvar es_typed_array_map = __nested_webpack_require_210484__(3739);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.reduce.js\nvar es_typed_array_reduce = __nested_webpack_require_210484__(9368);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.reduce-right.js\nvar es_typed_array_reduce_right = __nested_webpack_require_210484__(4483);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.reverse.js\nvar es_typed_array_reverse = __nested_webpack_require_210484__(2056);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.set.js\nvar es_typed_array_set = __nested_webpack_require_210484__(3462);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.slice.js\nvar es_typed_array_slice = __nested_webpack_require_210484__(678);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.some.js\nvar es_typed_array_some = __nested_webpack_require_210484__(7462);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.sort.js\nvar es_typed_array_sort = __nested_webpack_require_210484__(3824);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.subarray.js\nvar es_typed_array_subarray = __nested_webpack_require_210484__(5021);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.to-locale-string.js\nvar es_typed_array_to_locale_string = __nested_webpack_require_210484__(2974);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.to-string.js\nvar es_typed_array_to_string = __nested_webpack_require_210484__(5016);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js\nvar web_dom_collections_for_each = __nested_webpack_require_210484__(4747);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js\nvar web_dom_collections_iterator = __nested_webpack_require_210484__(3948);\n// EXTERNAL MODULE: ./node_modules/core-js/modules/web.url.js\nvar web_url = __nested_webpack_require_210484__(285);\n;// CONCATENATED MODULE: ./src/emitter.js\n\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it; if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = o[Symbol.iterator](); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n// The Emitter class provides the ability to call `.on()` on Dropzone to listen\n// to events.\n// It is strongly based on component's emitter class, and I removed the\n// functionality because of the dependency hell with different frameworks.\nvar Emitter = /*#__PURE__*/function () {\n  function Emitter() {\n    _classCallCheck(this, Emitter);\n  }\n\n  _createClass(Emitter, [{\n    key: \"on\",\n    value: // Add an event listener for given event\n    function on(event, fn) {\n      this._callbacks = this._callbacks || {}; // Create namespace for this event\n\n      if (!this._callbacks[event]) {\n        this._callbacks[event] = [];\n      }\n\n      this._callbacks[event].push(fn);\n\n      return this;\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(event) {\n      this._callbacks = this._callbacks || {};\n      var callbacks = this._callbacks[event];\n\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      if (callbacks) {\n        var _iterator = _createForOfIteratorHelper(callbacks, true),\n            _step;\n\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var callback = _step.value;\n            callback.apply(this, args);\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      } // trigger a corresponding DOM event\n\n\n      if (this.element) {\n        this.element.dispatchEvent(this.makeEvent(\"dropzone:\" + event, {\n          args: args\n        }));\n      }\n\n      return this;\n    }\n  }, {\n    key: \"makeEvent\",\n    value: function makeEvent(eventName, detail) {\n      var params = {\n        bubbles: true,\n        cancelable: true,\n        detail: detail\n      };\n\n      if (typeof window.CustomEvent === \"function\") {\n        return new CustomEvent(eventName, params);\n      } else {\n        // IE 11 support\n        // https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent\n        var evt = document.createEvent(\"CustomEvent\");\n        evt.initCustomEvent(eventName, params.bubbles, params.cancelable, params.detail);\n        return evt;\n      }\n    } // Remove event listener for given event. If fn is not provided, all event\n    // listeners for that event will be removed. If neither is provided, all\n    // event listeners will be removed.\n\n  }, {\n    key: \"off\",\n    value: function off(event, fn) {\n      if (!this._callbacks || arguments.length === 0) {\n        this._callbacks = {};\n        return this;\n      } // specific event\n\n\n      var callbacks = this._callbacks[event];\n\n      if (!callbacks) {\n        return this;\n      } // remove all handlers\n\n\n      if (arguments.length === 1) {\n        delete this._callbacks[event];\n        return this;\n      } // remove specific handler\n\n\n      for (var i = 0; i < callbacks.length; i++) {\n        var callback = callbacks[i];\n\n        if (callback === fn) {\n          callbacks.splice(i, 1);\n          break;\n        }\n      }\n\n      return this;\n    }\n  }]);\n\n  return Emitter;\n}();\n\n\n;// CONCATENATED MODULE: ./src/preview-template.html\n// Module\nvar code = \"<div class=\\\"dz-preview dz-file-preview\\\"> <div class=\\\"dz-image\\\"><img data-dz-thumbnail/></div> <div class=\\\"dz-details\\\"> <div class=\\\"dz-size\\\"><span data-dz-size></span></div> <div class=\\\"dz-filename\\\"><span data-dz-name></span></div> </div> <div class=\\\"dz-progress\\\"> <span class=\\\"dz-upload\\\" data-dz-uploadprogress></span> </div> <div class=\\\"dz-error-message\\\"><span data-dz-errormessage></span></div> <div class=\\\"dz-success-mark\\\"> <svg width=\\\"54px\\\" height=\\\"54px\\\" viewBox=\\\"0 0 54 54\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"> <title>Check</title> <g stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\"> <path d=\\\"M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z\\\" stroke-opacity=\\\"0.198794158\\\" stroke=\\\"#747474\\\" fill-opacity=\\\"0.816519475\\\" fill=\\\"#FFFFFF\\\"></path> </g> </svg> </div> <div class=\\\"dz-error-mark\\\"> <svg width=\\\"54px\\\" height=\\\"54px\\\" viewBox=\\\"0 0 54 54\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"> <title>Error</title> <g stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\"> <g stroke=\\\"#747474\\\" stroke-opacity=\\\"0.198794158\\\" fill=\\\"#FFFFFF\\\" fill-opacity=\\\"0.816519475\\\"> <path d=\\\"M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z\\\"></path> </g> </g> </svg> </div> </div> \";\n// Exports\n/* harmony default export */ var preview_template = (code);\n;// CONCATENATED MODULE: ./src/options.js\n\n\n\n\n\nfunction options_createForOfIteratorHelper(o, allowArrayLike) { var it; if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) { if (Array.isArray(o) || (it = options_unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = o[Symbol.iterator](); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction options_unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return options_arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return options_arrayLikeToArray(o, minLen); }\n\nfunction options_arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n\n\nvar defaultOptions = {\n  /**\n   * Has to be specified on elements other than form (or when the form\n   * doesn't have an `action` attribute). You can also\n   * provide a function that will be called with `files` and\n   * must return the url (since `v3.12.0`)\n   */\n  url: null,\n\n  /**\n   * Can be changed to `\"put\"` if necessary. You can also provide a function\n   * that will be called with `files` and must return the method (since `v3.12.0`).\n   */\n  method: \"post\",\n\n  /**\n   * Will be set on the XHRequest.\n   */\n  withCredentials: false,\n\n  /**\n   * The timeout for the XHR requests in milliseconds (since `v4.4.0`).\n   * If set to null or 0, no timeout is going to be set.\n   */\n  timeout: null,\n\n  /**\n   * How many file uploads to process in parallel (See the\n   * Enqueuing file uploads documentation section for more info)\n   */\n  parallelUploads: 2,\n\n  /**\n   * Whether to send multiple files in one request. If\n   * this it set to true, then the fallback file input element will\n   * have the `multiple` attribute as well. This option will\n   * also trigger additional events (like `processingmultiple`). See the events\n   * documentation section for more information.\n   */\n  uploadMultiple: false,\n\n  /**\n   * Whether you want files to be uploaded in chunks to your server. This can't be\n   * used in combination with `uploadMultiple`.\n   *\n   * See [chunksUploaded](#config-chunksUploaded) for the callback to finalise an upload.\n   */\n  chunking: false,\n\n  /**\n   * If `chunking` is enabled, this defines whether **every** file should be chunked,\n   * even if the file size is below chunkSize. This means, that the additional chunk\n   * form data will be submitted and the `chunksUploaded` callback will be invoked.\n   */\n  forceChunking: false,\n\n  /**\n   * If `chunking` is `true`, then this defines the chunk size in bytes.\n   */\n  chunkSize: 2000000,\n\n  /**\n   * If `true`, the individual chunks of a file are being uploaded simultaneously.\n   */\n  parallelChunkUploads: false,\n\n  /**\n   * Whether a chunk should be retried if it fails.\n   */\n  retryChunks: false,\n\n  /**\n   * If `retryChunks` is true, how many times should it be retried.\n   */\n  retryChunksLimit: 3,\n\n  /**\n   * The maximum filesize (in bytes) that is allowed to be uploaded.\n   */\n  maxFilesize: 256,\n\n  /**\n   * The name of the file param that gets transferred.\n   * **NOTE**: If you have the option  `uploadMultiple` set to `true`, then\n   * Dropzone will append `[]` to the name.\n   */\n  paramName: \"file\",\n\n  /**\n   * Whether thumbnails for images should be generated\n   */\n  createImageThumbnails: true,\n\n  /**\n   * In MB. When the filename exceeds this limit, the thumbnail will not be generated.\n   */\n  maxThumbnailFilesize: 10,\n\n  /**\n   * If `null`, the ratio of the image will be used to calculate it.\n   */\n  thumbnailWidth: 120,\n\n  /**\n   * The same as `thumbnailWidth`. If both are null, images will not be resized.\n   */\n  thumbnailHeight: 120,\n\n  /**\n   * How the images should be scaled down in case both, `thumbnailWidth` and `thumbnailHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  thumbnailMethod: \"crop\",\n\n  /**\n   * If set, images will be resized to these dimensions before being **uploaded**.\n   * If only one, `resizeWidth` **or** `resizeHeight` is provided, the original aspect\n   * ratio of the file will be preserved.\n   *\n   * The `options.transformFile` function uses these options, so if the `transformFile` function\n   * is overridden, these options don't do anything.\n   */\n  resizeWidth: null,\n\n  /**\n   * See `resizeWidth`.\n   */\n  resizeHeight: null,\n\n  /**\n   * The mime type of the resized image (before it gets uploaded to the server).\n   * If `null` the original mime type will be used. To force jpeg, for example, use `image/jpeg`.\n   * See `resizeWidth` for more information.\n   */\n  resizeMimeType: null,\n\n  /**\n   * The quality of the resized images. See `resizeWidth`.\n   */\n  resizeQuality: 0.8,\n\n  /**\n   * How the images should be scaled down in case both, `resizeWidth` and `resizeHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  resizeMethod: \"contain\",\n\n  /**\n   * The base that is used to calculate the **displayed** filesize. You can\n   * change this to 1024 if you would rather display kibibytes, mebibytes,\n   * etc... 1024 is technically incorrect, because `1024 bytes` are `1 kibibyte`\n   * not `1 kilobyte`. You can change this to `1024` if you don't care about\n   * validity.\n   */\n  filesizeBase: 1000,\n\n  /**\n   * If not `null` defines how many files this Dropzone handles. If it exceeds,\n   * the event `maxfilesexceeded` will be called. The dropzone element gets the\n   * class `dz-max-files-reached` accordingly so you can provide visual\n   * feedback.\n   */\n  maxFiles: null,\n\n  /**\n   * An optional object to send additional headers to the server. Eg:\n   * `{ \"My-Awesome-Header\": \"header value\" }`\n   */\n  headers: null,\n\n  /**\n   * If `true`, the dropzone element itself will be clickable, if `false`\n   * nothing will be clickable.\n   *\n   * You can also pass an HTML element, a CSS selector (for multiple elements)\n   * or an array of those. In that case, all of those elements will trigger an\n   * upload when clicked.\n   */\n  clickable: true,\n\n  /**\n   * Whether hidden files in directories should be ignored.\n   */\n  ignoreHiddenFiles: true,\n\n  /**\n   * The default implementation of `accept` checks the file's mime type or\n   * extension against this list. This is a comma separated list of mime\n   * types or file extensions.\n   *\n   * Eg.: `image/*,application/pdf,.psd`\n   *\n   * If the Dropzone is `clickable` this option will also be used as\n   * [`accept`](https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept)\n   * parameter on the hidden file input as well.\n   */\n  acceptedFiles: null,\n\n  /**\n   * **Deprecated!**\n   * Use acceptedFiles instead.\n   */\n  acceptedMimeTypes: null,\n\n  /**\n   * If false, files will be added to the queue but the queue will not be\n   * processed automatically.\n   * This can be useful if you need some additional user input before sending\n   * files (or if you want want all files sent at once).\n   * If you're ready to send the file simply call `myDropzone.processQueue()`.\n   *\n   * See the [enqueuing file uploads](#enqueuing-file-uploads) documentation\n   * section for more information.\n   */\n  autoProcessQueue: true,\n\n  /**\n   * If false, files added to the dropzone will not be queued by default.\n   * You'll have to call `enqueueFile(file)` manually.\n   */\n  autoQueue: true,\n\n  /**\n   * If `true`, this will add a link to every file preview to remove or cancel (if\n   * already uploading) the file. The `dictCancelUpload`, `dictCancelUploadConfirmation`\n   * and `dictRemoveFile` options are used for the wording.\n   */\n  addRemoveLinks: false,\n\n  /**\n   * Defines where to display the file previews – if `null` the\n   * Dropzone element itself is used. Can be a plain `HTMLElement` or a CSS\n   * selector. The element should have the `dropzone-previews` class so\n   * the previews are displayed properly.\n   */\n  previewsContainer: null,\n\n  /**\n   * Set this to `true` if you don't want previews to be shown.\n   */\n  disablePreviews: false,\n\n  /**\n   * This is the element the hidden input field (which is used when clicking on the\n   * dropzone to trigger file selection) will be appended to. This might\n   * be important in case you use frameworks to switch the content of your page.\n   *\n   * Can be a selector string, or an element directly.\n   */\n  hiddenInputContainer: \"body\",\n\n  /**\n   * If null, no capture type will be specified\n   * If camera, mobile devices will skip the file selection and choose camera\n   * If microphone, mobile devices will skip the file selection and choose the microphone\n   * If camcorder, mobile devices will skip the file selection and choose the camera in video mode\n   * On apple devices multiple must be set to false.  AcceptedFiles may need to\n   * be set to an appropriate mime type (e.g. \"image/*\", \"audio/*\", or \"video/*\").\n   */\n  capture: null,\n\n  /**\n   * **Deprecated**. Use `renameFile` instead.\n   */\n  renameFilename: null,\n\n  /**\n   * A function that is invoked before the file is uploaded to the server and renames the file.\n   * This function gets the `File` as argument and can use the `file.name`. The actual name of the\n   * file that gets used during the upload can be accessed through `file.upload.filename`.\n   */\n  renameFile: null,\n\n  /**\n   * If `true` the fallback will be forced. This is very useful to test your server\n   * implementations first and make sure that everything works as\n   * expected without dropzone if you experience problems, and to test\n   * how your fallbacks will look.\n   */\n  forceFallback: false,\n\n  /**\n   * The text used before any files are dropped.\n   */\n  dictDefaultMessage: \"Drop files here to upload\",\n\n  /**\n   * The text that replaces the default message text it the browser is not supported.\n   */\n  dictFallbackMessage: \"Your browser does not support drag'n'drop file uploads.\",\n\n  /**\n   * The text that will be added before the fallback form.\n   * If you provide a  fallback element yourself, or if this option is `null` this will\n   * be ignored.\n   */\n  dictFallbackText: \"Please use the fallback form below to upload your files like in the olden days.\",\n\n  /**\n   * If the filesize is too big.\n   * `{{filesize}}` and `{{maxFilesize}}` will be replaced with the respective configuration values.\n   */\n  dictFileTooBig: \"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.\",\n\n  /**\n   * If the file doesn't match the file type.\n   */\n  dictInvalidFileType: \"You can't upload files of this type.\",\n\n  /**\n   * If the server response was invalid.\n   * `{{statusCode}}` will be replaced with the servers status code.\n   */\n  dictResponseError: \"Server responded with {{statusCode}} code.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for the cancel upload link.\n   */\n  dictCancelUpload: \"Cancel upload\",\n\n  /**\n   * The text that is displayed if an upload was manually canceled\n   */\n  dictUploadCanceled: \"Upload canceled.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for confirmation when cancelling upload.\n   */\n  dictCancelUploadConfirmation: \"Are you sure you want to cancel this upload?\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used to remove a file.\n   */\n  dictRemoveFile: \"Remove file\",\n\n  /**\n   * If this is not null, then the user will be prompted before removing a file.\n   */\n  dictRemoveFileConfirmation: null,\n\n  /**\n   * Displayed if `maxFiles` is st and exceeded.\n   * The string `{{maxFiles}}` will be replaced by the configuration value.\n   */\n  dictMaxFilesExceeded: \"You can not upload any more files.\",\n\n  /**\n   * Allows you to translate the different units. Starting with `tb` for terabytes and going down to\n   * `b` for bytes.\n   */\n  dictFileSizeUnits: {\n    tb: \"TB\",\n    gb: \"GB\",\n    mb: \"MB\",\n    kb: \"KB\",\n    b: \"b\"\n  },\n\n  /**\n   * Called when dropzone initialized\n   * You can add event listeners here\n   */\n  init: function init() {},\n\n  /**\n   * Can be an **object** of additional parameters to transfer to the server, **or** a `Function`\n   * that gets invoked with the `files`, `xhr` and, if it's a chunked upload, `chunk` arguments. In case\n   * of a function, this needs to return a map.\n   *\n   * The default implementation does nothing for normal uploads, but adds relevant information for\n   * chunked uploads.\n   *\n   * This is the same as adding hidden input fields in the form element.\n   */\n  params: function params(files, xhr, chunk) {\n    if (chunk) {\n      return {\n        dzuuid: chunk.file.upload.uuid,\n        dzchunkindex: chunk.index,\n        dztotalfilesize: chunk.file.size,\n        dzchunksize: this.options.chunkSize,\n        dztotalchunkcount: chunk.file.upload.totalChunkCount,\n        dzchunkbyteoffset: chunk.index * this.options.chunkSize\n      };\n    }\n  },\n\n  /**\n   * A function that gets a [file](https://developer.mozilla.org/en-US/docs/DOM/File)\n   * and a `done` function as parameters.\n   *\n   * If the done function is invoked without arguments, the file is \"accepted\" and will\n   * be processed. If you pass an error message, the file is rejected, and the error\n   * message will be displayed.\n   * This function will not be called if the file is too big or doesn't match the mime types.\n   */\n  accept: function accept(file, done) {\n    return done();\n  },\n\n  /**\n   * The callback that will be invoked when all chunks have been uploaded for a file.\n   * It gets the file for which the chunks have been uploaded as the first parameter,\n   * and the `done` function as second. `done()` needs to be invoked when everything\n   * needed to finish the upload process is done.\n   */\n  chunksUploaded: function chunksUploaded(file, done) {\n    done();\n  },\n\n  /**\n   * Gets called when the browser is not supported.\n   * The default implementation shows the fallback input field and adds\n   * a text.\n   */\n  fallback: function fallback() {\n    // This code should pass in IE7... :(\n    var messageElement;\n    this.element.className = \"\".concat(this.element.className, \" dz-browser-not-supported\");\n\n    var _iterator = options_createForOfIteratorHelper(this.element.getElementsByTagName(\"div\"), true),\n        _step;\n\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var child = _step.value;\n\n        if (/(^| )dz-message($| )/.test(child.className)) {\n          messageElement = child;\n          child.className = \"dz-message\"; // Removes the 'dz-default' class\n\n          break;\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n\n    if (!messageElement) {\n      messageElement = Dropzone.createElement('<div class=\"dz-message\"><span></span></div>');\n      this.element.appendChild(messageElement);\n    }\n\n    var span = messageElement.getElementsByTagName(\"span\")[0];\n\n    if (span) {\n      if (span.textContent != null) {\n        span.textContent = this.options.dictFallbackMessage;\n      } else if (span.innerText != null) {\n        span.innerText = this.options.dictFallbackMessage;\n      }\n    }\n\n    return this.element.appendChild(this.getFallbackForm());\n  },\n\n  /**\n   * Gets called to calculate the thumbnail dimensions.\n   *\n   * It gets `file`, `width` and `height` (both may be `null`) as parameters and must return an object containing:\n   *\n   *  - `srcWidth` & `srcHeight` (required)\n   *  - `trgWidth` & `trgHeight` (required)\n   *  - `srcX` & `srcY` (optional, default `0`)\n   *  - `trgX` & `trgY` (optional, default `0`)\n   *\n   * Those values are going to be used by `ctx.drawImage()`.\n   */\n  resize: function resize(file, width, height, resizeMethod) {\n    var info = {\n      srcX: 0,\n      srcY: 0,\n      srcWidth: file.width,\n      srcHeight: file.height\n    };\n    var srcRatio = file.width / file.height; // Automatically calculate dimensions if not specified\n\n    if (width == null && height == null) {\n      width = info.srcWidth;\n      height = info.srcHeight;\n    } else if (width == null) {\n      width = height * srcRatio;\n    } else if (height == null) {\n      height = width / srcRatio;\n    } // Make sure images aren't upscaled\n\n\n    width = Math.min(width, info.srcWidth);\n    height = Math.min(height, info.srcHeight);\n    var trgRatio = width / height;\n\n    if (info.srcWidth > width || info.srcHeight > height) {\n      // Image is bigger and needs rescaling\n      if (resizeMethod === \"crop\") {\n        if (srcRatio > trgRatio) {\n          info.srcHeight = file.height;\n          info.srcWidth = info.srcHeight * trgRatio;\n        } else {\n          info.srcWidth = file.width;\n          info.srcHeight = info.srcWidth / trgRatio;\n        }\n      } else if (resizeMethod === \"contain\") {\n        // Method 'contain'\n        if (srcRatio > trgRatio) {\n          height = width / srcRatio;\n        } else {\n          width = height * srcRatio;\n        }\n      } else {\n        throw new Error(\"Unknown resizeMethod '\".concat(resizeMethod, \"'\"));\n      }\n    }\n\n    info.srcX = (file.width - info.srcWidth) / 2;\n    info.srcY = (file.height - info.srcHeight) / 2;\n    info.trgWidth = width;\n    info.trgHeight = height;\n    return info;\n  },\n\n  /**\n   * Can be used to transform the file (for example, resize an image if necessary).\n   *\n   * The default implementation uses `resizeWidth` and `resizeHeight` (if provided) and resizes\n   * images according to those dimensions.\n   *\n   * Gets the `file` as the first parameter, and a `done()` function as the second, that needs\n   * to be invoked with the file when the transformation is done.\n   */\n  transformFile: function transformFile(file, done) {\n    if ((this.options.resizeWidth || this.options.resizeHeight) && file.type.match(/image.*/)) {\n      return this.resizeImage(file, this.options.resizeWidth, this.options.resizeHeight, this.options.resizeMethod, done);\n    } else {\n      return done(file);\n    }\n  },\n\n  /**\n   * A string that contains the template used for each dropped\n   * file. Change it to fulfill your needs but make sure to properly\n   * provide all elements.\n   *\n   * If you want to use an actual HTML element instead of providing a String\n   * as a config option, you could create a div with the id `tpl`,\n   * put the template inside it and provide the element like this:\n   *\n   *     document\n   *       .querySelector('#tpl')\n   *       .innerHTML\n   *\n   */\n  previewTemplate: preview_template,\n\n  /*\n   Those functions register themselves to the events on init and handle all\n   the user interface specific stuff. Overwriting them won't break the upload\n   but can break the way it's displayed.\n   You can overwrite them if you don't like the default behavior. If you just\n   want to add an additional event handler, register it on the dropzone object\n   and don't overwrite those options.\n   */\n  // Those are self explanatory and simply concern the DragnDrop.\n  drop: function drop(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragstart: function dragstart(e) {},\n  dragend: function dragend(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragenter: function dragenter(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragover: function dragover(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragleave: function dragleave(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  paste: function paste(e) {},\n  // Called whenever there are no files left in the dropzone anymore, and the\n  // dropzone should be displayed as if in the initial state.\n  reset: function reset() {\n    return this.element.classList.remove(\"dz-started\");\n  },\n  // Called when a file is added to the queue\n  // Receives `file`\n  addedfile: function addedfile(file) {\n    var _this = this;\n\n    if (this.element === this.previewsContainer) {\n      this.element.classList.add(\"dz-started\");\n    }\n\n    if (this.previewsContainer && !this.options.disablePreviews) {\n      file.previewElement = Dropzone.createElement(this.options.previewTemplate.trim());\n      file.previewTemplate = file.previewElement; // Backwards compatibility\n\n      this.previewsContainer.appendChild(file.previewElement);\n\n      var _iterator2 = options_createForOfIteratorHelper(file.previewElement.querySelectorAll(\"[data-dz-name]\"), true),\n          _step2;\n\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var node = _step2.value;\n          node.textContent = file.name;\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n\n      var _iterator3 = options_createForOfIteratorHelper(file.previewElement.querySelectorAll(\"[data-dz-size]\"), true),\n          _step3;\n\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          node = _step3.value;\n          node.innerHTML = this.filesize(file.size);\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n\n      if (this.options.addRemoveLinks) {\n        file._removeLink = Dropzone.createElement(\"<a class=\\\"dz-remove\\\" href=\\\"javascript:undefined;\\\" data-dz-remove>\".concat(this.options.dictRemoveFile, \"</a>\"));\n        file.previewElement.appendChild(file._removeLink);\n      }\n\n      var removeFileEvent = function removeFileEvent(e) {\n        e.preventDefault();\n        e.stopPropagation();\n\n        if (file.status === Dropzone.UPLOADING) {\n          return Dropzone.confirm(_this.options.dictCancelUploadConfirmation, function () {\n            return _this.removeFile(file);\n          });\n        } else {\n          if (_this.options.dictRemoveFileConfirmation) {\n            return Dropzone.confirm(_this.options.dictRemoveFileConfirmation, function () {\n              return _this.removeFile(file);\n            });\n          } else {\n            return _this.removeFile(file);\n          }\n        }\n      };\n\n      var _iterator4 = options_createForOfIteratorHelper(file.previewElement.querySelectorAll(\"[data-dz-remove]\"), true),\n          _step4;\n\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var removeLink = _step4.value;\n          removeLink.addEventListener(\"click\", removeFileEvent);\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n    }\n  },\n  // Called whenever a file is removed.\n  removedfile: function removedfile(file) {\n    if (file.previewElement != null && file.previewElement.parentNode != null) {\n      file.previewElement.parentNode.removeChild(file.previewElement);\n    }\n\n    return this._updateMaxFilesReachedClass();\n  },\n  // Called when a thumbnail has been generated\n  // Receives `file` and `dataUrl`\n  thumbnail: function thumbnail(file, dataUrl) {\n    if (file.previewElement) {\n      file.previewElement.classList.remove(\"dz-file-preview\");\n\n      var _iterator5 = options_createForOfIteratorHelper(file.previewElement.querySelectorAll(\"[data-dz-thumbnail]\"), true),\n          _step5;\n\n      try {\n        for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n          var thumbnailElement = _step5.value;\n          thumbnailElement.alt = file.name;\n          thumbnailElement.src = dataUrl;\n        }\n      } catch (err) {\n        _iterator5.e(err);\n      } finally {\n        _iterator5.f();\n      }\n\n      return setTimeout(function () {\n        return file.previewElement.classList.add(\"dz-image-preview\");\n      }, 1);\n    }\n  },\n  // Called whenever an error occurs\n  // Receives `file` and `message`\n  error: function error(file, message) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-error\");\n\n      if (typeof message !== \"string\" && message.error) {\n        message = message.error;\n      }\n\n      var _iterator6 = options_createForOfIteratorHelper(file.previewElement.querySelectorAll(\"[data-dz-errormessage]\"), true),\n          _step6;\n\n      try {\n        for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {\n          var node = _step6.value;\n          node.textContent = message;\n        }\n      } catch (err) {\n        _iterator6.e(err);\n      } finally {\n        _iterator6.f();\n      }\n    }\n  },\n  errormultiple: function errormultiple() {},\n  // Called when a file gets processed. Since there is a cue, not all added\n  // files are processed immediately.\n  // Receives `file`\n  processing: function processing(file) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-processing\");\n\n      if (file._removeLink) {\n        return file._removeLink.innerHTML = this.options.dictCancelUpload;\n      }\n    }\n  },\n  processingmultiple: function processingmultiple() {},\n  // Called whenever the upload progress gets updated.\n  // Receives `file`, `progress` (percentage 0-100) and `bytesSent`.\n  // To get the total number of bytes of the file, use `file.size`\n  uploadprogress: function uploadprogress(file, progress, bytesSent) {\n    if (file.previewElement) {\n      var _iterator7 = options_createForOfIteratorHelper(file.previewElement.querySelectorAll(\"[data-dz-uploadprogress]\"), true),\n          _step7;\n\n      try {\n        for (_iterator7.s(); !(_step7 = _iterator7.n()).done;) {\n          var node = _step7.value;\n          node.nodeName === \"PROGRESS\" ? node.value = progress : node.style.width = \"\".concat(progress, \"%\");\n        }\n      } catch (err) {\n        _iterator7.e(err);\n      } finally {\n        _iterator7.f();\n      }\n    }\n  },\n  // Called whenever the total upload progress gets updated.\n  // Called with totalUploadProgress (0-100), totalBytes and totalBytesSent\n  totaluploadprogress: function totaluploadprogress() {},\n  // Called just before the file is sent. Gets the `xhr` object as second\n  // parameter, so you can modify it (for example to add a CSRF token) and a\n  // `formData` object to add additional information.\n  sending: function sending() {},\n  sendingmultiple: function sendingmultiple() {},\n  // When the complete upload is finished and successful\n  // Receives `file`\n  success: function success(file) {\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-success\");\n    }\n  },\n  successmultiple: function successmultiple() {},\n  // When the upload is canceled.\n  canceled: function canceled(file) {\n    return this.emit(\"error\", file, this.options.dictUploadCanceled);\n  },\n  canceledmultiple: function canceledmultiple() {},\n  // When the upload is finished, either with success or an error.\n  // Receives `file`\n  complete: function complete(file) {\n    if (file._removeLink) {\n      file._removeLink.innerHTML = this.options.dictRemoveFile;\n    }\n\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-complete\");\n    }\n  },\n  completemultiple: function completemultiple() {},\n  maxfilesexceeded: function maxfilesexceeded() {},\n  maxfilesreached: function maxfilesreached() {},\n  queuecomplete: function queuecomplete() {},\n  addedfiles: function addedfiles() {}\n};\n/* harmony default export */ var src_options = (defaultOptions);\n;// CONCATENATED MODULE: ./src/dropzone.js\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction dropzone_createForOfIteratorHelper(o, allowArrayLike) { var it; if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) { if (Array.isArray(o) || (it = dropzone_unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = o[Symbol.iterator](); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction dropzone_unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return dropzone_arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return dropzone_arrayLikeToArray(o, minLen); }\n\nfunction dropzone_arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction dropzone_classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction dropzone_defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction dropzone_createClass(Constructor, protoProps, staticProps) { if (protoProps) dropzone_defineProperties(Constructor.prototype, protoProps); if (staticProps) dropzone_defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\n\nvar Dropzone = /*#__PURE__*/function (_Emitter) {\n  _inherits(Dropzone, _Emitter);\n\n  var _super = _createSuper(Dropzone);\n\n  function Dropzone(el, options) {\n    var _this;\n\n    dropzone_classCallCheck(this, Dropzone);\n\n    _this = _super.call(this);\n    var fallback, left;\n    _this.element = el; // For backwards compatibility since the version was in the prototype previously\n\n    _this.version = Dropzone.version;\n    _this.clickableElements = [];\n    _this.listeners = [];\n    _this.files = []; // All files\n\n    if (typeof _this.element === \"string\") {\n      _this.element = document.querySelector(_this.element);\n    } // Not checking if instance of HTMLElement or Element since IE9 is extremely weird.\n\n\n    if (!_this.element || _this.element.nodeType == null) {\n      throw new Error(\"Invalid dropzone element.\");\n    }\n\n    if (_this.element.dropzone) {\n      throw new Error(\"Dropzone already attached.\");\n    } // Now add this dropzone to the instances.\n\n\n    Dropzone.instances.push(_assertThisInitialized(_this)); // Put the dropzone inside the element itself.\n\n    _this.element.dropzone = _assertThisInitialized(_this);\n    var elementOptions = (left = Dropzone.optionsForElement(_this.element)) != null ? left : {};\n    _this.options = Dropzone.extend({}, src_options, elementOptions, options != null ? options : {});\n    _this.options.previewTemplate = _this.options.previewTemplate.replace(/\\n*/g, \"\"); // If the browser failed, just call the fallback and leave\n\n    if (_this.options.forceFallback || !Dropzone.isBrowserSupported()) {\n      return _possibleConstructorReturn(_this, _this.options.fallback.call(_assertThisInitialized(_this)));\n    } // @options.url = @element.getAttribute \"action\" unless @options.url?\n\n\n    if (_this.options.url == null) {\n      _this.options.url = _this.element.getAttribute(\"action\");\n    }\n\n    if (!_this.options.url) {\n      throw new Error(\"No URL provided.\");\n    }\n\n    if (_this.options.acceptedFiles && _this.options.acceptedMimeTypes) {\n      throw new Error(\"You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.\");\n    }\n\n    if (_this.options.uploadMultiple && _this.options.chunking) {\n      throw new Error(\"You cannot set both: uploadMultiple and chunking.\");\n    } // Backwards compatibility\n\n\n    if (_this.options.acceptedMimeTypes) {\n      _this.options.acceptedFiles = _this.options.acceptedMimeTypes;\n      delete _this.options.acceptedMimeTypes;\n    } // Backwards compatibility\n\n\n    if (_this.options.renameFilename != null) {\n      _this.options.renameFile = function (file) {\n        return _this.options.renameFilename.call(_assertThisInitialized(_this), file.name, file);\n      };\n    }\n\n    if (typeof _this.options.method === \"string\") {\n      _this.options.method = _this.options.method.toUpperCase();\n    }\n\n    if ((fallback = _this.getExistingFallback()) && fallback.parentNode) {\n      // Remove the fallback\n      fallback.parentNode.removeChild(fallback);\n    } // Display previews in the previewsContainer element or the Dropzone element unless explicitly set to false\n\n\n    if (_this.options.previewsContainer !== false) {\n      if (_this.options.previewsContainer) {\n        _this.previewsContainer = Dropzone.getElement(_this.options.previewsContainer, \"previewsContainer\");\n      } else {\n        _this.previewsContainer = _this.element;\n      }\n    }\n\n    if (_this.options.clickable) {\n      if (_this.options.clickable === true) {\n        _this.clickableElements = [_this.element];\n      } else {\n        _this.clickableElements = Dropzone.getElements(_this.options.clickable, \"clickable\");\n      }\n    }\n\n    _this.init();\n\n    return _this;\n  } // Returns all files that have been accepted\n\n\n  dropzone_createClass(Dropzone, [{\n    key: \"getAcceptedFiles\",\n    value: function getAcceptedFiles() {\n      return this.files.filter(function (file) {\n        return file.accepted;\n      }).map(function (file) {\n        return file;\n      });\n    } // Returns all files that have been rejected\n    // Not sure when that's going to be useful, but added for completeness.\n\n  }, {\n    key: \"getRejectedFiles\",\n    value: function getRejectedFiles() {\n      return this.files.filter(function (file) {\n        return !file.accepted;\n      }).map(function (file) {\n        return file;\n      });\n    }\n  }, {\n    key: \"getFilesWithStatus\",\n    value: function getFilesWithStatus(status) {\n      return this.files.filter(function (file) {\n        return file.status === status;\n      }).map(function (file) {\n        return file;\n      });\n    } // Returns all files that are in the queue\n\n  }, {\n    key: \"getQueuedFiles\",\n    value: function getQueuedFiles() {\n      return this.getFilesWithStatus(Dropzone.QUEUED);\n    }\n  }, {\n    key: \"getUploadingFiles\",\n    value: function getUploadingFiles() {\n      return this.getFilesWithStatus(Dropzone.UPLOADING);\n    }\n  }, {\n    key: \"getAddedFiles\",\n    value: function getAddedFiles() {\n      return this.getFilesWithStatus(Dropzone.ADDED);\n    } // Files that are either queued or uploading\n\n  }, {\n    key: \"getActiveFiles\",\n    value: function getActiveFiles() {\n      return this.files.filter(function (file) {\n        return file.status === Dropzone.UPLOADING || file.status === Dropzone.QUEUED;\n      }).map(function (file) {\n        return file;\n      });\n    } // The function that gets called when Dropzone is initialized. You\n    // can (and should) setup event listeners inside this function.\n\n  }, {\n    key: \"init\",\n    value: function init() {\n      var _this2 = this;\n\n      // In case it isn't set already\n      if (this.element.tagName === \"form\") {\n        this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n      }\n\n      if (this.element.classList.contains(\"dropzone\") && !this.element.querySelector(\".dz-message\")) {\n        this.element.appendChild(Dropzone.createElement(\"<div class=\\\"dz-default dz-message\\\"><button class=\\\"dz-button\\\" type=\\\"button\\\">\".concat(this.options.dictDefaultMessage, \"</button></div>\")));\n      }\n\n      if (this.clickableElements.length) {\n        var setupHiddenFileInput = function setupHiddenFileInput() {\n          if (_this2.hiddenFileInput) {\n            _this2.hiddenFileInput.parentNode.removeChild(_this2.hiddenFileInput);\n          }\n\n          _this2.hiddenFileInput = document.createElement(\"input\");\n\n          _this2.hiddenFileInput.setAttribute(\"type\", \"file\");\n\n          if (_this2.options.maxFiles === null || _this2.options.maxFiles > 1) {\n            _this2.hiddenFileInput.setAttribute(\"multiple\", \"multiple\");\n          }\n\n          _this2.hiddenFileInput.className = \"dz-hidden-input\";\n\n          if (_this2.options.acceptedFiles !== null) {\n            _this2.hiddenFileInput.setAttribute(\"accept\", _this2.options.acceptedFiles);\n          }\n\n          if (_this2.options.capture !== null) {\n            _this2.hiddenFileInput.setAttribute(\"capture\", _this2.options.capture);\n          } // Making sure that no one can \"tab\" into this field.\n\n\n          _this2.hiddenFileInput.setAttribute(\"tabindex\", \"-1\"); // Not setting `display=\"none\"` because some browsers don't accept clicks\n          // on elements that aren't displayed.\n\n\n          _this2.hiddenFileInput.style.visibility = \"hidden\";\n          _this2.hiddenFileInput.style.position = \"absolute\";\n          _this2.hiddenFileInput.style.top = \"0\";\n          _this2.hiddenFileInput.style.left = \"0\";\n          _this2.hiddenFileInput.style.height = \"0\";\n          _this2.hiddenFileInput.style.width = \"0\";\n          Dropzone.getElement(_this2.options.hiddenInputContainer, \"hiddenInputContainer\").appendChild(_this2.hiddenFileInput);\n\n          _this2.hiddenFileInput.addEventListener(\"change\", function () {\n            var files = _this2.hiddenFileInput.files;\n\n            if (files.length) {\n              var _iterator = dropzone_createForOfIteratorHelper(files, true),\n                  _step;\n\n              try {\n                for (_iterator.s(); !(_step = _iterator.n()).done;) {\n                  var file = _step.value;\n\n                  _this2.addFile(file);\n                }\n              } catch (err) {\n                _iterator.e(err);\n              } finally {\n                _iterator.f();\n              }\n            }\n\n            _this2.emit(\"addedfiles\", files);\n\n            setupHiddenFileInput();\n          });\n        };\n\n        setupHiddenFileInput();\n      }\n\n      this.URL = window.URL !== null ? window.URL : window.webkitURL; // Setup all event listeners on the Dropzone object itself.\n      // They're not in @setupEventListeners() because they shouldn't be removed\n      // again when the dropzone gets disabled.\n\n      var _iterator2 = dropzone_createForOfIteratorHelper(this.events, true),\n          _step2;\n\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var eventName = _step2.value;\n          this.on(eventName, this.options[eventName]);\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n\n      this.on(\"uploadprogress\", function () {\n        return _this2.updateTotalUploadProgress();\n      });\n      this.on(\"removedfile\", function () {\n        return _this2.updateTotalUploadProgress();\n      });\n      this.on(\"canceled\", function (file) {\n        return _this2.emit(\"complete\", file);\n      }); // Emit a `queuecomplete` event if all files finished uploading.\n\n      this.on(\"complete\", function (file) {\n        if (_this2.getAddedFiles().length === 0 && _this2.getUploadingFiles().length === 0 && _this2.getQueuedFiles().length === 0) {\n          // This needs to be deferred so that `queuecomplete` really triggers after `complete`\n          return setTimeout(function () {\n            return _this2.emit(\"queuecomplete\");\n          }, 0);\n        }\n      });\n\n      var containsFiles = function containsFiles(e) {\n        if (e.dataTransfer.types) {\n          // Because e.dataTransfer.types is an Object in\n          // IE, we need to iterate like this instead of\n          // using e.dataTransfer.types.some()\n          for (var i = 0; i < e.dataTransfer.types.length; i++) {\n            if (e.dataTransfer.types[i] === \"Files\") return true;\n          }\n        }\n\n        return false;\n      };\n\n      var noPropagation = function noPropagation(e) {\n        // If there are no files, we don't want to stop\n        // propagation so we don't interfere with other\n        // drag and drop behaviour.\n        if (!containsFiles(e)) return;\n        e.stopPropagation();\n\n        if (e.preventDefault) {\n          return e.preventDefault();\n        } else {\n          return e.returnValue = false;\n        }\n      }; // Create the listeners\n\n\n      this.listeners = [{\n        element: this.element,\n        events: {\n          dragstart: function dragstart(e) {\n            return _this2.emit(\"dragstart\", e);\n          },\n          dragenter: function dragenter(e) {\n            noPropagation(e);\n            return _this2.emit(\"dragenter\", e);\n          },\n          dragover: function dragover(e) {\n            // Makes it possible to drag files from chrome's download bar\n            // http://stackoverflow.com/questions/19526430/drag-and-drop-file-uploads-from-chrome-downloads-bar\n            // Try is required to prevent bug in Internet Explorer 11 (SCRIPT65535 exception)\n            var efct;\n\n            try {\n              efct = e.dataTransfer.effectAllowed;\n            } catch (error) {}\n\n            e.dataTransfer.dropEffect = \"move\" === efct || \"linkMove\" === efct ? \"move\" : \"copy\";\n            noPropagation(e);\n            return _this2.emit(\"dragover\", e);\n          },\n          dragleave: function dragleave(e) {\n            return _this2.emit(\"dragleave\", e);\n          },\n          drop: function drop(e) {\n            noPropagation(e);\n            return _this2.drop(e);\n          },\n          dragend: function dragend(e) {\n            return _this2.emit(\"dragend\", e);\n          }\n        } // This is disabled right now, because the browsers don't implement it properly.\n        // \"paste\": (e) =>\n        //   noPropagation e\n        //   @paste e\n\n      }];\n      this.clickableElements.forEach(function (clickableElement) {\n        return _this2.listeners.push({\n          element: clickableElement,\n          events: {\n            click: function click(evt) {\n              // Only the actual dropzone or the message element should trigger file selection\n              if (clickableElement !== _this2.element || evt.target === _this2.element || Dropzone.elementInside(evt.target, _this2.element.querySelector(\".dz-message\"))) {\n                _this2.hiddenFileInput.click(); // Forward the click\n\n              }\n\n              return true;\n            }\n          }\n        });\n      });\n      this.enable();\n      return this.options.init.call(this);\n    } // Not fully tested yet\n\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.disable();\n      this.removeAllFiles(true);\n\n      if (this.hiddenFileInput != null ? this.hiddenFileInput.parentNode : undefined) {\n        this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n        this.hiddenFileInput = null;\n      }\n\n      delete this.element.dropzone;\n      return Dropzone.instances.splice(Dropzone.instances.indexOf(this), 1);\n    }\n  }, {\n    key: \"updateTotalUploadProgress\",\n    value: function updateTotalUploadProgress() {\n      var totalUploadProgress;\n      var totalBytesSent = 0;\n      var totalBytes = 0;\n      var activeFiles = this.getActiveFiles();\n\n      if (activeFiles.length) {\n        var _iterator3 = dropzone_createForOfIteratorHelper(this.getActiveFiles(), true),\n            _step3;\n\n        try {\n          for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n            var file = _step3.value;\n            totalBytesSent += file.upload.bytesSent;\n            totalBytes += file.upload.total;\n          }\n        } catch (err) {\n          _iterator3.e(err);\n        } finally {\n          _iterator3.f();\n        }\n\n        totalUploadProgress = 100 * totalBytesSent / totalBytes;\n      } else {\n        totalUploadProgress = 100;\n      }\n\n      return this.emit(\"totaluploadprogress\", totalUploadProgress, totalBytes, totalBytesSent);\n    } // @options.paramName can be a function taking one parameter rather than a string.\n    // A parameter name for a file is obtained simply by calling this with an index number.\n\n  }, {\n    key: \"_getParamName\",\n    value: function _getParamName(n) {\n      if (typeof this.options.paramName === \"function\") {\n        return this.options.paramName(n);\n      } else {\n        return \"\".concat(this.options.paramName).concat(this.options.uploadMultiple ? \"[\".concat(n, \"]\") : \"\");\n      }\n    } // If @options.renameFile is a function,\n    // the function will be used to rename the file.name before appending it to the formData\n\n  }, {\n    key: \"_renameFile\",\n    value: function _renameFile(file) {\n      if (typeof this.options.renameFile !== \"function\") {\n        return file.name;\n      }\n\n      return this.options.renameFile(file);\n    } // Returns a form that can be used as fallback if the browser does not support DragnDrop\n    //\n    // If the dropzone is already a form, only the input field and button are returned. Otherwise a complete form element is provided.\n    // This code has to pass in IE7 :(\n\n  }, {\n    key: \"getFallbackForm\",\n    value: function getFallbackForm() {\n      var existingFallback, form;\n\n      if (existingFallback = this.getExistingFallback()) {\n        return existingFallback;\n      }\n\n      var fieldsString = '<div class=\"dz-fallback\">';\n\n      if (this.options.dictFallbackText) {\n        fieldsString += \"<p>\".concat(this.options.dictFallbackText, \"</p>\");\n      }\n\n      fieldsString += \"<input type=\\\"file\\\" name=\\\"\".concat(this._getParamName(0), \"\\\" \").concat(this.options.uploadMultiple ? 'multiple=\"multiple\"' : undefined, \" /><input type=\\\"submit\\\" value=\\\"Upload!\\\"></div>\");\n      var fields = Dropzone.createElement(fieldsString);\n\n      if (this.element.tagName !== \"FORM\") {\n        form = Dropzone.createElement(\"<form action=\\\"\".concat(this.options.url, \"\\\" enctype=\\\"multipart/form-data\\\" method=\\\"\").concat(this.options.method, \"\\\"></form>\"));\n        form.appendChild(fields);\n      } else {\n        // Make sure that the enctype and method attributes are set properly\n        this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n        this.element.setAttribute(\"method\", this.options.method);\n      }\n\n      return form != null ? form : fields;\n    } // Returns the fallback elements if they exist already\n    //\n    // This code has to pass in IE7 :(\n\n  }, {\n    key: \"getExistingFallback\",\n    value: function getExistingFallback() {\n      var getFallback = function getFallback(elements) {\n        var _iterator4 = dropzone_createForOfIteratorHelper(elements, true),\n            _step4;\n\n        try {\n          for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n            var el = _step4.value;\n\n            if (/(^| )fallback($| )/.test(el.className)) {\n              return el;\n            }\n          }\n        } catch (err) {\n          _iterator4.e(err);\n        } finally {\n          _iterator4.f();\n        }\n      };\n\n      for (var _i = 0, _arr = [\"div\", \"form\"]; _i < _arr.length; _i++) {\n        var tagName = _arr[_i];\n        var fallback;\n\n        if (fallback = getFallback(this.element.getElementsByTagName(tagName))) {\n          return fallback;\n        }\n      }\n    } // Activates all listeners stored in @listeners\n\n  }, {\n    key: \"setupEventListeners\",\n    value: function setupEventListeners() {\n      return this.listeners.map(function (elementListeners) {\n        return function () {\n          var result = [];\n\n          for (var event in elementListeners.events) {\n            var listener = elementListeners.events[event];\n            result.push(elementListeners.element.addEventListener(event, listener, false));\n          }\n\n          return result;\n        }();\n      });\n    } // Deactivates all listeners stored in @listeners\n\n  }, {\n    key: \"removeEventListeners\",\n    value: function removeEventListeners() {\n      return this.listeners.map(function (elementListeners) {\n        return function () {\n          var result = [];\n\n          for (var event in elementListeners.events) {\n            var listener = elementListeners.events[event];\n            result.push(elementListeners.element.removeEventListener(event, listener, false));\n          }\n\n          return result;\n        }();\n      });\n    } // Removes all event listeners and cancels all files in the queue or being processed.\n\n  }, {\n    key: \"disable\",\n    value: function disable() {\n      var _this3 = this;\n\n      this.clickableElements.forEach(function (element) {\n        return element.classList.remove(\"dz-clickable\");\n      });\n      this.removeEventListeners();\n      this.disabled = true;\n      return this.files.map(function (file) {\n        return _this3.cancelUpload(file);\n      });\n    }\n  }, {\n    key: \"enable\",\n    value: function enable() {\n      delete this.disabled;\n      this.clickableElements.forEach(function (element) {\n        return element.classList.add(\"dz-clickable\");\n      });\n      return this.setupEventListeners();\n    } // Returns a nicely formatted filesize\n\n  }, {\n    key: \"filesize\",\n    value: function filesize(size) {\n      var selectedSize = 0;\n      var selectedUnit = \"b\";\n\n      if (size > 0) {\n        var units = [\"tb\", \"gb\", \"mb\", \"kb\", \"b\"];\n\n        for (var i = 0; i < units.length; i++) {\n          var unit = units[i];\n          var cutoff = Math.pow(this.options.filesizeBase, 4 - i) / 10;\n\n          if (size >= cutoff) {\n            selectedSize = size / Math.pow(this.options.filesizeBase, 4 - i);\n            selectedUnit = unit;\n            break;\n          }\n        }\n\n        selectedSize = Math.round(10 * selectedSize) / 10; // Cutting of digits\n      }\n\n      return \"<strong>\".concat(selectedSize, \"</strong> \").concat(this.options.dictFileSizeUnits[selectedUnit]);\n    } // Adds or removes the `dz-max-files-reached` class from the form.\n\n  }, {\n    key: \"_updateMaxFilesReachedClass\",\n    value: function _updateMaxFilesReachedClass() {\n      if (this.options.maxFiles != null && this.getAcceptedFiles().length >= this.options.maxFiles) {\n        if (this.getAcceptedFiles().length === this.options.maxFiles) {\n          this.emit(\"maxfilesreached\", this.files);\n        }\n\n        return this.element.classList.add(\"dz-max-files-reached\");\n      } else {\n        return this.element.classList.remove(\"dz-max-files-reached\");\n      }\n    }\n  }, {\n    key: \"drop\",\n    value: function drop(e) {\n      if (!e.dataTransfer) {\n        return;\n      }\n\n      this.emit(\"drop\", e); // Convert the FileList to an Array\n      // This is necessary for IE11\n\n      var files = [];\n\n      for (var i = 0; i < e.dataTransfer.files.length; i++) {\n        files[i] = e.dataTransfer.files[i];\n      } // Even if it's a folder, files.length will contain the folders.\n\n\n      if (files.length) {\n        var items = e.dataTransfer.items;\n\n        if (items && items.length && items[0].webkitGetAsEntry != null) {\n          // The browser supports dropping of folders, so handle items instead of files\n          this._addFilesFromItems(items);\n        } else {\n          this.handleFiles(files);\n        }\n      }\n\n      this.emit(\"addedfiles\", files);\n    }\n  }, {\n    key: \"paste\",\n    value: function paste(e) {\n      if (__guard__(e != null ? e.clipboardData : undefined, function (x) {\n        return x.items;\n      }) == null) {\n        return;\n      }\n\n      this.emit(\"paste\", e);\n      var items = e.clipboardData.items;\n\n      if (items.length) {\n        return this._addFilesFromItems(items);\n      }\n    }\n  }, {\n    key: \"handleFiles\",\n    value: function handleFiles(files) {\n      var _iterator5 = dropzone_createForOfIteratorHelper(files, true),\n          _step5;\n\n      try {\n        for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n          var file = _step5.value;\n          this.addFile(file);\n        }\n      } catch (err) {\n        _iterator5.e(err);\n      } finally {\n        _iterator5.f();\n      }\n    } // When a folder is dropped (or files are pasted), items must be handled\n    // instead of files.\n\n  }, {\n    key: \"_addFilesFromItems\",\n    value: function _addFilesFromItems(items) {\n      var _this4 = this;\n\n      return function () {\n        var result = [];\n\n        var _iterator6 = dropzone_createForOfIteratorHelper(items, true),\n            _step6;\n\n        try {\n          for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {\n            var item = _step6.value;\n            var entry;\n\n            if (item.webkitGetAsEntry != null && (entry = item.webkitGetAsEntry())) {\n              if (entry.isFile) {\n                result.push(_this4.addFile(item.getAsFile()));\n              } else if (entry.isDirectory) {\n                // Append all files from that directory to files\n                result.push(_this4._addFilesFromDirectory(entry, entry.name));\n              } else {\n                result.push(undefined);\n              }\n            } else if (item.getAsFile != null) {\n              if (item.kind == null || item.kind === \"file\") {\n                result.push(_this4.addFile(item.getAsFile()));\n              } else {\n                result.push(undefined);\n              }\n            } else {\n              result.push(undefined);\n            }\n          }\n        } catch (err) {\n          _iterator6.e(err);\n        } finally {\n          _iterator6.f();\n        }\n\n        return result;\n      }();\n    } // Goes through the directory, and adds each file it finds recursively\n\n  }, {\n    key: \"_addFilesFromDirectory\",\n    value: function _addFilesFromDirectory(directory, path) {\n      var _this5 = this;\n\n      var dirReader = directory.createReader();\n\n      var errorHandler = function errorHandler(error) {\n        return __guardMethod__(console, \"log\", function (o) {\n          return o.log(error);\n        });\n      };\n\n      var readEntries = function readEntries() {\n        return dirReader.readEntries(function (entries) {\n          if (entries.length > 0) {\n            var _iterator7 = dropzone_createForOfIteratorHelper(entries, true),\n                _step7;\n\n            try {\n              for (_iterator7.s(); !(_step7 = _iterator7.n()).done;) {\n                var entry = _step7.value;\n\n                if (entry.isFile) {\n                  entry.file(function (file) {\n                    if (_this5.options.ignoreHiddenFiles && file.name.substring(0, 1) === \".\") {\n                      return;\n                    }\n\n                    file.fullPath = \"\".concat(path, \"/\").concat(file.name);\n                    return _this5.addFile(file);\n                  });\n                } else if (entry.isDirectory) {\n                  _this5._addFilesFromDirectory(entry, \"\".concat(path, \"/\").concat(entry.name));\n                }\n              } // Recursively call readEntries() again, since browser only handle\n              // the first 100 entries.\n              // See: https://developer.mozilla.org/en-US/docs/Web/API/DirectoryReader#readEntries\n\n            } catch (err) {\n              _iterator7.e(err);\n            } finally {\n              _iterator7.f();\n            }\n\n            readEntries();\n          }\n\n          return null;\n        }, errorHandler);\n      };\n\n      return readEntries();\n    } // If `done()` is called without argument the file is accepted\n    // If you call it with an error message, the file is rejected\n    // (This allows for asynchronous validation)\n    //\n    // This function checks the filesize, and if the file.type passes the\n    // `acceptedFiles` check.\n\n  }, {\n    key: \"accept\",\n    value: function accept(file, done) {\n      if (this.options.maxFilesize && file.size > this.options.maxFilesize * 1024 * 1024) {\n        done(this.options.dictFileTooBig.replace(\"{{filesize}}\", Math.round(file.size / 1024 / 10.24) / 100).replace(\"{{maxFilesize}}\", this.options.maxFilesize));\n      } else if (!Dropzone.isValidFile(file, this.options.acceptedFiles)) {\n        done(this.options.dictInvalidFileType);\n      } else if (this.options.maxFiles != null && this.getAcceptedFiles().length >= this.options.maxFiles) {\n        done(this.options.dictMaxFilesExceeded.replace(\"{{maxFiles}}\", this.options.maxFiles));\n        this.emit(\"maxfilesexceeded\", file);\n      } else {\n        this.options.accept.call(this, file, done);\n      }\n    }\n  }, {\n    key: \"addFile\",\n    value: function addFile(file) {\n      var _this6 = this;\n\n      file.upload = {\n        uuid: Dropzone.uuidv4(),\n        progress: 0,\n        // Setting the total upload size to file.size for the beginning\n        // It's actual different than the size to be transmitted.\n        total: file.size,\n        bytesSent: 0,\n        filename: this._renameFile(file) // Not setting chunking information here, because the acutal data — and\n        // thus the chunks — might change if `options.transformFile` is set\n        // and does something to the data.\n\n      };\n      this.files.push(file);\n      file.status = Dropzone.ADDED;\n      this.emit(\"addedfile\", file);\n\n      this._enqueueThumbnail(file);\n\n      this.accept(file, function (error) {\n        if (error) {\n          file.accepted = false;\n\n          _this6._errorProcessing([file], error); // Will set the file.status\n\n        } else {\n          file.accepted = true;\n\n          if (_this6.options.autoQueue) {\n            _this6.enqueueFile(file);\n          } // Will set .accepted = true\n\n        }\n\n        _this6._updateMaxFilesReachedClass();\n      });\n    } // Wrapper for enqueueFile\n\n  }, {\n    key: \"enqueueFiles\",\n    value: function enqueueFiles(files) {\n      var _iterator8 = dropzone_createForOfIteratorHelper(files, true),\n          _step8;\n\n      try {\n        for (_iterator8.s(); !(_step8 = _iterator8.n()).done;) {\n          var file = _step8.value;\n          this.enqueueFile(file);\n        }\n      } catch (err) {\n        _iterator8.e(err);\n      } finally {\n        _iterator8.f();\n      }\n\n      return null;\n    }\n  }, {\n    key: \"enqueueFile\",\n    value: function enqueueFile(file) {\n      var _this7 = this;\n\n      if (file.status === Dropzone.ADDED && file.accepted === true) {\n        file.status = Dropzone.QUEUED;\n\n        if (this.options.autoProcessQueue) {\n          return setTimeout(function () {\n            return _this7.processQueue();\n          }, 0); // Deferring the call\n        }\n      } else {\n        throw new Error(\"This file can't be queued because it has already been processed or was rejected.\");\n      }\n    }\n  }, {\n    key: \"_enqueueThumbnail\",\n    value: function _enqueueThumbnail(file) {\n      var _this8 = this;\n\n      if (this.options.createImageThumbnails && file.type.match(/image.*/) && file.size <= this.options.maxThumbnailFilesize * 1024 * 1024) {\n        this._thumbnailQueue.push(file);\n\n        return setTimeout(function () {\n          return _this8._processThumbnailQueue();\n        }, 0); // Deferring the call\n      }\n    }\n  }, {\n    key: \"_processThumbnailQueue\",\n    value: function _processThumbnailQueue() {\n      var _this9 = this;\n\n      if (this._processingThumbnail || this._thumbnailQueue.length === 0) {\n        return;\n      }\n\n      this._processingThumbnail = true;\n\n      var file = this._thumbnailQueue.shift();\n\n      return this.createThumbnail(file, this.options.thumbnailWidth, this.options.thumbnailHeight, this.options.thumbnailMethod, true, function (dataUrl) {\n        _this9.emit(\"thumbnail\", file, dataUrl);\n\n        _this9._processingThumbnail = false;\n        return _this9._processThumbnailQueue();\n      });\n    } // Can be called by the user to remove a file\n\n  }, {\n    key: \"removeFile\",\n    value: function removeFile(file) {\n      if (file.status === Dropzone.UPLOADING) {\n        this.cancelUpload(file);\n      }\n\n      this.files = without(this.files, file);\n      this.emit(\"removedfile\", file);\n\n      if (this.files.length === 0) {\n        return this.emit(\"reset\");\n      }\n    } // Removes all files that aren't currently processed from the list\n\n  }, {\n    key: \"removeAllFiles\",\n    value: function removeAllFiles(cancelIfNecessary) {\n      // Create a copy of files since removeFile() changes the @files array.\n      if (cancelIfNecessary == null) {\n        cancelIfNecessary = false;\n      }\n\n      var _iterator9 = dropzone_createForOfIteratorHelper(this.files.slice(), true),\n          _step9;\n\n      try {\n        for (_iterator9.s(); !(_step9 = _iterator9.n()).done;) {\n          var file = _step9.value;\n\n          if (file.status !== Dropzone.UPLOADING || cancelIfNecessary) {\n            this.removeFile(file);\n          }\n        }\n      } catch (err) {\n        _iterator9.e(err);\n      } finally {\n        _iterator9.f();\n      }\n\n      return null;\n    } // Resizes an image before it gets sent to the server. This function is the default behavior of\n    // `options.transformFile` if `resizeWidth` or `resizeHeight` are set. The callback is invoked with\n    // the resized blob.\n\n  }, {\n    key: \"resizeImage\",\n    value: function resizeImage(file, width, height, resizeMethod, callback) {\n      var _this10 = this;\n\n      return this.createThumbnail(file, width, height, resizeMethod, true, function (dataUrl, canvas) {\n        if (canvas == null) {\n          // The image has not been resized\n          return callback(file);\n        } else {\n          var resizeMimeType = _this10.options.resizeMimeType;\n\n          if (resizeMimeType == null) {\n            resizeMimeType = file.type;\n          }\n\n          var resizedDataURL = canvas.toDataURL(resizeMimeType, _this10.options.resizeQuality);\n\n          if (resizeMimeType === \"image/jpeg\" || resizeMimeType === \"image/jpg\") {\n            // Now add the original EXIF information\n            resizedDataURL = ExifRestore.restore(file.dataURL, resizedDataURL);\n          }\n\n          return callback(Dropzone.dataURItoBlob(resizedDataURL));\n        }\n      });\n    }\n  }, {\n    key: \"createThumbnail\",\n    value: function createThumbnail(file, width, height, resizeMethod, fixOrientation, callback) {\n      var _this11 = this;\n\n      var fileReader = new FileReader();\n\n      fileReader.onload = function () {\n        file.dataURL = fileReader.result; // Don't bother creating a thumbnail for SVG images since they're vector\n\n        if (file.type === \"image/svg+xml\") {\n          if (callback != null) {\n            callback(fileReader.result);\n          }\n\n          return;\n        }\n\n        _this11.createThumbnailFromUrl(file, width, height, resizeMethod, fixOrientation, callback);\n      };\n\n      fileReader.readAsDataURL(file);\n    } // `mockFile` needs to have these attributes:\n    //\n    //     { name: 'name', size: 12345, imageUrl: '' }\n    //\n    // `callback` will be invoked when the image has been downloaded and displayed.\n    // `crossOrigin` will be added to the `img` tag when accessing the file.\n\n  }, {\n    key: \"displayExistingFile\",\n    value: function displayExistingFile(mockFile, imageUrl, callback, crossOrigin) {\n      var _this12 = this;\n\n      var resizeThumbnail = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n      this.emit(\"addedfile\", mockFile);\n      this.emit(\"complete\", mockFile);\n\n      if (!resizeThumbnail) {\n        this.emit(\"thumbnail\", mockFile, imageUrl);\n        if (callback) callback();\n      } else {\n        var onDone = function onDone(thumbnail) {\n          _this12.emit(\"thumbnail\", mockFile, thumbnail);\n\n          if (callback) callback();\n        };\n\n        mockFile.dataURL = imageUrl;\n        this.createThumbnailFromUrl(mockFile, this.options.thumbnailWidth, this.options.thumbnailHeight, this.options.thumbnailMethod, this.options.fixOrientation, onDone, crossOrigin);\n      }\n    }\n  }, {\n    key: \"createThumbnailFromUrl\",\n    value: function createThumbnailFromUrl(file, width, height, resizeMethod, fixOrientation, callback, crossOrigin) {\n      var _this13 = this;\n\n      // Not using `new Image` here because of a bug in latest Chrome versions.\n      // See https://github.com/enyo/dropzone/pull/226\n      var img = document.createElement(\"img\");\n\n      if (crossOrigin) {\n        img.crossOrigin = crossOrigin;\n      } // fixOrientation is not needed anymore with browsers handling imageOrientation\n\n\n      fixOrientation = getComputedStyle(document.body)[\"imageOrientation\"] == \"from-image\" ? false : fixOrientation;\n\n      img.onload = function () {\n        var loadExif = function loadExif(callback) {\n          return callback(1);\n        };\n\n        if (typeof EXIF !== \"undefined\" && EXIF !== null && fixOrientation) {\n          loadExif = function loadExif(callback) {\n            return EXIF.getData(img, function () {\n              return callback(EXIF.getTag(this, \"Orientation\"));\n            });\n          };\n        }\n\n        return loadExif(function (orientation) {\n          file.width = img.width;\n          file.height = img.height;\n\n          var resizeInfo = _this13.options.resize.call(_this13, file, width, height, resizeMethod);\n\n          var canvas = document.createElement(\"canvas\");\n          var ctx = canvas.getContext(\"2d\");\n          canvas.width = resizeInfo.trgWidth;\n          canvas.height = resizeInfo.trgHeight;\n\n          if (orientation > 4) {\n            canvas.width = resizeInfo.trgHeight;\n            canvas.height = resizeInfo.trgWidth;\n          }\n\n          switch (orientation) {\n            case 2:\n              // horizontal flip\n              ctx.translate(canvas.width, 0);\n              ctx.scale(-1, 1);\n              break;\n\n            case 3:\n              // 180° rotate left\n              ctx.translate(canvas.width, canvas.height);\n              ctx.rotate(Math.PI);\n              break;\n\n            case 4:\n              // vertical flip\n              ctx.translate(0, canvas.height);\n              ctx.scale(1, -1);\n              break;\n\n            case 5:\n              // vertical flip + 90 rotate right\n              ctx.rotate(0.5 * Math.PI);\n              ctx.scale(1, -1);\n              break;\n\n            case 6:\n              // 90° rotate right\n              ctx.rotate(0.5 * Math.PI);\n              ctx.translate(0, -canvas.width);\n              break;\n\n            case 7:\n              // horizontal flip + 90 rotate right\n              ctx.rotate(0.5 * Math.PI);\n              ctx.translate(canvas.height, -canvas.width);\n              ctx.scale(-1, 1);\n              break;\n\n            case 8:\n              // 90° rotate left\n              ctx.rotate(-0.5 * Math.PI);\n              ctx.translate(-canvas.height, 0);\n              break;\n          } // This is a bugfix for iOS' scaling bug.\n\n\n          drawImageIOSFix(ctx, img, resizeInfo.srcX != null ? resizeInfo.srcX : 0, resizeInfo.srcY != null ? resizeInfo.srcY : 0, resizeInfo.srcWidth, resizeInfo.srcHeight, resizeInfo.trgX != null ? resizeInfo.trgX : 0, resizeInfo.trgY != null ? resizeInfo.trgY : 0, resizeInfo.trgWidth, resizeInfo.trgHeight);\n          var thumbnail = canvas.toDataURL(\"image/png\");\n\n          if (callback != null) {\n            return callback(thumbnail, canvas);\n          }\n        });\n      };\n\n      if (callback != null) {\n        img.onerror = callback;\n      }\n\n      return img.src = file.dataURL;\n    } // Goes through the queue and processes files if there aren't too many already.\n\n  }, {\n    key: \"processQueue\",\n    value: function processQueue() {\n      var parallelUploads = this.options.parallelUploads;\n      var processingLength = this.getUploadingFiles().length;\n      var i = processingLength; // There are already at least as many files uploading than should be\n\n      if (processingLength >= parallelUploads) {\n        return;\n      }\n\n      var queuedFiles = this.getQueuedFiles();\n\n      if (!(queuedFiles.length > 0)) {\n        return;\n      }\n\n      if (this.options.uploadMultiple) {\n        // The files should be uploaded in one request\n        return this.processFiles(queuedFiles.slice(0, parallelUploads - processingLength));\n      } else {\n        while (i < parallelUploads) {\n          if (!queuedFiles.length) {\n            return;\n          } // Nothing left to process\n\n\n          this.processFile(queuedFiles.shift());\n          i++;\n        }\n      }\n    } // Wrapper for `processFiles`\n\n  }, {\n    key: \"processFile\",\n    value: function processFile(file) {\n      return this.processFiles([file]);\n    } // Loads the file, then calls finishedLoading()\n\n  }, {\n    key: \"processFiles\",\n    value: function processFiles(files) {\n      var _iterator10 = dropzone_createForOfIteratorHelper(files, true),\n          _step10;\n\n      try {\n        for (_iterator10.s(); !(_step10 = _iterator10.n()).done;) {\n          var file = _step10.value;\n          file.processing = true; // Backwards compatibility\n\n          file.status = Dropzone.UPLOADING;\n          this.emit(\"processing\", file);\n        }\n      } catch (err) {\n        _iterator10.e(err);\n      } finally {\n        _iterator10.f();\n      }\n\n      if (this.options.uploadMultiple) {\n        this.emit(\"processingmultiple\", files);\n      }\n\n      return this.uploadFiles(files);\n    }\n  }, {\n    key: \"_getFilesWithXhr\",\n    value: function _getFilesWithXhr(xhr) {\n      var files;\n      return files = this.files.filter(function (file) {\n        return file.xhr === xhr;\n      }).map(function (file) {\n        return file;\n      });\n    } // Cancels the file upload and sets the status to CANCELED\n    // **if** the file is actually being uploaded.\n    // If it's still in the queue, the file is being removed from it and the status\n    // set to CANCELED.\n\n  }, {\n    key: \"cancelUpload\",\n    value: function cancelUpload(file) {\n      if (file.status === Dropzone.UPLOADING) {\n        var groupedFiles = this._getFilesWithXhr(file.xhr);\n\n        var _iterator11 = dropzone_createForOfIteratorHelper(groupedFiles, true),\n            _step11;\n\n        try {\n          for (_iterator11.s(); !(_step11 = _iterator11.n()).done;) {\n            var groupedFile = _step11.value;\n            groupedFile.status = Dropzone.CANCELED;\n          }\n        } catch (err) {\n          _iterator11.e(err);\n        } finally {\n          _iterator11.f();\n        }\n\n        if (typeof file.xhr !== \"undefined\") {\n          file.xhr.abort();\n        }\n\n        var _iterator12 = dropzone_createForOfIteratorHelper(groupedFiles, true),\n            _step12;\n\n        try {\n          for (_iterator12.s(); !(_step12 = _iterator12.n()).done;) {\n            var _groupedFile = _step12.value;\n            this.emit(\"canceled\", _groupedFile);\n          }\n        } catch (err) {\n          _iterator12.e(err);\n        } finally {\n          _iterator12.f();\n        }\n\n        if (this.options.uploadMultiple) {\n          this.emit(\"canceledmultiple\", groupedFiles);\n        }\n      } else if (file.status === Dropzone.ADDED || file.status === Dropzone.QUEUED) {\n        file.status = Dropzone.CANCELED;\n        this.emit(\"canceled\", file);\n\n        if (this.options.uploadMultiple) {\n          this.emit(\"canceledmultiple\", [file]);\n        }\n      }\n\n      if (this.options.autoProcessQueue) {\n        return this.processQueue();\n      }\n    }\n  }, {\n    key: \"resolveOption\",\n    value: function resolveOption(option) {\n      if (typeof option === \"function\") {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n\n        return option.apply(this, args);\n      }\n\n      return option;\n    }\n  }, {\n    key: \"uploadFile\",\n    value: function uploadFile(file) {\n      return this.uploadFiles([file]);\n    }\n  }, {\n    key: \"uploadFiles\",\n    value: function uploadFiles(files) {\n      var _this14 = this;\n\n      this._transformFiles(files, function (transformedFiles) {\n        if (_this14.options.chunking) {\n          // Chunking is not allowed to be used with `uploadMultiple` so we know\n          // that there is only __one__file.\n          var transformedFile = transformedFiles[0];\n          files[0].upload.chunked = _this14.options.chunking && (_this14.options.forceChunking || transformedFile.size > _this14.options.chunkSize);\n          files[0].upload.totalChunkCount = Math.ceil(transformedFile.size / _this14.options.chunkSize);\n        }\n\n        if (files[0].upload.chunked) {\n          // This file should be sent in chunks!\n          // If the chunking option is set, we **know** that there can only be **one** file, since\n          // uploadMultiple is not allowed with this option.\n          var file = files[0];\n          var _transformedFile = transformedFiles[0];\n          var startedChunkCount = 0;\n          file.upload.chunks = [];\n\n          var handleNextChunk = function handleNextChunk() {\n            var chunkIndex = 0; // Find the next item in file.upload.chunks that is not defined yet.\n\n            while (file.upload.chunks[chunkIndex] !== undefined) {\n              chunkIndex++;\n            } // This means, that all chunks have already been started.\n\n\n            if (chunkIndex >= file.upload.totalChunkCount) return;\n            startedChunkCount++;\n            var start = chunkIndex * _this14.options.chunkSize;\n            var end = Math.min(start + _this14.options.chunkSize, _transformedFile.size);\n            var dataBlock = {\n              name: _this14._getParamName(0),\n              data: _transformedFile.webkitSlice ? _transformedFile.webkitSlice(start, end) : _transformedFile.slice(start, end),\n              filename: file.upload.filename,\n              chunkIndex: chunkIndex\n            };\n            file.upload.chunks[chunkIndex] = {\n              file: file,\n              index: chunkIndex,\n              dataBlock: dataBlock,\n              // In case we want to retry.\n              status: Dropzone.UPLOADING,\n              progress: 0,\n              retries: 0 // The number of times this block has been retried.\n\n            };\n\n            _this14._uploadData(files, [dataBlock]);\n          };\n\n          file.upload.finishedChunkUpload = function (chunk, response) {\n            var allFinished = true;\n            chunk.status = Dropzone.SUCCESS; // Clear the data from the chunk\n\n            chunk.dataBlock = null; // Leaving this reference to xhr intact here will cause memory leaks in some browsers\n\n            chunk.xhr = null;\n\n            for (var i = 0; i < file.upload.totalChunkCount; i++) {\n              if (file.upload.chunks[i] === undefined) {\n                return handleNextChunk();\n              }\n\n              if (file.upload.chunks[i].status !== Dropzone.SUCCESS) {\n                allFinished = false;\n              }\n            }\n\n            if (allFinished) {\n              _this14.options.chunksUploaded(file, function () {\n                _this14._finished(files, response, null);\n              });\n            }\n          };\n\n          if (_this14.options.parallelChunkUploads) {\n            for (var i = 0; i < file.upload.totalChunkCount; i++) {\n              handleNextChunk();\n            }\n          } else {\n            handleNextChunk();\n          }\n        } else {\n          var dataBlocks = [];\n\n          for (var _i2 = 0; _i2 < files.length; _i2++) {\n            dataBlocks[_i2] = {\n              name: _this14._getParamName(_i2),\n              data: transformedFiles[_i2],\n              filename: files[_i2].upload.filename\n            };\n          }\n\n          _this14._uploadData(files, dataBlocks);\n        }\n      });\n    } /// Returns the right chunk for given file and xhr\n\n  }, {\n    key: \"_getChunk\",\n    value: function _getChunk(file, xhr) {\n      for (var i = 0; i < file.upload.totalChunkCount; i++) {\n        if (file.upload.chunks[i] !== undefined && file.upload.chunks[i].xhr === xhr) {\n          return file.upload.chunks[i];\n        }\n      }\n    } // This function actually uploads the file(s) to the server.\n    // If dataBlocks contains the actual data to upload (meaning, that this could either be transformed\n    // files, or individual chunks for chunked upload).\n\n  }, {\n    key: \"_uploadData\",\n    value: function _uploadData(files, dataBlocks) {\n      var _this15 = this;\n\n      var xhr = new XMLHttpRequest(); // Put the xhr object in the file objects to be able to reference it later.\n\n      var _iterator13 = dropzone_createForOfIteratorHelper(files, true),\n          _step13;\n\n      try {\n        for (_iterator13.s(); !(_step13 = _iterator13.n()).done;) {\n          var file = _step13.value;\n          file.xhr = xhr;\n        }\n      } catch (err) {\n        _iterator13.e(err);\n      } finally {\n        _iterator13.f();\n      }\n\n      if (files[0].upload.chunked) {\n        // Put the xhr object in the right chunk object, so it can be associated later, and found with _getChunk\n        files[0].upload.chunks[dataBlocks[0].chunkIndex].xhr = xhr;\n      }\n\n      var method = this.resolveOption(this.options.method, files);\n      var url = this.resolveOption(this.options.url, files);\n      xhr.open(method, url, true); // Setting the timeout after open because of IE11 issue: https://gitlab.com/meno/dropzone/issues/8\n\n      var timeout = this.resolveOption(this.options.timeout, files);\n      if (timeout) xhr.timeout = this.resolveOption(this.options.timeout, files); // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n\n      xhr.withCredentials = !!this.options.withCredentials;\n\n      xhr.onload = function (e) {\n        _this15._finishedUploading(files, xhr, e);\n      };\n\n      xhr.ontimeout = function () {\n        _this15._handleUploadError(files, xhr, \"Request timedout after \".concat(_this15.options.timeout / 1000, \" seconds\"));\n      };\n\n      xhr.onerror = function () {\n        _this15._handleUploadError(files, xhr);\n      }; // Some browsers do not have the .upload property\n\n\n      var progressObj = xhr.upload != null ? xhr.upload : xhr;\n\n      progressObj.onprogress = function (e) {\n        return _this15._updateFilesUploadProgress(files, xhr, e);\n      };\n\n      var headers = {\n        Accept: \"application/json\",\n        \"Cache-Control\": \"no-cache\",\n        \"X-Requested-With\": \"XMLHttpRequest\"\n      };\n\n      if (this.options.headers) {\n        Dropzone.extend(headers, this.options.headers);\n      }\n\n      for (var headerName in headers) {\n        var headerValue = headers[headerName];\n\n        if (headerValue) {\n          xhr.setRequestHeader(headerName, headerValue);\n        }\n      }\n\n      var formData = new FormData(); // Adding all @options parameters\n\n      if (this.options.params) {\n        var additionalParams = this.options.params;\n\n        if (typeof additionalParams === \"function\") {\n          additionalParams = additionalParams.call(this, files, xhr, files[0].upload.chunked ? this._getChunk(files[0], xhr) : null);\n        }\n\n        for (var key in additionalParams) {\n          var value = additionalParams[key];\n\n          if (Array.isArray(value)) {\n            // The additional parameter contains an array,\n            // so lets iterate over it to attach each value\n            // individually.\n            for (var i = 0; i < value.length; i++) {\n              formData.append(key, value[i]);\n            }\n          } else {\n            formData.append(key, value);\n          }\n        }\n      } // Let the user add additional data if necessary\n\n\n      var _iterator14 = dropzone_createForOfIteratorHelper(files, true),\n          _step14;\n\n      try {\n        for (_iterator14.s(); !(_step14 = _iterator14.n()).done;) {\n          var _file = _step14.value;\n          this.emit(\"sending\", _file, xhr, formData);\n        }\n      } catch (err) {\n        _iterator14.e(err);\n      } finally {\n        _iterator14.f();\n      }\n\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr, formData);\n      }\n\n      this._addFormElementData(formData); // Finally add the files\n      // Has to be last because some servers (eg: S3) expect the file to be the last parameter\n\n\n      for (var _i3 = 0; _i3 < dataBlocks.length; _i3++) {\n        var dataBlock = dataBlocks[_i3];\n        formData.append(dataBlock.name, dataBlock.data, dataBlock.filename);\n      }\n\n      this.submitRequest(xhr, formData, files);\n    } // Transforms all files with this.options.transformFile and invokes done with the transformed files when done.\n\n  }, {\n    key: \"_transformFiles\",\n    value: function _transformFiles(files, done) {\n      var _this16 = this;\n\n      var transformedFiles = []; // Clumsy way of handling asynchronous calls, until I get to add a proper Future library.\n\n      var doneCounter = 0;\n\n      var _loop = function _loop(i) {\n        _this16.options.transformFile.call(_this16, files[i], function (transformedFile) {\n          transformedFiles[i] = transformedFile;\n\n          if (++doneCounter === files.length) {\n            done(transformedFiles);\n          }\n        });\n      };\n\n      for (var i = 0; i < files.length; i++) {\n        _loop(i);\n      }\n    } // Takes care of adding other input elements of the form to the AJAX request\n\n  }, {\n    key: \"_addFormElementData\",\n    value: function _addFormElementData(formData) {\n      // Take care of other input elements\n      if (this.element.tagName === \"FORM\") {\n        var _iterator15 = dropzone_createForOfIteratorHelper(this.element.querySelectorAll(\"input, textarea, select, button\"), true),\n            _step15;\n\n        try {\n          for (_iterator15.s(); !(_step15 = _iterator15.n()).done;) {\n            var input = _step15.value;\n            var inputName = input.getAttribute(\"name\");\n            var inputType = input.getAttribute(\"type\");\n            if (inputType) inputType = inputType.toLowerCase(); // If the input doesn't have a name, we can't use it.\n\n            if (typeof inputName === \"undefined\" || inputName === null) continue;\n\n            if (input.tagName === \"SELECT\" && input.hasAttribute(\"multiple\")) {\n              // Possibly multiple values\n              var _iterator16 = dropzone_createForOfIteratorHelper(input.options, true),\n                  _step16;\n\n              try {\n                for (_iterator16.s(); !(_step16 = _iterator16.n()).done;) {\n                  var option = _step16.value;\n\n                  if (option.selected) {\n                    formData.append(inputName, option.value);\n                  }\n                }\n              } catch (err) {\n                _iterator16.e(err);\n              } finally {\n                _iterator16.f();\n              }\n            } else if (!inputType || inputType !== \"checkbox\" && inputType !== \"radio\" || input.checked) {\n              formData.append(inputName, input.value);\n            }\n          }\n        } catch (err) {\n          _iterator15.e(err);\n        } finally {\n          _iterator15.f();\n        }\n      }\n    } // Invoked when there is new progress information about given files.\n    // If e is not provided, it is assumed that the upload is finished.\n\n  }, {\n    key: \"_updateFilesUploadProgress\",\n    value: function _updateFilesUploadProgress(files, xhr, e) {\n      if (!files[0].upload.chunked) {\n        // Handle file uploads without chunking\n        var _iterator17 = dropzone_createForOfIteratorHelper(files, true),\n            _step17;\n\n        try {\n          for (_iterator17.s(); !(_step17 = _iterator17.n()).done;) {\n            var file = _step17.value;\n\n            if (file.upload.total && file.upload.bytesSent && file.upload.bytesSent == file.upload.total) {\n              // If both, the `total` and `bytesSent` have already been set, and\n              // they are equal (meaning progress is at 100%), we can skip this\n              // file, since an upload progress shouldn't go down.\n              continue;\n            }\n\n            if (e) {\n              file.upload.progress = 100 * e.loaded / e.total;\n              file.upload.total = e.total;\n              file.upload.bytesSent = e.loaded;\n            } else {\n              // No event, so we're at 100%\n              file.upload.progress = 100;\n              file.upload.bytesSent = file.upload.total;\n            }\n\n            this.emit(\"uploadprogress\", file, file.upload.progress, file.upload.bytesSent);\n          }\n        } catch (err) {\n          _iterator17.e(err);\n        } finally {\n          _iterator17.f();\n        }\n      } else {\n        // Handle chunked file uploads\n        // Chunked upload is not compatible with uploading multiple files in one\n        // request, so we know there's only one file.\n        var _file2 = files[0]; // Since this is a chunked upload, we need to update the appropriate chunk\n        // progress.\n\n        var chunk = this._getChunk(_file2, xhr);\n\n        if (e) {\n          chunk.progress = 100 * e.loaded / e.total;\n          chunk.total = e.total;\n          chunk.bytesSent = e.loaded;\n        } else {\n          // No event, so we're at 100%\n          chunk.progress = 100;\n          chunk.bytesSent = chunk.total;\n        } // Now tally the *file* upload progress from its individual chunks\n\n\n        _file2.upload.progress = 0;\n        _file2.upload.total = 0;\n        _file2.upload.bytesSent = 0;\n\n        for (var i = 0; i < _file2.upload.totalChunkCount; i++) {\n          if (_file2.upload.chunks[i] && typeof _file2.upload.chunks[i].progress !== \"undefined\") {\n            _file2.upload.progress += _file2.upload.chunks[i].progress;\n            _file2.upload.total += _file2.upload.chunks[i].total;\n            _file2.upload.bytesSent += _file2.upload.chunks[i].bytesSent;\n          }\n        } // Since the process is a percentage, we need to divide by the amount of\n        // chunks we've used.\n\n\n        _file2.upload.progress = _file2.upload.progress / _file2.upload.totalChunkCount;\n        this.emit(\"uploadprogress\", _file2, _file2.upload.progress, _file2.upload.bytesSent);\n      }\n    }\n  }, {\n    key: \"_finishedUploading\",\n    value: function _finishedUploading(files, xhr, e) {\n      var response;\n\n      if (files[0].status === Dropzone.CANCELED) {\n        return;\n      }\n\n      if (xhr.readyState !== 4) {\n        return;\n      }\n\n      if (xhr.responseType !== \"arraybuffer\" && xhr.responseType !== \"blob\") {\n        response = xhr.responseText;\n\n        if (xhr.getResponseHeader(\"content-type\") && ~xhr.getResponseHeader(\"content-type\").indexOf(\"application/json\")) {\n          try {\n            response = JSON.parse(response);\n          } catch (error) {\n            e = error;\n            response = \"Invalid JSON response from server.\";\n          }\n        }\n      }\n\n      this._updateFilesUploadProgress(files, xhr);\n\n      if (!(200 <= xhr.status && xhr.status < 300)) {\n        this._handleUploadError(files, xhr, response);\n      } else {\n        if (files[0].upload.chunked) {\n          files[0].upload.finishedChunkUpload(this._getChunk(files[0], xhr), response);\n        } else {\n          this._finished(files, response, e);\n        }\n      }\n    }\n  }, {\n    key: \"_handleUploadError\",\n    value: function _handleUploadError(files, xhr, response) {\n      if (files[0].status === Dropzone.CANCELED) {\n        return;\n      }\n\n      if (files[0].upload.chunked && this.options.retryChunks) {\n        var chunk = this._getChunk(files[0], xhr);\n\n        if (chunk.retries++ < this.options.retryChunksLimit) {\n          this._uploadData(files, [chunk.dataBlock]);\n\n          return;\n        } else {\n          console.warn(\"Retried this chunk too often. Giving up.\");\n        }\n      }\n\n      this._errorProcessing(files, response || this.options.dictResponseError.replace(\"{{statusCode}}\", xhr.status), xhr);\n    }\n  }, {\n    key: \"submitRequest\",\n    value: function submitRequest(xhr, formData, files) {\n      if (xhr.readyState != 1) {\n        console.warn(\"Cannot send this request because the XMLHttpRequest.readyState is not OPENED.\");\n        return;\n      }\n\n      xhr.send(formData);\n    } // Called internally when processing is finished.\n    // Individual callbacks have to be called in the appropriate sections.\n\n  }, {\n    key: \"_finished\",\n    value: function _finished(files, responseText, e) {\n      var _iterator18 = dropzone_createForOfIteratorHelper(files, true),\n          _step18;\n\n      try {\n        for (_iterator18.s(); !(_step18 = _iterator18.n()).done;) {\n          var file = _step18.value;\n          file.status = Dropzone.SUCCESS;\n          this.emit(\"success\", file, responseText, e);\n          this.emit(\"complete\", file);\n        }\n      } catch (err) {\n        _iterator18.e(err);\n      } finally {\n        _iterator18.f();\n      }\n\n      if (this.options.uploadMultiple) {\n        this.emit(\"successmultiple\", files, responseText, e);\n        this.emit(\"completemultiple\", files);\n      }\n\n      if (this.options.autoProcessQueue) {\n        return this.processQueue();\n      }\n    } // Called internally when processing is finished.\n    // Individual callbacks have to be called in the appropriate sections.\n\n  }, {\n    key: \"_errorProcessing\",\n    value: function _errorProcessing(files, message, xhr) {\n      var _iterator19 = dropzone_createForOfIteratorHelper(files, true),\n          _step19;\n\n      try {\n        for (_iterator19.s(); !(_step19 = _iterator19.n()).done;) {\n          var file = _step19.value;\n          file.status = Dropzone.ERROR;\n          this.emit(\"error\", file, message, xhr);\n          this.emit(\"complete\", file);\n        }\n      } catch (err) {\n        _iterator19.e(err);\n      } finally {\n        _iterator19.f();\n      }\n\n      if (this.options.uploadMultiple) {\n        this.emit(\"errormultiple\", files, message, xhr);\n        this.emit(\"completemultiple\", files);\n      }\n\n      if (this.options.autoProcessQueue) {\n        return this.processQueue();\n      }\n    }\n  }], [{\n    key: \"initClass\",\n    value: function initClass() {\n      // Exposing the emitter class, mainly for tests\n      this.prototype.Emitter = Emitter;\n      /*\n       This is a list of all available events you can register on a dropzone object.\n        You can register an event handler like this:\n        dropzone.on(\"dragEnter\", function() { });\n        */\n\n      this.prototype.events = [\"drop\", \"dragstart\", \"dragend\", \"dragenter\", \"dragover\", \"dragleave\", \"addedfile\", \"addedfiles\", \"removedfile\", \"thumbnail\", \"error\", \"errormultiple\", \"processing\", \"processingmultiple\", \"uploadprogress\", \"totaluploadprogress\", \"sending\", \"sendingmultiple\", \"success\", \"successmultiple\", \"canceled\", \"canceledmultiple\", \"complete\", \"completemultiple\", \"reset\", \"maxfilesexceeded\", \"maxfilesreached\", \"queuecomplete\"];\n      this.prototype._thumbnailQueue = [];\n      this.prototype._processingThumbnail = false;\n    } // global utility\n\n  }, {\n    key: \"extend\",\n    value: function extend(target) {\n      for (var _len2 = arguments.length, objects = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        objects[_key2 - 1] = arguments[_key2];\n      }\n\n      for (var _i4 = 0, _objects = objects; _i4 < _objects.length; _i4++) {\n        var object = _objects[_i4];\n\n        for (var key in object) {\n          var val = object[key];\n          target[key] = val;\n        }\n      }\n\n      return target;\n    }\n  }, {\n    key: \"uuidv4\",\n    value: function uuidv4() {\n      return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n            v = c === \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    }\n  }]);\n\n  return Dropzone;\n}(Emitter);\n\n\nDropzone.initClass();\nDropzone.version = \"5.9.3\"; // This is a map of options for your different dropzones. Add configurations\n// to this object for your different dropzone elemens.\n//\n// Example:\n//\n//     Dropzone.options.myDropzoneElementId = { maxFilesize: 1 };\n//\n// To disable autoDiscover for a specific element, you can set `false` as an option:\n//\n//     Dropzone.options.myDisabledElementId = false;\n//\n// And in html:\n//\n//     <form action=\"/upload\" id=\"my-dropzone-element-id\" class=\"dropzone\"></form>\n\nDropzone.options = {}; // Returns the options for an element or undefined if none available.\n\nDropzone.optionsForElement = function (element) {\n  // Get the `Dropzone.options.elementId` for this element if it exists\n  if (element.getAttribute(\"id\")) {\n    return Dropzone.options[camelize(element.getAttribute(\"id\"))];\n  } else {\n    return undefined;\n  }\n}; // Holds a list of all dropzone instances\n\n\nDropzone.instances = []; // Returns the dropzone for given element if any\n\nDropzone.forElement = function (element) {\n  if (typeof element === \"string\") {\n    element = document.querySelector(element);\n  }\n\n  if ((element != null ? element.dropzone : undefined) == null) {\n    throw new Error(\"No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.\");\n  }\n\n  return element.dropzone;\n}; // Set to false if you don't want Dropzone to automatically find and attach to .dropzone elements.\n\n\nDropzone.autoDiscover = true; // Looks for all .dropzone elements and creates a dropzone for them\n\nDropzone.discover = function () {\n  var dropzones;\n\n  if (document.querySelectorAll) {\n    dropzones = document.querySelectorAll(\".dropzone\");\n  } else {\n    dropzones = []; // IE :(\n\n    var checkElements = function checkElements(elements) {\n      return function () {\n        var result = [];\n\n        var _iterator20 = dropzone_createForOfIteratorHelper(elements, true),\n            _step20;\n\n        try {\n          for (_iterator20.s(); !(_step20 = _iterator20.n()).done;) {\n            var el = _step20.value;\n\n            if (/(^| )dropzone($| )/.test(el.className)) {\n              result.push(dropzones.push(el));\n            } else {\n              result.push(undefined);\n            }\n          }\n        } catch (err) {\n          _iterator20.e(err);\n        } finally {\n          _iterator20.f();\n        }\n\n        return result;\n      }();\n    };\n\n    checkElements(document.getElementsByTagName(\"div\"));\n    checkElements(document.getElementsByTagName(\"form\"));\n  }\n\n  return function () {\n    var result = [];\n\n    var _iterator21 = dropzone_createForOfIteratorHelper(dropzones, true),\n        _step21;\n\n    try {\n      for (_iterator21.s(); !(_step21 = _iterator21.n()).done;) {\n        var dropzone = _step21.value;\n\n        // Create a dropzone unless auto discover has been disabled for specific element\n        if (Dropzone.optionsForElement(dropzone) !== false) {\n          result.push(new Dropzone(dropzone));\n        } else {\n          result.push(undefined);\n        }\n      }\n    } catch (err) {\n      _iterator21.e(err);\n    } finally {\n      _iterator21.f();\n    }\n\n    return result;\n  }();\n}; // Some browsers support drag and drog functionality, but not correctly.\n//\n// So I created a blocklist of userAgents. Yes, yes. Browser sniffing, I know.\n// But what to do when browsers *theoretically* support an API, but crash\n// when using it.\n//\n// This is a list of regular expressions tested against navigator.userAgent\n//\n// ** It should only be used on browser that *do* support the API, but\n// incorrectly **\n\n\nDropzone.blockedBrowsers = [// The mac os and windows phone version of opera 12 seems to have a problem with the File drag'n'drop API.\n/opera.*(Macintosh|Windows Phone).*version\\/12/i]; // Checks if the browser is supported\n\nDropzone.isBrowserSupported = function () {\n  var capableBrowser = true;\n\n  if (window.File && window.FileReader && window.FileList && window.Blob && window.FormData && document.querySelector) {\n    if (!(\"classList\" in document.createElement(\"a\"))) {\n      capableBrowser = false;\n    } else {\n      if (Dropzone.blacklistedBrowsers !== undefined) {\n        // Since this has been renamed, this makes sure we don't break older\n        // configuration.\n        Dropzone.blockedBrowsers = Dropzone.blacklistedBrowsers;\n      } // The browser supports the API, but may be blocked.\n\n\n      var _iterator22 = dropzone_createForOfIteratorHelper(Dropzone.blockedBrowsers, true),\n          _step22;\n\n      try {\n        for (_iterator22.s(); !(_step22 = _iterator22.n()).done;) {\n          var regex = _step22.value;\n\n          if (regex.test(navigator.userAgent)) {\n            capableBrowser = false;\n            continue;\n          }\n        }\n      } catch (err) {\n        _iterator22.e(err);\n      } finally {\n        _iterator22.f();\n      }\n    }\n  } else {\n    capableBrowser = false;\n  }\n\n  return capableBrowser;\n};\n\nDropzone.dataURItoBlob = function (dataURI) {\n  // convert base64 to raw binary data held in a string\n  // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this\n  var byteString = atob(dataURI.split(\",\")[1]); // separate out the mime component\n\n  var mimeString = dataURI.split(\",\")[0].split(\":\")[1].split(\";\")[0]; // write the bytes of the string to an ArrayBuffer\n\n  var ab = new ArrayBuffer(byteString.length);\n  var ia = new Uint8Array(ab);\n\n  for (var i = 0, end = byteString.length, asc = 0 <= end; asc ? i <= end : i >= end; asc ? i++ : i--) {\n    ia[i] = byteString.charCodeAt(i);\n  } // write the ArrayBuffer to a blob\n\n\n  return new Blob([ab], {\n    type: mimeString\n  });\n}; // Returns an array without the rejected item\n\n\nvar without = function without(list, rejectedItem) {\n  return list.filter(function (item) {\n    return item !== rejectedItem;\n  }).map(function (item) {\n    return item;\n  });\n}; // abc-def_ghi -> abcDefGhi\n\n\nvar camelize = function camelize(str) {\n  return str.replace(/[\\-_](\\w)/g, function (match) {\n    return match.charAt(1).toUpperCase();\n  });\n}; // Creates an element from string\n\n\nDropzone.createElement = function (string) {\n  var div = document.createElement(\"div\");\n  div.innerHTML = string;\n  return div.childNodes[0];\n}; // Tests if given element is inside (or simply is) the container\n\n\nDropzone.elementInside = function (element, container) {\n  if (element === container) {\n    return true;\n  } // Coffeescript doesn't support do/while loops\n\n\n  while (element = element.parentNode) {\n    if (element === container) {\n      return true;\n    }\n  }\n\n  return false;\n};\n\nDropzone.getElement = function (el, name) {\n  var element;\n\n  if (typeof el === \"string\") {\n    element = document.querySelector(el);\n  } else if (el.nodeType != null) {\n    element = el;\n  }\n\n  if (element == null) {\n    throw new Error(\"Invalid `\".concat(name, \"` option provided. Please provide a CSS selector or a plain HTML element.\"));\n  }\n\n  return element;\n};\n\nDropzone.getElements = function (els, name) {\n  var el, elements;\n\n  if (els instanceof Array) {\n    elements = [];\n\n    try {\n      var _iterator23 = dropzone_createForOfIteratorHelper(els, true),\n          _step23;\n\n      try {\n        for (_iterator23.s(); !(_step23 = _iterator23.n()).done;) {\n          el = _step23.value;\n          elements.push(this.getElement(el, name));\n        }\n      } catch (err) {\n        _iterator23.e(err);\n      } finally {\n        _iterator23.f();\n      }\n    } catch (e) {\n      elements = null;\n    }\n  } else if (typeof els === \"string\") {\n    elements = [];\n\n    var _iterator24 = dropzone_createForOfIteratorHelper(document.querySelectorAll(els), true),\n        _step24;\n\n    try {\n      for (_iterator24.s(); !(_step24 = _iterator24.n()).done;) {\n        el = _step24.value;\n        elements.push(el);\n      }\n    } catch (err) {\n      _iterator24.e(err);\n    } finally {\n      _iterator24.f();\n    }\n  } else if (els.nodeType != null) {\n    elements = [els];\n  }\n\n  if (elements == null || !elements.length) {\n    throw new Error(\"Invalid `\".concat(name, \"` option provided. Please provide a CSS selector, a plain HTML element or a list of those.\"));\n  }\n\n  return elements;\n}; // Asks the user the question and calls accepted or rejected accordingly\n//\n// The default implementation just uses `window.confirm` and then calls the\n// appropriate callback.\n\n\nDropzone.confirm = function (question, accepted, rejected) {\n  if (window.confirm(question)) {\n    return accepted();\n  } else if (rejected != null) {\n    return rejected();\n  }\n}; // Validates the mime type like this:\n//\n// https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept\n\n\nDropzone.isValidFile = function (file, acceptedFiles) {\n  if (!acceptedFiles) {\n    return true;\n  } // If there are no accepted mime types, it's OK\n\n\n  acceptedFiles = acceptedFiles.split(\",\");\n  var mimeType = file.type;\n  var baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n\n  var _iterator25 = dropzone_createForOfIteratorHelper(acceptedFiles, true),\n      _step25;\n\n  try {\n    for (_iterator25.s(); !(_step25 = _iterator25.n()).done;) {\n      var validType = _step25.value;\n      validType = validType.trim();\n\n      if (validType.charAt(0) === \".\") {\n        if (file.name.toLowerCase().indexOf(validType.toLowerCase(), file.name.length - validType.length) !== -1) {\n          return true;\n        }\n      } else if (/\\/\\*$/.test(validType)) {\n        // This is something like a image/* mime type\n        if (baseMimeType === validType.replace(/\\/.*$/, \"\")) {\n          return true;\n        }\n      } else {\n        if (mimeType === validType) {\n          return true;\n        }\n      }\n    }\n  } catch (err) {\n    _iterator25.e(err);\n  } finally {\n    _iterator25.f();\n  }\n\n  return false;\n}; // Augment jQuery\n\n\nif (typeof jQuery !== \"undefined\" && jQuery !== null) {\n  jQuery.fn.dropzone = function (options) {\n    return this.each(function () {\n      return new Dropzone(this, options);\n    });\n  };\n} // Dropzone file status codes\n\n\nDropzone.ADDED = \"added\";\nDropzone.QUEUED = \"queued\"; // For backwards compatibility. Now, if a file is accepted, it's either queued\n// or uploading.\n\nDropzone.ACCEPTED = Dropzone.QUEUED;\nDropzone.UPLOADING = \"uploading\";\nDropzone.PROCESSING = Dropzone.UPLOADING; // alias\n\nDropzone.CANCELED = \"canceled\";\nDropzone.ERROR = \"error\";\nDropzone.SUCCESS = \"success\";\n/*\n\n Bugfix for iOS 6 and 7\n Source: http://stackoverflow.com/questions/11929099/html5-canvas-drawimage-ratio-bug-ios\n based on the work of https://github.com/stomita/ios-imagefile-megapixel\n\n */\n// Detecting vertical squash in loaded image.\n// Fixes a bug which squash image vertically while drawing into canvas for some images.\n// This is a bug in iOS6 devices. This function from https://github.com/stomita/ios-imagefile-megapixel\n\nvar detectVerticalSquash = function detectVerticalSquash(img) {\n  var iw = img.naturalWidth;\n  var ih = img.naturalHeight;\n  var canvas = document.createElement(\"canvas\");\n  canvas.width = 1;\n  canvas.height = ih;\n  var ctx = canvas.getContext(\"2d\");\n  ctx.drawImage(img, 0, 0);\n\n  var _ctx$getImageData = ctx.getImageData(1, 0, 1, ih),\n      data = _ctx$getImageData.data; // search image edge pixel position in case it is squashed vertically.\n\n\n  var sy = 0;\n  var ey = ih;\n  var py = ih;\n\n  while (py > sy) {\n    var alpha = data[(py - 1) * 4 + 3];\n\n    if (alpha === 0) {\n      ey = py;\n    } else {\n      sy = py;\n    }\n\n    py = ey + sy >> 1;\n  }\n\n  var ratio = py / ih;\n\n  if (ratio === 0) {\n    return 1;\n  } else {\n    return ratio;\n  }\n}; // A replacement for context.drawImage\n// (args are for source and destination).\n\n\nvar drawImageIOSFix = function drawImageIOSFix(ctx, img, sx, sy, sw, sh, dx, dy, dw, dh) {\n  var vertSquashRatio = detectVerticalSquash(img);\n  return ctx.drawImage(img, sx, sy, sw, sh, dx, dy, dw, dh / vertSquashRatio);\n}; // Based on MinifyJpeg\n// Source: http://www.perry.cz/files/ExifRestorer.js\n// http://elicon.blog57.fc2.com/blog-entry-206.html\n\n\nvar ExifRestore = /*#__PURE__*/function () {\n  function ExifRestore() {\n    dropzone_classCallCheck(this, ExifRestore);\n  }\n\n  dropzone_createClass(ExifRestore, null, [{\n    key: \"initClass\",\n    value: function initClass() {\n      this.KEY_STR = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n    }\n  }, {\n    key: \"encode64\",\n    value: function encode64(input) {\n      var output = \"\";\n      var chr1 = undefined;\n      var chr2 = undefined;\n      var chr3 = \"\";\n      var enc1 = undefined;\n      var enc2 = undefined;\n      var enc3 = undefined;\n      var enc4 = \"\";\n      var i = 0;\n\n      while (true) {\n        chr1 = input[i++];\n        chr2 = input[i++];\n        chr3 = input[i++];\n        enc1 = chr1 >> 2;\n        enc2 = (chr1 & 3) << 4 | chr2 >> 4;\n        enc3 = (chr2 & 15) << 2 | chr3 >> 6;\n        enc4 = chr3 & 63;\n\n        if (isNaN(chr2)) {\n          enc3 = enc4 = 64;\n        } else if (isNaN(chr3)) {\n          enc4 = 64;\n        }\n\n        output = output + this.KEY_STR.charAt(enc1) + this.KEY_STR.charAt(enc2) + this.KEY_STR.charAt(enc3) + this.KEY_STR.charAt(enc4);\n        chr1 = chr2 = chr3 = \"\";\n        enc1 = enc2 = enc3 = enc4 = \"\";\n\n        if (!(i < input.length)) {\n          break;\n        }\n      }\n\n      return output;\n    }\n  }, {\n    key: \"restore\",\n    value: function restore(origFileBase64, resizedFileBase64) {\n      if (!origFileBase64.match(\"data:image/jpeg;base64,\")) {\n        return resizedFileBase64;\n      }\n\n      var rawImage = this.decode64(origFileBase64.replace(\"data:image/jpeg;base64,\", \"\"));\n      var segments = this.slice2Segments(rawImage);\n      var image = this.exifManipulation(resizedFileBase64, segments);\n      return \"data:image/jpeg;base64,\".concat(this.encode64(image));\n    }\n  }, {\n    key: \"exifManipulation\",\n    value: function exifManipulation(resizedFileBase64, segments) {\n      var exifArray = this.getExifArray(segments);\n      var newImageArray = this.insertExif(resizedFileBase64, exifArray);\n      var aBuffer = new Uint8Array(newImageArray);\n      return aBuffer;\n    }\n  }, {\n    key: \"getExifArray\",\n    value: function getExifArray(segments) {\n      var seg = undefined;\n      var x = 0;\n\n      while (x < segments.length) {\n        seg = segments[x];\n\n        if (seg[0] === 255 & seg[1] === 225) {\n          return seg;\n        }\n\n        x++;\n      }\n\n      return [];\n    }\n  }, {\n    key: \"insertExif\",\n    value: function insertExif(resizedFileBase64, exifArray) {\n      var imageData = resizedFileBase64.replace(\"data:image/jpeg;base64,\", \"\");\n      var buf = this.decode64(imageData);\n      var separatePoint = buf.indexOf(255, 3);\n      var mae = buf.slice(0, separatePoint);\n      var ato = buf.slice(separatePoint);\n      var array = mae;\n      array = array.concat(exifArray);\n      array = array.concat(ato);\n      return array;\n    }\n  }, {\n    key: \"slice2Segments\",\n    value: function slice2Segments(rawImageArray) {\n      var head = 0;\n      var segments = [];\n\n      while (true) {\n        var length;\n\n        if (rawImageArray[head] === 255 & rawImageArray[head + 1] === 218) {\n          break;\n        }\n\n        if (rawImageArray[head] === 255 & rawImageArray[head + 1] === 216) {\n          head += 2;\n        } else {\n          length = rawImageArray[head + 2] * 256 + rawImageArray[head + 3];\n          var endPoint = head + length + 2;\n          var seg = rawImageArray.slice(head, endPoint);\n          segments.push(seg);\n          head = endPoint;\n        }\n\n        if (head > rawImageArray.length) {\n          break;\n        }\n      }\n\n      return segments;\n    }\n  }, {\n    key: \"decode64\",\n    value: function decode64(input) {\n      var output = \"\";\n      var chr1 = undefined;\n      var chr2 = undefined;\n      var chr3 = \"\";\n      var enc1 = undefined;\n      var enc2 = undefined;\n      var enc3 = undefined;\n      var enc4 = \"\";\n      var i = 0;\n      var buf = []; // remove all characters that are not A-Z, a-z, 0-9, +, /, or =\n\n      var base64test = /[^A-Za-z0-9\\+\\/\\=]/g;\n\n      if (base64test.exec(input)) {\n        console.warn(\"There were invalid base64 characters in the input text.\\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\\nExpect errors in decoding.\");\n      }\n\n      input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, \"\");\n\n      while (true) {\n        enc1 = this.KEY_STR.indexOf(input.charAt(i++));\n        enc2 = this.KEY_STR.indexOf(input.charAt(i++));\n        enc3 = this.KEY_STR.indexOf(input.charAt(i++));\n        enc4 = this.KEY_STR.indexOf(input.charAt(i++));\n        chr1 = enc1 << 2 | enc2 >> 4;\n        chr2 = (enc2 & 15) << 4 | enc3 >> 2;\n        chr3 = (enc3 & 3) << 6 | enc4;\n        buf.push(chr1);\n\n        if (enc3 !== 64) {\n          buf.push(chr2);\n        }\n\n        if (enc4 !== 64) {\n          buf.push(chr3);\n        }\n\n        chr1 = chr2 = chr3 = \"\";\n        enc1 = enc2 = enc3 = enc4 = \"\";\n\n        if (!(i < input.length)) {\n          break;\n        }\n      }\n\n      return buf;\n    }\n  }]);\n\n  return ExifRestore;\n}();\n\nExifRestore.initClass();\n/*\n * contentloaded.js\n *\n * Author: Diego Perini (diego.perini at gmail.com)\n * Summary: cross-browser wrapper for DOMContentLoaded\n * Updated: 20101020\n * License: MIT\n * Version: 1.2\n *\n * URL:\n * http://javascript.nwbox.com/ContentLoaded/\n * http://javascript.nwbox.com/ContentLoaded/MIT-LICENSE\n */\n// @win window reference\n// @fn function reference\n\nvar contentLoaded = function contentLoaded(win, fn) {\n  var done = false;\n  var top = true;\n  var doc = win.document;\n  var root = doc.documentElement;\n  var add = doc.addEventListener ? \"addEventListener\" : \"attachEvent\";\n  var rem = doc.addEventListener ? \"removeEventListener\" : \"detachEvent\";\n  var pre = doc.addEventListener ? \"\" : \"on\";\n\n  var init = function init(e) {\n    if (e.type === \"readystatechange\" && doc.readyState !== \"complete\") {\n      return;\n    }\n\n    (e.type === \"load\" ? win : doc)[rem](pre + e.type, init, false);\n\n    if (!done && (done = true)) {\n      return fn.call(win, e.type || e);\n    }\n  };\n\n  var poll = function poll() {\n    try {\n      root.doScroll(\"left\");\n    } catch (e) {\n      setTimeout(poll, 50);\n      return;\n    }\n\n    return init(\"poll\");\n  };\n\n  if (doc.readyState !== \"complete\") {\n    if (doc.createEventObject && root.doScroll) {\n      try {\n        top = !win.frameElement;\n      } catch (error) {}\n\n      if (top) {\n        poll();\n      }\n    }\n\n    doc[add](pre + \"DOMContentLoaded\", init, false);\n    doc[add](pre + \"readystatechange\", init, false);\n    return win[add](pre + \"load\", init, false);\n  }\n}; // As a single function to be able to write tests.\n\n\nDropzone._autoDiscoverFunction = function () {\n  if (Dropzone.autoDiscover) {\n    return Dropzone.discover();\n  }\n};\n\ncontentLoaded(window, Dropzone._autoDiscoverFunction);\n\nfunction __guard__(value, transform) {\n  return typeof value !== \"undefined\" && value !== null ? transform(value) : undefined;\n}\n\nfunction __guardMethod__(obj, methodName, transform) {\n  if (typeof obj !== \"undefined\" && obj !== null && typeof obj[methodName] === \"function\") {\n    return transform(obj, methodName);\n  } else {\n    return undefined;\n  }\n}\n\n\n;// CONCATENATED MODULE: ./tool/dropzone.dist.js\n /// Make Dropzone a global variable.\n\nwindow.Dropzone = Dropzone;\n/* harmony default export */ var dropzone_dist = (Dropzone);\n\n}();\n/******/ \treturn __nested_webpack_exports__;\n/******/ })()\n;\n});\n\n//# sourceURL=webpack://Materialize/./node_modules/dropzone/dist/dropzone.js?")}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(n,{a:n}),n},__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/dropzone/dropzone.js");return __webpack_exports__}()}));