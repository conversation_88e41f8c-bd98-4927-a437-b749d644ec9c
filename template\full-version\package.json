{"name": "Materialize", "version": "3.0.0", "private": true, "license": "Commercial", "scripts": {"build": "npx gulp build", "build:js": "npx gulp build:js", "build:theme": "npx gulp build:theme", "build:css": "npx gulp build:css", "build:fonts": "npx gulp build:fonts", "build:copy": "npx gulp build:copy", "build:prod": "npx gulp build --env=production", "build:prod:js": "npx gulp build:js --env=production", "build:prod:css": "npx gulp build:css --env=production", "build:prod:fonts": "npx gulp build:fonts --env=production", "build:prod:copy": "npx gulp build:copy --env=production", "watch": "npx gulp watch", "serve": "npx gulp serve", "format:scss": "npx stylelint --fix \"**/*.scss\""}, "dependencies": {"@algolia/autocomplete-js": "1.19.0", "@algolia/autocomplete-theme-classic": "1.19.0", "@form-validation/bundle": "2.4.0", "@form-validation/core": "2.4.0", "@form-validation/plugin-alias": "2.4.0", "@form-validation/plugin-auto-focus": "2.4.0", "@form-validation/plugin-bootstrap5": "2.4.0", "@form-validation/plugin-excluded": "2.4.0", "@form-validation/plugin-field-status": "2.4.0", "@form-validation/plugin-framework": "2.4.0", "@form-validation/plugin-message": "2.4.0", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/list": "6.1.17", "@fullcalendar/timegrid": "6.1.17", "@fullcalendar/timeline": "6.1.17", "@iconify/json": "2.2.331", "@iconify/tools": "4.1.2", "@iconify/types": "2.0.0", "@iconify/utils": "2.3.0", "@popperjs/core": "2.11.8", "@simonwep/pickr": "1.9.1", "@yaireo/tagify": "4.32.2", "animate.css": "4.1.1", "aos": "2.3.4", "apexcharts": "4.2.0", "bloodhound-js": "1.2.3", "bootstrap": "5.3.5", "bootstrap-daterangepicker": "3.1.0", "bootstrap-select": "1.14.0-beta3", "bs-stepper": "1.7.0", "chart.js": "4.4.9", "cleave-zen": "0.0.17", "clipboard": "2.0.11", "datatables.net-bs5": "2.1.8", "datatables.net-buttons": "3.2.2", "datatables.net-buttons-bs5": "3.2.2", "datatables.net-fixedcolumns-bs5": "5.0.4", "datatables.net-fixedheader-bs5": "4.0.1", "datatables.net-responsive": "3.0.4", "datatables.net-responsive-bs5": "3.0.4", "datatables.net-rowgroup-bs5": "1.5.1", "datatables.net-select-bs5": "2.1.0", "dropzone": "5.9.3", "flag-icons": "7.3.2", "flatpickr": "4.6.13", "hammerjs": "2.0.8", "highlight.js": "11.10.0", "i18next": "24.1.2", "i18next-browser-languagedetector": "8.0.5", "i18next-http-backend": "3.0.2", "jkanban": "1.3.1", "jquery": "3.7.1", "jquery-idletimer": "1.0.0", "jquery.repeater": "1.2.1", "jstree": "3.3.17", "jszip": "3.10.1", "katex": "0.16.22", "leaflet": "1.9.4", "mapbox-gl": "3.8.0", "masonry-layout": "4.2.2", "moment": "2.30.1", "node-waves": "0.7.6", "notiflix": "3.2.8", "notyf": "3.10.0", "nouislider": "15.8.1", "numeral": "2.0.6", "pdfmake": "0.2.18", "perfect-scrollbar": "1.5.6", "plyr": "3.7.8", "quill": "2.0.3", "raty-js": "4.3.0", "select2": "4.0.13", "shepherd.js": "14.3.0", "sortablejs": "1.15.6", "spinkit": "2.0.1", "sweetalert2": "11.14.5", "swiper": "11.1.15", "timepicker": "1.14.1", "typeahead.js": "0.11.1"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/plugin-transform-destructuring": "7.23.3", "@babel/plugin-transform-object-rest-spread": "7.23.4", "@babel/plugin-transform-template-literals": "7.23.3", "@babel/preset-env": "7.26.9", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.3", "ajv": "8.17.1", "ansi-colors": "4.1.3", "babel-loader": "9.1.3", "browser-sync": "3.0.4", "color-support": "1.1.3", "css-loader": "6.9.1", "deepmerge": "4.3.1", "del": "8.0.0", "eslint": "9.16.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.6", "fancy-log": "2.0.0", "gulp": "4.0.2", "gulp-autoprefixer": "8.0.0", "gulp-dart-sass": "1.1.0", "gulp-environment": "1.5.2", "gulp-exec": "5.0.0", "gulp-if": "3.0.0", "gulp-purgecss": "7.0.2", "gulp-rename": "2.0.0", "gulp-replace": "1.1.4", "gulp-sourcemaps": "3.0.0", "gulp-uglify": "3.0.2", "gulp-useref": "5.0.0", "html-loader": "4.2.0", "js-beautify": "1.15.4", "prettier": "3.5.3", "sass": "1.78.0", "sass-loader": "14.0.0", "string-replace-webpack-plugin": "0.1.3", "style-loader": "3.3.4", "stylelint": "16.18.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "terser-webpack-plugin": "5.3.14", "webpack": "5.89.0", "yarn": "1.22.22"}, "overrides": {"algoliasearch": "5.17.1", "@algolia/client-search": "5.17.1", "@algolia/autocomplete-plugin-algolia-insights": "1.18.1", "prop-types": "15.8.1", "datatables.net": "2.1.8", "datatables.net-bs5": "2.1.8", "datatables.net-buttons": "3.2.2", "datatables.net-buttons-bs5": "3.2.2", "datatables.net-fixedcolumns": "5.0.4", "datatables.net-fixedheader": "4.0.1", "datatables.net-responsive": "3.0.4", "datatables.net-rowgroup": "1.5.1", "datatables.net-select": "2.1.0", "postcss": "8.5.3", "search-insights": "2.17.3", "superagent": "3.8.3", "chokidar": "3.6.0", "source-map-resolve": "0.6.0", "sass": "1.78.0", "rimraf": "4.0.0"}, "resolutions": {"algoliasearch": "5.17.1", "@algolia/client-search": "5.17.1", "@algolia/autocomplete-plugin-algolia-insights": "1.18.1", "prop-types": "15.8.1", "datatables.net": "2.1.8", "datatables.net-bs5": "2.1.8", "datatables.net-buttons": "3.2.2", "datatables.net-buttons-bs5": "3.2.2", "datatables.net-fixedcolumns": "5.0.4", "datatables.net-fixedheader": "4.0.1", "datatables.net-responsive": "3.0.4", "datatables.net-rowgroup": "1.5.1", "datatables.net-select": "2.1.0", "postcss": "8.5.3", "search-insights": "2.17.3", "superagent": "3.8.3", "chokidar": "3.6.0", "source-map-resolve": "0.6.0", "sass": "1.78.0", "rimraf": "4.0.0", "eslint": "9.16.0", "react": "19.1.0", "react-dom": "19.1.0"}, "gulp-environment": {"environments": [{"name": "development", "aliases": ["dev"]}, {"name": "production", "aliases": ["prod"]}], "default": "development"}}