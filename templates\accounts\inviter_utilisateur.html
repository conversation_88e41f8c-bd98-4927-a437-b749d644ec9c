<!doctype html>
<html lang="fr" class="layout-navbar-fixed layout-menu-fixed layout-compact" dir="ltr" data-skin="default" data-bs-theme="light">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <title>Inviter un utilisateur - {{ organisation_active.nom }}</title>
    <meta name="description" content="Invitez un nouvel utilisateur dans votre organisation" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap" rel="stylesheet" />

    <link rel="stylesheet" href="/static/vendor/fonts/iconify-icons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/node-waves/node-waves.css" />
    <link rel="stylesheet" href="/static/vendor/css/core.css" />
    <link rel="stylesheet" href="/static/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- Helpers -->
    <script src="/static/js/helpers.js"></script>
    <script src="/static/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        
        <!-- Content wrapper -->
        <div class="content-wrapper">
          <!-- Content -->
          <div class="container-xxl flex-grow-1 container-p-y">
            
            <!-- Header -->
            <div class="row">
              <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                  <div>
                    <h4 class="fw-bold py-3 mb-2">
                      <i class="ti ti-user-plus me-2"></i>Inviter un utilisateur
                    </h4>
                    <p class="text-muted">
                      Ajoutez un nouveau membre à votre organisation : {{ organisation_active.nom }}
                    </p>
                  </div>
                  <div>
                    <a href="{% url 'accounts:liste_utilisateurs' %}" class="btn btn-outline-secondary">
                      <i class="ti ti-arrow-left me-1"></i>Retour à la liste
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
              {% endfor %}
            {% endif %}

            <!-- Formulaire d'invitation -->
            <div class="row">
              <div class="col-xl-8 col-lg-10">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i class="ti ti-form me-2"></i>Informations du nouvel utilisateur
                    </h5>
                  </div>
                  
                  <div class="card-body">
                    <form method="post" novalidate>
                      {% csrf_token %}
                      
                      <!-- Erreurs générales -->
                      {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                          <h6 class="alert-heading">
                            <i class="ti ti-alert-circle me-1"></i>Erreurs de validation
                          </h6>
                          {% for error in form.non_field_errors %}
                            <div>{{ error }}</div>
                          {% endfor %}
                        </div>
                      {% endif %}

                      <!-- Informations personnelles -->
                      <div class="mb-4">
                        <h6 class="text-muted mb-3">
                          <i class="ti ti-user me-2"></i>Informations personnelles
                        </h6>
                        
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="{{ form.prenom.id_for_label }}" class="form-label">
                              {{ form.prenom.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.prenom }}
                            {% if form.prenom.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.prenom.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.nom.id_for_label }}" class="form-label">
                              {{ form.nom.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.nom }}
                            {% if form.nom.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.nom.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>
                        </div>
                      </div>

                      <!-- Informations de contact -->
                      <div class="mb-4">
                        <h6 class="text-muted mb-3">
                          <i class="ti ti-mail me-2"></i>Informations de contact
                        </h6>
                        
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                              {{ form.email.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.email.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                            {% if form.email.help_text %}
                              <div class="form-text">{{ form.email.help_text }}</div>
                            {% endif %}
                          </div>

                          <div class="col-md-6 mb-3">
                            <label for="{{ form.telephone.id_for_label }}" class="form-label">
                              {{ form.telephone.label }}
                            </label>
                            {{ form.telephone }}
                            {% if form.telephone.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.telephone.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                            {% if form.telephone.help_text %}
                              <div class="form-text">{{ form.telephone.help_text }}</div>
                            {% endif %}
                          </div>
                        </div>
                      </div>

                      <!-- Rôle et permissions -->
                      <div class="mb-4">
                        <h6 class="text-muted mb-3">
                          <i class="ti ti-shield me-2"></i>Rôle et permissions
                        </h6>
                        
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="{{ form.role.id_for_label }}" class="form-label">
                              {{ form.role.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.role }}
                            {% if form.role.errors %}
                              <div class="invalid-feedback d-block">
                                {% for error in form.role.errors %}{{ error }}{% endfor %}
                              </div>
                            {% endif %}
                          </div>
                        </div>

                        <!-- Descriptions des rôles -->
                        <div class="alert alert-info" role="alert">
                          <h6 class="alert-heading">
                            <i class="ti ti-info-circle me-1"></i>Description des rôles
                          </h6>
                          <div class="row">
                            <div class="col-md-6">
                              <ul class="mb-0">
                                <li><strong>Administrateur :</strong> Accès complet, gestion des utilisateurs</li>
                                <li><strong>Médecin :</strong> Gestion des patients, consultations, prescriptions</li>
                                <li><strong>Infirmier :</strong> Soins, suivi des patients, assistance médicale</li>
                              </ul>
                            </div>
                            <div class="col-md-6">
                              <ul class="mb-0">
                                <li><strong>Réceptionniste :</strong> Accueil, rendez-vous, gestion administrative</li>
                                <li><strong>Comptable :</strong> Facturation, paiements, gestion financière</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Options d'invitation -->
                      <div class="mb-4">
                        <h6 class="text-muted mb-3">
                          <i class="ti ti-settings me-2"></i>Options d'invitation
                        </h6>
                        
                        <div class="form-check">
                          {{ form.envoyer_email }}
                          <label class="form-check-label" for="{{ form.envoyer_email.id_for_label }}">
                            {{ form.envoyer_email.label }}
                          </label>
                        </div>
                        {% if form.envoyer_email.help_text %}
                          <div class="form-text">{{ form.envoyer_email.help_text }}</div>
                        {% endif %}
                      </div>

                      <!-- Informations importantes -->
                      <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">
                          <i class="ti ti-alert-triangle me-1"></i>Informations importantes
                        </h6>
                        <ul class="mb-0">
                          <li>Un mot de passe temporaire sera généré automatiquement</li>
                          <li>L'utilisateur devra changer son mot de passe lors de sa première connexion</li>
                          <li>Un email d'invitation sera envoyé avec les identifiants de connexion</li>
                          <li>Vous pouvez modifier les permissions de l'utilisateur après sa création</li>
                        </ul>
                      </div>

                      <!-- Boutons d'action -->
                      <div class="d-flex justify-content-between">
                        <a href="{% url 'accounts:liste_utilisateurs' %}" class="btn btn-outline-secondary">
                          <i class="ti ti-arrow-left me-1"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                          <i class="ti ti-send me-1"></i>Envoyer l'invitation
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Aide et conseils -->
              <div class="col-xl-4 col-lg-2">
                <div class="card">
                  <div class="card-header">
                    <h6 class="card-title mb-0">
                      <i class="ti ti-help me-2"></i>Aide et conseils
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <h6>💡 Bonnes pratiques</h6>
                      <ul class="small">
                        <li>Utilisez des adresses email professionnelles</li>
                        <li>Choisissez le rôle approprié selon les responsabilités</li>
                        <li>Informez l'utilisateur de son invitation</li>
                      </ul>
                    </div>
                    
                    <div class="mb-3">
                      <h6>🔒 Sécurité</h6>
                      <ul class="small">
                        <li>Les mots de passe temporaires expirent après 7 jours</li>
                        <li>L'utilisateur doit changer son mot de passe</li>
                        <li>Vous pouvez désactiver un utilisateur à tout moment</li>
                      </ul>
                    </div>

                    <div>
                      <h6>📞 Support</h6>
                      <p class="small text-muted">
                        Besoin d'aide ? Contactez notre support à 
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <!-- / Content -->

          <!-- Footer -->
          <footer class="content-footer footer bg-footer-theme">
            <div class="container-xxl">
              <div class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                <div class="text-body">
                  © 2025 Hospital SaaS. Tous droits réservés.
                </div>
                <div class="d-none d-lg-inline-block">
                  <a href="{% url 'organisations:liste' %}" class="footer-link me-4">Organisations</a>
                  <a href="{% url 'accounts:profil' %}" class="footer-link me-4">Profil</a>
                  <a href="{% url 'accounts:logout' %}" class="footer-link">Déconnexion</a>
                </div>
              </div>
            </div>
          </footer>
          <!-- / Footer -->

        </div>
        <!-- Content wrapper -->
      </div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <script src="/static/vendor/libs/jquery/jquery.js"></script>
    <script src="/static/vendor/libs/popper/popper.js"></script>
    <script src="/static/js/bootstrap.js"></script>
    <script src="/static/vendor/libs/node-waves/node-waves.js"></script>
    <script src="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="/static/vendor/libs/hammer/hammer.js"></script>
    <script src="/static/js/menu.js"></script>

    <!-- Main JS -->
    <script src="/static/js/main.js"></script>

    <!-- Page JS -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Validation en temps réel de l'email
        const emailField = document.getElementById('{{ form.email.id_for_label }}');
        if (emailField) {
            emailField.addEventListener('blur', function() {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (this.value && !emailRegex.test(this.value)) {
                    this.classList.add('is-invalid');
                } else if (this.value) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        }
        
        // Validation du téléphone
        const telephoneField = document.getElementById('{{ form.telephone.id_for_label }}');
        if (telephoneField) {
            telephoneField.addEventListener('blur', function() {
                const phoneRegex = /^\+[1-9]\d{1,14}$/;
                if (this.value && !phoneRegex.test(this.value.replace(/\s/g, ''))) {
                    this.classList.add('is-invalid');
                } else if (this.value) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        }

        // Mise à jour de la description du rôle
        const roleField = document.getElementById('{{ form.role.id_for_label }}');
        if (roleField) {
            roleField.addEventListener('change', function() {
                // Ici on pourrait ajouter une description dynamique du rôle sélectionné
                console.log('Rôle sélectionné:', this.value);
            });
        }
    });
    </script>
  </body>
</html>
