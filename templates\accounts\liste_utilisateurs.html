<!doctype html>
<html lang="fr" class="layout-navbar-fixed layout-menu-fixed layout-compact" dir="ltr" data-skin="default" data-bs-theme="light">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <title>Gestion des utilisateurs - {{ organisation_active.nom }}</title>
    <meta name="description" content="Gérez les utilisateurs de votre organisation" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap" rel="stylesheet" />

    <link rel="stylesheet" href="/static/vendor/fonts/iconify-icons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/node-waves/node-waves.css" />
    <link rel="stylesheet" href="/static/vendor/css/core.css" />
    <link rel="stylesheet" href="/static/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- Helpers -->
    <script src="/static/js/helpers.js"></script>
    <script src="/static/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        
        <!-- Content wrapper -->
        <div class="content-wrapper">
          <!-- Content -->
          <div class="container-xxl flex-grow-1 container-p-y">
            
            <!-- Header -->
            <div class="row">
              <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                  <div>
                    <h4 class="fw-bold py-3 mb-2">
                      <i class="ti ti-users me-2"></i>Gestion des utilisateurs
                    </h4>
                    <p class="text-muted">
                      Gérez les membres de votre organisation : {{ organisation_active.nom }}
                    </p>
                  </div>
                  <div>
                    {% if est_admin_organisation %}
                      <a href="{% url 'accounts:inviter_utilisateur' %}" class="btn btn-primary">
                        <i class="ti ti-user-plus me-1"></i>Inviter un utilisateur
                      </a>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>

            <!-- Messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
              {% endfor %}
            {% endif %}

            <!-- Statistiques -->
            <div class="row mb-4">
              <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-md me-3">
                        <span class="avatar-initial rounded-circle bg-label-primary">
                          <i class="ti ti-users ti-lg"></i>
                        </span>
                      </div>
                      <div>
                        <h5 class="mb-0">{{ total_membres }}</h5>
                        <small class="text-muted">Total utilisateurs</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-md me-3">
                        <span class="avatar-initial rounded-circle bg-label-success">
                          <i class="ti ti-user-check ti-lg"></i>
                        </span>
                      </div>
                      <div>
                        <h5 class="mb-0">{{ page_obj.object_list|length }}</h5>
                        <small class="text-muted">Utilisateurs actifs</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-md me-3">
                        <span class="avatar-initial rounded-circle bg-label-warning">
                          <i class="ti ti-crown ti-lg"></i>
                        </span>
                      </div>
                      <div>
                        <h5 class="mb-0">
                          {% for membre in page_obj.object_list %}
                            {% if membre.role == 'ADMIN' %}{{ forloop.counter0|add:1 }}{% endif %}
                          {% empty %}0{% endfor %}
                        </h5>
                        <small class="text-muted">Administrateurs</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-md me-3">
                        <span class="avatar-initial rounded-circle bg-label-info">
                          <i class="ti ti-user ti-lg"></i>
                        </span>
                      </div>
                      <div>
                        <h5 class="mb-0">
                          {% for membre in page_obj.object_list %}
                            {% if membre.role == 'UTILISATEUR' %}{{ forloop.counter0|add:1 }}{% endif %}
                          {% empty %}0{% endfor %}
                        </h5>
                        <small class="text-muted">Utilisateurs</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Liste des utilisateurs -->
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                  <i class="ti ti-list me-2"></i>Liste des utilisateurs
                </h5>
                <div class="d-flex gap-2">
                  <button class="btn btn-outline-secondary btn-sm" onclick="window.print()">
                    <i class="ti ti-printer me-1"></i>Imprimer
                  </button>
                </div>
              </div>
              
              <div class="card-body">
                {% if page_obj.object_list %}
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th>Utilisateur</th>
                          <th>Email</th>
                          <th>Rôle</th>
                          <th>Statut</th>
                          <th>Date d'ajout</th>
                          {% if est_admin_organisation %}
                            <th>Actions</th>
                          {% endif %}
                        </tr>
                      </thead>
                      <tbody>
                        {% for membre in page_obj.object_list %}
                          <tr>
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-3">
                                  <span class="avatar-initial rounded-circle bg-label-primary">
                                    {{ membre.utilisateur.first_name|first|upper }}{{ membre.utilisateur.last_name|first|upper }}
                                  </span>
                                </div>
                                <div>
                                  <h6 class="mb-0">{{ membre.utilisateur.get_full_name|default:membre.utilisateur.username }}</h6>
                                  {% if membre.invite_par %}
                                    <small class="text-muted">Invité par {{ membre.invite_par.get_full_name }}</small>
                                  {% endif %}
                                </div>
                              </div>
                            </td>
                            <td>{{ membre.utilisateur.email }}</td>
                            <td>
                              {% if membre.role == 'ADMIN' %}
                                <span class="badge bg-label-warning">
                                  <i class="ti ti-crown me-1"></i>Administrateur
                                </span>
                              {% else %}
                                <span class="badge bg-label-info">
                                  <i class="ti ti-user me-1"></i>Utilisateur
                                </span>
                              {% endif %}
                            </td>
                            <td>
                              {% if membre.actif %}
                                <span class="badge bg-label-success">
                                  <i class="ti ti-check me-1"></i>Actif
                                </span>
                              {% else %}
                                <span class="badge bg-label-secondary">
                                  <i class="ti ti-x me-1"></i>Inactif
                                </span>
                              {% endif %}
                            </td>
                            <td>
                              <small class="text-muted">{{ membre.date_ajout|date:"d/m/Y" }}</small>
                            </td>
                            {% if est_admin_organisation %}
                              <td>
                                <div class="dropdown">
                                  <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="ti ti-dots-vertical"></i>
                                  </button>
                                  <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'accounts:modifier_utilisateur' membre.utilisateur.pk %}">
                                      <i class="ti ti-edit me-2"></i>Modifier
                                    </a>
                                    {% if membre.actif %}
                                      <a class="dropdown-item text-warning" href="{% url 'accounts:desactiver_utilisateur' membre.utilisateur.pk %}">
                                        <i class="ti ti-user-off me-2"></i>Désactiver
                                      </a>
                                    {% else %}
                                      <a class="dropdown-item text-success" href="{% url 'accounts:desactiver_utilisateur' membre.utilisateur.pk %}">
                                        <i class="ti ti-user-check me-2"></i>Réactiver
                                      </a>
                                    {% endif %}
                                  </div>
                                </div>
                              </td>
                            {% endif %}
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>

                  <!-- Pagination -->
                  {% if page_obj.has_other_pages %}
                    <nav aria-label="Pagination">
                      <ul class="pagination justify-content-center mt-4">
                        {% if page_obj.has_previous %}
                          <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                              <i class="ti ti-chevron-left"></i>
                            </a>
                          </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                          {% if page_obj.number == num %}
                            <li class="page-item active">
                              <span class="page-link">{{ num }}</span>
                            </li>
                          {% else %}
                            <li class="page-item">
                              <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                          {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                          <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                              <i class="ti ti-chevron-right"></i>
                            </a>
                          </li>
                        {% endif %}
                      </ul>
                    </nav>
                  {% endif %}

                {% else %}
                  <div class="text-center py-5">
                    <i class="ti ti-users ti-80px text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun utilisateur trouvé</h5>
                    <p class="text-muted">
                      {% if est_admin_organisation %}
                        Commencez par inviter des utilisateurs à rejoindre votre organisation.
                      {% else %}
                        Contactez l'administrateur pour ajouter des utilisateurs.
                      {% endif %}
                    </p>
                    {% if est_admin_organisation %}
                      <a href="{% url 'accounts:inviter_utilisateur' %}" class="btn btn-primary">
                        <i class="ti ti-user-plus me-1"></i>Inviter le premier utilisateur
                      </a>
                    {% endif %}
                  </div>
                {% endif %}
              </div>
            </div>

          </div>
          <!-- / Content -->

          <!-- Footer -->
          <footer class="content-footer footer bg-footer-theme">
            <div class="container-xxl">
              <div class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                <div class="text-body">
                  © 2025 Hospital SaaS. Tous droits réservés.
                </div>
                <div class="d-none d-lg-inline-block">
                  <a href="{% url 'organisations:liste' %}" class="footer-link me-4">Organisations</a>
                  <a href="{% url 'accounts:profil' %}" class="footer-link me-4">Profil</a>
                  <a href="{% url 'accounts:logout' %}" class="footer-link">Déconnexion</a>
                </div>
              </div>
            </div>
          </footer>
          <!-- / Footer -->

        </div>
        <!-- Content wrapper -->
      </div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <script src="/static/vendor/libs/jquery/jquery.js"></script>
    <script src="/static/vendor/libs/popper/popper.js"></script>
    <script src="/static/js/bootstrap.js"></script>
    <script src="/static/vendor/libs/node-waves/node-waves.js"></script>
    <script src="/static/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="/static/vendor/libs/hammer/hammer.js"></script>
    <script src="/static/js/menu.js"></script>

    <!-- Main JS -->
    <script src="/static/js/main.js"></script>
  </body>
</html>
