@import "../../scss/_bootstrap-extended/include";
@import "perfect-scrollbar/css/perfect-scrollbar";

$ps-width: .25rem !default;
$ps-hover-width: .375rem !default;


.ps {
  position: relative;
  .ps__rail-x {
    block-size: $ps-width;
  }

  .ps__rail-y {
    z-index: 3;
    inline-size: $ps-width;
  }

  .ps__rail-x,
  .ps__rail-y,
  .ps__thumb-x,
  .ps__thumb-y {
    @include border-radius($border-radius-pill);
  }

  .ps__rail-x:hover,
  .ps__rail-x:focus,
  .ps__rail-x.ps--clicking,
  .ps__rail-x:hover > .ps__thumb-x,
  .ps__rail-x:focus > .ps__thumb-x,
  .ps__rail-x.ps--clicking > .ps__thumb-x {
    block-size: $ps-hover-width;
  }

  .ps__rail-y:hover,
  .ps__rail-y:focus,
  .ps__rail-y.ps--clicking,
  .ps__rail-y:hover > .ps__thumb-y,
  .ps__rail-y:focus > .ps__thumb-y,
  .ps__rail-y.ps--clicking > .ps__thumb-y {
    inline-size: $ps-hover-width;
  }

  .ps__thumb-x {
    block-size: $ps-width;
    inset-block-end: 0;
  }

  .ps__thumb-y {
    inline-size: $ps-width;
    inset-inline-end: 0;
  }
  .ps__thumb-x,
  .ps__thumb-y {
    background-color: var(--#{$prefix}gray-400);
  }

  .ps__rail-x:hover,
  .ps__rail-y:hover,
  .ps__rail-x:focus,
  .ps__rail-y:focus,
  .ps__rail-x.ps--clicking,
  .ps__rail-y.ps--clicking {
    background-color: var(--#{$prefix}gray-200);
  }

  .ps__rail-x:hover > .ps__thumb-x,
  .ps__rail-y:hover > .ps__thumb-y,
  .ps__rail-x:focus > .ps__thumb-x,
  .ps__rail-y:focus > .ps__thumb-y,
  .ps__rail-x.ps--clicking > .ps__thumb-x,
  .ps__rail-y.ps--clicking > .ps__thumb-y {
    background-color: var(--#{$prefix}gray-700);
  }
}

/* Firefox width issue fixed */
@supports (-moz-appearance: none) {
  #both-scrollbars-example {
    margin-block: 0;
    margin-inline: auto;
    max-inline-size: 1080px;
    padding-inline: 0;
  }
}
