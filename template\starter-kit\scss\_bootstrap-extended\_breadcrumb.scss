// Breadcrumbs
// *******************************************************************************


.breadcrumb {
  --#{$prefix}breadcrumb-color: #{$breadcrumb-color};
}


.breadcrumb-item {
  a {
    color: var(--#{$prefix}breadcrumb-color);

    &:hover,
    &:focus {
      color: var(--#{$prefix}breadcrumb-item-active-color);
    }
  }
  &:not(.active).icon-base.breadcrumb-icon {
    color: var(--#{$prefix}breadcrumb-divider-color);
  }
}

.breadcrumb-item.active a {
  &,
  &:hover,
  &:focus,
  &:active {
    color: inherit;
  }
}

.breadcrumb-custom-icon .breadcrumb-item + .breadcrumb-item::before {
  content: none !important;
}

// RTL
// ******************************************************************************

:dir(rtl) {
  .breadcrumb-item {
    & + .breadcrumb-item {
      padding-inline: $breadcrumb-item-padding-x 0;

      &::before {
        content: "#{$breadcrumb-divider-flipped}";
        float: inline-start;
        padding-inline: 0 $breadcrumb-item-padding-x;
      }
    }
    .icon-base.breadcrumb-icon {
      transform: scaleX(-1);
    }
  }
}
