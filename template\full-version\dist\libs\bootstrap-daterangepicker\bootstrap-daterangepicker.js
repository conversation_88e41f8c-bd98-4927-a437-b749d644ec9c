!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("moment"),require("jQuery"));else if("function"==typeof define&&define.amd)define(["moment","jQuery"],t);else{var n="object"==typeof exports?t(require("moment"),require("jQuery")):t(e.moment,e.jQuery);for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(self,(function(__WEBPACK_EXTERNAL_MODULE_moment__,__WEBPACK_EXTERNAL_MODULE_jquery__){return function(){var __webpack_modules__={"./libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var bootstrap_daterangepicker_daterangepicker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bootstrap-daterangepicker/daterangepicker */ \"./node_modules/bootstrap-daterangepicker/daterangepicker.js\");\n/* harmony import */ var bootstrap_daterangepicker_daterangepicker__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bootstrap_daterangepicker_daterangepicker__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// Patch detect when weeks are shown\n\nvar fnDaterangepicker = $.fn.daterangepicker;\n$.fn.daterangepicker = function (options, callback) {\n  fnDaterangepicker.call(this, options, callback);\n  if (options && (options.showWeekNumbers || options.showISOWeekNumbers)) {\n    this.each(function () {\n      var instance = $(this).data('daterangepicker');\n      if (instance && instance.container) instance.container.addClass('with-week-numbers');\n    });\n  }\n  return this;\n};\n\n//# sourceURL=webpack://Materialize/./libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js?")},"./node_modules/bootstrap-daterangepicker/daterangepicker.js":function(module,exports,__webpack_require__){eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/**\r\n* @version: 3.1\r\n* @author: Dan Grossman http://www.dangrossman.info/\r\n* @copyright: Copyright (c) 2012-2019 Dan Grossman. All rights reserved.\r\n* @license: Licensed under the MIT license. See http://www.opensource.org/licenses/mit-license.php\r\n* @website: http://www.daterangepicker.com/\r\n*/\r\n// Following the UMD template https://github.com/umdjs/umd/blob/master/templates/returnExportsGlobal.js\r\n(function (root, factory) {\r\n    if (true) {\r\n        // AMD. Make globaly available as well\r\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(/*! moment */ \"moment\"), __webpack_require__(/*! jquery */ \"jquery\")], __WEBPACK_AMD_DEFINE_RESULT__ = (function (moment, jquery) {\r\n            if (!jquery.fn) jquery.fn = {}; // webpack server rendering\r\n            if (typeof moment !== 'function' && moment.hasOwnProperty('default')) moment = moment['default']\r\n            return factory(moment, jquery);\r\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    } else { var moment, jQuery; }\r\n}(this, function(moment, $) {\r\n    var DateRangePicker = function(element, options, cb) {\r\n\r\n        //default settings for options\r\n        this.parentEl = 'body';\r\n        this.element = $(element);\r\n        this.startDate = moment().startOf('day');\r\n        this.endDate = moment().endOf('day');\r\n        this.minDate = false;\r\n        this.maxDate = false;\r\n        this.maxSpan = false;\r\n        this.autoApply = false;\r\n        this.singleDatePicker = false;\r\n        this.showDropdowns = false;\r\n        this.minYear = moment().subtract(100, 'year').format('YYYY');\r\n        this.maxYear = moment().add(100, 'year').format('YYYY');\r\n        this.showWeekNumbers = false;\r\n        this.showISOWeekNumbers = false;\r\n        this.showCustomRangeLabel = true;\r\n        this.timePicker = false;\r\n        this.timePicker24Hour = false;\r\n        this.timePickerIncrement = 1;\r\n        this.timePickerSeconds = false;\r\n        this.linkedCalendars = true;\r\n        this.autoUpdateInput = true;\r\n        this.alwaysShowCalendars = false;\r\n        this.ranges = {};\r\n\r\n        this.opens = 'right';\r\n        if (this.element.hasClass('pull-right'))\r\n            this.opens = 'left';\r\n\r\n        this.drops = 'down';\r\n        if (this.element.hasClass('dropup'))\r\n            this.drops = 'up';\r\n\r\n        this.buttonClasses = 'btn btn-sm';\r\n        this.applyButtonClasses = 'btn-primary';\r\n        this.cancelButtonClasses = 'btn-default';\r\n\r\n        this.locale = {\r\n            direction: 'ltr',\r\n            format: moment.localeData().longDateFormat('L'),\r\n            separator: ' - ',\r\n            applyLabel: 'Apply',\r\n            cancelLabel: 'Cancel',\r\n            weekLabel: 'W',\r\n            customRangeLabel: 'Custom Range',\r\n            daysOfWeek: moment.weekdaysMin(),\r\n            monthNames: moment.monthsShort(),\r\n            firstDay: moment.localeData().firstDayOfWeek()\r\n        };\r\n\r\n        this.callback = function() { };\r\n\r\n        //some state information\r\n        this.isShowing = false;\r\n        this.leftCalendar = {};\r\n        this.rightCalendar = {};\r\n\r\n        //custom options from user\r\n        if (typeof options !== 'object' || options === null)\r\n            options = {};\r\n\r\n        //allow setting options with data attributes\r\n        //data-api options will be overwritten with custom javascript options\r\n        options = $.extend(this.element.data(), options);\r\n\r\n        //html template for the picker UI\r\n        if (typeof options.template !== 'string' && !(options.template instanceof $))\r\n            options.template =\r\n            '<div class=\"daterangepicker\">' +\r\n                '<div class=\"ranges\"></div>' +\r\n                '<div class=\"drp-calendar left\">' +\r\n                    '<div class=\"calendar-table\"></div>' +\r\n                    '<div class=\"calendar-time\"></div>' +\r\n                '</div>' +\r\n                '<div class=\"drp-calendar right\">' +\r\n                    '<div class=\"calendar-table\"></div>' +\r\n                    '<div class=\"calendar-time\"></div>' +\r\n                '</div>' +\r\n                '<div class=\"drp-buttons\">' +\r\n                    '<span class=\"drp-selected\"></span>' +\r\n                    '<button class=\"cancelBtn\" type=\"button\"></button>' +\r\n                    '<button class=\"applyBtn\" disabled=\"disabled\" type=\"button\"></button> ' +\r\n                '</div>' +\r\n            '</div>';\r\n\r\n        this.parentEl = (options.parentEl && $(options.parentEl).length) ? $(options.parentEl) : $(this.parentEl);\r\n        this.container = $(options.template).appendTo(this.parentEl);\r\n\r\n        //\r\n        // handle all the possible options overriding defaults\r\n        //\r\n\r\n        if (typeof options.locale === 'object') {\r\n\r\n            if (typeof options.locale.direction === 'string')\r\n                this.locale.direction = options.locale.direction;\r\n\r\n            if (typeof options.locale.format === 'string')\r\n                this.locale.format = options.locale.format;\r\n\r\n            if (typeof options.locale.separator === 'string')\r\n                this.locale.separator = options.locale.separator;\r\n\r\n            if (typeof options.locale.daysOfWeek === 'object')\r\n                this.locale.daysOfWeek = options.locale.daysOfWeek.slice();\r\n\r\n            if (typeof options.locale.monthNames === 'object')\r\n              this.locale.monthNames = options.locale.monthNames.slice();\r\n\r\n            if (typeof options.locale.firstDay === 'number')\r\n              this.locale.firstDay = options.locale.firstDay;\r\n\r\n            if (typeof options.locale.applyLabel === 'string')\r\n              this.locale.applyLabel = options.locale.applyLabel;\r\n\r\n            if (typeof options.locale.cancelLabel === 'string')\r\n              this.locale.cancelLabel = options.locale.cancelLabel;\r\n\r\n            if (typeof options.locale.weekLabel === 'string')\r\n              this.locale.weekLabel = options.locale.weekLabel;\r\n\r\n            if (typeof options.locale.customRangeLabel === 'string'){\r\n                //Support unicode chars in the custom range name.\r\n                var elem = document.createElement('textarea');\r\n                elem.innerHTML = options.locale.customRangeLabel;\r\n                var rangeHtml = elem.value;\r\n                this.locale.customRangeLabel = rangeHtml;\r\n            }\r\n        }\r\n        this.container.addClass(this.locale.direction);\r\n\r\n        if (typeof options.startDate === 'string')\r\n            this.startDate = moment(options.startDate, this.locale.format);\r\n\r\n        if (typeof options.endDate === 'string')\r\n            this.endDate = moment(options.endDate, this.locale.format);\r\n\r\n        if (typeof options.minDate === 'string')\r\n            this.minDate = moment(options.minDate, this.locale.format);\r\n\r\n        if (typeof options.maxDate === 'string')\r\n            this.maxDate = moment(options.maxDate, this.locale.format);\r\n\r\n        if (typeof options.startDate === 'object')\r\n            this.startDate = moment(options.startDate);\r\n\r\n        if (typeof options.endDate === 'object')\r\n            this.endDate = moment(options.endDate);\r\n\r\n        if (typeof options.minDate === 'object')\r\n            this.minDate = moment(options.minDate);\r\n\r\n        if (typeof options.maxDate === 'object')\r\n            this.maxDate = moment(options.maxDate);\r\n\r\n        // sanity check for bad options\r\n        if (this.minDate && this.startDate.isBefore(this.minDate))\r\n            this.startDate = this.minDate.clone();\r\n\r\n        // sanity check for bad options\r\n        if (this.maxDate && this.endDate.isAfter(this.maxDate))\r\n            this.endDate = this.maxDate.clone();\r\n\r\n        if (typeof options.applyButtonClasses === 'string')\r\n            this.applyButtonClasses = options.applyButtonClasses;\r\n\r\n        if (typeof options.applyClass === 'string') //backwards compat\r\n            this.applyButtonClasses = options.applyClass;\r\n\r\n        if (typeof options.cancelButtonClasses === 'string')\r\n            this.cancelButtonClasses = options.cancelButtonClasses;\r\n\r\n        if (typeof options.cancelClass === 'string') //backwards compat\r\n            this.cancelButtonClasses = options.cancelClass;\r\n\r\n        if (typeof options.maxSpan === 'object')\r\n            this.maxSpan = options.maxSpan;\r\n\r\n        if (typeof options.dateLimit === 'object') //backwards compat\r\n            this.maxSpan = options.dateLimit;\r\n\r\n        if (typeof options.opens === 'string')\r\n            this.opens = options.opens;\r\n\r\n        if (typeof options.drops === 'string')\r\n            this.drops = options.drops;\r\n\r\n        if (typeof options.showWeekNumbers === 'boolean')\r\n            this.showWeekNumbers = options.showWeekNumbers;\r\n\r\n        if (typeof options.showISOWeekNumbers === 'boolean')\r\n            this.showISOWeekNumbers = options.showISOWeekNumbers;\r\n\r\n        if (typeof options.buttonClasses === 'string')\r\n            this.buttonClasses = options.buttonClasses;\r\n\r\n        if (typeof options.buttonClasses === 'object')\r\n            this.buttonClasses = options.buttonClasses.join(' ');\r\n\r\n        if (typeof options.showDropdowns === 'boolean')\r\n            this.showDropdowns = options.showDropdowns;\r\n\r\n        if (typeof options.minYear === 'number')\r\n            this.minYear = options.minYear;\r\n\r\n        if (typeof options.maxYear === 'number')\r\n            this.maxYear = options.maxYear;\r\n\r\n        if (typeof options.showCustomRangeLabel === 'boolean')\r\n            this.showCustomRangeLabel = options.showCustomRangeLabel;\r\n\r\n        if (typeof options.singleDatePicker === 'boolean') {\r\n            this.singleDatePicker = options.singleDatePicker;\r\n            if (this.singleDatePicker)\r\n                this.endDate = this.startDate.clone();\r\n        }\r\n\r\n        if (typeof options.timePicker === 'boolean')\r\n            this.timePicker = options.timePicker;\r\n\r\n        if (typeof options.timePickerSeconds === 'boolean')\r\n            this.timePickerSeconds = options.timePickerSeconds;\r\n\r\n        if (typeof options.timePickerIncrement === 'number')\r\n            this.timePickerIncrement = options.timePickerIncrement;\r\n\r\n        if (typeof options.timePicker24Hour === 'boolean')\r\n            this.timePicker24Hour = options.timePicker24Hour;\r\n\r\n        if (typeof options.autoApply === 'boolean')\r\n            this.autoApply = options.autoApply;\r\n\r\n        if (typeof options.autoUpdateInput === 'boolean')\r\n            this.autoUpdateInput = options.autoUpdateInput;\r\n\r\n        if (typeof options.linkedCalendars === 'boolean')\r\n            this.linkedCalendars = options.linkedCalendars;\r\n\r\n        if (typeof options.isInvalidDate === 'function')\r\n            this.isInvalidDate = options.isInvalidDate;\r\n\r\n        if (typeof options.isCustomDate === 'function')\r\n            this.isCustomDate = options.isCustomDate;\r\n\r\n        if (typeof options.alwaysShowCalendars === 'boolean')\r\n            this.alwaysShowCalendars = options.alwaysShowCalendars;\r\n\r\n        // update day names order to firstDay\r\n        if (this.locale.firstDay != 0) {\r\n            var iterator = this.locale.firstDay;\r\n            while (iterator > 0) {\r\n                this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift());\r\n                iterator--;\r\n            }\r\n        }\r\n\r\n        var start, end, range;\r\n\r\n        //if no start/end dates set, check if an input element contains initial values\r\n        if (typeof options.startDate === 'undefined' && typeof options.endDate === 'undefined') {\r\n            if ($(this.element).is(':text')) {\r\n                var val = $(this.element).val(),\r\n                    split = val.split(this.locale.separator);\r\n\r\n                start = end = null;\r\n\r\n                if (split.length == 2) {\r\n                    start = moment(split[0], this.locale.format);\r\n                    end = moment(split[1], this.locale.format);\r\n                } else if (this.singleDatePicker && val !== \"\") {\r\n                    start = moment(val, this.locale.format);\r\n                    end = moment(val, this.locale.format);\r\n                }\r\n                if (start !== null && end !== null) {\r\n                    this.setStartDate(start);\r\n                    this.setEndDate(end);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (typeof options.ranges === 'object') {\r\n            for (range in options.ranges) {\r\n\r\n                if (typeof options.ranges[range][0] === 'string')\r\n                    start = moment(options.ranges[range][0], this.locale.format);\r\n                else\r\n                    start = moment(options.ranges[range][0]);\r\n\r\n                if (typeof options.ranges[range][1] === 'string')\r\n                    end = moment(options.ranges[range][1], this.locale.format);\r\n                else\r\n                    end = moment(options.ranges[range][1]);\r\n\r\n                // If the start or end date exceed those allowed by the minDate or maxSpan\r\n                // options, shorten the range to the allowable period.\r\n                if (this.minDate && start.isBefore(this.minDate))\r\n                    start = this.minDate.clone();\r\n\r\n                var maxDate = this.maxDate;\r\n                if (this.maxSpan && maxDate && start.clone().add(this.maxSpan).isAfter(maxDate))\r\n                    maxDate = start.clone().add(this.maxSpan);\r\n                if (maxDate && end.isAfter(maxDate))\r\n                    end = maxDate.clone();\r\n\r\n                // If the end of the range is before the minimum or the start of the range is\r\n                // after the maximum, don't display this range option at all.\r\n                if ((this.minDate && end.isBefore(this.minDate, this.timepicker ? 'minute' : 'day'))\r\n                  || (maxDate && start.isAfter(maxDate, this.timepicker ? 'minute' : 'day')))\r\n                    continue;\r\n\r\n                //Support unicode chars in the range names.\r\n                var elem = document.createElement('textarea');\r\n                elem.innerHTML = range;\r\n                var rangeHtml = elem.value;\r\n\r\n                this.ranges[rangeHtml] = [start, end];\r\n            }\r\n\r\n            var list = '<ul>';\r\n            for (range in this.ranges) {\r\n                list += '<li data-range-key=\"' + range + '\">' + range + '</li>';\r\n            }\r\n            if (this.showCustomRangeLabel) {\r\n                list += '<li data-range-key=\"' + this.locale.customRangeLabel + '\">' + this.locale.customRangeLabel + '</li>';\r\n            }\r\n            list += '</ul>';\r\n            this.container.find('.ranges').prepend(list);\r\n        }\r\n\r\n        if (typeof cb === 'function') {\r\n            this.callback = cb;\r\n        }\r\n\r\n        if (!this.timePicker) {\r\n            this.startDate = this.startDate.startOf('day');\r\n            this.endDate = this.endDate.endOf('day');\r\n            this.container.find('.calendar-time').hide();\r\n        }\r\n\r\n        //can't be used together for now\r\n        if (this.timePicker && this.autoApply)\r\n            this.autoApply = false;\r\n\r\n        if (this.autoApply) {\r\n            this.container.addClass('auto-apply');\r\n        }\r\n\r\n        if (typeof options.ranges === 'object')\r\n            this.container.addClass('show-ranges');\r\n\r\n        if (this.singleDatePicker) {\r\n            this.container.addClass('single');\r\n            this.container.find('.drp-calendar.left').addClass('single');\r\n            this.container.find('.drp-calendar.left').show();\r\n            this.container.find('.drp-calendar.right').hide();\r\n            if (!this.timePicker && this.autoApply) {\r\n                this.container.addClass('auto-apply');\r\n            }\r\n        }\r\n\r\n        if ((typeof options.ranges === 'undefined' && !this.singleDatePicker) || this.alwaysShowCalendars) {\r\n            this.container.addClass('show-calendar');\r\n        }\r\n\r\n        this.container.addClass('opens' + this.opens);\r\n\r\n        //apply CSS classes and labels to buttons\r\n        this.container.find('.applyBtn, .cancelBtn').addClass(this.buttonClasses);\r\n        if (this.applyButtonClasses.length)\r\n            this.container.find('.applyBtn').addClass(this.applyButtonClasses);\r\n        if (this.cancelButtonClasses.length)\r\n            this.container.find('.cancelBtn').addClass(this.cancelButtonClasses);\r\n        this.container.find('.applyBtn').html(this.locale.applyLabel);\r\n        this.container.find('.cancelBtn').html(this.locale.cancelLabel);\r\n\r\n        //\r\n        // event listeners\r\n        //\r\n\r\n        this.container.find('.drp-calendar')\r\n            .on('click.daterangepicker', '.prev', $.proxy(this.clickPrev, this))\r\n            .on('click.daterangepicker', '.next', $.proxy(this.clickNext, this))\r\n            .on('mousedown.daterangepicker', 'td.available', $.proxy(this.clickDate, this))\r\n            .on('mouseenter.daterangepicker', 'td.available', $.proxy(this.hoverDate, this))\r\n            .on('change.daterangepicker', 'select.yearselect', $.proxy(this.monthOrYearChanged, this))\r\n            .on('change.daterangepicker', 'select.monthselect', $.proxy(this.monthOrYearChanged, this))\r\n            .on('change.daterangepicker', 'select.hourselect,select.minuteselect,select.secondselect,select.ampmselect', $.proxy(this.timeChanged, this));\r\n\r\n        this.container.find('.ranges')\r\n            .on('click.daterangepicker', 'li', $.proxy(this.clickRange, this));\r\n\r\n        this.container.find('.drp-buttons')\r\n            .on('click.daterangepicker', 'button.applyBtn', $.proxy(this.clickApply, this))\r\n            .on('click.daterangepicker', 'button.cancelBtn', $.proxy(this.clickCancel, this));\r\n\r\n        if (this.element.is('input') || this.element.is('button')) {\r\n            this.element.on({\r\n                'click.daterangepicker': $.proxy(this.show, this),\r\n                'focus.daterangepicker': $.proxy(this.show, this),\r\n                'keyup.daterangepicker': $.proxy(this.elementChanged, this),\r\n                'keydown.daterangepicker': $.proxy(this.keydown, this) //IE 11 compatibility\r\n            });\r\n        } else {\r\n            this.element.on('click.daterangepicker', $.proxy(this.toggle, this));\r\n            this.element.on('keydown.daterangepicker', $.proxy(this.toggle, this));\r\n        }\r\n\r\n        //\r\n        // if attached to a text input, set the initial value\r\n        //\r\n\r\n        this.updateElement();\r\n\r\n    };\r\n\r\n    DateRangePicker.prototype = {\r\n\r\n        constructor: DateRangePicker,\r\n\r\n        setStartDate: function(startDate) {\r\n            if (typeof startDate === 'string')\r\n                this.startDate = moment(startDate, this.locale.format);\r\n\r\n            if (typeof startDate === 'object')\r\n                this.startDate = moment(startDate);\r\n\r\n            if (!this.timePicker)\r\n                this.startDate = this.startDate.startOf('day');\r\n\r\n            if (this.timePicker && this.timePickerIncrement)\r\n                this.startDate.minute(Math.round(this.startDate.minute() / this.timePickerIncrement) * this.timePickerIncrement);\r\n\r\n            if (this.minDate && this.startDate.isBefore(this.minDate)) {\r\n                this.startDate = this.minDate.clone();\r\n                if (this.timePicker && this.timePickerIncrement)\r\n                    this.startDate.minute(Math.round(this.startDate.minute() / this.timePickerIncrement) * this.timePickerIncrement);\r\n            }\r\n\r\n            if (this.maxDate && this.startDate.isAfter(this.maxDate)) {\r\n                this.startDate = this.maxDate.clone();\r\n                if (this.timePicker && this.timePickerIncrement)\r\n                    this.startDate.minute(Math.floor(this.startDate.minute() / this.timePickerIncrement) * this.timePickerIncrement);\r\n            }\r\n\r\n            if (!this.isShowing)\r\n                this.updateElement();\r\n\r\n            this.updateMonthsInView();\r\n        },\r\n\r\n        setEndDate: function(endDate) {\r\n            if (typeof endDate === 'string')\r\n                this.endDate = moment(endDate, this.locale.format);\r\n\r\n            if (typeof endDate === 'object')\r\n                this.endDate = moment(endDate);\r\n\r\n            if (!this.timePicker)\r\n                this.endDate = this.endDate.endOf('day');\r\n\r\n            if (this.timePicker && this.timePickerIncrement)\r\n                this.endDate.minute(Math.round(this.endDate.minute() / this.timePickerIncrement) * this.timePickerIncrement);\r\n\r\n            if (this.endDate.isBefore(this.startDate))\r\n                this.endDate = this.startDate.clone();\r\n\r\n            if (this.maxDate && this.endDate.isAfter(this.maxDate))\r\n                this.endDate = this.maxDate.clone();\r\n\r\n            if (this.maxSpan && this.startDate.clone().add(this.maxSpan).isBefore(this.endDate))\r\n                this.endDate = this.startDate.clone().add(this.maxSpan);\r\n\r\n            this.previousRightTime = this.endDate.clone();\r\n\r\n            this.container.find('.drp-selected').html(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));\r\n\r\n            if (!this.isShowing)\r\n                this.updateElement();\r\n\r\n            this.updateMonthsInView();\r\n        },\r\n\r\n        isInvalidDate: function() {\r\n            return false;\r\n        },\r\n\r\n        isCustomDate: function() {\r\n            return false;\r\n        },\r\n\r\n        updateView: function() {\r\n            if (this.timePicker) {\r\n                this.renderTimePicker('left');\r\n                this.renderTimePicker('right');\r\n                if (!this.endDate) {\r\n                    this.container.find('.right .calendar-time select').prop('disabled', true).addClass('disabled');\r\n                } else {\r\n                    this.container.find('.right .calendar-time select').prop('disabled', false).removeClass('disabled');\r\n                }\r\n            }\r\n            if (this.endDate)\r\n                this.container.find('.drp-selected').html(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));\r\n            this.updateMonthsInView();\r\n            this.updateCalendars();\r\n            this.updateFormInputs();\r\n        },\r\n\r\n        updateMonthsInView: function() {\r\n            if (this.endDate) {\r\n\r\n                //if both dates are visible already, do nothing\r\n                if (!this.singleDatePicker && this.leftCalendar.month && this.rightCalendar.month &&\r\n                    (this.startDate.format('YYYY-MM') == this.leftCalendar.month.format('YYYY-MM') || this.startDate.format('YYYY-MM') == this.rightCalendar.month.format('YYYY-MM'))\r\n                    &&\r\n                    (this.endDate.format('YYYY-MM') == this.leftCalendar.month.format('YYYY-MM') || this.endDate.format('YYYY-MM') == this.rightCalendar.month.format('YYYY-MM'))\r\n                    ) {\r\n                    return;\r\n                }\r\n\r\n                this.leftCalendar.month = this.startDate.clone().date(2);\r\n                if (!this.linkedCalendars && (this.endDate.month() != this.startDate.month() || this.endDate.year() != this.startDate.year())) {\r\n                    this.rightCalendar.month = this.endDate.clone().date(2);\r\n                } else {\r\n                    this.rightCalendar.month = this.startDate.clone().date(2).add(1, 'month');\r\n                }\r\n\r\n            } else {\r\n                if (this.leftCalendar.month.format('YYYY-MM') != this.startDate.format('YYYY-MM') && this.rightCalendar.month.format('YYYY-MM') != this.startDate.format('YYYY-MM')) {\r\n                    this.leftCalendar.month = this.startDate.clone().date(2);\r\n                    this.rightCalendar.month = this.startDate.clone().date(2).add(1, 'month');\r\n                }\r\n            }\r\n            if (this.maxDate && this.linkedCalendars && !this.singleDatePicker && this.rightCalendar.month > this.maxDate) {\r\n              this.rightCalendar.month = this.maxDate.clone().date(2);\r\n              this.leftCalendar.month = this.maxDate.clone().date(2).subtract(1, 'month');\r\n            }\r\n        },\r\n\r\n        updateCalendars: function() {\r\n\r\n            if (this.timePicker) {\r\n                var hour, minute, second;\r\n                if (this.endDate) {\r\n                    hour = parseInt(this.container.find('.left .hourselect').val(), 10);\r\n                    minute = parseInt(this.container.find('.left .minuteselect').val(), 10);\r\n                    if (isNaN(minute)) {\r\n                        minute = parseInt(this.container.find('.left .minuteselect option:last').val(), 10);\r\n                    }\r\n                    second = this.timePickerSeconds ? parseInt(this.container.find('.left .secondselect').val(), 10) : 0;\r\n                    if (!this.timePicker24Hour) {\r\n                        var ampm = this.container.find('.left .ampmselect').val();\r\n                        if (ampm === 'PM' && hour < 12)\r\n                            hour += 12;\r\n                        if (ampm === 'AM' && hour === 12)\r\n                            hour = 0;\r\n                    }\r\n                } else {\r\n                    hour = parseInt(this.container.find('.right .hourselect').val(), 10);\r\n                    minute = parseInt(this.container.find('.right .minuteselect').val(), 10);\r\n                    if (isNaN(minute)) {\r\n                        minute = parseInt(this.container.find('.right .minuteselect option:last').val(), 10);\r\n                    }\r\n                    second = this.timePickerSeconds ? parseInt(this.container.find('.right .secondselect').val(), 10) : 0;\r\n                    if (!this.timePicker24Hour) {\r\n                        var ampm = this.container.find('.right .ampmselect').val();\r\n                        if (ampm === 'PM' && hour < 12)\r\n                            hour += 12;\r\n                        if (ampm === 'AM' && hour === 12)\r\n                            hour = 0;\r\n                    }\r\n                }\r\n                this.leftCalendar.month.hour(hour).minute(minute).second(second);\r\n                this.rightCalendar.month.hour(hour).minute(minute).second(second);\r\n            }\r\n\r\n            this.renderCalendar('left');\r\n            this.renderCalendar('right');\r\n\r\n            //highlight any predefined range matching the current start and end dates\r\n            this.container.find('.ranges li').removeClass('active');\r\n            if (this.endDate == null) return;\r\n\r\n            this.calculateChosenLabel();\r\n        },\r\n\r\n        renderCalendar: function(side) {\r\n\r\n            //\r\n            // Build the matrix of dates that will populate the calendar\r\n            //\r\n\r\n            var calendar = side == 'left' ? this.leftCalendar : this.rightCalendar;\r\n            var month = calendar.month.month();\r\n            var year = calendar.month.year();\r\n            var hour = calendar.month.hour();\r\n            var minute = calendar.month.minute();\r\n            var second = calendar.month.second();\r\n            var daysInMonth = moment([year, month]).daysInMonth();\r\n            var firstDay = moment([year, month, 1]);\r\n            var lastDay = moment([year, month, daysInMonth]);\r\n            var lastMonth = moment(firstDay).subtract(1, 'month').month();\r\n            var lastYear = moment(firstDay).subtract(1, 'month').year();\r\n            var daysInLastMonth = moment([lastYear, lastMonth]).daysInMonth();\r\n            var dayOfWeek = firstDay.day();\r\n\r\n            //initialize a 6 rows x 7 columns array for the calendar\r\n            var calendar = [];\r\n            calendar.firstDay = firstDay;\r\n            calendar.lastDay = lastDay;\r\n\r\n            for (var i = 0; i < 6; i++) {\r\n                calendar[i] = [];\r\n            }\r\n\r\n            //populate the calendar with date objects\r\n            var startDay = daysInLastMonth - dayOfWeek + this.locale.firstDay + 1;\r\n            if (startDay > daysInLastMonth)\r\n                startDay -= 7;\r\n\r\n            if (dayOfWeek == this.locale.firstDay)\r\n                startDay = daysInLastMonth - 6;\r\n\r\n            var curDate = moment([lastYear, lastMonth, startDay, 12, minute, second]);\r\n\r\n            var col, row;\r\n            for (var i = 0, col = 0, row = 0; i < 42; i++, col++, curDate = moment(curDate).add(24, 'hour')) {\r\n                if (i > 0 && col % 7 === 0) {\r\n                    col = 0;\r\n                    row++;\r\n                }\r\n                calendar[row][col] = curDate.clone().hour(hour).minute(minute).second(second);\r\n                curDate.hour(12);\r\n\r\n                if (this.minDate && calendar[row][col].format('YYYY-MM-DD') == this.minDate.format('YYYY-MM-DD') && calendar[row][col].isBefore(this.minDate) && side == 'left') {\r\n                    calendar[row][col] = this.minDate.clone();\r\n                }\r\n\r\n                if (this.maxDate && calendar[row][col].format('YYYY-MM-DD') == this.maxDate.format('YYYY-MM-DD') && calendar[row][col].isAfter(this.maxDate) && side == 'right') {\r\n                    calendar[row][col] = this.maxDate.clone();\r\n                }\r\n\r\n            }\r\n\r\n            //make the calendar object available to hoverDate/clickDate\r\n            if (side == 'left') {\r\n                this.leftCalendar.calendar = calendar;\r\n            } else {\r\n                this.rightCalendar.calendar = calendar;\r\n            }\r\n\r\n            //\r\n            // Display the calendar\r\n            //\r\n\r\n            var minDate = side == 'left' ? this.minDate : this.startDate;\r\n            var maxDate = this.maxDate;\r\n            var selected = side == 'left' ? this.startDate : this.endDate;\r\n            var arrow = this.locale.direction == 'ltr' ? {left: 'chevron-left', right: 'chevron-right'} : {left: 'chevron-right', right: 'chevron-left'};\r\n\r\n            var html = '<table class=\"table-condensed\">';\r\n            html += '<thead>';\r\n            html += '<tr>';\r\n\r\n            // add empty cell for week number\r\n            if (this.showWeekNumbers || this.showISOWeekNumbers)\r\n                html += '<th></th>';\r\n\r\n            if ((!minDate || minDate.isBefore(calendar.firstDay)) && (!this.linkedCalendars || side == 'left')) {\r\n                html += '<th class=\"prev available\"><span></span></th>';\r\n            } else {\r\n                html += '<th></th>';\r\n            }\r\n\r\n            var dateHtml = this.locale.monthNames[calendar[1][1].month()] + calendar[1][1].format(\" YYYY\");\r\n\r\n            if (this.showDropdowns) {\r\n                var currentMonth = calendar[1][1].month();\r\n                var currentYear = calendar[1][1].year();\r\n                var maxYear = (maxDate && maxDate.year()) || (this.maxYear);\r\n                var minYear = (minDate && minDate.year()) || (this.minYear);\r\n                var inMinYear = currentYear == minYear;\r\n                var inMaxYear = currentYear == maxYear;\r\n\r\n                var monthHtml = '<select class=\"monthselect\">';\r\n                for (var m = 0; m < 12; m++) {\r\n                    if ((!inMinYear || (minDate && m >= minDate.month())) && (!inMaxYear || (maxDate && m <= maxDate.month()))) {\r\n                        monthHtml += \"<option value='\" + m + \"'\" +\r\n                            (m === currentMonth ? \" selected='selected'\" : \"\") +\r\n                            \">\" + this.locale.monthNames[m] + \"</option>\";\r\n                    } else {\r\n                        monthHtml += \"<option value='\" + m + \"'\" +\r\n                            (m === currentMonth ? \" selected='selected'\" : \"\") +\r\n                            \" disabled='disabled'>\" + this.locale.monthNames[m] + \"</option>\";\r\n                    }\r\n                }\r\n                monthHtml += \"</select>\";\r\n\r\n                var yearHtml = '<select class=\"yearselect\">';\r\n                for (var y = minYear; y <= maxYear; y++) {\r\n                    yearHtml += '<option value=\"' + y + '\"' +\r\n                        (y === currentYear ? ' selected=\"selected\"' : '') +\r\n                        '>' + y + '</option>';\r\n                }\r\n                yearHtml += '</select>';\r\n\r\n                dateHtml = monthHtml + yearHtml;\r\n            }\r\n\r\n            html += '<th colspan=\"5\" class=\"month\">' + dateHtml + '</th>';\r\n            if ((!maxDate || maxDate.isAfter(calendar.lastDay)) && (!this.linkedCalendars || side == 'right' || this.singleDatePicker)) {\r\n                html += '<th class=\"next available\"><span></span></th>';\r\n            } else {\r\n                html += '<th></th>';\r\n            }\r\n\r\n            html += '</tr>';\r\n            html += '<tr>';\r\n\r\n            // add week number label\r\n            if (this.showWeekNumbers || this.showISOWeekNumbers)\r\n                html += '<th class=\"week\">' + this.locale.weekLabel + '</th>';\r\n\r\n            $.each(this.locale.daysOfWeek, function(index, dayOfWeek) {\r\n                html += '<th>' + dayOfWeek + '</th>';\r\n            });\r\n\r\n            html += '</tr>';\r\n            html += '</thead>';\r\n            html += '<tbody>';\r\n\r\n            //adjust maxDate to reflect the maxSpan setting in order to\r\n            //grey out end dates beyond the maxSpan\r\n            if (this.endDate == null && this.maxSpan) {\r\n                var maxLimit = this.startDate.clone().add(this.maxSpan).endOf('day');\r\n                if (!maxDate || maxLimit.isBefore(maxDate)) {\r\n                    maxDate = maxLimit;\r\n                }\r\n            }\r\n\r\n            for (var row = 0; row < 6; row++) {\r\n                html += '<tr>';\r\n\r\n                // add week number\r\n                if (this.showWeekNumbers)\r\n                    html += '<td class=\"week\">' + calendar[row][0].week() + '</td>';\r\n                else if (this.showISOWeekNumbers)\r\n                    html += '<td class=\"week\">' + calendar[row][0].isoWeek() + '</td>';\r\n\r\n                for (var col = 0; col < 7; col++) {\r\n\r\n                    var classes = [];\r\n\r\n                    //highlight today's date\r\n                    if (calendar[row][col].isSame(new Date(), \"day\"))\r\n                        classes.push('today');\r\n\r\n                    //highlight weekends\r\n                    if (calendar[row][col].isoWeekday() > 5)\r\n                        classes.push('weekend');\r\n\r\n                    //grey out the dates in other months displayed at beginning and end of this calendar\r\n                    if (calendar[row][col].month() != calendar[1][1].month())\r\n                        classes.push('off', 'ends');\r\n\r\n                    //don't allow selection of dates before the minimum date\r\n                    if (this.minDate && calendar[row][col].isBefore(this.minDate, 'day'))\r\n                        classes.push('off', 'disabled');\r\n\r\n                    //don't allow selection of dates after the maximum date\r\n                    if (maxDate && calendar[row][col].isAfter(maxDate, 'day'))\r\n                        classes.push('off', 'disabled');\r\n\r\n                    //don't allow selection of date if a custom function decides it's invalid\r\n                    if (this.isInvalidDate(calendar[row][col]))\r\n                        classes.push('off', 'disabled');\r\n\r\n                    //highlight the currently selected start date\r\n                    if (calendar[row][col].format('YYYY-MM-DD') == this.startDate.format('YYYY-MM-DD'))\r\n                        classes.push('active', 'start-date');\r\n\r\n                    //highlight the currently selected end date\r\n                    if (this.endDate != null && calendar[row][col].format('YYYY-MM-DD') == this.endDate.format('YYYY-MM-DD'))\r\n                        classes.push('active', 'end-date');\r\n\r\n                    //highlight dates in-between the selected dates\r\n                    if (this.endDate != null && calendar[row][col] > this.startDate && calendar[row][col] < this.endDate)\r\n                        classes.push('in-range');\r\n\r\n                    //apply custom classes for this date\r\n                    var isCustom = this.isCustomDate(calendar[row][col]);\r\n                    if (isCustom !== false) {\r\n                        if (typeof isCustom === 'string')\r\n                            classes.push(isCustom);\r\n                        else\r\n                            Array.prototype.push.apply(classes, isCustom);\r\n                    }\r\n\r\n                    var cname = '', disabled = false;\r\n                    for (var i = 0; i < classes.length; i++) {\r\n                        cname += classes[i] + ' ';\r\n                        if (classes[i] == 'disabled')\r\n                            disabled = true;\r\n                    }\r\n                    if (!disabled)\r\n                        cname += 'available';\r\n\r\n                    html += '<td class=\"' + cname.replace(/^\\s+|\\s+$/g, '') + '\" data-title=\"' + 'r' + row + 'c' + col + '\">' + calendar[row][col].date() + '</td>';\r\n\r\n                }\r\n                html += '</tr>';\r\n            }\r\n\r\n            html += '</tbody>';\r\n            html += '</table>';\r\n\r\n            this.container.find('.drp-calendar.' + side + ' .calendar-table').html(html);\r\n\r\n        },\r\n\r\n        renderTimePicker: function(side) {\r\n\r\n            // Don't bother updating the time picker if it's currently disabled\r\n            // because an end date hasn't been clicked yet\r\n            if (side == 'right' && !this.endDate) return;\r\n\r\n            var html, selected, minDate, maxDate = this.maxDate;\r\n\r\n            if (this.maxSpan && (!this.maxDate || this.startDate.clone().add(this.maxSpan).isBefore(this.maxDate)))\r\n                maxDate = this.startDate.clone().add(this.maxSpan);\r\n\r\n            if (side == 'left') {\r\n                selected = this.startDate.clone();\r\n                minDate = this.minDate;\r\n            } else if (side == 'right') {\r\n                selected = this.endDate.clone();\r\n                minDate = this.startDate;\r\n\r\n                //Preserve the time already selected\r\n                var timeSelector = this.container.find('.drp-calendar.right .calendar-time');\r\n                if (timeSelector.html() != '') {\r\n\r\n                    selected.hour(!isNaN(selected.hour()) ? selected.hour() : timeSelector.find('.hourselect option:selected').val());\r\n                    selected.minute(!isNaN(selected.minute()) ? selected.minute() : timeSelector.find('.minuteselect option:selected').val());\r\n                    selected.second(!isNaN(selected.second()) ? selected.second() : timeSelector.find('.secondselect option:selected').val());\r\n\r\n                    if (!this.timePicker24Hour) {\r\n                        var ampm = timeSelector.find('.ampmselect option:selected').val();\r\n                        if (ampm === 'PM' && selected.hour() < 12)\r\n                            selected.hour(selected.hour() + 12);\r\n                        if (ampm === 'AM' && selected.hour() === 12)\r\n                            selected.hour(0);\r\n                    }\r\n\r\n                }\r\n\r\n                if (selected.isBefore(this.startDate))\r\n                    selected = this.startDate.clone();\r\n\r\n                if (maxDate && selected.isAfter(maxDate))\r\n                    selected = maxDate.clone();\r\n\r\n            }\r\n\r\n            //\r\n            // hours\r\n            //\r\n\r\n            html = '<select class=\"hourselect\">';\r\n\r\n            var start = this.timePicker24Hour ? 0 : 1;\r\n            var end = this.timePicker24Hour ? 23 : 12;\r\n\r\n            for (var i = start; i <= end; i++) {\r\n                var i_in_24 = i;\r\n                if (!this.timePicker24Hour)\r\n                    i_in_24 = selected.hour() >= 12 ? (i == 12 ? 12 : i + 12) : (i == 12 ? 0 : i);\r\n\r\n                var time = selected.clone().hour(i_in_24);\r\n                var disabled = false;\r\n                if (minDate && time.minute(59).isBefore(minDate))\r\n                    disabled = true;\r\n                if (maxDate && time.minute(0).isAfter(maxDate))\r\n                    disabled = true;\r\n\r\n                if (i_in_24 == selected.hour() && !disabled) {\r\n                    html += '<option value=\"' + i + '\" selected=\"selected\">' + i + '</option>';\r\n                } else if (disabled) {\r\n                    html += '<option value=\"' + i + '\" disabled=\"disabled\" class=\"disabled\">' + i + '</option>';\r\n                } else {\r\n                    html += '<option value=\"' + i + '\">' + i + '</option>';\r\n                }\r\n            }\r\n\r\n            html += '</select> ';\r\n\r\n            //\r\n            // minutes\r\n            //\r\n\r\n            html += ': <select class=\"minuteselect\">';\r\n\r\n            for (var i = 0; i < 60; i += this.timePickerIncrement) {\r\n                var padded = i < 10 ? '0' + i : i;\r\n                var time = selected.clone().minute(i);\r\n\r\n                var disabled = false;\r\n                if (minDate && time.second(59).isBefore(minDate))\r\n                    disabled = true;\r\n                if (maxDate && time.second(0).isAfter(maxDate))\r\n                    disabled = true;\r\n\r\n                if (selected.minute() == i && !disabled) {\r\n                    html += '<option value=\"' + i + '\" selected=\"selected\">' + padded + '</option>';\r\n                } else if (disabled) {\r\n                    html += '<option value=\"' + i + '\" disabled=\"disabled\" class=\"disabled\">' + padded + '</option>';\r\n                } else {\r\n                    html += '<option value=\"' + i + '\">' + padded + '</option>';\r\n                }\r\n            }\r\n\r\n            html += '</select> ';\r\n\r\n            //\r\n            // seconds\r\n            //\r\n\r\n            if (this.timePickerSeconds) {\r\n                html += ': <select class=\"secondselect\">';\r\n\r\n                for (var i = 0; i < 60; i++) {\r\n                    var padded = i < 10 ? '0' + i : i;\r\n                    var time = selected.clone().second(i);\r\n\r\n                    var disabled = false;\r\n                    if (minDate && time.isBefore(minDate))\r\n                        disabled = true;\r\n                    if (maxDate && time.isAfter(maxDate))\r\n                        disabled = true;\r\n\r\n                    if (selected.second() == i && !disabled) {\r\n                        html += '<option value=\"' + i + '\" selected=\"selected\">' + padded + '</option>';\r\n                    } else if (disabled) {\r\n                        html += '<option value=\"' + i + '\" disabled=\"disabled\" class=\"disabled\">' + padded + '</option>';\r\n                    } else {\r\n                        html += '<option value=\"' + i + '\">' + padded + '</option>';\r\n                    }\r\n                }\r\n\r\n                html += '</select> ';\r\n            }\r\n\r\n            //\r\n            // AM/PM\r\n            //\r\n\r\n            if (!this.timePicker24Hour) {\r\n                html += '<select class=\"ampmselect\">';\r\n\r\n                var am_html = '';\r\n                var pm_html = '';\r\n\r\n                if (minDate && selected.clone().hour(12).minute(0).second(0).isBefore(minDate))\r\n                    am_html = ' disabled=\"disabled\" class=\"disabled\"';\r\n\r\n                if (maxDate && selected.clone().hour(0).minute(0).second(0).isAfter(maxDate))\r\n                    pm_html = ' disabled=\"disabled\" class=\"disabled\"';\r\n\r\n                if (selected.hour() >= 12) {\r\n                    html += '<option value=\"AM\"' + am_html + '>AM</option><option value=\"PM\" selected=\"selected\"' + pm_html + '>PM</option>';\r\n                } else {\r\n                    html += '<option value=\"AM\" selected=\"selected\"' + am_html + '>AM</option><option value=\"PM\"' + pm_html + '>PM</option>';\r\n                }\r\n\r\n                html += '</select>';\r\n            }\r\n\r\n            this.container.find('.drp-calendar.' + side + ' .calendar-time').html(html);\r\n\r\n        },\r\n\r\n        updateFormInputs: function() {\r\n\r\n            if (this.singleDatePicker || (this.endDate && (this.startDate.isBefore(this.endDate) || this.startDate.isSame(this.endDate)))) {\r\n                this.container.find('button.applyBtn').prop('disabled', false);\r\n            } else {\r\n                this.container.find('button.applyBtn').prop('disabled', true);\r\n            }\r\n\r\n        },\r\n\r\n        move: function() {\r\n            var parentOffset = { top: 0, left: 0 },\r\n                containerTop,\r\n                drops = this.drops;\r\n\r\n            var parentRightEdge = $(window).width();\r\n            if (!this.parentEl.is('body')) {\r\n                parentOffset = {\r\n                    top: this.parentEl.offset().top - this.parentEl.scrollTop(),\r\n                    left: this.parentEl.offset().left - this.parentEl.scrollLeft()\r\n                };\r\n                parentRightEdge = this.parentEl[0].clientWidth + this.parentEl.offset().left;\r\n            }\r\n\r\n            switch (drops) {\r\n            case 'auto':\r\n                containerTop = this.element.offset().top + this.element.outerHeight() - parentOffset.top;\r\n                if (containerTop + this.container.outerHeight() >= this.parentEl[0].scrollHeight) {\r\n                    containerTop = this.element.offset().top - this.container.outerHeight() - parentOffset.top;\r\n                    drops = 'up';\r\n                }\r\n                break;\r\n            case 'up':\r\n                containerTop = this.element.offset().top - this.container.outerHeight() - parentOffset.top;\r\n                break;\r\n            default:\r\n                containerTop = this.element.offset().top + this.element.outerHeight() - parentOffset.top;\r\n                break;\r\n            }\r\n\r\n            // Force the container to it's actual width\r\n            this.container.css({\r\n              top: 0,\r\n              left: 0,\r\n              right: 'auto'\r\n            });\r\n            var containerWidth = this.container.outerWidth();\r\n\r\n            this.container.toggleClass('drop-up', drops == 'up');\r\n\r\n            if (this.opens == 'left') {\r\n                var containerRight = parentRightEdge - this.element.offset().left - this.element.outerWidth();\r\n                if (containerWidth + containerRight > $(window).width()) {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        right: 'auto',\r\n                        left: 9\r\n                    });\r\n                } else {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        right: containerRight,\r\n                        left: 'auto'\r\n                    });\r\n                }\r\n            } else if (this.opens == 'center') {\r\n                var containerLeft = this.element.offset().left - parentOffset.left + this.element.outerWidth() / 2\r\n                                        - containerWidth / 2;\r\n                if (containerLeft < 0) {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        right: 'auto',\r\n                        left: 9\r\n                    });\r\n                } else if (containerLeft + containerWidth > $(window).width()) {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        left: 'auto',\r\n                        right: 0\r\n                    });\r\n                } else {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        left: containerLeft,\r\n                        right: 'auto'\r\n                    });\r\n                }\r\n            } else {\r\n                var containerLeft = this.element.offset().left - parentOffset.left;\r\n                if (containerLeft + containerWidth > $(window).width()) {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        left: 'auto',\r\n                        right: 0\r\n                    });\r\n                } else {\r\n                    this.container.css({\r\n                        top: containerTop,\r\n                        left: containerLeft,\r\n                        right: 'auto'\r\n                    });\r\n                }\r\n            }\r\n        },\r\n\r\n        show: function(e) {\r\n            if (this.isShowing) return;\r\n\r\n            // Create a click proxy that is private to this instance of datepicker, for unbinding\r\n            this._outsideClickProxy = $.proxy(function(e) { this.outsideClick(e); }, this);\r\n\r\n            // Bind global datepicker mousedown for hiding and\r\n            $(document)\r\n              .on('mousedown.daterangepicker', this._outsideClickProxy)\r\n              // also support mobile devices\r\n              .on('touchend.daterangepicker', this._outsideClickProxy)\r\n              // also explicitly play nice with Bootstrap dropdowns, which stopPropagation when clicking them\r\n              .on('click.daterangepicker', '[data-toggle=dropdown]', this._outsideClickProxy)\r\n              // and also close when focus changes to outside the picker (eg. tabbing between controls)\r\n              .on('focusin.daterangepicker', this._outsideClickProxy);\r\n\r\n            // Reposition the picker if the window is resized while it's open\r\n            $(window).on('resize.daterangepicker', $.proxy(function(e) { this.move(e); }, this));\r\n\r\n            this.oldStartDate = this.startDate.clone();\r\n            this.oldEndDate = this.endDate.clone();\r\n            this.previousRightTime = this.endDate.clone();\r\n\r\n            this.updateView();\r\n            this.container.show();\r\n            this.move();\r\n            this.element.trigger('show.daterangepicker', this);\r\n            this.isShowing = true;\r\n        },\r\n\r\n        hide: function(e) {\r\n            if (!this.isShowing) return;\r\n\r\n            //incomplete date selection, revert to last values\r\n            if (!this.endDate) {\r\n                this.startDate = this.oldStartDate.clone();\r\n                this.endDate = this.oldEndDate.clone();\r\n            }\r\n\r\n            //if a new date range was selected, invoke the user callback function\r\n            if (!this.startDate.isSame(this.oldStartDate) || !this.endDate.isSame(this.oldEndDate))\r\n                this.callback(this.startDate.clone(), this.endDate.clone(), this.chosenLabel);\r\n\r\n            //if picker is attached to a text input, update it\r\n            this.updateElement();\r\n\r\n            $(document).off('.daterangepicker');\r\n            $(window).off('.daterangepicker');\r\n            this.container.hide();\r\n            this.element.trigger('hide.daterangepicker', this);\r\n            this.isShowing = false;\r\n        },\r\n\r\n        toggle: function(e) {\r\n            if (this.isShowing) {\r\n                this.hide();\r\n            } else {\r\n                this.show();\r\n            }\r\n        },\r\n\r\n        outsideClick: function(e) {\r\n            var target = $(e.target);\r\n            // if the page is clicked anywhere except within the daterangerpicker/button\r\n            // itself then call this.hide()\r\n            if (\r\n                // ie modal dialog fix\r\n                e.type == \"focusin\" ||\r\n                target.closest(this.element).length ||\r\n                target.closest(this.container).length ||\r\n                target.closest('.calendar-table').length\r\n                ) return;\r\n            this.hide();\r\n            this.element.trigger('outsideClick.daterangepicker', this);\r\n        },\r\n\r\n        showCalendars: function() {\r\n            this.container.addClass('show-calendar');\r\n            this.move();\r\n            this.element.trigger('showCalendar.daterangepicker', this);\r\n        },\r\n\r\n        hideCalendars: function() {\r\n            this.container.removeClass('show-calendar');\r\n            this.element.trigger('hideCalendar.daterangepicker', this);\r\n        },\r\n\r\n        clickRange: function(e) {\r\n            var label = e.target.getAttribute('data-range-key');\r\n            this.chosenLabel = label;\r\n            if (label == this.locale.customRangeLabel) {\r\n                this.showCalendars();\r\n            } else {\r\n                var dates = this.ranges[label];\r\n                this.startDate = dates[0];\r\n                this.endDate = dates[1];\r\n\r\n                if (!this.timePicker) {\r\n                    this.startDate.startOf('day');\r\n                    this.endDate.endOf('day');\r\n                }\r\n\r\n                if (!this.alwaysShowCalendars)\r\n                    this.hideCalendars();\r\n                this.clickApply();\r\n            }\r\n        },\r\n\r\n        clickPrev: function(e) {\r\n            var cal = $(e.target).parents('.drp-calendar');\r\n            if (cal.hasClass('left')) {\r\n                this.leftCalendar.month.subtract(1, 'month');\r\n                if (this.linkedCalendars)\r\n                    this.rightCalendar.month.subtract(1, 'month');\r\n            } else {\r\n                this.rightCalendar.month.subtract(1, 'month');\r\n            }\r\n            this.updateCalendars();\r\n        },\r\n\r\n        clickNext: function(e) {\r\n            var cal = $(e.target).parents('.drp-calendar');\r\n            if (cal.hasClass('left')) {\r\n                this.leftCalendar.month.add(1, 'month');\r\n            } else {\r\n                this.rightCalendar.month.add(1, 'month');\r\n                if (this.linkedCalendars)\r\n                    this.leftCalendar.month.add(1, 'month');\r\n            }\r\n            this.updateCalendars();\r\n        },\r\n\r\n        hoverDate: function(e) {\r\n\r\n            //ignore dates that can't be selected\r\n            if (!$(e.target).hasClass('available')) return;\r\n\r\n            var title = $(e.target).attr('data-title');\r\n            var row = title.substr(1, 1);\r\n            var col = title.substr(3, 1);\r\n            var cal = $(e.target).parents('.drp-calendar');\r\n            var date = cal.hasClass('left') ? this.leftCalendar.calendar[row][col] : this.rightCalendar.calendar[row][col];\r\n\r\n            //highlight the dates between the start date and the date being hovered as a potential end date\r\n            var leftCalendar = this.leftCalendar;\r\n            var rightCalendar = this.rightCalendar;\r\n            var startDate = this.startDate;\r\n            if (!this.endDate) {\r\n                this.container.find('.drp-calendar tbody td').each(function(index, el) {\r\n\r\n                    //skip week numbers, only look at dates\r\n                    if ($(el).hasClass('week')) return;\r\n\r\n                    var title = $(el).attr('data-title');\r\n                    var row = title.substr(1, 1);\r\n                    var col = title.substr(3, 1);\r\n                    var cal = $(el).parents('.drp-calendar');\r\n                    var dt = cal.hasClass('left') ? leftCalendar.calendar[row][col] : rightCalendar.calendar[row][col];\r\n\r\n                    if ((dt.isAfter(startDate) && dt.isBefore(date)) || dt.isSame(date, 'day')) {\r\n                        $(el).addClass('in-range');\r\n                    } else {\r\n                        $(el).removeClass('in-range');\r\n                    }\r\n\r\n                });\r\n            }\r\n\r\n        },\r\n\r\n        clickDate: function(e) {\r\n\r\n            if (!$(e.target).hasClass('available')) return;\r\n\r\n            var title = $(e.target).attr('data-title');\r\n            var row = title.substr(1, 1);\r\n            var col = title.substr(3, 1);\r\n            var cal = $(e.target).parents('.drp-calendar');\r\n            var date = cal.hasClass('left') ? this.leftCalendar.calendar[row][col] : this.rightCalendar.calendar[row][col];\r\n\r\n            //\r\n            // this function needs to do a few things:\r\n            // * alternate between selecting a start and end date for the range,\r\n            // * if the time picker is enabled, apply the hour/minute/second from the select boxes to the clicked date\r\n            // * if autoapply is enabled, and an end date was chosen, apply the selection\r\n            // * if single date picker mode, and time picker isn't enabled, apply the selection immediately\r\n            // * if one of the inputs above the calendars was focused, cancel that manual input\r\n            //\r\n\r\n            if (this.endDate || date.isBefore(this.startDate, 'day')) { //picking start\r\n                if (this.timePicker) {\r\n                    var hour = parseInt(this.container.find('.left .hourselect').val(), 10);\r\n                    if (!this.timePicker24Hour) {\r\n                        var ampm = this.container.find('.left .ampmselect').val();\r\n                        if (ampm === 'PM' && hour < 12)\r\n                            hour += 12;\r\n                        if (ampm === 'AM' && hour === 12)\r\n                            hour = 0;\r\n                    }\r\n                    var minute = parseInt(this.container.find('.left .minuteselect').val(), 10);\r\n                    if (isNaN(minute)) {\r\n                        minute = parseInt(this.container.find('.left .minuteselect option:last').val(), 10);\r\n                    }\r\n                    var second = this.timePickerSeconds ? parseInt(this.container.find('.left .secondselect').val(), 10) : 0;\r\n                    date = date.clone().hour(hour).minute(minute).second(second);\r\n                }\r\n                this.endDate = null;\r\n                this.setStartDate(date.clone());\r\n            } else if (!this.endDate && date.isBefore(this.startDate)) {\r\n                //special case: clicking the same date for start/end,\r\n                //but the time of the end date is before the start date\r\n                this.setEndDate(this.startDate.clone());\r\n            } else { // picking end\r\n                if (this.timePicker) {\r\n                    var hour = parseInt(this.container.find('.right .hourselect').val(), 10);\r\n                    if (!this.timePicker24Hour) {\r\n                        var ampm = this.container.find('.right .ampmselect').val();\r\n                        if (ampm === 'PM' && hour < 12)\r\n                            hour += 12;\r\n                        if (ampm === 'AM' && hour === 12)\r\n                            hour = 0;\r\n                    }\r\n                    var minute = parseInt(this.container.find('.right .minuteselect').val(), 10);\r\n                    if (isNaN(minute)) {\r\n                        minute = parseInt(this.container.find('.right .minuteselect option:last').val(), 10);\r\n                    }\r\n                    var second = this.timePickerSeconds ? parseInt(this.container.find('.right .secondselect').val(), 10) : 0;\r\n                    date = date.clone().hour(hour).minute(minute).second(second);\r\n                }\r\n                this.setEndDate(date.clone());\r\n                if (this.autoApply) {\r\n                  this.calculateChosenLabel();\r\n                  this.clickApply();\r\n                }\r\n            }\r\n\r\n            if (this.singleDatePicker) {\r\n                this.setEndDate(this.startDate);\r\n                if (!this.timePicker && this.autoApply)\r\n                    this.clickApply();\r\n            }\r\n\r\n            this.updateView();\r\n\r\n            //This is to cancel the blur event handler if the mouse was in one of the inputs\r\n            e.stopPropagation();\r\n\r\n        },\r\n\r\n        calculateChosenLabel: function () {\r\n            var customRange = true;\r\n            var i = 0;\r\n            for (var range in this.ranges) {\r\n              if (this.timePicker) {\r\n                    var format = this.timePickerSeconds ? \"YYYY-MM-DD HH:mm:ss\" : \"YYYY-MM-DD HH:mm\";\r\n                    //ignore times when comparing dates if time picker seconds is not enabled\r\n                    if (this.startDate.format(format) == this.ranges[range][0].format(format) && this.endDate.format(format) == this.ranges[range][1].format(format)) {\r\n                        customRange = false;\r\n                        this.chosenLabel = this.container.find('.ranges li:eq(' + i + ')').addClass('active').attr('data-range-key');\r\n                        break;\r\n                    }\r\n                } else {\r\n                    //ignore times when comparing dates if time picker is not enabled\r\n                    if (this.startDate.format('YYYY-MM-DD') == this.ranges[range][0].format('YYYY-MM-DD') && this.endDate.format('YYYY-MM-DD') == this.ranges[range][1].format('YYYY-MM-DD')) {\r\n                        customRange = false;\r\n                        this.chosenLabel = this.container.find('.ranges li:eq(' + i + ')').addClass('active').attr('data-range-key');\r\n                        break;\r\n                    }\r\n                }\r\n                i++;\r\n            }\r\n            if (customRange) {\r\n                if (this.showCustomRangeLabel) {\r\n                    this.chosenLabel = this.container.find('.ranges li:last').addClass('active').attr('data-range-key');\r\n                } else {\r\n                    this.chosenLabel = null;\r\n                }\r\n                this.showCalendars();\r\n            }\r\n        },\r\n\r\n        clickApply: function(e) {\r\n            this.hide();\r\n            this.element.trigger('apply.daterangepicker', this);\r\n        },\r\n\r\n        clickCancel: function(e) {\r\n            this.startDate = this.oldStartDate;\r\n            this.endDate = this.oldEndDate;\r\n            this.hide();\r\n            this.element.trigger('cancel.daterangepicker', this);\r\n        },\r\n\r\n        monthOrYearChanged: function(e) {\r\n            var isLeft = $(e.target).closest('.drp-calendar').hasClass('left'),\r\n                leftOrRight = isLeft ? 'left' : 'right',\r\n                cal = this.container.find('.drp-calendar.'+leftOrRight);\r\n\r\n            // Month must be Number for new moment versions\r\n            var month = parseInt(cal.find('.monthselect').val(), 10);\r\n            var year = cal.find('.yearselect').val();\r\n\r\n            if (!isLeft) {\r\n                if (year < this.startDate.year() || (year == this.startDate.year() && month < this.startDate.month())) {\r\n                    month = this.startDate.month();\r\n                    year = this.startDate.year();\r\n                }\r\n            }\r\n\r\n            if (this.minDate) {\r\n                if (year < this.minDate.year() || (year == this.minDate.year() && month < this.minDate.month())) {\r\n                    month = this.minDate.month();\r\n                    year = this.minDate.year();\r\n                }\r\n            }\r\n\r\n            if (this.maxDate) {\r\n                if (year > this.maxDate.year() || (year == this.maxDate.year() && month > this.maxDate.month())) {\r\n                    month = this.maxDate.month();\r\n                    year = this.maxDate.year();\r\n                }\r\n            }\r\n\r\n            if (isLeft) {\r\n                this.leftCalendar.month.month(month).year(year);\r\n                if (this.linkedCalendars)\r\n                    this.rightCalendar.month = this.leftCalendar.month.clone().add(1, 'month');\r\n            } else {\r\n                this.rightCalendar.month.month(month).year(year);\r\n                if (this.linkedCalendars)\r\n                    this.leftCalendar.month = this.rightCalendar.month.clone().subtract(1, 'month');\r\n            }\r\n            this.updateCalendars();\r\n        },\r\n\r\n        timeChanged: function(e) {\r\n\r\n            var cal = $(e.target).closest('.drp-calendar'),\r\n                isLeft = cal.hasClass('left');\r\n\r\n            var hour = parseInt(cal.find('.hourselect').val(), 10);\r\n            var minute = parseInt(cal.find('.minuteselect').val(), 10);\r\n            if (isNaN(minute)) {\r\n                minute = parseInt(cal.find('.minuteselect option:last').val(), 10);\r\n            }\r\n            var second = this.timePickerSeconds ? parseInt(cal.find('.secondselect').val(), 10) : 0;\r\n\r\n            if (!this.timePicker24Hour) {\r\n                var ampm = cal.find('.ampmselect').val();\r\n                if (ampm === 'PM' && hour < 12)\r\n                    hour += 12;\r\n                if (ampm === 'AM' && hour === 12)\r\n                    hour = 0;\r\n            }\r\n\r\n            if (isLeft) {\r\n                var start = this.startDate.clone();\r\n                start.hour(hour);\r\n                start.minute(minute);\r\n                start.second(second);\r\n                this.setStartDate(start);\r\n                if (this.singleDatePicker) {\r\n                    this.endDate = this.startDate.clone();\r\n                } else if (this.endDate && this.endDate.format('YYYY-MM-DD') == start.format('YYYY-MM-DD') && this.endDate.isBefore(start)) {\r\n                    this.setEndDate(start.clone());\r\n                }\r\n            } else if (this.endDate) {\r\n                var end = this.endDate.clone();\r\n                end.hour(hour);\r\n                end.minute(minute);\r\n                end.second(second);\r\n                this.setEndDate(end);\r\n            }\r\n\r\n            //update the calendars so all clickable dates reflect the new time component\r\n            this.updateCalendars();\r\n\r\n            //update the form inputs above the calendars with the new time\r\n            this.updateFormInputs();\r\n\r\n            //re-render the time pickers because changing one selection can affect what's enabled in another\r\n            this.renderTimePicker('left');\r\n            this.renderTimePicker('right');\r\n\r\n        },\r\n\r\n        elementChanged: function() {\r\n            if (!this.element.is('input')) return;\r\n            if (!this.element.val().length) return;\r\n\r\n            var dateString = this.element.val().split(this.locale.separator),\r\n                start = null,\r\n                end = null;\r\n\r\n            if (dateString.length === 2) {\r\n                start = moment(dateString[0], this.locale.format);\r\n                end = moment(dateString[1], this.locale.format);\r\n            }\r\n\r\n            if (this.singleDatePicker || start === null || end === null) {\r\n                start = moment(this.element.val(), this.locale.format);\r\n                end = start;\r\n            }\r\n\r\n            if (!start.isValid() || !end.isValid()) return;\r\n\r\n            this.setStartDate(start);\r\n            this.setEndDate(end);\r\n            this.updateView();\r\n        },\r\n\r\n        keydown: function(e) {\r\n            //hide on tab or enter\r\n            if ((e.keyCode === 9) || (e.keyCode === 13)) {\r\n                this.hide();\r\n            }\r\n\r\n            //hide on esc and prevent propagation\r\n            if (e.keyCode === 27) {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n\r\n                this.hide();\r\n            }\r\n        },\r\n\r\n        updateElement: function() {\r\n            if (this.element.is('input') && this.autoUpdateInput) {\r\n                var newValue = this.startDate.format(this.locale.format);\r\n                if (!this.singleDatePicker) {\r\n                    newValue += this.locale.separator + this.endDate.format(this.locale.format);\r\n                }\r\n                if (newValue !== this.element.val()) {\r\n                    this.element.val(newValue).trigger('change');\r\n                }\r\n            }\r\n        },\r\n\r\n        remove: function() {\r\n            this.container.remove();\r\n            this.element.off('.daterangepicker');\r\n            this.element.removeData();\r\n        }\r\n\r\n    };\r\n\r\n    $.fn.daterangepicker = function(options, callback) {\r\n        var implementOptions = $.extend(true, {}, $.fn.daterangepicker.defaultOptions, options);\r\n        this.each(function() {\r\n            var el = $(this);\r\n            if (el.data('daterangepicker'))\r\n                el.data('daterangepicker').remove();\r\n            el.data('daterangepicker', new DateRangePicker(el, implementOptions, callback));\r\n        });\r\n        return this;\r\n    };\r\n\r\n    return DateRangePicker;\r\n\r\n}));\r\n\n\n//# sourceURL=webpack://Materialize/./node_modules/bootstrap-daterangepicker/daterangepicker.js?")},jquery:function(e){"use strict";e.exports=__WEBPACK_EXTERNAL_MODULE_jquery__},moment:function(e){"use strict";e.exports=__WEBPACK_EXTERNAL_MODULE_moment__}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(n.exports,n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=function(e,t){for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js");return __webpack_exports__}()}));