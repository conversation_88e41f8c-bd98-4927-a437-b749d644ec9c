/* The color-scheme CSS property https://web.dev/color-scheme/ */

:root,
[data-bs-theme="light"] {
  @if ($bordered-style == true){
    /* Bordered Skin Variables */
    &[data-skin="bordered"]{
      --#{$prefix}body-bg: var(--#{$prefix}paper-bg);
      --#{$prefix}navbar-bg: var(--#{$prefix}paper-bg);
      --#{$prefix}navbar-box-shadow: none;
      --#{$prefix}navbar-border-color: #{$border-color};
      --#{$prefix}menu-bg: var(--#{$prefix}paper-bg);
      --#{$prefix}menu-box-shadow: 0 0 0 1px #{$border-color};
      --#{$prefix}menu-horizontal-menu-box-shadow: var(--#{$prefix}menu-box-shadow);

      --tagify-dropdown-box-shadow: none;
      --tagify-dropdown-border-width: #{$border-width};

      .layout-menu,
      .layout-menu-horizontal {
        box-shadow: var(--#{$prefix}menu-box-shadow);
      }

      .footer {
        --#{$prefix}footer-border-width: 1px;
        --#{$prefix}footer-box-shadow: none;
      }

      // Accordion
      .accordion {
        --#{$prefix}accordion-box-shadow: none;
        --#{$prefix}accordion-active-box-shadow: none;
        --#{$prefix}accordion-border-color: #{$border-color};
        &:not(.accordion-custom-button) {
          .accordion-item {
            &.active,
            &.active + .accordion-item {
              border-color: var(--#{$prefix}border-color);
            }
            &:has(+ .previous-active) {
              border-block-end-color: var(--#{$prefix}paper-bg);
            }
          }
          &:not(:has(.accordion-item.active)) .accordion-item:not(:last-of-type) {
            border-block-end-color: var(--#{$prefix}paper-bg);
          }
        }
      }

      // Button
      .btn {
        --#{$prefix}btn-box-shadow: none;
        --#{$prefix}btn-focus-box-shadow: none;
        --#{$prefix}btn-active-shadow: none;
      }

      // Dropdown
      .dropdown-menu {
        --#{$prefix}dropdown-box-shadow: none;
        --#{$prefix}dropdown-border-width: #{$border-width};
      }

      // modal
      .modal .modal-content{
        --#{$prefix}modal-box-shadow: none;
        --#{$prefix}modal-border-width: #{$border-width};
      }

      // offcanvas
      .offcanvas {
        --#{$prefix}offcanvas-box-shadow: none;
        --#{$prefix}offcanvas-border-width: #{$border-width};
      }
      :dir(rtl) {
        .offcanvas-start {
          border-inline-end: #{$border-width} #{$border-style} var(--#{$prefix}offcanvas-border-color);
          border-inline-start: 0;
        }
        .offcanvas-end {
          border-inline-end: 0;
          border-inline-start: #{$border-width} #{$border-style} var(--#{$prefix}offcanvas-border-color);
        }
      }
      .toast,
      .bs-toast.toast {
        --#{$prefix}toast-box-shadow: 0 0 0 1px #{$border-color};
      }
      .notyf__toast {
        &.notyf__success,
        &.notyf__error,
        &.notyf__info,
        &.notyf__warning {
          box-shadow: none;
        }
      }

      // Tabs & Pills
      .nav,
      .nav-tabs-shadow {
        --#{$prefix}nav-box-shadow: none;
        --#{$prefix}nav-pills-box-shadow: none;
        --#{$prefix}nav-border-color: #{$border-color};
        ~ .tab-content {
          --#{$prefix}nav-box-shadow: 0 0 0 1px #{$border-color};
        }
      }

      // Pagination
      .pagination {
        --#{$prefix}pagination-box-shadow-color: transparent;
      }

      // Card
      .card {
        --#{$prefix}card-box-shadow: 0px 0px 0px var(--#{$prefix}border-width) var(--#{$prefix}card-border-color);
        --#{$prefix}card-hover-box-shadow: 0px 0px 0px var(--#{$prefix}border-width) var(--#{$prefix}card-border-color);
      }
      .card-group {
        --#{$prefix}card-box-shadow: none;
        .card {
          --#{$prefix}card-border-width: #{$border-width};
        }
      }

      // popover
      .popover:not(.custom-popover) {
        --#{$prefix}popover-box-shadow: none;
        --#{$prefix}popover-border-color: var(--#{$prefix}border-color);
      }

      // avatar
      .avatar {
        --#{$prefix}box-shadow: none;
      }

      // shepherd
      .shepherd-element {
        box-shadow: none;
      }

      // sweetalert2
      .swal2-container .swal2-popup {
        box-shadow: none;
      }

      // apexcharts
      .apexcharts-canvas .apexcharts-tooltip {
        box-shadow: none;
      }

      // sliders
      .noUi-target .noUi-tooltip {
        box-shadow: none;
      }

      // third-party libraries
      .ql-toolbar .ql-picker-options,
      .ql-snow .ql-tooltip,
      .flatpickr-calendar,
      .daterangepicker,
      .ui-timepicker-wrapper,
      .pcr-app,
      .bs-stepper:not(.wizard-modern),
      .bs-stepper.wizard-modern .bs-stepper-content {
        border-width: var(--#{$prefix}border-width);
        border-style: var(--#{$prefix}border-style);
        border-color: var(--#{$prefix}border-color);
        box-shadow: none;
      }

      // dropzone
      .dz-preview {
        border-width: var(--#{$prefix}border-width);
        box-shadow: none;
      }

      // Kanban
      .app-kanban .kanban-wrapper .kanban-board .kanban-item {
        border: var(--#{$prefix}border-width) solid var(--#{$prefix}border-color);
        box-shadow: none;
        &:hover {
          border-color: var(--#{$prefix}paper-bg);
        }
      }

      // select & tags
      .select2-container {
        --#{$prefix}select-dropdown-border-width: #{$border-width};
        --#{$prefix}select-dropdown-box-shadow: none;
      }

      // authentication
      .authentication-wrapper .authentication-bg {
        border-inline-start: 1px solid var(--#{$prefix}border-color);
      }

      .twitter-typeahead .tt-menu {
        border-width: #{$border-width};
        box-shadow: none;
      }
    }
  }
}

@if $enable-dark-mode {
  @include color-mode(dark, true) {
    color-scheme: dark;

    @if ($bordered-style == true){
      /* Bordered Skin Variables */
      &[data-skin="bordered"]{
        --#{$prefix}navbar-border-color: #{$border-color-dark};
        --#{$prefix}menu-box-shadow: 0 0 0 1px #{$border-color-dark};

        // Accordion
        .accordion {
          &:not([class*="accordion-border-background-"], [class*="accordion-border-solid-"], [class*="accordion-solid-"], [class*="accordion-outline-"]){
            --#{$prefix}accordion-border-color: #{$border-color-dark};
          }
        }

        // Tabs & Pills
        --#{$prefix}nav-border-color: #{$border-color-dark};
        .nav,
        .nav-tabs-shadow {
          ~ .tab-content {
            --#{$prefix}nav-box-shadow: 0 0 0 1px #{$border-color};
          }
        }
      }
    }
  }
}
