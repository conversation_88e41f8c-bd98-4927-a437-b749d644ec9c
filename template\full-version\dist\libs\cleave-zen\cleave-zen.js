!function(e,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var t=r();for(var n in t)("object"==typeof exports?exports:e)[n]=t[n]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./libs/cleave-zen/cleave-zen.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCreditCard: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.formatCreditCard; },\n/* harmony export */   formatDate: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.formatDate; },\n/* harmony export */   formatGeneral: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.formatGeneral; },\n/* harmony export */   formatNumeral: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.formatNumeral; },\n/* harmony export */   formatTime: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.formatTime; },\n/* harmony export */   getCreditCardType: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.getCreditCardType; },\n/* harmony export */   registerCursorTracker: function() { return /* reexport safe */ cleave_zen__WEBPACK_IMPORTED_MODULE_0__.registerCursorTracker; }\n/* harmony export */ });\n/* harmony import */ var cleave_zen__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cleave-zen */ "./node_modules/cleave-zen/dist/cleave-zen.module.js");\n\n\n\n//# sourceURL=webpack://Materialize/./libs/cleave-zen/cleave-zen.js?')},"./node_modules/cleave-zen/dist/cleave-zen.module.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultCreditCardDelimiter: function() { return /* binding */ d; },\n/* harmony export */   DefaultDateDelimiter: function() { return /* binding */ N; },\n/* harmony export */   DefaultNumeralDelimiter: function() { return /* binding */ A; },\n/* harmony export */   DefaultTimeDelimiter: function() { return /* binding */ D; },\n/* harmony export */   NumeralThousandGroupStyles: function() { return /* binding */ v; },\n/* harmony export */   formatCreditCard: function() { return /* binding */ E; },\n/* harmony export */   formatDate: function() { return /* binding */ y; },\n/* harmony export */   formatGeneral: function() { return /* binding */ s; },\n/* harmony export */   formatNumeral: function() { return /* binding */ M; },\n/* harmony export */   formatTime: function() { return /* binding */ w; },\n/* harmony export */   getCreditCardType: function() { return /* binding */ g; },\n/* harmony export */   registerCursorTracker: function() { return /* binding */ c; },\n/* harmony export */   unformatCreditCard: function() { return /* binding */ I; },\n/* harmony export */   unformatGeneral: function() { return /* binding */ o; },\n/* harmony export */   unformatNumeral: function() { return /* binding */ S; }\n/* harmony export */ });\nvar e,r,t,i=function(e){return e.replace(/[^\\d]/g,"")},n=function(e){return e.reduce(function(e,r){return e+r},0)},a=function(e,r){return e.slice(0,r)},l=function(e){var r=e.value;return e.delimiters.forEach(function(e){e.split("").forEach(function(e){r=r.replace(new RegExp(e.replace(/([.?*+^$[\\]\\\\(){}|-])/g,"\\\\$1"),"g"),"")})}),r},u=function(e){var r=e.blocks,t=e.delimiter,i=void 0===t?"":t,n=e.delimiters,a=void 0===n?[]:n,l=e.delimiterLazyShow,u=void 0!==l&&l,c="",s=e.value,o="";return r.forEach(function(e,t){if(s.length>0){var n,l=s.slice(0,e),d=s.slice(e);o=a.length>0?null!=(n=a[u?t-1:t])?n:o:i,u?(t>0&&(c+=o),c+=l):(c+=l,l.length===e&&t<r.length-1&&(c+=o)),s=d}}),c},c=function(e){var r=e.delimiter,t=e.delimiters,i=e.prefix,n=void 0===i?"":i,a=e.input;if(void 0!==a.CLEAVE_ZEN_cursor_tracker)return function(){a.removeEventListener("input",a.CLEAVE_ZEN_cursor_tracker),a.CLEAVE_ZEN_cursor_tracker=void 0};var u=[void 0===r?"":r].concat(void 0===t?[]:t);return a.CLEAVE_ZEN_cursor_tracker=function(e){var r,t=e.target;("deleteContentBackward"===e.inputType||t.value.length!==t.selectionEnd)&&(t.CLEAVE_ZEN_cleanCursorIndex=function(e){for(var r=e.value,t=e.dirtyCursorIndex,i=e.delimiters,n=t,a=0;a<t;a++)i.includes(r[a])&&n--;return n}({value:t.value,dirtyCursorIndex:null!=(r=t.selectionEnd)?r:0,delimiters:u}),setTimeout(function(){var e;if(l({value:t.value,delimiters:u})!==n){var r=function(e){for(var r=e.value,t=e.delimiters,i=e.cleanCursorIndex,n=0;n<r.length&&(t.includes(r[n])&&i++,n!==i-1);n++);return i}({value:t.value,cleanCursorIndex:null!=(e=t.CLEAVE_ZEN_cleanCursorIndex)?e:0,delimiters:u});t.setSelectionRange(r,r)}},0))},a.addEventListener("input",a.CLEAVE_ZEN_cursor_tracker),function(){a.removeEventListener("input",a.CLEAVE_ZEN_cursor_tracker),a.CLEAVE_ZEN_cursor_tracker=void 0}},s=function(e,r){var t=r.blocks,n=r.delimiter,a=void 0===n?"":n,c=r.delimiters,s=void 0===c?[]:c,o=r.delimiterLazyShow,d=void 0!==o&&o,v=r.prefix,m=void 0===v?"":v,f=r.numericOnly,p=void 0!==f&&f,h=r.uppercase,E=void 0!==h&&h,g=r.lowercase,I=void 0!==g&&g;return a.length>0&&s.push(a),e=function(e){var r=e.value,t=e.prefix,i=e.tailPrefix,n=t.length;return 0===n?r:r===t&&""!==r?"":r.slice(0,n)===t||i?r.slice(-n)!==t&&i?"":i?r.slice(0,-n):r.slice(n):""}({value:e=l({value:e,delimiters:s}),prefix:m,tailPrefix:!1}),e=p?i(e):e,e=E?e.toUpperCase():e,e=I?e.toLowerCase():e,m.length>0&&(e=m+e),u({value:e,blocks:t,delimiter:a,delimiters:s,delimiterLazyShow:d})},o=function(e,r){var t=r.delimiter,i=r.delimiters;return l({value:e,delimiters:[].concat(void 0===i?[]:i,[void 0===t?"":t])})},d=" ";!function(e){e.UATP="uatp",e.AMEX="amex",e.DINERS="diners",e.DISCOVER="discover",e.MASTERCARD="mastercard",e.DANKORT="dankort",e.INSTAPAYMENT="instapayment",e.JCB15="jcb15",e.JCB="jcb",e.MAESTRO="maestro",e.VISA="visa",e.MIR="mir",e.UNIONPAY="unionpay",e.GENERAL="general"}(t||(t={}));var v,m=((e={})[t.UATP]=[4,5,6],e[t.AMEX]=[4,6,5],e[t.DINERS]=[4,6,4],e[t.DISCOVER]=[4,4,4,4],e[t.MASTERCARD]=[4,4,4,4],e[t.DANKORT]=[4,4,4,4],e[t.INSTAPAYMENT]=[4,4,4,4],e[t.JCB15]=[4,6,5],e[t.JCB]=[4,4,4,4],e[t.MAESTRO]=[4,4,4,4],e[t.VISA]=[4,4,4,4],e[t.MIR]=[4,4,4,4],e[t.UNIONPAY]=[4,4,4,4],e[t.GENERAL]=[4,4,4,4],e),f=((r={})[t.UATP]=/^(?!1800)1\\d{0,14}/,r[t.AMEX]=/^3[47]\\d{0,13}/,r[t.DISCOVER]=/^(?:6011|65\\d{0,2}|64[4-9]\\d?)\\d{0,12}/,r[t.DINERS]=/^3(?:0([0-5]|9)|[689]\\d?)\\d{0,11}/,r[t.MASTERCARD]=/^(5[1-5]\\d{0,2}|22[2-9]\\d{0,1}|2[3-7]\\d{0,2})\\d{0,12}/,r[t.DANKORT]=/^(5019|4175|4571)\\d{0,12}/,r[t.INSTAPAYMENT]=/^63[7-9]\\d{0,13}/,r[t.JCB15]=/^(?:2131|1800)\\d{0,11}/,r[t.JCB]=/^(?:35\\d{0,2})\\d{0,12}/,r[t.MAESTRO]=/^(?:5[0678]\\d{0,2}|6304|67\\d{0,2})\\d{0,12}/,r[t.MIR]=/^220[0-4]\\d{0,12}/,r[t.VISA]=/^4\\d{0,15}/,r[t.UNIONPAY]=/^(62|81)\\d{0,14}/,r),p=function(e){var r=e.reduce(function(e,r){return e+r},0);return e.concat(19-r)},h=function(e){for(var r=e.value,i=e.strictMode,n=0,a=Object.keys(f);n<a.length;n++){var l=a[n];if(f[l].test(r)){var u=m[l];return{type:l,blocks:null!=i&&i?p(u):u}}}return{type:t.GENERAL,blocks:null!=i&&i?p(m.general):m.general}},E=function(e,r){var t=null!=r?r:{},c=t.delimiter,s=void 0===c?" ":c,o=t.delimiterLazyShow,d=void 0!==o&&o,v=t.strictMode,m=void 0!==v&&v;e=i(e),e=l({value:e,delimiters:[s]});var f=h({value:e,strictMode:m}).blocks,p=n(f);return e=a(e,p),u({value:e,blocks:f,delimiter:s,delimiterLazyShow:d})},g=function(e,r){return e=i(e),e=l({value:e,delimiters:[null!=r?r:" "]}),h({value:e}).type},I=function(e){return i(e)};!function(e){e.THOUSAND="thousand",e.LAKH="lakh",e.WAN="wan",e.NONE="none"}(v||(v={}));var A=",",x=v.THOUSAND,M=function(e,r){var t=null!=r?r:{},i=t.delimiter,n=t.numeralThousandsGroupStyle,a=t.numeralIntegerScale,l=t.numeralDecimalMark,u=t.numeralDecimalScale,c=t.stripLeadingZeroes,s=t.numeralPositiveOnly,o=t.tailPrefix,d=t.signBeforePrefix,m=t.prefix;return function(e){var r,t,i,n=e.delimiter,a=e.numeralDecimalMark,l=e.numeralDecimalScale,u=e.stripLeadingZeroes,c=e.numeralPositiveOnly,s=e.numeralIntegerScale,o=e.numeralThousandsGroupStyle,d=e.signBeforePrefix,m=e.tailPrefix,f=e.prefix,p="",h=e.value.replace(/[A-Za-z]/g,"").replace(a,"M").replace(/[^\\dM-]/g,"").replace(/^-/,"N").replace(/-/g,"").replace("N",null!=c&&c?"":"-").replace("M",a);u&&(h=h.replace(/^(-)?0+(?=\\d)/,"$1"));var E="-"===h.slice(0,1)?"-":"";switch(t=d?E+f:f+E,i=h,h.includes(a)&&(i=(r=h.split(a))[0],p=a+r[1].slice(0,l)),"-"===E&&(i=i.slice(1)),s>0&&(i=i.slice(0,s)),o){case v.LAKH:i=i.replace(/(\\d)(?=(\\d\\d)+\\d$)/g,"$1"+n);break;case v.WAN:i=i.replace(/(\\d)(?=(\\d{4})+$)/g,"$1"+n);break;case v.THOUSAND:i=i.replace(/(\\d)(?=(\\d{3})+$)/g,"$1"+n)}return m?E+i+(l>0?p:"")+f:t+i+(l>0?p:"")}({value:e,delimiter:void 0===i?",":i,numeralIntegerScale:void 0===a?0:a,numeralDecimalMark:void 0===l?".":l,numeralDecimalScale:void 0===u?2:u,stripLeadingZeroes:void 0===c||c,numeralPositiveOnly:void 0!==s&&s,numeralThousandsGroupStyle:void 0===n?x:n,tailPrefix:void 0!==o&&o,signBeforePrefix:void 0!==d&&d,prefix:void 0===m?"":m})},S=function(e,r){var t=(null!=r?r:{}).numeralDecimalMark;return e.replace(void 0===t?".":t,"M").replace(/[^0-9-M]/g,"").replace("M",".")},N="/",k=["d","m","Y"],C=function(e,r){return r?(e<10?"000":e<100?"00":e<1e3?"0":"")+e:(e<10?"0":"")+e},L=function(e){return(e<10?"0":"")+e},P=function(e,r,t){var i;return e=Math.min(e,31),t=null!=(i=t)?i:0,((r=Math.min(r,12))<7&&r%2==0||r>8&&r%2==1)&&(e=Math.min(e,2===r?function(e){return e%4==0&&e%100!=0||e%400==0}(t)?29:28:30)),[e,r,t]},y=function(e,r){var t=null!=r?r:{},c=t.delimiterLazyShow,s=void 0!==c&&c,o=t.delimiter,d=void 0===o?"/":o,v=t.datePattern,m=void 0===v?k:v,f=t.dateMax,p=void 0===f?"":f,h=t.dateMin,E=void 0===h?"":h;e=i(e);var g=function(e){var r=[];return e.forEach(function(e){r.push("Y"===e?4:2)}),r}(m),I=function(e){var r=e.dateMax,t=e.dateMin.split("-").reverse().map(function(e){return parseInt(e,10)});2===t.length&&t.unshift(0);var i=r.split("-").reverse().map(function(e){return parseInt(e,10)});return 2===i.length&&i.unshift(0),{min:t,max:i}}({dateMax:p,dateMin:E});e=function(e){var r=e.value,t=void 0===r?"":r,i=e.blocks,n=e.datePattern,a=e.min,l=e.max,u="";return(void 0===i?[]:i).forEach(function(e,r){if(t.length>0){var i=t.slice(0,e),a=i.slice(0,1),l=t.slice(e);switch(n[r]){case"d":"00"===i?i="01":parseInt(a,10)>3?i="0"+a:parseInt(i,10)>31&&(i="31");break;case"m":"00"===i?i="01":parseInt(a,10)>1?i="0"+a:parseInt(i,10)>12&&(i="12")}u+=i,t=l}}),function(e){var r,t,i,n=e.value,a=void 0===n?"":n,l=e.datePattern,u=e.min,c=e.max,s=[],o=0,d=0,v=0,m=0,f=0,p=0,h=!1;return 4===a.length&&"y"!==l[0].toLowerCase()&&"y"!==l[1].toLowerCase()&&(f=2-(m="d"===l[0]?0:2),r=parseInt(a.slice(m,m+2),10),t=parseInt(a.slice(f,f+2),10),s=P(r,t,0)),8===a.length&&(l.forEach(function(e,r){switch(e){case"d":o=r;break;case"m":d=r;break;default:v=r}}),p=2*v,m=o<=v?2*o:2*o+2,f=d<=v?2*d:2*d+2,r=parseInt(a.slice(m,m+2),10),t=parseInt(a.slice(f,f+2),10),i=parseInt(a.slice(p,p+4),10),h=4===a.slice(p,p+4).length,s=P(r,t,i)),4!==a.length||"y"!==l[0]&&"y"!==l[1]||(p=2-(f="m"===l[0]?0:2),t=parseInt(a.slice(f,f+2),10),i=parseInt(a.slice(p,p+2),10),h=2===a.slice(p,p+2).length,s=[0,t,i]),6!==a.length||"Y"!==l[0]&&"Y"!==l[1]||(p=2-.5*(f="m"===l[0]?0:4),t=parseInt(a.slice(f,f+2),10),i=parseInt(a.slice(p,p+4),10),h=4===a.slice(p,p+4).length,s=[0,t,i]),0===(s=function(e){var r=e.date,t=void 0===r?[]:r,i=e.min,n=e.max;return 0===t.length||i.length<3&&n.length<3||e.datePattern.filter(function(e){return"y"===e.toLowerCase()}).length>0&&0===t[2]?t:n.length>0&&(n[2]<t[2]||n[2]===t[2]&&(n[1]<t[1]||n[1]===t[1]&&n[0]<t[0]))?n:i.length>0&&(i[2]>t[2]||i[2]===t[2]&&(i[1]>t[1]||i[1]===t[1]&&i[0]>t[0]))?i:t}({date:s,datePattern:l,min:u,max:c})).length?a:l.reduce(function(e,r){switch(r){case"d":return e+(0===s[0]?"":L(s[0]));case"m":return e+(0===s[1]?"":L(s[1]));case"y":return e+(h?C(s[2],!1):"");case"Y":return e+(h?C(s[2],!0):"")}return e},"")}({value:u,datePattern:n,min:a,max:l})}({value:e,blocks:g,datePattern:m,min:I.min,max:I.max}),e=l({value:e,delimiters:[d]});var A=n(g);return e=a(e,A),u({value:e,blocks:g,delimiter:d,delimiterLazyShow:s})},D=":",R=["h","m","s"],T=function(e){return(e<10?"0":"")+e},b=function(e,r,t){return t=Math.min(t,60),r=Math.min(r,60),[e=Math.min(e,60),r,t]},w=function(e,r){var t=null!=r?r:{},c=t.delimiterLazyShow,s=void 0!==c&&c,o=t.delimiter,d=void 0===o?":":o,v=t.timePattern,m=void 0===v?R:v,f=t.timeFormat,p=void 0===f?"24":f;e=i(e);var h=function(e){var r=[];return e.forEach(function(){r.push(2)}),r}(m);e=function(e){var r=e.value,t=e.timePattern,i="",n="12"===e.timeFormat?{maxHourFirstDigit:1,maxHours:12,maxMinutesFirstDigit:5,maxMinutes:60}:{maxHourFirstDigit:2,maxHours:23,maxMinutesFirstDigit:5,maxMinutes:60};return e.blocks.forEach(function(e,a){if(r.length>0){var l=r.slice(0,e),u=l.slice(0,1),c=r.slice(e);switch(t[a]){case"h":parseInt(u,10)>n.maxHourFirstDigit?l="0"+u:parseInt(l,10)>n.maxHours&&(l=n.maxHours+"");break;case"m":case"s":parseInt(u,10)>n.maxMinutesFirstDigit?l="0"+u:parseInt(l,10)>n.maxMinutes&&(l=n.maxMinutes+"")}i+=l,r=c}}),function(e){var r,t,i,n=e.value,a=e.timePattern,l=[],u=0,c=0,s=0,o=0,d=0,v=0;return 6===n.length&&(a.forEach(function(e,r){switch(e){case"s":u=2*r;break;case"m":c=2*r;break;case"h":s=2*r}}),v=s,d=c,o=u,r=parseInt(n.slice(o,o+2),10),t=parseInt(n.slice(d,d+2),10),i=parseInt(n.slice(v,v+2),10),l=b(i,t,r)),4!==n.length||a.includes("s")||(a.forEach(function(e,r){switch(e){case"m":c=2*r;break;case"h":s=2*r}}),v=s,d=c,r=0,t=parseInt(n.slice(d,d+2),10),i=parseInt(n.slice(v,v+2),10),l=b(i,t,r)),0===l.length?n:a.reduce(function(e,r){switch(r){case"s":return e+T(l[2]);case"m":return e+T(l[1]);case"h":return e+T(l[0])}return e},"")}({value:i,timePattern:t})}({value:e,blocks:h,timePattern:m,timeFormat:p}),e=l({value:e,delimiters:[d]});var E=n(h);return e=a(e,E),u({value:e,blocks:h,delimiter:d,delimiterLazyShow:s})};\n//# sourceMappingURL=cleave-zen.module.js.map\n\n\n//# sourceURL=webpack://Materialize/./node_modules/cleave-zen/dist/cleave-zen.module.js?')}},__webpack_module_cache__={};function __webpack_require__(e){var r=__webpack_module_cache__[e];if(void 0!==r)return r.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.d=function(e,r){for(var t in r)__webpack_require__.o(r,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},__webpack_require__.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/cleave-zen/cleave-zen.js");return __webpack_exports__}()}));