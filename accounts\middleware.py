"""
Middleware pour l'isolation des données par organisation
"""
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from organisations.models import MembreOrganisation


class OrganisationMiddleware:
    """
    Middleware qui gère l'isolation des données par organisation.
    
    Ce middleware :
    1. Vérifie que l'utilisateur connecté appartient à une organisation
    2. Stocke l'organisation active dans la session
    3. Fournit l'organisation dans le contexte des templates
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # URLs qui ne nécessitent pas d'organisation
        self.public_urls = [
            '/',
            '/login/',
            '/logout/',
            '/onboarding/',
            '/onboarding/confirmation/',
            '/admin/',
        ]
    
    def __call__(self, request):
        # Traitement avant la vue
        self.process_request(request)
        
        # Appel de la vue
        response = self.get_response(request)
        
        return response
    
    def process_request(self, request):
        """Traite la requête avant qu'elle n'atteigne la vue"""
        
        # Ignorer les requêtes pour les fichiers statiques et admin
        if (request.path.startswith('/static/') or 
            request.path.startswith('/media/') or
            request.path.startswith('/admin/') or
            not request.user.is_authenticated):
            return
        
        # Ignorer les URLs publiques
        if any(request.path.startswith(url) for url in self.public_urls):
            return
        
        # Récupérer l'organisation de l'utilisateur
        try:
            membre = MembreOrganisation.objects.select_related('organisation').get(
                utilisateur=request.user,
                actif=True
            )
            
            # Stocker l'organisation dans la session et la requête
            request.session['organisation_id'] = membre.organisation.id
            request.organisation = membre.organisation
            request.membre_organisation = membre
            
        except MembreOrganisation.DoesNotExist:
            # L'utilisateur n'appartient à aucune organisation active
            messages.error(
                request,
                "Votre compte n'est associé à aucune organisation active. "
                "Contactez l'administrateur."
            )
            return redirect('accounts:logout')
        
        except MembreOrganisation.MultipleObjectsReturned:
            # L'utilisateur appartient à plusieurs organisations
            # Pour l'instant, on prend la première (plus tard on pourra implémenter un sélecteur)
            membre = MembreOrganisation.objects.select_related('organisation').filter(
                utilisateur=request.user,
                actif=True
            ).first()
            
            request.session['organisation_id'] = membre.organisation.id
            request.organisation = membre.organisation
            request.membre_organisation = membre


def organisation_context_processor(request):
    """
    Context processor qui ajoute l'organisation dans le contexte des templates
    """
    context = {}
    
    if hasattr(request, 'organisation'):
        context['organisation_active'] = request.organisation
        context['membre_organisation'] = request.membre_organisation
        context['est_admin_organisation'] = (
            request.membre_organisation.role == MembreOrganisation.RoleMembre.ADMIN
            if hasattr(request, 'membre_organisation') else False
        )
    
    return context


class OrganisationQuerySetMixin:
    """
    Mixin pour filtrer automatiquement les QuerySets par organisation
    """
    
    def get_queryset(self):
        """Filtre le QuerySet par l'organisation de l'utilisateur"""
        queryset = super().get_queryset()
        
        # Vérifier si le modèle a un champ organisation
        if hasattr(self.model, 'organisation'):
            if hasattr(self.request, 'organisation'):
                return queryset.filter(organisation=self.request.organisation)
            else:
                # Si pas d'organisation, retourner un QuerySet vide
                return queryset.none()
        
        return queryset


def require_organisation(view_func):
    """
    Décorateur qui s'assure qu'une organisation est définie pour la vue
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'organisation'):
            messages.error(
                request,
                "Accès refusé. Aucune organisation active."
            )
            return redirect('accounts:login')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def require_admin_organisation(view_func):
    """
    Décorateur qui s'assure que l'utilisateur est admin de son organisation
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'membre_organisation'):
            messages.error(
                request,
                "Accès refusé. Aucune organisation active."
            )
            return redirect('accounts:login')
        
        if request.membre_organisation.role != MembreOrganisation.RoleMembre.ADMIN:
            messages.error(
                request,
                "Accès refusé. Vous devez être administrateur de votre organisation."
            )
            return redirect('accounts:dashboard')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


class OrganisationModelMixin:
    """
    Mixin pour les modèles qui appartiennent à une organisation
    """
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        Sauvegarde en s'assurant que l'organisation est définie
        """
        # Si l'organisation n'est pas définie et qu'on a accès à la requête
        if not hasattr(self, 'organisation') or not self.organisation:
            from django.core.exceptions import ValidationError
            raise ValidationError("L'organisation doit être définie pour ce modèle.")
        
        super().save(*args, **kwargs)
    
    @classmethod
    def objects_for_organisation(cls, organisation):
        """
        Retourne les objets filtrés par organisation
        """
        return cls.objects.filter(organisation=organisation)


def get_organisation_from_request(request):
    """
    Utilitaire pour récupérer l'organisation depuis la requête
    """
    if hasattr(request, 'organisation'):
        return request.organisation
    
    if request.user.is_authenticated:
        try:
            membre = MembreOrganisation.objects.select_related('organisation').get(
                utilisateur=request.user,
                actif=True
            )
            return membre.organisation
        except (MembreOrganisation.DoesNotExist, MembreOrganisation.MultipleObjectsReturned):
            pass
    
    return None
