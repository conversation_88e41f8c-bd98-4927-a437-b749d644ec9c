/* Default */
.ui-timepicker-wrapper {
  overflow-y: auto;
  max-height: 150px;
  width: auto;
  background: #fff;
  border: 1px solid #ddd;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  outline: none;
  z-index: 10052;
  margin: 0;
}

.ui-timepicker-wrapper .ui-timepicker-list li {
  padding-right: 20px;
}

.ui-timepicker-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.ui-timepicker-duration {
  margin-left: 5px;
  color: #888;
}

.ui-timepicker-list:hover .ui-timepicker-duration {
  color: #888;
}

.ui-timepicker-list li {
  padding: 3px 0 3px 5px;
  cursor: pointer;
  white-space: nowrap;
  color: #000;
  list-style: none;
  margin: 0;
}

.ui-timepicker-list:hover .ui-timepicker-selected {
  background: #fff;
  color: #000;
}

li.ui-timepicker-selected,
.ui-timepicker-list li:hover,
.ui-timepicker-list .ui-timepicker-selected:hover {
  background: #1980EC;
  color: #fff;
}

li.ui-timepicker-selected .ui-timepicker-duration,
.ui-timepicker-list li:hover .ui-timepicker-duration {
  color: #ccc;
}

.ui-timepicker-list li.ui-timepicker-disabled,
.ui-timepicker-list li.ui-timepicker-disabled:hover,
.ui-timepicker-list li.ui-timepicker-selected.ui-timepicker-disabled {
  color: #888;
  cursor: default;
}

.ui-timepicker-list li.ui-timepicker-disabled:hover,
.ui-timepicker-list li.ui-timepicker-selected.ui-timepicker-disabled {
  background: #f2f2f2;
}

.ui-timepicker-wrapper {
  z-index: 1074;
  border-color: var(--bs-border-color);
  background: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow-lg);
  margin-block: 0.125rem;
  padding-block: 0.5rem;
  padding-inline: 0.5rem;
  border-radius: 0.375rem;
}
.ui-timepicker-wrapper .ui-timepicker-duration {
  margin-inline-start: 0.25rem;
}
.ui-timepicker-wrapper .ui-timepicker-list li {
  color: var(--bs-body-color);
  cursor: pointer;
  list-style: none;
  margin-block: 0.2rem;
  margin-inline: 0.75rem;
  padding-block: 0.4rem;
  padding-inline: 0.75rem;
  white-space: nowrap;
  border-radius: 0.5rem;
}
.ui-timepicker-wrapper .ui-timepicker-list li:hover {
  background: var(--bs-gray-50);
}
.ui-timepicker-wrapper .ui-timepicker-list li:not(.ui-timepicker-selected) .ui-timepicker-duration {
  color: var(--bs-secondary-color);
}
.ui-timepicker-wrapper .ui-timepicker-list li.ui-timepicker-selected,
.ui-timepicker-wrapper .ui-timepicker-list li .ui-timepicker-selected:hover {
  background: var(--bs-primary);
  color: var(--bs-primary-contrast);
}
.ui-timepicker-wrapper .ui-timepicker-list li.ui-timepicker-selected .ui-timepicker-duration,
.ui-timepicker-wrapper .ui-timepicker-list li .ui-timepicker-selected:hover .ui-timepicker-duration {
  color: var(--bs-primary-contrast);
}
.ui-timepicker-wrapper .ui-timepicker-list li.ui-timepicker-disabled, .ui-timepicker-wrapper .ui-timepicker-list li.ui-timepicker-selected.ui-timepicker-disabled {
  color: var(--bs-secondary-color);
  pointer-events: none;
}
