!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var o in t)("object"==typeof exports?exports:e)[o]=t[o]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./libs/shepherd/shepherd.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Shepherd: function() { return /* reexport safe */ shepherd_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }\n/* harmony export */ });\n/* harmony import */ var shepherd_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! shepherd.js */ "./node_modules/shepherd.js/dist/esm/shepherd.mjs");\n\ntry {\n  window.Shepherd = shepherd_js__WEBPACK_IMPORTED_MODULE_0__["default"];\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/shepherd/shepherd.js?')},"./node_modules/shepherd.js/dist/esm/shepherd.mjs":function(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShepherdBase: function() { return /* binding */ ShepherdBase; },\n/* harmony export */   \"default\": function() { return /* binding */ Shepherd; }\n/* harmony export */ });\n/*! shepherd.js 14.3.0 */\n\n/**\n * Checks if `value` is classified as an `Element`.\n * @param value The param to check if it is an Element\n */\nfunction isElement$1(value) {\n  return value instanceof Element;\n}\n\n/**\n * Checks if `value` is classified as an `HTMLElement`.\n * @param value The param to check if it is an HTMLElement\n */\nfunction isHTMLElement$1(value) {\n  return value instanceof HTMLElement;\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n * @param value The param to check if it is a function\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\n/**\n * Checks if `value` is classified as a `String` object.\n * @param value The param to check if it is a string\n */\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\n/**\n * Checks if `value` is undefined.\n * @param value The param to check if it is undefined\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\nclass Evented {\n  /**\n   * Adds an event listener for the given event string.\n   *\n   * @param {string} event\n   * @param {Function} handler\n   * @param ctx\n   * @param {boolean} once\n   * @returns\n   */\n  on(event, handler, ctx, once = false) {\n    var _this$bindings$event;\n    if (isUndefined(this.bindings)) {\n      this.bindings = {};\n    }\n    if (isUndefined(this.bindings[event])) {\n      this.bindings[event] = [];\n    }\n    (_this$bindings$event = this.bindings[event]) == null || _this$bindings$event.push({\n      handler,\n      ctx,\n      once\n    });\n    return this;\n  }\n\n  /**\n   * Adds an event listener that only fires once for the given event string.\n   *\n   * @param {string} event\n   * @param {Function} handler\n   * @param ctx\n   * @returns\n   */\n  once(event, handler, ctx) {\n    return this.on(event, handler, ctx, true);\n  }\n\n  /**\n   * Removes an event listener for the given event string.\n   *\n   * @param {string} event\n   * @param {Function} handler\n   * @returns\n   */\n  off(event, handler) {\n    if (isUndefined(this.bindings) || isUndefined(this.bindings[event])) {\n      return this;\n    }\n    if (isUndefined(handler)) {\n      delete this.bindings[event];\n    } else {\n      var _this$bindings$event2;\n      (_this$bindings$event2 = this.bindings[event]) == null || _this$bindings$event2.forEach((binding, index) => {\n        if (binding.handler === handler) {\n          var _this$bindings$event3;\n          (_this$bindings$event3 = this.bindings[event]) == null || _this$bindings$event3.splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Triggers an event listener for the given event string.\n   *\n   * @param {string} event\n   * @returns\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  trigger(event, ...args) {\n    if (!isUndefined(this.bindings) && this.bindings[event]) {\n      var _this$bindings$event4;\n      (_this$bindings$event4 = this.bindings[event]) == null || _this$bindings$event4.forEach((binding, index) => {\n        const {\n          ctx,\n          handler,\n          once\n        } = binding;\n        const context = ctx || this;\n        handler.apply(context, args);\n        if (once) {\n          var _this$bindings$event5;\n          (_this$bindings$event5 = this.bindings[event]) == null || _this$bindings$event5.splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n}\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n\n/**\n * Special values that tell deepmerge to perform a certain action.\n */\nconst actions = {\n  defaultMerge: Symbol(\"deepmerge-ts: default merge\"),\n  skip: Symbol(\"deepmerge-ts: skip\")\n};\n/**\n * Special values that tell deepmergeInto to perform a certain action.\n */\n({\n  defaultMerge: actions.defaultMerge\n});\n\n/**\n * The default function to update meta data.\n *\n * It doesn't update the meta data.\n */\nfunction defaultMetaDataUpdater(previousMeta, metaMeta) {\n  return metaMeta;\n}\n/**\n * The default function to filter values.\n *\n * It filters out undefined values.\n */\nfunction defaultFilterValues(values, meta) {\n  return values.filter(value => value !== undefined);\n}\n\n/**\n * The different types of objects deepmerge-ts support.\n */\nvar ObjectType;\n(function (ObjectType) {\n  ObjectType[ObjectType[\"NOT\"] = 0] = \"NOT\";\n  ObjectType[ObjectType[\"RECORD\"] = 1] = \"RECORD\";\n  ObjectType[ObjectType[\"ARRAY\"] = 2] = \"ARRAY\";\n  ObjectType[ObjectType[\"SET\"] = 3] = \"SET\";\n  ObjectType[ObjectType[\"MAP\"] = 4] = \"MAP\";\n  ObjectType[ObjectType[\"OTHER\"] = 5] = \"OTHER\";\n})(ObjectType || (ObjectType = {}));\n/**\n * Get the type of the given object.\n *\n * @param object - The object to get the type of.\n * @returns The type of the given object.\n */\nfunction getObjectType(object) {\n  if (typeof object !== \"object\" || object === null) {\n    return 0 /* ObjectType.NOT */;\n  }\n  if (Array.isArray(object)) {\n    return 2 /* ObjectType.ARRAY */;\n  }\n  if (isRecord(object)) {\n    return 1 /* ObjectType.RECORD */;\n  }\n  if (object instanceof Set) {\n    return 3 /* ObjectType.SET */;\n  }\n  if (object instanceof Map) {\n    return 4 /* ObjectType.MAP */;\n  }\n  return 5 /* ObjectType.OTHER */;\n}\n/**\n * Get the keys of the given objects including symbol keys.\n *\n * Note: Only keys to enumerable properties are returned.\n *\n * @param objects - An array of objects to get the keys of.\n * @returns A set containing all the keys of all the given objects.\n */\nfunction getKeys(objects) {\n  const keys = new Set();\n  for (const object of objects) {\n    for (const key of [...Object.keys(object), ...Object.getOwnPropertySymbols(object)]) {\n      keys.add(key);\n    }\n  }\n  return keys;\n}\n/**\n * Does the given object have the given property.\n *\n * @param object - The object to test.\n * @param property - The property to test.\n * @returns Whether the object has the property.\n */\nfunction objectHasProperty(object, property) {\n  return typeof object === \"object\" && Object.prototype.propertyIsEnumerable.call(object, property);\n}\n/**\n * Get an iterable object that iterates over the given iterables.\n */\nfunction getIterableOfIterables(iterables) {\n  return {\n    *[Symbol.iterator]() {\n      for (const iterable of iterables) {\n        for (const value of iterable) {\n          yield value;\n        }\n      }\n    }\n  };\n}\nconst validRecordToStringValues = new Set([\"[object Object]\", \"[object Module]\"]);\n/**\n * Does the given object appear to be a record.\n */\nfunction isRecord(value) {\n  // All records are objects.\n  if (!validRecordToStringValues.has(Object.prototype.toString.call(value))) {\n    return false;\n  }\n  const {\n    constructor\n  } = value;\n  // If has modified constructor.\n  // eslint-disable-next-line ts/no-unnecessary-condition\n  if (constructor === undefined) {\n    return true;\n  }\n  const prototype = constructor.prototype;\n  // If has modified prototype.\n  if (prototype === null || typeof prototype !== \"object\" || !validRecordToStringValues.has(Object.prototype.toString.call(prototype))) {\n    return false;\n  }\n  // If constructor does not have an Object-specific method.\n  // eslint-disable-next-line sonar/prefer-single-boolean-return, no-prototype-builtins\n  if (!prototype.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  // Most likely a record.\n  return true;\n}\n\n/**\n * The default strategy to merge records.\n *\n * @param values - The records.\n */\nfunction mergeRecords$1(values, utils, meta) {\n  const result = {};\n  for (const key of getKeys(values)) {\n    const propValues = [];\n    for (const value of values) {\n      if (objectHasProperty(value, key)) {\n        propValues.push(value[key]);\n      }\n    }\n    if (propValues.length === 0) {\n      continue;\n    }\n    const updatedMeta = utils.metaDataUpdater(meta, {\n      key,\n      parents: values\n    });\n    const propertyResult = mergeUnknowns(propValues, utils, updatedMeta);\n    if (propertyResult === actions.skip) {\n      continue;\n    }\n    if (key === \"__proto__\") {\n      Object.defineProperty(result, key, {\n        value: propertyResult,\n        configurable: true,\n        enumerable: true,\n        writable: true\n      });\n    } else {\n      result[key] = propertyResult;\n    }\n  }\n  return result;\n}\n/**\n * The default strategy to merge arrays.\n *\n * @param values - The arrays.\n */\nfunction mergeArrays$1(values) {\n  return values.flat();\n}\n/**\n * The default strategy to merge sets.\n *\n * @param values - The sets.\n */\nfunction mergeSets$1(values) {\n  return new Set(getIterableOfIterables(values));\n}\n/**\n * The default strategy to merge maps.\n *\n * @param values - The maps.\n */\nfunction mergeMaps$1(values) {\n  return new Map(getIterableOfIterables(values));\n}\n/**\n * Get the last non-undefined value in the given array.\n */\nfunction mergeOthers$1(values) {\n  return values.at(-1);\n}\n/**\n * The merge functions.\n */\nconst mergeFunctions = {\n  mergeRecords: mergeRecords$1,\n  mergeArrays: mergeArrays$1,\n  mergeSets: mergeSets$1,\n  mergeMaps: mergeMaps$1,\n  mergeOthers: mergeOthers$1\n};\n\n/**\n * Deeply merge objects.\n *\n * @param objects - The objects to merge.\n */\nfunction deepmerge(...objects) {\n  return deepmergeCustom({})(...objects);\n}\nfunction deepmergeCustom(options, rootMetaData) {\n  const utils = getUtils(options, customizedDeepmerge);\n  /**\n   * The customized deepmerge function.\n   */\n  function customizedDeepmerge(...objects) {\n    return mergeUnknowns(objects, utils, rootMetaData);\n  }\n  return customizedDeepmerge;\n}\n/**\n * The the utils that are available to the merge functions.\n *\n * @param options - The options the user specified\n */\nfunction getUtils(options, customizedDeepmerge) {\n  var _options$metaDataUpda, _options$enableImplic, _options$filterValues;\n  return {\n    defaultMergeFunctions: mergeFunctions,\n    mergeFunctions: _extends({}, mergeFunctions, Object.fromEntries(Object.entries(options).filter(([key, option]) => Object.hasOwn(mergeFunctions, key)).map(([key, option]) => option === false ? [key, mergeFunctions.mergeOthers] : [key, option]))),\n    metaDataUpdater: (_options$metaDataUpda = options.metaDataUpdater) != null ? _options$metaDataUpda : defaultMetaDataUpdater,\n    deepmerge: customizedDeepmerge,\n    useImplicitDefaultMerging: (_options$enableImplic = options.enableImplicitDefaultMerging) != null ? _options$enableImplic : false,\n    filterValues: options.filterValues === false ? undefined : (_options$filterValues = options.filterValues) != null ? _options$filterValues : defaultFilterValues,\n    actions\n  };\n}\n/**\n * Merge unknown things.\n *\n * @param values - The values.\n */\nfunction mergeUnknowns(values, utils, meta) {\n  var _utils$filterValues;\n  const filteredValues = (_utils$filterValues = utils.filterValues == null ? void 0 : utils.filterValues(values, meta)) != null ? _utils$filterValues : values;\n  if (filteredValues.length === 0) {\n    return undefined;\n  }\n  if (filteredValues.length === 1) {\n    return mergeOthers(filteredValues, utils, meta);\n  }\n  const type = getObjectType(filteredValues[0]);\n  if (type !== 0 /* ObjectType.NOT */ && type !== 5 /* ObjectType.OTHER */) {\n    for (let m_index = 1; m_index < filteredValues.length; m_index++) {\n      if (getObjectType(filteredValues[m_index]) === type) {\n        continue;\n      }\n      return mergeOthers(filteredValues, utils, meta);\n    }\n  }\n  switch (type) {\n    case 1 /* ObjectType.RECORD */:\n      {\n        return mergeRecords(filteredValues, utils, meta);\n      }\n    case 2 /* ObjectType.ARRAY */:\n      {\n        return mergeArrays(filteredValues, utils, meta);\n      }\n    case 3 /* ObjectType.SET */:\n      {\n        return mergeSets(filteredValues, utils, meta);\n      }\n    case 4 /* ObjectType.MAP */:\n      {\n        return mergeMaps(filteredValues, utils, meta);\n      }\n    default:\n      {\n        return mergeOthers(filteredValues, utils, meta);\n      }\n  }\n}\n/**\n * Merge records.\n *\n * @param values - The records.\n */\nfunction mergeRecords(values, utils, meta) {\n  const result = utils.mergeFunctions.mergeRecords(values, utils, meta);\n  if (result === actions.defaultMerge || utils.useImplicitDefaultMerging && result === undefined && utils.mergeFunctions.mergeRecords !== utils.defaultMergeFunctions.mergeRecords) {\n    return utils.defaultMergeFunctions.mergeRecords(values, utils, meta);\n  }\n  return result;\n}\n/**\n * Merge arrays.\n *\n * @param values - The arrays.\n */\nfunction mergeArrays(values, utils, meta) {\n  const result = utils.mergeFunctions.mergeArrays(values, utils, meta);\n  if (result === actions.defaultMerge || utils.useImplicitDefaultMerging && result === undefined && utils.mergeFunctions.mergeArrays !== utils.defaultMergeFunctions.mergeArrays) {\n    return utils.defaultMergeFunctions.mergeArrays(values);\n  }\n  return result;\n}\n/**\n * Merge sets.\n *\n * @param values - The sets.\n */\nfunction mergeSets(values, utils, meta) {\n  const result = utils.mergeFunctions.mergeSets(values, utils, meta);\n  if (result === actions.defaultMerge || utils.useImplicitDefaultMerging && result === undefined && utils.mergeFunctions.mergeSets !== utils.defaultMergeFunctions.mergeSets) {\n    return utils.defaultMergeFunctions.mergeSets(values);\n  }\n  return result;\n}\n/**\n * Merge maps.\n *\n * @param values - The maps.\n */\nfunction mergeMaps(values, utils, meta) {\n  const result = utils.mergeFunctions.mergeMaps(values, utils, meta);\n  if (result === actions.defaultMerge || utils.useImplicitDefaultMerging && result === undefined && utils.mergeFunctions.mergeMaps !== utils.defaultMergeFunctions.mergeMaps) {\n    return utils.defaultMergeFunctions.mergeMaps(values);\n  }\n  return result;\n}\n/**\n * Merge other things.\n *\n * @param values - The other things.\n */\nfunction mergeOthers(values, utils, meta) {\n  const result = utils.mergeFunctions.mergeOthers(values, utils, meta);\n  if (result === actions.defaultMerge || utils.useImplicitDefaultMerging && result === undefined && utils.mergeFunctions.mergeOthers !== utils.defaultMergeFunctions.mergeOthers) {\n    return utils.defaultMergeFunctions.mergeOthers(values);\n  }\n  return result;\n}\n\n/**\n * Binds all the methods on a JS Class to the `this` context of the class.\n * Adapted from https://github.com/sindresorhus/auto-bind\n * @param self The `this` context of the class\n * @return The `this` context of the class\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction autoBind(self) {\n  const keys = Object.getOwnPropertyNames(self.constructor.prototype);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const val = self[key];\n    if (key !== 'constructor' && typeof val === 'function') {\n      self[key] = val.bind(self);\n    }\n  }\n  return self;\n}\n\n/**\n * Sets up the handler to determine if we should advance the tour\n * @param step The step instance\n * @param selector\n * @private\n */\nfunction _setupAdvanceOnHandler(step, selector) {\n  return event => {\n    if (step.isOpen()) {\n      const targetIsEl = step.el && event.currentTarget === step.el;\n      const targetIsSelector = !isUndefined(selector) && event.currentTarget.matches(selector);\n      if (targetIsSelector || targetIsEl) {\n        step.tour.next();\n      }\n    }\n  };\n}\n\n/**\n * Bind the event handler for advanceOn\n * @param step The step instance\n */\nfunction bindAdvance(step) {\n  // An empty selector matches the step element\n  const {\n    event,\n    selector\n  } = step.options.advanceOn || {};\n  if (event) {\n    const handler = _setupAdvanceOnHandler(step, selector);\n\n    // TODO: this should also bind/unbind on show/hide\n    let el = null;\n    if (!isUndefined(selector)) {\n      el = document.querySelector(selector);\n      if (!el) {\n        return console.error(`No element was found for the selector supplied to advanceOn: ${selector}`);\n      }\n    }\n    if (el) {\n      el.addEventListener(event, handler);\n      step.on('destroy', () => {\n        return el.removeEventListener(event, handler);\n      });\n    } else {\n      document.body.addEventListener(event, handler, true);\n      step.on('destroy', () => {\n        return document.body.removeEventListener(event, handler, true);\n      });\n    }\n  } else {\n    return console.error('advanceOn was defined, but no event name was passed.');\n  }\n}\n\nclass StepNoOp {\n  constructor(_options) {}\n}\nclass TourNoOp {\n  constructor(_tour, _options) {}\n}\n\n/**\n * Ensure class prefix ends in `-`\n * @param prefix - The prefix to prepend to the class names generated by nano-css\n * @return The prefix ending in `-`\n */\nfunction normalizePrefix(prefix) {\n  if (!isString(prefix) || prefix === '') {\n    return '';\n  }\n  return prefix.charAt(prefix.length - 1) !== '-' ? `${prefix}-` : prefix;\n}\n\n/**\n * Resolves attachTo options, converting element option value to a qualified HTMLElement.\n * @param step - The step instance\n * @returns {{}|{element, on}}\n * `element` is a qualified HTML Element\n * `on` is a string position value\n */\nfunction parseAttachTo(step) {\n  const options = step.options.attachTo || {};\n  const returnOpts = Object.assign({}, options);\n  if (isFunction(returnOpts.element)) {\n    // Bind the callback to step so that it has access to the object, to enable running additional logic\n    returnOpts.element = returnOpts.element.call(step);\n  }\n  if (isString(returnOpts.element)) {\n    // Can't override the element in user opts reference because we can't\n    // guarantee that the element will exist in the future.\n    try {\n      returnOpts.element = document.querySelector(returnOpts.element);\n    } catch (e) {\n      // TODO\n    }\n    if (!returnOpts.element) {\n      console.error(`The element for this Shepherd step was not found ${options.element}`);\n    }\n  }\n  return returnOpts;\n}\n\n/*\n * Resolves the step's `extraHighlights` option, converting any locator values to HTMLElements.\n */\nfunction parseExtraHighlights(step) {\n  if (step.options.extraHighlights) {\n    return step.options.extraHighlights.flatMap(highlight => {\n      return Array.from(document.querySelectorAll(highlight));\n    });\n  }\n  return [];\n}\n\n/**\n * Checks if the step should be centered or not. Does not trigger attachTo.element evaluation, making it a pure\n * alternative for the deprecated step.isCentered() method.\n */\nfunction shouldCenterStep(resolvedAttachToOptions) {\n  if (resolvedAttachToOptions === undefined || resolvedAttachToOptions === null) {\n    return true;\n  }\n  return !resolvedAttachToOptions.element || !resolvedAttachToOptions.on;\n}\n\n/**\n * Create a unique id for steps, tours, modals, etc\n */\nfunction uuid() {\n  let d = Date.now();\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (d + Math.random() * 16) % 16 | 0;\n    d = Math.floor(d / 16);\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n}\n\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return _extends({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, padding);\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nconst _excluded = [\"crossAxis\", \"alignment\", \"allowedPlacements\", \"autoAlignment\"],\n  _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"];\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition$1 = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = _extends({}, middlewareData, {\n      [name]: _extends({}, middlewareData[name], data)\n    });\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: _extends({\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset\n      }, shouldAddOffset && {\n        alignmentOffset\n      }),\n      reset: shouldAddOffset\n    };\n  }\n});\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement$1 = function autoPlacement(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const _evaluate = evaluate(options, state),\n        {\n          crossAxis = false,\n          alignment,\n          allowedPlacements = placements,\n          autoAlignment = true\n        } = _evaluate,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate, _excluded);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip$1 = function flip(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const _evaluate2 = evaluate(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = true,\n          fallbackPlacements: specifiedFallbackPlacements,\n          fallbackStrategy = 'bestFit',\n          fallbackAxisSideDirection = 'none',\n          flipAlignment = true\n        } = _evaluate2,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate2, _excluded2);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift$1 = function shift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const _evaluate4 = evaluate(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = false,\n          limiter = {\n            fn: _ref => {\n              let {\n                x,\n                y\n              } = _ref;\n              return {\n                x,\n                y\n              };\n            }\n          }\n        } = _evaluate4,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(_evaluate4, _excluded4);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn(_extends({}, state, {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      }));\n      return _extends({}, limitedCoords, {\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y\n        }\n      });\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift$1 = function limitShift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _extends({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = currentWin.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = currentWin.frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = _extends({}, clippingAncestor, {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    });\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  const x = rect.left + scroll.scrollLeft - offsets.x;\n  const y = rect.top + scroll.scrollTop - offsets.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\nconst getElementRects = async function getElementRects(data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, _extends({}, options, {\n        // Handle <iframe>s\n        root: root.ownerDocument\n      }));\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = _extends({\n    platform\n  }, options);\n  const platformWithCache = _extends({}, mergedOptions.platform, {\n    _c: cache\n  });\n  return computePosition$1(reference, floating, _extends({}, mergedOptions, {\n    platform: platformWithCache\n  }));\n};\n\n/**\n * Determines options for the tooltip and initializes event listeners.\n *\n * @param step The step instance\n */\nfunction setupTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  const attachToOptions = step._getResolvedAttachToOptions();\n  let target = attachToOptions.element;\n  const floatingUIOptions = getFloatingUIOptions(attachToOptions, step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (shouldCenter) {\n    target = document.body;\n    // @ts-expect-error TODO: fix this type error when we type Svelte\n    const content = step.shepherdElementComponent.getElement();\n    content.classList.add('shepherd-centered');\n  }\n  step.cleanup = autoUpdate(target, step.el, () => {\n    // The element might have already been removed by the end of the tour.\n    if (!step.el) {\n      step.cleanup == null || step.cleanup();\n      return;\n    }\n    setPosition(target, step, floatingUIOptions, shouldCenter);\n  });\n  step.target = attachToOptions.element;\n  return floatingUIOptions;\n}\n\n/**\n * Merge tooltip options handling nested keys.\n *\n * @param tourOptions - The default tour options.\n * @param options - Step specific options.\n *\n * @return {floatingUIOptions: FloatingUIOptions}\n */\nfunction mergeTooltipConfig(tourOptions, options) {\n  return {\n    floatingUIOptions: deepmerge(tourOptions.floatingUIOptions || {}, options.floatingUIOptions || {})\n  };\n}\n\n/**\n * Cleanup function called when the step is closed/destroyed.\n *\n * @param step\n */\nfunction destroyTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  step.cleanup = null;\n}\nfunction setPosition(target, step, floatingUIOptions, shouldCenter) {\n  return computePosition(target, step.el, floatingUIOptions).then(floatingUIposition(step, shouldCenter))\n  // Wait before forcing focus.\n  .then(step => new Promise(resolve => {\n    setTimeout(() => resolve(step), 300);\n  }))\n  // Replaces focusAfterRender modifier.\n  .then(step => {\n    if (step != null && step.el) {\n      step.el.focus({\n        preventScroll: true\n      });\n    }\n  });\n}\nfunction floatingUIposition(step, shouldCenter) {\n  return ({\n    x,\n    y,\n    placement,\n    middlewareData\n  }) => {\n    if (!step.el) {\n      return step;\n    }\n    if (shouldCenter) {\n      Object.assign(step.el.style, {\n        position: 'fixed',\n        left: '50%',\n        top: '50%',\n        transform: 'translate(-50%, -50%)'\n      });\n    } else {\n      Object.assign(step.el.style, {\n        position: 'absolute',\n        left: `${x}px`,\n        top: `${y}px`\n      });\n    }\n    step.el.dataset['popperPlacement'] = placement;\n    placeArrow(step.el, middlewareData);\n    return step;\n  };\n}\nfunction placeArrow(el, middlewareData) {\n  const arrowEl = el.querySelector('.shepherd-arrow');\n  if (isHTMLElement$1(arrowEl) && middlewareData.arrow) {\n    const {\n      x: arrowX,\n      y: arrowY\n    } = middlewareData.arrow;\n    Object.assign(arrowEl.style, {\n      left: arrowX != null ? `${arrowX}px` : '',\n      top: arrowY != null ? `${arrowY}px` : ''\n    });\n  }\n}\n\n/**\n * Gets the `Floating UI` options from a set of base `attachTo` options\n * @param attachToOptions\n * @param step The step instance\n * @private\n */\nfunction getFloatingUIOptions(attachToOptions, step) {\n  var _attachToOptions$on, _attachToOptions$on2, _attachToOptions$on3;\n  const options = {\n    strategy: 'absolute'\n  };\n  options.middleware = [];\n  const arrowEl = addArrow(step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  const hasAutoPlacement = (_attachToOptions$on = attachToOptions.on) == null ? void 0 : _attachToOptions$on.includes('auto');\n  const hasEdgeAlignment = (attachToOptions == null || (_attachToOptions$on2 = attachToOptions.on) == null ? void 0 : _attachToOptions$on2.includes('-start')) || (attachToOptions == null || (_attachToOptions$on3 = attachToOptions.on) == null ? void 0 : _attachToOptions$on3.includes('-end'));\n  if (!shouldCenter) {\n    if (hasAutoPlacement) {\n      var _attachToOptions$on4;\n      options.middleware.push(autoPlacement({\n        crossAxis: true,\n        alignment: hasEdgeAlignment ? attachToOptions == null || (_attachToOptions$on4 = attachToOptions.on) == null ? void 0 : _attachToOptions$on4.split('-').pop() : null\n      }));\n    } else {\n      options.middleware.push(flip());\n    }\n    options.middleware.push(\n    // Replicate PopperJS default behavior.\n    shift({\n      limiter: limitShift(),\n      crossAxis: true\n    }));\n    if (arrowEl) {\n      options.middleware.push(arrow({\n        element: arrowEl,\n        padding: hasEdgeAlignment ? 4 : 0\n      }));\n    }\n    if (!hasAutoPlacement) options.placement = attachToOptions.on;\n  }\n  return deepmerge(options, step.options.floatingUIOptions || {});\n}\nfunction addArrow(step) {\n  if (step.options.arrow && step.el) {\n    return step.el.querySelector('.shepherd-arrow');\n  }\n  return false;\n}\n\n/** @returns {void} */\nfunction noop() {}\n\n/**\n * @template T\n * @template S\n * @param {T} tar\n * @param {S} src\n * @returns {T & S}\n */\nfunction assign(tar, src) {\n  // @ts-ignore\n  for (const k in src) tar[k] = src[k];\n  return /** @type {T & S} */tar;\n}\nfunction run(fn) {\n  return fn();\n}\nfunction blank_object() {\n  return Object.create(null);\n}\n\n/**\n * @param {Function[]} fns\n * @returns {void}\n */\nfunction run_all(fns) {\n  fns.forEach(run);\n}\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nfunction is_function(thing) {\n  return typeof thing === 'function';\n}\n\n/** @returns {boolean} */\nfunction safe_not_equal(a, b) {\n  return a != a ? b == b : a !== b || a && typeof a === 'object' || typeof a === 'function';\n}\n\n/** @returns {boolean} */\nfunction is_empty(obj) {\n  return Object.keys(obj).length === 0;\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @returns {void}\n */\nfunction append(target, node) {\n  target.appendChild(node);\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @param {Node} [anchor]\n * @returns {void}\n */\nfunction insert(target, node, anchor) {\n  target.insertBefore(node, anchor || null);\n}\n\n/**\n * @param {Node} node\n * @returns {void}\n */\nfunction detach(node) {\n  if (node.parentNode) {\n    node.parentNode.removeChild(node);\n  }\n}\n\n/**\n * @returns {void} */\nfunction destroy_each(iterations, detaching) {\n  for (let i = 0; i < iterations.length; i += 1) {\n    if (iterations[i]) iterations[i].d(detaching);\n  }\n}\n\n/**\n * @template {keyof HTMLElementTagNameMap} K\n * @param {K} name\n * @returns {HTMLElementTagNameMap[K]}\n */\nfunction element(name) {\n  return document.createElement(name);\n}\n\n/**\n * @template {keyof SVGElementTagNameMap} K\n * @param {K} name\n * @returns {SVGElement}\n */\nfunction svg_element(name) {\n  return document.createElementNS('http://www.w3.org/2000/svg', name);\n}\n\n/**\n * @param {string} data\n * @returns {Text}\n */\nfunction text(data) {\n  return document.createTextNode(data);\n}\n\n/**\n * @returns {Text} */\nfunction space() {\n  return text(' ');\n}\n\n/**\n * @returns {Text} */\nfunction empty() {\n  return text('');\n}\n\n/**\n * @param {EventTarget} node\n * @param {string} event\n * @param {EventListenerOrEventListenerObject} handler\n * @param {boolean | AddEventListenerOptions | EventListenerOptions} [options]\n * @returns {() => void}\n */\nfunction listen(node, event, handler, options) {\n  node.addEventListener(event, handler, options);\n  return () => node.removeEventListener(event, handler, options);\n}\n\n/**\n * @param {Element} node\n * @param {string} attribute\n * @param {string} [value]\n * @returns {void}\n */\nfunction attr(node, attribute, value) {\n  if (value == null) node.removeAttribute(attribute);else if (node.getAttribute(attribute) !== value) node.setAttribute(attribute, value);\n}\n/**\n * List of attributes that should always be set through the attr method,\n * because updating them through the property setter doesn't work reliably.\n * In the example of `width`/`height`, the problem is that the setter only\n * accepts numeric values, but the attribute can also be set to a string like `50%`.\n * If this list becomes too big, rethink this approach.\n */\nconst always_set_through_set_attribute = ['width', 'height'];\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {{ [x: string]: string }} attributes\n * @returns {void}\n */\nfunction set_attributes(node, attributes) {\n  // @ts-ignore\n  const descriptors = Object.getOwnPropertyDescriptors(node.__proto__);\n  for (const key in attributes) {\n    if (attributes[key] == null) {\n      node.removeAttribute(key);\n    } else if (key === 'style') {\n      node.style.cssText = attributes[key];\n    } else if (key === '__value') {\n      /** @type {any} */node.value = node[key] = attributes[key];\n    } else if (descriptors[key] && descriptors[key].set && always_set_through_set_attribute.indexOf(key) === -1) {\n      node[key] = attributes[key];\n    } else {\n      attr(node, key, attributes[key]);\n    }\n  }\n}\n\n/**\n * @param {Element} element\n * @returns {ChildNode[]}\n */\nfunction children(element) {\n  return Array.from(element.childNodes);\n}\n\n/**\n * @returns {void} */\nfunction toggle_class(element, name, toggle) {\n  // The `!!` is required because an `undefined` flag means flipping the current state.\n  element.classList.toggle(name, !!toggle);\n}\n\n/**\n * @typedef {Node & {\n * \tclaim_order?: number;\n * \thydrate_init?: true;\n * \tactual_end_child?: NodeEx;\n * \tchildNodes: NodeListOf<NodeEx>;\n * }} NodeEx\n */\n\n/** @typedef {ChildNode & NodeEx} ChildNodeEx */\n\n/** @typedef {NodeEx & { claim_order: number }} NodeEx2 */\n\n/**\n * @typedef {ChildNodeEx[] & {\n * \tclaim_info?: {\n * \t\tlast_index: number;\n * \t\ttotal_claimed: number;\n * \t};\n * }} ChildNodeArray\n */\n\nlet current_component;\n\n/** @returns {void} */\nfunction set_current_component(component) {\n  current_component = component;\n}\nfunction get_current_component() {\n  if (!current_component) throw new Error('Function called outside component initialization');\n  return current_component;\n}\n\n/**\n * The `onMount` function schedules a callback to run as soon as the component has been mounted to the DOM.\n * It must be called during the component's initialisation (but doesn't need to live *inside* the component;\n * it can be called from an external module).\n *\n * If a function is returned _synchronously_ from `onMount`, it will be called when the component is unmounted.\n *\n * `onMount` does not run inside a [server-side component](https://svelte.dev/docs#run-time-server-side-component-api).\n *\n * https://svelte.dev/docs/svelte#onmount\n * @template T\n * @param {() => import('./private.js').NotFunction<T> | Promise<import('./private.js').NotFunction<T>> | (() => any)} fn\n * @returns {void}\n */\nfunction onMount(fn) {\n  get_current_component().$$.on_mount.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`\n *\n * https://svelte.dev/docs/svelte#afterupdate\n * @param {() => any} fn\n * @returns {void}\n */\nfunction afterUpdate(fn) {\n  get_current_component().$$.after_update.push(fn);\n}\n\nconst dirty_components = [];\nconst binding_callbacks = [];\nlet render_callbacks = [];\nconst flush_callbacks = [];\nconst resolved_promise = /* @__PURE__ */Promise.resolve();\nlet update_scheduled = false;\n\n/** @returns {void} */\nfunction schedule_update() {\n  if (!update_scheduled) {\n    update_scheduled = true;\n    resolved_promise.then(flush);\n  }\n}\n\n/** @returns {void} */\nfunction add_render_callback(fn) {\n  render_callbacks.push(fn);\n}\n\n// flush() calls callbacks in this order:\n// 1. All beforeUpdate callbacks, in order: parents before children\n// 2. All bind:this callbacks, in reverse order: children before parents.\n// 3. All afterUpdate callbacks, in order: parents before children. EXCEPT\n//    for afterUpdates called during the initial onMount, which are called in\n//    reverse order: children before parents.\n// Since callbacks might update component values, which could trigger another\n// call to flush(), the following steps guard against this:\n// 1. During beforeUpdate, any updated components will be added to the\n//    dirty_components array and will cause a reentrant call to flush(). Because\n//    the flush index is kept outside the function, the reentrant call will pick\n//    up where the earlier call left off and go through all dirty components. The\n//    current_component value is saved and restored so that the reentrant call will\n//    not interfere with the \"parent\" flush() call.\n// 2. bind:this callbacks cannot trigger new flush() calls.\n// 3. During afterUpdate, any updated components will NOT have their afterUpdate\n//    callback called a second time; the seen_callbacks set, outside the flush()\n//    function, guarantees this behavior.\nconst seen_callbacks = new Set();\nlet flushidx = 0; // Do *not* move this inside the flush() function\n\n/** @returns {void} */\nfunction flush() {\n  // Do not reenter flush while dirty components are updated, as this can\n  // result in an infinite loop. Instead, let the inner flush handle it.\n  // Reentrancy is ok afterwards for bindings etc.\n  if (flushidx !== 0) {\n    return;\n  }\n  const saved_component = current_component;\n  do {\n    // first, call beforeUpdate functions\n    // and update components\n    try {\n      while (flushidx < dirty_components.length) {\n        const component = dirty_components[flushidx];\n        flushidx++;\n        set_current_component(component);\n        update(component.$$);\n      }\n    } catch (e) {\n      // reset dirty state to not end up in a deadlocked state and then rethrow\n      dirty_components.length = 0;\n      flushidx = 0;\n      throw e;\n    }\n    set_current_component(null);\n    dirty_components.length = 0;\n    flushidx = 0;\n    while (binding_callbacks.length) binding_callbacks.pop()();\n    // then, once components are updated, call\n    // afterUpdate functions. This may cause\n    // subsequent updates...\n    for (let i = 0; i < render_callbacks.length; i += 1) {\n      const callback = render_callbacks[i];\n      if (!seen_callbacks.has(callback)) {\n        // ...so guard against infinite loops\n        seen_callbacks.add(callback);\n        callback();\n      }\n    }\n    render_callbacks.length = 0;\n  } while (dirty_components.length);\n  while (flush_callbacks.length) {\n    flush_callbacks.pop()();\n  }\n  update_scheduled = false;\n  seen_callbacks.clear();\n  set_current_component(saved_component);\n}\n\n/** @returns {void} */\nfunction update($$) {\n  if ($$.fragment !== null) {\n    $$.update();\n    run_all($$.before_update);\n    const dirty = $$.dirty;\n    $$.dirty = [-1];\n    $$.fragment && $$.fragment.p($$.ctx, dirty);\n    $$.after_update.forEach(add_render_callback);\n  }\n}\n\n/**\n * Useful for example to execute remaining `afterUpdate` callbacks before executing `destroy`.\n * @param {Function[]} fns\n * @returns {void}\n */\nfunction flush_render_callbacks(fns) {\n  const filtered = [];\n  const targets = [];\n  render_callbacks.forEach(c => fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c));\n  targets.forEach(c => c());\n  render_callbacks = filtered;\n}\n\nconst outroing = new Set();\n\n/**\n * @type {Outro}\n */\nlet outros;\n\n/**\n * @returns {void} */\nfunction group_outros() {\n  outros = {\n    r: 0,\n    c: [],\n    p: outros // parent group\n  };\n}\n\n/**\n * @returns {void} */\nfunction check_outros() {\n  if (!outros.r) {\n    run_all(outros.c);\n  }\n  outros = outros.p;\n}\n\n/**\n * @param {import('./private.js').Fragment} block\n * @param {0 | 1} [local]\n * @returns {void}\n */\nfunction transition_in(block, local) {\n  if (block && block.i) {\n    outroing.delete(block);\n    block.i(local);\n  }\n}\n\n/**\n * @param {import('./private.js').Fragment} block\n * @param {0 | 1} local\n * @param {0 | 1} [detach]\n * @param {() => void} [callback]\n * @returns {void}\n */\nfunction transition_out(block, local, detach, callback) {\n  if (block && block.o) {\n    if (outroing.has(block)) return;\n    outroing.add(block);\n    outros.c.push(() => {\n      outroing.delete(block);\n      if (callback) {\n        if (detach) block.d(1);\n        callback();\n      }\n    });\n    block.o(local);\n  } else if (callback) {\n    callback();\n  }\n}\n\n/** @typedef {1} INTRO */\n/** @typedef {0} OUTRO */\n/** @typedef {{ direction: 'in' | 'out' | 'both' }} TransitionOptions */\n/** @typedef {(node: Element, params: any, options: TransitionOptions) => import('../transition/public.js').TransitionConfig} TransitionFn */\n\n/**\n * @typedef {Object} Outro\n * @property {number} r\n * @property {Function[]} c\n * @property {Object} p\n */\n\n/**\n * @typedef {Object} PendingProgram\n * @property {number} start\n * @property {INTRO|OUTRO} b\n * @property {Outro} [group]\n */\n\n/**\n * @typedef {Object} Program\n * @property {number} a\n * @property {INTRO|OUTRO} b\n * @property {1|-1} d\n * @property {number} duration\n * @property {number} start\n * @property {number} end\n * @property {Outro} [group]\n */\n\n// general each functions:\n\nfunction ensure_array_like(array_like_or_iterator) {\n  return (array_like_or_iterator == null ? void 0 : array_like_or_iterator.length) !== undefined ? array_like_or_iterator : Array.from(array_like_or_iterator);\n}\n\n/** @returns {{}} */\nfunction get_spread_update(levels, updates) {\n  const update = {};\n  const to_null_out = {};\n  const accounted_for = {\n    $$scope: 1\n  };\n  let i = levels.length;\n  while (i--) {\n    const o = levels[i];\n    const n = updates[i];\n    if (n) {\n      for (const key in o) {\n        if (!(key in n)) to_null_out[key] = 1;\n      }\n      for (const key in n) {\n        if (!accounted_for[key]) {\n          update[key] = n[key];\n          accounted_for[key] = 1;\n        }\n      }\n      levels[i] = n;\n    } else {\n      for (const key in o) {\n        accounted_for[key] = 1;\n      }\n    }\n  }\n  for (const key in to_null_out) {\n    if (!(key in update)) update[key] = undefined;\n  }\n  return update;\n}\n\n/** @returns {void} */\nfunction create_component(block) {\n  block && block.c();\n}\n\n/** @returns {void} */\nfunction mount_component(component, target, anchor) {\n  const {\n    fragment,\n    after_update\n  } = component.$$;\n  fragment && fragment.m(target, anchor);\n  // onMount happens before the initial afterUpdate\n  add_render_callback(() => {\n    const new_on_destroy = component.$$.on_mount.map(run).filter(is_function);\n    // if the component was destroyed immediately\n    // it will update the `$$.on_destroy` reference to `null`.\n    // the destructured on_destroy may still reference to the old array\n    if (component.$$.on_destroy) {\n      component.$$.on_destroy.push(...new_on_destroy);\n    } else {\n      // Edge case - component was destroyed immediately,\n      // most likely as a result of a binding initialising\n      run_all(new_on_destroy);\n    }\n    component.$$.on_mount = [];\n  });\n  after_update.forEach(add_render_callback);\n}\n\n/** @returns {void} */\nfunction destroy_component(component, detaching) {\n  const $$ = component.$$;\n  if ($$.fragment !== null) {\n    flush_render_callbacks($$.after_update);\n    run_all($$.on_destroy);\n    $$.fragment && $$.fragment.d(detaching);\n    // TODO null out other refs, including component.$$ (but need to\n    // preserve final state?)\n    $$.on_destroy = $$.fragment = null;\n    $$.ctx = [];\n  }\n}\n\n/** @returns {void} */\nfunction make_dirty(component, i) {\n  if (component.$$.dirty[0] === -1) {\n    dirty_components.push(component);\n    schedule_update();\n    component.$$.dirty.fill(0);\n  }\n  component.$$.dirty[i / 31 | 0] |= 1 << i % 31;\n}\n\n// TODO: Document the other params\n/**\n * @param {SvelteComponent} component\n * @param {import('./public.js').ComponentConstructorOptions} options\n *\n * @param {import('./utils.js')['not_equal']} not_equal Used to compare props and state values.\n * @param {(target: Element | ShadowRoot) => void} [append_styles] Function that appends styles to the DOM when the component is first initialised.\n * This will be the `add_css` function from the compiled component.\n *\n * @returns {void}\n */\nfunction init(component, options, instance, create_fragment, not_equal, props, append_styles = null, dirty = [-1]) {\n  const parent_component = current_component;\n  set_current_component(component);\n  /** @type {import('./private.js').T$$} */\n  const $$ = component.$$ = {\n    fragment: null,\n    ctx: [],\n    // state\n    props,\n    update: noop,\n    not_equal,\n    bound: blank_object(),\n    // lifecycle\n    on_mount: [],\n    on_destroy: [],\n    on_disconnect: [],\n    before_update: [],\n    after_update: [],\n    context: new Map(options.context || (parent_component ? parent_component.$$.context : [])),\n    // everything else\n    callbacks: blank_object(),\n    dirty,\n    skip_bound: false,\n    root: options.target || parent_component.$$.root\n  };\n  append_styles && append_styles($$.root);\n  let ready = false;\n  $$.ctx = instance ? instance(component, options.props || {}, (i, ret, ...rest) => {\n    const value = rest.length ? rest[0] : ret;\n    if ($$.ctx && not_equal($$.ctx[i], $$.ctx[i] = value)) {\n      if (!$$.skip_bound && $$.bound[i]) $$.bound[i](value);\n      if (ready) make_dirty(component, i);\n    }\n    return ret;\n  }) : [];\n  $$.update();\n  ready = true;\n  run_all($$.before_update);\n  // `false` as a special case of no DOM component\n  $$.fragment = create_fragment ? create_fragment($$.ctx) : false;\n  if (options.target) {\n    if (options.hydrate) {\n      // TODO: what is the correct type here?\n      // @ts-expect-error\n      const nodes = children(options.target);\n      $$.fragment && $$.fragment.l(nodes);\n      nodes.forEach(detach);\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.c();\n    }\n    if (options.intro) transition_in(component.$$.fragment);\n    mount_component(component, options.target, options.anchor);\n    flush();\n  }\n  set_current_component(parent_component);\n}\n\n/**\n * Base class for Svelte components. Used when dev=false.\n *\n * @template {Record<string, any>} [Props=any]\n * @template {Record<string, any>} [Events=any]\n */\nclass SvelteComponent {\n  constructor() {\n    /**\n     * ### PRIVATE API\n     *\n     * Do not use, may change at any time\n     *\n     * @type {any}\n     */\n    this.$$ = undefined;\n    /**\n     * ### PRIVATE API\n     *\n     * Do not use, may change at any time\n     *\n     * @type {any}\n     */\n    this.$$set = undefined;\n  }\n  /** @returns {void} */\n  $destroy() {\n    destroy_component(this, 1);\n    this.$destroy = noop;\n  }\n\n  /**\n   * @template {Extract<keyof Events, string>} K\n   * @param {K} type\n   * @param {((e: Events[K]) => void) | null | undefined} callback\n   * @returns {() => void}\n   */\n  $on(type, callback) {\n    if (!is_function(callback)) {\n      return noop;\n    }\n    const callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);\n    callbacks.push(callback);\n    return () => {\n      const index = callbacks.indexOf(callback);\n      if (index !== -1) callbacks.splice(index, 1);\n    };\n  }\n\n  /**\n   * @param {Partial<Props>} props\n   * @returns {void}\n   */\n  $set(props) {\n    if (this.$$set && !is_empty(props)) {\n      this.$$.skip_bound = true;\n      this.$$set(props);\n      this.$$.skip_bound = false;\n    }\n  }\n}\n\n/**\n * @typedef {Object} CustomElementPropDefinition\n * @property {string} [attribute]\n * @property {boolean} [reflect]\n * @property {'String'|'Boolean'|'Number'|'Array'|'Object'} [type]\n */\n\n// generated during release, do not modify\n\nconst PUBLIC_VERSION = '4';\n\nif (typeof window !== 'undefined')\n  // @ts-ignore\n  (window.__svelte || (window.__svelte = {\n    v: new Set()\n  })).v.add(PUBLIC_VERSION);\n\n/* src/components/shepherd-button.svelte generated by Svelte v4.2.19 */\nfunction create_fragment$8(ctx) {\n  let button;\n  let button_aria_label_value;\n  let button_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      attr(button, \"aria-label\", button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null);\n      attr(button, \"class\", button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`);\n      button.disabled = /*disabled*/ctx[2];\n      attr(button, \"tabindex\", \"0\");\n      attr(button, \"type\", \"button\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      button.innerHTML = /*text*/ctx[5];\n      if (!mounted) {\n        dispose = listen(button, \"click\", function () {\n          if (is_function( /*action*/ctx[0])) /*action*/ctx[0].apply(this, arguments);\n        });\n        mounted = true;\n      }\n    },\n    p(new_ctx, [dirty]) {\n      ctx = new_ctx;\n      if (dirty & /*text*/32) button.innerHTML = /*text*/ctx[5];\n      if (dirty & /*label*/8 && button_aria_label_value !== (button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null)) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n      if (dirty & /*classes, secondary*/18 && button_class_value !== (button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`)) {\n        attr(button, \"class\", button_class_value);\n      }\n      if (dirty & /*disabled*/4) {\n        button.disabled = /*disabled*/ctx[2];\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) {\n        detach(button);\n      }\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$8($$self, $$props, $$invalidate) {\n  let {\n    config,\n    step\n  } = $$props;\n  let action, classes, disabled, label, secondary, text;\n  function getConfigOption(option) {\n    if (isFunction(option)) {\n      return option = option.call(step);\n    }\n    return option;\n  }\n  $$self.$$set = $$props => {\n    if ('config' in $$props) $$invalidate(6, config = $$props.config);\n    if ('step' in $$props) $$invalidate(7, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*config, step*/192) {\n      {\n        $$invalidate(0, action = config.action ? config.action.bind(step.tour) : null);\n        $$invalidate(1, classes = config.classes);\n        $$invalidate(2, disabled = config.disabled ? getConfigOption(config.disabled) : false);\n        $$invalidate(3, label = config.label ? getConfigOption(config.label) : null);\n        $$invalidate(4, secondary = config.secondary);\n        $$invalidate(5, text = config.text ? getConfigOption(config.text) : null);\n      }\n    }\n  };\n  return [action, classes, disabled, label, secondary, text, config, step];\n}\nclass Shepherd_button extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$8, create_fragment$8, safe_not_equal, {\n      config: 6,\n      step: 7\n    });\n  }\n}\n\n/* src/components/shepherd-footer.svelte generated by Svelte v4.2.19 */\nfunction get_each_context(ctx, list, i) {\n  const child_ctx = ctx.slice();\n  child_ctx[2] = list[i];\n  return child_ctx;\n}\n\n// (10:2) {#if buttons}\nfunction create_if_block$3(ctx) {\n  let each_1_anchor;\n  let current;\n  let each_value = ensure_array_like( /*buttons*/ctx[1]);\n  let each_blocks = [];\n  for (let i = 0; i < each_value.length; i += 1) {\n    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));\n  }\n  const out = i => transition_out(each_blocks[i], 1, 1, () => {\n    each_blocks[i] = null;\n  });\n  return {\n    c() {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        each_blocks[i].c();\n      }\n      each_1_anchor = empty();\n    },\n    m(target, anchor) {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        if (each_blocks[i]) {\n          each_blocks[i].m(target, anchor);\n        }\n      }\n      insert(target, each_1_anchor, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      if (dirty & /*buttons, step*/3) {\n        each_value = ensure_array_like( /*buttons*/ctx[1]);\n        let i;\n        for (i = 0; i < each_value.length; i += 1) {\n          const child_ctx = get_each_context(ctx, each_value, i);\n          if (each_blocks[i]) {\n            each_blocks[i].p(child_ctx, dirty);\n            transition_in(each_blocks[i], 1);\n          } else {\n            each_blocks[i] = create_each_block(child_ctx);\n            each_blocks[i].c();\n            transition_in(each_blocks[i], 1);\n            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);\n          }\n        }\n        group_outros();\n        for (i = each_value.length; i < each_blocks.length; i += 1) {\n          out(i);\n        }\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      for (let i = 0; i < each_value.length; i += 1) {\n        transition_in(each_blocks[i]);\n      }\n      current = true;\n    },\n    o(local) {\n      each_blocks = each_blocks.filter(Boolean);\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        transition_out(each_blocks[i]);\n      }\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) {\n        detach(each_1_anchor);\n      }\n      destroy_each(each_blocks, detaching);\n    }\n  };\n}\n\n// (11:4) {#each buttons as config}\nfunction create_each_block(ctx) {\n  let shepherdbutton;\n  let current;\n  shepherdbutton = new Shepherd_button({\n    props: {\n      config: /*config*/ctx[2],\n      step: /*step*/ctx[0]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdbutton.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdbutton, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdbutton_changes = {};\n      if (dirty & /*buttons*/2) shepherdbutton_changes.config = /*config*/ctx[2];\n      if (dirty & /*step*/1) shepherdbutton_changes.step = /*step*/ctx[0];\n      shepherdbutton.$set(shepherdbutton_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdbutton.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdbutton.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdbutton, detaching);\n    }\n  };\n}\nfunction create_fragment$7(ctx) {\n  let footer;\n  let current;\n  let if_block = /*buttons*/ctx[1] && create_if_block$3(ctx);\n  return {\n    c() {\n      footer = element(\"footer\");\n      if (if_block) if_block.c();\n      attr(footer, \"class\", \"shepherd-footer\");\n    },\n    m(target, anchor) {\n      insert(target, footer, anchor);\n      if (if_block) if_block.m(footer, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if ( /*buttons*/ctx[1]) {\n        if (if_block) {\n          if_block.p(ctx, dirty);\n          if (dirty & /*buttons*/2) {\n            transition_in(if_block, 1);\n          }\n        } else {\n          if_block = create_if_block$3(ctx);\n          if_block.c();\n          transition_in(if_block, 1);\n          if_block.m(footer, null);\n        }\n      } else if (if_block) {\n        group_outros();\n        transition_out(if_block, 1, 1, () => {\n          if_block = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) {\n        detach(footer);\n      }\n      if (if_block) if_block.d();\n    }\n  };\n}\nfunction instance$7($$self, $$props, $$invalidate) {\n  let buttons;\n  let {\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('step' in $$props) $$invalidate(0, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/1) {\n      $$invalidate(1, buttons = step.options.buttons);\n    }\n  };\n  return [step, buttons];\n}\nclass Shepherd_footer extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$7, create_fragment$7, safe_not_equal, {\n      step: 0\n    });\n  }\n}\n\n/* src/components/shepherd-cancel-icon.svelte generated by Svelte v4.2.19 */\nfunction create_fragment$6(ctx) {\n  let button;\n  let span;\n  let button_aria_label_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      span = element(\"span\");\n      span.textContent = \"×\";\n      attr(span, \"aria-hidden\", \"true\");\n      attr(button, \"aria-label\", button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour');\n      attr(button, \"class\", \"shepherd-cancel-icon\");\n      attr(button, \"type\", \"button\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      append(button, span);\n      if (!mounted) {\n        dispose = listen(button, \"click\", /*handleCancelClick*/ctx[1]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*cancelIcon*/1 && button_aria_label_value !== (button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour')) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) {\n        detach(button);\n      }\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$6($$self, $$props, $$invalidate) {\n  let {\n    cancelIcon,\n    step\n  } = $$props;\n\n  /**\n  * Add a click listener to the cancel link that cancels the tour\n  */\n  const handleCancelClick = e => {\n    e.preventDefault();\n    step.cancel();\n  };\n  $$self.$$set = $$props => {\n    if ('cancelIcon' in $$props) $$invalidate(0, cancelIcon = $$props.cancelIcon);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [cancelIcon, handleCancelClick, step];\n}\nclass Shepherd_cancel_icon extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$6, create_fragment$6, safe_not_equal, {\n      cancelIcon: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/components/shepherd-title.svelte generated by Svelte v4.2.19 */\nfunction create_fragment$5(ctx) {\n  let h3;\n  return {\n    c() {\n      h3 = element(\"h3\");\n      attr(h3, \"id\", /*labelId*/ctx[1]);\n      attr(h3, \"class\", \"shepherd-title\");\n    },\n    m(target, anchor) {\n      insert(target, h3, anchor);\n      /*h3_binding*/\n      ctx[3](h3);\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*labelId*/2) {\n        attr(h3, \"id\", /*labelId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) {\n        detach(h3);\n      }\n\n      /*h3_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$5($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    element,\n    title\n  } = $$props;\n  afterUpdate(() => {\n    if (isFunction(title)) {\n      $$invalidate(2, title = title());\n    }\n    $$invalidate(0, element.innerHTML = title, element);\n  });\n  function h3_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('title' in $$props) $$invalidate(2, title = $$props.title);\n  };\n  return [element, labelId, title, h3_binding];\n}\nclass Shepherd_title extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$5, create_fragment$5, safe_not_equal, {\n      labelId: 1,\n      element: 0,\n      title: 2\n    });\n  }\n}\n\n/* src/components/shepherd-header.svelte generated by Svelte v4.2.19 */\nfunction create_if_block_1$1(ctx) {\n  let shepherdtitle;\n  let current;\n  shepherdtitle = new Shepherd_title({\n    props: {\n      labelId: /*labelId*/ctx[0],\n      title: /*title*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtitle.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtitle, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtitle_changes = {};\n      if (dirty & /*labelId*/1) shepherdtitle_changes.labelId = /*labelId*/ctx[0];\n      if (dirty & /*title*/4) shepherdtitle_changes.title = /*title*/ctx[2];\n      shepherdtitle.$set(shepherdtitle_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtitle.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtitle.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtitle, detaching);\n    }\n  };\n}\n\n// (19:2) {#if cancelIcon && cancelIcon.enabled}\nfunction create_if_block$2(ctx) {\n  let shepherdcancelicon;\n  let current;\n  shepherdcancelicon = new Shepherd_cancel_icon({\n    props: {\n      cancelIcon: /*cancelIcon*/ctx[3],\n      step: /*step*/ctx[1]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdcancelicon.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdcancelicon, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdcancelicon_changes = {};\n      if (dirty & /*cancelIcon*/8) shepherdcancelicon_changes.cancelIcon = /*cancelIcon*/ctx[3];\n      if (dirty & /*step*/2) shepherdcancelicon_changes.step = /*step*/ctx[1];\n      shepherdcancelicon.$set(shepherdcancelicon_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcancelicon.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcancelicon.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdcancelicon, detaching);\n    }\n  };\n}\nfunction create_fragment$4(ctx) {\n  let header;\n  let t;\n  let current;\n  let if_block0 = /*title*/ctx[2] && create_if_block_1$1(ctx);\n  let if_block1 = /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled && create_if_block$2(ctx);\n  return {\n    c() {\n      header = element(\"header\");\n      if (if_block0) if_block0.c();\n      t = space();\n      if (if_block1) if_block1.c();\n      attr(header, \"class\", \"shepherd-header\");\n    },\n    m(target, anchor) {\n      insert(target, header, anchor);\n      if (if_block0) if_block0.m(header, null);\n      append(header, t);\n      if (if_block1) if_block1.m(header, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if ( /*title*/ctx[2]) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*title*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_1$1(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(header, t);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if ( /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*cancelIcon*/8) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block$2(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(header, null);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) {\n        detach(header);\n      }\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n    }\n  };\n}\nfunction instance$4($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    step\n  } = $$props;\n  let title, cancelIcon;\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(0, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(1, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/2) {\n      {\n        $$invalidate(2, title = step.options.title);\n        $$invalidate(3, cancelIcon = step.options.cancelIcon);\n      }\n    }\n  };\n  return [labelId, step, title, cancelIcon];\n}\nclass Shepherd_header extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$4, create_fragment$4, safe_not_equal, {\n      labelId: 0,\n      step: 1\n    });\n  }\n}\n\n/* src/components/shepherd-text.svelte generated by Svelte v4.2.19 */\nfunction create_fragment$3(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-text\");\n      attr(div, \"id\", /*descriptionId*/ctx[1]);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      /*div_binding*/\n      ctx[3](div);\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*descriptionId*/2) {\n        attr(div, \"id\", /*descriptionId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) {\n        detach(div);\n      }\n\n      /*div_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$3($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    element,\n    step\n  } = $$props;\n  afterUpdate(() => {\n    let {\n      text\n    } = step.options;\n    if (isFunction(text)) {\n      text = text.call(step);\n    }\n    if (isHTMLElement$1(text)) {\n      element.appendChild(text);\n    } else {\n      $$invalidate(0, element.innerHTML = text, element);\n    }\n  });\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(1, descriptionId = $$props.descriptionId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [element, descriptionId, step, div_binding];\n}\nclass Shepherd_text extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$3, create_fragment$3, safe_not_equal, {\n      descriptionId: 1,\n      element: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/components/shepherd-content.svelte generated by Svelte v4.2.19 */\nfunction create_if_block_2(ctx) {\n  let shepherdheader;\n  let current;\n  shepherdheader = new Shepherd_header({\n    props: {\n      labelId: /*labelId*/ctx[1],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdheader.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdheader, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdheader_changes = {};\n      if (dirty & /*labelId*/2) shepherdheader_changes.labelId = /*labelId*/ctx[1];\n      if (dirty & /*step*/4) shepherdheader_changes.step = /*step*/ctx[2];\n      shepherdheader.$set(shepherdheader_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdheader.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdheader.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdheader, detaching);\n    }\n  };\n}\n\n// (15:2) {#if !isUndefined(step.options.text)}\nfunction create_if_block_1(ctx) {\n  let shepherdtext;\n  let current;\n  shepherdtext = new Shepherd_text({\n    props: {\n      descriptionId: /*descriptionId*/ctx[0],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtext.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtext, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtext_changes = {};\n      if (dirty & /*descriptionId*/1) shepherdtext_changes.descriptionId = /*descriptionId*/ctx[0];\n      if (dirty & /*step*/4) shepherdtext_changes.step = /*step*/ctx[2];\n      shepherdtext.$set(shepherdtext_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtext.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtext.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtext, detaching);\n    }\n  };\n}\n\n// (19:2) {#if Array.isArray(step.options.buttons) && step.options.buttons.length}\nfunction create_if_block$1(ctx) {\n  let shepherdfooter;\n  let current;\n  shepherdfooter = new Shepherd_footer({\n    props: {\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdfooter.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdfooter, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdfooter_changes = {};\n      if (dirty & /*step*/4) shepherdfooter_changes.step = /*step*/ctx[2];\n      shepherdfooter.$set(shepherdfooter_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdfooter.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdfooter.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdfooter, detaching);\n    }\n  };\n}\nfunction create_fragment$2(ctx) {\n  let div;\n  let show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n  let t0;\n  let show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n  let t1;\n  let show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n  let current;\n  let if_block0 = show_if_2 && create_if_block_2(ctx);\n  let if_block1 = show_if_1 && create_if_block_1(ctx);\n  let if_block2 = show_if && create_if_block$1(ctx);\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block0) if_block0.c();\n      t0 = space();\n      if (if_block1) if_block1.c();\n      t1 = space();\n      if (if_block2) if_block2.c();\n      attr(div, \"class\", \"shepherd-content\");\n      attr(div, \"tabindex\", \"0\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block0) if_block0.m(div, null);\n      append(div, t0);\n      if (if_block1) if_block1.m(div, null);\n      append(div, t1);\n      if (if_block2) if_block2.m(div, null);\n      current = true;\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*step*/4) show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n      if (show_if_2) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_2(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(div, t0);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n      if (show_if_1) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block_1(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(div, t1);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n      if (show_if) {\n        if (if_block2) {\n          if_block2.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block2, 1);\n          }\n        } else {\n          if_block2 = create_if_block$1(ctx);\n          if_block2.c();\n          transition_in(if_block2, 1);\n          if_block2.m(div, null);\n        }\n      } else if (if_block2) {\n        group_outros();\n        transition_out(if_block2, 1, 1, () => {\n          if_block2 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      transition_in(if_block2);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      transition_out(if_block2);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) {\n        detach(div);\n      }\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n      if (if_block2) if_block2.d();\n    }\n  };\n}\nfunction instance$2($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    labelId,\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(0, descriptionId = $$props.descriptionId);\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [descriptionId, labelId, step];\n}\nclass Shepherd_content extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$2, create_fragment$2, safe_not_equal, {\n      descriptionId: 0,\n      labelId: 1,\n      step: 2\n    });\n  }\n}\n\n/* src/components/shepherd-element.svelte generated by Svelte v4.2.19 */\nfunction create_if_block(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-arrow\");\n      attr(div, \"data-popper-arrow\", \"\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n    },\n    d(detaching) {\n      if (detaching) {\n        detach(div);\n      }\n    }\n  };\n}\nfunction create_fragment$1(ctx) {\n  let dialog;\n  let t;\n  let shepherdcontent;\n  let dialog_aria_describedby_value;\n  let dialog_aria_labelledby_value;\n  let current;\n  let mounted;\n  let dispose;\n  let if_block = /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on && create_if_block();\n  shepherdcontent = new Shepherd_content({\n    props: {\n      descriptionId: /*descriptionId*/ctx[2],\n      labelId: /*labelId*/ctx[3],\n      step: /*step*/ctx[4]\n    }\n  });\n  let dialog_levels = [{\n    \"aria-describedby\": dialog_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null\n  }, {\n    \"aria-labelledby\": dialog_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null\n  }, /*dataStepId*/ctx[1], {\n    open: \"true\"\n  }];\n  let dialog_data = {};\n  for (let i = 0; i < dialog_levels.length; i += 1) {\n    dialog_data = assign(dialog_data, dialog_levels[i]);\n  }\n  return {\n    c() {\n      dialog = element(\"dialog\");\n      if (if_block) if_block.c();\n      t = space();\n      create_component(shepherdcontent.$$.fragment);\n      set_attributes(dialog, dialog_data);\n      toggle_class(dialog, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(dialog, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(dialog, \"shepherd-element\", true);\n    },\n    m(target, anchor) {\n      insert(target, dialog, anchor);\n      if (if_block) if_block.m(dialog, null);\n      append(dialog, t);\n      mount_component(shepherdcontent, dialog, null);\n      /*dialog_binding*/\n      ctx[13](dialog);\n      current = true;\n      if (!mounted) {\n        dispose = listen(dialog, \"keydown\", /*handleKeyDown*/ctx[7]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if ( /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on) {\n        if (if_block) ; else {\n          if_block = create_if_block();\n          if_block.c();\n          if_block.m(dialog, t);\n        }\n      } else if (if_block) {\n        if_block.d(1);\n        if_block = null;\n      }\n      const shepherdcontent_changes = {};\n      if (dirty & /*descriptionId*/4) shepherdcontent_changes.descriptionId = /*descriptionId*/ctx[2];\n      if (dirty & /*labelId*/8) shepherdcontent_changes.labelId = /*labelId*/ctx[3];\n      if (dirty & /*step*/16) shepherdcontent_changes.step = /*step*/ctx[4];\n      shepherdcontent.$set(shepherdcontent_changes);\n      set_attributes(dialog, dialog_data = get_spread_update(dialog_levels, [(!current || dirty & /*step, descriptionId*/20 && dialog_aria_describedby_value !== (dialog_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null)) && {\n        \"aria-describedby\": dialog_aria_describedby_value\n      }, (!current || dirty & /*step, labelId*/24 && dialog_aria_labelledby_value !== (dialog_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null)) && {\n        \"aria-labelledby\": dialog_aria_labelledby_value\n      }, dirty & /*dataStepId*/2 && /*dataStepId*/ctx[1], {\n        open: \"true\"\n      }]));\n      toggle_class(dialog, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(dialog, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(dialog, \"shepherd-element\", true);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcontent.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcontent.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) {\n        detach(dialog);\n      }\n      if (if_block) if_block.d();\n      destroy_component(shepherdcontent);\n      /*dialog_binding*/\n      ctx[13](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nconst KEY_TAB = 9;\nconst KEY_ESC = 27;\nconst LEFT_ARROW = 37;\nconst RIGHT_ARROW = 39;\nfunction getClassesArray(classes) {\n  return classes.split(' ').filter(className => !!className.length);\n}\nfunction instance$1($$self, $$props, $$invalidate) {\n  let {\n    classPrefix,\n    element,\n    descriptionId,\n    firstFocusableElement,\n    focusableElements,\n    labelId,\n    lastFocusableElement,\n    step,\n    dataStepId\n  } = $$props;\n  let hasCancelIcon, hasTitle, classes;\n  const getElement = () => element;\n  onMount(() => {\n    // Get all elements that are focusable\n    $$invalidate(1, dataStepId = {\n      [`data-${classPrefix}shepherd-step-id`]: step.id\n    });\n    $$invalidate(9, focusableElements = element.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex=\"0\"]'));\n    $$invalidate(8, firstFocusableElement = focusableElements[0]);\n    $$invalidate(10, lastFocusableElement = focusableElements[focusableElements.length - 1]);\n  });\n  afterUpdate(() => {\n    if (classes !== step.options.classes) {\n      updateDynamicClasses();\n    }\n  });\n  function updateDynamicClasses() {\n    removeClasses(classes);\n    classes = step.options.classes;\n    addClasses(classes);\n  }\n  function removeClasses(classes) {\n    if (isString(classes)) {\n      const oldClasses = getClassesArray(classes);\n      if (oldClasses.length) {\n        element.classList.remove(...oldClasses);\n      }\n    }\n  }\n  function addClasses(classes) {\n    if (isString(classes)) {\n      const newClasses = getClassesArray(classes);\n      if (newClasses.length) {\n        element.classList.add(...newClasses);\n      }\n    }\n  }\n\n  /**\n  * Setup keydown events to allow closing the modal with ESC\n  *\n  * Borrowed from this great post! https://bitsofco.de/accessible-modal-dialog/\n  *\n  * @private\n  */\n  const handleKeyDown = e => {\n    const {\n      tour\n    } = step;\n    switch (e.keyCode) {\n      case KEY_TAB:\n        if (focusableElements.length === 0) {\n          e.preventDefault();\n          break;\n        }\n        // Backward tab\n        if (e.shiftKey) {\n          if (document.activeElement === firstFocusableElement || document.activeElement.classList.contains('shepherd-element')) {\n            e.preventDefault();\n            lastFocusableElement.focus();\n          }\n        } else {\n          if (document.activeElement === lastFocusableElement) {\n            e.preventDefault();\n            firstFocusableElement.focus();\n          }\n        }\n        break;\n      case KEY_ESC:\n        if (tour.options.exitOnEsc) {\n          e.preventDefault();\n          e.stopPropagation();\n          step.cancel();\n        }\n        break;\n      case LEFT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          e.preventDefault();\n          e.stopPropagation();\n          tour.back();\n        }\n        break;\n      case RIGHT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          e.preventDefault();\n          e.stopPropagation();\n          tour.next();\n        }\n        break;\n    }\n  };\n  function dialog_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('classPrefix' in $$props) $$invalidate(11, classPrefix = $$props.classPrefix);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('descriptionId' in $$props) $$invalidate(2, descriptionId = $$props.descriptionId);\n    if ('firstFocusableElement' in $$props) $$invalidate(8, firstFocusableElement = $$props.firstFocusableElement);\n    if ('focusableElements' in $$props) $$invalidate(9, focusableElements = $$props.focusableElements);\n    if ('labelId' in $$props) $$invalidate(3, labelId = $$props.labelId);\n    if ('lastFocusableElement' in $$props) $$invalidate(10, lastFocusableElement = $$props.lastFocusableElement);\n    if ('step' in $$props) $$invalidate(4, step = $$props.step);\n    if ('dataStepId' in $$props) $$invalidate(1, dataStepId = $$props.dataStepId);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/16) {\n      {\n        $$invalidate(5, hasCancelIcon = step.options && step.options.cancelIcon && step.options.cancelIcon.enabled);\n        $$invalidate(6, hasTitle = step.options && step.options.title);\n      }\n    }\n  };\n  return [element, dataStepId, descriptionId, labelId, step, hasCancelIcon, hasTitle, handleKeyDown, firstFocusableElement, focusableElements, lastFocusableElement, classPrefix, getElement, dialog_binding];\n}\nclass Shepherd_element extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$1, create_fragment$1, safe_not_equal, {\n      classPrefix: 11,\n      element: 0,\n      descriptionId: 2,\n      firstFocusableElement: 8,\n      focusableElements: 9,\n      labelId: 3,\n      lastFocusableElement: 10,\n      step: 4,\n      dataStepId: 1,\n      getElement: 12\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[12];\n  }\n}\n\n/**\n * The options for the step\n */\n\n/**\n * A class representing steps to be added to a tour.\n * @extends {Evented}\n */\nclass Step extends Evented {\n  constructor(tour, options = {}) {\n    super();\n    this._resolvedAttachTo = void 0;\n    this._resolvedExtraHighlightElements = void 0;\n    this.classPrefix = void 0;\n    this.el = void 0;\n    this.target = void 0;\n    this.tour = void 0;\n    this.tour = tour;\n    this.classPrefix = this.tour.options ? normalizePrefix(this.tour.options.classPrefix) : '';\n    // @ts-expect-error TODO: investigate where styles comes from\n    this.styles = tour.styles;\n\n    /**\n     * Resolved attachTo options. Due to lazy evaluation, we only resolve the options during `before-show` phase.\n     * Do not use this directly, use the _getResolvedAttachToOptions method instead.\n     * @type {StepOptionsAttachTo | null}\n     * @private\n     */\n    this._resolvedAttachTo = null;\n    autoBind(this);\n    this._setOptions(options);\n    return this;\n  }\n\n  /**\n   * Cancel the tour\n   * Triggers the `cancel` event\n   */\n  cancel() {\n    this.tour.cancel();\n    this.trigger('cancel');\n  }\n\n  /**\n   * Complete the tour\n   * Triggers the `complete` event\n   */\n  complete() {\n    this.tour.complete();\n    this.trigger('complete');\n  }\n\n  /**\n   * Remove the step, delete the step's element, and destroy the FloatingUI instance for the step.\n   * Triggers `destroy` event\n   */\n  destroy() {\n    destroyTooltip(this);\n    if (isHTMLElement$1(this.el)) {\n      this.el.remove();\n      this.el = null;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('destroy');\n  }\n\n  /**\n   * Returns the tour for the step\n   * @return The tour instance\n   */\n  getTour() {\n    return this.tour;\n  }\n\n  /**\n   * Hide the step\n   */\n  hide() {\n    var _this$tour$modal;\n    (_this$tour$modal = this.tour.modal) == null || _this$tour$modal.hide();\n    this.trigger('before-hide');\n    if (this.el) {\n      this.el.hidden = true;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('hide');\n  }\n\n  /**\n   * Resolves attachTo options.\n   * @returns {{}|{element, on}}\n   */\n  _resolveExtraHiglightElements() {\n    this._resolvedExtraHighlightElements = parseExtraHighlights(this);\n    return this._resolvedExtraHighlightElements;\n  }\n\n  /**\n   * Resolves attachTo options.\n   * @returns {{}|{element, on}}\n   */\n  _resolveAttachToOptions() {\n    this._resolvedAttachTo = parseAttachTo(this);\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * A selector for resolved attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _getResolvedAttachToOptions() {\n    if (this._resolvedAttachTo === null) {\n      return this._resolveAttachToOptions();\n    }\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * Check if the step is open and visible\n   * @return True if the step is open and visible\n   */\n  isOpen() {\n    return Boolean(this.el && !this.el.hidden);\n  }\n\n  /**\n   * Wraps `_show` and ensures `beforeShowPromise` resolves before calling show\n   */\n  show() {\n    if (isFunction(this.options.beforeShowPromise)) {\n      return Promise.resolve(this.options.beforeShowPromise()).then(() => this._show());\n    }\n    return Promise.resolve(this._show());\n  }\n\n  /**\n   * Updates the options of the step.\n   *\n   * @param {StepOptions} options The options for the step\n   */\n  updateStepOptions(options) {\n    Object.assign(this.options, options);\n\n    // @ts-expect-error TODO: get types for Svelte components\n    if (this.shepherdElementComponent) {\n      // @ts-expect-error TODO: get types for Svelte components\n      this.shepherdElementComponent.$set({\n        step: this\n      });\n    }\n  }\n\n  /**\n   * Returns the element for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if it has been destroyed\n   */\n  getElement() {\n    return this.el;\n  }\n\n  /**\n   * Returns the target for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if query string has not been found\n   */\n  getTarget() {\n    return this.target;\n  }\n\n  /**\n   * Creates Shepherd element for step based on options\n   *\n   * @return {HTMLElement} The DOM element for the step tooltip\n   * @private\n   */\n  _createTooltipContent() {\n    const descriptionId = `${this.id}-description`;\n    const labelId = `${this.id}-label`;\n\n    // @ts-expect-error TODO: get types for Svelte components\n    this.shepherdElementComponent = new Shepherd_element({\n      target: this.tour.options.stepsContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        descriptionId,\n        labelId,\n        step: this,\n        // @ts-expect-error TODO: investigate where styles comes from\n        styles: this.styles\n      }\n    });\n\n    // @ts-expect-error TODO: get types for Svelte components\n    return this.shepherdElementComponent.getElement();\n  }\n\n  /**\n   * If a custom scrollToHandler is defined, call that, otherwise do the generic\n   * scrollIntoView call.\n   *\n   * @param {boolean | ScrollIntoViewOptions} scrollToOptions - If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{ behavior: 'smooth', block: 'center' }`\n   * @private\n   */\n  _scrollTo(scrollToOptions) {\n    const {\n      element\n    } = this._getResolvedAttachToOptions();\n    if (isFunction(this.options.scrollToHandler)) {\n      this.options.scrollToHandler(element);\n    } else if (isElement$1(element) && typeof element.scrollIntoView === 'function') {\n      element.scrollIntoView(scrollToOptions);\n    }\n  }\n\n  /**\n   * _getClassOptions gets all possible classes for the step\n   * @param {StepOptions} stepOptions The step specific options\n   * @returns {string} unique string from array of classes\n   */\n  _getClassOptions(stepOptions) {\n    const defaultStepOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    const stepClasses = stepOptions.classes ? stepOptions.classes : '';\n    const defaultStepOptionsClasses = defaultStepOptions && defaultStepOptions.classes ? defaultStepOptions.classes : '';\n    const allClasses = [...stepClasses.split(' '), ...defaultStepOptionsClasses.split(' ')];\n    const uniqClasses = new Set(allClasses);\n    return Array.from(uniqClasses).join(' ').trim();\n  }\n\n  /**\n   * Sets the options for the step, maps `when` to events, sets up buttons\n   * @param options - The options for the step\n   */\n  _setOptions(options = {}) {\n    let tourOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    tourOptions = deepmerge({}, tourOptions || {});\n    this.options = Object.assign({\n      arrow: true\n    }, tourOptions, options, mergeTooltipConfig(tourOptions, options));\n    const {\n      when\n    } = this.options;\n    this.options.classes = this._getClassOptions(options);\n    this.destroy();\n    this.id = this.options.id || `step-${uuid()}`;\n    if (when) {\n      Object.keys(when).forEach(event => {\n        // @ts-expect-error TODO: fix this type error\n        this.on(event, when[event], this);\n      });\n    }\n  }\n\n  /**\n   * Create the element and set up the FloatingUI instance\n   * @private\n   */\n  _setupElements() {\n    if (!isUndefined(this.el)) {\n      this.destroy();\n    }\n    this.el = this._createTooltipContent();\n    if (this.options.advanceOn) {\n      bindAdvance(this);\n    }\n\n    // The tooltip implementation details are handled outside of the Step\n    // object.\n    setupTooltip(this);\n  }\n\n  /**\n   * Triggers `before-show`, generates the tooltip DOM content,\n   * sets up a FloatingUI instance for the tooltip, then triggers `show`.\n   * @private\n   */\n  _show() {\n    var _this$tour$modal2;\n    this.trigger('before-show');\n\n    // Force resolve to make sure the options are updated on subsequent shows.\n    this._resolveAttachToOptions();\n    this._resolveExtraHiglightElements();\n    this._setupElements();\n    if (!this.tour.modal) {\n      this.tour.setupModal();\n    }\n    (_this$tour$modal2 = this.tour.modal) == null || _this$tour$modal2.setupForStep(this);\n    this._styleTargetElementForStep(this);\n    if (this.el) {\n      this.el.hidden = false;\n    }\n\n    // start scrolling to target before showing the step\n    if (this.options.scrollTo) {\n      setTimeout(() => {\n        this._scrollTo(this.options.scrollTo);\n      });\n    }\n    if (this.el) {\n      this.el.hidden = false;\n    }\n\n    // @ts-expect-error TODO: get types for Svelte components\n    const content = this.shepherdElementComponent.getElement();\n    const target = this.target || document.body;\n    const extraHighlightElements = this._resolvedExtraHighlightElements;\n    target.classList.add(`${this.classPrefix}shepherd-enabled`);\n    target.classList.add(`${this.classPrefix}shepherd-target`);\n    content.classList.add('shepherd-enabled');\n    extraHighlightElements == null || extraHighlightElements.forEach(el => {\n      el.classList.add(`${this.classPrefix}shepherd-enabled`);\n      el.classList.add(`${this.classPrefix}shepherd-target`);\n    });\n    this.trigger('show');\n  }\n\n  /**\n   * Modulates the styles of the passed step's target element, based on the step's options and\n   * the tour's `modal` option, to visually emphasize the element\n   *\n   * @param {Step} step The step object that attaches to the element\n   * @private\n   */\n  _styleTargetElementForStep(step) {\n    const targetElement = step.target;\n    const extraHighlightElements = step._resolvedExtraHighlightElements;\n    if (!targetElement) {\n      return;\n    }\n    const highlightClass = step.options.highlightClass;\n    if (highlightClass) {\n      targetElement.classList.add(highlightClass);\n      extraHighlightElements == null || extraHighlightElements.forEach(el => el.classList.add(highlightClass));\n    }\n    targetElement.classList.remove('shepherd-target-click-disabled');\n    extraHighlightElements == null || extraHighlightElements.forEach(el => el.classList.remove('shepherd-target-click-disabled'));\n    if (step.options.canClickTarget === false) {\n      targetElement.classList.add('shepherd-target-click-disabled');\n      extraHighlightElements == null || extraHighlightElements.forEach(el => el.classList.add('shepherd-target-click-disabled'));\n    }\n  }\n\n  /**\n   * When a step is hidden, remove the highlightClass and 'shepherd-enabled'\n   * and 'shepherd-target' classes\n   * @private\n   */\n  _updateStepTargetOnHide() {\n    const target = this.target || document.body;\n    const extraHighlightElements = this._resolvedExtraHighlightElements;\n    const highlightClass = this.options.highlightClass;\n    if (highlightClass) {\n      target.classList.remove(highlightClass);\n      extraHighlightElements == null || extraHighlightElements.forEach(el => el.classList.remove(highlightClass));\n    }\n    target.classList.remove('shepherd-target-click-disabled', `${this.classPrefix}shepherd-enabled`, `${this.classPrefix}shepherd-target`);\n    extraHighlightElements == null || extraHighlightElements.forEach(el => {\n      el.classList.remove('shepherd-target-click-disabled', `${this.classPrefix}shepherd-enabled`, `${this.classPrefix}shepherd-target`);\n    });\n  }\n}\n\n/**\n * Cleanup the steps and set pointerEvents back to 'auto'\n * @param tour The tour object\n */\nfunction cleanupSteps(tour) {\n  if (tour) {\n    const {\n      steps\n    } = tour;\n    steps.forEach(step => {\n      if (step.options && step.options.canClickTarget === false && step.options.attachTo) {\n        if (isHTMLElement$1(step.target)) {\n          step.target.classList.remove('shepherd-target-click-disabled');\n        }\n        if (step._resolvedExtraHighlightElements) {\n          step._resolvedExtraHighlightElements.forEach(element => {\n            if (isHTMLElement$1(element)) {\n              element.classList.remove('shepherd-target-click-disabled');\n            }\n          });\n        }\n      }\n    });\n  }\n}\n\n/**\n * Generates the svg path data for a rounded rectangle overlay\n * @param dimension - Dimensions of rectangle.\n * @param dimension.width - Width.\n * @param dimension.height - Height.\n * @param dimension.x - Offset from top left corner in x axis. default 0.\n * @param dimension.y - Offset from top left corner in y axis. default 0.\n * @param dimension.r - Corner Radius. Keep this smaller than half of width or height.\n * @returns Rounded rectangle overlay path data.\n */\nfunction makeOverlayPath(overlayPaths) {\n  let openings = '';\n  const {\n    innerWidth: w,\n    innerHeight: h\n  } = window;\n  overlayPaths.forEach(overlayPath => {\n    const {\n      width,\n      height,\n      x = 0,\n      y = 0,\n      r = 0\n    } = overlayPath;\n    const {\n      topLeft = 0,\n      topRight = 0,\n      bottomRight = 0,\n      bottomLeft = 0\n    } = typeof r === 'number' ? {\n      topLeft: r,\n      topRight: r,\n      bottomRight: r,\n      bottomLeft: r\n    } : r;\n    openings += `M${x + topLeft},${y}\\\n      a${topLeft},${topLeft},0,0,0-${topLeft},${topLeft}\\\n      V${height + y - bottomLeft}\\\n      a${bottomLeft},${bottomLeft},0,0,0,${bottomLeft},${bottomLeft}\\\n      H${width + x - bottomRight}\\\n      a${bottomRight},${bottomRight},0,0,0,${bottomRight}-${bottomRight}\\\n      V${y + topRight}\\\n      a${topRight},${topRight},0,0,0-${topRight}-${topRight}\\\n      Z`;\n  });\n  return `M${w},${h}\\\n          H0\\\n          V0\\\n          H${w}\\\n          V${h}\\\n          Z\\\n          ${openings}`.replace(/\\s/g, '');\n}\n\n/* src/components/shepherd-modal.svelte generated by Svelte v4.2.19 */\nfunction create_fragment(ctx) {\n  let svg;\n  let path;\n  let svg_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      svg = svg_element(\"svg\");\n      path = svg_element(\"path\");\n      attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      attr(svg, \"class\", svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`);\n    },\n    m(target, anchor) {\n      insert(target, svg, anchor);\n      append(svg, path);\n      /*svg_binding*/\n      ctx[11](svg);\n      if (!mounted) {\n        dispose = listen(svg, \"touchmove\", /*_preventModalOverlayTouch*/ctx[3]);\n        mounted = true;\n      }\n    },\n    p(ctx, [dirty]) {\n      if (dirty & /*pathDefinition*/4) {\n        attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      }\n      if (dirty & /*modalIsVisible*/2 && svg_class_value !== (svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`)) {\n        attr(svg, \"class\", svg_class_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) {\n        detach(svg);\n      }\n\n      /*svg_binding*/\n      ctx[11](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction _getScrollParent(element) {\n  if (!element) {\n    return null;\n  }\n  const isHtmlElement = element instanceof HTMLElement;\n  const overflowY = isHtmlElement && window.getComputedStyle(element).overflowY;\n  const isScrollable = overflowY !== 'hidden' && overflowY !== 'visible';\n  if (isScrollable && element.scrollHeight >= element.clientHeight) {\n    return element;\n  }\n  return _getScrollParent(element.parentElement);\n}\n\n/**\n * Get the top and left offset required to position the modal overlay cutout\n * when the target element is within an iframe\n * @param {HTMLElement} element The target element\n * @private\n */\nfunction _getIframeOffset(element) {\n  let offset = {\n    top: 0,\n    left: 0\n  };\n  if (!element) {\n    return offset;\n  }\n  let targetWindow = element.ownerDocument.defaultView;\n  while (targetWindow !== window.top) {\n    var _targetWindow;\n    const targetIframe = (_targetWindow = targetWindow) == null ? void 0 : _targetWindow.frameElement;\n    if (targetIframe) {\n      var _targetIframeRect$scr, _targetIframeRect$scr2;\n      const targetIframeRect = targetIframe.getBoundingClientRect();\n      offset.top += targetIframeRect.top + ((_targetIframeRect$scr = targetIframeRect.scrollTop) != null ? _targetIframeRect$scr : 0);\n      offset.left += targetIframeRect.left + ((_targetIframeRect$scr2 = targetIframeRect.scrollLeft) != null ? _targetIframeRect$scr2 : 0);\n    }\n    targetWindow = targetWindow.parent;\n  }\n  return offset;\n}\n\n/**\n * Get the visible height of the target element relative to its scrollParent.\n * If there is no scroll parent, the height of the element is returned.\n *\n * @param {HTMLElement} element The target element\n * @param {HTMLElement} [scrollParent] The scrollable parent element\n * @returns {{y: number, height: number}}\n * @private\n */\nfunction _getVisibleHeight(element, scrollParent) {\n  const elementRect = element.getBoundingClientRect();\n  let top = elementRect.y || elementRect.top;\n  let bottom = elementRect.bottom || top + elementRect.height;\n  if (scrollParent) {\n    const scrollRect = scrollParent.getBoundingClientRect();\n    const scrollTop = scrollRect.y || scrollRect.top;\n    const scrollBottom = scrollRect.bottom || scrollTop + scrollRect.height;\n    top = Math.max(top, scrollTop);\n    bottom = Math.min(bottom, scrollBottom);\n  }\n  const height = Math.max(bottom - top, 0); // Default to 0 if height is negative\n  return {\n    y: top,\n    height\n  };\n}\nfunction instance($$self, $$props, $$invalidate) {\n  let {\n    element,\n    openingProperties\n  } = $$props;\n  let modalIsVisible = false;\n  let rafId = undefined;\n  let pathDefinition;\n  closeModalOpening();\n  const getElement = () => element;\n  function closeModalOpening() {\n    $$invalidate(4, openingProperties = [{\n      width: 0,\n      height: 0,\n      x: 0,\n      y: 0,\n      r: 0\n    }]);\n  }\n  function hide() {\n    $$invalidate(1, modalIsVisible = false);\n\n    // Ensure we cleanup all event listeners when we hide the modal\n    _cleanupStepEventListeners();\n  }\n  function positionModal(modalOverlayOpeningPadding = 0, modalOverlayOpeningRadius = 0, modalOverlayOpeningXOffset = 0, modalOverlayOpeningYOffset = 0, scrollParent, targetElement, extraHighlights) {\n    if (targetElement) {\n      const elementsToHighlight = [targetElement, ...(extraHighlights || [])];\n      $$invalidate(4, openingProperties = []);\n      for (const element of elementsToHighlight) {\n        if (!element) continue;\n\n        // Skip duplicate elements\n        if (elementsToHighlight.indexOf(element) !== elementsToHighlight.lastIndexOf(element)) {\n          continue;\n        }\n        const {\n          y,\n          height\n        } = _getVisibleHeight(element, scrollParent);\n        const {\n          x,\n          width,\n          left\n        } = element.getBoundingClientRect();\n\n        // Check if the element is contained by another element\n        const isContained = elementsToHighlight.some(otherElement => {\n          if (otherElement === element) return false;\n          const otherRect = otherElement.getBoundingClientRect();\n          return x >= otherRect.left && x + width <= otherRect.right && y >= otherRect.top && y + height <= otherRect.bottom;\n        });\n        if (isContained) continue;\n\n        // getBoundingClientRect is not consistent. Some browsers use x and y, while others use left and top\n        openingProperties.push({\n          width: width + modalOverlayOpeningPadding * 2,\n          height: height + modalOverlayOpeningPadding * 2,\n          x: (x || left) + modalOverlayOpeningXOffset - modalOverlayOpeningPadding,\n          y: y + modalOverlayOpeningYOffset - modalOverlayOpeningPadding,\n          r: modalOverlayOpeningRadius\n        });\n      }\n    } else {\n      closeModalOpening();\n    }\n  }\n  function setupForStep(step) {\n    // Ensure we move listeners from the previous step, before we setup new ones\n    _cleanupStepEventListeners();\n    if (step.tour.options.useModalOverlay) {\n      _styleForStep(step);\n      show();\n    } else {\n      hide();\n    }\n  }\n  function show() {\n    $$invalidate(1, modalIsVisible = true);\n  }\n  const _preventModalBodyTouch = e => {\n    e.preventDefault();\n  };\n  const _preventModalOverlayTouch = e => {\n    e.stopPropagation();\n  };\n\n  /**\n  * Add touchmove event listener\n  * @private\n  */\n  function _addStepEventListeners() {\n    // Prevents window from moving on touch.\n    window.addEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Cancel the requestAnimationFrame loop and remove touchmove event listeners\n  * @private\n  */\n  function _cleanupStepEventListeners() {\n    if (rafId) {\n      cancelAnimationFrame(rafId);\n      rafId = undefined;\n    }\n    window.removeEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Style the modal for the step\n  * @param {Step} step The step to style the opening for\n  * @private\n  */\n  function _styleForStep(step) {\n    const {\n      modalOverlayOpeningPadding,\n      modalOverlayOpeningRadius,\n      modalOverlayOpeningXOffset = 0,\n      modalOverlayOpeningYOffset = 0\n    } = step.options;\n    const iframeOffset = _getIframeOffset(step.target);\n    const scrollParent = _getScrollParent(step.target);\n\n    // Setup recursive function to call requestAnimationFrame to update the modal opening position\n    const rafLoop = () => {\n      rafId = undefined;\n      positionModal(modalOverlayOpeningPadding, modalOverlayOpeningRadius, modalOverlayOpeningXOffset + iframeOffset.left, modalOverlayOpeningYOffset + iframeOffset.top, scrollParent, step.target, step._resolvedExtraHighlightElements);\n      rafId = requestAnimationFrame(rafLoop);\n    };\n    rafLoop();\n    _addStepEventListeners();\n  }\n  function svg_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('openingProperties' in $$props) $$invalidate(4, openingProperties = $$props.openingProperties);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*openingProperties*/16) {\n      $$invalidate(2, pathDefinition = makeOverlayPath(openingProperties));\n    }\n  };\n  return [element, modalIsVisible, pathDefinition, _preventModalOverlayTouch, openingProperties, getElement, closeModalOpening, hide, positionModal, setupForStep, show, svg_binding];\n}\nclass Shepherd_modal extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance, create_fragment, safe_not_equal, {\n      element: 0,\n      openingProperties: 4,\n      getElement: 5,\n      closeModalOpening: 6,\n      hide: 7,\n      positionModal: 8,\n      setupForStep: 9,\n      show: 10\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[5];\n  }\n  get closeModalOpening() {\n    return this.$$.ctx[6];\n  }\n  get hide() {\n    return this.$$.ctx[7];\n  }\n  get positionModal() {\n    return this.$$.ctx[8];\n  }\n  get setupForStep() {\n    return this.$$.ctx[9];\n  }\n  get show() {\n    return this.$$.ctx[10];\n  }\n}\n\n/**\n * The options for the tour\n */\n\nclass ShepherdBase extends Evented {\n  constructor() {\n    super();\n    this.activeTour = void 0;\n    autoBind(this);\n  }\n}\n\n/**\n * Class representing the site tour\n * @extends {Evented}\n */\nclass Tour extends Evented {\n  constructor(options = {}) {\n    super();\n    this.trackedEvents = ['active', 'cancel', 'complete', 'show'];\n    this.classPrefix = void 0;\n    this.currentStep = void 0;\n    this.focusedElBeforeOpen = void 0;\n    this.id = void 0;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this.modal = void 0;\n    this.options = void 0;\n    this.steps = void 0;\n    autoBind(this);\n    const defaultTourOptions = {\n      exitOnEsc: true,\n      keyboardNavigation: true\n    };\n    this.options = Object.assign({}, defaultTourOptions, options);\n    this.classPrefix = normalizePrefix(this.options.classPrefix);\n    this.steps = [];\n    this.addSteps(this.options.steps);\n\n    // Pass these events onto the global Shepherd object\n    const events = ['active', 'cancel', 'complete', 'inactive', 'show', 'start'];\n    events.map(event => {\n      (e => {\n        this.on(e, opts => {\n          opts = opts || {};\n          opts['tour'] = this;\n          Shepherd.trigger(e, opts);\n        });\n      })(event);\n    });\n    this._setTourID(options.id);\n    return this;\n  }\n\n  /**\n   * Adds a new step to the tour\n   * @param {StepOptions} options - An object containing step options or a Step instance\n   * @param {number | undefined} index - The optional index to insert the step at. If undefined, the step\n   * is added to the end of the array.\n   * @return The newly added step\n   */\n  addStep(options, index) {\n    let step = options;\n    if (!(step instanceof Step)) {\n      step = new Step(this, step);\n    } else {\n      step.tour = this;\n    }\n    if (!isUndefined(index)) {\n      this.steps.splice(index, 0, step);\n    } else {\n      this.steps.push(step);\n    }\n    return step;\n  }\n\n  /**\n   * Add multiple steps to the tour\n   * @param {Array<StepOptions> | Array<Step> | undefined} steps - The steps to add to the tour\n   */\n  addSteps(steps) {\n    if (Array.isArray(steps)) {\n      steps.forEach(step => {\n        this.addStep(step);\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Go to the previous step in the tour\n   */\n  back() {\n    const index = this.steps.indexOf(this.currentStep);\n    this.show(index - 1, false);\n  }\n\n  /**\n   * Calls _done() triggering the 'cancel' event\n   * If `confirmCancel` is true, will show a window.confirm before cancelling\n   * If `confirmCancel` is a function, will call it and wait for the return value,\n   * and only cancel when the value returned is true\n   */\n  async cancel() {\n    if (this.options.confirmCancel) {\n      const cancelMessage = this.options.confirmCancelMessage || 'Are you sure you want to stop the tour?';\n      let stopTour;\n      if (isFunction(this.options.confirmCancel)) {\n        stopTour = await this.options.confirmCancel();\n      } else {\n        stopTour = window.confirm(cancelMessage);\n      }\n      if (stopTour) {\n        this._done('cancel');\n      }\n    } else {\n      this._done('cancel');\n    }\n  }\n\n  /**\n   * Calls _done() triggering the `complete` event\n   */\n  complete() {\n    this._done('complete');\n  }\n\n  /**\n   * Gets the step from a given id\n   * @param {number | string} id - The id of the step to retrieve\n   * @return The step corresponding to the `id`\n   */\n  getById(id) {\n    return this.steps.find(step => {\n      return step.id === id;\n    });\n  }\n\n  /**\n   * Gets the current step\n   */\n  getCurrentStep() {\n    return this.currentStep;\n  }\n\n  /**\n   * Hide the current step\n   */\n  hide() {\n    const currentStep = this.getCurrentStep();\n    if (currentStep) {\n      return currentStep.hide();\n    }\n  }\n\n  /**\n   * Check if the tour is active\n   */\n  isActive() {\n    return Shepherd.activeTour === this;\n  }\n\n  /**\n   * Go to the next step in the tour\n   * If we are at the end, call `complete`\n   */\n  next() {\n    const index = this.steps.indexOf(this.currentStep);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      this.show(index + 1, true);\n    }\n  }\n\n  /**\n   * Removes the step from the tour\n   * @param {string} name - The id for the step to remove\n   */\n  removeStep(name) {\n    const current = this.getCurrentStep();\n\n    // Find the step, destroy it and remove it from this.steps\n    this.steps.some((step, i) => {\n      if (step.id === name) {\n        if (step.isOpen()) {\n          step.hide();\n        }\n        step.destroy();\n        this.steps.splice(i, 1);\n        return true;\n      }\n    });\n    if (current && current.id === name) {\n      this.currentStep = undefined;\n\n      // If we have steps left, show the first one, otherwise just cancel the tour\n      this.steps.length ? this.show(0) : this.cancel();\n    }\n  }\n\n  /**\n   * Show a specific step in the tour\n   * @param {number | string} key - The key to look up the step by\n   * @param {boolean} forward - True if we are going forward, false if backward\n   */\n  show(key = 0, forward = true) {\n    const step = isString(key) ? this.getById(key) : this.steps[key];\n    if (step) {\n      this._updateStateBeforeShow();\n      const shouldSkipStep = isFunction(step.options.showOn) && !step.options.showOn();\n\n      // If `showOn` returns false, we want to skip the step, otherwise, show the step like normal\n      if (shouldSkipStep) {\n        this._skipStep(step, forward);\n      } else {\n        this.currentStep = step;\n        this.trigger('show', {\n          step,\n          previous: this.currentStep\n        });\n        step.show();\n      }\n    }\n  }\n\n  /**\n   * Start the tour\n   */\n  async start() {\n    this.trigger('start');\n\n    // Save the focused element before the tour opens\n    this.focusedElBeforeOpen = document.activeElement;\n    this.currentStep = null;\n    this.setupModal();\n    this._setupActiveTour();\n    this.next();\n  }\n\n  /**\n   * Called whenever the tour is cancelled or completed, basically anytime we exit the tour\n   * @param {string} event - The event name to trigger\n   * @private\n   */\n  _done(event) {\n    const index = this.steps.indexOf(this.currentStep);\n    if (Array.isArray(this.steps)) {\n      this.steps.forEach(step => step.destroy());\n    }\n    cleanupSteps(this);\n    this.trigger(event, {\n      index\n    });\n    Shepherd.activeTour = null;\n    this.trigger('inactive', {\n      tour: this\n    });\n    if (this.modal) {\n      this.modal.hide();\n    }\n    if (event === 'cancel' || event === 'complete') {\n      if (this.modal) {\n        const modalContainer = document.querySelector('.shepherd-modal-overlay-container');\n        if (modalContainer) {\n          modalContainer.remove();\n          this.modal = null;\n        }\n      }\n    }\n\n    // Focus the element that was focused before the tour started\n    if (isHTMLElement$1(this.focusedElBeforeOpen)) {\n      this.focusedElBeforeOpen.focus();\n    }\n  }\n\n  /**\n   * Make this tour \"active\"\n   */\n  _setupActiveTour() {\n    this.trigger('active', {\n      tour: this\n    });\n    Shepherd.activeTour = this;\n  }\n\n  /**\n   * setupModal create the modal container and instance\n   */\n  setupModal() {\n    this.modal = new Shepherd_modal({\n      target: this.options.modalContainer || document.body,\n      props: {\n        // @ts-expect-error TODO: investigate where styles comes from\n        styles: this.styles\n      }\n    });\n  }\n\n  /**\n   * Called when `showOn` evaluates to false, to skip the step or complete the tour if it's the last step\n   * @param {Step} step - The step to skip\n   * @param {boolean} forward - True if we are going forward, false if backward\n   * @private\n   */\n  _skipStep(step, forward) {\n    const index = this.steps.indexOf(step);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      const nextIndex = forward ? index + 1 : index - 1;\n      this.show(nextIndex, forward);\n    }\n  }\n\n  /**\n   * Before showing, hide the current step and if the tour is not\n   * already active, call `this._setupActiveTour`.\n   * @private\n   */\n  _updateStateBeforeShow() {\n    if (this.currentStep) {\n      this.currentStep.hide();\n    }\n    if (!this.isActive()) {\n      this._setupActiveTour();\n    }\n  }\n\n  /**\n   * Sets this.id to a provided tourName and id or `${tourName}--${uuid}`\n   * @param {string} optionsId - True if we are going forward, false if backward\n   * @private\n   */\n  _setTourID(optionsId) {\n    const tourName = this.options.tourName || 'tour';\n    const tourId = optionsId || uuid();\n    this.id = `${tourName}--${tourId}`;\n  }\n}\n\n/**\n * @public\n */\nconst Shepherd = new ShepherdBase();\n\nconst isServerSide = typeof window === 'undefined';\nShepherd.Step = isServerSide ? StepNoOp : Step;\nShepherd.Tour = isServerSide ? TourNoOp : Tour;\n// Reexport types so they can be more easily used.\n\n\n//# sourceMappingURL=shepherd.mjs.map\n\n\n//# sourceURL=webpack://Materialize/./node_modules/shepherd.js/dist/esm/shepherd.mjs?")}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/shepherd/shepherd.js");return __webpack_exports__}()}));