/* Typeahead
******************************************************************************* */
.twitter-typeahead {
  display: block !important;
}
.twitter-typeahead .tt-menu {
  border: 0 solid var(--bs-border-color);
  background-clip: padding-box;
  background-color: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow-lg);
  color: var(--bs-body-color);
  font-size: 0.9375rem;
  inset-inline: 0 auto !important;
  margin-block: 0.25rem;
  min-inline-size: 10rem;
  padding-block: 0.5rem;
  border-radius: 0.375rem;
}
.twitter-typeahead .tt-menu .tt-suggestion {
  color: var(--bs-heading-color);
  cursor: pointer;
  padding-block: 0.543rem;
  padding-inline: 1.25rem;
  white-space: nowrap;
}
.twitter-typeahead .tt-menu .tt-suggestion:hover, .twitter-typeahead .tt-menu .tt-suggestion:focus {
  background-color: var(--bs-gray-50);
  color: var(--bs-dropdown-link-color);
  text-decoration: none;
}
.twitter-typeahead .tt-menu .tt-suggestion p {
  margin: 0;
}
.twitter-typeahead .tt-menu .tt-suggestion .tt-highlight {
  font-weight: 500;
}
.twitter-typeahead .tt-menu .tt-suggestion:active,
.twitter-typeahead .tt-menu .tt-cursor {
  background: var(--bs-primary);
  color: var(--bs-primary-contrast);
}
.twitter-typeahead .tt-hint {
  color: var(--bs-gray-400);
}
.twitter-typeahead .tt-input {
  direction: ltr;
}
:dir(rtl) .twitter-typeahead .tt-input {
  direction: rtl;
}
