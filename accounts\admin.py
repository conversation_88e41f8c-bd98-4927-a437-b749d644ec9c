from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import DemandeInscription, StatutDemande


@admin.register(DemandeInscription)
class DemandeInscriptionAdmin(admin.ModelAdmin):
    """Interface d'administration pour les demandes d'inscription"""

    list_display = (
        'nom_organisation',
        'get_nom_complet_admin',
        'email_admin',
        'type_abonnement_demande',
        'statut_badge',
        'date_demande',
        'actions_rapides'
    )

    list_filter = (
        'statut',
        'type_abonnement_demande',
        'date_demande',
        'date_traitement'
    )

    search_fields = (
        'nom_organisation',
        'email_organisation',
        'prenom_admin',
        'nom_admin',
        'email_admin'
    )

    readonly_fields = (
        'date_demande',
        'date_traitement',
        'organisation_creee',
        'utilisateur_cree'
    )

    fieldsets = (
        ('Informations de l\'organisation', {
            'fields': (
                'nom_organisation',
                'adresse_organisation',
                'telephone_organisation',
                'email_organisation',
                'type_abonnement_demande'
            )
        }),
        ('Informations de l\'administrateur', {
            'fields': (
                'prenom_admin',
                'nom_admin',
                'email_admin',
                'telephone_admin'
            )
        }),
        ('Gestion de la demande', {
            'fields': (
                'statut',
                'commentaire_admin',
                'date_demande',
                'date_traitement',
                'traite_par'
            )
        }),
        ('Relations créées', {
            'fields': (
                'organisation_creee',
                'utilisateur_cree'
            ),
            'classes': ('collapse',)
        })
    )

    actions = ['valider_demandes', 'rejeter_demandes']

    def get_nom_complet_admin(self, obj):
        return obj.get_nom_complet_admin()
    get_nom_complet_admin.short_description = "Administrateur"

    def statut_badge(self, obj):
        colors = {
            'EN_ATTENTE': 'orange',
            'VALIDEE': 'green',
            'REJETEE': 'red'
        }
        color = colors.get(obj.statut, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_statut_display()
        )
    statut_badge.short_description = "Statut"

    def actions_rapides(self, obj):
        if obj.est_en_attente():
            return format_html(
                '<a class="button" href="{}">Valider</a> '
                '<a class="button" href="{}">Rejeter</a>',
                reverse('admin:accounts_demandeinscription_change', args=[obj.pk]),
                reverse('admin:accounts_demandeinscription_change', args=[obj.pk])
            )
        return "-"
    actions_rapides.short_description = "Actions"

    def valider_demandes(self, request, queryset):
        """Action pour valider plusieurs demandes"""
        count = 0
        for demande in queryset.filter(statut=StatutDemande.EN_ATTENTE):
            # TODO: Implémenter la logique de validation
            demande.statut = StatutDemande.VALIDEE
            demande.date_traitement = timezone.now()
            demande.traite_par = request.user
            demande.save()
            count += 1

        self.message_user(
            request,
            f"{count} demande(s) validée(s) avec succès."
        )
    valider_demandes.short_description = "Valider les demandes sélectionnées"

    def rejeter_demandes(self, request, queryset):
        """Action pour rejeter plusieurs demandes"""
        count = 0
        for demande in queryset.filter(statut=StatutDemande.EN_ATTENTE):
            demande.statut = StatutDemande.REJETEE
            demande.date_traitement = timezone.now()
            demande.traite_par = request.user
            demande.save()
            count += 1

        self.message_user(
            request,
            f"{count} demande(s) rejetée(s)."
        )
    rejeter_demandes.short_description = "Rejeter les demandes sélectionnées"
