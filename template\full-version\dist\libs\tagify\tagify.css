@charset "UTF-8";
:root {
  --tagify-dd-color-primary: rgb(53,149,246);
  --tagify-dd-text-color: black;
  --tagify-dd-bg-color: white;
  --tagify-dd-item-pad: .3em .5em;
  --tagify-dd-max-height: 300px;
}

.tagify {
  --tags-disabled-bg: #F1F1F1;
  --tags-border-color: #DDD;
  --tags-hover-border-color: #CCC;
  --tags-focus-border-color: #3595f6;
  --tag-border-radius: 3px;
  --tag-bg: #E5E5E5;
  --tag-hover: #D3E2E2;
  --tag-text-color: black;
  --tag-text-color--edit: black;
  --tag-pad: 0.3em 0.5em;
  --tag-inset-shadow-size: 1.1em;
  --tag-invalid-color: #D39494;
  --tag-invalid-bg: rgba(211, 148, 148, 0.5);
  --tag--min-width: 1ch;
  --tag--max-width: 100%;
  --tag-hide-transition: 0.3s;
  --tag-remove-bg: rgba(211, 148, 148, 0.3);
  --tag-remove-btn-color: black;
  --tag-remove-btn-bg: none;
  --tag-remove-btn-bg--hover: #c77777;
  --input-color: var(--bs-heading-color);
  --placeholder-color: rgba(0, 0, 0, 0.4);
  --placeholder-color-focus: rgba(0, 0, 0, 0.25);
  --loader-size: .8em;
  --readonly-striped: 1;
  display: inline-flex;
  align-items: flex-start;
  flex-wrap: wrap;
  border: 1px solid var(--tags-border-color);
  padding: 0;
  line-height: 0;
  outline: none;
  position: relative;
  box-sizing: border-box;
  transition: 0.1s;
}
@keyframes tags--bump {
  30% {
    transform: scale(1.2);
  }
}
@keyframes rotateLoader {
  to {
    transform: rotate(1turn);
  }
}
.tagify:has([contenteditable=true]) {
  cursor: text;
}
.tagify:hover:not(.tagify--focus):not(.tagify--invalid) {
  --tags-border-color: var(--tags-hover-border-color);
}
.tagify[disabled] {
  background: var(--tags-disabled-bg);
  filter: saturate(0);
  opacity: 0.5;
  pointer-events: none;
}
.tagify[readonly].tagify--select, .tagify[disabled].tagify--select {
  pointer-events: none;
}
.tagify[readonly]:not(.tagify--mix):not(.tagify--select), .tagify[disabled]:not(.tagify--mix):not(.tagify--select) {
  cursor: default;
}
.tagify[readonly]:not(.tagify--mix):not(.tagify--select) > .tagify__input, .tagify[disabled]:not(.tagify--mix):not(.tagify--select) > .tagify__input {
  visibility: hidden;
  width: 0;
  margin: 5px 0;
}
.tagify[readonly]:not(.tagify--mix):not(.tagify--select) .tagify__tag > div, .tagify[disabled]:not(.tagify--mix):not(.tagify--select) .tagify__tag > div {
  padding: var(--tag-pad);
}
.tagify[readonly]:not(.tagify--mix):not(.tagify--select) .tagify__tag > div::before, .tagify[disabled]:not(.tagify--mix):not(.tagify--select) .tagify__tag > div::before {
  animation: readonlyStyles 1s calc(-1s * (var(--readonly-striped) - 1)) paused;
}
@keyframes readonlyStyles {
  0% {
    background: linear-gradient(45deg, var(--tag-bg) 25%, transparent 25%, transparent 50%, var(--tag-bg) 50%, var(--tag-bg) 75%, transparent 75%, transparent) 0/5px 5px;
    box-shadow: none;
    filter: brightness(0.95);
  }
}
.tagify[readonly] .tagify__tag__removeBtn, .tagify[disabled] .tagify__tag__removeBtn {
  display: none;
}
.tagify--loading .tagify__input > br:last-child {
  display: none;
}
.tagify--loading .tagify__input::before {
  content: none;
}
.tagify--loading .tagify__input::after {
  content: "";
  vertical-align: middle;
  opacity: 1;
  width: 0.7em;
  height: 0.7em;
  width: var(--loader-size);
  height: var(--loader-size);
  min-width: 0;
  border: 3px solid;
  border-color: #EEE #BBB #888 transparent;
  border-radius: 50%;
  animation: rotateLoader 0.4s infinite linear;
  content: "" !important;
  margin: -2px 0 -2px 0.5em;
}
.tagify--loading .tagify__input:empty::after {
  margin-left: 0;
}
.tagify + input,
.tagify + textarea {
  position: absolute !important;
  left: -9999em !important;
  transform: scale(0) !important;
}
.tagify__tag {
  display: inline-flex;
  align-items: center;
  max-width: var(--tag--max-width);
  margin-inline: 5px 0;
  margin-block: 5px;
  position: relative;
  z-index: 1;
  outline: none;
  line-height: normal;
  cursor: default;
  transition: 0.13s ease-out;
}
.tagify__tag > div {
  display: flex;
  flex: 1;
  vertical-align: top;
  box-sizing: border-box;
  max-width: 100%;
  padding: var(--tag-pad);
  color: var(--tag-text-color);
  line-height: inherit;
  border-radius: var(--tag-border-radius);
  white-space: nowrap;
  transition: 0.13s ease-out;
}
.tagify__tag > div > * {
  white-space: pre-wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: top;
  min-width: var(--tag--min-width);
  max-width: var(--tag--max-width);
  transition: 0.8s ease, 0.1s color;
}
.tagify__tag > div > *[contenteditable] {
  display: block;
  outline: none;
  user-select: text;
  cursor: text;
  margin: -2px;
  padding: 2px;
  max-width: 350px;
}
.tagify__tag > div > *:only-child {
  width: 100%;
}
.tagify__tag > div::before {
  content: "";
  position: absolute;
  border-radius: inherit;
  inset: var(--tag-bg-inset, 0);
  z-index: -1;
  pointer-events: none;
  transition: 120ms ease;
  animation: tags--bump 0.3s ease-out 1;
  box-shadow: 0 0 0 var(--tag-inset-shadow-size) var(--tag-bg) inset;
}
.tagify__tag:hover:not([readonly]) div::before, .tagify__tag:focus div::before {
  --tag-bg-inset: -2.5px;
  --tag-bg: var(--tag-hover);
}
.tagify__tag--loading {
  pointer-events: none;
}
.tagify__tag--loading .tagify__tag__removeBtn {
  display: none;
}
.tagify__tag--loading::after {
  --loader-size: .4em;
  content: "";
  vertical-align: middle;
  opacity: 1;
  width: 0.7em;
  height: 0.7em;
  width: var(--loader-size);
  height: var(--loader-size);
  min-width: 0;
  border: 3px solid;
  border-color: #EEE #BBB #888 transparent;
  border-radius: 50%;
  animation: rotateLoader 0.4s infinite linear;
  margin: 0 0.5em 0 -0.1em;
}
.tagify__tag--flash div::before {
  animation: none;
}
.tagify__tag--hide {
  width: 0 !important;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
  opacity: 0;
  transform: scale(0);
  transition: var(--tag-hide-transition);
  pointer-events: none;
}
.tagify__tag--hide > div > * {
  white-space: nowrap;
}
.tagify__tag.tagify--noAnim > div::before {
  animation: none;
}
.tagify__tag.tagify--notAllowed:not(.tagify__tag--editable) div > span {
  opacity: 0.5;
}
.tagify__tag.tagify--notAllowed:not(.tagify__tag--editable) div::before {
  --tag-bg: var(--tag-invalid-bg);
  transition: 0.2s;
}
.tagify__tag[readonly] .tagify__tag__removeBtn {
  display: none;
}
.tagify__tag[readonly] > div::before {
  animation: readonlyStyles 1s calc(-1s * (var(--readonly-striped) - 1)) paused;
}
@keyframes readonlyStyles {
  0% {
    background: linear-gradient(45deg, var(--tag-bg) 25%, transparent 25%, transparent 50%, var(--tag-bg) 50%, var(--tag-bg) 75%, transparent 75%, transparent) 0/5px 5px;
    box-shadow: none;
    filter: brightness(0.95);
  }
}
.tagify__tag--editable > div {
  color: var(--tag-text-color--edit);
}
.tagify__tag--editable > div::before {
  box-shadow: 0 0 0 2px var(--tag-hover) inset !important;
}
.tagify__tag--editable > .tagify__tag__removeBtn {
  pointer-events: none;
  opacity: 0;
  transform: translateX(100%) translateX(5px);
}
.tagify__tag--editable.tagify--invalid > div::before {
  box-shadow: 0 0 0 2px var(--tag-invalid-color) inset !important;
}
.tagify__tag__removeBtn {
  order: 5;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  cursor: pointer;
  font: 14px/1 Arial;
  background: var(--tag-remove-btn-bg);
  color: var(--tag-remove-btn-color);
  width: 14px;
  height: 14px;
  margin-inline: auto 4.6666666667px;
  overflow: hidden;
  transition: 0.2s ease-out;
}
.tagify__tag__removeBtn::after {
  content: "×";
  transition: 0.3s, color 0s;
}
.tagify__tag__removeBtn:hover {
  color: white;
  background: var(--tag-remove-btn-bg--hover);
}
.tagify__tag__removeBtn:hover + div > span {
  opacity: 0.5;
}
.tagify__tag__removeBtn:hover + div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size) var(--tag-remove-bg, rgba(211, 148, 148, 0.3)) inset !important;
  transition: box-shadow 0.2s;
}
.tagify:not(.tagify--mix) .tagify__input br {
  display: none;
}
.tagify:not(.tagify--mix) .tagify__input * {
  display: inline;
  white-space: nowrap;
}
.tagify__input {
  flex-grow: 1;
  display: inline-block;
  min-width: 110px;
  margin: 5px;
  padding: var(--tag-pad);
  line-height: normal;
  position: relative;
  white-space: pre-wrap;
  color: var(--input-color);
  box-sizing: inherit;
  overflow: hidden;
  /* Seems firefox newer versions don't need this any more
  @supports ( -moz-appearance:none ){
      &::before{
          line-height: inherit;
          position:relative;
      }
  }
  */
}
@-moz-document url-prefix() {}
.tagify__input:focus {
  outline: none;
}
.tagify__input:focus::before {
  transition: 0.2s ease-out;
  opacity: 0;
  transform: translatex(6px);
  /* ALL MS BROWSERS: hide placeholder (on focus) otherwise the caret is placed after it, which is weird */
  /* IE Edge 12+ CSS styles go here */
}
@supports (-ms-ime-align: auto) {
  .tagify__input:focus::before {
    display: none;
  }
}
.tagify__input:focus:empty::before {
  transition: 0.2s ease-out;
  opacity: 1;
  transform: none;
  color: rgba(0, 0, 0, 0.25);
  color: var(--placeholder-color-focus);
}
@-moz-document url-prefix() {
  .tagify__input:focus:empty::after {
    display: none;
  }
}
.tagify__input::before {
  content: attr(data-placeholder);
  width: 100%;
  height: 100%;
  margin: auto 0;
  z-index: 1;
  color: var(--placeholder-color);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  pointer-events: none;
  opacity: 0;
  position: absolute;
}
.tagify__input::after {
  content: attr(data-suggest);
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  min-width: calc(100% - 1.5em);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: pre; /* allows spaces at the beginning */
  color: var(--tag-text-color);
  opacity: 0.3;
  pointer-events: none;
  max-width: 100px;
}
.tagify__input .tagify__tag {
  margin: 0 1px;
}
.tagify--mix {
  display: block;
}
.tagify--mix .tagify__input {
  padding: 5px;
  margin: 0;
  width: 100%;
  height: 100%;
  line-height: 1.5;
  display: block;
}
.tagify--mix .tagify__input::before {
  height: auto;
  display: none;
  line-height: inherit;
}
.tagify--mix .tagify__input::after {
  content: none;
}
.tagify--select {
  cursor: default;
}
.tagify--select::after {
  content: ">";
  opacity: 0.5;
  position: absolute;
  top: 50%;
  right: 0;
  bottom: 0;
  font: 16px monospace;
  line-height: 8px;
  height: 8px;
  pointer-events: none;
  transform: translate(-150%, -50%) scaleX(1.2) rotate(90deg);
  transition: 0.2s ease-in-out;
}
.tagify--select[aria-expanded=true]::after {
  transform: translate(-150%, -50%) rotate(270deg) scaleY(1.2);
}
.tagify--select[aria-expanded=true] .tagify__tag__removeBtn {
  pointer-events: none;
  opacity: 0;
  transform: translateX(100%) translateX(5px);
}
.tagify--select .tagify__tag {
  flex: 1;
  max-width: none;
  margin-inline-end: 2em;
  margin-block: 0;
  padding-block: 5px;
  cursor: text;
}
.tagify--select .tagify__tag div::before {
  display: none;
}
.tagify--select .tagify__tag + .tagify__input {
  display: none;
}
.tagify--empty .tagify__input::before {
  transition: 0.2s ease-out;
  opacity: 1;
  transform: none;
  display: inline-block;
  width: auto;
}
.tagify--mix .tagify--empty .tagify__input::before {
  display: inline-block;
}
.tagify--focus {
  --tags-border-color: var(--tags-focus-border-color);
  transition: 0s;
}
.tagify--invalid {
  --tags-border-color: #D39494;
}
.tagify__dropdown {
  position: absolute;
  z-index: 9999;
  transform: translateY(-1px);
  border-top: 1px solid var(--tagify-dd-color-primary);
  overflow: hidden;
}
.tagify__dropdown[dir=rtl] {
  transform: translate(-100%, -1px);
}
.tagify__dropdown[placement=top] {
  margin-top: 0;
  transform: translateY(-100%);
}
.tagify__dropdown[placement=top] .tagify__dropdown__wrapper {
  border-top-width: 1.1px;
  border-bottom-width: 0;
}
.tagify__dropdown[position=text] {
  box-shadow: 0 0 0 3px rgba(var(--tagify-dd-color-primary), 0.1);
  font-size: 0.9em;
}
.tagify__dropdown[position=text] .tagify__dropdown__wrapper {
  border-width: 1px;
}
.tagify__dropdown__wrapper {
  scroll-behavior: auto;
  max-height: var(--tagify-dd-max-height);
  overflow: hidden;
  overflow-x: hidden;
  color: var(--tagify-dd-text-color);
  background: var(--tagify-dd-bg-color);
  border: 1px solid;
  border-color: var(--tagify-dd-color-primary);
  border-bottom-width: 1.5px;
  border-top-width: 0;
  box-shadow: 0 2px 4px -2px rgba(0, 0, 0, 0.2);
  transition: 0.3s cubic-bezier(0.5, 0, 0.3, 1), transform 0.15s;
  animation: dd-wrapper-show 0s 0.3s forwards;
}
@keyframes dd-wrapper-show {
  to {
    overflow-y: auto;
  }
}
.tagify__dropdown__header:empty {
  display: none;
}
.tagify__dropdown__footer {
  display: inline-block;
  margin-top: 0.5em;
  padding: var(--tagify-dd-item-pad);
  font-size: 0.7em;
  font-style: italic;
  opacity: 0.5;
}
.tagify__dropdown__footer:empty {
  display: none;
}
.tagify__dropdown--initial .tagify__dropdown__wrapper {
  max-height: 20px;
  transform: translateY(-1em);
}
.tagify__dropdown--initial[placement=top] .tagify__dropdown__wrapper {
  transform: translateY(2em);
}
.tagify__dropdown__item {
  box-sizing: border-box;
  padding: var(--tagify-dd-item-pad);
  margin: 1px;
  white-space: pre-wrap;
  cursor: pointer;
  border-radius: 2px;
  position: relative;
  outline: none;
  max-height: 60px;
  max-width: 100%;
  line-height: normal;
  position: relative;
  /* custom hidden transition effect is needed for horizontal-layout suggestions */
}
.tagify__dropdown__item--active {
  background: var(--tagify-dd-color-primary);
  color: white;
}
.tagify__dropdown__item:active {
  filter: brightness(105%);
}
.tagify__dropdown__item--hidden {
  padding-top: 0;
  padding-bottom: 0;
  margin: 0 1px;
  pointer-events: none;
  overflow: hidden;
  max-height: 0;
  transition: var(--tagify-dd-item--hidden-duration, 0.3s) !important;
}
.tagify__dropdown__item--hidden > * {
  transform: translateY(-100%);
  opacity: 0;
  transition: inherit;
}
.tagify__dropdown__item--selected::before {
  content: "✓";
  font-family: monospace;
  position: absolute;
  inset-inline-start: 6px;
  text-indent: 0;
  line-height: 1.1;
}
.tagify__dropdown:has(.tagify__dropdown__item--selected) .tagify__dropdown__item {
  text-indent: 1em;
}

:root {
  --tagify-dd-color-primary: var(--bs-primary-rgb);
  --tagify-dd-bg-color: var(--bs-paper-bg);
  --tagify-item-margin: .3125rem;
  --tagify-item-padding: .5rem;
  --tagify-item-active-bg: var(--bs-primary);
  --tagify-item-active-color: var(--bs-primary-contrast);
  --tagify-item-active-border-color: var(--tagify-item-active-bg);
  --tagify-dropdown-box-shadow: var(--bs-box-shadow-lg);
  --tagify-dropdown-border-width: 0;
}

/* Height calc to match form-control height */
/* Override tagify vars */
.tagify {
  --tags-border-color: color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg));
  --tags-hover-border-color: color-mix(in sRGB, var(--bs-base-color) 60%, var(--bs-paper-bg));
  --tag-border-radius: 50rem;
  --tag-bg: color-mix(in sRGB, var(--bs-base-color) 6%, var(--bs-paper-bg));
  --tag-hover: color-mix(in sRGB, var(--bs-base-color) 15%, var(--bs-paper-bg));
  --tag-text-color: var(--bs-heading-color);
  --tag-text-color--edit: var(--bs-heading-color);
  --tag-pad: 0;
  --tags-disabled-bg: color-mix(in sRGB, var(--bs-base-color) 6%, var(--bs-paper-bg));
  --tags-disabled-color: var(--bs-secondary-color);
  --tags-disabled-border-color: color-mix(in sRGB, var(--bs-base-color) 24%, var(--bs-paper-bg));
  --tag-remove-bg: rgba(var(--bs-danger-rgb), .3);
  --tag-remove-btn-color: color-mix(in sRGB, var(--bs-base-color) 35%, var(--bs-paper-bg));
  --tag-remove-btn-bg--hover: var(--bs-danger);
  --placeholder-color: var(--bs-gray-400);
  --placeholder-color-focus: var(--bs-gray-400);
  padding: 1px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tagify {
    transition: none;
  }
}
.form-floating.form-floating-outline .tagify.form-control {
  block-size: auto;
  padding-block: 0.375rem;
  padding-inline: 0.4375rem;
}
.tagify.tagify--focus.form-control, .tagify.form-control:focus {
  block-size: auto;
  padding-block: calc(0.375rem - 1px) !important;
  padding-inline: calc(0.4375rem - 1px) !important;
}
.tagify.tagify--focus, .tagify.form-control:focus, .tagify:focus {
  padding: 0;
  border-width: 2px;
  border-color: var(--bs-primary);
  box-shadow: none;
}
.tagify.tagify--focus:not(.placeholder-shown) ~ label, .tagify.form-control:focus:not(.placeholder-shown) ~ label, .tagify:focus:not(.placeholder-shown) ~ label {
  color: var(--bs-primary);
}
.tagify__tag, .tagify__input {
  line-height: 1.5rem;
}
.tagify__input:first-child {
  padding-inline-end: calc(1rem - 2px + 5px);
}
.tagify__tag > div {
  align-items: center;
  padding-inline-start: var(--tagify-item-padding);
}
.tagify__tag > div > * {
  white-space: nowrap;
}
.tagify__tag__removeBtn {
  block-size: 1rem;
  font-size: inherit;
  inline-size: 1rem;
  margin-inline: calc(var(--tagify-item-padding) * 0.3571) calc(var(--tagify-item-padding) * 0.5429);
  opacity: 0.7;
}
.tagify__tag__removeBtn:hover {
  background-color: transparent;
}
.tagify__tag__removeBtn:hover::after {
  background-color: var(--bs-danger);
}
.tagify__tag__removeBtn::after {
  display: inline-block;
  background-color: var(--bs-gray-900);
  block-size: 1rem;
  content: "";
  inline-size: 1rem;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10s10-4.486 10-10S17.514 2 12 2m4.207 12.793l-1.414 1.414L12 13.414l-2.793 2.793l-1.414-1.414L10.586 12L7.793 9.207l1.414-1.414L12 10.586l2.793-2.793l1.414 1.414L13.414 12z'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-size: 100% 100%;
}
.tagify__tag:hover:not([readonly]) div::before, .tagify__tag:focus div::before {
  inset: 0;
}
.tagify__dropdown {
  border: none;
  box-shadow: var(--tagify-dropdown-box-shadow);
  transform: translateY(0);
}
.tagify__dropdown__wrapper {
  border-width: var(--tagify-dropdown-border-width);
  border-color: var(--bs-border-color);
  background-color: var(--bs-paper-bg);
  color: var(--bs-heading-color);
  border-bottom-right-radius: 0.625rem;
  border-bottom-left-radius: 0.625rem;
}
.tagify__dropdown__item {
  border-radius: 0.25rem;
}
.tagify[readonly]:not(.tagify--mix):not(.tagify--select) .tagify__tag > div, .tagify[disabled]:not(.tagify--mix):not(.tagify--select) .tagify__tag > div {
  padding-inline: var(--tagify-item-padding);
}
.tagify__tag-text {
  font-size: 0.8125rem;
  font-weight: 500;
}
.tagify[readonly]:not(.tagify--focus):not(.tagify--invalid), .tagify[readonly]:not(.tagify--focus):not(.tagify--invalid):hover, .tagify[readonly]:not(.tagify--focus):not(.tagify--invalid):focus {
  border-color: color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg));
  box-shadow: none;
}
.tagify[readonly]:not(.tagify--mix) .tagify__tag > div::before {
  background: linear-gradient(45deg, color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg)) 25%, transparent 25%, transparent 50%, color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg)) 50%, color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg)) 75%, transparent 75%, transparent) 0/5px 5px;
}
.tagify[disabled] {
  border-color: var(--tags-disabled-border-color);
}
.tagify[disabled] > div {
  color: var(--tags-disabled-color);
}
.tagify[disabled]:not(.tagify--mix) .tagify__tag > div::before {
  background: linear-gradient(45deg, color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg)) 25%, transparent 25%, transparent 50%, color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg)) 50%, color-mix(in sRGB, var(--bs-base-color) 22%, var(--bs-paper-bg)) 75%, transparent 75%, transparent) 0/5px 5px;
}

/* Suggestions items */
.tagify__dropdown.users-list .tagify__dropdown__item {
  display: grid;
  gap: 0 1em;
  grid-template-areas: "avatar name" "avatar email";
  grid-template-columns: auto 1fr;
}
.tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap {
  overflow: hidden;
  background: var(--bs-body-bg);
  block-size: 36px;
  grid-area: avatar;
  inline-size: 36px;
  transition: 0.1s ease-out;
  border-radius: 50%;
}
.tagify__dropdown.users-list .tagify__dropdown__item__addAll {
  border-block-end: 1px solid color-mix(in sRGB, #262b43 12%, #fff);
  gap: 0;
}
.tagify__dropdown.users-list img {
  inline-size: 100%;
  vertical-align: top;
}
.tagify__dropdown.users-list strong {
  align-self: center;
  font-weight: 500;
  grid-area: name;
  inline-size: 100%;
}
.tagify__dropdown.users-list span {
  font-size: 0.9em;
  grid-area: email;
  inline-size: 100%;
  opacity: 0.6;
}

/* Tags items */
.tagify__tag {
  white-space: nowrap;
}
.tagify__tag .tagify__tag__avatar-wrap {
  border-radius: 50%;
  background: var(--bs-body-bg);
  block-size: 22px;
  inline-size: 22px;
  margin-inline-end: 5px;
  transition: 0.12s ease-out;
  vertical-align: middle;
  white-space: normal;
}
.tagify__tag img {
  inline-size: 100%;
  vertical-align: top;
}

.tags-inline .tagify__dropdown__wrapper {
  padding-block: 0 var(--tagify-item-padding);
  padding-inline: var(--tagify-item-padding);
}
.tags-inline .tagify__dropdown__item {
  display: inline-block;
  border: 1px solid var(--bs-border-color);
  border-radius: 3px;
  color: var(--bs-body-color);
  font-size: 0.85em;
  margin-block: var(--tagify-item-margin) 0;
  margin-inline: 0 var(--tagify-item-margin);
  padding-block: 0.3em;
  padding-inline: 0.5em;
  transition: 0s;
  vertical-align: middle;
}
.tags-inline .tagify__dropdown__item--active {
  border-color: var(--tagify-item-active-border-color);
  background: var(--tagify-item-active-bg);
  color: var(--tagify-item-active-color);
}

.tagify-email-list {
  display: inline-block;
  border: none;
  min-inline-size: 0;
  /* Do not show the "remove tag" (x) button when only a single tag remains */
}
.tagify-email-list.tagify {
  padding: 0;
  margin-inline-start: calc(var(--tagify-item-margin) * -1);
}
.tagify-email-list.tagify, .tagify-email-list.tagify--focus {
  border: none;
  box-shadow: none;
  padding-block: 0 var(--tagify-item-margin);
}
.tagify-email-list .tagify__tag {
  line-height: 1.7rem;
  margin-block-end: 0;
  margin-inline-end: 0.625rem;
}
.tagify-email-list .tagify__tag > div {
  padding-inline-end: calc(var(--tagify-item-padding) * 3);
  padding-inline-start: calc(var(--tagify-item-padding) * 2);
}
.tagify-email-list .tagify__tag:only-of-type > div {
  padding-inline: calc(var(--tagify-item-padding) * 2);
}
.tagify-email-list .tagify__tag:hover .tagify__tag__removeBtn:not(:hover) {
  background-color: var(--tag-remove-btn-color);
  color: var(--tag-text-color);
}
.tagify-email-list .tagify__tag:only-of-type .tagify__tag__removeBtn {
  display: none;
}
.tagify-email-list .tagify__tag__removeBtn {
  position: absolute;
  z-index: 1;
  font-size: inherit;
  inset-inline-end: 0;
  margin-inline: -20px 6px;
  opacity: 0;
  transform: translateX(-100%) scale(0.5);
  transition: 0.12s;
}
.tagify-email-list .tagify__tag:not(.tagify__tag--editable):hover .tagify__tag__removeBtn {
  opacity: 1;
  transform: none;
}
.tagify-email-list .tagify__input {
  display: none;
}

.tagify__dropdown__item--active {
  background: var(--tagify-item-active-bg);
  color: var(--tagify-item-active-color);
}
