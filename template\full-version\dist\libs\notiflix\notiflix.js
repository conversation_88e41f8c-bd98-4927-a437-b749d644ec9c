!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var o in t)("object"==typeof exports?exports:e)[o]=t[o]}}(self,(function(){return function(){var __webpack_modules__={"./libs/notiflix/notiflix.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Block: function() { return /* reexport safe */ notiflix_build_notiflix_block_aio__WEBPACK_IMPORTED_MODULE_1__.Block; },\n/* harmony export */   Loading: function() { return /* reexport safe */ notiflix_build_notiflix_loading_aio__WEBPACK_IMPORTED_MODULE_0__.Loading; }\n/* harmony export */ });\n/* harmony import */ var notiflix_build_notiflix_loading_aio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! notiflix/build/notiflix-loading-aio */ "./node_modules/notiflix/build/notiflix-loading-aio.js");\n/* harmony import */ var notiflix_build_notiflix_loading_aio__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(notiflix_build_notiflix_loading_aio__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var notiflix_build_notiflix_block_aio__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! notiflix/build/notiflix-block-aio */ "./node_modules/notiflix/build/notiflix-block-aio.js");\n/* harmony import */ var notiflix_build_notiflix_block_aio__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(notiflix_build_notiflix_block_aio__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\n//# sourceURL=webpack://Materialize/./libs/notiflix/notiflix.js?')},"./node_modules/notiflix/build/notiflix-block-aio.js":function(module,exports,__webpack_require__){eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*\n* Notiflix Block AIO (https://notiflix.github.io)\n* Description: This file has been created automatically that using \"notiflix.js\", and \"notiflix.css\" files.\n* Version: 3.2.8\n* Author: Furkan (https://github.com/furcan)\n* Copyright 2019 - 2025 Notiflix, MIT License (https://opensource.org/licenses/MIT)\n*/\n\n/* global define */\n(function (root, factory) {\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      return factory(root);\n    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n})(typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof window !== 'undefined' ? window : this, function (window) {\n\n  'use strict';\n\n  // COMMON: SSR check: begin\n  if (typeof window === 'undefined' && typeof window.document === 'undefined') {\n    return false;\n  }\n  // COMMON: SSR check: end\n\n  // COMMON: Variables: begin\n  var notiflixNamespace = 'Notiflix';\n  var notiflixConsoleDocs = '\\n\\nVisit documentation page to learn more: https://notiflix.github.io/documentation';\n  var defaultFontFamily = '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif';\n  // COMMON: Variables: end\n\n  // BLOCK: Default Settings: begin\n  var typesBlock = {\n    Standard: 'Standard',\n    Hourglass: 'Hourglass',\n    Circle: 'Circle',\n    Arrows: 'Arrows',\n    Dots: 'Dots',\n    Pulse: 'Pulse',\n  };\n  var newBlockSettings;\n  var blockSettings = {\n    ID: 'NotiflixBlockWrap', // can not customizable\n    querySelectorLimit: 200,\n    className: 'notiflix-block',\n    position: 'absolute',\n    zindex: 1000,\n    backgroundColor: 'rgba(255,255,255,0.9)',\n    rtl: false,\n    fontFamily: 'Quicksand',\n    cssAnimation: true,\n    cssAnimationDuration: 300,\n    svgSize: '45px',\n    svgColor: '#383838',\n    messageFontSize: '14px',\n    messageMaxLength: 34,\n    messageColor: '#383838',\n  };\n  // BLOCK: Default Settings: end\n\n  // COMMON: Console Error: begin\n  var commonConsoleError = function (message) {\n    return console.error('%c ' + notiflixNamespace + ' Error ', 'padding:2px;border-radius:20px;color:#fff;background:#ff5549', '\\n' + message + notiflixConsoleDocs);\n  };\n  // COMMON: Console Error: end\n\n  // COMMON: Console Log: begin\n  var commonConsoleLog = function (message) {\n    return console.log('%c ' + notiflixNamespace + ' Info ', 'padding:2px;border-radius:20px;color:#fff;background:#26c0d3', '\\n' + message + notiflixConsoleDocs);\n  };\n  // COMMON: Console Log: end\n\n  // COMMON: Check Head or Body: begin\n  var commonCheckHeadOrBody = function (element) {\n    if (!element) { element = 'head'; }\n    if (window.document[element] === undefined) {\n      commonConsoleError('\\nNotiflix needs to be appended to the \"<' + element + '>\" element, but you called it before the \"<' + element + '>\" element has been created.');\n      return false;\n    }\n    return true;\n  };\n  // COMMON: Check Head or Body: end\n\n  // COMMON: Set Internal CSS Codes: begin\n  var commonSetInternalCSSCodes = function (getInternalCSSCodes, styleElementId) {\n    // check doc head\n    if (!commonCheckHeadOrBody('head')) { return false; }\n\n    // internal css\n    if (getInternalCSSCodes() !== null && !window.document.getElementById(styleElementId)) {\n      var internalCSS = window.document.createElement('style');\n      internalCSS.id = styleElementId;\n      internalCSS.innerHTML = getInternalCSSCodes();\n      window.document.head.appendChild(internalCSS);\n    }\n  };\n  // COMMON: Set Internal CSS Codes: end\n\n  // COMMON: Extend Options: begin\n  var commonExtendOptions = function () {\n    // variables\n    var extended = {};\n    var deep = false;\n    var i = 0;\n    // check if a deep merge\n    if (Object.prototype.toString.call(arguments[0]) === '[object Boolean]') {\n      deep = arguments[0];\n      i++;\n    }\n    // merge the object into the extended object\n    var merge = function (obj) {\n      for (var prop in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n          // if property is an object, merge properties\n          if (deep && Object.prototype.toString.call(obj[prop]) === '[object Object]') {\n            extended[prop] = commonExtendOptions(extended[prop], obj[prop]);\n          } else {\n            extended[prop] = obj[prop];\n          }\n        }\n      }\n    };\n    // loop through each object and conduct a merge\n    for (; i < arguments.length; i++) {\n      merge(arguments[i]);\n    }\n    return extended;\n  };\n  // COMMON: Extend Options: end\n\n  // COMMON: Get Plaintext: begin\n  var commonGetPlaintext = function (html) {\n    var htmlPool = window.document.createElement('div');\n    htmlPool.innerHTML = html;\n    return htmlPool.textContent || htmlPool.innerText || '';\n  };\n  // COMMON: Get Plaintext: end\n\n  // LOADING && BLOCK: SVG Icon Standard: begin\n  var loadingAndBlockSvgIconStandard = function (width, color) {\n    if (!width) { width = '60px'; }\n    if (!color) { color = '#32c682'; }\n    var standard = '<svg xmlns=\"http://www.w3.org/2000/svg\" stroke=\"' + color + '\" width=\"' + width + '\" height=\"' + width + '\" transform=\"scale(.8)\" viewBox=\"0 0 38 38\"><g fill=\"none\" fill-rule=\"evenodd\" stroke-width=\"2\" transform=\"translate(1 1)\"><circle cx=\"18\" cy=\"18\" r=\"18\" stroke-opacity=\".25\"/><path d=\"M36 18c0-9.94-8.06-18-18-18\"><animateTransform attributeName=\"transform\" dur=\"1s\" from=\"0 18 18\" repeatCount=\"indefinite\" to=\"360 18 18\" type=\"rotate\"/></path></g></svg>';\n    return standard;\n  };\n  // LOADING && BLOCK: SVG Icon Standard: end\n\n  // LOADING && BLOCK: SVG Icon Hourglass: begin\n  var loadingAndBlockSvgIconHourglass = function (width, color) {\n    if (!width) { width = '60px'; }\n    if (!color) { color = '#32c682'; }\n    var hourglass = '<svg xmlns=\"http://www.w3.org/2000/svg\" id=\"NXLoadingHourglass\" fill=\"' + color + '\" width=\"' + width + '\" height=\"' + width + '\" viewBox=\"0 0 200 200\"><style>@-webkit-keyframes NXhourglass5-animation{0%{-webkit-transform:scale(1,1);transform:scale(1,1)}16.67%{-webkit-transform:scale(1,.8);transform:scale(1,.8)}33.33%{-webkit-transform:scale(.88,.6);transform:scale(.88,.6)}37.5%{-webkit-transform:scale(.85,.55);transform:scale(.85,.55)}41.67%{-webkit-transform:scale(.8,.5);transform:scale(.8,.5)}45.83%{-webkit-transform:scale(.75,.45);transform:scale(.75,.45)}50%{-webkit-transform:scale(.7,.4);transform:scale(.7,.4)}54.17%{-webkit-transform:scale(.6,.35);transform:scale(.6,.35)}58.33%{-webkit-transform:scale(.5,.3);transform:scale(.5,.3)}83.33%,to{-webkit-transform:scale(.2,0);transform:scale(.2,0)}}@keyframes NXhourglass5-animation{0%{-webkit-transform:scale(1,1);transform:scale(1,1)}16.67%{-webkit-transform:scale(1,.8);transform:scale(1,.8)}33.33%{-webkit-transform:scale(.88,.6);transform:scale(.88,.6)}37.5%{-webkit-transform:scale(.85,.55);transform:scale(.85,.55)}41.67%{-webkit-transform:scale(.8,.5);transform:scale(.8,.5)}45.83%{-webkit-transform:scale(.75,.45);transform:scale(.75,.45)}50%{-webkit-transform:scale(.7,.4);transform:scale(.7,.4)}54.17%{-webkit-transform:scale(.6,.35);transform:scale(.6,.35)}58.33%{-webkit-transform:scale(.5,.3);transform:scale(.5,.3)}83.33%,to{-webkit-transform:scale(.2,0);transform:scale(.2,0)}}@-webkit-keyframes NXhourglass3-animation{0%{-webkit-transform:scale(1,.02);transform:scale(1,.02)}79.17%,to{-webkit-transform:scale(1,1);transform:scale(1,1)}}@keyframes NXhourglass3-animation{0%{-webkit-transform:scale(1,.02);transform:scale(1,.02)}79.17%,to{-webkit-transform:scale(1,1);transform:scale(1,1)}}@-webkit-keyframes NXhourglass1-animation{0%,83.33%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes NXhourglass1-animation{0%,83.33%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}#NXLoadingHourglass *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g data-animator-group=\"true\" data-animator-type=\"1\" style=\"-webkit-animation-name:NXhourglass1-animation;animation-name:NXhourglass1-animation;-webkit-transform-origin:50% 50%;transform-origin:50% 50%;transform-box:fill-box\"><g id=\"NXhourglass2\" fill=\"inherit\"><g data-animator-group=\"true\" data-animator-type=\"2\" style=\"-webkit-animation-name:NXhourglass3-animation;animation-name:NXhourglass3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform-origin:50% 100%;transform-origin:50% 100%;transform-box:fill-box\" opacity=\".4\"><path id=\"NXhourglass4\" d=\"M100 100l-34.38 32.08v31.14h68.76v-31.14z\"/></g><g data-animator-group=\"true\" data-animator-type=\"2\" style=\"-webkit-animation-name:NXhourglass5-animation;animation-name:NXhourglass5-animation;-webkit-transform-origin:50% 100%;transform-origin:50% 100%;transform-box:fill-box\" opacity=\".4\"><path id=\"NXhourglass6\" d=\"M100 100L65.62 67.92V36.78h68.76v31.14z\"/></g><path d=\"M51.14 38.89h8.33v14.93c0 15.1 8.29 28.99 23.34 39.1 1.88 1.25 3.04 3.97 3.04 7.08s-1.16 5.83-3.04 7.09c-15.05 10.1-23.34 23.99-23.34 39.09v14.93h-8.33a4.859 4.859 0 1 0 0 9.72h97.72a4.859 4.859 0 1 0 0-9.72h-8.33v-14.93c0-15.1-8.29-28.99-23.34-39.09-1.88-1.26-3.04-3.98-3.04-7.09s1.16-5.83 3.04-7.08c15.05-10.11 23.34-24 23.34-39.1V38.89h8.33a4.859 4.859 0 1 0 0-9.72H51.14a4.859 4.859 0 1 0 0 9.72zm79.67 14.93c0 15.87-11.93 26.25-19.04 31.03-4.6 3.08-7.34 8.75-7.34 15.15 0 6.41 2.74 12.07 7.34 15.15 7.11 4.78 19.04 15.16 19.04 31.03v14.93H69.19v-14.93c0-15.87 11.93-26.25 19.04-31.02 4.6-3.09 7.34-8.75 7.34-15.16 0-6.4-2.74-12.07-7.34-15.15-7.11-4.78-19.04-15.16-19.04-31.03V38.89h61.62v14.93z\"/></g></g></svg>';\n    return hourglass;\n  };\n  // LOADING && BLOCK: SVG Icon Hourglass: end\n\n  // LOADING && BLOCK: SVG Icon Circle: begin\n  var loadingAndBlockSvgIconCircle = function (width, color) {\n    if (!width) { width = '60px'; }\n    if (!color) { color = '#32c682'; }\n    var circle = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"' + width + '\" height=\"' + width + '\" viewBox=\"25 25 50 50\" style=\"-webkit-animation:rotate 2s linear infinite;animation:rotate 2s linear infinite;height:' + width + ';-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;width:' + width + ';position:absolute;top:0;left:0;margin:auto\"><style>@-webkit-keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}to{stroke-dasharray:89,200;stroke-dashoffset:-124}}@keyframes dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}to{stroke-dasharray:89,200;stroke-dashoffset:-124}}</style><circle cx=\"50\" cy=\"50\" r=\"20\" fill=\"none\" stroke=\"' + color + '\" stroke-width=\"2\" style=\"-webkit-animation:dash 1.5s ease-in-out infinite,color 1.5s ease-in-out infinite;animation:dash 1.5s ease-in-out infinite,color 1.5s ease-in-out infinite\" stroke-dasharray=\"150 200\" stroke-dashoffset=\"-10\" stroke-linecap=\"round\"/></svg>';\n    return circle;\n  };\n  // LOADING && BLOCK: SVG Icon Circle: end\n\n  // LOADING && BLOCK: SVG Icon Arrows: begin\n  var loadingAndBlockSvgIconArrows = function (width, color) {\n    if (!width) { width = '60px'; }\n    if (!color) { color = '#32c682'; }\n    var arrows = '<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"' + color + '\" width=\"' + width + '\" height=\"' + width + '\" viewBox=\"0 0 128 128\"><g><path fill=\"inherit\" d=\"M109.25 55.5h-36l12-12a29.54 29.54 0 0 0-49.53 12H18.75A46.04 46.04 0 0 1 96.9 31.84l12.35-12.34v36zm-90.5 17h36l-12 12a29.54 29.54 0 0 0 49.53-12h16.97A46.04 46.04 0 0 1 31.1 96.16L18.74 108.5v-36z\"/><animateTransform attributeName=\"transform\" dur=\"1.5s\" from=\"0 64 64\" repeatCount=\"indefinite\" to=\"360 64 64\" type=\"rotate\"/></g></svg>';\n    return arrows;\n  };\n  // LOADING && BLOCK: SVG Icon Arrows: end\n\n  // LOADING && BLOCK: SVG Icon Dots: begin\n  var loadingAndBlockSvgIconDots = function (width, color) {\n    if (!width) { width = '60px'; }\n    if (!color) { color = '#32c682'; }\n    var dots = '<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"' + color + '\" width=\"' + width + '\" height=\"' + width + '\" viewBox=\"0 0 100 100\"><g transform=\"translate(25 50)\"><circle r=\"9\" fill=\"inherit\" transform=\"scale(.239)\"><animateTransform attributeName=\"transform\" begin=\"-0.266s\" calcMode=\"spline\" dur=\"0.8s\" keySplines=\"0.3 0 0.7 1;0.3 0 0.7 1\" keyTimes=\"0;0.5;1\" repeatCount=\"indefinite\" type=\"scale\" values=\"0;1;0\"/></circle></g><g transform=\"translate(50 50)\"><circle r=\"9\" fill=\"inherit\" transform=\"scale(.00152)\"><animateTransform attributeName=\"transform\" begin=\"-0.133s\" calcMode=\"spline\" dur=\"0.8s\" keySplines=\"0.3 0 0.7 1;0.3 0 0.7 1\" keyTimes=\"0;0.5;1\" repeatCount=\"indefinite\" type=\"scale\" values=\"0;1;0\"/></circle></g><g transform=\"translate(75 50)\"><circle r=\"9\" fill=\"inherit\" transform=\"scale(.299)\"><animateTransform attributeName=\"transform\" begin=\"0s\" calcMode=\"spline\" dur=\"0.8s\" keySplines=\"0.3 0 0.7 1;0.3 0 0.7 1\" keyTimes=\"0;0.5;1\" repeatCount=\"indefinite\" type=\"scale\" values=\"0;1;0\"/></circle></g></svg>';\n    return dots;\n  };\n  // LOADING && BLOCK: SVG Icon Dots: end\n\n  // LOADING && BLOCK: SVG Icon Pulse: begin\n  var loadingAndBlockSvgIconPulse = function (width, color) {\n    if (!width) { width = '60px'; }\n    if (!color) { color = '#32c682'; }\n    var pulse = '<svg xmlns=\"http://www.w3.org/2000/svg\" stroke=\"' + color + '\" width=\"' + width + '\" height=\"' + width + '\" viewBox=\"0 0 44 44\"><g fill=\"none\" fill-rule=\"evenodd\" stroke-width=\"2\"><circle cx=\"22\" cy=\"22\" r=\"1\"><animate attributeName=\"r\" begin=\"0s\" calcMode=\"spline\" dur=\"1.8s\" keySplines=\"0.165, 0.84, 0.44, 1\" keyTimes=\"0; 1\" repeatCount=\"indefinite\" values=\"1; 20\"/><animate attributeName=\"stroke-opacity\" begin=\"0s\" calcMode=\"spline\" dur=\"1.8s\" keySplines=\"0.3, 0.61, 0.355, 1\" keyTimes=\"0; 1\" repeatCount=\"indefinite\" values=\"1; 0\"/></circle><circle cx=\"22\" cy=\"22\" r=\"1\"><animate attributeName=\"r\" begin=\"-0.9s\" calcMode=\"spline\" dur=\"1.8s\" keySplines=\"0.165, 0.84, 0.44, 1\" keyTimes=\"0; 1\" repeatCount=\"indefinite\" values=\"1; 20\"/><animate attributeName=\"stroke-opacity\" begin=\"-0.9s\" calcMode=\"spline\" dur=\"1.8s\" keySplines=\"0.3, 0.61, 0.355, 1\" keyTimes=\"0; 1\" repeatCount=\"indefinite\" values=\"1; 0\"/></circle></g></svg>';\n    return pulse;\n  };\n  // LOADING && BLOCK: SVG Icon Pulse: end\n\n  // BLOCK: Get Internal CSS Codes: begin\n  var blockGetInternalCSSCodes = function () {\n    var blockCSS = '[id^=NotiflixBlockWrap]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1000;font-family:\"Quicksand\",-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;background:rgba(255,255,255,.9);text-align:center;animation-duration:.4s;width:100%;height:100%;left:0;top:0;border-radius:inherit;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}[id^=NotiflixBlockWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixBlockWrap]>span[class*=\"-icon\"]{display:block;width:45px;height:45px;position:relative;margin:0 auto}[id^=NotiflixBlockWrap]>span[class*=\"-icon\"] svg{width:inherit;height:inherit}[id^=NotiflixBlockWrap]>span[class*=\"-message\"]{position:relative;display:block;width:100%;margin:10px auto 0;padding:0 10px;font-family:inherit!important;font-weight:normal;font-size:14px;line-height:1.4}[id^=NotiflixBlockWrap].nx-with-animation{-webkit-animation:block-animation-fade .3s ease-in-out 0s normal;animation:block-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes block-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes block-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixBlockWrap].nx-with-animation.nx-remove{opacity:0;-webkit-animation:block-animation-fade-remove .3s ease-in-out 0s normal;animation:block-animation-fade-remove .3s ease-in-out 0s normal}@-webkit-keyframes block-animation-fade-remove{0%{opacity:1}100%{opacity:0}}@keyframes block-animation-fade-remove{0%{opacity:1}100%{opacity:0}}';\n    return blockCSS || null;\n  };\n  // BLOCK: Get Internal CSS Codes: end\n\n  // BLOCK: Create or Remove: begin\n  var blockCreateOrRemoveCounter = 0;\n  var blockCreateOrRemove = function (isCreate, blockType, selectorOrHTMLElements, messageOrOptions, options, delay) {\n    var allHTMLElements;\n\n    // if, check and set Array of HTMLElements\n    if (Array.isArray(selectorOrHTMLElements)) {\n      if (selectorOrHTMLElements.length < 1) {\n        commonConsoleError('Array of HTMLElements should contains at least one HTMLElement.');\n        return false;\n      }\n      allHTMLElements = selectorOrHTMLElements;\n    }\n    // else if, check and set NodeListOf<HTMLElement>\n    else if (Object.prototype.isPrototypeOf.call(NodeList.prototype, selectorOrHTMLElements)) {\n      if (selectorOrHTMLElements.length < 1) {\n        commonConsoleError('NodeListOf<HTMLElement> should contains at least one HTMLElement.');\n        return false;\n      }\n      allHTMLElements = Array.prototype.slice.call(selectorOrHTMLElements);\n    }\n    // else, check and set the selector\n    else {\n      // check selector is valid: begin\n      var selectorIsNotValid = (typeof selectorOrHTMLElements !== 'string') || ((selectorOrHTMLElements || '').length < 1) || ((selectorOrHTMLElements || '').length === 1 && ((selectorOrHTMLElements || '')[0] === '#' || (selectorOrHTMLElements || '')[0] === '.'));\n      if (selectorIsNotValid) {\n        commonConsoleError('The selector parameter must be a string and matches a specified CSS selector(s).');\n        return false;\n      }\n      // check selector is valid: end\n\n      // check the selector: begin\n      var nodeListOfHTMLElements = window.document.querySelectorAll(selectorOrHTMLElements);\n      if (nodeListOfHTMLElements.length < 1) {\n        commonConsoleError('You called the \"Notiflix.Block...\" function with \"' + selectorOrHTMLElements + '\" selector, but there is no such element(s) in the document.');\n        return false;\n      }\n      // check the selector: end\n\n      allHTMLElements = nodeListOfHTMLElements;\n    }\n\n    // if not initialized pretend like init: begin\n    if (!newBlockSettings) {\n      Notiflix.Block.init({});\n    }\n    // if not initialized pretend like init: end\n\n    // create a backup for new settings\n    var newBlockSettingsBackup = commonExtendOptions(true, newBlockSettings, {});\n\n    // check \"messageOrOptions\" and \"options\": begin\n    if ((typeof messageOrOptions === 'object' && !Array.isArray(messageOrOptions)) || (typeof options === 'object' && !Array.isArray(options))) {\n      // new options\n      var newOptions = {};\n      if (typeof messageOrOptions === 'object') {\n        newOptions = messageOrOptions;\n      } else if (typeof options === 'object') {\n        newOptions = options;\n      }\n\n      // extend new settings with the new options\n      newBlockSettings = commonExtendOptions(true, newBlockSettings, newOptions);\n    }\n    // check \"messageOrOptions\" and \"options\": end\n\n    // check the message: begin\n    var message = '';\n    if (typeof messageOrOptions === 'string' && messageOrOptions.length > 0) {\n      message = messageOrOptions;\n    }\n    // check the message: end\n\n    // if cssAnimation is false => duration: begin\n    if (!newBlockSettings.cssAnimation) {\n      newBlockSettings.cssAnimationDuration = 0;\n    }\n    // if cssAnimation is false => duration: end\n\n    // check the class name: begin\n    var blockClassName = blockSettings.className;\n    if (typeof newBlockSettings.className === 'string') {\n      blockClassName = newBlockSettings.className.trim();\n    }\n    // check the class name: end\n\n    // check query limit: begin\n    var getQueryLimit = typeof newBlockSettings.querySelectorLimit === 'number' ? newBlockSettings.querySelectorLimit : 200;\n    var checkQueryLimit = (allHTMLElements || []).length >= getQueryLimit ? getQueryLimit : allHTMLElements.length;\n    // check query limit: end\n\n    // position class name for the non-static reference elements\n    var positionClassForNonStaticRef = 'nx-block-temporary-position';\n\n    // block\n    if (isCreate) {\n      // void and unavailable elements\n      var voidAndUnavailableElements = ['area', 'base', 'br', 'col', 'command', 'embed', 'hr', 'img', 'input', 'keygen', 'link', 'meta', 'param', 'source', 'track', 'wbr', 'html', 'head', 'title', 'script', 'style', 'iframe'];\n\n      // add element(s) and style: begin\n      for (var queryIndex = 0; queryIndex < checkQueryLimit; queryIndex++) {\n        var eachElement = allHTMLElements[queryIndex];\n        if (eachElement) {\n          if (voidAndUnavailableElements.indexOf(eachElement.tagName.toLocaleLowerCase('en')) > -1) {\n            break;\n          }\n\n          // check block element exist: begin\n          var eachBlockElement = eachElement.querySelectorAll('[id^=' + blockSettings.ID + ']');\n          if (eachBlockElement.length < 1) {\n\n            // check the icon: begin\n            var icon = '';\n            if (blockType) {\n              if (blockType === typesBlock.Hourglass) {\n                icon = loadingAndBlockSvgIconHourglass(newBlockSettings.svgSize, newBlockSettings.svgColor);\n              } else if (blockType === typesBlock.Circle) {\n                icon = loadingAndBlockSvgIconCircle(newBlockSettings.svgSize, newBlockSettings.svgColor);\n              } else if (blockType === typesBlock.Arrows) {\n                icon = loadingAndBlockSvgIconArrows(newBlockSettings.svgSize, newBlockSettings.svgColor);\n              } else if (blockType === typesBlock.Dots) {\n                icon = loadingAndBlockSvgIconDots(newBlockSettings.svgSize, newBlockSettings.svgColor);\n              } else if (blockType === typesBlock.Pulse) {\n                icon = loadingAndBlockSvgIconPulse(newBlockSettings.svgSize, newBlockSettings.svgColor);\n              } else { // typesBlock.Standard, also fallback\n                icon = loadingAndBlockSvgIconStandard(newBlockSettings.svgSize, newBlockSettings.svgColor);\n              }\n            }\n            var iconElement = '<span class=\"' + blockClassName + '-icon\" style=\"width:' + newBlockSettings.svgSize + ';height:' + newBlockSettings.svgSize + ';\">' + icon + '</span>';\n            // check the icon: end\n\n            // check the message: begin\n            var messageElement = '';\n            if (message.length > 0) {\n              if (message.length > newBlockSettings.messageMaxLength) {\n                message = commonGetPlaintext(message).substring(0, newBlockSettings.messageMaxLength) + '...';\n              } else {\n                message = commonGetPlaintext(message);\n              }\n              messageElement = '<span style=\"font-size:' + newBlockSettings.messageFontSize + ';color:' + newBlockSettings.messageColor + ';\" class=\"' + blockClassName + '-message\">' + message + '</span>';\n            }\n            // check the message: end\n\n            // block element: begin\n            blockCreateOrRemoveCounter++;\n            var notiflixBlockWrap = window.document.createElement('div');\n            notiflixBlockWrap.id = blockSettings.ID + '-' + blockCreateOrRemoveCounter;\n            notiflixBlockWrap.className = blockClassName + (newBlockSettings.cssAnimation ? ' nx-with-animation' : '');\n            notiflixBlockWrap.style.position = newBlockSettings.position;\n            notiflixBlockWrap.style.zIndex = newBlockSettings.zindex;\n            notiflixBlockWrap.style.background = newBlockSettings.backgroundColor;\n            notiflixBlockWrap.style.animationDuration = newBlockSettings.cssAnimationDuration + 'ms';\n            notiflixBlockWrap.style.fontFamily = '\"' + newBlockSettings.fontFamily + '\", ' + defaultFontFamily;\n            notiflixBlockWrap.style.display = 'flex';\n            notiflixBlockWrap.style.flexWrap = 'wrap';\n            notiflixBlockWrap.style.flexDirection = 'column';\n            notiflixBlockWrap.style.alignItems = 'center';\n            notiflixBlockWrap.style.justifyContent = 'center';\n            // block element: end\n\n            // block element rtl: begin\n            if (newBlockSettings.rtl) {\n              notiflixBlockWrap.setAttribute('dir', 'rtl');\n              notiflixBlockWrap.classList.add('nx-rtl-on');\n            }\n            // block element rtl: end\n\n            // block element data: begin\n            notiflixBlockWrap.innerHTML = iconElement + messageElement;\n            // block element data: end\n\n            // append block element: begin\n            var getEachElementPosition = window.getComputedStyle(eachElement).getPropertyValue('position');\n            var eachElementPosition = typeof getEachElementPosition === 'string' ? getEachElementPosition.toLocaleLowerCase('en') : 'relative';\n\n            var averageMinHeight = Math.round(parseInt(newBlockSettings.svgSize) * 1.25) + 40;\n            var eachElementHeight = eachElement.offsetHeight || 0;\n            var minHeightStyle = '';\n            if (averageMinHeight > eachElementHeight) {\n              minHeightStyle = 'min-height:' + averageMinHeight + 'px;';\n            }\n\n            // internal style: begin\n            var eachElementIdOrClass = '';\n            if (eachElement.getAttribute('id')) {\n              eachElementIdOrClass = '#' + eachElement.getAttribute('id');\n            } else if (eachElement.classList[0]) {\n              eachElementIdOrClass = '.' + eachElement.classList[0];\n            } else {\n              eachElementIdOrClass = (eachElement.tagName || '').toLocaleLowerCase('en');\n            }\n\n            var positionStyle = '';\n            var positions = ['absolute', 'relative', 'fixed', 'sticky'];\n            var addPosition = positions.indexOf(eachElementPosition) <= -1;\n            if (addPosition || minHeightStyle.length > 0) {\n              // check doc head\n              if (!commonCheckHeadOrBody('head')) { return false; }\n\n              // check position style\n              if (addPosition) {\n                positionStyle = 'position:relative!important;';\n              }\n\n              // create and add internal style to the head\n              var style = '<style id=\"Style-' + blockSettings.ID + '-' + blockCreateOrRemoveCounter + '\">' +\n                eachElementIdOrClass + '.' + positionClassForNonStaticRef + '{' + positionStyle + minHeightStyle + '}' +\n                '</style>';\n              var styleRange = window.document.createRange();\n              styleRange.selectNode(window.document.head);\n              var styleFragment = styleRange.createContextualFragment(style);\n              window.document.head.appendChild(styleFragment);\n\n              // add the \"positionClassForNonStaticRef\" to each element\n              eachElement.classList.add(positionClassForNonStaticRef);\n            }\n            // internal style: end\n\n            // append\n            eachElement.appendChild(notiflixBlockWrap);\n            // append block element: end\n          }\n          // check block element exist: end\n        }\n      }\n      // add element(s) and style: end\n    }\n    // unblock/remove\n    else {\n      // Step 3 => Remove each block element: begin\n      var removeBlockElements = function (eachOne) {\n        var timeout = setTimeout(function () {\n          // remove element\n          if (eachOne.parentNode !== null) {\n            eachOne.parentNode.removeChild(eachOne);\n          }\n\n          // remove element's internal style\n          var eachOneId = eachOne.getAttribute('id');\n          var eachOneStyle = window.document.getElementById('Style-' + eachOneId);\n          if (eachOneStyle && eachOneStyle.parentNode !== null) {\n            eachOneStyle.parentNode.removeChild(eachOneStyle);\n          }\n\n          // clear timeout\n          clearTimeout(timeout);\n        }, newBlockSettings.cssAnimationDuration);\n      };\n      // Step 3 => Remove each block element: end\n\n      // Step 2A => Remove each block element: begin\n      var removeClassBlockElements = function (eachBlockElement) {\n        // if elements exist\n        if (eachBlockElement && eachBlockElement.length > 0) {\n          for (var i = 0; i < eachBlockElement.length; i++) {\n            var eachOne = eachBlockElement[i];\n            if (eachOne) {\n              // add remove class\n              eachOne.classList.add('nx-remove');\n              // remove block elements\n              removeBlockElements(eachOne);\n            }\n          }\n        }\n        // not exist\n        else {\n          if (typeof selectorOrHTMLElements === 'string') {\n            commonConsoleLog('\"Notiflix.Block.remove();\" function called with \"' + selectorOrHTMLElements + '\" selector, but this selector does not have a \"Block\" element to remove.');\n          } else {\n            commonConsoleLog('\"Notiflix.Block.remove();\" function called with \"' + selectorOrHTMLElements + '\", but this \"Array<HTMLElement>\" or \"NodeListOf<HTMLElement>\" does not have a \"Block\" element to remove.');\n          }\n        }\n      };\n      // Step 2A => Remove each block element: end\n\n      // Step 2B => Remove each element's class name: begin\n      var removeEachElementClassName = function (eachElement) {\n        var timeout = setTimeout(function () {\n          // remove class name\n          eachElement.classList.remove(positionClassForNonStaticRef);\n\n          // clear timeout\n          clearTimeout(timeout);\n        }, newBlockSettings.cssAnimationDuration + 300);\n      };\n      // Step 2B => Remove each element's class name: end\n\n      // Step 1 => Remove each element: begin\n      var removeElementTimeout = setTimeout(function () {\n        for (var i = 0; i < checkQueryLimit; i++) {\n          var eachElement = allHTMLElements[i];\n          if (eachElement) {\n            // remove each element's class name\n            removeEachElementClassName(eachElement);\n\n            // remove each block element\n            eachBlockElement = eachElement.querySelectorAll('[id^=' + blockSettings.ID + ']');\n            removeClassBlockElements(eachBlockElement);\n          }\n        }\n        // clear timeout\n        clearTimeout(removeElementTimeout);\n      }, delay);\n      // Step 1 => Remove each element: end\n    }\n\n    // extend new settings with the backup settings\n    newBlockSettings = commonExtendOptions(true, newBlockSettings, newBlockSettingsBackup);\n  };\n  // BLOCK: Create or Remove: end\n\n  var Notiflix = {\n    Block: {\n      // Initialize\n      init: function (userBlockOptions) {\n        // extend options\n        newBlockSettings = commonExtendOptions(true, blockSettings, userBlockOptions);\n        // internal css if exist\n        commonSetInternalCSSCodes(blockGetInternalCSSCodes, 'NotiflixBlockInternalCSS');\n      },\n      // Merge First Initialize\n      merge: function (userBlockExtendOptions) {\n        // if initialized already\n        if (newBlockSettings) {\n          newBlockSettings = commonExtendOptions(true, newBlockSettings, userBlockExtendOptions);\n        }\n        // initialize first\n        else {\n          commonConsoleError('You have to initialize the \"Notiflix.Block\" module before call Merge function.');\n          return false;\n        }\n      },\n      // Standard\n      standard: function (selectorOrHTMLElements, messageOrOptions, options) {\n        blockCreateOrRemove(true, typesBlock.Standard, selectorOrHTMLElements, messageOrOptions, options); // true => show\n      },\n      // Hourglass\n      hourglass: function (selectorOrHTMLElements, messageOrOptions, options) {\n        blockCreateOrRemove(true, typesBlock.Hourglass, selectorOrHTMLElements, messageOrOptions, options); // true => show\n      },\n      // Circle\n      circle: function (selectorOrHTMLElements, messageOrOptions, options) {\n        blockCreateOrRemove(true, typesBlock.Circle, selectorOrHTMLElements, messageOrOptions, options); // true => show\n      },\n      // Arrows\n      arrows: function (selectorOrHTMLElements, messageOrOptions, options) {\n        blockCreateOrRemove(true, typesBlock.Arrows, selectorOrHTMLElements, messageOrOptions, options); // true => show\n      },\n      // Dots\n      dots: function (selectorOrHTMLElements, messageOrOptions, options) {\n        blockCreateOrRemove(true, typesBlock.Dots, selectorOrHTMLElements, messageOrOptions, options); // true => show\n      },\n      // Pulse\n      pulse: function (selectorOrHTMLElements, messageOrOptions, options) {\n        blockCreateOrRemove(true, typesBlock.Pulse, selectorOrHTMLElements, messageOrOptions, options); // true => show\n      },\n      // Remove\n      remove: function (selectorOrHTMLElements, delay) {\n        if (typeof delay !== 'number') { delay = 0; }\n        blockCreateOrRemove(false, null, selectorOrHTMLElements, null, null, delay); // false => hide/remove\n      },\n    },\n  };\n\n  if (typeof window.Notiflix === 'object') {\n    return commonExtendOptions(true, window.Notiflix, { Block: Notiflix.Block });\n  } else {\n    return { Block: Notiflix.Block };\n  }\n\n});\n\n//# sourceURL=webpack://Materialize/./node_modules/notiflix/build/notiflix-block-aio.js?")},"./node_modules/notiflix/build/notiflix-loading-aio.js":function(module,exports,__webpack_require__){eval('var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*\n* Notiflix Loading AIO (https://notiflix.github.io)\n* Description: This file has been created automatically that using "notiflix.js", and "notiflix.css" files.\n* Version: 3.2.8\n* Author: Furkan (https://github.com/furcan)\n* Copyright 2019 - 2025 Notiflix, MIT License (https://opensource.org/licenses/MIT)\n*/\n\n/* global define */\n(function (root, factory) {\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      return factory(root);\n    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n})(typeof __webpack_require__.g !== \'undefined\' ? __webpack_require__.g : typeof window !== \'undefined\' ? window : this, function (window) {\n\n  \'use strict\';\n\n  // COMMON: SSR check: begin\n  if (typeof window === \'undefined\' && typeof window.document === \'undefined\') {\n    return false;\n  }\n  // COMMON: SSR check: end\n\n  // COMMON: Variables: begin\n  var notiflixNamespace = \'Notiflix\';\n  var notiflixConsoleDocs = \'\\n\\nVisit documentation page to learn more: https://notiflix.github.io/documentation\';\n  var defaultFontFamily = \'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif\';\n  // COMMON: Variables: end\n\n  // LOADING: Default Settings: begin\n  var typesLoading = {\n    Standard: \'Standard\',\n    Hourglass: \'Hourglass\',\n    Circle: \'Circle\',\n    Arrows: \'Arrows\',\n    Dots: \'Dots\',\n    Pulse: \'Pulse\',\n    Custom: \'Custom\',\n    Notiflix: \'Notiflix\',\n  };\n  var newLoadingSettings;\n  var loadingSettings = {\n    ID: \'NotiflixLoadingWrap\', // can not customizable\n    className: \'notiflix-loading\',\n    zindex: 4000,\n    backgroundColor: \'rgba(0,0,0,0.8)\',\n    rtl: false,\n    fontFamily: \'Quicksand\',\n    cssAnimation: true,\n    cssAnimationDuration: 400,\n    clickToClose: false,\n    customSvgUrl: null,\n    customSvgCode: null,\n    svgSize: \'80px\',\n    svgColor: \'#32c682\',\n    messageID: \'NotiflixLoadingMessage\',\n    messageFontSize: \'15px\',\n    messageMaxLength: 34,\n    messageColor: \'#dcdcdc\',\n  };\n  // LOADING: Default Settings: end\n\n  // COMMON: Console Error: begin\n  var commonConsoleError = function (message) {\n    return console.error(\'%c \' + notiflixNamespace + \' Error \', \'padding:2px;border-radius:20px;color:#fff;background:#ff5549\', \'\\n\' + message + notiflixConsoleDocs);\n  };\n  // COMMON: Console Error: end\n\n  // COMMON: Check Head or Body: begin\n  var commonCheckHeadOrBody = function (element) {\n    if (!element) { element = \'head\'; }\n    if (window.document[element] === undefined) {\n      commonConsoleError(\'\\nNotiflix needs to be appended to the "<\' + element + \'>" element, but you called it before the "<\' + element + \'>" element has been created.\');\n      return false;\n    }\n    return true;\n  };\n  // COMMON: Check Head or Body: end\n\n  // COMMON: Set Internal CSS Codes: begin\n  var commonSetInternalCSSCodes = function (getInternalCSSCodes, styleElementId) {\n    // check doc head\n    if (!commonCheckHeadOrBody(\'head\')) { return false; }\n\n    // internal css\n    if (getInternalCSSCodes() !== null && !window.document.getElementById(styleElementId)) {\n      var internalCSS = window.document.createElement(\'style\');\n      internalCSS.id = styleElementId;\n      internalCSS.innerHTML = getInternalCSSCodes();\n      window.document.head.appendChild(internalCSS);\n    }\n  };\n  // COMMON: Set Internal CSS Codes: end\n\n  // COMMON: Extend Options: begin\n  var commonExtendOptions = function () {\n    // variables\n    var extended = {};\n    var deep = false;\n    var i = 0;\n    // check if a deep merge\n    if (Object.prototype.toString.call(arguments[0]) === \'[object Boolean]\') {\n      deep = arguments[0];\n      i++;\n    }\n    // merge the object into the extended object\n    var merge = function (obj) {\n      for (var prop in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n          // if property is an object, merge properties\n          if (deep && Object.prototype.toString.call(obj[prop]) === \'[object Object]\') {\n            extended[prop] = commonExtendOptions(extended[prop], obj[prop]);\n          } else {\n            extended[prop] = obj[prop];\n          }\n        }\n      }\n    };\n    // loop through each object and conduct a merge\n    for (; i < arguments.length; i++) {\n      merge(arguments[i]);\n    }\n    return extended;\n  };\n  // COMMON: Extend Options: end\n\n  // COMMON: Get Plaintext: begin\n  var commonGetPlaintext = function (html) {\n    var htmlPool = window.document.createElement(\'div\');\n    htmlPool.innerHTML = html;\n    return htmlPool.textContent || htmlPool.innerText || \'\';\n  };\n  // COMMON: Get Plaintext: end\n\n  // LOADING && BLOCK: SVG Icon Standard: begin\n  var loadingAndBlockSvgIconStandard = function (width, color) {\n    if (!width) { width = \'60px\'; }\n    if (!color) { color = \'#32c682\'; }\n    var standard = \'<svg xmlns="http://www.w3.org/2000/svg" stroke="\' + color + \'" width="\' + width + \'" height="\' + width + \'" transform="scale(.8)" viewBox="0 0 38 38"><g fill="none" fill-rule="evenodd" stroke-width="2" transform="translate(1 1)"><circle cx="18" cy="18" r="18" stroke-opacity=".25"/><path d="M36 18c0-9.94-8.06-18-18-18"><animateTransform attributeName="transform" dur="1s" from="0 18 18" repeatCount="indefinite" to="360 18 18" type="rotate"/></path></g></svg>\';\n    return standard;\n  };\n  // LOADING && BLOCK: SVG Icon Standard: end\n\n  // LOADING && BLOCK: SVG Icon Hourglass: begin\n  var loadingAndBlockSvgIconHourglass = function (width, color) {\n    if (!width) { width = \'60px\'; }\n    if (!color) { color = \'#32c682\'; }\n    var hourglass = \'<svg xmlns="http://www.w3.org/2000/svg" id="NXLoadingHourglass" fill="\' + color + \'" width="\' + width + \'" height="\' + width + \'" viewBox="0 0 200 200"><style>@-webkit-keyframes NXhourglass5-animation{0%{-webkit-transform:scale(1,1);transform:scale(1,1)}16.67%{-webkit-transform:scale(1,.8);transform:scale(1,.8)}33.33%{-webkit-transform:scale(.88,.6);transform:scale(.88,.6)}37.5%{-webkit-transform:scale(.85,.55);transform:scale(.85,.55)}41.67%{-webkit-transform:scale(.8,.5);transform:scale(.8,.5)}45.83%{-webkit-transform:scale(.75,.45);transform:scale(.75,.45)}50%{-webkit-transform:scale(.7,.4);transform:scale(.7,.4)}54.17%{-webkit-transform:scale(.6,.35);transform:scale(.6,.35)}58.33%{-webkit-transform:scale(.5,.3);transform:scale(.5,.3)}83.33%,to{-webkit-transform:scale(.2,0);transform:scale(.2,0)}}@keyframes NXhourglass5-animation{0%{-webkit-transform:scale(1,1);transform:scale(1,1)}16.67%{-webkit-transform:scale(1,.8);transform:scale(1,.8)}33.33%{-webkit-transform:scale(.88,.6);transform:scale(.88,.6)}37.5%{-webkit-transform:scale(.85,.55);transform:scale(.85,.55)}41.67%{-webkit-transform:scale(.8,.5);transform:scale(.8,.5)}45.83%{-webkit-transform:scale(.75,.45);transform:scale(.75,.45)}50%{-webkit-transform:scale(.7,.4);transform:scale(.7,.4)}54.17%{-webkit-transform:scale(.6,.35);transform:scale(.6,.35)}58.33%{-webkit-transform:scale(.5,.3);transform:scale(.5,.3)}83.33%,to{-webkit-transform:scale(.2,0);transform:scale(.2,0)}}@-webkit-keyframes NXhourglass3-animation{0%{-webkit-transform:scale(1,.02);transform:scale(1,.02)}79.17%,to{-webkit-transform:scale(1,1);transform:scale(1,1)}}@keyframes NXhourglass3-animation{0%{-webkit-transform:scale(1,.02);transform:scale(1,.02)}79.17%,to{-webkit-transform:scale(1,1);transform:scale(1,1)}}@-webkit-keyframes NXhourglass1-animation{0%,83.33%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes NXhourglass1-animation{0%,83.33%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}#NXLoadingHourglass *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g data-animator-group="true" data-animator-type="1" style="-webkit-animation-name:NXhourglass1-animation;animation-name:NXhourglass1-animation;-webkit-transform-origin:50% 50%;transform-origin:50% 50%;transform-box:fill-box"><g id="NXhourglass2" fill="inherit"><g data-animator-group="true" data-animator-type="2" style="-webkit-animation-name:NXhourglass3-animation;animation-name:NXhourglass3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform-origin:50% 100%;transform-origin:50% 100%;transform-box:fill-box" opacity=".4"><path id="NXhourglass4" d="M100 100l-34.38 32.08v31.14h68.76v-31.14z"/></g><g data-animator-group="true" data-animator-type="2" style="-webkit-animation-name:NXhourglass5-animation;animation-name:NXhourglass5-animation;-webkit-transform-origin:50% 100%;transform-origin:50% 100%;transform-box:fill-box" opacity=".4"><path id="NXhourglass6" d="M100 100L65.62 67.92V36.78h68.76v31.14z"/></g><path d="M51.14 38.89h8.33v14.93c0 15.1 8.29 28.99 23.34 39.1 1.88 1.25 3.04 3.97 3.04 7.08s-1.16 5.83-3.04 7.09c-15.05 10.1-23.34 23.99-23.34 39.09v14.93h-8.33a4.859 4.859 0 1 0 0 9.72h97.72a4.859 4.859 0 1 0 0-9.72h-8.33v-14.93c0-15.1-8.29-28.99-23.34-39.09-1.88-1.26-3.04-3.98-3.04-7.09s1.16-5.83 3.04-7.08c15.05-10.11 23.34-24 23.34-39.1V38.89h8.33a4.859 4.859 0 1 0 0-9.72H51.14a4.859 4.859 0 1 0 0 9.72zm79.67 14.93c0 15.87-11.93 26.25-19.04 31.03-4.6 3.08-7.34 8.75-7.34 15.15 0 6.41 2.74 12.07 7.34 15.15 7.11 4.78 19.04 15.16 19.04 31.03v14.93H69.19v-14.93c0-15.87 11.93-26.25 19.04-31.02 4.6-3.09 7.34-8.75 7.34-15.16 0-6.4-2.74-12.07-7.34-15.15-7.11-4.78-19.04-15.16-19.04-31.03V38.89h61.62v14.93z"/></g></g></svg>\';\n    return hourglass;\n  };\n  // LOADING && BLOCK: SVG Icon Hourglass: end\n\n  // LOADING && BLOCK: SVG Icon Circle: begin\n  var loadingAndBlockSvgIconCircle = function (width, color) {\n    if (!width) { width = \'60px\'; }\n    if (!color) { color = \'#32c682\'; }\n    var circle = \'<svg xmlns="http://www.w3.org/2000/svg" width="\' + width + \'" height="\' + width + \'" viewBox="25 25 50 50" style="-webkit-animation:rotate 2s linear infinite;animation:rotate 2s linear infinite;height:\' + width + \';-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;width:\' + width + \';position:absolute;top:0;left:0;margin:auto"><style>@-webkit-keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}to{stroke-dasharray:89,200;stroke-dashoffset:-124}}@keyframes dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}to{stroke-dasharray:89,200;stroke-dashoffset:-124}}</style><circle cx="50" cy="50" r="20" fill="none" stroke="\' + color + \'" stroke-width="2" style="-webkit-animation:dash 1.5s ease-in-out infinite,color 1.5s ease-in-out infinite;animation:dash 1.5s ease-in-out infinite,color 1.5s ease-in-out infinite" stroke-dasharray="150 200" stroke-dashoffset="-10" stroke-linecap="round"/></svg>\';\n    return circle;\n  };\n  // LOADING && BLOCK: SVG Icon Circle: end\n\n  // LOADING && BLOCK: SVG Icon Arrows: begin\n  var loadingAndBlockSvgIconArrows = function (width, color) {\n    if (!width) { width = \'60px\'; }\n    if (!color) { color = \'#32c682\'; }\n    var arrows = \'<svg xmlns="http://www.w3.org/2000/svg" fill="\' + color + \'" width="\' + width + \'" height="\' + width + \'" viewBox="0 0 128 128"><g><path fill="inherit" d="M109.25 55.5h-36l12-12a29.54 29.54 0 0 0-49.53 12H18.75A46.04 46.04 0 0 1 96.9 31.84l12.35-12.34v36zm-90.5 17h36l-12 12a29.54 29.54 0 0 0 49.53-12h16.97A46.04 46.04 0 0 1 31.1 96.16L18.74 108.5v-36z"/><animateTransform attributeName="transform" dur="1.5s" from="0 64 64" repeatCount="indefinite" to="360 64 64" type="rotate"/></g></svg>\';\n    return arrows;\n  };\n  // LOADING && BLOCK: SVG Icon Arrows: end\n\n  // LOADING && BLOCK: SVG Icon Dots: begin\n  var loadingAndBlockSvgIconDots = function (width, color) {\n    if (!width) { width = \'60px\'; }\n    if (!color) { color = \'#32c682\'; }\n    var dots = \'<svg xmlns="http://www.w3.org/2000/svg" fill="\' + color + \'" width="\' + width + \'" height="\' + width + \'" viewBox="0 0 100 100"><g transform="translate(25 50)"><circle r="9" fill="inherit" transform="scale(.239)"><animateTransform attributeName="transform" begin="-0.266s" calcMode="spline" dur="0.8s" keySplines="0.3 0 0.7 1;0.3 0 0.7 1" keyTimes="0;0.5;1" repeatCount="indefinite" type="scale" values="0;1;0"/></circle></g><g transform="translate(50 50)"><circle r="9" fill="inherit" transform="scale(.00152)"><animateTransform attributeName="transform" begin="-0.133s" calcMode="spline" dur="0.8s" keySplines="0.3 0 0.7 1;0.3 0 0.7 1" keyTimes="0;0.5;1" repeatCount="indefinite" type="scale" values="0;1;0"/></circle></g><g transform="translate(75 50)"><circle r="9" fill="inherit" transform="scale(.299)"><animateTransform attributeName="transform" begin="0s" calcMode="spline" dur="0.8s" keySplines="0.3 0 0.7 1;0.3 0 0.7 1" keyTimes="0;0.5;1" repeatCount="indefinite" type="scale" values="0;1;0"/></circle></g></svg>\';\n    return dots;\n  };\n  // LOADING && BLOCK: SVG Icon Dots: end\n\n  // LOADING && BLOCK: SVG Icon Pulse: begin\n  var loadingAndBlockSvgIconPulse = function (width, color) {\n    if (!width) { width = \'60px\'; }\n    if (!color) { color = \'#32c682\'; }\n    var pulse = \'<svg xmlns="http://www.w3.org/2000/svg" stroke="\' + color + \'" width="\' + width + \'" height="\' + width + \'" viewBox="0 0 44 44"><g fill="none" fill-rule="evenodd" stroke-width="2"><circle cx="22" cy="22" r="1"><animate attributeName="r" begin="0s" calcMode="spline" dur="1.8s" keySplines="0.165, 0.84, 0.44, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 20"/><animate attributeName="stroke-opacity" begin="0s" calcMode="spline" dur="1.8s" keySplines="0.3, 0.61, 0.355, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 0"/></circle><circle cx="22" cy="22" r="1"><animate attributeName="r" begin="-0.9s" calcMode="spline" dur="1.8s" keySplines="0.165, 0.84, 0.44, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 20"/><animate attributeName="stroke-opacity" begin="-0.9s" calcMode="spline" dur="1.8s" keySplines="0.3, 0.61, 0.355, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 0"/></circle></g></svg>\';\n    return pulse;\n  };\n  // LOADING && BLOCK: SVG Icon Pulse: end\n\n  // LOADING && BLOCK: SVG Icon Notiflix: begin\n  var loadingAndBlockSvgIconNotiflix = function (width, white, green) {\n    if (!width) { width = \'60px\'; }\n    if (!white) { white = \'#f8f8f8\'; }\n    if (!green) { green = \'#32c682\'; }\n    var notiflixIcon = \'<svg xmlns="http://www.w3.org/2000/svg" id="NXLoadingNotiflixLib" width="\' + width + \'" height="\' + width + \'" viewBox="0 0 200 200"><defs><style>@keyframes notiflix-n{0%{stroke-dashoffset:1000}to{stroke-dashoffset:0}}@keyframes notiflix-x{0%{stroke-dashoffset:1000}to{stroke-dashoffset:0}}@keyframes notiflix-dot{0%,to{stroke-width:0}50%{stroke-width:12}}.nx-icon-line{stroke:\' + white + \';stroke-width:12;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22;fill:none}</style></defs><path d="M47.97 135.05a6.5 6.5 0 1 1 0 13 6.5 6.5 0 0 1 0-13z" style="animation-name:notiflix-dot;animation-timing-function:ease-in-out;animation-duration:1.25s;animation-iteration-count:infinite;animation-direction:normal" fill="\' + green + \'" stroke="\' + green + \'" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="22" stroke-width="12"/><path class="nx-icon-line" d="M10.14 144.76V87.55c0-5.68-4.54-41.36 37.83-41.36 42.36 0 37.82 35.68 37.82 41.36v57.21" style="animation-name:notiflix-n;animation-timing-function:linear;animation-duration:2.5s;animation-delay:0s;animation-iteration-count:infinite;animation-direction:normal" stroke-dasharray="500"/><path class="nx-icon-line" d="M115.06 144.49c24.98-32.68 49.96-65.35 74.94-98.03M114.89 46.6c25.09 32.58 50.19 65.17 75.29 97.75" style="animation-name:notiflix-x;animation-timing-function:linear;animation-duration:2.5s;animation-delay:.2s;animation-iteration-count:infinite;animation-direction:normal" stroke-dasharray="500"/></svg>\';\n    return notiflixIcon;\n  };\n  // LOADING && BLOCK: SVG Icon Notiflix: end\n\n  // LOADING: Get Internal CSS Codes: begin\n  var loadingGetInternalCSSCodes = function () {\n    var loadingCSS = \'[id^=NotiflixLoadingWrap]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:fixed;z-index:4000;width:100%;height:100%;left:0;top:0;right:0;bottom:0;margin:auto;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;background:rgba(0,0,0,.8);font-family:"Quicksand",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif}[id^=NotiflixLoadingWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixLoadingWrap].nx-loading-click-to-close{cursor:pointer}[id^=NotiflixLoadingWrap]>div[class*="-icon"]{width:60px;height:60px;position:relative;-webkit-transition:top .2s ease-in-out;-o-transition:top .2s ease-in-out;transition:top .2s ease-in-out;margin:0 auto}[id^=NotiflixLoadingWrap]>div[class*="-icon"] img,[id^=NotiflixLoadingWrap]>div[class*="-icon"] svg{max-width:unset;max-height:unset;width:100%;height:auto;position:absolute;left:0;top:0}[id^=NotiflixLoadingWrap]>p{position:relative;margin:10px auto 0;font-family:inherit!important;font-weight:normal;font-size:15px;line-height:1.4;padding:0 10px;width:100%;text-align:center}[id^=NotiflixLoadingWrap].nx-with-animation{-webkit-animation:loading-animation-fade .3s ease-in-out 0s normal;animation:loading-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes loading-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes loading-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixLoadingWrap].nx-with-animation.nx-remove{opacity:0;-webkit-animation:loading-animation-fade-remove .3s ease-in-out 0s normal;animation:loading-animation-fade-remove .3s ease-in-out 0s normal}@-webkit-keyframes loading-animation-fade-remove{0%{opacity:1}100%{opacity:0}}@keyframes loading-animation-fade-remove{0%{opacity:1}100%{opacity:0}}[id^=NotiflixLoadingWrap]>p.nx-loading-message-new{-webkit-animation:loading-new-message-fade .3s ease-in-out 0s normal;animation:loading-new-message-fade .3s ease-in-out 0s normal}@-webkit-keyframes loading-new-message-fade{0%{opacity:0}100%{opacity:1}}@keyframes loading-new-message-fade{0%{opacity:0}100%{opacity:1}}\';\n    return loadingCSS || null;\n  };\n  // LOADING: Get Internal CSS Codes: end\n\n  // LOADING: Create: begin\n  var loadingCreate = function (loadingType, messageOrOptions, options, display, delay) {\n    // check doc body\n    if (!commonCheckHeadOrBody(\'body\')) { return false; }\n\n    // if not initialized pretend like init\n    if (!newLoadingSettings) {\n      Notiflix.Loading.init({});\n    }\n\n    // create a backup for new settings\n    var newLoadingSettingsBackup = commonExtendOptions(true, newLoadingSettings, {});\n\n    // check "messageOrOptions" and "options": begin\n    if ((typeof messageOrOptions === \'object\' && !Array.isArray(messageOrOptions)) || (typeof options === \'object\' && !Array.isArray(options))) {\n      // new options\n      var newOptions = {};\n      if (typeof messageOrOptions === \'object\') {\n        newOptions = messageOrOptions;\n      } else if (typeof options === \'object\') {\n        newOptions = options;\n      }\n\n      // extend new settings with the new options\n      newLoadingSettings = commonExtendOptions(true, newLoadingSettings, newOptions);\n    }\n    // check "messageOrOptions" and "options": end\n\n    // check the message\n    var message = \'\';\n    if (typeof messageOrOptions === \'string\' && messageOrOptions.length > 0) {\n      message = messageOrOptions;\n    }\n\n    // show loading\n    if (display) {\n\n      // if message settings: begin\n      if (message.length > newLoadingSettings.messageMaxLength) {\n        message = commonGetPlaintext(message).toString().substring(0, newLoadingSettings.messageMaxLength) + \'...\';\n      } else {\n        message = commonGetPlaintext(message).toString();\n      }\n      var messageHTML = \'\';\n      if (message.length > 0) {\n        messageHTML = \'<p id="\' + newLoadingSettings.messageID + \'" class="nx-loading-message" style="color:\' + newLoadingSettings.messageColor + \';font-size:\' + newLoadingSettings.messageFontSize + \';">\' + message + \'</p>\';\n      }\n      // if message settings: end\n\n      // if cssAnimation is false => duration: begin\n      if (!newLoadingSettings.cssAnimation) {\n        newLoadingSettings.cssAnimationDuration = 0;\n      }\n      // if cssAnimation is false => duration: end\n\n      // svgIcon: begin\n      var svgIcon = \'\';\n      if (loadingType === typesLoading.Standard) {\n        svgIcon = loadingAndBlockSvgIconStandard(newLoadingSettings.svgSize, newLoadingSettings.svgColor);\n      } else if (loadingType === typesLoading.Hourglass) {\n        svgIcon = loadingAndBlockSvgIconHourglass(newLoadingSettings.svgSize, newLoadingSettings.svgColor);\n      } else if (loadingType === typesLoading.Circle) {\n        svgIcon = loadingAndBlockSvgIconCircle(newLoadingSettings.svgSize, newLoadingSettings.svgColor);\n      } else if (loadingType === typesLoading.Arrows) {\n        svgIcon = loadingAndBlockSvgIconArrows(newLoadingSettings.svgSize, newLoadingSettings.svgColor);\n      } else if (loadingType === typesLoading.Dots) {\n        svgIcon = loadingAndBlockSvgIconDots(newLoadingSettings.svgSize, newLoadingSettings.svgColor);\n      } else if (loadingType === typesLoading.Pulse) {\n        svgIcon = loadingAndBlockSvgIconPulse(newLoadingSettings.svgSize, newLoadingSettings.svgColor);\n      } else if (\n        loadingType === typesLoading.Custom &&\n        newLoadingSettings.customSvgCode !== null &&\n        newLoadingSettings.customSvgUrl === null\n      ) {\n        svgIcon = newLoadingSettings.customSvgCode || \'\';\n      } else if (\n        loadingType === typesLoading.Custom &&\n        newLoadingSettings.customSvgUrl !== null &&\n        newLoadingSettings.customSvgCode === null\n      ) {\n        svgIcon = \'<img class="nx-custom-loading-icon" width="\' + newLoadingSettings.svgSize + \'" height="\' + newLoadingSettings.svgSize + \'" src="\' + newLoadingSettings.customSvgUrl + \'" alt="Notiflix">\';\n      } else if (\n        loadingType === typesLoading.Custom &&\n        (newLoadingSettings.customSvgUrl === null || newLoadingSettings.customSvgCode === null)\n      ) {\n        commonConsoleError(\'You have to set a static SVG url to "customSvgUrl" option to use Loading Custom.\');\n        return false;\n      } else {\n        svgIcon = loadingAndBlockSvgIconNotiflix(newLoadingSettings.svgSize, \'#f8f8f8\', \'#32c682\');\n      }\n      var svgSizeAsDigit = parseInt((newLoadingSettings.svgSize || \'\').replace(/[^0-9]/g, \'\'));\n      var winWidth = window.innerWidth;\n      var maxSvgWidthPx = svgSizeAsDigit >= winWidth ? (winWidth - 40) + \'px\' : svgSizeAsDigit + \'px\';\n      var svgIconHTML = \'<div style="width:\' + maxSvgWidthPx + \'; height:\' + maxSvgWidthPx + \';" class="\' + newLoadingSettings.className + \'-icon\' + (message.length > 0 ? \' nx-with-message\' : \'\') + \'">\' + svgIcon + \'</div>\';\n      // svgIcon: end\n\n      // loading wrap: begin\n      var ntflxLoadingWrap = window.document.createElement(\'div\');\n      ntflxLoadingWrap.id = loadingSettings.ID;\n      ntflxLoadingWrap.className = newLoadingSettings.className + (newLoadingSettings.cssAnimation ? \' nx-with-animation\' : \'\') + (newLoadingSettings.clickToClose ? \' nx-loading-click-to-close\' : \'\');\n      ntflxLoadingWrap.style.zIndex = newLoadingSettings.zindex;\n      ntflxLoadingWrap.style.background = newLoadingSettings.backgroundColor;\n      ntflxLoadingWrap.style.animationDuration = newLoadingSettings.cssAnimationDuration + \'ms\';\n      ntflxLoadingWrap.style.fontFamily = \'"\' + newLoadingSettings.fontFamily + \'", \' + defaultFontFamily;\n      ntflxLoadingWrap.style.display = \'flex\';\n      ntflxLoadingWrap.style.flexWrap = \'wrap\';\n      ntflxLoadingWrap.style.flexDirection = \'column\';\n      ntflxLoadingWrap.style.alignItems = \'center\';\n      ntflxLoadingWrap.style.justifyContent = \'center\';\n\n      // rtl: begin\n      if (newLoadingSettings.rtl) {\n        ntflxLoadingWrap.setAttribute(\'dir\', \'rtl\');\n        ntflxLoadingWrap.classList.add(\'nx-rtl-on\');\n      }\n      // rtl: end\n\n      // append: begin\n      ntflxLoadingWrap.innerHTML = svgIconHTML + messageHTML;\n\n      // if there is no loading element\n      if (!window.document.getElementById(ntflxLoadingWrap.id)) {\n        // append\n        window.document.body.appendChild(ntflxLoadingWrap);\n\n        // if click to close\n        if (newLoadingSettings.clickToClose) {\n          var loadingWrapElm = window.document.getElementById(ntflxLoadingWrap.id);\n          loadingWrapElm.addEventListener(\'click\', function () {\n            ntflxLoadingWrap.classList.add(\'nx-remove\');\n            var timeout = setTimeout(function () {\n              if (ntflxLoadingWrap.parentNode !== null) {\n                ntflxLoadingWrap.parentNode.removeChild(ntflxLoadingWrap);\n                clearTimeout(timeout);\n              }\n            }, newLoadingSettings.cssAnimationDuration);\n          });\n        }\n      }\n      // append: end\n    }\n    // remove loading\n    else {\n      // if there is a loading element\n      if (window.document.getElementById(loadingSettings.ID)) {\n        var loadingElm = window.document.getElementById(loadingSettings.ID);\n        var timeout = setTimeout(function () {\n          loadingElm.classList.add(\'nx-remove\');\n          var timeout2 = setTimeout(function () {\n            if (loadingElm.parentNode !== null) {\n              loadingElm.parentNode.removeChild(loadingElm);\n              clearTimeout(timeout2);\n            }\n          }, newLoadingSettings.cssAnimationDuration);\n          clearTimeout(timeout);\n        }, delay);\n      }\n    }\n\n    // extend new settings with the backup settings\n    newLoadingSettings = commonExtendOptions(true, newLoadingSettings, newLoadingSettingsBackup);\n  };\n  // LOADING: Create: end\n\n  // LOADING: Change Message: begin\n  var loadingChangeMessage = function (newMessage) {\n    // check the new message\n    if (typeof newMessage !== \'string\') {\n      newMessage = \'\';\n    }\n    // if has any loading\n    var messageWrap = window.document.getElementById(loadingSettings.ID);\n    if (messageWrap) {\n      // if there is a new message\n      if (newMessage.length > 0) {\n        // max length: begin\n        if (newMessage.length > newLoadingSettings.messageMaxLength) {\n          newMessage = commonGetPlaintext(newMessage).substring(0, newLoadingSettings.messageMaxLength) + \'...\';\n        } else {\n          newMessage = commonGetPlaintext(newMessage);\n        }\n        // max length: end\n\n        // there is a message element\n        var oldMessageElm = messageWrap.getElementsByTagName(\'p\')[0];\n        if (oldMessageElm) {\n          oldMessageElm.innerHTML = newMessage; // change the message\n        }\n        // there is no message element\n        else {\n          // create a new message element: begin\n          var newMessageHTML = window.document.createElement(\'p\');\n          newMessageHTML.id = newLoadingSettings.messageID;\n          newMessageHTML.className = \'nx-loading-message nx-loading-message-new\';\n          newMessageHTML.style.color = newLoadingSettings.messageColor;\n          newMessageHTML.style.fontSize = newLoadingSettings.messageFontSize;\n          newMessageHTML.innerHTML = newMessage;\n          messageWrap.appendChild(newMessageHTML);\n          // create a new message element: end\n        }\n      }\n      // if no message\n      else {\n        commonConsoleError(\'Where is the new message?\');\n      }\n    }\n  };\n  // LOADING: Change Message: end\n\n  var Notiflix = {\n    Loading: {\n      // Init\n      init: function (userLoadingOptions) {\n        // extend options\n        newLoadingSettings = commonExtendOptions(true, loadingSettings, userLoadingOptions);\n        // internal css if exist\n        commonSetInternalCSSCodes(loadingGetInternalCSSCodes, \'NotiflixLoadingInternalCSS\');\n      },\n      // Merge First Init\n      merge: function (userLoadingExtendOptions) {\n        // if initialized already\n        if (newLoadingSettings) {\n          newLoadingSettings = commonExtendOptions(true, newLoadingSettings, userLoadingExtendOptions);\n        }\n        // initialize first\n        else {\n          commonConsoleError(\'You have to initialize the Loading module before call Merge function.\');\n          return false;\n        }\n      },\n      // Standard\n      standard: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Standard, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Hourglass\n      hourglass: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Hourglass, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Circle\n      circle: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Circle, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Arrows\n      arrows: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Arrows, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Dots\n      dots: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Dots, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Pulse\n      pulse: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Pulse, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Custom\n      custom: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Custom, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Notiflix\n      notiflix: function (messageOrOptions, options) {\n        loadingCreate(typesLoading.Notiflix, messageOrOptions, options, true, 0); // true => show && 0 => delay\n      },\n      // Remove\n      remove: function (delay) {\n        if (typeof delay !== \'number\') { delay = 0; }\n        loadingCreate(null, null, null, false, delay); // false => hide/remove\n      },\n      // Change The Message\n      change: function (newMessage) {\n        loadingChangeMessage(newMessage);\n      },\n    },\n  };\n\n  if (typeof window.Notiflix === \'object\') {\n    return commonExtendOptions(true, window.Notiflix, { Loading: Notiflix.Loading });\n  } else {\n    return { Loading: Notiflix.Loading };\n  }\n\n});\n\n//# sourceURL=webpack://Materialize/./node_modules/notiflix/build/notiflix-loading-aio.js?')}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(t.exports,t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(n,{a:n}),n},__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/notiflix/notiflix.js");return __webpack_exports__}()}));