{% extends 'base.html' %}
{% load static %}

{% block title %}{{ titre }}{% endblock %}

{% block breadcrumb %}
<div class="row">
  <div class="col-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Accueil</a>
        </li>
        <li class="breadcrumb-item">
          <a href="{% url 'organisations:liste' %}">Organisations</a>
        </li>
        {% if organisation %}
          <li class="breadcrumb-item">
            <a href="{% url 'organisations:detail' organisation.pk %}">{{ organisation.nom }}</a>
          </li>
          <li class="breadcrumb-item active">Modifier</li>
        {% else %}
          <li class="breadcrumb-item active">Créer</li>
        {% endif %}
      </ol>
    </nav>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <!-- En-tête de page -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h4 class="fw-bold py-3 mb-2">
          {% if organisation %}
            <i class="ti ti-pencil me-2"></i>{{ titre }}
          {% else %}
            <i class="ti ti-plus me-2"></i>{{ titre }}
          {% endif %}
        </h4>
        <p class="text-muted">
          {% if organisation %}
            Modifiez les informations de l'organisation
          {% else %}
            Créez une nouvelle organisation hospitalière
          {% endif %}
        </p>
      </div>
      <div>
        <a href="{% url 'organisations:liste' %}" class="btn btn-outline-secondary">
          <i class="ti ti-arrow-left me-1"></i>Retour à la liste
        </a>
      </div>
    </div>

    <div class="row">
      <div class="col-xl-8 col-lg-10 mx-auto">
        <!-- Formulaire principal -->
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="ti ti-info-circle me-2"></i>Informations de l'organisation
            </h5>
          </div>
          <div class="card-body">
            <form method="post" novalidate>
              {% csrf_token %}
              
              <!-- Affichage des erreurs générales -->
              {% if form.non_field_errors %}
                <div class="alert alert-danger" role="alert">
                  <h6 class="alert-heading">
                    <i class="ti ti-alert-circle me-1"></i>Erreurs de validation
                  </h6>
                  {% for error in form.non_field_errors %}
                    <div>{{ error }}</div>
                  {% endfor %}
                </div>
              {% endif %}

              <div class="row">
                <!-- Nom de l'organisation -->
                <div class="col-12 mb-3">
                  <label for="{{ form.nom.id_for_label }}" class="form-label">
                    {{ form.nom.label }} <span class="text-danger">*</span>
                  </label>
                  {{ form.nom }}
                  {% if form.nom.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.nom.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                  {% if form.nom.help_text %}
                    <div class="form-text">{{ form.nom.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Adresse -->
                <div class="col-12 mb-3">
                  <label for="{{ form.adresse.id_for_label }}" class="form-label">
                    {{ form.adresse.label }} <span class="text-danger">*</span>
                  </label>
                  {{ form.adresse }}
                  {% if form.adresse.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.adresse.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                  {% if form.adresse.help_text %}
                    <div class="form-text">{{ form.adresse.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Téléphone et Email -->
                <div class="col-md-6 mb-3">
                  <label for="{{ form.telephone.id_for_label }}" class="form-label">
                    {{ form.telephone.label }} <span class="text-danger">*</span>
                  </label>
                  {{ form.telephone }}
                  {% if form.telephone.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.telephone.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                  {% if form.telephone.help_text %}
                    <div class="form-text">{{ form.telephone.help_text }}</div>
                  {% endif %}
                </div>

                <div class="col-md-6 mb-3">
                  <label for="{{ form.email.id_for_label }}" class="form-label">
                    {{ form.email.label }} <span class="text-danger">*</span>
                  </label>
                  {{ form.email }}
                  {% if form.email.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.email.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                  {% if form.email.help_text %}
                    <div class="form-text">{{ form.email.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Type d'abonnement -->
                <div class="col-md-6 mb-3">
                  <label for="{{ form.type_abonnement.id_for_label }}" class="form-label">
                    {{ form.type_abonnement.label }} <span class="text-danger">*</span>
                  </label>
                  {{ form.type_abonnement }}
                  {% if form.type_abonnement.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.type_abonnement.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                  {% if form.type_abonnement.help_text %}
                    <div class="form-text">{{ form.type_abonnement.help_text }}</div>
                  {% endif %}
                </div>
              </div>

              <!-- Informations sur les abonnements -->
              <div class="row mt-4">
                <div class="col-12">
                  <div class="alert alert-info" role="alert">
                    <h6 class="alert-heading">
                      <i class="ti ti-info-circle me-1"></i>Types d'abonnement
                    </h6>
                    <div class="row">
                      <div class="col-md-6">
                        <strong>Gratuit :</strong>
                        <ul class="mb-0 mt-1">
                          <li>Jusqu'à 5 utilisateurs</li>
                          <li>Fonctionnalités de base</li>
                          <li>Support par email</li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <strong>Premium :</strong>
                        <ul class="mb-0 mt-1">
                          <li>Utilisateurs illimités</li>
                          <li>Toutes les fonctionnalités</li>
                          <li>Support prioritaire</li>
                          <li>Rapports avancés</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="row mt-4">
                <div class="col-12">
                  <div class="d-flex justify-content-between">
                    <a href="{% url 'organisations:liste' %}" class="btn btn-outline-secondary">
                      <i class="ti ti-x me-1"></i>Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                      {% if organisation %}
                        <i class="ti ti-device-floppy me-1"></i>Enregistrer les modifications
                      {% else %}
                        <i class="ti ti-plus me-1"></i>Créer l'organisation
                      {% endif %}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation côté client pour améliorer l'expérience utilisateur
    const form = document.querySelector('form');
    const nomField = document.getElementById('{{ form.nom.id_for_label }}');
    const emailField = document.getElementById('{{ form.email.id_for_label }}');
    const telephoneField = document.getElementById('{{ form.telephone.id_for_label }}');

    // Validation du nom en temps réel
    if (nomField) {
        nomField.addEventListener('blur', function() {
            if (this.value.trim().length < 3) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }

    // Validation de l'email en temps réel
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(this.value)) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }

    // Validation du téléphone en temps réel
    if (telephoneField) {
        telephoneField.addEventListener('blur', function() {
            const phoneRegex = /^(\+33|0)[1-9](\d{8})$/;
            if (!phoneRegex.test(this.value.replace(/\s/g, ''))) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }
});
</script>
{% endblock %}
