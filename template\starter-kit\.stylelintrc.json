{"extends": ["stylelint-config-standard-scss", "stylelint-config-idiomatic-order", "@stylistic/stylelint-config"], "plugins": ["stylelint-use-logical-spec", "@stylistic/stylelint-plugin"], "rules": {"alpha-value-notation": null, "at-rule-empty-line-before": null, "block-no-empty": null, "color-function-notation": null, "color-named": "never", "custom-property-empty-line-before": null, "custom-property-pattern": null, "declaration-block-no-redundant-longhand-properties": null, "declaration-empty-line-before": null, "declaration-no-important": null, "font-family-no-missing-generic-family-keyword": [true, {"ignoreFontFamilies": ["boxicons", "tabler-icons", "remix-icons"]}], "font-weight-notation": ["numeric", {"ignore": ["relative"]}], "function-url-no-scheme-relative": true, "liberty/use-logical-spec": true, "media-feature-range-notation": null, "media-query-no-invalid": null, "no-descending-specificity": null, "no-invalid-double-slash-comments": true, "no-invalid-position-at-import-rule": null, "number-max-precision": null, "rule-empty-line-before": null, "selector-class-pattern": null, "selector-id-pattern": null, "selector-max-attribute": 2, "selector-max-id": 1, "selector-max-specificity": null, "selector-not-notation": null, "scss/at-extend-no-missing-placeholder": null, "scss/at-function-named-arguments": "never", "scss/at-if-closing-brace-newline-after": null, "scss/at-if-closing-brace-space-after": null, "scss/at-if-no-null": null, "scss/at-mixin-pattern": null, "scss/at-mixin-argumentless-call-parentheses": "always", "scss/at-rule-conditional-no-parentheses": null, "scss/comment-no-empty": null, "scss/dimension-no-non-numeric-values": true, "scss/dollar-variable-empty-line-before": null, "scss/dollar-variable-pattern": null, "scss/double-slash-comment-empty-line-before": null, "scss/double-slash-comment-whitespace-inside": null, "scss/function-quote-no-quoted-strings-inside": null, "scss/media-feature-value-dollar-variable": null, "scss/no-global-function-names": null, "@stylistic/at-rule-name-space-after": "always", "@stylistic/at-rule-semicolon-space-before": "never", "@stylistic/block-closing-brace-empty-line-before": null, "@stylistic/block-closing-brace-newline-after": ["always", {"ignoreAtRules": ["if", "else"]}], "@stylistic/block-opening-brace-space-before": null, "@stylistic/declaration-block-semicolon-newline-before": "never-multi-line", "@stylistic/indentation": 2, "@stylistic/max-empty-lines": 2, "@stylistic/max-line-length": [220, {"ignore": "comments"}], "@stylistic/no-eol-whitespace": true, "@stylistic/number-leading-zero": "never", "@stylistic/selector-list-comma-newline-before": "never-multi-line", "@stylistic/selector-list-comma-space-after": "always-single-line", "@stylistic/selector-list-comma-space-before": "never-single-line", "@stylistic/unicode-bom": "never"}, "overrides": [{"files": ["**/_bootstrap-extended/**/*.scss"], "rules": {"declaration-property-value-disallowed-list": {"border": "none", "outline": "none"}, "function-disallowed-list": ["lighten", "darken"], "property-disallowed-list": ["border-radius", "border-top-left-radius", "border-top-right-radius", "border-bottom-right-radius", "border-bottom-left-radius", "transition"], "scss/dollar-variable-default": [true, {"ignore": "local"}], "scss/selector-no-union-class-name": true}}]}