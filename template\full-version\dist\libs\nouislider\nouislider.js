!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var a in t)("object"==typeof exports?exports:e)[a]=t[a]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./libs/nouislider/nouislider.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noUiSlider: function() { return /* reexport safe */ nouislider__WEBPACK_IMPORTED_MODULE_0__["default"]; }\n/* harmony export */ });\n/* harmony import */ var nouislider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nouislider */ "./node_modules/nouislider/dist/nouislider.mjs");\n\ntry {\n  window.noUiSlider = nouislider__WEBPACK_IMPORTED_MODULE_0__["default"];\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/nouislider/nouislider.js?')},"./node_modules/nouislider/dist/nouislider.mjs":function(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PipsMode: function() { return /* binding */ PipsMode; },\n/* harmony export */   PipsType: function() { return /* binding */ PipsType; },\n/* harmony export */   create: function() { return /* binding */ initialize; },\n/* harmony export */   cssClasses: function() { return /* binding */ cssClasses; }\n/* harmony export */ });\n\nvar PipsMode;\n(function (PipsMode) {\n    PipsMode["Range"] = "range";\n    PipsMode["Steps"] = "steps";\n    PipsMode["Positions"] = "positions";\n    PipsMode["Count"] = "count";\n    PipsMode["Values"] = "values";\n})(PipsMode || (PipsMode = {}));\nvar PipsType;\n(function (PipsType) {\n    PipsType[PipsType["None"] = -1] = "None";\n    PipsType[PipsType["NoValue"] = 0] = "NoValue";\n    PipsType[PipsType["LargeValue"] = 1] = "LargeValue";\n    PipsType[PipsType["SmallValue"] = 2] = "SmallValue";\n})(PipsType || (PipsType = {}));\n//region Helper Methods\nfunction isValidFormatter(entry) {\n    return isValidPartialFormatter(entry) && typeof entry.from === "function";\n}\nfunction isValidPartialFormatter(entry) {\n    // partial formatters only need a to function and not a from function\n    return typeof entry === "object" && typeof entry.to === "function";\n}\nfunction removeElement(el) {\n    el.parentElement.removeChild(el);\n}\nfunction isSet(value) {\n    return value !== null && value !== undefined;\n}\n// Bindable version\nfunction preventDefault(e) {\n    e.preventDefault();\n}\n// Removes duplicates from an array.\nfunction unique(array) {\n    return array.filter(function (a) {\n        return !this[a] ? (this[a] = true) : false;\n    }, {});\n}\n// Round a value to the closest \'to\'.\nfunction closest(value, to) {\n    return Math.round(value / to) * to;\n}\n// Current position of an element relative to the document.\nfunction offset(elem, orientation) {\n    var rect = elem.getBoundingClientRect();\n    var doc = elem.ownerDocument;\n    var docElem = doc.documentElement;\n    var pageOffset = getPageOffset(doc);\n    // getBoundingClientRect contains left scroll in Chrome on Android.\n    // I haven\'t found a feature detection that proves this. Worst case\n    // scenario on mis-match: the \'tap\' feature on horizontal sliders breaks.\n    if (/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)) {\n        pageOffset.x = 0;\n    }\n    return orientation ? rect.top + pageOffset.y - docElem.clientTop : rect.left + pageOffset.x - docElem.clientLeft;\n}\n// Checks whether a value is numerical.\nfunction isNumeric(a) {\n    return typeof a === "number" && !isNaN(a) && isFinite(a);\n}\n// Sets a class and removes it after [duration] ms.\nfunction addClassFor(element, className, duration) {\n    if (duration > 0) {\n        addClass(element, className);\n        setTimeout(function () {\n            removeClass(element, className);\n        }, duration);\n    }\n}\n// Limits a value to 0 - 100\nfunction limit(a) {\n    return Math.max(Math.min(a, 100), 0);\n}\n// Wraps a variable as an array, if it isn\'t one yet.\n// Note that an input array is returned by reference!\nfunction asArray(a) {\n    return Array.isArray(a) ? a : [a];\n}\n// Counts decimals\nfunction countDecimals(numStr) {\n    numStr = String(numStr);\n    var pieces = numStr.split(".");\n    return pieces.length > 1 ? pieces[1].length : 0;\n}\n// http://youmightnotneedjquery.com/#add_class\nfunction addClass(el, className) {\n    if (el.classList && !/\\s/.test(className)) {\n        el.classList.add(className);\n    }\n    else {\n        el.className += " " + className;\n    }\n}\n// http://youmightnotneedjquery.com/#remove_class\nfunction removeClass(el, className) {\n    if (el.classList && !/\\s/.test(className)) {\n        el.classList.remove(className);\n    }\n    else {\n        el.className = el.className.replace(new RegExp("(^|\\\\b)" + className.split(" ").join("|") + "(\\\\b|$)", "gi"), " ");\n    }\n}\n// https://plainjs.com/javascript/attributes/adding-removing-and-testing-for-classes-9/\nfunction hasClass(el, className) {\n    return el.classList ? el.classList.contains(className) : new RegExp("\\\\b" + className + "\\\\b").test(el.className);\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/Window/scrollY#Notes\nfunction getPageOffset(doc) {\n    var supportPageOffset = window.pageXOffset !== undefined;\n    var isCSS1Compat = (doc.compatMode || "") === "CSS1Compat";\n    var x = supportPageOffset\n        ? window.pageXOffset\n        : isCSS1Compat\n            ? doc.documentElement.scrollLeft\n            : doc.body.scrollLeft;\n    var y = supportPageOffset\n        ? window.pageYOffset\n        : isCSS1Compat\n            ? doc.documentElement.scrollTop\n            : doc.body.scrollTop;\n    return {\n        x: x,\n        y: y,\n    };\n}\n// we provide a function to compute constants instead\n// of accessing window.* as soon as the module needs it\n// so that we do not compute anything if not needed\nfunction getActions() {\n    // Determine the events to bind. IE11 implements pointerEvents without\n    // a prefix, which breaks compatibility with the IE10 implementation.\n    return window.navigator.pointerEnabled\n        ? {\n            start: "pointerdown",\n            move: "pointermove",\n            end: "pointerup",\n        }\n        : window.navigator.msPointerEnabled\n            ? {\n                start: "MSPointerDown",\n                move: "MSPointerMove",\n                end: "MSPointerUp",\n            }\n            : {\n                start: "mousedown touchstart",\n                move: "mousemove touchmove",\n                end: "mouseup touchend",\n            };\n}\n// https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n// Issue #785\nfunction getSupportsPassive() {\n    var supportsPassive = false;\n    /* eslint-disable */\n    try {\n        var opts = Object.defineProperty({}, "passive", {\n            get: function () {\n                supportsPassive = true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener("test", null, opts);\n    }\n    catch (e) { }\n    /* eslint-enable */\n    return supportsPassive;\n}\nfunction getSupportsTouchActionNone() {\n    return window.CSS && CSS.supports && CSS.supports("touch-action", "none");\n}\n//endregion\n//region Range Calculation\n// Determine the size of a sub-range in relation to a full range.\nfunction subRangeRatio(pa, pb) {\n    return 100 / (pb - pa);\n}\n// (percentage) How many percent is this value of this range?\nfunction fromPercentage(range, value, startRange) {\n    return (value * 100) / (range[startRange + 1] - range[startRange]);\n}\n// (percentage) Where is this value on this range?\nfunction toPercentage(range, value) {\n    return fromPercentage(range, range[0] < 0 ? value + Math.abs(range[0]) : value - range[0], 0);\n}\n// (value) How much is this percentage on this range?\nfunction isPercentage(range, value) {\n    return (value * (range[1] - range[0])) / 100 + range[0];\n}\nfunction getJ(value, arr) {\n    var j = 1;\n    while (value >= arr[j]) {\n        j += 1;\n    }\n    return j;\n}\n// (percentage) Input a value, find where, on a scale of 0-100, it applies.\nfunction toStepping(xVal, xPct, value) {\n    if (value >= xVal.slice(-1)[0]) {\n        return 100;\n    }\n    var j = getJ(value, xVal);\n    var va = xVal[j - 1];\n    var vb = xVal[j];\n    var pa = xPct[j - 1];\n    var pb = xPct[j];\n    return pa + toPercentage([va, vb], value) / subRangeRatio(pa, pb);\n}\n// (value) Input a percentage, find where it is on the specified range.\nfunction fromStepping(xVal, xPct, value) {\n    // There is no range group that fits 100\n    if (value >= 100) {\n        return xVal.slice(-1)[0];\n    }\n    var j = getJ(value, xPct);\n    var va = xVal[j - 1];\n    var vb = xVal[j];\n    var pa = xPct[j - 1];\n    var pb = xPct[j];\n    return isPercentage([va, vb], (value - pa) * subRangeRatio(pa, pb));\n}\n// (percentage) Get the step that applies at a certain value.\nfunction getStep(xPct, xSteps, snap, value) {\n    if (value === 100) {\n        return value;\n    }\n    var j = getJ(value, xPct);\n    var a = xPct[j - 1];\n    var b = xPct[j];\n    // If \'snap\' is set, steps are used as fixed points on the slider.\n    if (snap) {\n        // Find the closest position, a or b.\n        if (value - a > (b - a) / 2) {\n            return b;\n        }\n        return a;\n    }\n    if (!xSteps[j - 1]) {\n        return value;\n    }\n    return xPct[j - 1] + closest(value - xPct[j - 1], xSteps[j - 1]);\n}\n//endregion\n//region Spectrum\nvar Spectrum = /** @class */ (function () {\n    function Spectrum(entry, snap, singleStep) {\n        this.xPct = [];\n        this.xVal = [];\n        this.xSteps = [];\n        this.xNumSteps = [];\n        this.xHighestCompleteStep = [];\n        this.xSteps = [singleStep || false];\n        this.xNumSteps = [false];\n        this.snap = snap;\n        var index;\n        var ordered = [];\n        // Map the object keys to an array.\n        Object.keys(entry).forEach(function (index) {\n            ordered.push([asArray(entry[index]), index]);\n        });\n        // Sort all entries by value (numeric sort).\n        ordered.sort(function (a, b) {\n            return a[0][0] - b[0][0];\n        });\n        // Convert all entries to subranges.\n        for (index = 0; index < ordered.length; index++) {\n            this.handleEntryPoint(ordered[index][1], ordered[index][0]);\n        }\n        // Store the actual step values.\n        // xSteps is sorted in the same order as xPct and xVal.\n        this.xNumSteps = this.xSteps.slice(0);\n        // Convert all numeric steps to the percentage of the subrange they represent.\n        for (index = 0; index < this.xNumSteps.length; index++) {\n            this.handleStepPoint(index, this.xNumSteps[index]);\n        }\n    }\n    Spectrum.prototype.getDistance = function (value) {\n        var distances = [];\n        for (var index = 0; index < this.xNumSteps.length - 1; index++) {\n            distances[index] = fromPercentage(this.xVal, value, index);\n        }\n        return distances;\n    };\n    // Calculate the percentual distance over the whole scale of ranges.\n    // direction: 0 = backwards / 1 = forwards\n    Spectrum.prototype.getAbsoluteDistance = function (value, distances, direction) {\n        var xPct_index = 0;\n        // Calculate range where to start calculation\n        if (value < this.xPct[this.xPct.length - 1]) {\n            while (value > this.xPct[xPct_index + 1]) {\n                xPct_index++;\n            }\n        }\n        else if (value === this.xPct[this.xPct.length - 1]) {\n            xPct_index = this.xPct.length - 2;\n        }\n        // If looking backwards and the value is exactly at a range separator then look one range further\n        if (!direction && value === this.xPct[xPct_index + 1]) {\n            xPct_index++;\n        }\n        if (distances === null) {\n            distances = [];\n        }\n        var start_factor;\n        var rest_factor = 1;\n        var rest_rel_distance = distances[xPct_index];\n        var range_pct = 0;\n        var rel_range_distance = 0;\n        var abs_distance_counter = 0;\n        var range_counter = 0;\n        // Calculate what part of the start range the value is\n        if (direction) {\n            start_factor = (value - this.xPct[xPct_index]) / (this.xPct[xPct_index + 1] - this.xPct[xPct_index]);\n        }\n        else {\n            start_factor = (this.xPct[xPct_index + 1] - value) / (this.xPct[xPct_index + 1] - this.xPct[xPct_index]);\n        }\n        // Do until the complete distance across ranges is calculated\n        while (rest_rel_distance > 0) {\n            // Calculate the percentage of total range\n            range_pct = this.xPct[xPct_index + 1 + range_counter] - this.xPct[xPct_index + range_counter];\n            // Detect if the margin, padding or limit is larger then the current range and calculate\n            if (distances[xPct_index + range_counter] * rest_factor + 100 - start_factor * 100 > 100) {\n                // If larger then take the percentual distance of the whole range\n                rel_range_distance = range_pct * start_factor;\n                // Rest factor of relative percentual distance still to be calculated\n                rest_factor = (rest_rel_distance - 100 * start_factor) / distances[xPct_index + range_counter];\n                // Set start factor to 1 as for next range it does not apply.\n                start_factor = 1;\n            }\n            else {\n                // If smaller or equal then take the percentual distance of the calculate percentual part of that range\n                rel_range_distance = ((distances[xPct_index + range_counter] * range_pct) / 100) * rest_factor;\n                // No rest left as the rest fits in current range\n                rest_factor = 0;\n            }\n            if (direction) {\n                abs_distance_counter = abs_distance_counter - rel_range_distance;\n                // Limit range to first range when distance becomes outside of minimum range\n                if (this.xPct.length + range_counter >= 1) {\n                    range_counter--;\n                }\n            }\n            else {\n                abs_distance_counter = abs_distance_counter + rel_range_distance;\n                // Limit range to last range when distance becomes outside of maximum range\n                if (this.xPct.length - range_counter >= 1) {\n                    range_counter++;\n                }\n            }\n            // Rest of relative percentual distance still to be calculated\n            rest_rel_distance = distances[xPct_index + range_counter] * rest_factor;\n        }\n        return value + abs_distance_counter;\n    };\n    Spectrum.prototype.toStepping = function (value) {\n        value = toStepping(this.xVal, this.xPct, value);\n        return value;\n    };\n    Spectrum.prototype.fromStepping = function (value) {\n        return fromStepping(this.xVal, this.xPct, value);\n    };\n    Spectrum.prototype.getStep = function (value) {\n        value = getStep(this.xPct, this.xSteps, this.snap, value);\n        return value;\n    };\n    Spectrum.prototype.getDefaultStep = function (value, isDown, size) {\n        var j = getJ(value, this.xPct);\n        // When at the top or stepping down, look at the previous sub-range\n        if (value === 100 || (isDown && value === this.xPct[j - 1])) {\n            j = Math.max(j - 1, 1);\n        }\n        return (this.xVal[j] - this.xVal[j - 1]) / size;\n    };\n    Spectrum.prototype.getNearbySteps = function (value) {\n        var j = getJ(value, this.xPct);\n        return {\n            stepBefore: {\n                startValue: this.xVal[j - 2],\n                step: this.xNumSteps[j - 2],\n                highestStep: this.xHighestCompleteStep[j - 2],\n            },\n            thisStep: {\n                startValue: this.xVal[j - 1],\n                step: this.xNumSteps[j - 1],\n                highestStep: this.xHighestCompleteStep[j - 1],\n            },\n            stepAfter: {\n                startValue: this.xVal[j],\n                step: this.xNumSteps[j],\n                highestStep: this.xHighestCompleteStep[j],\n            },\n        };\n    };\n    Spectrum.prototype.countStepDecimals = function () {\n        var stepDecimals = this.xNumSteps.map(countDecimals);\n        return Math.max.apply(null, stepDecimals);\n    };\n    Spectrum.prototype.hasNoSize = function () {\n        return this.xVal[0] === this.xVal[this.xVal.length - 1];\n    };\n    // Outside testing\n    Spectrum.prototype.convert = function (value) {\n        return this.getStep(this.toStepping(value));\n    };\n    Spectrum.prototype.handleEntryPoint = function (index, value) {\n        var percentage;\n        // Covert min/max syntax to 0 and 100.\n        if (index === "min") {\n            percentage = 0;\n        }\n        else if (index === "max") {\n            percentage = 100;\n        }\n        else {\n            percentage = parseFloat(index);\n        }\n        // Check for correct input.\n        if (!isNumeric(percentage) || !isNumeric(value[0])) {\n            throw new Error("noUiSlider: \'range\' value isn\'t numeric.");\n        }\n        // Store values.\n        this.xPct.push(percentage);\n        this.xVal.push(value[0]);\n        var value1 = Number(value[1]);\n        // NaN will evaluate to false too, but to keep\n        // logging clear, set step explicitly. Make sure\n        // not to override the \'step\' setting with false.\n        if (!percentage) {\n            if (!isNaN(value1)) {\n                this.xSteps[0] = value1;\n            }\n        }\n        else {\n            this.xSteps.push(isNaN(value1) ? false : value1);\n        }\n        this.xHighestCompleteStep.push(0);\n    };\n    Spectrum.prototype.handleStepPoint = function (i, n) {\n        // Ignore \'false\' stepping.\n        if (!n) {\n            return;\n        }\n        // Step over zero-length ranges (#948);\n        if (this.xVal[i] === this.xVal[i + 1]) {\n            this.xSteps[i] = this.xHighestCompleteStep[i] = this.xVal[i];\n            return;\n        }\n        // Factor to range ratio\n        this.xSteps[i] =\n            fromPercentage([this.xVal[i], this.xVal[i + 1]], n, 0) / subRangeRatio(this.xPct[i], this.xPct[i + 1]);\n        var totalSteps = (this.xVal[i + 1] - this.xVal[i]) / this.xNumSteps[i];\n        var highestStep = Math.ceil(Number(totalSteps.toFixed(3)) - 1);\n        var step = this.xVal[i] + this.xNumSteps[i] * highestStep;\n        this.xHighestCompleteStep[i] = step;\n    };\n    return Spectrum;\n}());\n//endregion\n//region Options\n/*\tEvery input option is tested and parsed. This will prevent\n    endless validation in internal methods. These tests are\n    structured with an item for every option available. An\n    option can be marked as required by setting the \'r\' flag.\n    The testing function is provided with three arguments:\n        - The provided value for the option;\n        - A reference to the options object;\n        - The name for the option;\n\n    The testing function returns false when an error is detected,\n    or true when everything is OK. It can also modify the option\n    object, to make sure all values can be correctly looped elsewhere. */\n//region Defaults\nvar defaultFormatter = {\n    to: function (value) {\n        return value === undefined ? "" : value.toFixed(2);\n    },\n    from: Number,\n};\nvar cssClasses = {\n    target: "target",\n    base: "base",\n    origin: "origin",\n    handle: "handle",\n    handleLower: "handle-lower",\n    handleUpper: "handle-upper",\n    touchArea: "touch-area",\n    horizontal: "horizontal",\n    vertical: "vertical",\n    background: "background",\n    connect: "connect",\n    connects: "connects",\n    ltr: "ltr",\n    rtl: "rtl",\n    textDirectionLtr: "txt-dir-ltr",\n    textDirectionRtl: "txt-dir-rtl",\n    draggable: "draggable",\n    drag: "state-drag",\n    tap: "state-tap",\n    active: "active",\n    tooltip: "tooltip",\n    pips: "pips",\n    pipsHorizontal: "pips-horizontal",\n    pipsVertical: "pips-vertical",\n    marker: "marker",\n    markerHorizontal: "marker-horizontal",\n    markerVertical: "marker-vertical",\n    markerNormal: "marker-normal",\n    markerLarge: "marker-large",\n    markerSub: "marker-sub",\n    value: "value",\n    valueHorizontal: "value-horizontal",\n    valueVertical: "value-vertical",\n    valueNormal: "value-normal",\n    valueLarge: "value-large",\n    valueSub: "value-sub",\n};\n// Namespaces of internal event listeners\nvar INTERNAL_EVENT_NS = {\n    tooltips: ".__tooltips",\n    aria: ".__aria",\n};\n//endregion\nfunction testStep(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error("noUiSlider: \'step\' is not numeric.");\n    }\n    // The step option can still be used to set stepping\n    // for linear sliders. Overwritten if set in \'range\'.\n    parsed.singleStep = entry;\n}\nfunction testKeyboardPageMultiplier(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error("noUiSlider: \'keyboardPageMultiplier\' is not numeric.");\n    }\n    parsed.keyboardPageMultiplier = entry;\n}\nfunction testKeyboardMultiplier(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error("noUiSlider: \'keyboardMultiplier\' is not numeric.");\n    }\n    parsed.keyboardMultiplier = entry;\n}\nfunction testKeyboardDefaultStep(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error("noUiSlider: \'keyboardDefaultStep\' is not numeric.");\n    }\n    parsed.keyboardDefaultStep = entry;\n}\nfunction testRange(parsed, entry) {\n    // Filter incorrect input.\n    if (typeof entry !== "object" || Array.isArray(entry)) {\n        throw new Error("noUiSlider: \'range\' is not an object.");\n    }\n    // Catch missing start or end.\n    if (entry.min === undefined || entry.max === undefined) {\n        throw new Error("noUiSlider: Missing \'min\' or \'max\' in \'range\'.");\n    }\n    parsed.spectrum = new Spectrum(entry, parsed.snap || false, parsed.singleStep);\n}\nfunction testStart(parsed, entry) {\n    entry = asArray(entry);\n    // Validate input. Values aren\'t tested, as the public .val method\n    // will always provide a valid location.\n    if (!Array.isArray(entry) || !entry.length) {\n        throw new Error("noUiSlider: \'start\' option is incorrect.");\n    }\n    // Store the number of handles.\n    parsed.handles = entry.length;\n    // When the slider is initialized, the .val method will\n    // be called with the start options.\n    parsed.start = entry;\n}\nfunction testSnap(parsed, entry) {\n    if (typeof entry !== "boolean") {\n        throw new Error("noUiSlider: \'snap\' option must be a boolean.");\n    }\n    // Enforce 100% stepping within subranges.\n    parsed.snap = entry;\n}\nfunction testAnimate(parsed, entry) {\n    if (typeof entry !== "boolean") {\n        throw new Error("noUiSlider: \'animate\' option must be a boolean.");\n    }\n    // Enforce 100% stepping within subranges.\n    parsed.animate = entry;\n}\nfunction testAnimationDuration(parsed, entry) {\n    if (typeof entry !== "number") {\n        throw new Error("noUiSlider: \'animationDuration\' option must be a number.");\n    }\n    parsed.animationDuration = entry;\n}\nfunction testConnect(parsed, entry) {\n    var connect = [false];\n    var i;\n    // Map legacy options\n    if (entry === "lower") {\n        entry = [true, false];\n    }\n    else if (entry === "upper") {\n        entry = [false, true];\n    }\n    // Handle boolean options\n    if (entry === true || entry === false) {\n        for (i = 1; i < parsed.handles; i++) {\n            connect.push(entry);\n        }\n        connect.push(false);\n    }\n    // Reject invalid input\n    else if (!Array.isArray(entry) || !entry.length || entry.length !== parsed.handles + 1) {\n        throw new Error("noUiSlider: \'connect\' option doesn\'t match handle count.");\n    }\n    else {\n        connect = entry;\n    }\n    parsed.connect = connect;\n}\nfunction testOrientation(parsed, entry) {\n    // Set orientation to an a numerical value for easy\n    // array selection.\n    switch (entry) {\n        case "horizontal":\n            parsed.ort = 0;\n            break;\n        case "vertical":\n            parsed.ort = 1;\n            break;\n        default:\n            throw new Error("noUiSlider: \'orientation\' option is invalid.");\n    }\n}\nfunction testMargin(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error("noUiSlider: \'margin\' option must be numeric.");\n    }\n    // Issue #582\n    if (entry === 0) {\n        return;\n    }\n    parsed.margin = parsed.spectrum.getDistance(entry);\n}\nfunction testLimit(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error("noUiSlider: \'limit\' option must be numeric.");\n    }\n    parsed.limit = parsed.spectrum.getDistance(entry);\n    if (!parsed.limit || parsed.handles < 2) {\n        throw new Error("noUiSlider: \'limit\' option is only supported on linear sliders with 2 or more handles.");\n    }\n}\nfunction testPadding(parsed, entry) {\n    var index;\n    if (!isNumeric(entry) && !Array.isArray(entry)) {\n        throw new Error("noUiSlider: \'padding\' option must be numeric or array of exactly 2 numbers.");\n    }\n    if (Array.isArray(entry) && !(entry.length === 2 || isNumeric(entry[0]) || isNumeric(entry[1]))) {\n        throw new Error("noUiSlider: \'padding\' option must be numeric or array of exactly 2 numbers.");\n    }\n    if (entry === 0) {\n        return;\n    }\n    if (!Array.isArray(entry)) {\n        entry = [entry, entry];\n    }\n    // \'getDistance\' returns false for invalid values.\n    parsed.padding = [parsed.spectrum.getDistance(entry[0]), parsed.spectrum.getDistance(entry[1])];\n    for (index = 0; index < parsed.spectrum.xNumSteps.length - 1; index++) {\n        // last "range" can\'t contain step size as it is purely an endpoint.\n        if (parsed.padding[0][index] < 0 || parsed.padding[1][index] < 0) {\n            throw new Error("noUiSlider: \'padding\' option must be a positive number(s).");\n        }\n    }\n    var totalPadding = entry[0] + entry[1];\n    var firstValue = parsed.spectrum.xVal[0];\n    var lastValue = parsed.spectrum.xVal[parsed.spectrum.xVal.length - 1];\n    if (totalPadding / (lastValue - firstValue) > 1) {\n        throw new Error("noUiSlider: \'padding\' option must not exceed 100% of the range.");\n    }\n}\nfunction testDirection(parsed, entry) {\n    // Set direction as a numerical value for easy parsing.\n    // Invert connection for RTL sliders, so that the proper\n    // handles get the connect/background classes.\n    switch (entry) {\n        case "ltr":\n            parsed.dir = 0;\n            break;\n        case "rtl":\n            parsed.dir = 1;\n            break;\n        default:\n            throw new Error("noUiSlider: \'direction\' option was not recognized.");\n    }\n}\nfunction testBehaviour(parsed, entry) {\n    // Make sure the input is a string.\n    if (typeof entry !== "string") {\n        throw new Error("noUiSlider: \'behaviour\' must be a string containing options.");\n    }\n    // Check if the string contains any keywords.\n    // None are required.\n    var tap = entry.indexOf("tap") >= 0;\n    var drag = entry.indexOf("drag") >= 0;\n    var fixed = entry.indexOf("fixed") >= 0;\n    var snap = entry.indexOf("snap") >= 0;\n    var hover = entry.indexOf("hover") >= 0;\n    var unconstrained = entry.indexOf("unconstrained") >= 0;\n    var invertConnects = entry.indexOf("invert-connects") >= 0;\n    var dragAll = entry.indexOf("drag-all") >= 0;\n    var smoothSteps = entry.indexOf("smooth-steps") >= 0;\n    if (fixed) {\n        if (parsed.handles !== 2) {\n            throw new Error("noUiSlider: \'fixed\' behaviour must be used with 2 handles");\n        }\n        // Use margin to enforce fixed state\n        testMargin(parsed, parsed.start[1] - parsed.start[0]);\n    }\n    if (invertConnects && parsed.handles !== 2) {\n        throw new Error("noUiSlider: \'invert-connects\' behaviour must be used with 2 handles");\n    }\n    if (unconstrained && (parsed.margin || parsed.limit)) {\n        throw new Error("noUiSlider: \'unconstrained\' behaviour cannot be used with margin or limit");\n    }\n    parsed.events = {\n        tap: tap || snap,\n        drag: drag,\n        dragAll: dragAll,\n        smoothSteps: smoothSteps,\n        fixed: fixed,\n        snap: snap,\n        hover: hover,\n        unconstrained: unconstrained,\n        invertConnects: invertConnects,\n    };\n}\nfunction testTooltips(parsed, entry) {\n    if (entry === false) {\n        return;\n    }\n    if (entry === true || isValidPartialFormatter(entry)) {\n        parsed.tooltips = [];\n        for (var i = 0; i < parsed.handles; i++) {\n            parsed.tooltips.push(entry);\n        }\n    }\n    else {\n        entry = asArray(entry);\n        if (entry.length !== parsed.handles) {\n            throw new Error("noUiSlider: must pass a formatter for all handles.");\n        }\n        entry.forEach(function (formatter) {\n            if (typeof formatter !== "boolean" && !isValidPartialFormatter(formatter)) {\n                throw new Error("noUiSlider: \'tooltips\' must be passed a formatter or \'false\'.");\n            }\n        });\n        parsed.tooltips = entry;\n    }\n}\nfunction testHandleAttributes(parsed, entry) {\n    if (entry.length !== parsed.handles) {\n        throw new Error("noUiSlider: must pass a attributes for all handles.");\n    }\n    parsed.handleAttributes = entry;\n}\nfunction testAriaFormat(parsed, entry) {\n    if (!isValidPartialFormatter(entry)) {\n        throw new Error("noUiSlider: \'ariaFormat\' requires \'to\' method.");\n    }\n    parsed.ariaFormat = entry;\n}\nfunction testFormat(parsed, entry) {\n    if (!isValidFormatter(entry)) {\n        throw new Error("noUiSlider: \'format\' requires \'to\' and \'from\' methods.");\n    }\n    parsed.format = entry;\n}\nfunction testKeyboardSupport(parsed, entry) {\n    if (typeof entry !== "boolean") {\n        throw new Error("noUiSlider: \'keyboardSupport\' option must be a boolean.");\n    }\n    parsed.keyboardSupport = entry;\n}\nfunction testDocumentElement(parsed, entry) {\n    // This is an advanced option. Passed values are used without validation.\n    parsed.documentElement = entry;\n}\nfunction testCssPrefix(parsed, entry) {\n    if (typeof entry !== "string" && entry !== false) {\n        throw new Error("noUiSlider: \'cssPrefix\' must be a string or `false`.");\n    }\n    parsed.cssPrefix = entry;\n}\nfunction testCssClasses(parsed, entry) {\n    if (typeof entry !== "object") {\n        throw new Error("noUiSlider: \'cssClasses\' must be an object.");\n    }\n    if (typeof parsed.cssPrefix === "string") {\n        parsed.cssClasses = {};\n        Object.keys(entry).forEach(function (key) {\n            parsed.cssClasses[key] = parsed.cssPrefix + entry[key];\n        });\n    }\n    else {\n        parsed.cssClasses = entry;\n    }\n}\n// Test all developer settings and parse to assumption-safe values.\nfunction testOptions(options) {\n    // To prove a fix for #537, freeze options here.\n    // If the object is modified, an error will be thrown.\n    // Object.freeze(options);\n    var parsed = {\n        margin: null,\n        limit: null,\n        padding: null,\n        animate: true,\n        animationDuration: 300,\n        ariaFormat: defaultFormatter,\n        format: defaultFormatter,\n    };\n    // Tests are executed in the order they are presented here.\n    var tests = {\n        step: { r: false, t: testStep },\n        keyboardPageMultiplier: { r: false, t: testKeyboardPageMultiplier },\n        keyboardMultiplier: { r: false, t: testKeyboardMultiplier },\n        keyboardDefaultStep: { r: false, t: testKeyboardDefaultStep },\n        start: { r: true, t: testStart },\n        connect: { r: true, t: testConnect },\n        direction: { r: true, t: testDirection },\n        snap: { r: false, t: testSnap },\n        animate: { r: false, t: testAnimate },\n        animationDuration: { r: false, t: testAnimationDuration },\n        range: { r: true, t: testRange },\n        orientation: { r: false, t: testOrientation },\n        margin: { r: false, t: testMargin },\n        limit: { r: false, t: testLimit },\n        padding: { r: false, t: testPadding },\n        behaviour: { r: true, t: testBehaviour },\n        ariaFormat: { r: false, t: testAriaFormat },\n        format: { r: false, t: testFormat },\n        tooltips: { r: false, t: testTooltips },\n        keyboardSupport: { r: true, t: testKeyboardSupport },\n        documentElement: { r: false, t: testDocumentElement },\n        cssPrefix: { r: true, t: testCssPrefix },\n        cssClasses: { r: true, t: testCssClasses },\n        handleAttributes: { r: false, t: testHandleAttributes },\n    };\n    var defaults = {\n        connect: false,\n        direction: "ltr",\n        behaviour: "tap",\n        orientation: "horizontal",\n        keyboardSupport: true,\n        cssPrefix: "noUi-",\n        cssClasses: cssClasses,\n        keyboardPageMultiplier: 5,\n        keyboardMultiplier: 1,\n        keyboardDefaultStep: 10,\n    };\n    // AriaFormat defaults to regular format, if any.\n    if (options.format && !options.ariaFormat) {\n        options.ariaFormat = options.format;\n    }\n    // Run all options through a testing mechanism to ensure correct\n    // input. It should be noted that options might get modified to\n    // be handled properly. E.g. wrapping integers in arrays.\n    Object.keys(tests).forEach(function (name) {\n        // If the option isn\'t set, but it is required, throw an error.\n        if (!isSet(options[name]) && defaults[name] === undefined) {\n            if (tests[name].r) {\n                throw new Error("noUiSlider: \'" + name + "\' is required.");\n            }\n            return;\n        }\n        tests[name].t(parsed, !isSet(options[name]) ? defaults[name] : options[name]);\n    });\n    // Forward pips options\n    parsed.pips = options.pips;\n    // All recent browsers accept unprefixed transform.\n    // We need -ms- for IE9 and -webkit- for older Android;\n    // Assume use of -webkit- if unprefixed and -ms- are not supported.\n    // https://caniuse.com/#feat=transforms2d\n    var d = document.createElement("div");\n    var msPrefix = d.style.msTransform !== undefined;\n    var noPrefix = d.style.transform !== undefined;\n    parsed.transformRule = noPrefix ? "transform" : msPrefix ? "msTransform" : "webkitTransform";\n    // Pips don\'t move, so we can place them using left/top.\n    var styles = [\n        ["left", "top"],\n        ["right", "bottom"],\n    ];\n    parsed.style = styles[parsed.dir][parsed.ort];\n    return parsed;\n}\n//endregion\nfunction scope(target, options, originalOptions) {\n    var actions = getActions();\n    var supportsTouchActionNone = getSupportsTouchActionNone();\n    var supportsPassive = supportsTouchActionNone && getSupportsPassive();\n    // All variables local to \'scope\' are prefixed with \'scope_\'\n    // Slider DOM Nodes\n    var scope_Target = target;\n    var scope_Base;\n    var scope_ConnectBase;\n    var scope_Handles;\n    var scope_Connects;\n    var scope_Pips;\n    var scope_Tooltips;\n    // Slider state values\n    var scope_Spectrum = options.spectrum;\n    var scope_Values = [];\n    var scope_Locations = [];\n    var scope_HandleNumbers = [];\n    var scope_ActiveHandlesCount = 0;\n    var scope_Events = {};\n    var scope_ConnectsInverted = false;\n    // Document Nodes\n    var scope_Document = target.ownerDocument;\n    var scope_DocumentElement = options.documentElement || scope_Document.documentElement;\n    var scope_Body = scope_Document.body;\n    // For horizontal sliders in standard ltr documents,\n    // make .noUi-origin overflow to the left so the document doesn\'t scroll.\n    var scope_DirOffset = scope_Document.dir === "rtl" || options.ort === 1 ? 0 : 100;\n    // Creates a node, adds it to target, returns the new node.\n    function addNodeTo(addTarget, className) {\n        var div = scope_Document.createElement("div");\n        if (className) {\n            addClass(div, className);\n        }\n        addTarget.appendChild(div);\n        return div;\n    }\n    // Append a origin to the base\n    function addOrigin(base, handleNumber) {\n        var origin = addNodeTo(base, options.cssClasses.origin);\n        var handle = addNodeTo(origin, options.cssClasses.handle);\n        addNodeTo(handle, options.cssClasses.touchArea);\n        handle.setAttribute("data-handle", String(handleNumber));\n        if (options.keyboardSupport) {\n            // https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex\n            // 0 = focusable and reachable\n            handle.setAttribute("tabindex", "0");\n            handle.addEventListener("keydown", function (event) {\n                return eventKeydown(event, handleNumber);\n            });\n        }\n        if (options.handleAttributes !== undefined) {\n            var attributes_1 = options.handleAttributes[handleNumber];\n            Object.keys(attributes_1).forEach(function (attribute) {\n                handle.setAttribute(attribute, attributes_1[attribute]);\n            });\n        }\n        handle.setAttribute("role", "slider");\n        handle.setAttribute("aria-orientation", options.ort ? "vertical" : "horizontal");\n        if (handleNumber === 0) {\n            addClass(handle, options.cssClasses.handleLower);\n        }\n        else if (handleNumber === options.handles - 1) {\n            addClass(handle, options.cssClasses.handleUpper);\n        }\n        origin.handle = handle;\n        return origin;\n    }\n    // Insert nodes for connect elements\n    function addConnect(base, add) {\n        if (!add) {\n            return false;\n        }\n        return addNodeTo(base, options.cssClasses.connect);\n    }\n    // Add handles to the slider base.\n    function addElements(connectOptions, base) {\n        scope_ConnectBase = addNodeTo(base, options.cssClasses.connects);\n        scope_Handles = [];\n        scope_Connects = [];\n        scope_Connects.push(addConnect(scope_ConnectBase, connectOptions[0]));\n        // [::::O====O====O====]\n        // connectOptions = [0, 1, 1, 1]\n        for (var i = 0; i < options.handles; i++) {\n            // Keep a list of all added handles.\n            scope_Handles.push(addOrigin(base, i));\n            scope_HandleNumbers[i] = i;\n            scope_Connects.push(addConnect(scope_ConnectBase, connectOptions[i + 1]));\n        }\n    }\n    // Initialize a single slider.\n    function addSlider(addTarget) {\n        // Apply classes and data to the target.\n        addClass(addTarget, options.cssClasses.target);\n        if (options.dir === 0) {\n            addClass(addTarget, options.cssClasses.ltr);\n        }\n        else {\n            addClass(addTarget, options.cssClasses.rtl);\n        }\n        if (options.ort === 0) {\n            addClass(addTarget, options.cssClasses.horizontal);\n        }\n        else {\n            addClass(addTarget, options.cssClasses.vertical);\n        }\n        var textDirection = getComputedStyle(addTarget).direction;\n        if (textDirection === "rtl") {\n            addClass(addTarget, options.cssClasses.textDirectionRtl);\n        }\n        else {\n            addClass(addTarget, options.cssClasses.textDirectionLtr);\n        }\n        return addNodeTo(addTarget, options.cssClasses.base);\n    }\n    function addTooltip(handle, handleNumber) {\n        if (!options.tooltips || !options.tooltips[handleNumber]) {\n            return false;\n        }\n        return addNodeTo(handle.firstChild, options.cssClasses.tooltip);\n    }\n    function isSliderDisabled() {\n        return scope_Target.hasAttribute("disabled");\n    }\n    // Disable the slider dragging if any handle is disabled\n    function isHandleDisabled(handleNumber) {\n        var handleOrigin = scope_Handles[handleNumber];\n        return handleOrigin.hasAttribute("disabled");\n    }\n    function disable(handleNumber) {\n        if (handleNumber !== null && handleNumber !== undefined) {\n            scope_Handles[handleNumber].setAttribute("disabled", "");\n            scope_Handles[handleNumber].handle.removeAttribute("tabindex");\n        }\n        else {\n            scope_Target.setAttribute("disabled", "");\n            scope_Handles.forEach(function (handle) {\n                handle.handle.removeAttribute("tabindex");\n            });\n        }\n    }\n    function enable(handleNumber) {\n        if (handleNumber !== null && handleNumber !== undefined) {\n            scope_Handles[handleNumber].removeAttribute("disabled");\n            scope_Handles[handleNumber].handle.setAttribute("tabindex", "0");\n        }\n        else {\n            scope_Target.removeAttribute("disabled");\n            scope_Handles.forEach(function (handle) {\n                handle.removeAttribute("disabled");\n                handle.handle.setAttribute("tabindex", "0");\n            });\n        }\n    }\n    function removeTooltips() {\n        if (scope_Tooltips) {\n            removeEvent("update" + INTERNAL_EVENT_NS.tooltips);\n            scope_Tooltips.forEach(function (tooltip) {\n                if (tooltip) {\n                    removeElement(tooltip);\n                }\n            });\n            scope_Tooltips = null;\n        }\n    }\n    // The tooltips option is a shorthand for using the \'update\' event.\n    function tooltips() {\n        removeTooltips();\n        // Tooltips are added with options.tooltips in original order.\n        scope_Tooltips = scope_Handles.map(addTooltip);\n        bindEvent("update" + INTERNAL_EVENT_NS.tooltips, function (values, handleNumber, unencoded) {\n            if (!scope_Tooltips || !options.tooltips) {\n                return;\n            }\n            if (scope_Tooltips[handleNumber] === false) {\n                return;\n            }\n            var formattedValue = values[handleNumber];\n            if (options.tooltips[handleNumber] !== true) {\n                formattedValue = options.tooltips[handleNumber].to(unencoded[handleNumber]);\n            }\n            scope_Tooltips[handleNumber].innerHTML = formattedValue;\n        });\n    }\n    function aria() {\n        removeEvent("update" + INTERNAL_EVENT_NS.aria);\n        bindEvent("update" + INTERNAL_EVENT_NS.aria, function (values, handleNumber, unencoded, tap, positions) {\n            // Update Aria Values for all handles, as a change in one changes min and max values for the next.\n            scope_HandleNumbers.forEach(function (index) {\n                var handle = scope_Handles[index];\n                var min = checkHandlePosition(scope_Locations, index, 0, true, true, true);\n                var max = checkHandlePosition(scope_Locations, index, 100, true, true, true);\n                var now = positions[index];\n                // Formatted value for display\n                var text = String(options.ariaFormat.to(unencoded[index]));\n                // Map to slider range values\n                min = scope_Spectrum.fromStepping(min).toFixed(1);\n                max = scope_Spectrum.fromStepping(max).toFixed(1);\n                now = scope_Spectrum.fromStepping(now).toFixed(1);\n                handle.children[0].setAttribute("aria-valuemin", min);\n                handle.children[0].setAttribute("aria-valuemax", max);\n                handle.children[0].setAttribute("aria-valuenow", now);\n                handle.children[0].setAttribute("aria-valuetext", text);\n            });\n        });\n    }\n    function getGroup(pips) {\n        // Use the range.\n        if (pips.mode === PipsMode.Range || pips.mode === PipsMode.Steps) {\n            return scope_Spectrum.xVal;\n        }\n        if (pips.mode === PipsMode.Count) {\n            if (pips.values < 2) {\n                throw new Error("noUiSlider: \'values\' (>= 2) required for mode \'count\'.");\n            }\n            // Divide 0 - 100 in \'count\' parts.\n            var interval = pips.values - 1;\n            var spread = 100 / interval;\n            var values = [];\n            // List these parts and have them handled as \'positions\'.\n            while (interval--) {\n                values[interval] = interval * spread;\n            }\n            values.push(100);\n            return mapToRange(values, pips.stepped);\n        }\n        if (pips.mode === PipsMode.Positions) {\n            // Map all percentages to on-range values.\n            return mapToRange(pips.values, pips.stepped);\n        }\n        if (pips.mode === PipsMode.Values) {\n            // If the value must be stepped, it needs to be converted to a percentage first.\n            if (pips.stepped) {\n                return pips.values.map(function (value) {\n                    // Convert to percentage, apply step, return to value.\n                    return scope_Spectrum.fromStepping(scope_Spectrum.getStep(scope_Spectrum.toStepping(value)));\n                });\n            }\n            // Otherwise, we can simply use the values.\n            return pips.values;\n        }\n        return []; // pips.mode = never\n    }\n    function mapToRange(values, stepped) {\n        return values.map(function (value) {\n            return scope_Spectrum.fromStepping(stepped ? scope_Spectrum.getStep(value) : value);\n        });\n    }\n    function generateSpread(pips) {\n        function safeIncrement(value, increment) {\n            // Avoid floating point variance by dropping the smallest decimal places.\n            return Number((value + increment).toFixed(7));\n        }\n        var group = getGroup(pips);\n        var indexes = {};\n        var firstInRange = scope_Spectrum.xVal[0];\n        var lastInRange = scope_Spectrum.xVal[scope_Spectrum.xVal.length - 1];\n        var ignoreFirst = false;\n        var ignoreLast = false;\n        var prevPct = 0;\n        // Create a copy of the group, sort it and filter away all duplicates.\n        group = unique(group.slice().sort(function (a, b) {\n            return a - b;\n        }));\n        // Make sure the range starts with the first element.\n        if (group[0] !== firstInRange) {\n            group.unshift(firstInRange);\n            ignoreFirst = true;\n        }\n        // Likewise for the last one.\n        if (group[group.length - 1] !== lastInRange) {\n            group.push(lastInRange);\n            ignoreLast = true;\n        }\n        group.forEach(function (current, index) {\n            // Get the current step and the lower + upper positions.\n            var step;\n            var i;\n            var q;\n            var low = current;\n            var high = group[index + 1];\n            var newPct;\n            var pctDifference;\n            var pctPos;\n            var type;\n            var steps;\n            var realSteps;\n            var stepSize;\n            var isSteps = pips.mode === PipsMode.Steps;\n            // When using \'steps\' mode, use the provided steps.\n            // Otherwise, we\'ll step on to the next subrange.\n            if (isSteps) {\n                step = scope_Spectrum.xNumSteps[index];\n            }\n            // Default to a \'full\' step.\n            if (!step) {\n                step = high - low;\n            }\n            // If high is undefined we are at the last subrange. Make sure it iterates once (#1088)\n            if (high === undefined) {\n                high = low;\n            }\n            // Make sure step isn\'t 0, which would cause an infinite loop (#654)\n            step = Math.max(step, 0.0000001);\n            // Find all steps in the subrange.\n            for (i = low; i <= high; i = safeIncrement(i, step)) {\n                // Get the percentage value for the current step,\n                // calculate the size for the subrange.\n                newPct = scope_Spectrum.toStepping(i);\n                pctDifference = newPct - prevPct;\n                steps = pctDifference / (pips.density || 1);\n                realSteps = Math.round(steps);\n                // This ratio represents the amount of percentage-space a point indicates.\n                // For a density 1 the points/percentage = 1. For density 2, that percentage needs to be re-divided.\n                // Round the percentage offset to an even number, then divide by two\n                // to spread the offset on both sides of the range.\n                stepSize = pctDifference / realSteps;\n                // Divide all points evenly, adding the correct number to this subrange.\n                // Run up to <= so that 100% gets a point, event if ignoreLast is set.\n                for (q = 1; q <= realSteps; q += 1) {\n                    // The ratio between the rounded value and the actual size might be ~1% off.\n                    // Correct the percentage offset by the number of points\n                    // per subrange. density = 1 will result in 100 points on the\n                    // full range, 2 for 50, 4 for 25, etc.\n                    pctPos = prevPct + q * stepSize;\n                    indexes[pctPos.toFixed(5)] = [scope_Spectrum.fromStepping(pctPos), 0];\n                }\n                // Determine the point type.\n                type = group.indexOf(i) > -1 ? PipsType.LargeValue : isSteps ? PipsType.SmallValue : PipsType.NoValue;\n                // Enforce the \'ignoreFirst\' option by overwriting the type for 0.\n                if (!index && ignoreFirst && i !== high) {\n                    type = 0;\n                }\n                if (!(i === high && ignoreLast)) {\n                    // Mark the \'type\' of this point. 0 = plain, 1 = real value, 2 = step value.\n                    indexes[newPct.toFixed(5)] = [i, type];\n                }\n                // Update the percentage count.\n                prevPct = newPct;\n            }\n        });\n        return indexes;\n    }\n    function addMarking(spread, filterFunc, formatter) {\n        var _a, _b;\n        var element = scope_Document.createElement("div");\n        var valueSizeClasses = (_a = {},\n            _a[PipsType.None] = "",\n            _a[PipsType.NoValue] = options.cssClasses.valueNormal,\n            _a[PipsType.LargeValue] = options.cssClasses.valueLarge,\n            _a[PipsType.SmallValue] = options.cssClasses.valueSub,\n            _a);\n        var markerSizeClasses = (_b = {},\n            _b[PipsType.None] = "",\n            _b[PipsType.NoValue] = options.cssClasses.markerNormal,\n            _b[PipsType.LargeValue] = options.cssClasses.markerLarge,\n            _b[PipsType.SmallValue] = options.cssClasses.markerSub,\n            _b);\n        var valueOrientationClasses = [options.cssClasses.valueHorizontal, options.cssClasses.valueVertical];\n        var markerOrientationClasses = [options.cssClasses.markerHorizontal, options.cssClasses.markerVertical];\n        addClass(element, options.cssClasses.pips);\n        addClass(element, options.ort === 0 ? options.cssClasses.pipsHorizontal : options.cssClasses.pipsVertical);\n        function getClasses(type, source) {\n            var a = source === options.cssClasses.value;\n            var orientationClasses = a ? valueOrientationClasses : markerOrientationClasses;\n            var sizeClasses = a ? valueSizeClasses : markerSizeClasses;\n            return source + " " + orientationClasses[options.ort] + " " + sizeClasses[type];\n        }\n        function addSpread(offset, value, type) {\n            // Apply the filter function, if it is set.\n            type = filterFunc ? filterFunc(value, type) : type;\n            if (type === PipsType.None) {\n                return;\n            }\n            // Add a marker for every point\n            var node = addNodeTo(element, false);\n            node.className = getClasses(type, options.cssClasses.marker);\n            node.style[options.style] = offset + "%";\n            // Values are only appended for points marked \'1\' or \'2\'.\n            if (type > PipsType.NoValue) {\n                node = addNodeTo(element, false);\n                node.className = getClasses(type, options.cssClasses.value);\n                node.setAttribute("data-value", String(value));\n                node.style[options.style] = offset + "%";\n                node.innerHTML = String(formatter.to(value));\n            }\n        }\n        // Append all points.\n        Object.keys(spread).forEach(function (offset) {\n            addSpread(offset, spread[offset][0], spread[offset][1]);\n        });\n        return element;\n    }\n    function removePips() {\n        if (scope_Pips) {\n            removeElement(scope_Pips);\n            scope_Pips = null;\n        }\n    }\n    function pips(pips) {\n        // Fix #669\n        removePips();\n        var spread = generateSpread(pips);\n        var filter = pips.filter;\n        var format = pips.format || {\n            to: function (value) {\n                return String(Math.round(value));\n            },\n        };\n        scope_Pips = scope_Target.appendChild(addMarking(spread, filter, format));\n        return scope_Pips;\n    }\n    // Shorthand for base dimensions.\n    function baseSize() {\n        var rect = scope_Base.getBoundingClientRect();\n        var alt = ("offset" + ["Width", "Height"][options.ort]);\n        return options.ort === 0 ? rect.width || scope_Base[alt] : rect.height || scope_Base[alt];\n    }\n    // Handler for attaching events trough a proxy.\n    function attachEvent(events, element, callback, data) {\n        // This function can be used to \'filter\' events to the slider.\n        // element is a node, not a nodeList\n        var method = function (event) {\n            var e = fixEvent(event, data.pageOffset, data.target || element);\n            // fixEvent returns false if this event has a different target\n            // when handling (multi-) touch events;\n            if (!e) {\n                return false;\n            }\n            // doNotReject is passed by all end events to make sure released touches\n            // are not rejected, leaving the slider "stuck" to the cursor;\n            if (isSliderDisabled() && !data.doNotReject) {\n                return false;\n            }\n            // Stop if an active \'tap\' transition is taking place.\n            if (hasClass(scope_Target, options.cssClasses.tap) && !data.doNotReject) {\n                return false;\n            }\n            // Ignore right or middle clicks on start #454\n            if (events === actions.start && e.buttons !== undefined && e.buttons > 1) {\n                return false;\n            }\n            // Ignore right or middle clicks on start #454\n            if (data.hover && e.buttons) {\n                return false;\n            }\n            // \'supportsPassive\' is only true if a browser also supports touch-action: none in CSS.\n            // iOS safari does not, so it doesn\'t get to benefit from passive scrolling. iOS does support\n            // touch-action: manipulation, but that allows panning, which breaks\n            // sliders after zooming/on non-responsive pages.\n            // See: https://bugs.webkit.org/show_bug.cgi?id=133112\n            if (!supportsPassive) {\n                e.preventDefault();\n            }\n            e.calcPoint = e.points[options.ort];\n            // Call the event handler with the event [ and additional data ].\n            callback(e, data);\n            return;\n        };\n        var methods = [];\n        // Bind a closure on the target for every event type.\n        events.split(" ").forEach(function (eventName) {\n            element.addEventListener(eventName, method, supportsPassive ? { passive: true } : false);\n            methods.push([eventName, method]);\n        });\n        return methods;\n    }\n    // Provide a clean event with standardized offset values.\n    function fixEvent(e, pageOffset, eventTarget) {\n        // Filter the event to register the type, which can be\n        // touch, mouse or pointer. Offset changes need to be\n        // made on an event specific basis.\n        var touch = e.type.indexOf("touch") === 0;\n        var mouse = e.type.indexOf("mouse") === 0;\n        var pointer = e.type.indexOf("pointer") === 0;\n        var x = 0;\n        var y = 0;\n        // IE10 implemented pointer events with a prefix;\n        if (e.type.indexOf("MSPointer") === 0) {\n            pointer = true;\n        }\n        // Erroneous events seem to be passed in occasionally on iOS/iPadOS after user finishes interacting with\n        // the slider. They appear to be of type MouseEvent, yet they don\'t have usual properties set. Ignore\n        // events that have no touches or buttons associated with them. (#1057, #1079, #1095)\n        if (e.type === "mousedown" && !e.buttons && !e.touches) {\n            return false;\n        }\n        // The only thing one handle should be concerned about is the touches that originated on top of it.\n        if (touch) {\n            // Returns true if a touch originated on the target.\n            var isTouchOnTarget = function (checkTouch) {\n                var target = checkTouch.target;\n                return (target === eventTarget ||\n                    eventTarget.contains(target) ||\n                    (e.composed && e.composedPath().shift() === eventTarget));\n            };\n            // In the case of touchstart events, we need to make sure there is still no more than one\n            // touch on the target so we look amongst all touches.\n            if (e.type === "touchstart") {\n                var targetTouches = Array.prototype.filter.call(e.touches, isTouchOnTarget);\n                // Do not support more than one touch per handle.\n                if (targetTouches.length > 1) {\n                    return false;\n                }\n                x = targetTouches[0].pageX;\n                y = targetTouches[0].pageY;\n            }\n            else {\n                // In the other cases, find on changedTouches is enough.\n                var targetTouch = Array.prototype.find.call(e.changedTouches, isTouchOnTarget);\n                // Cancel if the target touch has not moved.\n                if (!targetTouch) {\n                    return false;\n                }\n                x = targetTouch.pageX;\n                y = targetTouch.pageY;\n            }\n        }\n        pageOffset = pageOffset || getPageOffset(scope_Document);\n        if (mouse || pointer) {\n            x = e.clientX + pageOffset.x;\n            y = e.clientY + pageOffset.y;\n        }\n        e.pageOffset = pageOffset;\n        e.points = [x, y];\n        e.cursor = mouse || pointer; // Fix #435\n        return e;\n    }\n    // Translate a coordinate in the document to a percentage on the slider\n    function calcPointToPercentage(calcPoint) {\n        var location = calcPoint - offset(scope_Base, options.ort);\n        var proposal = (location * 100) / baseSize();\n        // Clamp proposal between 0% and 100%\n        // Out-of-bound coordinates may occur when .noUi-base pseudo-elements\n        // are used (e.g. contained handles feature)\n        proposal = limit(proposal);\n        return options.dir ? 100 - proposal : proposal;\n    }\n    // Find handle closest to a certain percentage on the slider\n    function getClosestHandle(clickedPosition) {\n        var smallestDifference = 100;\n        var handleNumber = false;\n        scope_Handles.forEach(function (handle, index) {\n            // Disabled handles are ignored\n            if (isHandleDisabled(index)) {\n                return;\n            }\n            var handlePosition = scope_Locations[index];\n            var differenceWithThisHandle = Math.abs(handlePosition - clickedPosition);\n            // Initial state\n            var clickAtEdge = differenceWithThisHandle === 100 && smallestDifference === 100;\n            // Difference with this handle is smaller than the previously checked handle\n            var isCloser = differenceWithThisHandle < smallestDifference;\n            var isCloserAfter = differenceWithThisHandle <= smallestDifference && clickedPosition > handlePosition;\n            if (isCloser || isCloserAfter || clickAtEdge) {\n                handleNumber = index;\n                smallestDifference = differenceWithThisHandle;\n            }\n        });\n        return handleNumber;\n    }\n    // Fire \'end\' when a mouse or pen leaves the document.\n    function documentLeave(event, data) {\n        if (event.type === "mouseout" &&\n            event.target.nodeName === "HTML" &&\n            event.relatedTarget === null) {\n            eventEnd(event, data);\n        }\n    }\n    // Handle movement on document for handle and range drag.\n    function eventMove(event, data) {\n        // Fix #498\n        // Check value of .buttons in \'start\' to work around a bug in IE10 mobile (data.buttonsProperty).\n        // https://connect.microsoft.com/IE/feedback/details/927005/mobile-ie10-windows-phone-buttons-property-of-pointermove-event-always-zero\n        // IE9 has .buttons and .which zero on mousemove.\n        // Firefox breaks the spec MDN defines.\n        if (navigator.appVersion.indexOf("MSIE 9") === -1 && event.buttons === 0 && data.buttonsProperty !== 0) {\n            return eventEnd(event, data);\n        }\n        // Check if we are moving up or down\n        var movement = (options.dir ? -1 : 1) * (event.calcPoint - data.startCalcPoint);\n        // Convert the movement into a percentage of the slider width/height\n        var proposal = (movement * 100) / data.baseSize;\n        moveHandles(movement > 0, proposal, data.locations, data.handleNumbers, data.connect);\n    }\n    // Unbind move events on document, call callbacks.\n    function eventEnd(event, data) {\n        // The handle is no longer active, so remove the class.\n        if (data.handle) {\n            removeClass(data.handle, options.cssClasses.active);\n            scope_ActiveHandlesCount -= 1;\n        }\n        // Unbind the move and end events, which are added on \'start\'.\n        data.listeners.forEach(function (c) {\n            scope_DocumentElement.removeEventListener(c[0], c[1]);\n        });\n        if (scope_ActiveHandlesCount === 0) {\n            // Remove dragging class.\n            removeClass(scope_Target, options.cssClasses.drag);\n            setZindex();\n            // Remove cursor styles and text-selection events bound to the body.\n            if (event.cursor) {\n                scope_Body.style.cursor = "";\n                scope_Body.removeEventListener("selectstart", preventDefault);\n            }\n        }\n        if (options.events.smoothSteps) {\n            data.handleNumbers.forEach(function (handleNumber) {\n                setHandle(handleNumber, scope_Locations[handleNumber], true, true, false, false);\n            });\n            data.handleNumbers.forEach(function (handleNumber) {\n                fireEvent("update", handleNumber);\n            });\n        }\n        data.handleNumbers.forEach(function (handleNumber) {\n            fireEvent("change", handleNumber);\n            fireEvent("set", handleNumber);\n            fireEvent("end", handleNumber);\n        });\n    }\n    // Bind move events on document.\n    function eventStart(event, data) {\n        // Ignore event if any handle is disabled\n        if (data.handleNumbers.some(isHandleDisabled)) {\n            return;\n        }\n        var handle;\n        if (data.handleNumbers.length === 1) {\n            var handleOrigin = scope_Handles[data.handleNumbers[0]];\n            handle = handleOrigin.children[0];\n            scope_ActiveHandlesCount += 1;\n            // Mark the handle as \'active\' so it can be styled.\n            addClass(handle, options.cssClasses.active);\n        }\n        // A drag should never propagate up to the \'tap\' event.\n        event.stopPropagation();\n        // Record the event listeners.\n        var listeners = [];\n        // Attach the move and end events.\n        var moveEvent = attachEvent(actions.move, scope_DocumentElement, eventMove, {\n            // The event target has changed so we need to propagate the original one so that we keep\n            // relying on it to extract target touches.\n            target: event.target,\n            handle: handle,\n            connect: data.connect,\n            listeners: listeners,\n            startCalcPoint: event.calcPoint,\n            baseSize: baseSize(),\n            pageOffset: event.pageOffset,\n            handleNumbers: data.handleNumbers,\n            buttonsProperty: event.buttons,\n            locations: scope_Locations.slice(),\n        });\n        var endEvent = attachEvent(actions.end, scope_DocumentElement, eventEnd, {\n            target: event.target,\n            handle: handle,\n            listeners: listeners,\n            doNotReject: true,\n            handleNumbers: data.handleNumbers,\n        });\n        var outEvent = attachEvent("mouseout", scope_DocumentElement, documentLeave, {\n            target: event.target,\n            handle: handle,\n            listeners: listeners,\n            doNotReject: true,\n            handleNumbers: data.handleNumbers,\n        });\n        // We want to make sure we pushed the listeners in the listener list rather than creating\n        // a new one as it has already been passed to the event handlers.\n        listeners.push.apply(listeners, moveEvent.concat(endEvent, outEvent));\n        // Text selection isn\'t an issue on touch devices,\n        // so adding cursor styles can be skipped.\n        if (event.cursor) {\n            // Prevent the \'I\' cursor and extend the range-drag cursor.\n            scope_Body.style.cursor = getComputedStyle(event.target).cursor;\n            // Mark the target with a dragging state.\n            if (scope_Handles.length > 1) {\n                addClass(scope_Target, options.cssClasses.drag);\n            }\n            // Prevent text selection when dragging the handles.\n            // In noUiSlider <= 9.2.0, this was handled by calling preventDefault on mouse/touch start/move,\n            // which is scroll blocking. The selectstart event is supported by FireFox starting from version 52,\n            // meaning the only holdout is iOS Safari. This doesn\'t matter: text selection isn\'t triggered there.\n            // The \'cursor\' flag is false.\n            // See: http://caniuse.com/#search=selectstart\n            scope_Body.addEventListener("selectstart", preventDefault, false);\n        }\n        data.handleNumbers.forEach(function (handleNumber) {\n            fireEvent("start", handleNumber);\n        });\n    }\n    // Move closest handle to tapped location.\n    function eventTap(event) {\n        // The tap event shouldn\'t propagate up\n        event.stopPropagation();\n        var proposal = calcPointToPercentage(event.calcPoint);\n        var handleNumber = getClosestHandle(proposal);\n        // Tackle the case that all handles are \'disabled\'.\n        if (handleNumber === false) {\n            return;\n        }\n        // Flag the slider as it is now in a transitional state.\n        // Transition takes a configurable amount of ms (default 300). Re-enable the slider after that.\n        if (!options.events.snap) {\n            addClassFor(scope_Target, options.cssClasses.tap, options.animationDuration);\n        }\n        setHandle(handleNumber, proposal, true, true);\n        setZindex();\n        fireEvent("slide", handleNumber, true);\n        fireEvent("update", handleNumber, true);\n        if (!options.events.snap) {\n            fireEvent("change", handleNumber, true);\n            fireEvent("set", handleNumber, true);\n        }\n        else {\n            eventStart(event, { handleNumbers: [handleNumber] });\n        }\n    }\n    // Fires a \'hover\' event for a hovered mouse/pen position.\n    function eventHover(event) {\n        var proposal = calcPointToPercentage(event.calcPoint);\n        var to = scope_Spectrum.getStep(proposal);\n        var value = scope_Spectrum.fromStepping(to);\n        Object.keys(scope_Events).forEach(function (targetEvent) {\n            if ("hover" === targetEvent.split(".")[0]) {\n                scope_Events[targetEvent].forEach(function (callback) {\n                    callback.call(scope_Self, value);\n                });\n            }\n        });\n    }\n    // Handles keydown on focused handles\n    // Don\'t move the document when pressing arrow keys on focused handles\n    function eventKeydown(event, handleNumber) {\n        if (isSliderDisabled() || isHandleDisabled(handleNumber)) {\n            return false;\n        }\n        var horizontalKeys = ["Left", "Right"];\n        var verticalKeys = ["Down", "Up"];\n        var largeStepKeys = ["PageDown", "PageUp"];\n        var edgeKeys = ["Home", "End"];\n        if (options.dir && !options.ort) {\n            // On an right-to-left slider, the left and right keys act inverted\n            horizontalKeys.reverse();\n        }\n        else if (options.ort && !options.dir) {\n            // On a top-to-bottom slider, the up and down keys act inverted\n            verticalKeys.reverse();\n            largeStepKeys.reverse();\n        }\n        // Strip "Arrow" for IE compatibility. https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key\n        var key = event.key.replace("Arrow", "");\n        var isLargeDown = key === largeStepKeys[0];\n        var isLargeUp = key === largeStepKeys[1];\n        var isDown = key === verticalKeys[0] || key === horizontalKeys[0] || isLargeDown;\n        var isUp = key === verticalKeys[1] || key === horizontalKeys[1] || isLargeUp;\n        var isMin = key === edgeKeys[0];\n        var isMax = key === edgeKeys[1];\n        if (!isDown && !isUp && !isMin && !isMax) {\n            return true;\n        }\n        event.preventDefault();\n        var to;\n        if (isUp || isDown) {\n            var direction = isDown ? 0 : 1;\n            var steps = getNextStepsForHandle(handleNumber);\n            var step = steps[direction];\n            // At the edge of a slider, do nothing\n            if (step === null) {\n                return false;\n            }\n            // No step set, use the default of 10% of the sub-range\n            if (step === false) {\n                step = scope_Spectrum.getDefaultStep(scope_Locations[handleNumber], isDown, options.keyboardDefaultStep);\n            }\n            if (isLargeUp || isLargeDown) {\n                step *= options.keyboardPageMultiplier;\n            }\n            else {\n                step *= options.keyboardMultiplier;\n            }\n            // Step over zero-length ranges (#948);\n            step = Math.max(step, 0.0000001);\n            // Decrement for down steps\n            step = (isDown ? -1 : 1) * step;\n            to = scope_Values[handleNumber] + step;\n        }\n        else if (isMax) {\n            // End key\n            to = options.spectrum.xVal[options.spectrum.xVal.length - 1];\n        }\n        else {\n            // Home key\n            to = options.spectrum.xVal[0];\n        }\n        setHandle(handleNumber, scope_Spectrum.toStepping(to), true, true);\n        fireEvent("slide", handleNumber);\n        fireEvent("update", handleNumber);\n        fireEvent("change", handleNumber);\n        fireEvent("set", handleNumber);\n        return false;\n    }\n    // Attach events to several slider parts.\n    function bindSliderEvents(behaviour) {\n        // Attach the standard drag event to the handles.\n        if (!behaviour.fixed) {\n            scope_Handles.forEach(function (handle, index) {\n                // These events are only bound to the visual handle\n                // element, not the \'real\' origin element.\n                attachEvent(actions.start, handle.children[0], eventStart, {\n                    handleNumbers: [index],\n                });\n            });\n        }\n        // Attach the tap event to the slider base.\n        if (behaviour.tap) {\n            attachEvent(actions.start, scope_Base, eventTap, {});\n        }\n        // Fire hover events\n        if (behaviour.hover) {\n            attachEvent(actions.move, scope_Base, eventHover, {\n                hover: true,\n            });\n        }\n        // Make the range draggable.\n        if (behaviour.drag) {\n            scope_Connects.forEach(function (connect, index) {\n                if (connect === false || index === 0 || index === scope_Connects.length - 1) {\n                    return;\n                }\n                var handleBefore = scope_Handles[index - 1];\n                var handleAfter = scope_Handles[index];\n                var eventHolders = [connect];\n                var handlesToDrag = [handleBefore, handleAfter];\n                var handleNumbersToDrag = [index - 1, index];\n                addClass(connect, options.cssClasses.draggable);\n                // When the range is fixed, the entire range can\n                // be dragged by the handles. The handle in the first\n                // origin will propagate the start event upward,\n                // but it needs to be bound manually on the other.\n                if (behaviour.fixed) {\n                    eventHolders.push(handleBefore.children[0]);\n                    eventHolders.push(handleAfter.children[0]);\n                }\n                if (behaviour.dragAll) {\n                    handlesToDrag = scope_Handles;\n                    handleNumbersToDrag = scope_HandleNumbers;\n                }\n                eventHolders.forEach(function (eventHolder) {\n                    attachEvent(actions.start, eventHolder, eventStart, {\n                        handles: handlesToDrag,\n                        handleNumbers: handleNumbersToDrag,\n                        connect: connect,\n                    });\n                });\n            });\n        }\n    }\n    // Attach an event to this slider, possibly including a namespace\n    function bindEvent(namespacedEvent, callback) {\n        scope_Events[namespacedEvent] = scope_Events[namespacedEvent] || [];\n        scope_Events[namespacedEvent].push(callback);\n        // If the event bound is \'update,\' fire it immediately for all handles.\n        if (namespacedEvent.split(".")[0] === "update") {\n            scope_Handles.forEach(function (a, index) {\n                fireEvent("update", index);\n            });\n        }\n    }\n    function isInternalNamespace(namespace) {\n        return namespace === INTERNAL_EVENT_NS.aria || namespace === INTERNAL_EVENT_NS.tooltips;\n    }\n    // Undo attachment of event\n    function removeEvent(namespacedEvent) {\n        var event = namespacedEvent && namespacedEvent.split(".")[0];\n        var namespace = event ? namespacedEvent.substring(event.length) : namespacedEvent;\n        Object.keys(scope_Events).forEach(function (bind) {\n            var tEvent = bind.split(".")[0];\n            var tNamespace = bind.substring(tEvent.length);\n            if ((!event || event === tEvent) && (!namespace || namespace === tNamespace)) {\n                // only delete protected internal event if intentional\n                if (!isInternalNamespace(tNamespace) || namespace === tNamespace) {\n                    delete scope_Events[bind];\n                }\n            }\n        });\n    }\n    // External event handling\n    function fireEvent(eventName, handleNumber, tap) {\n        Object.keys(scope_Events).forEach(function (targetEvent) {\n            var eventType = targetEvent.split(".")[0];\n            if (eventName === eventType) {\n                scope_Events[targetEvent].forEach(function (callback) {\n                    callback.call(\n                    // Use the slider public API as the scope (\'this\')\n                    scope_Self, \n                    // Return values as array, so arg_1[arg_2] is always valid.\n                    scope_Values.map(options.format.to), \n                    // Handle index, 0 or 1\n                    handleNumber, \n                    // Un-formatted slider values\n                    scope_Values.slice(), \n                    // Event is fired by tap, true or false\n                    tap || false, \n                    // Left offset of the handle, in relation to the slider\n                    scope_Locations.slice(), \n                    // add the slider public API to an accessible parameter when this is unavailable\n                    scope_Self);\n                });\n            }\n        });\n    }\n    // Split out the handle positioning logic so the Move event can use it, too\n    function checkHandlePosition(reference, handleNumber, to, lookBackward, lookForward, getValue, smoothSteps) {\n        var distance;\n        // For sliders with multiple handles, limit movement to the other handle.\n        // Apply the margin option by adding it to the handle positions.\n        if (scope_Handles.length > 1 && !options.events.unconstrained) {\n            if (lookBackward && handleNumber > 0) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber - 1], options.margin, false);\n                to = Math.max(to, distance);\n            }\n            if (lookForward && handleNumber < scope_Handles.length - 1) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber + 1], options.margin, true);\n                to = Math.min(to, distance);\n            }\n        }\n        // The limit option has the opposite effect, limiting handles to a\n        // maximum distance from another. Limit must be > 0, as otherwise\n        // handles would be unmovable.\n        if (scope_Handles.length > 1 && options.limit) {\n            if (lookBackward && handleNumber > 0) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber - 1], options.limit, false);\n                to = Math.min(to, distance);\n            }\n            if (lookForward && handleNumber < scope_Handles.length - 1) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber + 1], options.limit, true);\n                to = Math.max(to, distance);\n            }\n        }\n        // The padding option keeps the handles a certain distance from the\n        // edges of the slider. Padding must be > 0.\n        if (options.padding) {\n            if (handleNumber === 0) {\n                distance = scope_Spectrum.getAbsoluteDistance(0, options.padding[0], false);\n                to = Math.max(to, distance);\n            }\n            if (handleNumber === scope_Handles.length - 1) {\n                distance = scope_Spectrum.getAbsoluteDistance(100, options.padding[1], true);\n                to = Math.min(to, distance);\n            }\n        }\n        if (!smoothSteps) {\n            to = scope_Spectrum.getStep(to);\n        }\n        // Limit percentage to the 0 - 100 range\n        to = limit(to);\n        // Return false if handle can\'t move\n        if (to === reference[handleNumber] && !getValue) {\n            return false;\n        }\n        return to;\n    }\n    // Uses slider orientation to create CSS rules. a = base value;\n    function inRuleOrder(v, a) {\n        var o = options.ort;\n        return (o ? a : v) + ", " + (o ? v : a);\n    }\n    // Moves handle(s) by a percentage\n    // (bool, % to move, [% where handle started, ...], [index in scope_Handles, ...])\n    function moveHandles(upward, proposal, locations, handleNumbers, connect) {\n        var proposals = locations.slice();\n        // Store first handle now, so we still have it in case handleNumbers is reversed\n        var firstHandle = handleNumbers[0];\n        var smoothSteps = options.events.smoothSteps;\n        var b = [!upward, upward];\n        var f = [upward, !upward];\n        // Copy handleNumbers so we don\'t change the dataset\n        handleNumbers = handleNumbers.slice();\n        // Check to see which handle is \'leading\'.\n        // If that one can\'t move the second can\'t either.\n        if (upward) {\n            handleNumbers.reverse();\n        }\n        // Step 1: get the maximum percentage that any of the handles can move\n        if (handleNumbers.length > 1) {\n            handleNumbers.forEach(function (handleNumber, o) {\n                var to = checkHandlePosition(proposals, handleNumber, proposals[handleNumber] + proposal, b[o], f[o], false, smoothSteps);\n                // Stop if one of the handles can\'t move.\n                if (to === false) {\n                    proposal = 0;\n                }\n                else {\n                    proposal = to - proposals[handleNumber];\n                    proposals[handleNumber] = to;\n                }\n            });\n        }\n        // If using one handle, check backward AND forward\n        else {\n            b = f = [true];\n        }\n        var state = false;\n        // Step 2: Try to set the handles with the found percentage\n        handleNumbers.forEach(function (handleNumber, o) {\n            state =\n                setHandle(handleNumber, locations[handleNumber] + proposal, b[o], f[o], false, smoothSteps) || state;\n        });\n        // Step 3: If a handle moved, fire events\n        if (state) {\n            handleNumbers.forEach(function (handleNumber) {\n                fireEvent("update", handleNumber);\n                fireEvent("slide", handleNumber);\n            });\n            // If target is a connect, then fire drag event\n            if (connect != undefined) {\n                fireEvent("drag", firstHandle);\n            }\n        }\n    }\n    // Takes a base value and an offset. This offset is used for the connect bar size.\n    // In the initial design for this feature, the origin element was 1% wide.\n    // Unfortunately, a rounding bug in Chrome makes it impossible to implement this feature\n    // in this manner: https://bugs.chromium.org/p/chromium/issues/detail?id=798223\n    function transformDirection(a, b) {\n        return options.dir ? 100 - a - b : a;\n    }\n    // Updates scope_Locations and scope_Values, updates visual state\n    function updateHandlePosition(handleNumber, to) {\n        // Update locations.\n        scope_Locations[handleNumber] = to;\n        // Convert the value to the slider stepping/range.\n        scope_Values[handleNumber] = scope_Spectrum.fromStepping(to);\n        var translation = transformDirection(to, 0) - scope_DirOffset;\n        var translateRule = "translate(" + inRuleOrder(translation + "%", "0") + ")";\n        scope_Handles[handleNumber].style[options.transformRule] = translateRule;\n        // sanity check for at least 2 handles (e.g. during setup)\n        if (options.events.invertConnects && scope_Locations.length > 1) {\n            // check if handles passed each other, but don\'t match the ConnectsInverted state\n            var handlesAreInOrder = scope_Locations.every(function (position, index, locations) {\n                return index === 0 || position >= locations[index - 1];\n            });\n            if (scope_ConnectsInverted !== !handlesAreInOrder) {\n                // invert connects when handles pass each other\n                invertConnects();\n                // invertConnects already updates all connect elements\n                return;\n            }\n        }\n        updateConnect(handleNumber);\n        updateConnect(handleNumber + 1);\n        if (scope_ConnectsInverted) {\n            // When connects are inverted, we also have to update adjacent connects\n            updateConnect(handleNumber - 1);\n            updateConnect(handleNumber + 2);\n        }\n    }\n    // Handles before the slider middle are stacked later = higher,\n    // Handles after the middle later is lower\n    // [[7] [8] .......... | .......... [5] [4]\n    function setZindex() {\n        scope_HandleNumbers.forEach(function (handleNumber) {\n            var dir = scope_Locations[handleNumber] > 50 ? -1 : 1;\n            var zIndex = 3 + (scope_Handles.length + dir * handleNumber);\n            scope_Handles[handleNumber].style.zIndex = String(zIndex);\n        });\n    }\n    // Test suggested values and apply margin, step.\n    // if exactInput is true, don\'t run checkHandlePosition, then the handle can be placed in between steps (#436)\n    function setHandle(handleNumber, to, lookBackward, lookForward, exactInput, smoothSteps) {\n        if (!exactInput) {\n            to = checkHandlePosition(scope_Locations, handleNumber, to, lookBackward, lookForward, false, smoothSteps);\n        }\n        if (to === false) {\n            return false;\n        }\n        updateHandlePosition(handleNumber, to);\n        return true;\n    }\n    // Updates style attribute for connect nodes\n    function updateConnect(index) {\n        // Skip connects set to false\n        if (!scope_Connects[index]) {\n            return;\n        }\n        // Create a copy of locations, so we can sort them for the local scope logic\n        var locations = scope_Locations.slice();\n        if (scope_ConnectsInverted) {\n            locations.sort(function (a, b) {\n                return a - b;\n            });\n        }\n        var l = 0;\n        var h = 100;\n        if (index !== 0) {\n            l = locations[index - 1];\n        }\n        if (index !== scope_Connects.length - 1) {\n            h = locations[index];\n        }\n        // We use two rules:\n        // \'translate\' to change the left/top offset;\n        // \'scale\' to change the width of the element;\n        // As the element has a width of 100%, a translation of 100% is equal to 100% of the parent (.noUi-base)\n        var connectWidth = h - l;\n        var translateRule = "translate(" + inRuleOrder(transformDirection(l, connectWidth) + "%", "0") + ")";\n        var scaleRule = "scale(" + inRuleOrder(connectWidth / 100, "1") + ")";\n        scope_Connects[index].style[options.transformRule] =\n            translateRule + " " + scaleRule;\n    }\n    // Parses value passed to .set method. Returns current value if not parse-able.\n    function resolveToValue(to, handleNumber) {\n        // Setting with null indicates an \'ignore\'.\n        // Inputting \'false\' is invalid.\n        if (to === null || to === false || to === undefined) {\n            return scope_Locations[handleNumber];\n        }\n        // If a formatted number was passed, attempt to decode it.\n        if (typeof to === "number") {\n            to = String(to);\n        }\n        to = options.format.from(to);\n        if (to !== false) {\n            to = scope_Spectrum.toStepping(to);\n        }\n        // If parsing the number failed, use the current value.\n        if (to === false || isNaN(to)) {\n            return scope_Locations[handleNumber];\n        }\n        return to;\n    }\n    // Set the slider value.\n    function valueSet(input, fireSetEvent, exactInput) {\n        var values = asArray(input);\n        var isInit = scope_Locations[0] === undefined;\n        // Event fires by default\n        fireSetEvent = fireSetEvent === undefined ? true : fireSetEvent;\n        // Animation is optional.\n        // Make sure the initial values were set before using animated placement.\n        if (options.animate && !isInit) {\n            addClassFor(scope_Target, options.cssClasses.tap, options.animationDuration);\n        }\n        // First pass, without lookAhead but with lookBackward. Values are set from left to right.\n        scope_HandleNumbers.forEach(function (handleNumber) {\n            setHandle(handleNumber, resolveToValue(values[handleNumber], handleNumber), true, false, exactInput);\n        });\n        var i = scope_HandleNumbers.length === 1 ? 0 : 1;\n        // Spread handles evenly across the slider if the range has no size (min=max)\n        if (isInit && scope_Spectrum.hasNoSize()) {\n            exactInput = true;\n            scope_Locations[0] = 0;\n            if (scope_HandleNumbers.length > 1) {\n                var space_1 = 100 / (scope_HandleNumbers.length - 1);\n                scope_HandleNumbers.forEach(function (handleNumber) {\n                    scope_Locations[handleNumber] = handleNumber * space_1;\n                });\n            }\n        }\n        // Secondary passes. Now that all base values are set, apply constraints.\n        // Iterate all handles to ensure constraints are applied for the entire slider (Issue #1009)\n        for (; i < scope_HandleNumbers.length; ++i) {\n            scope_HandleNumbers.forEach(function (handleNumber) {\n                setHandle(handleNumber, scope_Locations[handleNumber], true, true, exactInput);\n            });\n        }\n        setZindex();\n        scope_HandleNumbers.forEach(function (handleNumber) {\n            fireEvent("update", handleNumber);\n            // Fire the event only for handles that received a new value, as per #579\n            if (values[handleNumber] !== null && fireSetEvent) {\n                fireEvent("set", handleNumber);\n            }\n        });\n    }\n    // Reset slider to initial values\n    function valueReset(fireSetEvent) {\n        valueSet(options.start, fireSetEvent);\n    }\n    // Set value for a single handle\n    function valueSetHandle(handleNumber, value, fireSetEvent, exactInput) {\n        // Ensure numeric input\n        handleNumber = Number(handleNumber);\n        if (!(handleNumber >= 0 && handleNumber < scope_HandleNumbers.length)) {\n            throw new Error("noUiSlider: invalid handle number, got: " + handleNumber);\n        }\n        // Look both backward and forward, since we don\'t want this handle to "push" other handles (#960);\n        // The exactInput argument can be used to ignore slider stepping (#436)\n        setHandle(handleNumber, resolveToValue(value, handleNumber), true, true, exactInput);\n        fireEvent("update", handleNumber);\n        if (fireSetEvent) {\n            fireEvent("set", handleNumber);\n        }\n    }\n    // Get the slider value.\n    function valueGet(unencoded) {\n        if (unencoded === void 0) { unencoded = false; }\n        if (unencoded) {\n            // return a copy of the raw values\n            return scope_Values.length === 1 ? scope_Values[0] : scope_Values.slice(0);\n        }\n        var values = scope_Values.map(options.format.to);\n        // If only one handle is used, return a single value.\n        if (values.length === 1) {\n            return values[0];\n        }\n        return values;\n    }\n    // Removes classes from the root and empties it.\n    function destroy() {\n        // remove protected internal listeners\n        removeEvent(INTERNAL_EVENT_NS.aria);\n        removeEvent(INTERNAL_EVENT_NS.tooltips);\n        Object.keys(options.cssClasses).forEach(function (key) {\n            removeClass(scope_Target, options.cssClasses[key]);\n        });\n        while (scope_Target.firstChild) {\n            scope_Target.removeChild(scope_Target.firstChild);\n        }\n        delete scope_Target.noUiSlider;\n    }\n    function getNextStepsForHandle(handleNumber) {\n        var location = scope_Locations[handleNumber];\n        var nearbySteps = scope_Spectrum.getNearbySteps(location);\n        var value = scope_Values[handleNumber];\n        var increment = nearbySteps.thisStep.step;\n        var decrement = null;\n        // If snapped, directly use defined step value\n        if (options.snap) {\n            return [\n                value - nearbySteps.stepBefore.startValue || null,\n                nearbySteps.stepAfter.startValue - value || null,\n            ];\n        }\n        // If the next value in this step moves into the next step,\n        // the increment is the start of the next step - the current value\n        if (increment !== false) {\n            if (value + increment > nearbySteps.stepAfter.startValue) {\n                increment = nearbySteps.stepAfter.startValue - value;\n            }\n        }\n        // If the value is beyond the starting point\n        if (value > nearbySteps.thisStep.startValue) {\n            decrement = nearbySteps.thisStep.step;\n        }\n        else if (nearbySteps.stepBefore.step === false) {\n            decrement = false;\n        }\n        // If a handle is at the start of a step, it always steps back into the previous step first\n        else {\n            decrement = value - nearbySteps.stepBefore.highestStep;\n        }\n        // Now, if at the slider edges, there is no in/decrement\n        if (location === 100) {\n            increment = null;\n        }\n        else if (location === 0) {\n            decrement = null;\n        }\n        // As per #391, the comparison for the decrement step can have some rounding issues.\n        var stepDecimals = scope_Spectrum.countStepDecimals();\n        // Round per #391\n        if (increment !== null && increment !== false) {\n            increment = Number(increment.toFixed(stepDecimals));\n        }\n        if (decrement !== null && decrement !== false) {\n            decrement = Number(decrement.toFixed(stepDecimals));\n        }\n        return [decrement, increment];\n    }\n    // Get the current step size for the slider.\n    function getNextSteps() {\n        return scope_HandleNumbers.map(getNextStepsForHandle);\n    }\n    // Updatable: margin, limit, padding, step, range, animate, snap\n    function updateOptions(optionsToUpdate, fireSetEvent) {\n        // Spectrum is created using the range, snap, direction and step options.\n        // \'snap\' and \'step\' can be updated.\n        // If \'snap\' and \'step\' are not passed, they should remain unchanged.\n        var v = valueGet();\n        var updateAble = [\n            "margin",\n            "limit",\n            "padding",\n            "range",\n            "animate",\n            "snap",\n            "step",\n            "format",\n            "pips",\n            "tooltips",\n            "connect",\n        ];\n        // Only change options that we\'re actually passed to update.\n        updateAble.forEach(function (name) {\n            // Check for undefined. null removes the value.\n            if (optionsToUpdate[name] !== undefined) {\n                originalOptions[name] = optionsToUpdate[name];\n            }\n        });\n        var newOptions = testOptions(originalOptions);\n        // Load new options into the slider state\n        updateAble.forEach(function (name) {\n            if (optionsToUpdate[name] !== undefined) {\n                options[name] = newOptions[name];\n            }\n        });\n        scope_Spectrum = newOptions.spectrum;\n        // Limit, margin and padding depend on the spectrum but are stored outside of it. (#677)\n        options.margin = newOptions.margin;\n        options.limit = newOptions.limit;\n        options.padding = newOptions.padding;\n        // Update pips, removes existing.\n        if (options.pips) {\n            pips(options.pips);\n        }\n        else {\n            removePips();\n        }\n        // Update tooltips, removes existing.\n        if (options.tooltips) {\n            tooltips();\n        }\n        else {\n            removeTooltips();\n        }\n        // Invalidate the current positioning so valueSet forces an update.\n        scope_Locations = [];\n        valueSet(isSet(optionsToUpdate.start) ? optionsToUpdate.start : v, fireSetEvent);\n        // Update connects only if it was set\n        if (optionsToUpdate.connect) {\n            updateConnectOption();\n        }\n    }\n    function updateConnectOption() {\n        // IE supported way of removing children including event handlers\n        while (scope_ConnectBase.firstChild) {\n            scope_ConnectBase.removeChild(scope_ConnectBase.firstChild);\n        }\n        // Adding new connects according to the new connect options\n        for (var i = 0; i <= options.handles; i++) {\n            scope_Connects[i] = addConnect(scope_ConnectBase, options.connect[i]);\n            updateConnect(i);\n        }\n        // re-adding drag events for the new connect elements\n        // to ignore the other events we have to negate the \'if (!behaviour.fixed)\' check\n        bindSliderEvents({ drag: options.events.drag, fixed: true });\n    }\n    // Invert options for connect handles\n    function invertConnects() {\n        scope_ConnectsInverted = !scope_ConnectsInverted;\n        testConnect(options, \n        // inverse the connect boolean array\n        options.connect.map(function (b) { return !b; }));\n        updateConnectOption();\n    }\n    // Initialization steps\n    function setupSlider() {\n        // Create the base element, initialize HTML and set classes.\n        // Add handles and connect elements.\n        scope_Base = addSlider(scope_Target);\n        addElements(options.connect, scope_Base);\n        // Attach user events.\n        bindSliderEvents(options.events);\n        // Use the public value method to set the start values.\n        valueSet(options.start);\n        if (options.pips) {\n            pips(options.pips);\n        }\n        if (options.tooltips) {\n            tooltips();\n        }\n        aria();\n    }\n    setupSlider();\n    var scope_Self = {\n        destroy: destroy,\n        steps: getNextSteps,\n        on: bindEvent,\n        off: removeEvent,\n        get: valueGet,\n        set: valueSet,\n        setHandle: valueSetHandle,\n        reset: valueReset,\n        disable: disable,\n        enable: enable,\n        // Exposed for unit testing, don\'t use this in your application.\n        __moveHandles: function (upward, proposal, handleNumbers) {\n            moveHandles(upward, proposal, scope_Locations, handleNumbers);\n        },\n        options: originalOptions,\n        updateOptions: updateOptions,\n        target: scope_Target,\n        removePips: removePips,\n        removeTooltips: removeTooltips,\n        getPositions: function () {\n            return scope_Locations.slice();\n        },\n        getTooltips: function () {\n            return scope_Tooltips;\n        },\n        getOrigins: function () {\n            return scope_Handles;\n        },\n        pips: pips, // Issue #594\n    };\n    return scope_Self;\n}\n// Run the standard initializer\nfunction initialize(target, originalOptions) {\n    if (!target || !target.nodeName) {\n        throw new Error("noUiSlider: create requires a single element, got: " + target);\n    }\n    // Throw an error if the slider was already initialized.\n    if (target.noUiSlider) {\n        throw new Error("noUiSlider: Slider was already initialized.");\n    }\n    // Test the options and create the slider environment;\n    var options = testOptions(originalOptions);\n    var api = scope(target, options, originalOptions);\n    target.noUiSlider = api;\n    return api;\n}\n\n\n/* harmony default export */ __webpack_exports__["default"] = ({\n    // Exposed for unit testing, don\'t use this in your application.\n    __spectrum: Spectrum,\n    // A reference to the default classes, allows global changes.\n    // Use the cssClasses option for changes to one slider.\n    cssClasses: cssClasses,\n    create: initialize,\n});\n\n\n//# sourceURL=webpack://Materialize/./node_modules/nouislider/dist/nouislider.mjs?')}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/nouislider/nouislider.js");return __webpack_exports__}()}));