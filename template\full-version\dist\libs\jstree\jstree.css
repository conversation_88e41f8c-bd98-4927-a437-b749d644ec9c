/* jsTree default theme */
.jstree-node,
.jstree-children,
.jstree-container-ul {
  display: block;
  margin: 0;
  padding: 0;
  list-style-type: none;
  list-style-image: none;
}

.jstree-node {
  white-space: nowrap;
}

.jstree-anchor {
  display: inline-block;
  color: black;
  white-space: nowrap;
  padding: 0 4px 0 1px;
  margin: 0;
  vertical-align: top;
}

.jstree-anchor:focus {
  outline: 0;
}

.jstree-anchor,
.jstree-anchor:link,
.jstree-anchor:visited,
.jstree-anchor:hover,
.jstree-anchor:active {
  text-decoration: none;
  color: inherit;
}

.jstree-icon {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0;
  vertical-align: top;
  text-align: center;
}

.jstree-icon:empty {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0;
  vertical-align: top;
  text-align: center;
}

.jstree-ocl {
  cursor: pointer;
}

.jstree-leaf > .jstree-ocl {
  cursor: default;
}

.jstree .jstree-open > .jstree-children {
  display: block;
}

.jstree .jstree-closed > .jstree-children,
.jstree .jstree-leaf > .jstree-children {
  display: none;
}

.jstree-anchor > .jstree-themeicon {
  margin-right: 2px;
}

.jstree-no-icons .jstree-themeicon,
.jstree-anchor > .jstree-themeicon-hidden {
  display: none;
}

.jstree-hidden,
.jstree-node.jstree-hidden {
  display: none;
}

.jstree-rtl .jstree-anchor {
  padding: 0 1px 0 4px;
}

.jstree-rtl .jstree-anchor > .jstree-themeicon {
  margin-left: 2px;
  margin-right: 0;
}

.jstree-rtl .jstree-node {
  margin-left: 0;
}

.jstree-rtl .jstree-container-ul > .jstree-node {
  margin-right: 0;
}

.jstree-wholerow-ul {
  position: relative;
  display: inline-block;
  min-width: 100%;
}

.jstree-wholerow-ul .jstree-leaf > .jstree-ocl {
  cursor: pointer;
}

.jstree-wholerow-ul .jstree-anchor,
.jstree-wholerow-ul .jstree-icon {
  position: relative;
}

.jstree-wholerow-ul .jstree-wholerow {
  width: 100%;
  cursor: pointer;
  position: absolute;
  left: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.jstree-contextmenu .jstree-anchor {
  -webkit-user-select: none;
  /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none;
  /* disable the IOS popup when long-press on a link */
  user-select: none;
}

.vakata-context {
  display: none;
}

.vakata-context,
.vakata-context ul {
  margin: 0;
  padding: 2px;
  position: absolute;
  background: #f5f5f5;
  border: 1px solid #979797;
  box-shadow: 2px 2px 2px #999999;
}

.vakata-context ul {
  list-style: none;
  left: 100%;
  margin-top: -2.7em;
  margin-left: -4px;
}

.vakata-context .vakata-context-right ul {
  left: auto;
  right: 100%;
  margin-left: auto;
  margin-right: -4px;
}

.vakata-context li {
  list-style: none;
}

.vakata-context li > a {
  display: block;
  padding: 0 2em 0 2em;
  text-decoration: none;
  width: auto;
  color: black;
  white-space: nowrap;
  line-height: 2.4em;
  text-shadow: 1px 1px 0 white;
  border-radius: 1px;
}

.vakata-context li > a:hover {
  position: relative;
  background-color: #e8eff7;
  box-shadow: 0 0 2px #0a6aa1;
}

.vakata-context li > a.vakata-context-parent {
  background-image: url("data:image/gif;base64,R0lGODlhCwAHAIAAACgoKP///yH5BAEAAAEALAAAAAALAAcAAAIORI4JlrqN1oMSnmmZDQUAOw==");
  background-position: right center;
  background-repeat: no-repeat;
}

.vakata-context li > a:focus {
  outline: 0;
}

.vakata-context .vakata-context-no-icons {
  margin-left: 0;
}

.vakata-context .vakata-context-hover > a {
  position: relative;
  background-color: #e8eff7;
  box-shadow: 0 0 2px #0a6aa1;
}

.vakata-context .vakata-context-separator > a,
.vakata-context .vakata-context-separator > a:hover {
  background: white;
  border: 0;
  border-top: 1px solid #e2e3e3;
  height: 1px;
  min-height: 1px;
  max-height: 1px;
  padding: 0;
  margin: 0 0 0 2.4em;
  border-left: 1px solid #e0e0e0;
  text-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  border-radius: 0;
}

.vakata-context .vakata-contextmenu-disabled a,
.vakata-context .vakata-contextmenu-disabled a:hover {
  color: silver;
  background-color: transparent;
  border: 0;
  box-shadow: 0 0 0;
}

.vakata-context .vakata-contextmenu-disabled > a > i {
  filter: grayscale(100%);
}

.vakata-context li > a > i {
  text-decoration: none;
  display: inline-block;
  width: 2.4em;
  height: 2.4em;
  background: transparent;
  margin: 0 0 0 -2em;
  vertical-align: top;
  text-align: center;
  line-height: 2.4em;
}

.vakata-context li > a > i:empty {
  width: 2.4em;
  line-height: 2.4em;
}

.vakata-context li > a .vakata-contextmenu-sep {
  display: inline-block;
  width: 1px;
  height: 2.4em;
  background: white;
  margin: 0 0.5em 0 0;
  border-left: 1px solid #e2e3e3;
}

.vakata-context .vakata-contextmenu-shortcut {
  font-size: 0.8em;
  color: silver;
  opacity: 0.5;
  display: none;
}

.vakata-context-rtl ul {
  left: auto;
  right: 100%;
  margin-left: auto;
  margin-right: -4px;
}

.vakata-context-rtl li > a.vakata-context-parent {
  background-image: url("data:image/gif;base64,R0lGODlhCwAHAIAAACgoKP///yH5BAEAAAEALAAAAAALAAcAAAINjI+AC7rWHIsPtmoxLAA7");
  background-position: left center;
  background-repeat: no-repeat;
}

.vakata-context-rtl .vakata-context-separator > a {
  margin: 0 2.4em 0 0;
  border-left: 0;
  border-right: 1px solid #e2e3e3;
}

.vakata-context-rtl .vakata-context-left ul {
  right: auto;
  left: 100%;
  margin-left: -4px;
  margin-right: auto;
}

.vakata-context-rtl li > a > i {
  margin: 0 -2em 0 0;
}

.vakata-context-rtl li > a .vakata-contextmenu-sep {
  margin: 0 0 0 0.5em;
  border-left-color: white;
  background: #e2e3e3;
}

#jstree-marker {
  position: absolute;
  top: 0;
  left: 0;
  margin: -5px 0 0 0;
  padding: 0;
  border-right: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid;
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
}

#jstree-dnd {
  line-height: 16px;
  margin: 0;
  padding: 4px;
}

#jstree-dnd .jstree-icon,
#jstree-dnd .jstree-copy {
  display: inline-block;
  text-decoration: none;
  margin: 0 2px 0 0;
  padding: 0;
  width: 16px;
  height: 16px;
}

#jstree-dnd .jstree-ok {
  background: green;
}

#jstree-dnd .jstree-er {
  background: red;
}

#jstree-dnd .jstree-copy {
  margin: 0 2px 0 2px;
}

.jstree-default .jstree-node,
.jstree-default .jstree-icon {
  background-repeat: no-repeat;
  background-color: transparent;
}

.jstree-default .jstree-anchor,
.jstree-default .jstree-animated,
.jstree-default .jstree-wholerow {
  transition: background-color 0.15s, box-shadow 0.15s;
}

.jstree-default .jstree-hovered {
  background: #e7f4f9;
  border-radius: 2px;
  box-shadow: inset 0 0 1px #cccccc;
}

.jstree-default .jstree-context {
  background: #e7f4f9;
  border-radius: 2px;
  box-shadow: inset 0 0 1px #cccccc;
}

.jstree-default .jstree-clicked {
  background: #beebff;
  border-radius: 2px;
  box-shadow: inset 0 0 1px #999999;
}

.jstree-default .jstree-no-icons .jstree-anchor > .jstree-themeicon {
  display: none;
}

.jstree-default .jstree-disabled {
  background: transparent;
  color: #666666;
}

.jstree-default .jstree-disabled.jstree-hovered {
  background: transparent;
  box-shadow: none;
}

.jstree-default .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default .jstree-disabled > .jstree-icon {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default .jstree-search {
  font-style: italic;
  color: #8b0000;
  font-weight: bold;
}

.jstree-default .jstree-no-checkboxes .jstree-checkbox {
  display: none !important;
}

.jstree-default.jstree-checkbox-no-clicked .jstree-clicked {
  background: transparent;
  box-shadow: none;
}

.jstree-default.jstree-checkbox-no-clicked .jstree-clicked.jstree-hovered {
  background: #e7f4f9;
}

.jstree-default.jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked {
  background: transparent;
}

.jstree-default.jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked.jstree-wholerow-hovered {
  background: #e7f4f9;
}

.jstree-default > .jstree-striped {
  min-width: 100%;
  display: inline-block;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAkCAMAAAB/qqA+AAAABlBMVEUAAAAAAAClZ7nPAAAAAnRSTlMNAMM9s3UAAAAXSURBVHjajcEBAQAAAIKg/H/aCQZ70AUBjAATb6YPDgAAAABJRU5ErkJggg==") left top repeat;
}

.jstree-default > .jstree-wholerow-ul .jstree-hovered,
.jstree-default > .jstree-wholerow-ul .jstree-clicked {
  background: transparent;
  box-shadow: none;
  border-radius: 0;
}

.jstree-default .jstree-wholerow {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.jstree-default .jstree-wholerow-hovered {
  background: #e7f4f9;
}

.jstree-default .jstree-wholerow-clicked {
  background: #beebff;
  background: -webkit-linear-gradient(top, #beebff 0%, #a8e4ff 100%);
  background: linear-gradient(to bottom, #beebff 0%, #a8e4ff 100%);
}

.jstree-default .jstree-node {
  min-height: 24px;
  line-height: 24px;
  margin-left: 24px;
  min-width: 24px;
}

.jstree-default .jstree-anchor {
  line-height: 24px;
  height: 24px;
}

.jstree-default .jstree-icon {
  width: 24px;
  height: 24px;
  line-height: 24px;
}

.jstree-default .jstree-icon:empty {
  width: 24px;
  height: 24px;
  line-height: 24px;
}

.jstree-default.jstree-rtl .jstree-node {
  margin-right: 24px;
}

.jstree-default .jstree-wholerow {
  height: 24px;
}

.jstree-default .jstree-node,
.jstree-default .jstree-icon {
  background-image: url("32px.png");
}

.jstree-default .jstree-node {
  background-position: -292px -4px;
  background-repeat: repeat-y;
}

.jstree-default .jstree-last {
  background-image: none;
}

.jstree-default .jstree-open > .jstree-ocl {
  background-position: -132px -4px;
}

.jstree-default .jstree-closed > .jstree-ocl {
  background-position: -100px -4px;
}

.jstree-default .jstree-leaf > .jstree-ocl {
  background-position: -68px -4px;
}

.jstree-default .jstree-themeicon {
  background-position: -260px -4px;
}

.jstree-default > .jstree-no-dots .jstree-node,
.jstree-default > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -36px -4px;
}

.jstree-default > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -4px -4px;
}

.jstree-default .jstree-disabled {
  background: transparent;
}

.jstree-default .jstree-disabled.jstree-hovered {
  background: transparent;
}

.jstree-default .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default .jstree-checkbox {
  background-position: -164px -4px;
}

.jstree-default .jstree-checkbox:hover {
  background-position: -164px -36px;
}

.jstree-default.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
.jstree-default .jstree-checked > .jstree-checkbox {
  background-position: -228px -4px;
}

.jstree-default.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
.jstree-default .jstree-checked > .jstree-checkbox:hover {
  background-position: -228px -36px;
}

.jstree-default .jstree-anchor > .jstree-undetermined {
  background-position: -196px -4px;
}

.jstree-default .jstree-anchor > .jstree-undetermined:hover {
  background-position: -196px -36px;
}

.jstree-default .jstree-checkbox-disabled {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default > .jstree-striped {
  background-size: auto 48px;
}

.jstree-default.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
  background-position: 100% 1px;
  background-repeat: repeat-y;
}

.jstree-default.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default.jstree-rtl .jstree-open > .jstree-ocl {
  background-position: -132px -36px;
}

.jstree-default.jstree-rtl .jstree-closed > .jstree-ocl {
  background-position: -100px -36px;
}

.jstree-default.jstree-rtl .jstree-leaf > .jstree-ocl {
  background-position: -68px -36px;
}

.jstree-default.jstree-rtl > .jstree-no-dots .jstree-node,
.jstree-default.jstree-rtl > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default.jstree-rtl > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -36px -36px;
}

.jstree-default.jstree-rtl > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -4px -36px;
}

.jstree-default .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0;
}

.jstree-default > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("throbber.gif") center center no-repeat;
}

.jstree-default .jstree-file {
  background: url("32px.png") -100px -68px no-repeat;
}

.jstree-default .jstree-folder {
  background: url("32px.png") -260px -4px no-repeat;
}

.jstree-default > .jstree-container-ul > .jstree-node {
  margin-left: 0;
  margin-right: 0;
}

#jstree-dnd.jstree-default {
  line-height: 24px;
  padding: 0 4px;
}

#jstree-dnd.jstree-default .jstree-ok,
#jstree-dnd.jstree-default .jstree-er {
  background-image: url("32px.png");
  background-repeat: no-repeat;
  background-color: transparent;
}

#jstree-dnd.jstree-default i {
  background: transparent;
  width: 24px;
  height: 24px;
  line-height: 24px;
}

#jstree-dnd.jstree-default .jstree-ok {
  background-position: -4px -68px;
}

#jstree-dnd.jstree-default .jstree-er {
  background-position: -36px -68px;
}

.jstree-default .jstree-ellipsis {
  overflow: hidden;
}

.jstree-default .jstree-ellipsis .jstree-anchor {
  width: calc(100% - 24px + 5px);
  text-overflow: ellipsis;
  overflow: hidden;
}

.jstree-default.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
}

.jstree-default.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-small .jstree-node {
  min-height: 18px;
  line-height: 18px;
  margin-left: 18px;
  min-width: 18px;
}

.jstree-default-small .jstree-anchor {
  line-height: 18px;
  height: 18px;
}

.jstree-default-small .jstree-icon {
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.jstree-default-small .jstree-icon:empty {
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.jstree-default-small.jstree-rtl .jstree-node {
  margin-right: 18px;
}

.jstree-default-small .jstree-wholerow {
  height: 18px;
}

.jstree-default-small .jstree-node,
.jstree-default-small .jstree-icon {
  background-image: url("32px.png");
}

.jstree-default-small .jstree-node {
  background-position: -295px -7px;
  background-repeat: repeat-y;
}

.jstree-default-small .jstree-last {
  background-image: none;
}

.jstree-default-small .jstree-open > .jstree-ocl {
  background-position: -135px -7px;
}

.jstree-default-small .jstree-closed > .jstree-ocl {
  background-position: -103px -7px;
}

.jstree-default-small .jstree-leaf > .jstree-ocl {
  background-position: -71px -7px;
}

.jstree-default-small .jstree-themeicon {
  background-position: -263px -7px;
}

.jstree-default-small > .jstree-no-dots .jstree-node,
.jstree-default-small > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-small > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -39px -7px;
}

.jstree-default-small > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -7px -7px;
}

.jstree-default-small .jstree-disabled {
  background: transparent;
}

.jstree-default-small .jstree-disabled.jstree-hovered {
  background: transparent;
}

.jstree-default-small .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default-small .jstree-checkbox {
  background-position: -167px -7px;
}

.jstree-default-small .jstree-checkbox:hover {
  background-position: -167px -39px;
}

.jstree-default-small.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
.jstree-default-small .jstree-checked > .jstree-checkbox {
  background-position: -231px -7px;
}

.jstree-default-small.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
.jstree-default-small .jstree-checked > .jstree-checkbox:hover {
  background-position: -231px -39px;
}

.jstree-default-small .jstree-anchor > .jstree-undetermined {
  background-position: -199px -7px;
}

.jstree-default-small .jstree-anchor > .jstree-undetermined:hover {
  background-position: -199px -39px;
}

.jstree-default-small .jstree-checkbox-disabled {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default-small > .jstree-striped {
  background-size: auto 36px;
}

.jstree-default-small.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
  background-position: 100% 1px;
  background-repeat: repeat-y;
}

.jstree-default-small.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-small.jstree-rtl .jstree-open > .jstree-ocl {
  background-position: -135px -39px;
}

.jstree-default-small.jstree-rtl .jstree-closed > .jstree-ocl {
  background-position: -103px -39px;
}

.jstree-default-small.jstree-rtl .jstree-leaf > .jstree-ocl {
  background-position: -71px -39px;
}

.jstree-default-small.jstree-rtl > .jstree-no-dots .jstree-node,
.jstree-default-small.jstree-rtl > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-small.jstree-rtl > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -39px -39px;
}

.jstree-default-small.jstree-rtl > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -7px -39px;
}

.jstree-default-small .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0;
}

.jstree-default-small > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("throbber.gif") center center no-repeat;
}

.jstree-default-small .jstree-file {
  background: url("32px.png") -103px -71px no-repeat;
}

.jstree-default-small .jstree-folder {
  background: url("32px.png") -263px -7px no-repeat;
}

.jstree-default-small > .jstree-container-ul > .jstree-node {
  margin-left: 0;
  margin-right: 0;
}

#jstree-dnd.jstree-default-small {
  line-height: 18px;
  padding: 0 4px;
}

#jstree-dnd.jstree-default-small .jstree-ok,
#jstree-dnd.jstree-default-small .jstree-er {
  background-image: url("32px.png");
  background-repeat: no-repeat;
  background-color: transparent;
}

#jstree-dnd.jstree-default-small i {
  background: transparent;
  width: 18px;
  height: 18px;
  line-height: 18px;
}

#jstree-dnd.jstree-default-small .jstree-ok {
  background-position: -7px -71px;
}

#jstree-dnd.jstree-default-small .jstree-er {
  background-position: -39px -71px;
}

.jstree-default-small .jstree-ellipsis {
  overflow: hidden;
}

.jstree-default-small .jstree-ellipsis .jstree-anchor {
  width: calc(100% - 18px + 5px);
  text-overflow: ellipsis;
  overflow: hidden;
}

.jstree-default-small.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAACAQMAAABv1h6PAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMHBgAAiABBI4gz9AAAAABJRU5ErkJggg==");
}

.jstree-default-small.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-large .jstree-node {
  min-height: 32px;
  line-height: 32px;
  margin-left: 32px;
  min-width: 32px;
}

.jstree-default-large .jstree-anchor {
  line-height: 32px;
  height: 32px;
}

.jstree-default-large .jstree-icon {
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.jstree-default-large .jstree-icon:empty {
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.jstree-default-large.jstree-rtl .jstree-node {
  margin-right: 32px;
}

.jstree-default-large .jstree-wholerow {
  height: 32px;
}

.jstree-default-large .jstree-node,
.jstree-default-large .jstree-icon {
  background-image: url("32px.png");
}

.jstree-default-large .jstree-node {
  background-position: -288px 0px;
  background-repeat: repeat-y;
}

.jstree-default-large .jstree-last {
  background-image: none;
}

.jstree-default-large .jstree-open > .jstree-ocl {
  background-position: -128px 0px;
}

.jstree-default-large .jstree-closed > .jstree-ocl {
  background-position: -96px 0px;
}

.jstree-default-large .jstree-leaf > .jstree-ocl {
  background-position: -64px 0px;
}

.jstree-default-large .jstree-themeicon {
  background-position: -256px 0px;
}

.jstree-default-large > .jstree-no-dots .jstree-node,
.jstree-default-large > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-large > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -32px 0px;
}

.jstree-default-large > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: 0px 0px;
}

.jstree-default-large .jstree-disabled {
  background: transparent;
}

.jstree-default-large .jstree-disabled.jstree-hovered {
  background: transparent;
}

.jstree-default-large .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default-large .jstree-checkbox {
  background-position: -160px 0px;
}

.jstree-default-large .jstree-checkbox:hover {
  background-position: -160px -32px;
}

.jstree-default-large.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
.jstree-default-large .jstree-checked > .jstree-checkbox {
  background-position: -224px 0px;
}

.jstree-default-large.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
.jstree-default-large .jstree-checked > .jstree-checkbox:hover {
  background-position: -224px -32px;
}

.jstree-default-large .jstree-anchor > .jstree-undetermined {
  background-position: -192px 0px;
}

.jstree-default-large .jstree-anchor > .jstree-undetermined:hover {
  background-position: -192px -32px;
}

.jstree-default-large .jstree-checkbox-disabled {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default-large > .jstree-striped {
  background-size: auto 64px;
}

.jstree-default-large.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
  background-position: 100% 1px;
  background-repeat: repeat-y;
}

.jstree-default-large.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-large.jstree-rtl .jstree-open > .jstree-ocl {
  background-position: -128px -32px;
}

.jstree-default-large.jstree-rtl .jstree-closed > .jstree-ocl {
  background-position: -96px -32px;
}

.jstree-default-large.jstree-rtl .jstree-leaf > .jstree-ocl {
  background-position: -64px -32px;
}

.jstree-default-large.jstree-rtl > .jstree-no-dots .jstree-node,
.jstree-default-large.jstree-rtl > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-large.jstree-rtl > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -32px -32px;
}

.jstree-default-large.jstree-rtl > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: 0px -32px;
}

.jstree-default-large .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0;
}

.jstree-default-large > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("throbber.gif") center center no-repeat;
}

.jstree-default-large .jstree-file {
  background: url("32px.png") -96px -64px no-repeat;
}

.jstree-default-large .jstree-folder {
  background: url("32px.png") -256px 0px no-repeat;
}

.jstree-default-large > .jstree-container-ul > .jstree-node {
  margin-left: 0;
  margin-right: 0;
}

#jstree-dnd.jstree-default-large {
  line-height: 32px;
  padding: 0 4px;
}

#jstree-dnd.jstree-default-large .jstree-ok,
#jstree-dnd.jstree-default-large .jstree-er {
  background-image: url("32px.png");
  background-repeat: no-repeat;
  background-color: transparent;
}

#jstree-dnd.jstree-default-large i {
  background: transparent;
  width: 32px;
  height: 32px;
  line-height: 32px;
}

#jstree-dnd.jstree-default-large .jstree-ok {
  background-position: 0px -64px;
}

#jstree-dnd.jstree-default-large .jstree-er {
  background-position: -32px -64px;
}

.jstree-default-large .jstree-ellipsis {
  overflow: hidden;
}

.jstree-default-large .jstree-ellipsis .jstree-anchor {
  width: calc(100% - 32px + 5px);
  text-overflow: ellipsis;
  overflow: hidden;
}

.jstree-default-large.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAACAQMAAAAD0EyKAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjgIIGBgABCgCBvVLXcAAAAABJRU5ErkJggg==");
}

.jstree-default-large.jstree-rtl .jstree-last {
  background-image: none;
}

@media (max-width: 768px) {
  #jstree-dnd.jstree-dnd-responsive {
    line-height: 40px;
    font-weight: bold;
    font-size: 1.1em;
    text-shadow: 1px 1px white;
  }
  #jstree-dnd.jstree-dnd-responsive > i {
    background: transparent;
    width: 40px;
    height: 40px;
  }
  #jstree-dnd.jstree-dnd-responsive > .jstree-ok {
    background-image: url("40px.png");
    background-position: 0 -200px;
    background-size: 120px 240px;
  }
  #jstree-dnd.jstree-dnd-responsive > .jstree-er {
    background-image: url("40px.png");
    background-position: -40px -200px;
    background-size: 120px 240px;
  }
  #jstree-marker.jstree-dnd-responsive {
    border-left-width: 10px;
    border-top-width: 10px;
    border-bottom-width: 10px;
    margin-top: -10px;
  }
}
@media (max-width: 768px) {
  .jstree-default-responsive {
    /*
    .jstree-open > .jstree-ocl,
    .jstree-closed > .jstree-ocl { border-radius:20px; background-color:white; }
    */
  }
  .jstree-default-responsive .jstree-icon {
    background-image: url("40px.png");
  }
  .jstree-default-responsive .jstree-node,
  .jstree-default-responsive .jstree-leaf > .jstree-ocl {
    background: transparent;
  }
  .jstree-default-responsive .jstree-node {
    min-height: 40px;
    line-height: 40px;
    margin-left: 40px;
    min-width: 40px;
    white-space: nowrap;
  }
  .jstree-default-responsive .jstree-anchor {
    line-height: 40px;
    height: 40px;
  }
  .jstree-default-responsive .jstree-icon,
  .jstree-default-responsive .jstree-icon:empty {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .jstree-default-responsive > .jstree-container-ul > .jstree-node {
    margin-left: 0;
  }
  .jstree-default-responsive.jstree-rtl .jstree-node {
    margin-left: 0;
    margin-right: 40px;
    background: transparent;
  }
  .jstree-default-responsive.jstree-rtl .jstree-container-ul > .jstree-node {
    margin-right: 0;
  }
  .jstree-default-responsive .jstree-ocl,
  .jstree-default-responsive .jstree-themeicon,
  .jstree-default-responsive .jstree-checkbox {
    background-size: 120px 240px;
  }
  .jstree-default-responsive .jstree-leaf > .jstree-ocl,
  .jstree-default-responsive.jstree-rtl .jstree-leaf > .jstree-ocl {
    background: transparent;
  }
  .jstree-default-responsive .jstree-open > .jstree-ocl {
    background-position: 0 0 !important;
  }
  .jstree-default-responsive .jstree-closed > .jstree-ocl {
    background-position: 0 -40px !important;
  }
  .jstree-default-responsive.jstree-rtl .jstree-closed > .jstree-ocl {
    background-position: -40px 0 !important;
  }
  .jstree-default-responsive .jstree-themeicon {
    background-position: -40px -40px;
  }
  .jstree-default-responsive .jstree-checkbox,
  .jstree-default-responsive .jstree-checkbox:hover {
    background-position: -40px -80px;
  }
  .jstree-default-responsive.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
  .jstree-default-responsive.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
  .jstree-default-responsive .jstree-checked > .jstree-checkbox,
  .jstree-default-responsive .jstree-checked > .jstree-checkbox:hover {
    background-position: 0 -80px;
  }
  .jstree-default-responsive .jstree-anchor > .jstree-undetermined,
  .jstree-default-responsive .jstree-anchor > .jstree-undetermined:hover {
    background-position: 0 -120px;
  }
  .jstree-default-responsive .jstree-anchor {
    font-weight: bold;
    font-size: 1.1em;
    text-shadow: 1px 1px white;
  }
  .jstree-default-responsive > .jstree-striped {
    background: transparent;
  }
  .jstree-default-responsive .jstree-wholerow {
    border-top: 1px solid rgba(255, 255, 255, 0.7);
    border-bottom: 1px solid rgba(64, 64, 64, 0.2);
    background: #ebebeb;
    height: 40px;
  }
  .jstree-default-responsive .jstree-wholerow-hovered {
    background: #e7f4f9;
  }
  .jstree-default-responsive .jstree-wholerow-clicked {
    background: #beebff;
  }
  .jstree-default-responsive .jstree-children .jstree-last > .jstree-wholerow {
    box-shadow: inset 0 -6px 3px -5px #666666;
  }
  .jstree-default-responsive .jstree-children .jstree-open > .jstree-wholerow {
    box-shadow: inset 0 6px 3px -5px #666666;
    border-top: 0;
  }
  .jstree-default-responsive .jstree-children .jstree-open + .jstree-open {
    box-shadow: none;
  }
  .jstree-default-responsive .jstree-node,
  .jstree-default-responsive .jstree-icon,
  .jstree-default-responsive .jstree-node > .jstree-ocl,
  .jstree-default-responsive .jstree-themeicon,
  .jstree-default-responsive .jstree-checkbox {
    background-image: url("40px.png");
    background-size: 120px 240px;
  }
  .jstree-default-responsive .jstree-node {
    background-position: -80px 0;
    background-repeat: repeat-y;
  }
  .jstree-default-responsive .jstree-last {
    background-image: none;
  }
  .jstree-default-responsive .jstree-leaf > .jstree-ocl {
    background-position: -40px -120px;
  }
  .jstree-default-responsive .jstree-last > .jstree-ocl {
    background-position: -40px -160px;
  }
  .jstree-default-responsive .jstree-themeicon-custom {
    background-color: transparent;
    background-image: none;
    background-position: 0 0;
  }
  .jstree-default-responsive .jstree-file {
    background: url("40px.png") 0 -160px no-repeat;
    background-size: 120px 240px;
  }
  .jstree-default-responsive .jstree-folder {
    background: url("40px.png") -40px -40px no-repeat;
    background-size: 120px 240px;
  }
  .jstree-default-responsive > .jstree-container-ul > .jstree-node {
    margin-left: 0;
    margin-right: 0;
  }
}
/* jsTree default dark theme */
.jstree-node,
.jstree-children,
.jstree-container-ul {
  display: block;
  margin: 0;
  padding: 0;
  list-style-type: none;
  list-style-image: none;
}

.jstree-node {
  white-space: nowrap;
}

.jstree-anchor {
  display: inline-block;
  color: black;
  white-space: nowrap;
  padding: 0 4px 0 1px;
  margin: 0;
  vertical-align: top;
}

.jstree-anchor:focus {
  outline: 0;
}

.jstree-anchor,
.jstree-anchor:link,
.jstree-anchor:visited,
.jstree-anchor:hover,
.jstree-anchor:active {
  text-decoration: none;
  color: inherit;
}

.jstree-icon {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0;
  vertical-align: top;
  text-align: center;
}

.jstree-icon:empty {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0;
  vertical-align: top;
  text-align: center;
}

.jstree-ocl {
  cursor: pointer;
}

.jstree-leaf > .jstree-ocl {
  cursor: default;
}

.jstree .jstree-open > .jstree-children {
  display: block;
}

.jstree .jstree-closed > .jstree-children,
.jstree .jstree-leaf > .jstree-children {
  display: none;
}

.jstree-anchor > .jstree-themeicon {
  margin-right: 2px;
}

.jstree-no-icons .jstree-themeicon,
.jstree-anchor > .jstree-themeicon-hidden {
  display: none;
}

.jstree-hidden,
.jstree-node.jstree-hidden {
  display: none;
}

.jstree-rtl .jstree-anchor {
  padding: 0 1px 0 4px;
}

.jstree-rtl .jstree-anchor > .jstree-themeicon {
  margin-left: 2px;
  margin-right: 0;
}

.jstree-rtl .jstree-node {
  margin-left: 0;
}

.jstree-rtl .jstree-container-ul > .jstree-node {
  margin-right: 0;
}

.jstree-wholerow-ul {
  position: relative;
  display: inline-block;
  min-width: 100%;
}

.jstree-wholerow-ul .jstree-leaf > .jstree-ocl {
  cursor: pointer;
}

.jstree-wholerow-ul .jstree-anchor,
.jstree-wholerow-ul .jstree-icon {
  position: relative;
}

.jstree-wholerow-ul .jstree-wholerow {
  width: 100%;
  cursor: pointer;
  position: absolute;
  left: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.jstree-contextmenu .jstree-anchor {
  -webkit-user-select: none;
  /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none;
  /* disable the IOS popup when long-press on a link */
  user-select: none;
}

.vakata-context {
  display: none;
}

.vakata-context,
.vakata-context ul {
  margin: 0;
  padding: 2px;
  position: absolute;
  background: #f5f5f5;
  border: 1px solid #979797;
  box-shadow: 2px 2px 2px #999999;
}

.vakata-context ul {
  list-style: none;
  left: 100%;
  margin-top: -2.7em;
  margin-left: -4px;
}

.vakata-context .vakata-context-right ul {
  left: auto;
  right: 100%;
  margin-left: auto;
  margin-right: -4px;
}

.vakata-context li {
  list-style: none;
}

.vakata-context li > a {
  display: block;
  padding: 0 2em 0 2em;
  text-decoration: none;
  width: auto;
  color: black;
  white-space: nowrap;
  line-height: 2.4em;
  text-shadow: 1px 1px 0 white;
  border-radius: 1px;
}

.vakata-context li > a:hover {
  position: relative;
  background-color: #e8eff7;
  box-shadow: 0 0 2px #0a6aa1;
}

.vakata-context li > a.vakata-context-parent {
  background-image: url("data:image/gif;base64,R0lGODlhCwAHAIAAACgoKP///yH5BAEAAAEALAAAAAALAAcAAAIORI4JlrqN1oMSnmmZDQUAOw==");
  background-position: right center;
  background-repeat: no-repeat;
}

.vakata-context li > a:focus {
  outline: 0;
}

.vakata-context .vakata-context-no-icons {
  margin-left: 0;
}

.vakata-context .vakata-context-hover > a {
  position: relative;
  background-color: #e8eff7;
  box-shadow: 0 0 2px #0a6aa1;
}

.vakata-context .vakata-context-separator > a,
.vakata-context .vakata-context-separator > a:hover {
  background: white;
  border: 0;
  border-top: 1px solid #e2e3e3;
  height: 1px;
  min-height: 1px;
  max-height: 1px;
  padding: 0;
  margin: 0 0 0 2.4em;
  border-left: 1px solid #e0e0e0;
  text-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  border-radius: 0;
}

.vakata-context .vakata-contextmenu-disabled a,
.vakata-context .vakata-contextmenu-disabled a:hover {
  color: silver;
  background-color: transparent;
  border: 0;
  box-shadow: 0 0 0;
}

.vakata-context .vakata-contextmenu-disabled > a > i {
  filter: grayscale(100%);
}

.vakata-context li > a > i {
  text-decoration: none;
  display: inline-block;
  width: 2.4em;
  height: 2.4em;
  background: transparent;
  margin: 0 0 0 -2em;
  vertical-align: top;
  text-align: center;
  line-height: 2.4em;
}

.vakata-context li > a > i:empty {
  width: 2.4em;
  line-height: 2.4em;
}

.vakata-context li > a .vakata-contextmenu-sep {
  display: inline-block;
  width: 1px;
  height: 2.4em;
  background: white;
  margin: 0 0.5em 0 0;
  border-left: 1px solid #e2e3e3;
}

.vakata-context .vakata-contextmenu-shortcut {
  font-size: 0.8em;
  color: silver;
  opacity: 0.5;
  display: none;
}

.vakata-context-rtl ul {
  left: auto;
  right: 100%;
  margin-left: auto;
  margin-right: -4px;
}

.vakata-context-rtl li > a.vakata-context-parent {
  background-image: url("data:image/gif;base64,R0lGODlhCwAHAIAAACgoKP///yH5BAEAAAEALAAAAAALAAcAAAINjI+AC7rWHIsPtmoxLAA7");
  background-position: left center;
  background-repeat: no-repeat;
}

.vakata-context-rtl .vakata-context-separator > a {
  margin: 0 2.4em 0 0;
  border-left: 0;
  border-right: 1px solid #e2e3e3;
}

.vakata-context-rtl .vakata-context-left ul {
  right: auto;
  left: 100%;
  margin-left: -4px;
  margin-right: auto;
}

.vakata-context-rtl li > a > i {
  margin: 0 -2em 0 0;
}

.vakata-context-rtl li > a .vakata-contextmenu-sep {
  margin: 0 0 0 0.5em;
  border-left-color: white;
  background: #e2e3e3;
}

#jstree-marker {
  position: absolute;
  top: 0;
  left: 0;
  margin: -5px 0 0 0;
  padding: 0;
  border-right: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid;
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
}

#jstree-dnd {
  line-height: 16px;
  margin: 0;
  padding: 4px;
}

#jstree-dnd .jstree-icon,
#jstree-dnd .jstree-copy {
  display: inline-block;
  text-decoration: none;
  margin: 0 2px 0 0;
  padding: 0;
  width: 16px;
  height: 16px;
}

#jstree-dnd .jstree-ok {
  background: green;
}

#jstree-dnd .jstree-er {
  background: red;
}

#jstree-dnd .jstree-copy {
  margin: 0 2px 0 2px;
}

.jstree-default-dark .jstree-node,
.jstree-default-dark .jstree-icon {
  background-repeat: no-repeat;
  background-color: transparent;
}

.jstree-default-dark .jstree-anchor,
.jstree-default-dark .jstree-animated,
.jstree-default-dark .jstree-wholerow {
  transition: background-color 0.15s, box-shadow 0.15s;
}

.jstree-default-dark .jstree-hovered {
  background: #555;
  border-radius: 2px;
  box-shadow: inset 0 0 1px #555;
}

.jstree-default-dark .jstree-context {
  background: #555;
  border-radius: 2px;
  box-shadow: inset 0 0 1px #555;
}

.jstree-default-dark .jstree-clicked {
  background: #5fa2db;
  border-radius: 2px;
  box-shadow: inset 0 0 1px #666666;
}

.jstree-default-dark .jstree-no-icons .jstree-anchor > .jstree-themeicon {
  display: none;
}

.jstree-default-dark .jstree-disabled {
  background: transparent;
  color: #666666;
}

.jstree-default-dark .jstree-disabled.jstree-hovered {
  background: transparent;
  box-shadow: none;
}

.jstree-default-dark .jstree-disabled.jstree-clicked {
  background: #333333;
}

.jstree-default-dark .jstree-disabled > .jstree-icon {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default-dark .jstree-search {
  font-style: italic;
  color: #ffffff;
  font-weight: bold;
}

.jstree-default-dark .jstree-no-checkboxes .jstree-checkbox {
  display: none !important;
}

.jstree-default-dark.jstree-checkbox-no-clicked .jstree-clicked {
  background: transparent;
  box-shadow: none;
}

.jstree-default-dark.jstree-checkbox-no-clicked .jstree-clicked.jstree-hovered {
  background: #555;
}

.jstree-default-dark.jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked {
  background: transparent;
}

.jstree-default-dark.jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked.jstree-wholerow-hovered {
  background: #555;
}

.jstree-default-dark > .jstree-striped {
  min-width: 100%;
  display: inline-block;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAkCAMAAAB/qqA+AAAABlBMVEUAAAAAAAClZ7nPAAAAAnRSTlMNAMM9s3UAAAAXSURBVHjajcEBAQAAAIKg/H/aCQZ70AUBjAATb6YPDgAAAABJRU5ErkJggg==") left top repeat;
}

.jstree-default-dark > .jstree-wholerow-ul .jstree-hovered,
.jstree-default-dark > .jstree-wholerow-ul .jstree-clicked {
  background: transparent;
  box-shadow: none;
  border-radius: 0;
}

.jstree-default-dark .jstree-wholerow {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.jstree-default-dark .jstree-wholerow-hovered {
  background: #555;
}

.jstree-default-dark .jstree-wholerow-clicked {
  background: #5fa2db;
  background: -webkit-linear-gradient(top, #5fa2db 0%, #5fa2db 100%);
  background: linear-gradient(to bottom, #5fa2db 0%, #5fa2db 100%);
}

.jstree-default-dark .jstree-node {
  min-height: 24px;
  line-height: 24px;
  margin-left: 24px;
  min-width: 24px;
}

.jstree-default-dark .jstree-anchor {
  line-height: 24px;
  height: 24px;
}

.jstree-default-dark .jstree-icon {
  width: 24px;
  height: 24px;
  line-height: 24px;
}

.jstree-default-dark .jstree-icon:empty {
  width: 24px;
  height: 24px;
  line-height: 24px;
}

.jstree-default-dark.jstree-rtl .jstree-node {
  margin-right: 24px;
}

.jstree-default-dark .jstree-wholerow {
  height: 24px;
}

.jstree-default-dark .jstree-node,
.jstree-default-dark .jstree-icon {
  background-image: url("32px.png");
}

.jstree-default-dark .jstree-node {
  background-position: -292px -4px;
  background-repeat: repeat-y;
}

.jstree-default-dark .jstree-last {
  background-image: none;
}

.jstree-default-dark .jstree-open > .jstree-ocl {
  background-position: -132px -4px;
}

.jstree-default-dark .jstree-closed > .jstree-ocl {
  background-position: -100px -4px;
}

.jstree-default-dark .jstree-leaf > .jstree-ocl {
  background-position: -68px -4px;
}

.jstree-default-dark .jstree-themeicon {
  background-position: -260px -4px;
}

.jstree-default-dark > .jstree-no-dots .jstree-node,
.jstree-default-dark > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-dark > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -36px -4px;
}

.jstree-default-dark > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -4px -4px;
}

.jstree-default-dark .jstree-disabled {
  background: transparent;
}

.jstree-default-dark .jstree-disabled.jstree-hovered {
  background: transparent;
}

.jstree-default-dark .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default-dark .jstree-checkbox {
  background-position: -164px -4px;
}

.jstree-default-dark .jstree-checkbox:hover {
  background-position: -164px -36px;
}

.jstree-default-dark.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
.jstree-default-dark .jstree-checked > .jstree-checkbox {
  background-position: -228px -4px;
}

.jstree-default-dark.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
.jstree-default-dark .jstree-checked > .jstree-checkbox:hover {
  background-position: -228px -36px;
}

.jstree-default-dark .jstree-anchor > .jstree-undetermined {
  background-position: -196px -4px;
}

.jstree-default-dark .jstree-anchor > .jstree-undetermined:hover {
  background-position: -196px -36px;
}

.jstree-default-dark .jstree-checkbox-disabled {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default-dark > .jstree-striped {
  background-size: auto 48px;
}

.jstree-default-dark.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
  background-position: 100% 1px;
  background-repeat: repeat-y;
}

.jstree-default-dark.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark.jstree-rtl .jstree-open > .jstree-ocl {
  background-position: -132px -36px;
}

.jstree-default-dark.jstree-rtl .jstree-closed > .jstree-ocl {
  background-position: -100px -36px;
}

.jstree-default-dark.jstree-rtl .jstree-leaf > .jstree-ocl {
  background-position: -68px -36px;
}

.jstree-default-dark.jstree-rtl > .jstree-no-dots .jstree-node,
.jstree-default-dark.jstree-rtl > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-dark.jstree-rtl > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -36px -36px;
}

.jstree-default-dark.jstree-rtl > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -4px -36px;
}

.jstree-default-dark .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0;
}

.jstree-default-dark > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("throbber.gif") center center no-repeat;
}

.jstree-default-dark .jstree-file {
  background: url("32px.png") -100px -68px no-repeat;
}

.jstree-default-dark .jstree-folder {
  background: url("32px.png") -260px -4px no-repeat;
}

.jstree-default-dark > .jstree-container-ul > .jstree-node {
  margin-left: 0;
  margin-right: 0;
}

#jstree-dnd.jstree-default-dark {
  line-height: 24px;
  padding: 0 4px;
}

#jstree-dnd.jstree-default-dark .jstree-ok,
#jstree-dnd.jstree-default-dark .jstree-er {
  background-image: url("32px.png");
  background-repeat: no-repeat;
  background-color: transparent;
}

#jstree-dnd.jstree-default-dark i {
  background: transparent;
  width: 24px;
  height: 24px;
  line-height: 24px;
}

#jstree-dnd.jstree-default-dark .jstree-ok {
  background-position: -4px -68px;
}

#jstree-dnd.jstree-default-dark .jstree-er {
  background-position: -36px -68px;
}

.jstree-default-dark .jstree-ellipsis {
  overflow: hidden;
}

.jstree-default-dark .jstree-ellipsis .jstree-anchor {
  width: calc(100% - 24px + 5px);
  text-overflow: ellipsis;
  overflow: hidden;
}

.jstree-default-dark.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
}

.jstree-default-dark.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark-small .jstree-node {
  min-height: 18px;
  line-height: 18px;
  margin-left: 18px;
  min-width: 18px;
}

.jstree-default-dark-small .jstree-anchor {
  line-height: 18px;
  height: 18px;
}

.jstree-default-dark-small .jstree-icon {
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.jstree-default-dark-small .jstree-icon:empty {
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.jstree-default-dark-small.jstree-rtl .jstree-node {
  margin-right: 18px;
}

.jstree-default-dark-small .jstree-wholerow {
  height: 18px;
}

.jstree-default-dark-small .jstree-node,
.jstree-default-dark-small .jstree-icon {
  background-image: url("32px.png");
}

.jstree-default-dark-small .jstree-node {
  background-position: -295px -7px;
  background-repeat: repeat-y;
}

.jstree-default-dark-small .jstree-last {
  background-image: none;
}

.jstree-default-dark-small .jstree-open > .jstree-ocl {
  background-position: -135px -7px;
}

.jstree-default-dark-small .jstree-closed > .jstree-ocl {
  background-position: -103px -7px;
}

.jstree-default-dark-small .jstree-leaf > .jstree-ocl {
  background-position: -71px -7px;
}

.jstree-default-dark-small .jstree-themeicon {
  background-position: -263px -7px;
}

.jstree-default-dark-small > .jstree-no-dots .jstree-node,
.jstree-default-dark-small > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-dark-small > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -39px -7px;
}

.jstree-default-dark-small > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -7px -7px;
}

.jstree-default-dark-small .jstree-disabled {
  background: transparent;
}

.jstree-default-dark-small .jstree-disabled.jstree-hovered {
  background: transparent;
}

.jstree-default-dark-small .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default-dark-small .jstree-checkbox {
  background-position: -167px -7px;
}

.jstree-default-dark-small .jstree-checkbox:hover {
  background-position: -167px -39px;
}

.jstree-default-dark-small.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
.jstree-default-dark-small .jstree-checked > .jstree-checkbox {
  background-position: -231px -7px;
}

.jstree-default-dark-small.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
.jstree-default-dark-small .jstree-checked > .jstree-checkbox:hover {
  background-position: -231px -39px;
}

.jstree-default-dark-small .jstree-anchor > .jstree-undetermined {
  background-position: -199px -7px;
}

.jstree-default-dark-small .jstree-anchor > .jstree-undetermined:hover {
  background-position: -199px -39px;
}

.jstree-default-dark-small .jstree-checkbox-disabled {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default-dark-small > .jstree-striped {
  background-size: auto 36px;
}

.jstree-default-dark-small.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
  background-position: 100% 1px;
  background-repeat: repeat-y;
}

.jstree-default-dark-small.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark-small.jstree-rtl .jstree-open > .jstree-ocl {
  background-position: -135px -39px;
}

.jstree-default-dark-small.jstree-rtl .jstree-closed > .jstree-ocl {
  background-position: -103px -39px;
}

.jstree-default-dark-small.jstree-rtl .jstree-leaf > .jstree-ocl {
  background-position: -71px -39px;
}

.jstree-default-dark-small.jstree-rtl > .jstree-no-dots .jstree-node,
.jstree-default-dark-small.jstree-rtl > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-dark-small.jstree-rtl > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -39px -39px;
}

.jstree-default-dark-small.jstree-rtl > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: -7px -39px;
}

.jstree-default-dark-small .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0;
}

.jstree-default-dark-small > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("throbber.gif") center center no-repeat;
}

.jstree-default-dark-small .jstree-file {
  background: url("32px.png") -103px -71px no-repeat;
}

.jstree-default-dark-small .jstree-folder {
  background: url("32px.png") -263px -7px no-repeat;
}

.jstree-default-dark-small > .jstree-container-ul > .jstree-node {
  margin-left: 0;
  margin-right: 0;
}

#jstree-dnd.jstree-default-dark-small {
  line-height: 18px;
  padding: 0 4px;
}

#jstree-dnd.jstree-default-dark-small .jstree-ok,
#jstree-dnd.jstree-default-dark-small .jstree-er {
  background-image: url("32px.png");
  background-repeat: no-repeat;
  background-color: transparent;
}

#jstree-dnd.jstree-default-dark-small i {
  background: transparent;
  width: 18px;
  height: 18px;
  line-height: 18px;
}

#jstree-dnd.jstree-default-dark-small .jstree-ok {
  background-position: -7px -71px;
}

#jstree-dnd.jstree-default-dark-small .jstree-er {
  background-position: -39px -71px;
}

.jstree-default-dark-small .jstree-ellipsis {
  overflow: hidden;
}

.jstree-default-dark-small .jstree-ellipsis .jstree-anchor {
  width: calc(100% - 18px + 5px);
  text-overflow: ellipsis;
  overflow: hidden;
}

.jstree-default-dark-small.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAACAQMAAABv1h6PAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMHBgAAiABBI4gz9AAAAABJRU5ErkJggg==");
}

.jstree-default-dark-small.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark-large .jstree-node {
  min-height: 32px;
  line-height: 32px;
  margin-left: 32px;
  min-width: 32px;
}

.jstree-default-dark-large .jstree-anchor {
  line-height: 32px;
  height: 32px;
}

.jstree-default-dark-large .jstree-icon {
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.jstree-default-dark-large .jstree-icon:empty {
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.jstree-default-dark-large.jstree-rtl .jstree-node {
  margin-right: 32px;
}

.jstree-default-dark-large .jstree-wholerow {
  height: 32px;
}

.jstree-default-dark-large .jstree-node,
.jstree-default-dark-large .jstree-icon {
  background-image: url("32px.png");
}

.jstree-default-dark-large .jstree-node {
  background-position: -288px 0px;
  background-repeat: repeat-y;
}

.jstree-default-dark-large .jstree-last {
  background-image: none;
}

.jstree-default-dark-large .jstree-open > .jstree-ocl {
  background-position: -128px 0px;
}

.jstree-default-dark-large .jstree-closed > .jstree-ocl {
  background-position: -96px 0px;
}

.jstree-default-dark-large .jstree-leaf > .jstree-ocl {
  background-position: -64px 0px;
}

.jstree-default-dark-large .jstree-themeicon {
  background-position: -256px 0px;
}

.jstree-default-dark-large > .jstree-no-dots .jstree-node,
.jstree-default-dark-large > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-dark-large > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -32px 0px;
}

.jstree-default-dark-large > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: 0px 0px;
}

.jstree-default-dark-large .jstree-disabled {
  background: transparent;
}

.jstree-default-dark-large .jstree-disabled.jstree-hovered {
  background: transparent;
}

.jstree-default-dark-large .jstree-disabled.jstree-clicked {
  background: #efefef;
}

.jstree-default-dark-large .jstree-checkbox {
  background-position: -160px 0px;
}

.jstree-default-dark-large .jstree-checkbox:hover {
  background-position: -160px -32px;
}

.jstree-default-dark-large.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
.jstree-default-dark-large .jstree-checked > .jstree-checkbox {
  background-position: -224px 0px;
}

.jstree-default-dark-large.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
.jstree-default-dark-large .jstree-checked > .jstree-checkbox:hover {
  background-position: -224px -32px;
}

.jstree-default-dark-large .jstree-anchor > .jstree-undetermined {
  background-position: -192px 0px;
}

.jstree-default-dark-large .jstree-anchor > .jstree-undetermined:hover {
  background-position: -192px -32px;
}

.jstree-default-dark-large .jstree-checkbox-disabled {
  opacity: 0.8;
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='jstree-grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#jstree-grayscale");
  /* Firefox 10+ */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+ & Safari 6+ */
}

.jstree-default-dark-large > .jstree-striped {
  background-size: auto 64px;
}

.jstree-default-dark-large.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
  background-position: 100% 1px;
  background-repeat: repeat-y;
}

.jstree-default-dark-large.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark-large.jstree-rtl .jstree-open > .jstree-ocl {
  background-position: -128px -32px;
}

.jstree-default-dark-large.jstree-rtl .jstree-closed > .jstree-ocl {
  background-position: -96px -32px;
}

.jstree-default-dark-large.jstree-rtl .jstree-leaf > .jstree-ocl {
  background-position: -64px -32px;
}

.jstree-default-dark-large.jstree-rtl > .jstree-no-dots .jstree-node,
.jstree-default-dark-large.jstree-rtl > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}

.jstree-default-dark-large.jstree-rtl > .jstree-no-dots .jstree-open > .jstree-ocl {
  background-position: -32px -32px;
}

.jstree-default-dark-large.jstree-rtl > .jstree-no-dots .jstree-closed > .jstree-ocl {
  background-position: 0px -32px;
}

.jstree-default-dark-large .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0;
}

.jstree-default-dark-large > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("throbber.gif") center center no-repeat;
}

.jstree-default-dark-large .jstree-file {
  background: url("32px.png") -96px -64px no-repeat;
}

.jstree-default-dark-large .jstree-folder {
  background: url("32px.png") -256px 0px no-repeat;
}

.jstree-default-dark-large > .jstree-container-ul > .jstree-node {
  margin-left: 0;
  margin-right: 0;
}

#jstree-dnd.jstree-default-dark-large {
  line-height: 32px;
  padding: 0 4px;
}

#jstree-dnd.jstree-default-dark-large .jstree-ok,
#jstree-dnd.jstree-default-dark-large .jstree-er {
  background-image: url("32px.png");
  background-repeat: no-repeat;
  background-color: transparent;
}

#jstree-dnd.jstree-default-dark-large i {
  background: transparent;
  width: 32px;
  height: 32px;
  line-height: 32px;
}

#jstree-dnd.jstree-default-dark-large .jstree-ok {
  background-position: 0px -64px;
}

#jstree-dnd.jstree-default-dark-large .jstree-er {
  background-position: -32px -64px;
}

.jstree-default-dark-large .jstree-ellipsis {
  overflow: hidden;
}

.jstree-default-dark-large .jstree-ellipsis .jstree-anchor {
  width: calc(100% - 32px + 5px);
  text-overflow: ellipsis;
  overflow: hidden;
}

.jstree-default-dark-large.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAACAQMAAAAD0EyKAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjgIIGBgABCgCBvVLXcAAAAABJRU5ErkJggg==");
}

.jstree-default-dark-large.jstree-rtl .jstree-last {
  background-image: none;
}

@media (max-width: 768px) {
  #jstree-dnd.jstree-dnd-responsive {
    line-height: 40px;
    font-weight: bold;
    font-size: 1.1em;
    text-shadow: 1px 1px white;
  }
  #jstree-dnd.jstree-dnd-responsive > i {
    background: transparent;
    width: 40px;
    height: 40px;
  }
  #jstree-dnd.jstree-dnd-responsive > .jstree-ok {
    background-image: url("40px.png");
    background-position: 0 -200px;
    background-size: 120px 240px;
  }
  #jstree-dnd.jstree-dnd-responsive > .jstree-er {
    background-image: url("40px.png");
    background-position: -40px -200px;
    background-size: 120px 240px;
  }
  #jstree-marker.jstree-dnd-responsive {
    border-left-width: 10px;
    border-top-width: 10px;
    border-bottom-width: 10px;
    margin-top: -10px;
  }
}
@media (max-width: 768px) {
  .jstree-default-dark-responsive {
    /*
    .jstree-open > .jstree-ocl,
    .jstree-closed > .jstree-ocl { border-radius:20px; background-color:white; }
    */
  }
  .jstree-default-dark-responsive .jstree-icon {
    background-image: url("40px.png");
  }
  .jstree-default-dark-responsive .jstree-node,
  .jstree-default-dark-responsive .jstree-leaf > .jstree-ocl {
    background: transparent;
  }
  .jstree-default-dark-responsive .jstree-node {
    min-height: 40px;
    line-height: 40px;
    margin-left: 40px;
    min-width: 40px;
    white-space: nowrap;
  }
  .jstree-default-dark-responsive .jstree-anchor {
    line-height: 40px;
    height: 40px;
  }
  .jstree-default-dark-responsive .jstree-icon,
  .jstree-default-dark-responsive .jstree-icon:empty {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .jstree-default-dark-responsive > .jstree-container-ul > .jstree-node {
    margin-left: 0;
  }
  .jstree-default-dark-responsive.jstree-rtl .jstree-node {
    margin-left: 0;
    margin-right: 40px;
    background: transparent;
  }
  .jstree-default-dark-responsive.jstree-rtl .jstree-container-ul > .jstree-node {
    margin-right: 0;
  }
  .jstree-default-dark-responsive .jstree-ocl,
  .jstree-default-dark-responsive .jstree-themeicon,
  .jstree-default-dark-responsive .jstree-checkbox {
    background-size: 120px 240px;
  }
  .jstree-default-dark-responsive .jstree-leaf > .jstree-ocl,
  .jstree-default-dark-responsive.jstree-rtl .jstree-leaf > .jstree-ocl {
    background: transparent;
  }
  .jstree-default-dark-responsive .jstree-open > .jstree-ocl {
    background-position: 0 0 !important;
  }
  .jstree-default-dark-responsive .jstree-closed > .jstree-ocl {
    background-position: 0 -40px !important;
  }
  .jstree-default-dark-responsive.jstree-rtl .jstree-closed > .jstree-ocl {
    background-position: -40px 0 !important;
  }
  .jstree-default-dark-responsive .jstree-themeicon {
    background-position: -40px -40px;
  }
  .jstree-default-dark-responsive .jstree-checkbox,
  .jstree-default-dark-responsive .jstree-checkbox:hover {
    background-position: -40px -80px;
  }
  .jstree-default-dark-responsive.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
  .jstree-default-dark-responsive.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
  .jstree-default-dark-responsive .jstree-checked > .jstree-checkbox,
  .jstree-default-dark-responsive .jstree-checked > .jstree-checkbox:hover {
    background-position: 0 -80px;
  }
  .jstree-default-dark-responsive .jstree-anchor > .jstree-undetermined,
  .jstree-default-dark-responsive .jstree-anchor > .jstree-undetermined:hover {
    background-position: 0 -120px;
  }
  .jstree-default-dark-responsive .jstree-anchor {
    font-weight: bold;
    font-size: 1.1em;
    text-shadow: 1px 1px white;
  }
  .jstree-default-dark-responsive > .jstree-striped {
    background: transparent;
  }
  .jstree-default-dark-responsive .jstree-wholerow {
    border-top: 1px solid #666;
    border-bottom: 1px solid #000;
    background: #333333;
    height: 40px;
  }
  .jstree-default-dark-responsive .jstree-wholerow-hovered {
    background: #555;
  }
  .jstree-default-dark-responsive .jstree-wholerow-clicked {
    background: #5fa2db;
  }
  .jstree-default-dark-responsive .jstree-children .jstree-last > .jstree-wholerow {
    box-shadow: inset 0 -6px 3px -5px #111111;
  }
  .jstree-default-dark-responsive .jstree-children .jstree-open > .jstree-wholerow {
    box-shadow: inset 0 6px 3px -5px #111111;
    border-top: 0;
  }
  .jstree-default-dark-responsive .jstree-children .jstree-open + .jstree-open {
    box-shadow: none;
  }
  .jstree-default-dark-responsive .jstree-node,
  .jstree-default-dark-responsive .jstree-icon,
  .jstree-default-dark-responsive .jstree-node > .jstree-ocl,
  .jstree-default-dark-responsive .jstree-themeicon,
  .jstree-default-dark-responsive .jstree-checkbox {
    background-image: url("40px.png");
    background-size: 120px 240px;
  }
  .jstree-default-dark-responsive .jstree-node {
    background-position: -80px 0;
    background-repeat: repeat-y;
  }
  .jstree-default-dark-responsive .jstree-last {
    background-image: none;
  }
  .jstree-default-dark-responsive .jstree-leaf > .jstree-ocl {
    background-position: -40px -120px;
  }
  .jstree-default-dark-responsive .jstree-last > .jstree-ocl {
    background-position: -40px -160px;
  }
  .jstree-default-dark-responsive .jstree-themeicon-custom {
    background-color: transparent;
    background-image: none;
    background-position: 0 0;
  }
  .jstree-default-dark-responsive .jstree-file {
    background: url("40px.png") 0 -160px no-repeat;
    background-size: 120px 240px;
  }
  .jstree-default-dark-responsive .jstree-folder {
    background: url("40px.png") -40px -40px no-repeat;
    background-size: 120px 240px;
  }
  .jstree-default-dark-responsive > .jstree-container-ul > .jstree-node {
    margin-left: 0;
    margin-right: 0;
  }
}
.jstree-default-dark {
  background: #333;
}

.jstree-default-dark .jstree-anchor {
  color: #999;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
}

.jstree-default-dark .jstree-clicked,
.jstree-default-dark .jstree-checked {
  color: white;
}

.jstree-default-dark .jstree-hovered {
  color: white;
}

#jstree-marker.jstree-default-dark {
  border-left-color: #999;
  background: transparent;
}

.jstree-default-dark .jstree-anchor > .jstree-icon {
  opacity: 0.75;
}

.jstree-default-dark .jstree-clicked > .jstree-icon,
.jstree-default-dark .jstree-hovered > .jstree-icon,
.jstree-default-dark .jstree-checked > .jstree-icon {
  opacity: 1;
}

.jstree-default-dark.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAACZmZl+9SADAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg==");
}

.jstree-default-dark.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark-small.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAACAQMAAABv1h6PAAAABlBMVEUAAACZmZl+9SADAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMHBgAAiABBI4gz9AAAAABJRU5ErkJggg==");
}

.jstree-default-dark-small.jstree-rtl .jstree-last {
  background-image: none;
}

.jstree-default-dark-large.jstree-rtl .jstree-node {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAACAQMAAAAD0EyKAAAABlBMVEUAAACZmZl+9SADAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjgIIGBgABCgCBvVLXcAAAAABJRU5ErkJggg==");
}

.jstree-default-dark-large.jstree-rtl .jstree-last {
  background-image: none;
}

/* imported for media query mixin */
.jstree-default {
  background: transparent;
}

.jstree-default > .jstree-container-ul .jstree-loading > .jstree-ocl,
.jstree-default-small > .jstree-container-ul .jstree-loading > .jstree-ocl,
.jstree-default-large > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background-image: url('./themes/default/throbber.gif');
}

.jstree-default .jstree-node,
.jstree-default .jstree-icon,
.jstree-default .jstree-file,
.jstree-default .jstree-folder,
#jstree-dnd.jstree-default .jstree-ok,
#jstree-dnd.jstree-default .jstree-er,
.jstree-default-small .jstree-node,
.jstree-default-small .jstree-icon,
.jstree-default-small .jstree-file,
.jstree-default-small .jstree-folder,
#jstree-dnd.jstree-default-small .jstree-ok,
#jstree-dnd.jstree-default-small .jstree-er,
.jstree-default-large .jstree-node,
.jstree-default-large .jstree-icon,
.jstree-default-large .jstree-file,
.jstree-default-large .jstree-folder,
#jstree-dnd.jstree-default-large .jstree-ok,
#jstree-dnd.jstree-default-large .jstree-er {
  background-image: url('./themes/default/32px.png');
}

@media (max-width: 767.98px) {
  #jstree-dnd.jstree-dnd-responsive > .jstree-ok,
  #jstree-dnd.jstree-dnd-responsive > .jstree-er,
  .jstree-default-responsive .jstree-icon,
  .jstree-default-responsive .jstree-node,
  .jstree-default-responsive .jstree-icon,
  .jstree-default-responsive .jstree-node > .jstree-ocl,
  .jstree-default-responsive .jstree-themeicon,
  .jstree-default-responsive .jstree-checkbox,
  .jstree-default-responsive .jstree-file,
  .jstree-default-responsive .jstree-folder {
    background-image: url('./themes/default/40px.png');
  }
}
/* Dark Style */
[data-bs-theme=dark] .jstree-default-dark {
  background: transparent;
}
[data-bs-theme=dark] .jstree-default-dark > .jstree-container-ul .jstree-loading > .jstree-ocl,
[data-bs-theme=dark] .jstree-default-dark-small > .jstree-container-ul .jstree-loading > .jstree-ocl,
[data-bs-theme=dark] .jstree-default-dark-large > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background-image: url('./themes/default-dark/throbber.gif');
}
[data-bs-theme=dark] .jstree-default-dark:not(.jstree-rtl) .jstree-node,
[data-bs-theme=dark] .jstree-default-dark .jstree-icon,
[data-bs-theme=dark] .jstree-default-dark .jstree-file,
[data-bs-theme=dark] .jstree-default-dark .jstree-folder,
[data-bs-theme=dark] #jstree-dnd.jstree-default-dark .jstree-ok,
[data-bs-theme=dark] #jstree-dnd.jstree-default-dark .jstree-er,
[data-bs-theme=dark] .jstree-default-dark-small .jstree-node,
[data-bs-theme=dark] .jstree-default-dark-small .jstree-icon,
[data-bs-theme=dark] .jstree-default-dark-small .jstree-file,
[data-bs-theme=dark] .jstree-default-dark-small .jstree-folder,
[data-bs-theme=dark] #jstree-dnd.jstree-default-dark-small .jstree-ok,
[data-bs-theme=dark] #jstree-dnd.jstree-default-dark-small .jstree-er,
[data-bs-theme=dark] .jstree-default-dark-large .jstree-node,
[data-bs-theme=dark] .jstree-default-dark-large .jstree-icon,
[data-bs-theme=dark] .jstree-default-dark-large .jstree-file,
[data-bs-theme=dark] .jstree-default-dark-large .jstree-folder,
[data-bs-theme=dark] #jstree-dnd.jstree-default-dark-large .jstree-ok,
[data-bs-theme=dark] #jstree-dnd.jstree-default-dark-large .jstree-er {
  background-image: url('./themes/default-dark/32px.png');
}
@media (max-width: 767.98px) {
  [data-bs-theme=dark] #jstree-dnd.jstree-dnd-responsive > .jstree-ok,
  [data-bs-theme=dark] #jstree-dnd.jstree-dnd-responsive > .jstree-er,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-icon,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-node,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-icon,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-node > .jstree-ocl,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-themeicon,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-checkbox,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-file,
  [data-bs-theme=dark] .jstree-default-dark-responsive .jstree-folder {
    background-image: url('./themes/default-dark/40px.png');
  }
}

.jstree {
  --bs-jstree-bg-amount: 15%;
  --bs-jstree-mix-bg: var(--bs-white);
}
.jstree .jstree-container-ul .jstree-anchor {
  color: var(--bs-heading-color);
}
.jstree.jstree-default .jstree-icon:not(.jstree-ocl):not(.jstree-checkbox), .jstree.jstree-default-dark .jstree-icon:not(.jstree-ocl):not(.jstree-checkbox) {
  block-size: 1.25rem;
  inline-size: 1.25rem;
  vertical-align: middle;
}
.jstree.jstree-default .jstree-themeicon-custom, .jstree.jstree-default-dark .jstree-themeicon-custom {
  background: var(--bs-body-color);
}
.jstree.jstree-default > :not(.jstree-wholerow-ul) .jstree-hovered,
.jstree.jstree-default > :not(.jstree-wholerow-ul) .jstree-clicked, .jstree.jstree-default-dark > :not(.jstree-wholerow-ul) .jstree-hovered,
.jstree.jstree-default-dark > :not(.jstree-wholerow-ul) .jstree-clicked {
  background: var(--bs-primary-bg-subtle);
  box-shadow: none;
}
.jstree.jstree-default .jstree-wholerow-clicked,
.jstree.jstree-default .jstree-wholerow-hovered, .jstree.jstree-default-dark .jstree-wholerow-clicked,
.jstree.jstree-default-dark .jstree-wholerow-hovered {
  background: color-mix(in sRGB, var(--bs-primary) var(--bs-jstree-bg-amount), var(--bs-jstree-mix-bg));
}
.jstree.jstree-default > .jstree-no-dots .jstree-node,
.jstree.jstree-default > .jstree-no-dots .jstree-leaf > .jstree-ocl, .jstree.jstree-default-dark > .jstree-no-dots .jstree-node,
.jstree.jstree-default-dark > .jstree-no-dots .jstree-leaf > .jstree-ocl {
  background: transparent;
}
.jstree.jstree-default .jstree-hovered,
.jstree.jstree-default .jstree-wholerow-hovered,
.jstree.jstree-default .jstree-checkbox-no-clicked .jstree-clicked.jstree-hovered,
.jstree.jstree-default .jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked.jstree-wholerow-hovered,
.jstree.jstree-default .jstree-default-responsive .jstree-wholerow-hovered, .jstree.jstree-default-dark .jstree-hovered,
.jstree.jstree-default-dark .jstree-wholerow-hovered,
.jstree.jstree-default-dark .jstree-checkbox-no-clicked .jstree-clicked.jstree-hovered,
.jstree.jstree-default-dark .jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked.jstree-wholerow-hovered,
.jstree.jstree-default-dark .jstree-default-responsive .jstree-wholerow-hovered {
  background-color: var(--bs-gray-50);
}
.jstree.jstree-default .jstree-clicked,
.jstree.jstree-default .jstree-context,
.jstree.jstree-default .jstree-wholerow-clicked,
.jstree.jstree-default .jstree-default-responsive .jstree-wholerow-clicked, .jstree.jstree-default-dark .jstree-clicked,
.jstree.jstree-default-dark .jstree-context,
.jstree.jstree-default-dark .jstree-wholerow-clicked,
.jstree.jstree-default-dark .jstree-default-responsive .jstree-wholerow-clicked {
  background-color: rgba(var(--bs-primary-rgb), 0.08);
}
.jstree.jstree-default .jstree-icon.jstree-checkbox {
  background-image: url("data:image/png;base64,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");
  margin-block-start: 0.1875rem;
}
.jstree.jstree-default-dark {
  --bs-jstree-bg-amount: 45%;
  --bs-jstree-mix-bg: var(--bs-dark);
  background: var(--bs-paper-bg);
}
.jstree.jstree-default-dark .jstree-anchor {
  text-shadow: none;
}
.jstree.jstree-default-dark .jstree-hovered,
.jstree.jstree-default-dark .jstree-wholerow-hovered,
.jstree.jstree-default-dark .jstree-checkbox-no-clicked .jstree-clicked.jstree-hovered,
.jstree.jstree-default-dark .jstree-checkbox-no-clicked > .jstree-wholerow-ul .jstree-wholerow-clicked.jstree-wholerow-hovered,
.jstree.jstree-default-dark .jstree-default-responsive .jstree-wholerow-hovered {
  background-color: var(--bs-gray-50);
}
.jstree.jstree-default-dark .jstree-clicked,
.jstree.jstree-default-dark .jstree-context,
.jstree.jstree-default-dark .jstree-wholerow-clicked,
.jstree.jstree-default-dark .jstree-default-responsive .jstree-wholerow-clicked {
  background-color: rgba(var(--bs-primary-rgb), 0.08);
}
