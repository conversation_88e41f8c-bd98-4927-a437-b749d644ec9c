from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone
from organisations.models import TypeAbonnement


class StatutDemande(models.TextChoices):
    """Statuts possibles pour une demande d'inscription"""
    EN_ATTENTE = 'EN_ATTENTE', 'En attente de validation'
    VALIDEE = 'VALIDEE', 'Validée et activée'
    REJETEE = 'REJETEE', 'Rejetée'


class DemandeInscription(models.Model):
    """Modèle pour les demandes d'inscription d'organisations"""

    # === INFORMATIONS ORGANISATION ===
    nom_organisation = models.CharField(
        max_length=200,
        verbose_name="Nom de l'organisation",
        help_text="Nom complet de l'établissement hospitalier"
    )

    adresse_organisation = models.TextField(
        verbose_name="Adresse de l'organisation",
        help_text="Adresse postale complète"
    )

    telephone_organisation = models.Char<PERSON>ield(
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+[1-9]\d{1,14}$',
            message="Format de téléphone invalide. Utilisez le format international."
        )],
        verbose_name="Téléphone de l'organisation",
        help_text="Numéro de téléphone au format international"
    )

    email_organisation = models.EmailField(
        verbose_name="Email de l'organisation",
        help_text="Adresse email principale de l'organisation"
    )

    type_abonnement_demande = models.CharField(
        max_length=10,
        choices=TypeAbonnement.choices,
        default=TypeAbonnement.GRATUIT,
        verbose_name="Type d'abonnement demandé",
        help_text="Type d'abonnement souhaité"
    )

    # === INFORMATIONS ADMINISTRATEUR ===
    prenom_admin = models.CharField(
        max_length=100,
        verbose_name="Prénom de l'administrateur",
        help_text="Prénom de la personne qui sera administrateur de l'organisation"
    )

    nom_admin = models.CharField(
        max_length=100,
        verbose_name="Nom de l'administrateur",
        help_text="Nom de famille de l'administrateur"
    )

    email_admin = models.EmailField(
        verbose_name="Email de l'administrateur",
        help_text="Adresse email qui servira pour la connexion",
        unique=True
    )

    telephone_admin = models.CharField(
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+[1-9]\d{1,14}$',
            message="Format de téléphone invalide. Utilisez le format international."
        )],
        verbose_name="Téléphone de l'administrateur",
        help_text="Numéro de téléphone personnel"
    )

    mot_de_passe = models.CharField(
        max_length=128,
        verbose_name="Mot de passe",
        help_text="Mot de passe pour le compte administrateur (sera hashé)"
    )

    # === GESTION DE LA DEMANDE ===
    statut = models.CharField(
        max_length=15,
        choices=StatutDemande.choices,
        default=StatutDemande.EN_ATTENTE,
        verbose_name="Statut de la demande"
    )

    date_demande = models.DateTimeField(
        default=timezone.now,
        verbose_name="Date de la demande"
    )

    date_traitement = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Date de traitement"
    )

    commentaire_admin = models.TextField(
        blank=True,
        verbose_name="Commentaire du super admin",
        help_text="Commentaire lors de la validation ou du rejet"
    )

    traite_par = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        verbose_name="Traité par",
        help_text="Super admin qui a traité la demande"
    )

    # === RELATIONS ===
    organisation_creee = models.OneToOneField(
        'organisations.Organisation',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        verbose_name="Organisation créée",
        help_text="Organisation créée suite à la validation"
    )

    utilisateur_cree = models.OneToOneField(
        User,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='demande_inscription',
        verbose_name="Utilisateur créé",
        help_text="Compte utilisateur créé pour l'administrateur"
    )

    class Meta:
        verbose_name = "Demande d'inscription"
        verbose_name_plural = "Demandes d'inscription"
        ordering = ['-date_demande']

    def __str__(self):
        return f"{self.nom_organisation} - {self.get_statut_display()}"

    def get_nom_complet_admin(self):
        """Retourne le nom complet de l'administrateur"""
        return f"{self.prenom_admin} {self.nom_admin}"

    def est_en_attente(self):
        """Vérifie si la demande est en attente"""
        return self.statut == StatutDemande.EN_ATTENTE

    def est_validee(self):
        """Vérifie si la demande est validée"""
        return self.statut == StatutDemande.VALIDEE

    def est_rejetee(self):
        """Vérifie si la demande est rejetée"""
        return self.statut == StatutDemande.REJETEE
