/* List groups
******************************************************************************* */

/* List Group Mixin */
.list-group {
  --#{$prefix}list-group-timeline-bg: var(--#{$prefix}primary);
  --#{$prefix}list-group-border-color: #{$list-group-border-color};
  --#{$prefix}list-group-active-border-color: var(--#{$prefix}border-color);
  --#{$prefix}list-group-action-hover-color: var(--#{$prefix}heading-color);
  --#{$prefix}list-group-action-active-color: var(--#{$prefix}body-color);
  --#{$prefix}list-group-active-bg: var(--#{$prefix}primary-bg-subtle);

  .list-group-item {
    line-height: 1.375rem;
    padding-block-end: calc($list-group-item-padding-y - 1px);
  }
  &:not([class*="list-group-flush"]) .list-group-item:first-of-type {
    padding-block-start: calc($list-group-item-padding-y - 1px);
  }
  &[class*="list-group-flush"] .list-group-item:last-of-type {
    padding-block-end: $list-group-item-padding-y;
  }
  &[class*="list-group-horizontal-md"] .list-group-item {
    word-wrap: normal;
    @include media-breakpoint-up(md) {
      padding-block-start: calc($list-group-item-padding-y - 1px);
    }
  }

  // Timeline CSS
  &.list-group-timeline {
    position: relative;

    &::before {
      position: absolute;
      background-color: var(--#{$prefix}border-color);
      block-size: 100%;
      content: "";
      inline-size: 1px;
      inset-block: 0;
      inset-inline-start: .2rem;
    }

    .list-group-item {
      border: 0;
      padding-inline-start: 1.25rem;

      &::before {
        position: absolute;
        background-color: var(--#{$prefix}list-group-timeline-bg);
        block-size: 7px;
        content: "";
        inline-size: 7px;
        inset-block-start: 50%;
        inset-inline-start: 0;
        margin-block-start: -3.5px;
        @include border-radius(100%);
      }
    }
  }

  .list-group-item.active {
    color: var(--#{$prefix}primary);
    h1,
    .h1,
    h2,
    .h2,
    h3,
    .h3,
    h4,
    .h4,
    h5,
    .h5,
    h6,
    .h6 {
      color: var(--#{$prefix}primary);
    }
    &,
    &:hover,
    &:focus {
      --#{$prefix}list-group-color: var(--#{$prefix}white);
    }
  }
}

/* RTL
******************************************************************************* */

:dir(rtl) {
  .list-group {
    padding-inline-start: 0;


    // List group horizontal RTL style

    &.list-group-horizontal {
      .list-group-item {
        &:first-child {
          @include border-radius(var(--#{$prefix}list-group-border-radius));
          @include border-start-radius(0);
        }

        &:last-child {
          @include border-radius(var(--#{$prefix}list-group-border-radius));
          border-inline-end-width: 1px;
          @include border-end-radius(0);
        }
      }
    }

    @include media-breakpoint-up(sm) {
      &.list-group-horizontal-sm {
        .list-group-item {
          &:first-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            @include border-start-radius(0);
          }

          &:last-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            border-inline-end-width: 1px;
            @include border-end-radius(0);
          }
        }
      }
    }

    @include media-breakpoint-up(md) {
      &.list-group-horizontal-md {
        .list-group-item {
          &:first-child {
            @include border-radius(0);
            @include border-start-radius(var(--#{$prefix}list-group-border-radius));
          }

          &:last-child {
            @include border-radius(0);
            border-inline-end-width: 1px;
            @include border-end-radius(var(--#{$prefix}list-group-border-radius));
          }
        }
      }
    }

    @include media-breakpoint-up(lg) {
      &.list-group-horizontal-lg {
        .list-group-item {
          &:first-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            @include border-start-radius(0);
          }

          &:last-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            border-inline-end-width: 1px;
            @include border-end-radius(0);
          }
        }
      }
    }

    @include media-breakpoint-up(xl) {
      &.list-group-horizontal-xl {
        .list-group-item {
          &:first-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            @include border-start-radius(0);
          }

          &:last-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            border-inline-end-width: 1px;
            @include border-end-radius(0);
          }
        }
      }
    }

    @include media-breakpoint-up(xxl) {
      &.list-group-horizontal-xxl {
        .list-group-item {
          &:first-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            @include border-start-radius(0);
          }

          &:last-child {
            @include border-radius(var(--#{$prefix}list-group-border-radius));
            border-inline-end-width: 1px;
            @include border-end-radius(0);
          }
        }
      }
    }
  }
}

// scss-docs-start list-group-modifiers

@each $state in map-keys($theme-colors) {
  .list-group-item-#{$state} {
    --#{$prefix}list-group-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}list-group-active-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}list-group-active-bg: var(--#{$prefix}#{$state}-bg-subtle);
    --#{$prefix}list-group-color: var(--#{$prefix}#{$state}-text-emphasis);
    --#{$prefix}list-group-action-hover-color: var(--#{$prefix}#{$state}-text-emphasis);
    --#{$prefix}list-group-action-active-color: var(--#{$prefix}#{$state}-text-emphasis);
  }
  .list-group-timeline-#{$state} {
    --#{$prefix}list-group-timeline-bg: var(--#{$prefix}#{$state});
  }
}

/* Dark
******************************************************************************* */

@if $enable-dark-mode {
  @include color-mode(dark) {
    .list-group {
      --#{$prefix}list-group-border-color: #{$list-group-border-color-dark};
    }
  }
}

// scss-docs-end list-group-modifiers
