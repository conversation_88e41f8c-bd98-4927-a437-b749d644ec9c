!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("jQuery"));else if("function"==typeof define&&define.amd)define(["jQuery"],e);else{var t="object"==typeof exports?e(require("jQuery")):e(n.jQuery);for(var r in t)("object"==typeof exports?exports:n)[r]=t[r]}}(self,(function(__WEBPACK_EXTERNAL_MODULE_jquery__){return function(){var __webpack_modules__={"./libs/bloodhound/bloodhound.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bloodhound: function() { return /* reexport default from dynamic */ typeahead_js_dist_bloodhound__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var typeahead_js_dist_bloodhound__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! typeahead.js/dist/bloodhound */ "./node_modules/typeahead.js/dist/bloodhound.js");\n/* harmony import */ var typeahead_js_dist_bloodhound__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(typeahead_js_dist_bloodhound__WEBPACK_IMPORTED_MODULE_0__);\n\ntry {\n  window.Bloodhound = (typeahead_js_dist_bloodhound__WEBPACK_IMPORTED_MODULE_0___default());\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/bloodhound/bloodhound.js?')},"./node_modules/typeahead.js/dist/bloodhound.js":function(module,exports,__webpack_require__){eval('var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*!\n * typeahead.js 0.11.1\n * https://github.com/twitter/typeahead.js\n * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT\n */\n\n(function(root, factory) {\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [ __webpack_require__(/*! jquery */ "jquery") ], __WEBPACK_AMD_DEFINE_RESULT__ = (function(a0) {\n            return root["Bloodhound"] = factory(a0);\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n})(this, function($) {\n    var _ = function() {\n        "use strict";\n        return {\n            isMsie: function() {\n                return /(msie|trident)/i.test(navigator.userAgent) ? navigator.userAgent.match(/(msie |rv:)(\\d+(.\\d+)?)/i)[2] : false;\n            },\n            isBlankString: function(str) {\n                return !str || /^\\s*$/.test(str);\n            },\n            escapeRegExChars: function(str) {\n                return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, "\\\\$&");\n            },\n            isString: function(obj) {\n                return typeof obj === "string";\n            },\n            isNumber: function(obj) {\n                return typeof obj === "number";\n            },\n            isArray: $.isArray,\n            isFunction: $.isFunction,\n            isObject: $.isPlainObject,\n            isUndefined: function(obj) {\n                return typeof obj === "undefined";\n            },\n            isElement: function(obj) {\n                return !!(obj && obj.nodeType === 1);\n            },\n            isJQuery: function(obj) {\n                return obj instanceof $;\n            },\n            toStr: function toStr(s) {\n                return _.isUndefined(s) || s === null ? "" : s + "";\n            },\n            bind: $.proxy,\n            each: function(collection, cb) {\n                $.each(collection, reverseArgs);\n                function reverseArgs(index, value) {\n                    return cb(value, index);\n                }\n            },\n            map: $.map,\n            filter: $.grep,\n            every: function(obj, test) {\n                var result = true;\n                if (!obj) {\n                    return result;\n                }\n                $.each(obj, function(key, val) {\n                    if (!(result = test.call(null, val, key, obj))) {\n                        return false;\n                    }\n                });\n                return !!result;\n            },\n            some: function(obj, test) {\n                var result = false;\n                if (!obj) {\n                    return result;\n                }\n                $.each(obj, function(key, val) {\n                    if (result = test.call(null, val, key, obj)) {\n                        return false;\n                    }\n                });\n                return !!result;\n            },\n            mixin: $.extend,\n            identity: function(x) {\n                return x;\n            },\n            clone: function(obj) {\n                return $.extend(true, {}, obj);\n            },\n            getIdGenerator: function() {\n                var counter = 0;\n                return function() {\n                    return counter++;\n                };\n            },\n            templatify: function templatify(obj) {\n                return $.isFunction(obj) ? obj : template;\n                function template() {\n                    return String(obj);\n                }\n            },\n            defer: function(fn) {\n                setTimeout(fn, 0);\n            },\n            debounce: function(func, wait, immediate) {\n                var timeout, result;\n                return function() {\n                    var context = this, args = arguments, later, callNow;\n                    later = function() {\n                        timeout = null;\n                        if (!immediate) {\n                            result = func.apply(context, args);\n                        }\n                    };\n                    callNow = immediate && !timeout;\n                    clearTimeout(timeout);\n                    timeout = setTimeout(later, wait);\n                    if (callNow) {\n                        result = func.apply(context, args);\n                    }\n                    return result;\n                };\n            },\n            throttle: function(func, wait) {\n                var context, args, timeout, result, previous, later;\n                previous = 0;\n                later = function() {\n                    previous = new Date();\n                    timeout = null;\n                    result = func.apply(context, args);\n                };\n                return function() {\n                    var now = new Date(), remaining = wait - (now - previous);\n                    context = this;\n                    args = arguments;\n                    if (remaining <= 0) {\n                        clearTimeout(timeout);\n                        timeout = null;\n                        previous = now;\n                        result = func.apply(context, args);\n                    } else if (!timeout) {\n                        timeout = setTimeout(later, remaining);\n                    }\n                    return result;\n                };\n            },\n            stringify: function(val) {\n                return _.isString(val) ? val : JSON.stringify(val);\n            },\n            noop: function() {}\n        };\n    }();\n    var VERSION = "0.11.1";\n    var tokenizers = function() {\n        "use strict";\n        return {\n            nonword: nonword,\n            whitespace: whitespace,\n            obj: {\n                nonword: getObjTokenizer(nonword),\n                whitespace: getObjTokenizer(whitespace)\n            }\n        };\n        function whitespace(str) {\n            str = _.toStr(str);\n            return str ? str.split(/\\s+/) : [];\n        }\n        function nonword(str) {\n            str = _.toStr(str);\n            return str ? str.split(/\\W+/) : [];\n        }\n        function getObjTokenizer(tokenizer) {\n            return function setKey(keys) {\n                keys = _.isArray(keys) ? keys : [].slice.call(arguments, 0);\n                return function tokenize(o) {\n                    var tokens = [];\n                    _.each(keys, function(k) {\n                        tokens = tokens.concat(tokenizer(_.toStr(o[k])));\n                    });\n                    return tokens;\n                };\n            };\n        }\n    }();\n    var LruCache = function() {\n        "use strict";\n        function LruCache(maxSize) {\n            this.maxSize = _.isNumber(maxSize) ? maxSize : 100;\n            this.reset();\n            if (this.maxSize <= 0) {\n                this.set = this.get = $.noop;\n            }\n        }\n        _.mixin(LruCache.prototype, {\n            set: function set(key, val) {\n                var tailItem = this.list.tail, node;\n                if (this.size >= this.maxSize) {\n                    this.list.remove(tailItem);\n                    delete this.hash[tailItem.key];\n                    this.size--;\n                }\n                if (node = this.hash[key]) {\n                    node.val = val;\n                    this.list.moveToFront(node);\n                } else {\n                    node = new Node(key, val);\n                    this.list.add(node);\n                    this.hash[key] = node;\n                    this.size++;\n                }\n            },\n            get: function get(key) {\n                var node = this.hash[key];\n                if (node) {\n                    this.list.moveToFront(node);\n                    return node.val;\n                }\n            },\n            reset: function reset() {\n                this.size = 0;\n                this.hash = {};\n                this.list = new List();\n            }\n        });\n        function List() {\n            this.head = this.tail = null;\n        }\n        _.mixin(List.prototype, {\n            add: function add(node) {\n                if (this.head) {\n                    node.next = this.head;\n                    this.head.prev = node;\n                }\n                this.head = node;\n                this.tail = this.tail || node;\n            },\n            remove: function remove(node) {\n                node.prev ? node.prev.next = node.next : this.head = node.next;\n                node.next ? node.next.prev = node.prev : this.tail = node.prev;\n            },\n            moveToFront: function(node) {\n                this.remove(node);\n                this.add(node);\n            }\n        });\n        function Node(key, val) {\n            this.key = key;\n            this.val = val;\n            this.prev = this.next = null;\n        }\n        return LruCache;\n    }();\n    var PersistentStorage = function() {\n        "use strict";\n        var LOCAL_STORAGE;\n        try {\n            LOCAL_STORAGE = window.localStorage;\n            LOCAL_STORAGE.setItem("~~~", "!");\n            LOCAL_STORAGE.removeItem("~~~");\n        } catch (err) {\n            LOCAL_STORAGE = null;\n        }\n        function PersistentStorage(namespace, override) {\n            this.prefix = [ "__", namespace, "__" ].join("");\n            this.ttlKey = "__ttl__";\n            this.keyMatcher = new RegExp("^" + _.escapeRegExChars(this.prefix));\n            this.ls = override || LOCAL_STORAGE;\n            !this.ls && this._noop();\n        }\n        _.mixin(PersistentStorage.prototype, {\n            _prefix: function(key) {\n                return this.prefix + key;\n            },\n            _ttlKey: function(key) {\n                return this._prefix(key) + this.ttlKey;\n            },\n            _noop: function() {\n                this.get = this.set = this.remove = this.clear = this.isExpired = _.noop;\n            },\n            _safeSet: function(key, val) {\n                try {\n                    this.ls.setItem(key, val);\n                } catch (err) {\n                    if (err.name === "QuotaExceededError") {\n                        this.clear();\n                        this._noop();\n                    }\n                }\n            },\n            get: function(key) {\n                if (this.isExpired(key)) {\n                    this.remove(key);\n                }\n                return decode(this.ls.getItem(this._prefix(key)));\n            },\n            set: function(key, val, ttl) {\n                if (_.isNumber(ttl)) {\n                    this._safeSet(this._ttlKey(key), encode(now() + ttl));\n                } else {\n                    this.ls.removeItem(this._ttlKey(key));\n                }\n                return this._safeSet(this._prefix(key), encode(val));\n            },\n            remove: function(key) {\n                this.ls.removeItem(this._ttlKey(key));\n                this.ls.removeItem(this._prefix(key));\n                return this;\n            },\n            clear: function() {\n                var i, keys = gatherMatchingKeys(this.keyMatcher);\n                for (i = keys.length; i--; ) {\n                    this.remove(keys[i]);\n                }\n                return this;\n            },\n            isExpired: function(key) {\n                var ttl = decode(this.ls.getItem(this._ttlKey(key)));\n                return _.isNumber(ttl) && now() > ttl ? true : false;\n            }\n        });\n        return PersistentStorage;\n        function now() {\n            return new Date().getTime();\n        }\n        function encode(val) {\n            return JSON.stringify(_.isUndefined(val) ? null : val);\n        }\n        function decode(val) {\n            return $.parseJSON(val);\n        }\n        function gatherMatchingKeys(keyMatcher) {\n            var i, key, keys = [], len = LOCAL_STORAGE.length;\n            for (i = 0; i < len; i++) {\n                if ((key = LOCAL_STORAGE.key(i)).match(keyMatcher)) {\n                    keys.push(key.replace(keyMatcher, ""));\n                }\n            }\n            return keys;\n        }\n    }();\n    var Transport = function() {\n        "use strict";\n        var pendingRequestsCount = 0, pendingRequests = {}, maxPendingRequests = 6, sharedCache = new LruCache(10);\n        function Transport(o) {\n            o = o || {};\n            this.cancelled = false;\n            this.lastReq = null;\n            this._send = o.transport;\n            this._get = o.limiter ? o.limiter(this._get) : this._get;\n            this._cache = o.cache === false ? new LruCache(0) : sharedCache;\n        }\n        Transport.setMaxPendingRequests = function setMaxPendingRequests(num) {\n            maxPendingRequests = num;\n        };\n        Transport.resetCache = function resetCache() {\n            sharedCache.reset();\n        };\n        _.mixin(Transport.prototype, {\n            _fingerprint: function fingerprint(o) {\n                o = o || {};\n                return o.url + o.type + $.param(o.data || {});\n            },\n            _get: function(o, cb) {\n                var that = this, fingerprint, jqXhr;\n                fingerprint = this._fingerprint(o);\n                if (this.cancelled || fingerprint !== this.lastReq) {\n                    return;\n                }\n                if (jqXhr = pendingRequests[fingerprint]) {\n                    jqXhr.done(done).fail(fail);\n                } else if (pendingRequestsCount < maxPendingRequests) {\n                    pendingRequestsCount++;\n                    pendingRequests[fingerprint] = this._send(o).done(done).fail(fail).always(always);\n                } else {\n                    this.onDeckRequestArgs = [].slice.call(arguments, 0);\n                }\n                function done(resp) {\n                    cb(null, resp);\n                    that._cache.set(fingerprint, resp);\n                }\n                function fail() {\n                    cb(true);\n                }\n                function always() {\n                    pendingRequestsCount--;\n                    delete pendingRequests[fingerprint];\n                    if (that.onDeckRequestArgs) {\n                        that._get.apply(that, that.onDeckRequestArgs);\n                        that.onDeckRequestArgs = null;\n                    }\n                }\n            },\n            get: function(o, cb) {\n                var resp, fingerprint;\n                cb = cb || $.noop;\n                o = _.isString(o) ? {\n                    url: o\n                } : o || {};\n                fingerprint = this._fingerprint(o);\n                this.cancelled = false;\n                this.lastReq = fingerprint;\n                if (resp = this._cache.get(fingerprint)) {\n                    cb(null, resp);\n                } else {\n                    this._get(o, cb);\n                }\n            },\n            cancel: function() {\n                this.cancelled = true;\n            }\n        });\n        return Transport;\n    }();\n    var SearchIndex = window.SearchIndex = function() {\n        "use strict";\n        var CHILDREN = "c", IDS = "i";\n        function SearchIndex(o) {\n            o = o || {};\n            if (!o.datumTokenizer || !o.queryTokenizer) {\n                $.error("datumTokenizer and queryTokenizer are both required");\n            }\n            this.identify = o.identify || _.stringify;\n            this.datumTokenizer = o.datumTokenizer;\n            this.queryTokenizer = o.queryTokenizer;\n            this.reset();\n        }\n        _.mixin(SearchIndex.prototype, {\n            bootstrap: function bootstrap(o) {\n                this.datums = o.datums;\n                this.trie = o.trie;\n            },\n            add: function(data) {\n                var that = this;\n                data = _.isArray(data) ? data : [ data ];\n                _.each(data, function(datum) {\n                    var id, tokens;\n                    that.datums[id = that.identify(datum)] = datum;\n                    tokens = normalizeTokens(that.datumTokenizer(datum));\n                    _.each(tokens, function(token) {\n                        var node, chars, ch;\n                        node = that.trie;\n                        chars = token.split("");\n                        while (ch = chars.shift()) {\n                            node = node[CHILDREN][ch] || (node[CHILDREN][ch] = newNode());\n                            node[IDS].push(id);\n                        }\n                    });\n                });\n            },\n            get: function get(ids) {\n                var that = this;\n                return _.map(ids, function(id) {\n                    return that.datums[id];\n                });\n            },\n            search: function search(query) {\n                var that = this, tokens, matches;\n                tokens = normalizeTokens(this.queryTokenizer(query));\n                _.each(tokens, function(token) {\n                    var node, chars, ch, ids;\n                    if (matches && matches.length === 0) {\n                        return false;\n                    }\n                    node = that.trie;\n                    chars = token.split("");\n                    while (node && (ch = chars.shift())) {\n                        node = node[CHILDREN][ch];\n                    }\n                    if (node && chars.length === 0) {\n                        ids = node[IDS].slice(0);\n                        matches = matches ? getIntersection(matches, ids) : ids;\n                    } else {\n                        matches = [];\n                        return false;\n                    }\n                });\n                return matches ? _.map(unique(matches), function(id) {\n                    return that.datums[id];\n                }) : [];\n            },\n            all: function all() {\n                var values = [];\n                for (var key in this.datums) {\n                    values.push(this.datums[key]);\n                }\n                return values;\n            },\n            reset: function reset() {\n                this.datums = {};\n                this.trie = newNode();\n            },\n            serialize: function serialize() {\n                return {\n                    datums: this.datums,\n                    trie: this.trie\n                };\n            }\n        });\n        return SearchIndex;\n        function normalizeTokens(tokens) {\n            tokens = _.filter(tokens, function(token) {\n                return !!token;\n            });\n            tokens = _.map(tokens, function(token) {\n                return token.toLowerCase();\n            });\n            return tokens;\n        }\n        function newNode() {\n            var node = {};\n            node[IDS] = [];\n            node[CHILDREN] = {};\n            return node;\n        }\n        function unique(array) {\n            var seen = {}, uniques = [];\n            for (var i = 0, len = array.length; i < len; i++) {\n                if (!seen[array[i]]) {\n                    seen[array[i]] = true;\n                    uniques.push(array[i]);\n                }\n            }\n            return uniques;\n        }\n        function getIntersection(arrayA, arrayB) {\n            var ai = 0, bi = 0, intersection = [];\n            arrayA = arrayA.sort();\n            arrayB = arrayB.sort();\n            var lenArrayA = arrayA.length, lenArrayB = arrayB.length;\n            while (ai < lenArrayA && bi < lenArrayB) {\n                if (arrayA[ai] < arrayB[bi]) {\n                    ai++;\n                } else if (arrayA[ai] > arrayB[bi]) {\n                    bi++;\n                } else {\n                    intersection.push(arrayA[ai]);\n                    ai++;\n                    bi++;\n                }\n            }\n            return intersection;\n        }\n    }();\n    var Prefetch = function() {\n        "use strict";\n        var keys;\n        keys = {\n            data: "data",\n            protocol: "protocol",\n            thumbprint: "thumbprint"\n        };\n        function Prefetch(o) {\n            this.url = o.url;\n            this.ttl = o.ttl;\n            this.cache = o.cache;\n            this.prepare = o.prepare;\n            this.transform = o.transform;\n            this.transport = o.transport;\n            this.thumbprint = o.thumbprint;\n            this.storage = new PersistentStorage(o.cacheKey);\n        }\n        _.mixin(Prefetch.prototype, {\n            _settings: function settings() {\n                return {\n                    url: this.url,\n                    type: "GET",\n                    dataType: "json"\n                };\n            },\n            store: function store(data) {\n                if (!this.cache) {\n                    return;\n                }\n                this.storage.set(keys.data, data, this.ttl);\n                this.storage.set(keys.protocol, location.protocol, this.ttl);\n                this.storage.set(keys.thumbprint, this.thumbprint, this.ttl);\n            },\n            fromCache: function fromCache() {\n                var stored = {}, isExpired;\n                if (!this.cache) {\n                    return null;\n                }\n                stored.data = this.storage.get(keys.data);\n                stored.protocol = this.storage.get(keys.protocol);\n                stored.thumbprint = this.storage.get(keys.thumbprint);\n                isExpired = stored.thumbprint !== this.thumbprint || stored.protocol !== location.protocol;\n                return stored.data && !isExpired ? stored.data : null;\n            },\n            fromNetwork: function(cb) {\n                var that = this, settings;\n                if (!cb) {\n                    return;\n                }\n                settings = this.prepare(this._settings());\n                this.transport(settings).fail(onError).done(onResponse);\n                function onError() {\n                    cb(true);\n                }\n                function onResponse(resp) {\n                    cb(null, that.transform(resp));\n                }\n            },\n            clear: function clear() {\n                this.storage.clear();\n                return this;\n            }\n        });\n        return Prefetch;\n    }();\n    var Remote = function() {\n        "use strict";\n        function Remote(o) {\n            this.url = o.url;\n            this.prepare = o.prepare;\n            this.transform = o.transform;\n            this.transport = new Transport({\n                cache: o.cache,\n                limiter: o.limiter,\n                transport: o.transport\n            });\n        }\n        _.mixin(Remote.prototype, {\n            _settings: function settings() {\n                return {\n                    url: this.url,\n                    type: "GET",\n                    dataType: "json"\n                };\n            },\n            get: function get(query, cb) {\n                var that = this, settings;\n                if (!cb) {\n                    return;\n                }\n                query = query || "";\n                settings = this.prepare(query, this._settings());\n                return this.transport.get(settings, onResponse);\n                function onResponse(err, resp) {\n                    err ? cb([]) : cb(that.transform(resp));\n                }\n            },\n            cancelLastRequest: function cancelLastRequest() {\n                this.transport.cancel();\n            }\n        });\n        return Remote;\n    }();\n    var oParser = function() {\n        "use strict";\n        return function parse(o) {\n            var defaults, sorter;\n            defaults = {\n                initialize: true,\n                identify: _.stringify,\n                datumTokenizer: null,\n                queryTokenizer: null,\n                sufficient: 5,\n                sorter: null,\n                local: [],\n                prefetch: null,\n                remote: null\n            };\n            o = _.mixin(defaults, o || {});\n            !o.datumTokenizer && $.error("datumTokenizer is required");\n            !o.queryTokenizer && $.error("queryTokenizer is required");\n            sorter = o.sorter;\n            o.sorter = sorter ? function(x) {\n                return x.sort(sorter);\n            } : _.identity;\n            o.local = _.isFunction(o.local) ? o.local() : o.local;\n            o.prefetch = parsePrefetch(o.prefetch);\n            o.remote = parseRemote(o.remote);\n            return o;\n        };\n        function parsePrefetch(o) {\n            var defaults;\n            if (!o) {\n                return null;\n            }\n            defaults = {\n                url: null,\n                ttl: 24 * 60 * 60 * 1e3,\n                cache: true,\n                cacheKey: null,\n                thumbprint: "",\n                prepare: _.identity,\n                transform: _.identity,\n                transport: null\n            };\n            o = _.isString(o) ? {\n                url: o\n            } : o;\n            o = _.mixin(defaults, o);\n            !o.url && $.error("prefetch requires url to be set");\n            o.transform = o.filter || o.transform;\n            o.cacheKey = o.cacheKey || o.url;\n            o.thumbprint = VERSION + o.thumbprint;\n            o.transport = o.transport ? callbackToDeferred(o.transport) : $.ajax;\n            return o;\n        }\n        function parseRemote(o) {\n            var defaults;\n            if (!o) {\n                return;\n            }\n            defaults = {\n                url: null,\n                cache: true,\n                prepare: null,\n                replace: null,\n                wildcard: null,\n                limiter: null,\n                rateLimitBy: "debounce",\n                rateLimitWait: 300,\n                transform: _.identity,\n                transport: null\n            };\n            o = _.isString(o) ? {\n                url: o\n            } : o;\n            o = _.mixin(defaults, o);\n            !o.url && $.error("remote requires url to be set");\n            o.transform = o.filter || o.transform;\n            o.prepare = toRemotePrepare(o);\n            o.limiter = toLimiter(o);\n            o.transport = o.transport ? callbackToDeferred(o.transport) : $.ajax;\n            delete o.replace;\n            delete o.wildcard;\n            delete o.rateLimitBy;\n            delete o.rateLimitWait;\n            return o;\n        }\n        function toRemotePrepare(o) {\n            var prepare, replace, wildcard;\n            prepare = o.prepare;\n            replace = o.replace;\n            wildcard = o.wildcard;\n            if (prepare) {\n                return prepare;\n            }\n            if (replace) {\n                prepare = prepareByReplace;\n            } else if (o.wildcard) {\n                prepare = prepareByWildcard;\n            } else {\n                prepare = idenityPrepare;\n            }\n            return prepare;\n            function prepareByReplace(query, settings) {\n                settings.url = replace(settings.url, query);\n                return settings;\n            }\n            function prepareByWildcard(query, settings) {\n                settings.url = settings.url.replace(wildcard, encodeURIComponent(query));\n                return settings;\n            }\n            function idenityPrepare(query, settings) {\n                return settings;\n            }\n        }\n        function toLimiter(o) {\n            var limiter, method, wait;\n            limiter = o.limiter;\n            method = o.rateLimitBy;\n            wait = o.rateLimitWait;\n            if (!limiter) {\n                limiter = /^throttle$/i.test(method) ? throttle(wait) : debounce(wait);\n            }\n            return limiter;\n            function debounce(wait) {\n                return function debounce(fn) {\n                    return _.debounce(fn, wait);\n                };\n            }\n            function throttle(wait) {\n                return function throttle(fn) {\n                    return _.throttle(fn, wait);\n                };\n            }\n        }\n        function callbackToDeferred(fn) {\n            return function wrapper(o) {\n                var deferred = $.Deferred();\n                fn(o, onSuccess, onError);\n                return deferred;\n                function onSuccess(resp) {\n                    _.defer(function() {\n                        deferred.resolve(resp);\n                    });\n                }\n                function onError(err) {\n                    _.defer(function() {\n                        deferred.reject(err);\n                    });\n                }\n            };\n        }\n    }();\n    var Bloodhound = function() {\n        "use strict";\n        var old;\n        old = window && window.Bloodhound;\n        function Bloodhound(o) {\n            o = oParser(o);\n            this.sorter = o.sorter;\n            this.identify = o.identify;\n            this.sufficient = o.sufficient;\n            this.local = o.local;\n            this.remote = o.remote ? new Remote(o.remote) : null;\n            this.prefetch = o.prefetch ? new Prefetch(o.prefetch) : null;\n            this.index = new SearchIndex({\n                identify: this.identify,\n                datumTokenizer: o.datumTokenizer,\n                queryTokenizer: o.queryTokenizer\n            });\n            o.initialize !== false && this.initialize();\n        }\n        Bloodhound.noConflict = function noConflict() {\n            window && (window.Bloodhound = old);\n            return Bloodhound;\n        };\n        Bloodhound.tokenizers = tokenizers;\n        _.mixin(Bloodhound.prototype, {\n            __ttAdapter: function ttAdapter() {\n                var that = this;\n                return this.remote ? withAsync : withoutAsync;\n                function withAsync(query, sync, async) {\n                    return that.search(query, sync, async);\n                }\n                function withoutAsync(query, sync) {\n                    return that.search(query, sync);\n                }\n            },\n            _loadPrefetch: function loadPrefetch() {\n                var that = this, deferred, serialized;\n                deferred = $.Deferred();\n                if (!this.prefetch) {\n                    deferred.resolve();\n                } else if (serialized = this.prefetch.fromCache()) {\n                    this.index.bootstrap(serialized);\n                    deferred.resolve();\n                } else {\n                    this.prefetch.fromNetwork(done);\n                }\n                return deferred.promise();\n                function done(err, data) {\n                    if (err) {\n                        return deferred.reject();\n                    }\n                    that.add(data);\n                    that.prefetch.store(that.index.serialize());\n                    deferred.resolve();\n                }\n            },\n            _initialize: function initialize() {\n                var that = this, deferred;\n                this.clear();\n                (this.initPromise = this._loadPrefetch()).done(addLocalToIndex);\n                return this.initPromise;\n                function addLocalToIndex() {\n                    that.add(that.local);\n                }\n            },\n            initialize: function initialize(force) {\n                return !this.initPromise || force ? this._initialize() : this.initPromise;\n            },\n            add: function add(data) {\n                this.index.add(data);\n                return this;\n            },\n            get: function get(ids) {\n                ids = _.isArray(ids) ? ids : [].slice.call(arguments);\n                return this.index.get(ids);\n            },\n            search: function search(query, sync, async) {\n                var that = this, local;\n                local = this.sorter(this.index.search(query));\n                sync(this.remote ? local.slice() : local);\n                if (this.remote && local.length < this.sufficient) {\n                    this.remote.get(query, processRemote);\n                } else if (this.remote) {\n                    this.remote.cancelLastRequest();\n                }\n                return this;\n                function processRemote(remote) {\n                    var nonDuplicates = [];\n                    _.each(remote, function(r) {\n                        !_.some(local, function(l) {\n                            return that.identify(r) === that.identify(l);\n                        }) && nonDuplicates.push(r);\n                    });\n                    async && async(nonDuplicates);\n                }\n            },\n            all: function all() {\n                return this.index.all();\n            },\n            clear: function clear() {\n                this.index.reset();\n                return this;\n            },\n            clearPrefetchCache: function clearPrefetchCache() {\n                this.prefetch && this.prefetch.clear();\n                return this;\n            },\n            clearRemoteCache: function clearRemoteCache() {\n                Transport.resetCache();\n                return this;\n            },\n            ttAdapter: function ttAdapter() {\n                return this.__ttAdapter();\n            }\n        });\n        return Bloodhound;\n    }();\n    return Bloodhound;\n});\n\n//# sourceURL=webpack://Materialize/./node_modules/typeahead.js/dist/bloodhound.js?')},jquery:function(n){"use strict";n.exports=__WEBPACK_EXTERNAL_MODULE_jquery__}},__webpack_module_cache__={};function __webpack_require__(n){var e=__webpack_module_cache__[n];if(void 0!==e)return e.exports;var t=__webpack_module_cache__[n]={exports:{}};return __webpack_modules__[n].call(t.exports,t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return __webpack_require__.d(e,{a:e}),e},__webpack_require__.d=function(n,e){for(var t in e)__webpack_require__.o(e,t)&&!__webpack_require__.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},__webpack_require__.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},__webpack_require__.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/bloodhound/bloodhound.js");return __webpack_exports__}()}));