!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var o in n)("object"==typeof exports?exports:e)[o]=n[o]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./js/template-customizer.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCustomizer: function() { return /* binding */ TemplateCustomizer; }\n/* harmony export */ });\n/* harmony import */ var _template_customizer_template_customizer_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_template-customizer/_template-customizer.scss */ \"./js/_template-customizer/_template-customizer.scss\");\n/* harmony import */ var _template_customizer_template_customizer_html__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_template-customizer/_template-customizer.html */ \"./js/_template-customizer/_template-customizer.html\");\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n\n\nvar CONTROLS = ['color', 'theme', 'skins', 'semiDark', 'contentLayout', 'headerType', 'layoutCollapsed', 'layoutNavbarOptions', 'rtl', 'layoutFooterFixed', 'showDropdownOnHover'];\nvar THEMES = ['light', 'dark', 'system'];\nvar layoutNavbarVar;\nvar cl = document.documentElement.classList;\nif (cl.contains('layout-navbar-fixed')) layoutNavbarVar = 'sticky';else if (cl.contains('layout-navbar-hidden')) layoutNavbarVar = 'hidden';else layoutNavbarVar = 'static';\nvar DISPLAY_CUSTOMIZER = true;\nvar DEFAULT_THEME = document.documentElement.getAttribute('data-bs-theme') || 'light';\nvar DEFAULT_SKIN = document.getElementsByTagName('HTML')[0].getAttribute('data-skin') || 0;\nvar DEFAULT_CONTENT_LAYOUT = cl.contains('layout-wide') ? 'wide' : 'compact';\nvar headerType;\nif (cl.contains('layout-menu-offcanvas')) {\n  headerType = 'static-offcanvas';\n} else if (cl.contains('layout-menu-fixed')) {\n  headerType = 'fixed';\n} else if (cl.contains('layout-menu-fixed-offcanvas')) {\n  headerType = 'fixed-offcanvas';\n} else {\n  headerType = 'static';\n}\nvar DEFAULT_HEADER_TYPE = headerType;\nvar DEFAULT_MENU_COLLAPSED = !!cl.contains('layout-menu-collapsed');\nvar DEFAULT_NAVBAR_FIXED = layoutNavbarVar;\nvar DEFAULT_TEXT_DIR = document.documentElement.getAttribute('dir') === 'rtl';\nvar DEFAULT_FOOTER_FIXED = !!cl.contains('layout-footer-fixed');\nvar DEFAULT_SHOW_DROPDOWN_ON_HOVER = true;\nvar primaryColorFlag;\nvar rootStyles = getComputedStyle(document.documentElement);\nvar TemplateCustomizer = /*#__PURE__*/function () {\n  function TemplateCustomizer(_ref) {\n    var displayCustomizer = _ref.displayCustomizer,\n      lang = _ref.lang,\n      defaultPrimaryColor = _ref.defaultPrimaryColor,\n      defaultSkin = _ref.defaultSkin,\n      defaultTheme = _ref.defaultTheme,\n      defaultSemiDark = _ref.defaultSemiDark,\n      defaultContentLayout = _ref.defaultContentLayout,\n      defaultHeaderType = _ref.defaultHeaderType,\n      defaultMenuCollapsed = _ref.defaultMenuCollapsed,\n      defaultNavbarType = _ref.defaultNavbarType,\n      defaultTextDir = _ref.defaultTextDir,\n      defaultFooterFixed = _ref.defaultFooterFixed,\n      defaultShowDropdownOnHover = _ref.defaultShowDropdownOnHover,\n      controls = _ref.controls,\n      themes = _ref.themes,\n      availableColors = _ref.availableColors,\n      availableSkins = _ref.availableSkins,\n      availableThemes = _ref.availableThemes,\n      availableContentLayouts = _ref.availableContentLayouts,\n      availableHeaderTypes = _ref.availableHeaderTypes,\n      availableMenuCollapsed = _ref.availableMenuCollapsed,\n      availableNavbarOptions = _ref.availableNavbarOptions,\n      availableDirections = _ref.availableDirections,\n      onSettingsChange = _ref.onSettingsChange;\n    _classCallCheck(this, TemplateCustomizer);\n    if (this._ssr) return;\n    if (!window.Helpers) throw new Error('window.Helpers required.');\n    this.settings = {};\n    this.settings.displayCustomizer = typeof displayCustomizer !== 'undefined' ? displayCustomizer : DISPLAY_CUSTOMIZER;\n    this.settings.lang = lang || 'en';\n    if (defaultPrimaryColor) {\n      this.settings.defaultPrimaryColor = defaultPrimaryColor;\n      primaryColorFlag = true;\n    } else {\n      this.settings.defaultPrimaryColor = rootStyles.getPropertyValue('--bs-primary').trim();\n      primaryColorFlag = false;\n    }\n    this.settings.defaultTheme = defaultTheme || DEFAULT_THEME;\n    this.settings.defaultSemiDark = typeof defaultSemiDark !== 'undefined' ? defaultSemiDark : false;\n    this.settings.defaultContentLayout = typeof defaultContentLayout !== 'undefined' ? defaultContentLayout : DEFAULT_CONTENT_LAYOUT;\n    this.settings.defaultHeaderType = defaultHeaderType || DEFAULT_HEADER_TYPE;\n    this.settings.defaultMenuCollapsed = typeof defaultMenuCollapsed !== 'undefined' ? defaultMenuCollapsed : DEFAULT_MENU_COLLAPSED;\n    this.settings.defaultNavbarType = typeof defaultNavbarType !== 'undefined' ? defaultNavbarType : DEFAULT_NAVBAR_FIXED;\n    this.settings.defaultTextDir = defaultTextDir === 'rtl' ? true :  false || DEFAULT_TEXT_DIR;\n    this.settings.defaultFooterFixed = typeof defaultFooterFixed !== 'undefined' ? defaultFooterFixed : DEFAULT_FOOTER_FIXED;\n    this.settings.defaultShowDropdownOnHover = typeof defaultShowDropdownOnHover !== 'undefined' ? defaultShowDropdownOnHover : DEFAULT_SHOW_DROPDOWN_ON_HOVER;\n    this.settings.controls = controls || CONTROLS;\n    this.settings.availableColors = availableColors || TemplateCustomizer.COLORS;\n    this.settings.availableSkins = availableSkins || TemplateCustomizer.SKINS;\n    this.settings.availableThemes = availableThemes || TemplateCustomizer.THEMES;\n    this.settings.availableContentLayouts = availableContentLayouts || TemplateCustomizer.CONTENT;\n    this.settings.availableHeaderTypes = availableHeaderTypes || TemplateCustomizer.HEADER_TYPES;\n    this.settings.availableMenuCollapsed = availableMenuCollapsed || TemplateCustomizer.LAYOUTS;\n    this.settings.availableNavbarOptions = availableNavbarOptions || TemplateCustomizer.NAVBAR_OPTIONS;\n    this.settings.availableDirections = availableDirections || TemplateCustomizer.DIRECTIONS;\n    this.settings.defaultSkin = this._getDefaultSkin(typeof defaultSkin !== 'undefined' ? defaultSkin : DEFAULT_SKIN);\n    this.settings.themes = themes || THEMES;\n    if (this.settings.themes.length < 2) {\n      var i = this.settings.controls.indexOf('theme');\n      if (i !== -1) {\n        this.settings.controls = this.settings.controls.slice(0, i).concat(this.settings.controls.slice(i + 1));\n      }\n    }\n    this.settings.onSettingsChange = typeof onSettingsChange === 'function' ? onSettingsChange : function () {};\n    this._loadSettings();\n    this._listeners = [];\n    this._controls = {};\n    this._initDirection();\n    this.setContentLayout(this.settings.contentLayout, false);\n    this.setHeaderType(this.settings.headerType, false);\n    this.setLayoutNavbarOption(this.settings.layoutNavbarOptions, false);\n    this.setLayoutFooterFixed(this.settings.layoutFooterFixed, false);\n    this.setDropdownOnHover(this.settings.showDropdownOnHover, false);\n    this._setup();\n  }\n  return _createClass(TemplateCustomizer, [{\n    key: \"setColor\",\n    value: function setColor(color) {\n      var defaultChange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      // Use Helpers method\n      window.Helpers.setColor(color, defaultChange);\n    }\n  }, {\n    key: \"setTheme\",\n    value: function setTheme(theme) {\n      this._setSetting('Theme', theme);\n    }\n  }, {\n    key: \"setSkin\",\n    value: function setSkin(skinName) {\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      var cb = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!this._hasControls('skins')) return;\n      var skin = this._getSkinByName(skinName);\n      if (!skin) return;\n      this.settings.skin = skin;\n      if (updateStorage) this._setSetting('Skin', skinName);\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n    }\n  }, {\n    key: \"setLayoutNavbarOption\",\n    value: function setLayoutNavbarOption(navbarType) {\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      if (!this._hasControls('layoutNavbarOptions')) return;\n      this.settings.layoutNavbarOptions = navbarType;\n      if (updateStorage) this._setSetting('FixedNavbarOption', navbarType);\n      window.Helpers.setNavbar(navbarType);\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n    }\n  }, {\n    key: \"setContentLayout\",\n    value: function setContentLayout(contentLayout) {\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      if (!this._hasControls('contentLayout')) return;\n      this.settings.contentLayout = contentLayout;\n      if (updateStorage) this._setSetting('contentLayout', contentLayout);\n      window.Helpers.setContentLayout(contentLayout);\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n    }\n  }, {\n    key: \"setHeaderType\",\n    value: function setHeaderType(pos) {\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      if (!this._hasControls('headerType')) return;\n      if (!['static', 'static-offcanvas', 'fixed', 'fixed-offcanvas'].includes(pos)) return;\n      this.settings.headerType = pos;\n      if (updateStorage) this._setSetting('HeaderType', pos);\n      window.Helpers.setPosition(pos === 'fixed' || pos === 'fixed-offcanvas', pos === 'static-offcanvas' || pos === 'fixed-offcanvas');\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n\n      // Perfect Scrollbar change on Layout change\n      var menuScroll = window.Helpers.menuPsScroll;\n      var PerfectScrollbarLib = window.PerfectScrollbar;\n      if (this.settings.headerType === 'fixed' || this.settings.headerType === 'fixed-offcanvas') {\n        // Set perfect scrollbar wheelPropagation false for fixed layout\n        if (PerfectScrollbarLib && menuScroll) {\n          window.Helpers.menuPsScroll.destroy();\n          menuScroll = new PerfectScrollbarLib(document.querySelector('.menu-inner'), {\n            suppressScrollX: true,\n            wheelPropagation: false\n          });\n          window.Helpers.menuPsScroll = menuScroll;\n        }\n      } else if (menuScroll) {\n        // Destroy perfect scrollbar for static layout\n        window.Helpers.menuPsScroll.destroy();\n      }\n    }\n  }, {\n    key: \"setLayoutFooterFixed\",\n    value: function setLayoutFooterFixed(fixed) {\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      // if (!this._hasControls('layoutFooterFixed')) return\n      this.settings.layoutFooterFixed = fixed;\n      if (updateStorage) this._setSetting('FixedFooter', fixed);\n      window.Helpers.setFooterFixed(fixed);\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n    }\n  }, {\n    key: \"setDropdownOnHover\",\n    value: function setDropdownOnHover(open) {\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      if (!this._hasControls('showDropdownOnHover')) return;\n      this.settings.showDropdownOnHover = open;\n      if (updateStorage) this._setSetting('ShowDropdownOnHover', open);\n      if (window.Helpers.mainMenu) {\n        window.Helpers.mainMenu.destroy();\n        config.showDropdownOnHover = open;\n        var _window = window,\n          Menu = _window.Menu;\n        window.Helpers.mainMenu = new Menu(document.getElementById('layout-menu'), {\n          orientation: 'horizontal',\n          closeChildren: true,\n          showDropdownOnHover: config.showDropdownOnHover\n        });\n      }\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n    }\n  }, {\n    key: \"setRtl\",\n    value: function setRtl(rtl) {\n      if (!this._hasControls('rtl')) return;\n      this._setSetting('Rtl', String(rtl));\n    }\n  }, {\n    key: \"setLang\",\n    value: function setLang(lang) {\n      var _this = this;\n      var updateStorage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      var force = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      if (lang === this.settings.lang && !force) return;\n      if (!TemplateCustomizer.LANGUAGES[lang]) throw new Error(\"Language \\\"\".concat(lang, \"\\\" not found!\"));\n      var t = TemplateCustomizer.LANGUAGES[lang];\n      ['panel_header', 'panel_sub_header', 'theming_header', 'color_label', 'theme_label', 'style_switch_light', 'style_switch_dark', 'layout_header', 'layout_label', 'layout_header_label', 'content_label', 'layout_static', 'layout_offcanvas', 'layout_fixed', 'layout_fixed_offcanvas', 'layout_dd_open_label', 'layout_navbar_label', 'layout_footer_label', 'misc_header', 'skin_label', 'semiDark_label', 'direction_label'].forEach(function (key) {\n        var el = _this.container.querySelector(\".template-customizer-t-\".concat(key));\n        // eslint-disable-next-line no-unused-expressions\n        el && (el.textContent = t[key]);\n      });\n      this.settings.lang = lang;\n      if (updateStorage) this._setSetting('Lang', lang);\n      if (updateStorage) this.settings.onSettingsChange.call(this, this.settings);\n    }\n\n    // Update theme settings control\n  }, {\n    key: \"update\",\n    value: function update() {\n      if (this._ssr) return;\n      var hasNavbar = !!document.querySelector('.layout-navbar');\n      var hasMenu = !!document.querySelector('.layout-menu');\n      var hasHorizontalMenu = !!document.querySelector('.layout-menu-horizontal.menu, .layout-menu-horizontal .menu');\n      var hasFooter = !!document.querySelector('.content-footer');\n      if (this._controls.showDropdownOnHover) {\n        if (hasMenu) {\n          this._controls.showDropdownOnHover.setAttribute('disabled', 'disabled');\n          this._controls.showDropdownOnHover.classList.add('disabled');\n        } else {\n          this._controls.showDropdownOnHover.removeAttribute('disabled');\n          this._controls.showDropdownOnHover.classList.remove('disabled');\n        }\n      }\n      if (this._controls.layoutNavbarOptions) {\n        if (!hasNavbar) {\n          this._controls.layoutNavbarOptions.setAttribute('disabled', 'disabled');\n          this._controls.layoutNavbarOptionsW.classList.add('disabled');\n        } else {\n          this._controls.layoutNavbarOptions.removeAttribute('disabled');\n          this._controls.layoutNavbarOptionsW.classList.remove('disabled');\n        }\n\n        //  Horizontal menu fixed layout - disabled fixed navbar switch\n        if (hasHorizontalMenu && hasNavbar && this.settings.headerType === 'fixed') {\n          this._controls.layoutNavbarOptions.setAttribute('disabled', 'disabled');\n          this._controls.layoutNavbarOptionsW.classList.add('disabled');\n        }\n      }\n      if (this._controls.layoutFooterFixed) {\n        if (!hasFooter) {\n          this._controls.layoutFooterFixed.setAttribute('disabled', 'disabled');\n          this._controls.layoutFooterFixedW.classList.add('disabled');\n        } else {\n          this._controls.layoutFooterFixed.removeAttribute('disabled');\n          this._controls.layoutFooterFixedW.classList.remove('disabled');\n        }\n      }\n      if (this._controls.headerType) {\n        // Disable menu layouts options if menu (vertical or horizontal) is not there\n        if (hasMenu || hasHorizontalMenu) {\n          // (Updated condition)\n          this._controls.headerType.removeAttribute('disabled');\n        } else {\n          this._controls.headerType.setAttribute('disabled', 'disabled');\n        }\n      }\n    }\n\n    // Clear local storage\n  }, {\n    key: \"clearLocalStorage\",\n    value: function clearLocalStorage() {\n      if (this._ssr) return;\n      var layoutName = this._getLayoutName();\n      var keysToRemove = ['Color', 'Theme', 'Skin', 'SemiDark', 'LayoutCollapsed', 'FixedNavbarOption', 'HeaderType', 'contentLayout', 'Rtl', 'Lang'];\n      keysToRemove.forEach(function (key) {\n        var localStorageKey = \"templateCustomizer-\".concat(layoutName, \"--\").concat(key);\n        localStorage.removeItem(localStorageKey);\n      });\n      this._showResetBtnNotification(false);\n    }\n\n    // Clear local storage\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (this._ssr) return;\n      this._cleanup();\n      this.settings = null;\n      this.container.parentNode.removeChild(this.container);\n      this.container = null;\n    }\n  }, {\n    key: \"_loadSettings\",\n    value: function _loadSettings() {\n      // Get settings\n      var rtlOption = this._getSetting('Rtl');\n      var color = this._getSetting('Color');\n      var theme = this._getSetting('Theme');\n      var skin = this._getSetting('Skin');\n      var semiDark = this._getSetting('SemiDark'); // Default value will be set from main.js\n      var contentLayout = this._getSetting('contentLayout');\n      var collapsedMenu = this._getSetting('LayoutCollapsed'); // Value will be set from main.js\n      var dropdownOnHover = this._getSetting('ShowDropdownOnHover'); // Value will be set from main.js\n      var navbarOption = this._getSetting('FixedNavbarOption');\n      var fixedFooter = this._getSetting('FixedFooter');\n      var layoutType = this._getSetting('HeaderType');\n\n      // Reset Button\n      if (rtlOption || theme || skin || contentLayout || collapsedMenu || navbarOption || layoutType || color || semiDark) {\n        this._showResetBtnNotification(true);\n      } else {\n        this._showResetBtnNotification(false);\n      }\n\n      // Header Type\n\n      this.settings.headerType = ['static', 'static-offcanvas', 'fixed', 'fixed-offcanvas'].includes(layoutType) ? layoutType : this.settings.defaultHeaderType;\n\n      // ! Set settings by following priority: Local Storage, Theme Config, HTML Classes\n      this.settings.rtl = rtlOption !== '' ? rtlOption === 'true' : this.settings.defaultTextDir;\n\n      // Color\n      if (color) {\n        primaryColorFlag = true;\n      }\n      this.settings.color = color || this.settings.defaultPrimaryColor;\n      this.setColor(this.settings.color, primaryColorFlag);\n\n      // Style\n      this.settings.themesOpt = this.settings.themes.includes(theme) ? theme : this.settings.defaultTheme;\n\n      // Get the systemTheme value using JS\n      var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n\n      // appliedTheme will be used to set the theme based on the settings, we keep this separate as we can't set 'system' or 'system' in data-bs-theme\n      var appliedTheme;\n      if (this.settings.themes.includes(theme)) {\n        appliedTheme = theme === 'system' ? systemTheme : theme;\n      } else if (this.settings.defaultTheme === 'system') {\n        appliedTheme = systemTheme;\n      } else {\n        appliedTheme = this.settings.defaultTheme;\n      }\n      this.settings.theme = this.settings.defaultTheme;\n      document.documentElement.setAttribute('data-bs-theme', appliedTheme);\n\n      // Semi Dark\n      this.settings.semiDark = semiDark ? semiDark === 'true' : this.settings.defaultSemiDark;\n      //! FIX: Added data-semidark-menu attribute to avoid semi dark menu flicker effect on page load\n      if (this.settings.semiDark) document.documentElement.setAttribute('data-semidark-menu', this.settings.semiDark);\n\n      // Content Layout\n      this.settings.contentLayout = contentLayout || this.settings.defaultContentLayout;\n\n      // Layout Collapsed\n      this.settings.layoutCollapsed = collapsedMenu ? collapsedMenu === 'true' : this.settings.defaultMenuCollapsed;\n      // Add layout-menu-collapsed class to the body if the menu is collapsed\n      if (this.settings.layoutCollapsed) document.documentElement.classList.add('layout-menu-collapsed');\n\n      // Dropdown on Hover\n      this.settings.showDropdownOnHover = dropdownOnHover ? dropdownOnHover === 'true' : this.settings.defaultShowDropdownOnHover;\n\n      // Navbar Option\n      this.settings.layoutNavbarOptions = ['static', 'sticky', 'hidden'].includes(navbarOption) ? navbarOption : this.settings.defaultNavbarType;\n\n      // Footer Fixed\n      this.settings.layoutFooterFixed = fixedFooter ? fixedFooter === 'true' : this.settings.defaultFooterFixed;\n      this.settings.skin = this._getSkinByName(this._getSetting('Skin'), true);\n\n      // Filter options depending on available controls\n      if (!this._hasControls('rtl')) this.settings.rtl = document.documentElement.getAttribute('dir') === 'rtl';\n      if (!this._hasControls('theme')) this.settings.theme = window.Helpers.isDarkStyle() ? 'dark' : 'light';\n      if (!this._hasControls('contentLayout')) this.settings.contentLayout = null;\n      if (!this._hasControls('headerType')) this.settings.headerType = null;\n      if (!this._hasControls('layoutCollapsed')) this.settings.layoutCollapsed = null;\n      if (!this._hasControls('layoutNavbarOptions')) this.settings.layoutNavbarOptions = null;\n      if (!this._hasControls('skins')) this.settings.skin = null;\n      if (!this._hasControls('semiDark')) this.settings.semiDark = null;\n    }\n\n    // Setup theme settings controls and events\n  }, {\n    key: \"_setup\",\n    value: function _setup() {\n      var _this2 = this;\n      var _container = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n      // Function to create customizer elements\n      var createOptionElement = function createOptionElement(nameVal, title, inputName, image) {\n        var isIcon = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n        var divElement = document.createElement('div');\n        divElement.classList.add('col-4', 'px-2');\n\n        // Determine the correct classes based on whether it's an icon or image\n        var optionClass = isIcon ? 'custom-option custom-option-icon' : 'custom-option custom-option-image custom-option-image-radio';\n\n        // Create the inner HTML structure\n        divElement.innerHTML = \"\\n        <div class=\\\"form-check \".concat(optionClass, \" mb-0\\\">\\n          <label class=\\\"form-check-label custom-option-content p-0\\\" for=\\\"\").concat(inputName).concat(nameVal, \"\\\">\\n            <span class=\\\"custom-option-body mb-0 scaleX-n1-rtl\\\"></span>\\n          </label>\\n          <input\\n            name=\\\"\").concat(inputName, \"\\\"\\n            class=\\\"form-check-input d-none\\\"\\n            type=\\\"radio\\\"\\n            value=\\\"\").concat(nameVal, \"\\\"\\n            id=\\\"\").concat(inputName).concat(nameVal, \"\\\" />\\n        </div>\\n        <label class=\\\"form-check-label small text-nowrap text-body\\\" for=\\\"\").concat(inputName).concat(nameVal, \"\\\">\").concat(title, \"</label>\\n      \");\n        if (isIcon) {\n          // If it's an icon, insert the icon HTML directly\n          divElement.querySelector('.custom-option-body').innerHTML = image;\n        } else {\n          // Otherwise, assume it's an SVG file name and fetch its content\n          fetch(\"\".concat(assetsPath, \"img/customizer/\").concat(image)).then(function (response) {\n            return response.text();\n          }).then(function (svgContent) {\n            // Insert the SVG content into the HTML\n            divElement.querySelector('.custom-option-body').innerHTML = svgContent;\n          }).catch(function (error) {\n            return console.error('Error loading SVG:', error);\n          });\n        }\n        return divElement;\n      };\n      this._cleanup();\n      this.container = this._getElementFromString(_template_customizer_template_customizer_html__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n      // Customizer visibility\n      //\n      this.container.setAttribute('style', \"visibility: \".concat(this.settings.displayCustomizer ? 'visible' : 'hidden'));\n\n      // Open btn\n      var openBtn = this.container.querySelector('.template-customizer-open-btn');\n      var openBtnCb = function openBtnCb() {\n        _this2.container.classList.add('template-customizer-open');\n        _this2.update();\n        if (_this2._updateInterval) clearInterval(_this2._updateInterval);\n        _this2._updateInterval = setInterval(function () {\n          _this2.update();\n        }, 500);\n      };\n      openBtn.addEventListener('click', openBtnCb);\n      this._listeners.push([openBtn, 'click', openBtnCb]);\n\n      // Reset btn\n      var resetBtn = this.container.querySelector('.template-customizer-reset-btn');\n      var resetBtnCb = function resetBtnCb() {\n        _this2.clearLocalStorage();\n        window.location.reload();\n      };\n      resetBtn.addEventListener('click', resetBtnCb);\n      this._listeners.push([resetBtn, 'click', resetBtnCb]);\n\n      // Close btn\n      var closeBtn = this.container.querySelector('.template-customizer-close-btn');\n      var closeBtnCb = function closeBtnCb() {\n        _this2.container.classList.remove('template-customizer-open');\n        if (_this2._updateInterval) {\n          clearInterval(_this2._updateInterval);\n          _this2._updateInterval = null;\n        }\n      };\n      closeBtn.addEventListener('click', closeBtnCb);\n      this._listeners.push([closeBtn, 'click', closeBtnCb]);\n\n      // Color\n      var colorW = this.container.querySelector('.template-customizer-color');\n      var colorOpt = colorW.querySelector('.template-customizer-colors-options');\n      if (!this._hasControls('color')) {\n        colorW.parentNode.removeChild(colorW);\n      } else {\n        var inputName = 'colorRadioIcon';\n        this.settings.availableColors.forEach(function (color) {\n          var colorEl = \"<div class=\\\"form-check custom-option custom-option-icon mb-0\\\">\\n          <label class=\\\"form-check-label custom-option-content p-0\\\" for=\\\"\".concat(inputName).concat(color.name, \"\\\">\\n            <span class=\\\"custom-option-body mb-0 scaleX-n1-rtl\\\" style=\\\"background-color: \").concat(color.color, \";\\\"></span>\\n          </label>\\n          <input\\n            name=\\\"\").concat(inputName, \"\\\"\\n            class=\\\"form-check-input d-none\\\"\\n            type=\\\"radio\\\"\\n            value=\\\"\").concat(color.color, \"\\\"\\n            data-color=\\\"\").concat(color.color, \"\\\"\\n            id=\\\"\").concat(inputName).concat(color.name, \"\\\" />\\n        </div>\");\n\n          // convert colorEl string to HTML element\n          var colorEle = _this2._getElementFromString(colorEl);\n          colorOpt.appendChild(colorEle);\n        });\n        colorOpt.appendChild(this._getElementFromString('<div class=\"form-check custom-option custom-option-icon mb-0\"><label class=\"form-check-label custom-option-content\" for=\"colorRadioIcon\"><span class=\"custom-option-body customizer-nano-picker mb-50\"><i class=\"ri ri-palette-line icon-base\"></i></span></label><input name=\"colorRadioIcon\" class=\"form-check-input picker d-none\" type=\"radio\" value=\"picker\" id=\"colorRadioIcon\" /> </div>'));\n        var colorSelector = colorOpt.querySelector(\"input[value=\\\"\".concat(this.settings.color, \"\\\"]\"));\n        if (colorSelector) {\n          colorSelector.setAttribute('checked', 'checked');\n          colorOpt.querySelector('input[value=\"picker\"]').removeAttribute('checked');\n        } else {\n          colorOpt.querySelector('input[value=\"picker\"]').setAttribute('checked', 'checked');\n        }\n        var colorCb = function colorCb(e) {\n          if (e.target.value === 'picker') {\n            document.querySelector('.custom-option-content .pcr-button').click();\n          } else {\n            _this2._setSetting('Color', e.target.dataset.color);\n            _this2.setColor(e.target.dataset.color, function () {\n              _this2._loadingState(false);\n            }, true);\n          }\n        };\n        colorOpt.addEventListener('change', colorCb);\n        this._listeners.push([colorOpt, 'change', colorCb]);\n      }\n\n      // Theme\n      var themeW = this.container.querySelector('.template-customizer-theme');\n      var themeOpt = themeW.querySelector('.template-customizer-themes-options');\n      if (!this._hasControls('theme')) {\n        themeW.parentNode.removeChild(themeW);\n      } else {\n        this.settings.availableThemes.forEach(function (theme) {\n          var themeEl = createOptionElement(theme.name, theme.title, 'customRadioIcon', theme.image, true);\n          themeOpt.appendChild(themeEl);\n        });\n        if (themeOpt.querySelector(\"input[value=\\\"\".concat(this.settings.themesOpt, \"\\\"]\"))) {\n          themeOpt.querySelector(\"input[value=\\\"\".concat(this.settings.themesOpt, \"\\\"]\")).setAttribute('checked', 'checked');\n        }\n\n        // themeCb\n        var themeCb = function themeCb(e) {\n          document.documentElement.setAttribute('data-bs-theme', e.target.value);\n          if (_this2._hasControls('semiDark')) {\n            var semiDarkL = _this2.container.querySelector('.template-customizer-semiDark');\n            if (e.target.value === 'dark') {\n              semiDarkL.classList.add('d-none');\n            } else {\n              semiDarkL.classList.remove('d-none');\n            }\n          }\n\n          // Update below commented code for data-bs-theme-value attribute value matches with e.target.value\n          window.Helpers.syncThemeToggles(e.target.value);\n          _this2.setTheme(e.target.value, true, function () {\n            _this2._loadingState(false);\n          });\n        };\n        themeOpt.addEventListener('change', themeCb);\n        this._listeners.push([themeOpt, 'change', themeCb]);\n      }\n\n      // Skin\n      var skinsW = this.container.querySelector('.template-customizer-skins');\n      var skinsWInner = skinsW.querySelector('.template-customizer-skins-options');\n      if (!this._hasControls('skins')) {\n        skinsW.parentNode.removeChild(skinsW);\n      } else {\n        this.settings.availableSkins.forEach(function (skin) {\n          var skinEl = createOptionElement(skin.name, skin.title, 'skinRadios', skin.image);\n          skinsWInner.appendChild(skinEl);\n        });\n        skinsWInner.querySelector(\"input[value=\\\"\".concat(this.settings.skin.name, \"\\\"]\")).setAttribute('checked', 'checked');\n        document.documentElement.setAttribute('data-skin', this.settings.skin.name);\n        var skinCb = function skinCb(e) {\n          document.documentElement.setAttribute('data-skin', e.target.value);\n          _this2.setSkin(e.target.value, true, function () {\n            _this2._loadingState(false, true);\n          });\n        };\n        skinsWInner.addEventListener('change', skinCb);\n        this._listeners.push([skinsWInner, 'change', skinCb]);\n      }\n\n      // SemiDark\n      // update menu's data-bs-theme attribute to dark & light on switch changes on & off respectively\n      var semiDarkSwitch = this.container.querySelector('.template-customizer-semi-dark-switch');\n      var semiDarkSection = this.container.querySelector('.template-customizer-semiDark');\n      if (document.documentElement.getAttribute('data-bs-theme') === 'dark') {\n        semiDarkSection.classList.add('d-none');\n      }\n      if (!this._hasControls('semiDark')) {\n        semiDarkSection.remove();\n      } else if (this._hasControls('semiDark') && this._getSetting('Theme') === 'dark') {\n        semiDarkSwitch.classList.add('d-none');\n      } else {\n        if (this.settings.semiDark) {\n          semiDarkSwitch.setAttribute('checked', 'checked');\n        }\n        var semiDarkSwitchCb = function semiDarkSwitchCb(e) {\n          var isDark = e.target.checked;\n          var theme = isDark ? 'dark' : 'light';\n          if (theme === 'dark') {\n            document.getElementById('layout-menu').setAttribute('data-bs-theme', theme);\n            //! FIX: Added data-semidark-menu attribute to avoid semi dark menu flicker effect on page load\n            document.documentElement.setAttribute('data-semidark-menu', 'true');\n          } else {\n            document.getElementById('layout-menu').removeAttribute('data-bs-theme');\n            document.documentElement.removeAttribute('data-semidark-menu');\n          }\n          _this2._setSetting('SemiDark', isDark);\n        };\n        semiDarkSwitch.addEventListener('change', semiDarkSwitchCb);\n        this._listeners.push([semiDarkSwitch, 'change', semiDarkSwitchCb]);\n      }\n      var themingW = this.container.querySelector('.template-customizer-theming');\n      if (!this._hasControls('color') && !this._hasControls('theme') && !this._hasControls('skins') && !this._hasControls('semiDark')) {\n        themingW.parentNode.removeChild(themingW);\n      }\n\n      // Layout wrapper\n      var layoutW = this.container.querySelector('.template-customizer-layout');\n      if (!this._hasControls('contentLayout headerType layoutCollapsed layoutNavbarOptions rtl', true)) {\n        layoutW.parentNode.removeChild(layoutW);\n      } else {\n        // Layouts Collapsed: Expanded, Collapsed\n        var layoutCollapsedW = this.container.querySelector('.template-customizer-layouts');\n        if (!this._hasControls('layoutCollapsed')) {\n          layoutCollapsedW.parentNode.removeChild(layoutCollapsedW);\n        } else {\n          setTimeout(function () {\n            if (document.querySelector('.layout-menu-horizontal')) {\n              layoutCollapsedW.parentNode.removeChild(layoutCollapsedW);\n            }\n          }, 100);\n          var layoutCollapsedOpt = layoutCollapsedW.querySelector('.template-customizer-layouts-options');\n          this.settings.availableMenuCollapsed.forEach(function (layoutOpt) {\n            var layoutsEl = createOptionElement(layoutOpt.name, layoutOpt.title, 'layoutsRadios', layoutOpt.image);\n            layoutCollapsedOpt.appendChild(layoutsEl);\n          });\n          layoutCollapsedOpt.querySelector(\"input[value=\\\"\".concat(this.settings.layoutCollapsed ? 'collapsed' : 'expanded', \"\\\"]\")).setAttribute('checked', 'checked');\n          var layoutCb = function layoutCb(e) {\n            window.Helpers.setCollapsed(e.target.value === 'collapsed', true);\n            _this2._setSetting('LayoutCollapsed', e.target.value === 'collapsed');\n          };\n          layoutCollapsedOpt.addEventListener('change', layoutCb);\n          this._listeners.push([layoutCollapsedOpt, 'change', layoutCb]);\n        }\n\n        // CONTENT\n        var contentWrapper = this.container.querySelector('.template-customizer-content');\n        // ? Hide RTL control in following 2 case\n        if (!this._hasControls('contentLayout')) {\n          contentWrapper.parentNode.removeChild(contentWrapper);\n        } else {\n          var contentOpt = contentWrapper.querySelector('.template-customizer-content-options');\n          this.settings.availableContentLayouts.forEach(function (content) {\n            var contentEl = createOptionElement(content.name, content.title, 'contentRadioIcon', content.image);\n            contentOpt.appendChild(contentEl);\n          });\n          contentOpt.querySelector(\"input[value=\\\"\".concat(this.settings.contentLayout, \"\\\"]\")).setAttribute('checked', 'checked');\n          var contentCb = function contentCb(e) {\n            _this2._loading = true;\n            _this2._loadingState(true, true);\n            _this2.setContentLayout(e.target.value, true, function () {\n              _this2._loading = false;\n              _this2._loadingState(false, true);\n            });\n          };\n          contentOpt.addEventListener('change', contentCb);\n          this._listeners.push([contentOpt, 'change', contentCb]);\n        }\n\n        // Header Layout Type\n        var headerTypeW = this.container.querySelector('.template-customizer-headerOptions');\n        var templateName = document.documentElement.getAttribute('data-template').split('-');\n        if (!this._hasControls('headerType')) {\n          headerTypeW.parentNode.removeChild(headerTypeW);\n        } else {\n          var headerOpt = headerTypeW.querySelector('.template-customizer-header-options');\n          setTimeout(function () {\n            if (templateName.includes('vertical')) {\n              headerTypeW.parentNode.removeChild(headerTypeW);\n            }\n          }, 100);\n          this.settings.availableHeaderTypes.forEach(function (header) {\n            var headerEl = createOptionElement(header.name, header.title, 'headerRadioIcon', header.image);\n            headerOpt.appendChild(headerEl);\n          });\n          headerOpt.querySelector(\"input[value=\\\"\".concat(this.settings.headerType, \"\\\"]\")).setAttribute('checked', 'checked');\n          var headerTypeCb = function headerTypeCb(e) {\n            _this2.setHeaderType(e.target.value);\n          };\n          headerOpt.addEventListener('change', headerTypeCb);\n          this._listeners.push([headerOpt, 'change', headerTypeCb]);\n        }\n\n        // Layout Navbar Options\n        var navbarOption = this.container.querySelector('.template-customizer-layoutNavbarOptions');\n        if (!this._hasControls('layoutNavbarOptions')) {\n          navbarOption.parentNode.removeChild(navbarOption);\n        } else {\n          setTimeout(function () {\n            if (templateName.includes('horizontal')) {\n              navbarOption.parentNode.removeChild(navbarOption);\n            }\n          }, 100);\n          var navbarTypeOpt = navbarOption.querySelector('.template-customizer-navbar-options');\n          this.settings.availableNavbarOptions.forEach(function (navbarOpt) {\n            var navbarEl = createOptionElement(navbarOpt.name, navbarOpt.title, 'navbarOptionRadios', navbarOpt.image);\n            navbarTypeOpt.appendChild(navbarEl);\n          });\n          // check navbar option from settings\n          navbarTypeOpt.querySelector(\"input[value=\\\"\".concat(this.settings.layoutNavbarOptions, \"\\\"]\")).setAttribute('checked', 'checked');\n          var navbarCb = function navbarCb(e) {\n            _this2._loading = true;\n            _this2._loadingState(true, true);\n            _this2.setLayoutNavbarOption(e.target.value, true, function () {\n              _this2._loading = false;\n              _this2._loadingState(false, true);\n            });\n          };\n          navbarTypeOpt.addEventListener('change', navbarCb);\n          this._listeners.push([navbarTypeOpt, 'change', navbarCb]);\n        }\n\n        // RTL\n        var directionW = this.container.querySelector('.template-customizer-directions');\n        // ? Hide RTL control in following 2 case\n        if (!this._hasControls('rtl')) {\n          directionW.parentNode.removeChild(directionW);\n        } else {\n          var directionOpt = directionW.querySelector('.template-customizer-directions-options');\n          this.settings.availableDirections.forEach(function (dir) {\n            var dirEl = createOptionElement(dir.name, dir.title, 'directionRadioIcon', dir.image);\n            directionOpt.appendChild(dirEl);\n          });\n          directionOpt.querySelector(\"input[value=\\\"\".concat(this.settings.rtl ? 'rtl' : 'ltr', \"\\\"]\")).setAttribute('checked', 'checked');\n          var rtlCb = function rtlCb(e) {\n            _this2._setSetting('Lang', _this2.settings.lang);\n\n            // For demo purpose, we will use EN as LTR and AR as RTL Language\n            _this2._setSetting('Lang', _this2.settings.lang === 'ar' ? 'en' : 'ar');\n            _this2.settings.rtl = e.target.value === 'rtl';\n\n            // Cache the language setting\n            var currentLang = _this2._getSetting('Lang');\n            var languageDropdown = document.querySelector('.dropdown-language .dropdown-menu');\n            if (languageDropdown) {\n              var dropdownItem = languageDropdown.querySelector(\"[data-language=\\\"\".concat(currentLang, \"\\\"]\"));\n              dropdownItem.click();\n            }\n\n            // Use querySelector for cleaner and more flexible selection\n            _this2._initDirection();\n            _this2.setRtl(e.target.value === 'rtl', true, function () {\n              _this2._loadingState(false);\n            });\n          };\n          directionOpt.addEventListener('change', rtlCb);\n          this._listeners.push([directionOpt, 'change', rtlCb]);\n        }\n      }\n      setTimeout(function () {\n        var layoutCustom = _this2.container.querySelector('.template-customizer-layout');\n        var layoutTheme = _this2.container.querySelector('.template-customizer-theming');\n        var checkSemiDarkWrapper = document.documentElement.getAttribute('data-bs-theme');\n        var checkSemiDark = false;\n        if (checkSemiDarkWrapper === 'light' && document.querySelector('.layout-menu')) {\n          if (document.querySelector('.layout-menu').getAttribute('data-bs-theme') === 'dark') {\n            checkSemiDark = true;\n          }\n          if (checkSemiDark === true) {\n            var _semiDarkSwitch = layoutTheme.querySelector('.template-customizer-semi-dark-switch');\n            _semiDarkSwitch.setAttribute('checked', 'checked');\n          }\n        }\n        if (document.querySelector('.menu-vertical')) {\n          if (!_this2._hasControls('rtl contentLayout layoutCollapsed layoutNavbarOptions', true)) {\n            if (layoutCustom) {\n              layoutCustom.parentNode.removeChild(layoutCustom);\n            }\n          }\n        } else if (document.querySelector('.menu-horizontal')) {\n          if (!_this2._hasControls('rtl contentLayout headerType', true)) {\n            if (layoutCustom) {\n              layoutCustom.parentNode.removeChild(layoutCustom);\n            }\n          }\n        }\n      }, 100);\n\n      // Set language\n      this.setLang(this.settings.lang, false, true);\n\n      // Append container\n      if (_container === document) {\n        if (_container.body) {\n          _container.body.appendChild(this.container);\n        } else {\n          window.addEventListener('DOMContentLoaded', function () {\n            return _container.body.appendChild(_this2.container);\n          });\n        }\n      } else {\n        _container.appendChild(this.container);\n      }\n    }\n  }, {\n    key: \"_initDirection\",\n    value: function _initDirection() {\n      if (this._hasControls('rtl')) document.documentElement.setAttribute('dir', this.settings.rtl ? 'rtl' : 'ltr');\n    }\n  }, {\n    key: \"_loadingState\",\n    value: function _loadingState(enable, skins) {\n      this.container.classList[enable ? 'add' : 'remove'](\"template-customizer-loading\".concat(skins ? '-theme' : ''));\n    }\n  }, {\n    key: \"_getElementFromString\",\n    value: function _getElementFromString(str) {\n      var wrapper = document.createElement('div');\n      wrapper.innerHTML = str;\n      return wrapper.firstChild;\n    }\n\n    // Set settings in LocalStorage with layout & key\n  }, {\n    key: \"_setSetting\",\n    value: function _setSetting(key, val) {\n      var layoutName = this._getLayoutName();\n      try {\n        localStorage.setItem(\"templateCustomizer-\".concat(layoutName, \"--\").concat(key), String(val));\n        this._showResetBtnNotification();\n      } catch (e) {\n        // Catch Error\n      }\n    }\n\n    // Set settings in LocalStorage with layout & key\n  }, {\n    key: \"_getSetting\",\n    value: function _getSetting(key) {\n      var result = null;\n      var layoutName = this._getLayoutName();\n      try {\n        result = localStorage.getItem(\"templateCustomizer-\".concat(layoutName, \"--\").concat(key));\n      } catch (e) {\n        // Catch error\n      }\n      return String(result || '');\n    }\n  }, {\n    key: \"_showResetBtnNotification\",\n    value: function _showResetBtnNotification() {\n      var _this3 = this;\n      var show = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      setTimeout(function () {\n        var resetBtnAttr = _this3.container.querySelector('.template-customizer-reset-btn .badge');\n        if (show) {\n          resetBtnAttr.classList.remove('d-none');\n        } else {\n          resetBtnAttr.classList.add('d-none');\n        }\n      }, 200);\n    }\n\n    // Get layout name to set unique\n  }, {\n    key: \"_getLayoutName\",\n    value: function _getLayoutName() {\n      return document.getElementsByTagName('HTML')[0].getAttribute('data-template');\n    }\n  }, {\n    key: \"_removeListeners\",\n    value: function _removeListeners() {\n      for (var i = 0, l = this._listeners.length; i < l; i++) {\n        this._listeners[i][0].removeEventListener(this._listeners[i][1], this._listeners[i][2]);\n      }\n    }\n  }, {\n    key: \"_cleanup\",\n    value: function _cleanup() {\n      this._removeListeners();\n      this._listeners = [];\n      this._controls = {};\n      if (this._updateInterval) {\n        clearInterval(this._updateInterval);\n        this._updateInterval = null;\n      }\n    }\n  }, {\n    key: \"_ssr\",\n    get: function get() {\n      return typeof window === 'undefined';\n    }\n\n    // Check controls availability\n  }, {\n    key: \"_hasControls\",\n    value: function _hasControls(controls) {\n      var _this4 = this;\n      var oneOf = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return controls.split(' ').reduce(function (result, control) {\n        if (_this4.settings.controls.indexOf(control) !== -1) {\n          if (oneOf || result !== false) result = true;\n        } else if (!oneOf || result !== true) result = false;\n        return result;\n      }, null);\n    }\n\n    // Get the default Skin\n  }, {\n    key: \"_getDefaultSkin\",\n    value: function _getDefaultSkin(skinId) {\n      var skin = typeof skinId === 'string' ? this._getSkinByName(skinId, false) : this.settings.availableSkins[skinId];\n      if (!skin) {\n        throw new Error(\"Skin ID \\\"\".concat(skinId, \"\\\" not found!\"));\n      }\n      return skin;\n    }\n\n    // Get theme by skinId/skinName\n  }, {\n    key: \"_getSkinByName\",\n    value: function _getSkinByName(skinName) {\n      var returnDefault = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var skins = this.settings.availableSkins;\n      for (var i = 0, l = skins.length; i < l; i++) {\n        if (skins[i].name === skinName) return skins[i];\n      }\n      return returnDefault ? this.settings.defaultSkin : null;\n    }\n  }]);\n}(); // Colors\nTemplateCustomizer.COLORS = [{\n  name: 'primary',\n  title: 'Primary',\n  color: rootStyles.getPropertyValue('--bs-primary').trim()\n}, {\n  name: 'success',\n  title: 'Success',\n  color: '#0D9394'\n}, {\n  name: 'warning',\n  title: 'Warning',\n  color: '#FFAB1D'\n}, {\n  name: 'danger',\n  title: 'Danger',\n  color: '#EB3D63'\n}, {\n  name: 'info',\n  title: 'Info',\n  color: '#2092EC'\n}];\n\n// Themes\nTemplateCustomizer.THEMES = [{\n  name: 'light',\n  title: 'Light',\n  image: '<i class=\"ri ri-sun-line icon-base mb-0\"></i>'\n}, {\n  name: 'dark',\n  title: 'Dark',\n  image: '<i class=\"ri ri-moon-clear-line icon-base mb-0\"></i>'\n}, {\n  name: 'system',\n  title: 'System',\n  image: '<i class=\"ri ri-computer-line icon-base mb-0\"></i>'\n}];\n\n// Skins\nTemplateCustomizer.SKINS = [{\n  name: 'default',\n  title: 'Default',\n  image: 'skin-default.svg'\n}, {\n  name: 'bordered',\n  title: 'Bordered',\n  image: 'skin-border.svg'\n}];\n\n// Layouts\nTemplateCustomizer.LAYOUTS = [{\n  name: 'expanded',\n  title: 'Expanded',\n  image: 'layouts-expanded.svg'\n}, {\n  name: 'collapsed',\n  title: 'Collapsed',\n  image: 'layouts-collapsed.svg'\n}];\n\n// Navbar Options\nTemplateCustomizer.NAVBAR_OPTIONS = [{\n  name: 'sticky',\n  title: 'Sticky',\n  image: 'navbar-sticky.svg'\n}, {\n  name: 'static',\n  title: 'Static',\n  image: 'navbar-static.svg'\n}, {\n  name: 'hidden',\n  title: 'Hidden',\n  image: 'navbar-hidden.svg'\n}];\n\n// Header Types\nTemplateCustomizer.HEADER_TYPES = [{\n  name: 'fixed',\n  title: 'Fixed',\n  image: 'horizontal-fixed.svg'\n}, {\n  name: 'static',\n  title: 'Static',\n  image: 'horizontal-static.svg'\n}];\n\n// Content Types\nTemplateCustomizer.CONTENT = [{\n  name: 'compact',\n  title: 'Compact',\n  image: 'content-compact.svg'\n}, {\n  name: 'wide',\n  title: 'Wide',\n  image: 'content-wide.svg'\n}];\n\n// Directions\nTemplateCustomizer.DIRECTIONS = [{\n  name: 'ltr',\n  title: 'Left to Right (En)',\n  image: 'direction-ltr.svg'\n}, {\n  name: 'rtl',\n  title: 'Right to Left (Ar)',\n  image: 'direction-rtl.svg'\n}];\n\n// Theme setting language\nTemplateCustomizer.LANGUAGES = {\n  en: {\n    panel_header: 'Template Customizer',\n    panel_sub_header: 'Customize and preview in real time',\n    theming_header: 'Theming',\n    color_label: 'Primary Color',\n    theme_label: 'Theme',\n    skin_label: 'Skins',\n    semiDark_label: 'Semi Dark',\n    layout_header: 'Layout',\n    layout_label: 'Menu (Navigation)',\n    layout_header_label: 'Header Types',\n    content_label: 'Content',\n    layout_navbar_label: 'Navbar Type',\n    direction_label: 'Direction'\n  },\n  fr: {\n    panel_header: 'Modèle De Personnalisation',\n    panel_sub_header: 'Personnalisez et prévisualisez en temps réel',\n    theming_header: 'Thématisation',\n    color_label: 'Couleur primaire',\n    theme_label: 'Thème',\n    skin_label: 'Peaux',\n    semiDark_label: 'Demi-foncé',\n    layout_header: 'Disposition',\n    layout_label: 'Menu (Navigation)',\n    layout_header_label: \"Types d'en-tête\",\n    content_label: 'Contenu',\n    layout_navbar_label: 'Type de barre de navigation',\n    direction_label: 'Direction'\n  },\n  ar: {\n    panel_header: 'أداة تخصيص القالب',\n    panel_sub_header: 'تخصيص ومعاينة في الوقت الحقيقي',\n    theming_header: 'السمات',\n    color_label: 'اللون الأساسي',\n    theme_label: 'سمة',\n    skin_label: 'جلود',\n    semiDark_label: 'شبه داكن',\n    layout_header: 'تَخطِيط',\n    layout_label: 'القائمة (الملاحة)',\n    layout_header_label: 'أنواع الرأس',\n    content_label: 'محتوى',\n    layout_navbar_label: 'نوع شريط التنقل',\n    direction_label: 'اتجاه'\n  },\n  de: {\n    panel_header: 'Vorlagen-Anpasser',\n    panel_sub_header: 'Anpassen und Vorschau in Echtzeit',\n    theming_header: 'Themen',\n    color_label: 'Grundfarbe',\n    theme_label: 'Thema',\n    skin_label: 'Skins',\n    semiDark_label: 'Halbdunkel',\n    layout_header: 'Layout',\n    layout_label: 'Menü (Navigation)',\n    layout_header_label: 'Header-Typen',\n    content_label: 'Inhalt',\n    layout_navbar_label: 'Art der Navigationsleiste',\n    direction_label: 'Richtung'\n  }\n};\nwindow.TemplateCustomizer = TemplateCustomizer;\n\n\n/**\n * Initialize color picker functionality for template customization\n * Caches DOM elements and handles color picker setup\n */\nvar initializeColorPicker = function initializeColorPicker() {\n  // Cache DOM elements\n  var elements = {\n    pickerWrapper: document.querySelector('.template-customizer-colors-options input[value=\"picker\"]'),\n    pickerEl: document.querySelector('.customizer-nano-picker'),\n    pcrButton: document.querySelector('.custom-option-content .pcr-button')\n  };\n\n  // Early return if required elements missing\n  if (!elements.pickerWrapper || !elements.pickerEl) {\n    console.warn('Required color picker elements not found');\n    return;\n  }\n\n  // Seth the current color based on the checked state of the picker wrapper\n  var currentColor = elements.pickerWrapper.getAttribute('checked') === 'checked' ? window.templateCustomizer._getSetting('Color') ? window.templateCustomizer._getSetting('Color') : window.templateCustomizer.settings.defaultPrimaryColor : window.Helpers.getCssVar('gray-900', true);\n\n  // Configure and initialize Pickr color picker\n  var picker = new Pickr({\n    el: elements.pickerEl,\n    theme: 'nano',\n    default: currentColor,\n    defaultRepresentation: 'HEX',\n    comparison: false,\n    components: {\n      hue: true,\n      preview: true,\n      interaction: {\n        input: true\n      }\n    }\n  });\n  // Add color picker icon\n  picker._root.button.classList.add('ri', 'ri-palette-line');\n\n  // Handle color changes\n  picker.on('change', function (color) {\n    var _elements$pcrButton;\n    var hexColor = color.toHEXA().toString();\n    var rgbaColor = color.toRGBA().toString();\n\n    // Update picker button color if exists\n    (_elements$pcrButton = elements.pcrButton) === null || _elements$pcrButton === void 0 || _elements$pcrButton.style.setProperty('--pcr-color', rgbaColor);\n\n    // Update selected option state\n    elements.pickerWrapper.checked = true;\n    window.Helpers.updateCustomOptionCheck(elements.pickerWrapper);\n\n    // Update theme color settings\n    window.templateCustomizer._setSetting('Color', hexColor);\n    window.templateCustomizer.setColor(hexColor, true);\n  });\n};\nwindow.onload = function () {\n  initializeColorPicker();\n  var pcrButton = document.querySelector('.custom-option-content .pcr-button');\n  pcrButton === null || pcrButton === void 0 || pcrButton.style.setProperty('--pcr-color', window.templateCustomizer.settings.defaultPrimaryColor);\n};\n\n//# sourceURL=webpack://Materialize/./js/template-customizer.js?")},"./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./js/_template-customizer/_template-customizer.scss":function(module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "./node_modules/css-loader/dist/runtime/noSourceMaps.js");\n/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/getUrl.js */ "./node_modules/css-loader/dist/runtime/getUrl.js");\n/* harmony import */ var _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__);\n// Imports\n\n\n\nvar ___CSS_LOADER_URL_IMPORT_0___ = new URL(/* asset import */ __webpack_require__(/*! data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABClJREFUaEPtmY1RFEEQhbsjUCIQIhAiUCNQIxAiECIQIxAiECIAIpAMhAiECIQI2vquZqnZvp6fhb3SK5mqq6Ju92b69bzXf6is+dI1t1+eAfztG5z1BsxsU0S+ici2iPB3vm5E5EpEDlSVv2dZswFIxv8UkZcNy+5EZGcuEHMCOBeR951uvVDVD53vVl+bE8DvDu8Pxtyo6ta/BsByg1R15Bwzqz5/LJgn34CZwfnPInI4BUB6/1hV0cSjVxcAM4PbcBZjL0XklIPN7Is3fLCkdQPpPYw/VNXj5IhPIvJWRIhSl6p60ULWBGBm30Vk123EwRxCuIzWkkjNrCZywith10ewE1Xdq4GoAjCz/RTXW44Ynt+LyBEfT43kYfbj86J3w5Q32DNcRQDpwF+dkQXDMey8xem0L3TEqB4g3PZWad8agBMRgZPeu96D1/C2Zbh3X0p80Op1xxloztN48bMQQNoc7+eLEuAoPSPiIDY4Ooo+E6ixeNXM+D3GERz2U3CIqMstLJUgJQDe+7eq6mub0NYEkLAKwEHkiBQDCZtddZCZ8d6r7JDwFkoARklHRPZUFVDVZWbwGuNrC4EfdOzFrRABh3Wnqhv+d70AEBLGFROPmeHlnM81G69UdSd6IUuM0GgUVn1uqWmg5EmMfBeEyB7Pe3txBkY+rGT8j0J+WXq/BgDkUCaqLgEAnwcRog0veMIqFAAwCy2wnw+bI2GaGboBgF9k5N0o0rUSGUb4eO0BeO9j/GYhkSHMHMTIqwGARX6p6a+nlPBl8kZuXMD9j6pKfF9aZuaFOdJCEL5D4eYb9wCYVCanrBmGyii/tIq+SLj/HQBCaM5bLzwfPqdQ6FpVHyra4IbuVbXaY7dETC2ESPNNWiIOi69CcdgSMXsh4tNSUiklMgwmC0aNd08Y5WAES6HHehM4gu97wyhBgWpgqXsrASglprDy7CwhehMZOSbK6JMSma+Fio1KltCmlBIj7gfZOGx8ppQSXrhzFnOhJ/31BDkjFHRvOd09x0mRBA9SFgxUgHpQg0q0t5ymPMlL+EnldFTfDA0NAmf+OTQ0X0sRouf7NNkYGhrOYNrxtIaGg83MNzVDSe3LXLhP7O/yrCsCz1zlWTpjWkuZAOBpX3yVnLqI1yLCOKU6qMrmP7SSrUEw54XF4WBIK5FxCMOr3lVsfGqNSmPzBXUnJTIX1jyVBq9wO6UObOpgC5GjO98vFKnTdQMZXxEsWZlDiCZMIxAbNxQOqlpVZtobejBaZNoBnRDzMFpkxvTQOD36BlrcySZuI6p1ACB6LU3wWuf5581+oHfD1vi89bz3nFUC8Nm7ZlP3nKkFbM4bWPt/MSFwklprYItwt6cmvpWJ2IVcQBCz6bLysSCv3SaANCiTsnaNRrNRqMXVVT1/BrAqz/buu/Y38Ad3KC5PARej0QAAAABJRU5ErkJggg== */ "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABClJREFUaEPtmY1RFEEQhbsjUCIQIhAiUCNQIxAiECIQIxAiECIAIpAMhAiECIQI2vquZqnZvp6fhb3SK5mqq6Ju92b69bzXf6is+dI1t1+eAfztG5z1BsxsU0S+ici2iPB3vm5E5EpEDlSVv2dZswFIxv8UkZcNy+5EZGcuEHMCOBeR951uvVDVD53vVl+bE8DvDu8Pxtyo6ta/BsByg1R15Bwzqz5/LJgn34CZwfnPInI4BUB6/1hV0cSjVxcAM4PbcBZjL0XklIPN7Is3fLCkdQPpPYw/VNXj5IhPIvJWRIhSl6p60ULWBGBm30Vk123EwRxCuIzWkkjNrCZywith10ewE1Xdq4GoAjCz/RTXW44Ynt+LyBEfT43kYfbj86J3w5Q32DNcRQDpwF+dkQXDMey8xem0L3TEqB4g3PZWad8agBMRgZPeu96D1/C2Zbh3X0p80Op1xxloztN48bMQQNoc7+eLEuAoPSPiIDY4Ooo+E6ixeNXM+D3GERz2U3CIqMstLJUgJQDe+7eq6mub0NYEkLAKwEHkiBQDCZtddZCZ8d6r7JDwFkoARklHRPZUFVDVZWbwGuNrC4EfdOzFrRABh3Wnqhv+d70AEBLGFROPmeHlnM81G69UdSd6IUuM0GgUVn1uqWmg5EmMfBeEyB7Pe3txBkY+rGT8j0J+WXq/BgDkUCaqLgEAnwcRog0veMIqFAAwCy2wnw+bI2GaGboBgF9k5N0o0rUSGUb4eO0BeO9j/GYhkSHMHMTIqwGARX6p6a+nlPBl8kZuXMD9j6pKfF9aZuaFOdJCEL5D4eYb9wCYVCanrBmGyii/tIq+SLj/HQBCaM5bLzwfPqdQ6FpVHyra4IbuVbXaY7dETC2ESPNNWiIOi69CcdgSMXsh4tNSUiklMgwmC0aNd08Y5WAES6HHehM4gu97wyhBgWpgqXsrASglprDy7CwhehMZOSbK6JMSma+Fio1KltCmlBIj7gfZOGx8ppQSXrhzFnOhJ/31BDkjFHRvOd09x0mRBA9SFgxUgHpQg0q0t5ymPMlL+EnldFTfDA0NAmf+OTQ0X0sRouf7NNkYGhrOYNrxtIaGg83MNzVDSe3LXLhP7O/yrCsCz1zlWTpjWkuZAOBpX3yVnLqI1yLCOKU6qMrmP7SSrUEw54XF4WBIK5FxCMOr3lVsfGqNSmPzBXUnJTIX1jyVBq9wO6UObOpgC5GjO98vFKnTdQMZXxEsWZlDiCZMIxAbNxQOqlpVZtobejBaZNoBnRDzMFpkxvTQOD36BlrcySZuI6p1ACB6LU3wWuf5581+oHfD1vi89bz3nFUC8Nm7ZlP3nKkFbM4bWPt/MSFwklprYItwt6cmvpWJ2IVcQBCz6bLysSCv3SaANCiTsnaNRrNRqMXVVT1/BrAqz/buu/Y38Ad3KC5PARej0QAAAABJRU5ErkJggg=="), __webpack_require__.b);\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, "/*\\n* Template Customizer Style\\n**/\\n#template-customizer {\\n  position: fixed;\\n  z-index: 99999999;\\n  display: flex;\\n  flex-direction: column;\\n  block-size: 100%;\\n  -webkit-box-direction: normal;\\n  -webkit-box-orient: vertical;\\n  box-shadow: 0 0.3125rem 1.375rem 0 rgba(34, 48, 62, 0.18);\\n  font-family: \\"Open Sans\\", BlinkMacSystemFont, \\"Segoe UI\\", Roboto, \\"Helvetica Neue\\", Arial, sans-serif, \\"Apple Color Emoji\\", \\"Segoe UI Emoji\\", \\"Segoe UI Symbol\\";\\n  font-size: inherit;\\n  inline-size: 400px;\\n  inset-block-start: 0;\\n  inset-inline-end: 0;\\n  transform: translateX(420px);\\n  transition: transform 0.2s ease-in;\\n  /* Color option styles */\\n  /* Font Icons sizing and alignments */\\n  /* border-color for hr */\\n  /* To update svg image\'s color */\\n  /* Customizer button */\\n  /* Customizer inner */\\n}\\n[data-bs-theme=dark] #template-customizer {\\n  box-shadow: 0 0.3125rem 1.375rem 0 rgba(20, 20, 29, 0.26);\\n}\\n#template-customizer h5 {\\n  position: relative;\\n  font-size: 11px;\\n}\\n#template-customizer .form-label {\\n  font-size: 0.9375rem;\\n  font-weight: 500;\\n}\\n#template-customizer .template-customizer-colors-options {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-around;\\n  margin: 0;\\n  gap: 0.3rem;\\n}\\n#template-customizer .template-customizer-colors-options .custom-option {\\n  inline-size: 50px;\\n}\\n#template-customizer .template-customizer-colors-options .custom-option .custom-option-content {\\n  padding: 0;\\n  min-block-size: 46px;\\n}\\n#template-customizer .template-customizer-colors-options .custom-option .custom-option-content .pcr-button {\\n  padding: 0.625rem;\\n  block-size: 30px;\\n  inline-size: 30px;\\n}\\n#template-customizer .template-customizer-colors-options .custom-option .custom-option-content .pcr-button::before, #template-customizer .template-customizer-colors-options .custom-option .custom-option-content .pcr-button::after {\\n  border-radius: 0.5rem;\\n}\\n#template-customizer .template-customizer-colors-options .custom-option .custom-option-content .pcr-button:focus {\\n  box-shadow: none;\\n}\\n#template-customizer .template-customizer-colors-options .custom-option-body {\\n  border-radius: 0.5rem;\\n  block-size: 30px;\\n  inline-size: 30px;\\n}\\n#template-customizer .custom-option-icon {\\n  padding: 0;\\n}\\n#template-customizer .custom-option-icon .custom-option-content {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-block-size: 50px;\\n}\\n#template-customizer hr {\\n  border-color: var(--bs-border-color);\\n}\\n#template-customizer .custom-option {\\n  border-width: 2px;\\n  margin: 0;\\n}\\n#template-customizer .custom-option.custom-option-image .custom-option-content .custom-option-body svg {\\n  inline-size: 100%;\\n}\\n#template-customizer.template-customizer-open {\\n  transform: none;\\n  transition-delay: 0.1s;\\n}\\n#template-customizer.template-customizer-open .template-customizer-theme .custom-option.checked {\\n  background-color: rgba(var(--bs-primary-rgb), 0.08);\\n}\\n#template-customizer.template-customizer-open .template-customizer-theme .custom-option.checked *,\\n#template-customizer.template-customizer-open .template-customizer-theme .custom-option.checked *::before,\\n#template-customizer.template-customizer-open .template-customizer-theme .custom-option.checked *::after {\\n  color: var(--bs-primary);\\n}\\n#template-customizer.template-customizer-open .custom-option.checked {\\n  border-width: 2px;\\n  color: var(--bs-primary);\\n}\\n#template-customizer.template-customizer-open .custom-option.checked .custom-option-content {\\n  border: none;\\n}\\n#template-customizer .template-customizer-header a:hover,\\n#template-customizer .template-customizer-header a:hover .icon-base {\\n  color: inherit !important;\\n}\\n#template-customizer .template-customizer-open-btn {\\n  position: absolute;\\n  z-index: -1;\\n  display: block;\\n  background: var(--bs-primary);\\n  block-size: 38px;\\n  border-end-start-radius: 50%;\\n  border-start-start-radius: 50%;\\n  box-shadow: 0 0.125rem 0.25rem 0 rgba(var(--bs-primary-rgb), 0.4);\\n  color: #fff;\\n  font-size: 18px;\\n  inline-size: 38px;\\n  inset-block-start: 180px;\\n  inset-inline-start: 0;\\n  line-height: 38px;\\n  opacity: 1;\\n  text-align: center;\\n  transform: translateX(-58px);\\n  transition: all 0.1s linear 0.2s;\\n  /* Customizer Hidden */\\n}\\n#template-customizer .template-customizer-open-btn::before {\\n  position: absolute;\\n  display: block;\\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");\\n  background-size: 100% 100%;\\n  block-size: 20px;\\n  content: \\"\\";\\n  inline-size: 20px;\\n  inset-block-start: 50%;\\n  inset-inline-start: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n:dir(rtl) #template-customizer .template-customizer-open-btn::before {\\n  margin-inline-start: 2px;\\n  transform: translate(50%, -50%);\\n}\\n.customizer-hide #template-customizer .template-customizer-open-btn {\\n  display: none;\\n}\\n:dir(rtl) #template-customizer .template-customizer-open-btn {\\n  transform: translateX(58px);\\n}\\n#template-customizer.template-customizer-open .template-customizer-open-btn {\\n  opacity: 0;\\n  transform: none;\\n  transition-delay: 0s;\\n}\\n#template-customizer .template-customizer-inner {\\n  position: relative;\\n  overflow: auto;\\n  flex: 0 1 auto;\\n  -webkit-box-flex: 0;\\n  opacity: 1;\\n  transition: opacity 0.2s;\\n}\\n\\n@media (max-width: 1200px) {\\n  #template-customizer {\\n    display: none;\\n    visibility: hidden;\\n  }\\n}\\n.layout-menu-100vh #template-customizer {\\n  block-size: 100dvh;\\n}\\n\\n/* RTL */\\n:dir(rtl) #template-customizer:not(.template-customizer-open) {\\n  transform: translateX(-420px);\\n}", ""]);\n// Exports\n/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);\n\n\n//# sourceURL=webpack://Materialize/./js/_template-customizer/_template-customizer.scss?./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js')},"./node_modules/css-loader/dist/runtime/api.js":function(module){eval('\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = "";\n      var needLayer = typeof item[5] !== "undefined";\n      if (item[4]) {\n        content += "@supports (".concat(item[4], ") {");\n      }\n      if (item[2]) {\n        content += "@media ".concat(item[2], " {");\n      }\n      if (needLayer) {\n        content += "@layer".concat(item[5].length > 0 ? " ".concat(item[5]) : "", " {");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += "}";\n      }\n      if (item[2]) {\n        content += "}";\n      }\n      if (item[4]) {\n        content += "}";\n      }\n      return content;\n    }).join("");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === "string") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== "undefined") {\n        if (typeof item[5] === "undefined") {\n          item[5] = layer;\n        } else {\n          item[1] = "@layer".concat(item[5].length > 0 ? " ".concat(item[5]) : "", " {").concat(item[1], "}");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = "@media ".concat(item[2], " {").concat(item[1], "}");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = "".concat(supports);\n        } else {\n          item[1] = "@supports (".concat(item[4], ") {").concat(item[1], "}");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/css-loader/dist/runtime/api.js?')},"./node_modules/css-loader/dist/runtime/getUrl.js":function(module){eval('\n\nmodule.exports = function (url, options) {\n  if (!options) {\n    options = {};\n  }\n  if (!url) {\n    return url;\n  }\n  url = String(url.__esModule ? url.default : url);\n\n  // If url is already wrapped in quotes, remove them\n  if (/^[\'"].*[\'"]$/.test(url)) {\n    url = url.slice(1, -1);\n  }\n  if (options.hash) {\n    url += options.hash;\n  }\n\n  // Should url be wrapped?\n  // See https://drafts.csswg.org/css-values-3/#urls\n  if (/["\'() \\t\\n]|(%20)/.test(url) || options.needQuotes) {\n    return "\\"".concat(url.replace(/"/g, \'\\\\"\').replace(/\\n/g, "\\\\n"), "\\"");\n  }\n  return url;\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/css-loader/dist/runtime/getUrl.js?')},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":function(module){eval("\n\nmodule.exports = function (i) {\n  return i[1];\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/css-loader/dist/runtime/noSourceMaps.js?")},"./js/_template-customizer/_template-customizer.html":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n// Module\nvar code = "<div id=\\"template-customizer\\" class=\\"card rounded-0\\"> <a href=\\"javascript:void(0)\\" class=\\"template-customizer-open-btn\\" tabindex=\\"-1\\"></a> <div class=\\"p-6 m-0 lh-sm border-bottom template-customizer-header position-relative py-4\\"> <h6 class=\\"template-customizer-t-panel_header mb-0\\"></h6> <p class=\\"template-customizer-t-panel_sub_header mb-0 small\\"></p> <div class=\\"d-flex align-items-center gap-2 position-absolute end-0 top-0 mt-6 me-5\\"> <a href=\\"javascript:void(0)\\" class=\\"template-customizer-reset-btn text-heading\\" data-bs-toggle=\\"tooltip\\" data-bs-placement=\\"bottom\\" title=\\"Reset Customizer\\"> <i class=\\"icon-base ri ri-refresh-line\\"></i><span class=\\"badge rounded-pill bg-danger badge-dot badge-notifications d-none\\"></span> </a> <a href=\\"javascript:void(0)\\" class=\\"template-customizer-close-btn fw-light text-heading\\" tabindex=\\"-1\\"> <i class=\\"icon-base ri ri-close-line icon-lg\\"></i> </a> </div> </div> <div class=\\"template-customizer-inner pt-5\\"> <div class=\\"template-customizer-theming\\"> <h5 class=\\"m-0 px-6 pb-6\\"> <span class=\\"template-customizer-t-theming_header bg-label-primary rounded-1 py-1 px-3 small\\"></span> </h5> <div class=\\"m-0 px-6 pb-6 template-customizer-color w-100\\"> <label for=\\"customizerColor\\" class=\\"form-label d-block template-customizer-t-color_label mb-2\\"></label> <div class=\\"row template-customizer-colors-options\\"></div> </div> <div class=\\"m-0 px-6 pb-6 template-customizer-theme w-100\\"> <label for=\\"customizerTheme\\" class=\\"form-label d-block template-customizer-t-theme_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-themes-options\\"></div> </div> <div class=\\"m-0 px-6 pb-6 template-customizer-skins w-100\\"> <label for=\\"customizerSkin\\" class=\\"form-label template-customizer-t-skin_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-skins-options\\"></div> </div> <div class=\\"m-0 px-6 template-customizer-semiDark w-100 d-flex justify-content-between pe-12\\"> <span class=\\"form-label template-customizer-t-semiDark_label\\"></span> <label class=\\"switch template-customizer-t-semiDark_label\\"> <input type=\\"checkbox\\" class=\\"template-customizer-semi-dark-switch switch-input\\"/> <span class=\\"switch-toggle-slider\\"> <span class=\\"switch-on\\"></span> <span class=\\"switch-off\\"></span> </span> </label> </div> <hr class=\\"m-0 px-6 my-6\\"/> </div> <div class=\\"template-customizer-layout\\"> <h5 class=\\"m-0 px-6 pb-6\\"> <span class=\\"template-customizer-t-layout_header bg-label-primary rounded-2 py-1 px-3 small\\"></span> </h5> <div class=\\"m-0 px-6 pb-6 d-block template-customizer-layouts\\"> <label for=\\"customizerStyle\\" class=\\"form-label d-block template-customizer-t-layout_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-layouts-options\\"></div> </div> <div class=\\"m-0 px-6 pb-6 template-customizer-headerOptions w-100\\"> <label for=\\"customizerHeader\\" class=\\"form-label template-customizer-t-layout_header_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-header-options\\"></div> </div> <div class=\\"m-0 px-6 pb-6 template-customizer-layoutNavbarOptions w-100\\"> <label for=\\"customizerNavbar\\" class=\\"form-label template-customizer-t-layout_navbar_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-navbar-options\\"></div> </div> <div class=\\"m-0 px-6 pb-6 template-customizer-content w-100\\"> <label for=\\"customizerContent\\" class=\\"form-label template-customizer-t-content_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-content-options\\"></div> </div> <div class=\\"m-0 px-6 pb-6 template-customizer-directions w-100\\"> <label for=\\"customizerDirection\\" class=\\"form-label template-customizer-t-direction_label mb-2\\"></label> <div class=\\"row px-1 template-customizer-directions-options\\"></div> </div> </div> </div> </div> ";\n// Exports\n/* harmony default export */ __webpack_exports__["default"] = (code);\n\n//# sourceURL=webpack://Materialize/./js/_template-customizer/_template-customizer.html?')},"./js/_template-customizer/_template-customizer.scss":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "./node_modules/style-loader/dist/runtime/styleDomAPI.js");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "./node_modules/style-loader/dist/runtime/insertBySelector.js");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "./node_modules/style-loader/dist/runtime/insertStyleElement.js");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "./node_modules/style-loader/dist/runtime/styleTagTransform.js");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _node_modules_css_loader_dist_cjs_js_node_modules_sass_loader_dist_cjs_js_template_customizer_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../node_modules/css-loader/dist/cjs.js!../../node_modules/sass-loader/dist/cjs.js!./_template-customizer.scss */ "./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./js/_template-customizer/_template-customizer.scss");\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());\noptions.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());\n\n      options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");\n    \noptions.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());\noptions.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());\n\nvar update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_node_modules_sass_loader_dist_cjs_js_template_customizer_scss__WEBPACK_IMPORTED_MODULE_6__["default"], options);\n\n\n\n\n       /* harmony default export */ __webpack_exports__["default"] = (_node_modules_css_loader_dist_cjs_js_node_modules_sass_loader_dist_cjs_js_template_customizer_scss__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_node_modules_sass_loader_dist_cjs_js_template_customizer_scss__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_node_modules_sass_loader_dist_cjs_js_template_customizer_scss__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);\n\n\n//# sourceURL=webpack://Materialize/./js/_template-customizer/_template-customizer.scss?')},"./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js":function(module){eval('\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = "".concat(id, " ").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};\n\n//# sourceURL=webpack://Materialize/./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js?')},"./node_modules/style-loader/dist/runtime/insertBySelector.js":function(module){eval('\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === "undefined") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error("Couldn\'t find a style target. This probably means that the value for the \'insert\' parameter is invalid.");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;\n\n//# sourceURL=webpack://Materialize/./node_modules/style-loader/dist/runtime/insertBySelector.js?')},"./node_modules/style-loader/dist/runtime/insertStyleElement.js":function(module){eval('\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement("style");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;\n\n//# sourceURL=webpack://Materialize/./node_modules/style-loader/dist/runtime/insertStyleElement.js?')},"./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js":function(module,__unused_webpack_exports,__webpack_require__){eval('\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute("nonce", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;\n\n//# sourceURL=webpack://Materialize/./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js?')},"./node_modules/style-loader/dist/runtime/styleDomAPI.js":function(module){eval('\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = "";\n  if (obj.supports) {\n    css += "@supports (".concat(obj.supports, ") {");\n  }\n  if (obj.media) {\n    css += "@media ".concat(obj.media, " {");\n  }\n  var needLayer = typeof obj.layer !== "undefined";\n  if (needLayer) {\n    css += "@layer".concat(obj.layer.length > 0 ? " ".concat(obj.layer) : "", " {");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += "}";\n  }\n  if (obj.media) {\n    css += "}";\n  }\n  if (obj.supports) {\n    css += "}";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== "undefined") {\n    css += "\\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), " */");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === "undefined") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;\n\n//# sourceURL=webpack://Materialize/./node_modules/style-loader/dist/runtime/styleDomAPI.js?')},"./node_modules/style-loader/dist/runtime/styleTagTransform.js":function(module){eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;\n\n//# sourceURL=webpack://Materialize/./node_modules/style-loader/dist/runtime/styleTagTransform.js?")},"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABClJREFUaEPtmY1RFEEQhbsjUCIQIhAiUCNQIxAiECIQIxAiECIAIpAMhAiECIQI2vquZqnZvp6fhb3SK5mqq6Ju92b69bzXf6is+dI1t1+eAfztG5z1BsxsU0S+ici2iPB3vm5E5EpEDlSVv2dZswFIxv8UkZcNy+5EZGcuEHMCOBeR951uvVDVD53vVl+bE8DvDu8Pxtyo6ta/BsByg1R15Bwzqz5/LJgn34CZwfnPInI4BUB6/1hV0cSjVxcAM4PbcBZjL0XklIPN7Is3fLCkdQPpPYw/VNXj5IhPIvJWRIhSl6p60ULWBGBm30Vk123EwRxCuIzWkkjNrCZywith10ewE1Xdq4GoAjCz/RTXW44Ynt+LyBEfT43kYfbj86J3w5Q32DNcRQDpwF+dkQXDMey8xem0L3TEqB4g3PZWad8agBMRgZPeu96D1/C2Zbh3X0p80Op1xxloztN48bMQQNoc7+eLEuAoPSPiIDY4Ooo+E6ixeNXM+D3GERz2U3CIqMstLJUgJQDe+7eq6mub0NYEkLAKwEHkiBQDCZtddZCZ8d6r7JDwFkoARklHRPZUFVDVZWbwGuNrC4EfdOzFrRABh3Wnqhv+d70AEBLGFROPmeHlnM81G69UdSd6IUuM0GgUVn1uqWmg5EmMfBeEyB7Pe3txBkY+rGT8j0J+WXq/BgDkUCaqLgEAnwcRog0veMIqFAAwCy2wnw+bI2GaGboBgF9k5N0o0rUSGUb4eO0BeO9j/GYhkSHMHMTIqwGARX6p6a+nlPBl8kZuXMD9j6pKfF9aZuaFOdJCEL5D4eYb9wCYVCanrBmGyii/tIq+SLj/HQBCaM5bLzwfPqdQ6FpVHyra4IbuVbXaY7dETC2ESPNNWiIOi69CcdgSMXsh4tNSUiklMgwmC0aNd08Y5WAES6HHehM4gu97wyhBgWpgqXsrASglprDy7CwhehMZOSbK6JMSma+Fio1KltCmlBIj7gfZOGx8ppQSXrhzFnOhJ/31BDkjFHRvOd09x0mRBA9SFgxUgHpQg0q0t5ymPMlL+EnldFTfDA0NAmf+OTQ0X0sRouf7NNkYGhrOYNrxtIaGg83MNzVDSe3LXLhP7O/yrCsCz1zlWTpjWkuZAOBpX3yVnLqI1yLCOKU6qMrmP7SSrUEw54XF4WBIK5FxCMOr3lVsfGqNSmPzBXUnJTIX1jyVBq9wO6UObOpgC5GjO98vFKnTdQMZXxEsWZlDiCZMIxAbNxQOqlpVZtobejBaZNoBnRDzMFpkxvTQOD36BlrcySZuI6p1ACB6LU3wWuf5581+oHfD1vi89bz3nFUC8Nm7ZlP3nKkFbM4bWPt/MSFwklprYItwt6cmvpWJ2IVcQBCz6bLysSCv3SaANCiTsnaNRrNRqMXVVT1/BrAqz/buu/Y38Ad3KC5PARej0QAAAABJRU5ErkJggg==":function(module){eval('module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABClJREFUaEPtmY1RFEEQhbsjUCIQIhAiUCNQIxAiECIQIxAiECIAIpAMhAiECIQI2vquZqnZvp6fhb3SK5mqq6Ju92b69bzXf6is+dI1t1+eAfztG5z1BsxsU0S+ici2iPB3vm5E5EpEDlSVv2dZswFIxv8UkZcNy+5EZGcuEHMCOBeR951uvVDVD53vVl+bE8DvDu8Pxtyo6ta/BsByg1R15Bwzqz5/LJgn34CZwfnPInI4BUB6/1hV0cSjVxcAM4PbcBZjL0XklIPN7Is3fLCkdQPpPYw/VNXj5IhPIvJWRIhSl6p60ULWBGBm30Vk123EwRxCuIzWkkjNrCZywith10ewE1Xdq4GoAjCz/RTXW44Ynt+LyBEfT43kYfbj86J3w5Q32DNcRQDpwF+dkQXDMey8xem0L3TEqB4g3PZWad8agBMRgZPeu96D1/C2Zbh3X0p80Op1xxloztN48bMQQNoc7+eLEuAoPSPiIDY4Ooo+E6ixeNXM+D3GERz2U3CIqMstLJUgJQDe+7eq6mub0NYEkLAKwEHkiBQDCZtddZCZ8d6r7JDwFkoARklHRPZUFVDVZWbwGuNrC4EfdOzFrRABh3Wnqhv+d70AEBLGFROPmeHlnM81G69UdSd6IUuM0GgUVn1uqWmg5EmMfBeEyB7Pe3txBkY+rGT8j0J+WXq/BgDkUCaqLgEAnwcRog0veMIqFAAwCy2wnw+bI2GaGboBgF9k5N0o0rUSGUb4eO0BeO9j/GYhkSHMHMTIqwGARX6p6a+nlPBl8kZuXMD9j6pKfF9aZuaFOdJCEL5D4eYb9wCYVCanrBmGyii/tIq+SLj/HQBCaM5bLzwfPqdQ6FpVHyra4IbuVbXaY7dETC2ESPNNWiIOi69CcdgSMXsh4tNSUiklMgwmC0aNd08Y5WAES6HHehM4gu97wyhBgWpgqXsrASglprDy7CwhehMZOSbK6JMSma+Fio1KltCmlBIj7gfZOGx8ppQSXrhzFnOhJ/31BDkjFHRvOd09x0mRBA9SFgxUgHpQg0q0t5ymPMlL+EnldFTfDA0NAmf+OTQ0X0sRouf7NNkYGhrOYNrxtIaGg83MNzVDSe3LXLhP7O/yrCsCz1zlWTpjWkuZAOBpX3yVnLqI1yLCOKU6qMrmP7SSrUEw54XF4WBIK5FxCMOr3lVsfGqNSmPzBXUnJTIX1jyVBq9wO6UObOpgC5GjO98vFKnTdQMZXxEsWZlDiCZMIxAbNxQOqlpVZtobejBaZNoBnRDzMFpkxvTQOD36BlrcySZuI6p1ACB6LU3wWuf5581+oHfD1vi89bz3nFUC8Nm7ZlP3nKkFbM4bWPt/MSFwklprYItwt6cmvpWJ2IVcQBCz6bLysSCv3SaANCiTsnaNRrNRqMXVVT1/BrAqz/buu/Y38Ad3KC5PARej0QAAAABJRU5ErkJggg==";\n\n//# sourceURL=webpack://Materialize/data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABClJREFUaEPtmY1RFEEQhbsjUCIQIhAiUCNQIxAiECIQIxAiECIAIpAMhAiECIQI2vquZqnZvp6fhb3SK5mqq6Ju92b69bzXf6is+dI1t1+eAfztG5z1BsxsU0S+ici2iPB3vm5E5EpEDlSVv2dZswFIxv8UkZcNy+5EZGcuEHMCOBeR951uvVDVD53vVl+bE8DvDu8Pxtyo6ta/BsByg1R15Bwzqz5/LJgn34CZwfnPInI4BUB6/1hV0cSjVxcAM4PbcBZjL0XklIPN7Is3fLCkdQPpPYw/VNXj5IhPIvJWRIhSl6p60ULWBGBm30Vk123EwRxCuIzWkkjNrCZywith10ewE1Xdq4GoAjCz/RTXW44Ynt+LyBEfT43kYfbj86J3w5Q32DNcRQDpwF+dkQXDMey8xem0L3TEqB4g3PZWad8agBMRgZPeu96D1/C2Zbh3X0p80Op1xxloztN48bMQQNoc7+eLEuAoPSPiIDY4Ooo+E6ixeNXM+D3GERz2U3CIqMstLJUgJQDe+7eq6mub0NYEkLAKwEHkiBQDCZtddZCZ8d6r7JDwFkoARklHRPZUFVDVZWbwGuNrC4EfdOzFrRABh3Wnqhv+d70AEBLGFROPmeHlnM81G69UdSd6IUuM0GgUVn1uqWmg5EmMfBeEyB7Pe3txBkY+rGT8j0J+WXq/BgDkUCaqLgEAnwcRog0veMIqFAAwCy2wnw+bI2GaGboBgF9k5N0o0rUSGUb4eO0BeO9j/GYhkSHMHMTIqwGARX6p6a+nlPBl8kZuXMD9j6pKfF9aZuaFOdJCEL5D4eYb9wCYVCanrBmGyii/tIq+SLj/HQBCaM5bLzwfPqdQ6FpVHyra4IbuVbXaY7dETC2ESPNNWiIOi69CcdgSMXsh4tNSUiklMgwmC0aNd08Y5WAES6HHehM4gu97wyhBgWpgqXsrASglprDy7CwhehMZOSbK6JMSma+Fio1KltCmlBIj7gfZOGx8ppQSXrhzFnOhJ/31BDkjFHRvOd09x0mRBA9SFgxUgHpQg0q0t5ymPMlL+EnldFTfDA0NAmf+OTQ0X0sRouf7NNkYGhrOYNrxtIaGg83MNzVDSe3LXLhP7O/yrCsCz1zlWTpjWkuZAOBpX3yVnLqI1yLCOKU6qMrmP7SSrUEw54XF4WBIK5FxCMOr3lVsfGqNSmPzBXUnJTIX1jyVBq9wO6UObOpgC5GjO98vFKnTdQMZXxEsWZlDiCZMIxAbNxQOqlpVZtobejBaZNoBnRDzMFpkxvTQOD36BlrcySZuI6p1ACB6LU3wWuf5581+oHfD1vi89bz3nFUC8Nm7ZlP3nKkFbM4bWPt/MSFwklprYItwt6cmvpWJ2IVcQBCz6bLysSCv3SaANCiTsnaNRrNRqMXVVT1/BrAqz/buu/Y38Ad3KC5PARej0QAAAABJRU5ErkJggg==?')}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={id:e,exports:{}};return __webpack_modules__[e](n,n.exports,__webpack_require__),n.exports}__webpack_require__.m=__webpack_modules__,__webpack_require__.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=function(e,t){for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__webpack_require__.b=document.baseURI||self.location.href,__webpack_require__.nc=void 0;var __webpack_exports__=__webpack_require__("./js/template-customizer.js");return __webpack_exports__}()}));