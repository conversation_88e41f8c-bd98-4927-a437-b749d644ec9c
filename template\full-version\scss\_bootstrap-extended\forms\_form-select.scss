/* Select
******************************************************************************* */

.form-select {
  background-clip: padding-box;
  padding-block: calc($form-select-padding-y - $input-border-width);
  padding-inline-end: calc($form-select-indicator-padding - $input-border-width);
  padding-inline-start: calc($form-select-padding-x - $input-border-width);
  optgroup,
  option {
    background-color: var(--#{$prefix}paper-bg);
  }
  &[multiple],
  &[size]:not([size="1"]) {
    padding-inline-end: $form-select-padding-x;
  }
  &:hover {
    &:not(:focus):not(:disabled) {
      border-color: $input-hover-border-color;
    }
  }
  &:disabled {
    background-image: escape-svg($form-select-disabled-indicator);
  }
  &:focus,
  &:focus-within,
  .was-validated &:invalid,
  .was-validated &:valid,
  &.is-invalid,
  &.is-valid {
    border-width: $input-focus-border-width;
    background-position: right calc($form-select-padding-x - 1px) center;
    padding-block: calc($form-select-padding-y - $input-focus-border-width);
    padding-inline-end: calc($form-select-indicator-padding - $input-focus-border-width);
    padding-inline-start: calc($form-select-padding-x - $input-focus-border-width);
  }
  &.form-select-lg {
    background-size: 24px 24px;
    min-block-size: $input-height-lg;
    padding-block: calc($form-select-padding-y-lg - $input-border-width);
    padding-inline-start: calc($form-select-padding-x-lg - $input-border-width);
    &:focus,
    .was-validated &:invalid,
    .was-validated &:valid,
    &.is-invalid,
    &.is-valid {
      padding-block: calc($form-select-padding-y-lg - $input-focus-border-width);
      padding-inline-start: calc($form-select-padding-x-lg - $input-focus-border-width);
    }
  }
  &.form-select-sm {
    background-size: 20px 20px;
    min-block-size: $input-height-sm;
    padding-block: calc($form-select-padding-y-sm - $input-border-width);
    padding-inline-start: calc($form-select-padding-x-sm - $input-border-width);
    &:focus,
    .was-validated &:invalid,
    .was-validated &:valid,
    &.is-invalid,
    &.is-valid {
      padding-block: calc($form-select-padding-y-sm - $input-focus-border-width);
      padding-inline-start: calc($form-select-padding-x-sm - $input-focus-border-width);
    }
  }
  &[multiple]:focus {
    padding-inline-end: .875rem !important;
  }
}

/* RTL */
:dir(rtl) {
  .form-select {
    background-position: left $form-select-padding-x center;
    &:focus,
    .was-validated &:invalid,
    .was-validated &:valid,
    &.is-invalid,
    &.is-valid {
      background-position: left calc($form-select-padding-x - 1px) center;
    }
  }
}

/* Dark Theme */
@if $enable-dark-mode {
  @include color-mode(dark) {
    .form-select {
      &:disabled {
        background-image: escape-svg($form-select-disabled-indicator-dark);
      }
    }
    &:hover {
      &:not([disabled]):not([focus]) {
        border-color: var(--#{$prefix}gray-600);
      }
    }
  }
}
