from django import forms
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import DemandeInscription, StatutDemande
from organisations.models import TypeAbonnement, MembreOrganisation
from organisations.utils import valider_telephone_international


class OnboardingForm(forms.ModelForm):
    """Formulaire de demande d'inscription pour une nouvelle organisation"""
    
    # Champs supplémentaires pour la validation du mot de passe
    confirmer_mot_de_passe = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirmez votre mot de passe'
        }),
        label="Confirmer le mot de passe",
        help_text="Saisissez à nouveau votre mot de passe"
    )
    
    # Acceptation des conditions
    accepter_conditions = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label="J'accepte les conditions d'utilisation",
        error_messages={
            'required': 'Vous devez accepter les conditions d\'utilisation pour continuer.'
        }
    )

    class Meta:
        model = DemandeInscription
        fields = [
            # Informations organisation
            'nom_organisation',
            'adresse_organisation', 
            'telephone_organisation',
            'email_organisation',
            'type_abonnement_demande',
            # Informations administrateur
            'prenom_admin',
            'nom_admin',
            'email_admin',
            'telephone_admin',
            'mot_de_passe'
        ]
        
        widgets = {
            # Organisation
            'nom_organisation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de votre établissement hospitalier'
            }),
            'adresse_organisation': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Adresse complète de l\'établissement'
            }),
            'telephone_organisation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+225 01 23 45 67 89'
            }),
            'email_organisation': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'type_abonnement_demande': forms.Select(attrs={
                'class': 'form-control'
            }),
            
            # Administrateur
            'prenom_admin': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Votre prénom'
            }),
            'nom_admin': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Votre nom de famille'
            }),
            'email_admin': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'telephone_admin': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+225 01 23 45 67 89'
            }),
            'mot_de_passe': forms.PasswordInput(attrs={
                'class': 'form-control',
                'placeholder': 'Choisissez un mot de passe sécurisé'
            })
        }
        
        labels = {
            'nom_organisation': 'Nom de l\'établissement',
            'adresse_organisation': 'Adresse de l\'établissement',
            'telephone_organisation': 'Téléphone de l\'établissement',
            'email_organisation': 'Email de l\'établissement',
            'type_abonnement_demande': 'Type d\'abonnement souhaité',
            'prenom_admin': 'Votre prénom',
            'nom_admin': 'Votre nom',
            'email_admin': 'Votre adresse email',
            'telephone_admin': 'Votre téléphone',
            'mot_de_passe': 'Mot de passe'
        }

    def clean_nom_organisation(self):
        """Validation du nom de l'organisation"""
        nom = self.cleaned_data.get('nom_organisation')
        if nom and len(nom.strip()) < 3:
            raise forms.ValidationError(
                "Le nom de l'organisation doit contenir au moins 3 caractères."
            )
        return nom.strip() if nom else nom

    def clean_email_organisation(self):
        """Validation de l'email de l'organisation"""
        email = self.cleaned_data.get('email_organisation')
        if email:
            # Vérifier si l'email n'est pas déjà utilisé
            if DemandeInscription.objects.filter(
                email_organisation=email,
                statut__in=[StatutDemande.EN_ATTENTE, StatutDemande.VALIDEE]
            ).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée par une autre organisation."
                )
        return email

    def clean_telephone_organisation(self):
        """Validation du téléphone de l'organisation"""
        telephone = self.cleaned_data.get('telephone_organisation')
        if telephone:
            if not valider_telephone_international(telephone):
                raise forms.ValidationError(
                    "Format de téléphone invalide. Utilisez le format international "
                    "(ex: +225 01 23 45 67 89)."
                )
        return telephone

    def clean_email_admin(self):
        """Validation de l'email de l'administrateur"""
        email = self.cleaned_data.get('email_admin')
        if email:
            # Vérifier si l'email n'est pas déjà utilisé par un utilisateur existant
            if User.objects.filter(email=email).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée par un autre utilisateur."
                )
            
            # Vérifier si l'email n'est pas déjà dans une demande en attente
            if DemandeInscription.objects.filter(
                email_admin=email,
                statut__in=[StatutDemande.EN_ATTENTE, StatutDemande.VALIDEE]
            ).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée dans une autre demande."
                )
        return email

    def clean_telephone_admin(self):
        """Validation du téléphone de l'administrateur"""
        telephone = self.cleaned_data.get('telephone_admin')
        if telephone:
            if not valider_telephone_international(telephone):
                raise forms.ValidationError(
                    "Format de téléphone invalide. Utilisez le format international "
                    "(ex: +225 01 23 45 67 89)."
                )
        return telephone

    def clean_mot_de_passe(self):
        """Validation du mot de passe"""
        mot_de_passe = self.cleaned_data.get('mot_de_passe')
        if mot_de_passe:
            try:
                # Utiliser les validateurs de mot de passe de Django
                validate_password(mot_de_passe)
            except ValidationError as e:
                raise forms.ValidationError(e.messages)
        return mot_de_passe

    def clean(self):
        """Validation globale du formulaire"""
        cleaned_data = super().clean()
        mot_de_passe = cleaned_data.get('mot_de_passe')
        confirmer_mot_de_passe = cleaned_data.get('confirmer_mot_de_passe')
        
        # Vérifier que les mots de passe correspondent
        if mot_de_passe and confirmer_mot_de_passe:
            if mot_de_passe != confirmer_mot_de_passe:
                raise forms.ValidationError(
                    "Les mots de passe ne correspondent pas."
                )
        
        return cleaned_data

    def save(self, commit=True):
        """Sauvegarde personnalisée"""
        instance = super().save(commit=False)
        
        # Hasher le mot de passe avant de le sauvegarder
        from django.contrib.auth.hashers import make_password
        instance.mot_de_passe = make_password(instance.mot_de_passe)
        
        # Définir le statut par défaut
        instance.statut = StatutDemande.EN_ATTENTE
        
        if commit:
            instance.save()
        
        return instance


class LoginForm(forms.Form):
    """Formulaire de connexion"""

    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>',
            'autofocus': True
        }),
        label="Adresse email",
        help_text="Utilisez l'email de votre compte administrateur"
    )

    mot_de_passe = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Votre mot de passe'
        }),
        label="Mot de passe"
    )

    se_souvenir = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label="Se souvenir de moi",
        help_text="Rester connecté sur cet appareil"
    )

    def __init__(self, request=None, *args, **kwargs):
        self.request = request
        self.user_cache = None
        super().__init__(*args, **kwargs)

    def clean(self):
        """Validation de la connexion"""
        cleaned_data = super().clean()
        email = cleaned_data.get('email')
        mot_de_passe = cleaned_data.get('mot_de_passe')

        if email and mot_de_passe:
            # Tenter l'authentification
            self.user_cache = authenticate(
                self.request,
                username=email,
                password=mot_de_passe
            )

            if self.user_cache is None:
                raise forms.ValidationError(
                    "Email ou mot de passe incorrect. Veuillez vérifier vos identifiants."
                )

            # Vérifier que l'utilisateur est actif
            if not self.user_cache.is_active:
                raise forms.ValidationError(
                    "Ce compte a été désactivé. Contactez l'administrateur."
                )

            # Vérifier que l'utilisateur appartient à une organisation
            if not MembreOrganisation.objects.filter(
                utilisateur=self.user_cache,
                actif=True
            ).exists():
                raise forms.ValidationError(
                    "Votre compte n'est associé à aucune organisation active. "
                    "Contactez l'administrateur de votre organisation."
                )

        return cleaned_data

    def get_user(self):
        """Retourne l'utilisateur authentifié"""
        return self.user_cache


class InvitationUtilisateurForm(forms.Form):
    """Formulaire pour inviter un nouvel utilisateur dans l'organisation"""

    prenom = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Prénom de l\'utilisateur'
        }),
        label="Prénom"
    )

    nom = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nom de famille'
        }),
        label="Nom"
    )

    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        }),
        label="Adresse email",
        help_text="Cette adresse servira pour la connexion"
    )

    telephone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+225 01 23 45 67 89'
        }),
        label="Téléphone (optionnel)",
        help_text="Format international recommandé"
    )

    role = forms.ChoiceField(
        choices=MembreOrganisation.RoleMembre.choices,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label="Rôle dans l'organisation",
        initial=MembreOrganisation.RoleMembre.RECEPTIONNISTE  # Rôle par défaut
    )

    envoyer_email = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label="Envoyer un email d'invitation",
        help_text="L'utilisateur recevra ses identifiants par email"
    )

    def __init__(self, *args, organisation=None, **kwargs):
        self.organisation = organisation
        super().__init__(*args, **kwargs)

    def clean_email(self):
        """Validation de l'email"""
        email = self.cleaned_data.get('email')

        if email:
            # Vérifier que l'email n'est pas déjà utilisé
            if User.objects.filter(email=email).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée par un autre utilisateur."
                )

            # Vérifier que l'email n'est pas dans une demande en attente
            if DemandeInscription.objects.filter(
                email_admin=email,
                statut__in=[StatutDemande.EN_ATTENTE, StatutDemande.VALIDEE]
            ).exists():
                raise forms.ValidationError(
                    "Cette adresse email est utilisée dans une demande d'inscription."
                )

        return email

    def clean_telephone(self):
        """Validation du téléphone"""
        telephone = self.cleaned_data.get('telephone')

        if telephone:
            if not valider_telephone_international(telephone):
                raise forms.ValidationError(
                    "Format de téléphone invalide. Utilisez le format international "
                    "(ex: +225 01 23 45 67 89)."
                )

        return telephone

    def save(self, organisation, admin_user):
        """
        Crée l'utilisateur et l'associe à l'organisation

        Args:
            organisation: L'organisation à laquelle associer l'utilisateur
            admin_user: L'utilisateur admin qui fait l'invitation

        Returns:
            User: L'utilisateur créé
        """
        from django.contrib.auth.models import User
        from django.utils.crypto import get_random_string
        from organisations.models import MembreOrganisation
        from .services import InvitationService

        # Générer un mot de passe temporaire
        mot_de_passe_temporaire = get_random_string(12)

        # Créer l'utilisateur
        utilisateur = User.objects.create_user(
            username=self.cleaned_data['email'],
            email=self.cleaned_data['email'],
            first_name=self.cleaned_data['prenom'],
            last_name=self.cleaned_data['nom'],
            password=mot_de_passe_temporaire
        )

        # Créer le membre organisation
        membre = MembreOrganisation.objects.create(
            utilisateur=utilisateur,
            organisation=organisation,
            role=self.cleaned_data['role'],
            actif=True,
            invite_par=admin_user
        )

        # Envoyer l'email d'invitation si demandé
        if self.cleaned_data.get('envoyer_email', True):
            try:
                InvitationService.envoyer_invitation(
                    utilisateur,
                    organisation,
                    mot_de_passe_temporaire,
                    admin_user
                )
            except Exception as e:
                # Log l'erreur mais ne fait pas échouer la création
                print(f"Erreur lors de l'envoi de l'email d'invitation: {e}")

        return utilisateur
