from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import DemandeInscription, StatutDemande
from organisations.models import TypeAbonnement
from organisations.utils import valider_telephone_international


class OnboardingForm(forms.ModelForm):
    """Formulaire de demande d'inscription pour une nouvelle organisation"""
    
    # Champs supplémentaires pour la validation du mot de passe
    confirmer_mot_de_passe = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirmez votre mot de passe'
        }),
        label="Confirmer le mot de passe",
        help_text="Saisissez à nouveau votre mot de passe"
    )
    
    # Acceptation des conditions
    accepter_conditions = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label="J'accepte les conditions d'utilisation",
        error_messages={
            'required': 'Vous devez accepter les conditions d\'utilisation pour continuer.'
        }
    )

    class Meta:
        model = DemandeInscription
        fields = [
            # Informations organisation
            'nom_organisation',
            'adresse_organisation', 
            'telephone_organisation',
            'email_organisation',
            'type_abonnement_demande',
            # Informations administrateur
            'prenom_admin',
            'nom_admin',
            'email_admin',
            'telephone_admin',
            'mot_de_passe'
        ]
        
        widgets = {
            # Organisation
            'nom_organisation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de votre établissement hospitalier'
            }),
            'adresse_organisation': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Adresse complète de l\'établissement'
            }),
            'telephone_organisation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+225 01 23 45 67 89'
            }),
            'email_organisation': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'type_abonnement_demande': forms.Select(attrs={
                'class': 'form-control'
            }),
            
            # Administrateur
            'prenom_admin': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Votre prénom'
            }),
            'nom_admin': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Votre nom de famille'
            }),
            'email_admin': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'telephone_admin': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+225 01 23 45 67 89'
            }),
            'mot_de_passe': forms.PasswordInput(attrs={
                'class': 'form-control',
                'placeholder': 'Choisissez un mot de passe sécurisé'
            })
        }
        
        labels = {
            'nom_organisation': 'Nom de l\'établissement',
            'adresse_organisation': 'Adresse de l\'établissement',
            'telephone_organisation': 'Téléphone de l\'établissement',
            'email_organisation': 'Email de l\'établissement',
            'type_abonnement_demande': 'Type d\'abonnement souhaité',
            'prenom_admin': 'Votre prénom',
            'nom_admin': 'Votre nom',
            'email_admin': 'Votre adresse email',
            'telephone_admin': 'Votre téléphone',
            'mot_de_passe': 'Mot de passe'
        }

    def clean_nom_organisation(self):
        """Validation du nom de l'organisation"""
        nom = self.cleaned_data.get('nom_organisation')
        if nom and len(nom.strip()) < 3:
            raise forms.ValidationError(
                "Le nom de l'organisation doit contenir au moins 3 caractères."
            )
        return nom.strip() if nom else nom

    def clean_email_organisation(self):
        """Validation de l'email de l'organisation"""
        email = self.cleaned_data.get('email_organisation')
        if email:
            # Vérifier si l'email n'est pas déjà utilisé
            if DemandeInscription.objects.filter(
                email_organisation=email,
                statut__in=[StatutDemande.EN_ATTENTE, StatutDemande.VALIDEE]
            ).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée par une autre organisation."
                )
        return email

    def clean_telephone_organisation(self):
        """Validation du téléphone de l'organisation"""
        telephone = self.cleaned_data.get('telephone_organisation')
        if telephone:
            if not valider_telephone_international(telephone):
                raise forms.ValidationError(
                    "Format de téléphone invalide. Utilisez le format international "
                    "(ex: +225 01 23 45 67 89)."
                )
        return telephone

    def clean_email_admin(self):
        """Validation de l'email de l'administrateur"""
        email = self.cleaned_data.get('email_admin')
        if email:
            # Vérifier si l'email n'est pas déjà utilisé par un utilisateur existant
            if User.objects.filter(email=email).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée par un autre utilisateur."
                )
            
            # Vérifier si l'email n'est pas déjà dans une demande en attente
            if DemandeInscription.objects.filter(
                email_admin=email,
                statut__in=[StatutDemande.EN_ATTENTE, StatutDemande.VALIDEE]
            ).exists():
                raise forms.ValidationError(
                    "Cette adresse email est déjà utilisée dans une autre demande."
                )
        return email

    def clean_telephone_admin(self):
        """Validation du téléphone de l'administrateur"""
        telephone = self.cleaned_data.get('telephone_admin')
        if telephone:
            if not valider_telephone_international(telephone):
                raise forms.ValidationError(
                    "Format de téléphone invalide. Utilisez le format international "
                    "(ex: +225 01 23 45 67 89)."
                )
        return telephone

    def clean_mot_de_passe(self):
        """Validation du mot de passe"""
        mot_de_passe = self.cleaned_data.get('mot_de_passe')
        if mot_de_passe:
            try:
                # Utiliser les validateurs de mot de passe de Django
                validate_password(mot_de_passe)
            except ValidationError as e:
                raise forms.ValidationError(e.messages)
        return mot_de_passe

    def clean(self):
        """Validation globale du formulaire"""
        cleaned_data = super().clean()
        mot_de_passe = cleaned_data.get('mot_de_passe')
        confirmer_mot_de_passe = cleaned_data.get('confirmer_mot_de_passe')
        
        # Vérifier que les mots de passe correspondent
        if mot_de_passe and confirmer_mot_de_passe:
            if mot_de_passe != confirmer_mot_de_passe:
                raise forms.ValidationError(
                    "Les mots de passe ne correspondent pas."
                )
        
        return cleaned_data

    def save(self, commit=True):
        """Sauvegarde personnalisée"""
        instance = super().save(commit=False)
        
        # Hasher le mot de passe avant de le sauvegarder
        from django.contrib.auth.hashers import make_password
        instance.mot_de_passe = make_password(instance.mot_de_passe)
        
        # Définir le statut par défaut
        instance.statut = StatutDemande.EN_ATTENTE
        
        if commit:
            instance.save()
        
        return instance
