/* Footer
******************************************************************************* */

.footer {
  --#{$prefix}footer-color: #{$footer-color};
  --#{$prefix}footer-bg: #{$footer-bg};
  --#{$prefix}footer-border-width: #{$footer-border-width};
  --#{$prefix}footer-border-color: #{$footer-border-color};
  --#{$prefix}footer-link-color: #{$footer-link-color};
  --#{$prefix}footer-link-hover-color: #{$footer-link-hover-color};
  --#{$prefix}footer-link-disabled-color: #{$footer-link-disabled-color};
  --#{$prefix}footer-link-active-color: #{$footer-link-active-color};
  --#{$prefix}footer-brand-color: #{$footer-brand-color};
  --#{$prefix}footer-brand-hover-color: #{$footer-brand-hover-color};
  --#{$prefix}footer-box-shadow: #{$footer-box-shadow};

  color: var(--#{$prefix}footer-color);

  .footer-brand{
    color: var(--#{$prefix}footer-brand-color);
    &:hover,
    &:focus {
      color: var(--#{$prefix}footer-brand-hover-color);
    }
  }

  .footer-link{
    display: inline-block;
    color: var(--#{$prefix}footer-link-color);
    &:hover,
    &:focus {
      color: var(--#{$prefix}footer-link-hover-color);
    }
    &.disabled {
      color: var(--#{$prefix}footer-link-disabled-color) !important;
    }
  }
  &.bg-footer-theme {
    --#{$prefix}footer-brand-color: var(--#{$prefix}body-color);
  }
  &.bg-white {
    --#{$prefix}footer-color: #{$black-dark};
    --#{$prefix}footer-link-color: #{$black-dark};
    --#{$prefix}footer-link-hover-color: #{$pure-black};
    --#{$prefix}footer-brand-color: #{$black};
    --#{$prefix}footer-brand-hover-color: #{$pure-black};
  }
}

/* Generate contextual modifier classes for colorizing the footer */
@each $state in map-keys($theme-colors) {
  .footer.bg-#{$state} {
    @if $state == "light" {
      --#{$prefix}footer-brand-hover-color: color-mix(in sRGB, var(--#{$prefix}paper-bg) 40%, var(--#{$prefix}#{$state}-contrast));
      --#{$prefix}footer-link-hover-color: var(--#{$prefix}#{$state}-contrast);
      --#{$prefix}footer-color: var(--#{$prefix}body-color);
      --#{$prefix}footer-link-color: var(--#{$prefix}body-color);
      --#{$prefix}footer-brand-color: var(--#{$prefix}heading-color);
    }
    @else {
      --#{$prefix}footer-brand-hover-color: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$bg-label-tint-amount}, var(--#{$prefix}#{$state}));
      --#{$prefix}footer-link-hover-color: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$bg-label-tint-amount}, var(--#{$prefix}#{$state}));
      --#{$prefix}footer-color: var(--#{$prefix}#{$state}-contrast);
      --#{$prefix}footer-link-color: var(--#{$prefix}#{$state}-contrast);
      --#{$prefix}footer-brand-color: var(--#{$prefix}#{$state}-contrast);
    }
  }
}


/* Dark Theme */

@if $enable-dark-mode {
  @include color-mode(dark) {
    @each $state in map-keys($theme-colors) {
      .footer.bg-#{$state} {
        @if $state == "dark" {
          --#{$prefix}footer-brand-hover-color: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$bg-label-tint-amount}, var(--#{$prefix}#{$state}-contrast));
          --#{$prefix}footer-link-hover-color: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$bg-label-tint-amount}, var(--#{$prefix}#{$state}-contrast));
        }
      }
    }
  }
}
