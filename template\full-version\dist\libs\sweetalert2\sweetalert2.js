!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t=e();for(var a in t)("object"==typeof exports?exports:n)[a]=t[a]}}(self,(function(){return function(){var __webpack_modules__={"./libs/sweetalert2/sweetalert2.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Swal: function() { return /* binding */ Swal; }\n/* harmony export */ });\n/* harmony import */ var sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sweetalert2/dist/sweetalert2 */ \"./node_modules/sweetalert2/dist/sweetalert2.js\");\n/* harmony import */ var sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0__);\n\nvar Swal = sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0___default().mixin({\n  buttonsStyling: false,\n  customClass: {\n    confirmButton: 'btn btn-primary',\n    cancelButton: 'btn btn-outline-danger',\n    denyButton: 'btn btn-outline-secondary'\n  }\n});\ntry {\n  window.Swal = Swal;\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/sweetalert2/sweetalert2.js?")},"./node_modules/sweetalert2/dist/sweetalert2.js":function(module){eval("/*!\n* sweetalert2 v11.14.5\n* Released under the MIT License.\n*/\n(function (global, factory) {\n   true ? module.exports = factory() :\n  0;\n})(this, (function () { 'use strict';\n\n  function _assertClassBrand(e, t, n) {\n    if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n    throw new TypeError(\"Private element is not present on this object\");\n  }\n  function _checkPrivateRedeclaration(e, t) {\n    if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n  }\n  function _classPrivateFieldGet2(s, a) {\n    return s.get(_assertClassBrand(s, a));\n  }\n  function _classPrivateFieldInitSpec(e, t, a) {\n    _checkPrivateRedeclaration(e, t), t.set(e, a);\n  }\n  function _classPrivateFieldSet2(s, a, r) {\n    return s.set(_assertClassBrand(s, a), r), r;\n  }\n\n  const RESTORE_FOCUS_TIMEOUT = 100;\n\n  /** @type {GlobalState} */\n  const globalState = {};\n  const focusPreviousActiveElement = () => {\n    if (globalState.previousActiveElement instanceof HTMLElement) {\n      globalState.previousActiveElement.focus();\n      globalState.previousActiveElement = null;\n    } else if (document.body) {\n      document.body.focus();\n    }\n  };\n\n  /**\n   * Restore previous active (focused) element\n   *\n   * @param {boolean} returnFocus\n   * @returns {Promise<void>}\n   */\n  const restoreActiveElement = returnFocus => {\n    return new Promise(resolve => {\n      if (!returnFocus) {\n        return resolve();\n      }\n      const x = window.scrollX;\n      const y = window.scrollY;\n      globalState.restoreFocusTimeout = setTimeout(() => {\n        focusPreviousActiveElement();\n        resolve();\n      }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n      window.scrollTo(x, y);\n    });\n  };\n\n  const swalPrefix = 'swal2-';\n\n  /**\n   * @typedef {Record<SwalClass, string>} SwalClasses\n   */\n\n  /**\n   * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n   * @typedef {Record<SwalIcon, string>} SwalIcons\n   */\n\n  /** @type {SwalClass[]} */\n  const classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'default-outline', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error'];\n  const swalClasses = classNames.reduce((acc, className) => {\n    acc[className] = swalPrefix + className;\n    return acc;\n  }, /** @type {SwalClasses} */{});\n\n  /** @type {SwalIcon[]} */\n  const icons = ['success', 'warning', 'info', 'question', 'error'];\n  const iconTypes = icons.reduce((acc, icon) => {\n    acc[icon] = swalPrefix + icon;\n    return acc;\n  }, /** @type {SwalIcons} */{});\n\n  const consolePrefix = 'SweetAlert2:';\n\n  /**\n   * Capitalize the first letter of a string\n   *\n   * @param {string} str\n   * @returns {string}\n   */\n  const capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n  /**\n   * Standardize console warnings\n   *\n   * @param {string | string[]} message\n   */\n  const warn = message => {\n    console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n  };\n\n  /**\n   * Standardize console errors\n   *\n   * @param {string} message\n   */\n  const error = message => {\n    console.error(`${consolePrefix} ${message}`);\n  };\n\n  /**\n   * Private global state for `warnOnce`\n   *\n   * @type {string[]}\n   * @private\n   */\n  const previousWarnOnceMessages = [];\n\n  /**\n   * Show a console warning, but only if it hasn't already been shown\n   *\n   * @param {string} message\n   */\n  const warnOnce = message => {\n    if (!previousWarnOnceMessages.includes(message)) {\n      previousWarnOnceMessages.push(message);\n      warn(message);\n    }\n  };\n\n  /**\n   * Show a one-time console warning about deprecated params/methods\n   *\n   * @param {string} deprecatedParam\n   * @param {string?} useInstead\n   */\n  const warnAboutDeprecation = function (deprecatedParam) {\n    let useInstead = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n  };\n\n  /**\n   * If `arg` is a function, call it (with no arguments or context) and return the result.\n   * Otherwise, just pass the value through\n   *\n   * @param {Function | any} arg\n   * @returns {any}\n   */\n  const callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n  /**\n   * @param {any} arg\n   * @returns {Promise<any>}\n   */\n  const asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n  /**\n   * Gets the popup container which contains the backdrop and the popup itself.\n   *\n   * @returns {HTMLElement | null}\n   */\n  const getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n  /**\n   * @param {string} selectorString\n   * @returns {HTMLElement | null}\n   */\n  const elementBySelector = selectorString => {\n    const container = getContainer();\n    return container ? container.querySelector(selectorString) : null;\n  };\n\n  /**\n   * @param {string} className\n   * @returns {HTMLElement | null}\n   */\n  const elementByClass = className => {\n    return elementBySelector(`.${className}`);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getPopup = () => elementByClass(swalClasses.popup);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIcon = () => elementByClass(swalClasses.icon);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTitle = () => elementByClass(swalClasses.title);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getImage = () => elementByClass(swalClasses.image);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getActions = () => elementByClass(swalClasses.actions);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getFooter = () => elementByClass(swalClasses.footer);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getCloseButton = () => elementByClass(swalClasses.close);\n\n  // https://github.com/jkup/focusable/blob/master/index.js\n  const focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n  /**\n   * @returns {HTMLElement[]}\n   */\n  const getFocusableElements = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return [];\n    }\n    /** @type {NodeListOf<HTMLElement>} */\n    const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n    const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n    // sort according to tabindex\n    .sort((a, b) => {\n      const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n      const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n      if (tabindexA > tabindexB) {\n        return 1;\n      } else if (tabindexA < tabindexB) {\n        return -1;\n      }\n      return 0;\n    });\n\n    /** @type {NodeListOf<HTMLElement>} */\n    const otherFocusableElements = popup.querySelectorAll(focusable);\n    const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n    return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isModal = () => {\n    return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isToast = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return hasClass(popup, swalClasses.toast);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isLoading = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return popup.hasAttribute('data-loading');\n  };\n\n  /**\n   * Securely set innerHTML of an element\n   * https://github.com/sweetalert2/sweetalert2/issues/1926\n   *\n   * @param {HTMLElement} elem\n   * @param {string} html\n   */\n  const setInnerHtml = (elem, html) => {\n    elem.textContent = '';\n    if (html) {\n      const parser = new DOMParser();\n      const parsed = parser.parseFromString(html, `text/html`);\n      const head = parsed.querySelector('head');\n      if (head) {\n        Array.from(head.childNodes).forEach(child => {\n          elem.appendChild(child);\n        });\n      }\n      const body = parsed.querySelector('body');\n      if (body) {\n        Array.from(body.childNodes).forEach(child => {\n          if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n            elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n          } else {\n            elem.appendChild(child);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {boolean}\n   */\n  const hasClass = (elem, className) => {\n    if (!className) {\n      return false;\n    }\n    const classList = className.split(/\\s+/);\n    for (let i = 0; i < classList.length; i++) {\n      if (!elem.classList.contains(classList[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   */\n  const removeCustomClasses = (elem, params) => {\n    Array.from(elem.classList).forEach(className => {\n      if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n        elem.classList.remove(className);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   * @param {string} className\n   */\n  const applyCustomClass = (elem, params, className) => {\n    removeCustomClasses(elem, params);\n    if (!params.customClass) {\n      return;\n    }\n    const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n    if (!customClass) {\n      return;\n    }\n    if (typeof customClass !== 'string' && !customClass.forEach) {\n      warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n      return;\n    }\n    addClass(elem, customClass);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n   * @returns {HTMLInputElement | null}\n   */\n  const getInput$1 = (popup, inputClass) => {\n    if (!inputClass) {\n      return null;\n    }\n    switch (inputClass) {\n      case 'select':\n      case 'textarea':\n      case 'file':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n      case 'checkbox':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n      case 'radio':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n      case 'range':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n      default:\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n   */\n  const focusInput = input => {\n    input.focus();\n\n    // place cursor at end of text in text input\n    if (input.type !== 'file') {\n      // http://stackoverflow.com/a/2345915\n      const val = input.value;\n      input.value = '';\n      input.value = val;\n    }\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   * @param {boolean} condition\n   */\n  const toggleClass = (target, classList, condition) => {\n    if (!target || !classList) {\n      return;\n    }\n    if (typeof classList === 'string') {\n      classList = classList.split(/\\s+/).filter(Boolean);\n    }\n    classList.forEach(className => {\n      if (Array.isArray(target)) {\n        target.forEach(elem => {\n          if (condition) {\n            elem.classList.add(className);\n          } else {\n            elem.classList.remove(className);\n          }\n        });\n      } else {\n        if (condition) {\n          target.classList.add(className);\n        } else {\n          target.classList.remove(className);\n        }\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const addClass = (target, classList) => {\n    toggleClass(target, classList, true);\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const removeClass = (target, classList) => {\n    toggleClass(target, classList, false);\n  };\n\n  /**\n   * Get direct child of an element by class name\n   *\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {HTMLElement | undefined}\n   */\n  const getDirectChildByClass = (elem, className) => {\n    const children = Array.from(elem.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (child instanceof HTMLElement && hasClass(child, className)) {\n        return child;\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} property\n   * @param {*} value\n   */\n  const applyNumericalStyle = (elem, property, value) => {\n    if (value === `${parseInt(value)}`) {\n      value = parseInt(value);\n    }\n    if (value || parseInt(value) === 0) {\n      elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n    } else {\n      elem.style.removeProperty(property);\n    }\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const show = function (elem) {\n    let display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'flex';\n    if (!elem) {\n      return;\n    }\n    elem.style.display = display;\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   */\n  const hide = elem => {\n    if (!elem) {\n      return;\n    }\n    elem.style.display = 'none';\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const showWhenInnerHtmlPresent = function (elem) {\n    let display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'block';\n    if (!elem) {\n      return;\n    }\n    new MutationObserver(() => {\n      toggle(elem, elem.innerHTML, display);\n    }).observe(elem, {\n      childList: true,\n      subtree: true\n    });\n  };\n\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} selector\n   * @param {string} property\n   * @param {string} value\n   */\n  const setStyle = (parent, selector, property, value) => {\n    /** @type {HTMLElement | null} */\n    const el = parent.querySelector(selector);\n    if (el) {\n      el.style.setProperty(property, value);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {any} condition\n   * @param {string} display\n   */\n  const toggle = function (elem, condition) {\n    let display = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'flex';\n    if (condition) {\n      show(elem, display);\n    } else {\n      hide(elem);\n    }\n  };\n\n  /**\n   * borrowed from jquery $(elem).is(':visible') implementation\n   *\n   * @param {HTMLElement | null} elem\n   * @returns {boolean}\n   */\n  const isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n  /**\n   * @returns {boolean}\n   */\n  const allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n  /**\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n  /**\n   * borrowed from https://stackoverflow.com/a/46352119\n   *\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const hasCssAnimation = elem => {\n    const style = window.getComputedStyle(elem);\n    const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n    const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n    return animDuration > 0 || transDuration > 0;\n  };\n\n  /**\n   * @param {number} timer\n   * @param {boolean} reset\n   */\n  const animateTimerProgressBar = function (timer) {\n    let reset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    if (isVisible$1(timerProgressBar)) {\n      if (reset) {\n        timerProgressBar.style.transition = 'none';\n        timerProgressBar.style.width = '100%';\n      }\n      setTimeout(() => {\n        timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n        timerProgressBar.style.width = '0%';\n      }, 10);\n    }\n  };\n  const stopTimerProgressBar = () => {\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    timerProgressBar.style.removeProperty('transition');\n    timerProgressBar.style.width = '100%';\n    const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n    timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n  };\n\n  /**\n   * Detect Node env\n   *\n   * @returns {boolean}\n   */\n  const isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n\n  const sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n  /**\n   * @returns {boolean}\n   */\n  const resetOldContainer = () => {\n    const oldContainer = getContainer();\n    if (!oldContainer) {\n      return false;\n    }\n    oldContainer.remove();\n    removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n    return true;\n  };\n  const resetValidationMessage$1 = () => {\n    globalState.currentInstance.resetValidationMessage();\n  };\n  const addInputChangeListeners = () => {\n    const popup = getPopup();\n    const input = getDirectChildByClass(popup, swalClasses.input);\n    const file = getDirectChildByClass(popup, swalClasses.file);\n    /** @type {HTMLInputElement} */\n    const range = popup.querySelector(`.${swalClasses.range} input`);\n    /** @type {HTMLOutputElement} */\n    const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    /** @type {HTMLInputElement} */\n    const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n    const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n    input.oninput = resetValidationMessage$1;\n    file.onchange = resetValidationMessage$1;\n    select.onchange = resetValidationMessage$1;\n    checkbox.onchange = resetValidationMessage$1;\n    textarea.oninput = resetValidationMessage$1;\n    range.oninput = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n    range.onchange = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n  };\n\n  /**\n   * @param {string | HTMLElement} target\n   * @returns {HTMLElement}\n   */\n  const getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setupAccessibility = params => {\n    const popup = getPopup();\n    popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n    popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n    if (!params.toast) {\n      popup.setAttribute('aria-modal', 'true');\n    }\n  };\n\n  /**\n   * @param {HTMLElement} targetElement\n   */\n  const setupRTL = targetElement => {\n    if (window.getComputedStyle(targetElement).direction === 'rtl') {\n      addClass(getContainer(), swalClasses.rtl);\n    }\n  };\n\n  /**\n   * Add modal + backdrop + no-war message for Russians to DOM\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const init = params => {\n    // Clean up the old popup container if it exists\n    const oldContainerExisted = resetOldContainer();\n    if (isNodeEnv()) {\n      error('SweetAlert2 requires document to initialize');\n      return;\n    }\n    const container = document.createElement('div');\n    container.className = swalClasses.container;\n    if (oldContainerExisted) {\n      addClass(container, swalClasses['no-transition']);\n    }\n    setInnerHtml(container, sweetHTML);\n    const targetElement = getTarget(params.target);\n    targetElement.appendChild(container);\n    setupAccessibility(params);\n    setupRTL(targetElement);\n    addInputChangeListeners();\n  };\n\n  /**\n   * @param {HTMLElement | object | string} param\n   * @param {HTMLElement} target\n   */\n  const parseHtmlToContainer = (param, target) => {\n    // DOM element\n    if (param instanceof HTMLElement) {\n      target.appendChild(param);\n    }\n\n    // Object\n    else if (typeof param === 'object') {\n      handleObject(param, target);\n    }\n\n    // Plain string\n    else if (param) {\n      setInnerHtml(target, param);\n    }\n  };\n\n  /**\n   * @param {any} param\n   * @param {HTMLElement} target\n   */\n  const handleObject = (param, target) => {\n    // JQuery element(s)\n    if (param.jquery) {\n      handleJqueryElem(target, param);\n    }\n\n    // For other objects use their string representation\n    else {\n      setInnerHtml(target, param.toString());\n    }\n  };\n\n  /**\n   * @param {HTMLElement} target\n   * @param {any} elem\n   */\n  const handleJqueryElem = (target, elem) => {\n    target.textContent = '';\n    if (0 in elem) {\n      for (let i = 0; i in elem; i++) {\n        target.appendChild(elem[i].cloneNode(true));\n      }\n    } else {\n      target.appendChild(elem.cloneNode(true));\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderActions = (instance, params) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n\n    // Actions (buttons) wrapper\n    if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n      hide(actions);\n    } else {\n      show(actions);\n    }\n\n    // Custom class\n    applyCustomClass(actions, params, 'actions');\n\n    // Render all the buttons\n    renderButtons(actions, loader, params);\n\n    // Loader\n    setInnerHtml(loader, params.loaderHtml || '');\n    applyCustomClass(loader, params, 'loader');\n  };\n\n  /**\n   * @param {HTMLElement} actions\n   * @param {HTMLElement} loader\n   * @param {SweetAlertOptions} params\n   */\n  function renderButtons(actions, loader, params) {\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n\n    // Render buttons\n    renderButton(confirmButton, 'confirm', params);\n    renderButton(denyButton, 'deny', params);\n    renderButton(cancelButton, 'cancel', params);\n    handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n    if (params.reverseButtons) {\n      if (params.toast) {\n        actions.insertBefore(cancelButton, confirmButton);\n        actions.insertBefore(denyButton, confirmButton);\n      } else {\n        actions.insertBefore(cancelButton, loader);\n        actions.insertBefore(denyButton, loader);\n        actions.insertBefore(confirmButton, loader);\n      }\n    }\n  }\n\n  /**\n   * @param {HTMLElement} confirmButton\n   * @param {HTMLElement} denyButton\n   * @param {HTMLElement} cancelButton\n   * @param {SweetAlertOptions} params\n   */\n  function handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n    if (!params.buttonsStyling) {\n      removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n      return;\n    }\n    addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n    // Buttons background colors\n    if (params.confirmButtonColor) {\n      confirmButton.style.backgroundColor = params.confirmButtonColor;\n      addClass(confirmButton, swalClasses['default-outline']);\n    }\n    if (params.denyButtonColor) {\n      denyButton.style.backgroundColor = params.denyButtonColor;\n      addClass(denyButton, swalClasses['default-outline']);\n    }\n    if (params.cancelButtonColor) {\n      cancelButton.style.backgroundColor = params.cancelButtonColor;\n      addClass(cancelButton, swalClasses['default-outline']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} button\n   * @param {'confirm' | 'deny' | 'cancel'} buttonType\n   * @param {SweetAlertOptions} params\n   */\n  function renderButton(button, buttonType, params) {\n    const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n    toggle(button, params[`show${buttonName}Button`], 'inline-block');\n    setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n    button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n    // Add buttons custom classes\n    button.className = swalClasses[buttonType];\n    applyCustomClass(button, params, `${buttonType}Button`);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderCloseButton = (instance, params) => {\n    const closeButton = getCloseButton();\n    if (!closeButton) {\n      return;\n    }\n    setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n    // Custom class\n    applyCustomClass(closeButton, params, 'closeButton');\n    toggle(closeButton, params.showCloseButton);\n    closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContainer = (instance, params) => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    handleBackdropParam(container, params.backdrop);\n    handlePositionParam(container, params.position);\n    handleGrowParam(container, params.grow);\n\n    // Custom class\n    applyCustomClass(container, params, 'container');\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['backdrop']} backdrop\n   */\n  function handleBackdropParam(container, backdrop) {\n    if (typeof backdrop === 'string') {\n      container.style.background = backdrop;\n    } else if (!backdrop) {\n      addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['position']} position\n   */\n  function handlePositionParam(container, position) {\n    if (!position) {\n      return;\n    }\n    if (position in swalClasses) {\n      addClass(container, swalClasses[position]);\n    } else {\n      warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n      addClass(container, swalClasses.center);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['grow']} grow\n   */\n  function handleGrowParam(container, grow) {\n    if (!grow) {\n      return;\n    }\n    addClass(container, swalClasses[`grow-${grow}`]);\n  }\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateProps = {\n    innerParams: new WeakMap(),\n    domCache: new WeakMap()\n  };\n\n  /// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n\n  /** @type {InputClass[]} */\n  const inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderInput = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    const rerender = !innerParams || params.input !== innerParams.input;\n    inputClasses.forEach(inputClass => {\n      const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n      if (!inputContainer) {\n        return;\n      }\n\n      // set attributes\n      setAttributes(inputClass, params.inputAttributes);\n\n      // set class\n      inputContainer.className = swalClasses[inputClass];\n      if (rerender) {\n        hide(inputContainer);\n      }\n    });\n    if (params.input) {\n      if (rerender) {\n        showInput(params);\n      }\n      // set custom class\n      setCustomClass(params);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const showInput = params => {\n    if (!params.input) {\n      return;\n    }\n    if (!renderInputType[params.input]) {\n      error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (!inputContainer) {\n      return;\n    }\n    const input = renderInputType[params.input](inputContainer, params);\n    show(inputContainer);\n\n    // input autofocus\n    if (params.inputAutoFocus) {\n      setTimeout(() => {\n        focusInput(input);\n      });\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   */\n  const removeAttributes = input => {\n    for (let i = 0; i < input.attributes.length; i++) {\n      const attrName = input.attributes[i].name;\n      if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n        input.removeAttribute(attrName);\n      }\n    }\n  };\n\n  /**\n   * @param {InputClass} inputClass\n   * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n   */\n  const setAttributes = (inputClass, inputAttributes) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const input = getInput$1(popup, inputClass);\n    if (!input) {\n      return;\n    }\n    removeAttributes(input);\n    for (const attr in inputAttributes) {\n      input.setAttribute(attr, inputAttributes[attr]);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setCustomClass = params => {\n    if (!params.input) {\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (inputContainer) {\n      applyCustomClass(inputContainer, params, 'input');\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions} params\n   */\n  const setInputPlaceholder = (input, params) => {\n    if (!input.placeholder && params.inputPlaceholder) {\n      input.placeholder = params.inputPlaceholder;\n    }\n  };\n\n  /**\n   * @param {Input} input\n   * @param {Input} prependTo\n   * @param {SweetAlertOptions} params\n   */\n  const setInputLabel = (input, prependTo, params) => {\n    if (params.inputLabel) {\n      const label = document.createElement('label');\n      const labelClass = swalClasses['input-label'];\n      label.setAttribute('for', input.id);\n      label.className = labelClass;\n      if (typeof params.customClass === 'object') {\n        addClass(label, params.customClass.inputLabel);\n      }\n      label.innerText = params.inputLabel;\n      prependTo.insertAdjacentElement('beforebegin', label);\n    }\n  };\n\n  /**\n   * @param {SweetAlertInput} inputType\n   * @returns {HTMLElement | undefined}\n   */\n  const getInputContainer = inputType => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions['inputValue']} inputValue\n   */\n  const checkAndSetInputValue = (input, inputValue) => {\n    if (['string', 'number'].includes(typeof inputValue)) {\n      input.value = `${inputValue}`;\n    } else if (!isPromise(inputValue)) {\n      warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n    }\n  };\n\n  /** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\n  const renderInputType = {};\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n  (input, params) => {\n    checkAndSetInputValue(input, params.inputValue);\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    input.type = params.input;\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.file = (input, params) => {\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} range\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.range = (range, params) => {\n    const rangeInput = range.querySelector('input');\n    const rangeOutput = range.querySelector('output');\n    checkAndSetInputValue(rangeInput, params.inputValue);\n    rangeInput.type = params.input;\n    checkAndSetInputValue(rangeOutput, params.inputValue);\n    setInputLabel(rangeInput, range, params);\n    return range;\n  };\n\n  /**\n   * @param {HTMLSelectElement} select\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLSelectElement}\n   */\n  renderInputType.select = (select, params) => {\n    select.textContent = '';\n    if (params.inputPlaceholder) {\n      const placeholder = document.createElement('option');\n      setInnerHtml(placeholder, params.inputPlaceholder);\n      placeholder.value = '';\n      placeholder.disabled = true;\n      placeholder.selected = true;\n      select.appendChild(placeholder);\n    }\n    setInputLabel(select, select, params);\n    return select;\n  };\n\n  /**\n   * @param {HTMLInputElement} radio\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.radio = radio => {\n    radio.textContent = '';\n    return radio;\n  };\n\n  /**\n   * @param {HTMLLabelElement} checkboxContainer\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.checkbox = (checkboxContainer, params) => {\n    const checkbox = getInput$1(getPopup(), 'checkbox');\n    checkbox.value = '1';\n    checkbox.checked = Boolean(params.inputValue);\n    const label = checkboxContainer.querySelector('span');\n    setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n    return checkbox;\n  };\n\n  /**\n   * @param {HTMLTextAreaElement} textarea\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLTextAreaElement}\n   */\n  renderInputType.textarea = (textarea, params) => {\n    checkAndSetInputValue(textarea, params.inputValue);\n    setInputPlaceholder(textarea, params);\n    setInputLabel(textarea, textarea, params);\n\n    /**\n     * @param {HTMLElement} el\n     * @returns {number}\n     */\n    const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n    // https://github.com/sweetalert2/sweetalert2/issues/2291\n    setTimeout(() => {\n      // https://github.com/sweetalert2/sweetalert2/issues/1699\n      if ('MutationObserver' in window) {\n        const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n        const textareaResizeHandler = () => {\n          // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n          if (!document.body.contains(textarea)) {\n            return;\n          }\n          const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n          if (textareaWidth > initialPopupWidth) {\n            getPopup().style.width = `${textareaWidth}px`;\n          } else {\n            applyNumericalStyle(getPopup(), 'width', params.width);\n          }\n        };\n        new MutationObserver(textareaResizeHandler).observe(textarea, {\n          attributes: true,\n          attributeFilter: ['style']\n        });\n      }\n    });\n    return textarea;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContent = (instance, params) => {\n    const htmlContainer = getHtmlContainer();\n    if (!htmlContainer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(htmlContainer);\n    applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n    // Content as HTML\n    if (params.html) {\n      parseHtmlToContainer(params.html, htmlContainer);\n      show(htmlContainer, 'block');\n    }\n\n    // Content as plain text\n    else if (params.text) {\n      htmlContainer.textContent = params.text;\n      show(htmlContainer, 'block');\n    }\n\n    // No content\n    else {\n      hide(htmlContainer);\n    }\n    renderInput(instance, params);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderFooter = (instance, params) => {\n    const footer = getFooter();\n    if (!footer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(footer);\n    toggle(footer, params.footer, 'block');\n    if (params.footer) {\n      parseHtmlToContainer(params.footer, footer);\n    }\n\n    // Custom class\n    applyCustomClass(footer, params, 'footer');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderIcon = (instance, params) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    const icon = getIcon();\n    if (!icon) {\n      return;\n    }\n\n    // if the given icon already rendered, apply the styling without re-rendering the icon\n    if (innerParams && params.icon === innerParams.icon) {\n      // Custom or default content\n      setContent(icon, params);\n      applyStyles(icon, params);\n      return;\n    }\n    if (!params.icon && !params.iconHtml) {\n      hide(icon);\n      return;\n    }\n    if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n      error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n      hide(icon);\n      return;\n    }\n    show(icon);\n\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n\n    // Animate icon\n    addClass(icon, params.showClass && params.showClass.icon);\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const applyStyles = (icon, params) => {\n    for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n      if (params.icon !== iconType) {\n        removeClass(icon, iconClassName);\n      }\n    }\n    addClass(icon, params.icon && iconTypes[params.icon]);\n\n    // Icon color\n    setColor(icon, params);\n\n    // Success icon background color\n    adjustSuccessIconBackgroundColor();\n\n    // Custom class\n    applyCustomClass(icon, params, 'icon');\n  };\n\n  // Adjust success icon background color to match the popup background color\n  const adjustSuccessIconBackgroundColor = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n    /** @type {NodeListOf<HTMLElement>} */\n    const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n    for (let i = 0; i < successIconParts.length; i++) {\n      successIconParts[i].style.backgroundColor = popupBackgroundColor;\n    }\n  };\n  const successIconHtml = `\n  <div class=\"swal2-success-circular-line-left\"></div>\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div> <div class=\"swal2-success-fix\"></div>\n  <div class=\"swal2-success-circular-line-right\"></div>\n`;\n  const errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setContent = (icon, params) => {\n    if (!params.icon && !params.iconHtml) {\n      return;\n    }\n    let oldContent = icon.innerHTML;\n    let newContent = '';\n    if (params.iconHtml) {\n      newContent = iconContent(params.iconHtml);\n    } else if (params.icon === 'success') {\n      newContent = successIconHtml;\n      oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n    } else if (params.icon === 'error') {\n      newContent = errorIconHtml;\n    } else if (params.icon) {\n      const defaultIconHtml = {\n        question: '?',\n        warning: '!',\n        info: 'i'\n      };\n      newContent = iconContent(defaultIconHtml[params.icon]);\n    }\n    if (oldContent.trim() !== newContent.trim()) {\n      setInnerHtml(icon, newContent);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setColor = (icon, params) => {\n    if (!params.iconColor) {\n      return;\n    }\n    icon.style.color = params.iconColor;\n    icon.style.borderColor = params.iconColor;\n    for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n      setStyle(icon, sel, 'background-color', params.iconColor);\n    }\n    setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n  };\n\n  /**\n   * @param {string} content\n   * @returns {string}\n   */\n  const iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderImage = (instance, params) => {\n    const image = getImage();\n    if (!image) {\n      return;\n    }\n    if (!params.imageUrl) {\n      hide(image);\n      return;\n    }\n    show(image, '');\n\n    // Src, alt\n    image.setAttribute('src', params.imageUrl);\n    image.setAttribute('alt', params.imageAlt || '');\n\n    // Width, height\n    applyNumericalStyle(image, 'width', params.imageWidth);\n    applyNumericalStyle(image, 'height', params.imageHeight);\n\n    // Class\n    image.className = swalClasses.image;\n    applyCustomClass(image, params, 'image');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderPopup = (instance, params) => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (!container || !popup) {\n      return;\n    }\n\n    // Width\n    // https://github.com/sweetalert2/sweetalert2/issues/2170\n    if (params.toast) {\n      applyNumericalStyle(container, 'width', params.width);\n      popup.style.width = '100%';\n      const loader = getLoader();\n      if (loader) {\n        popup.insertBefore(loader, getIcon());\n      }\n    } else {\n      applyNumericalStyle(popup, 'width', params.width);\n    }\n\n    // Padding\n    applyNumericalStyle(popup, 'padding', params.padding);\n\n    // Color\n    if (params.color) {\n      popup.style.color = params.color;\n    }\n\n    // Background\n    if (params.background) {\n      popup.style.background = params.background;\n    }\n    hide(getValidationMessage());\n\n    // Classes\n    addClasses$1(popup, params);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses$1 = (popup, params) => {\n    const showClass = params.showClass || {};\n    // Default Class + showClass when updating Swal.update({})\n    popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n    if (params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n      addClass(popup, swalClasses.toast);\n    } else {\n      addClass(popup, swalClasses.modal);\n    }\n\n    // Custom class\n    applyCustomClass(popup, params, 'popup');\n    // TODO: remove in the next major\n    if (typeof params.customClass === 'string') {\n      addClass(popup, params.customClass);\n    }\n\n    // Icon class (#1842)\n    if (params.icon) {\n      addClass(popup, swalClasses[`icon-${params.icon}`]);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderProgressSteps = (instance, params) => {\n    const progressStepsContainer = getProgressSteps();\n    if (!progressStepsContainer) {\n      return;\n    }\n    const {\n      progressSteps,\n      currentProgressStep\n    } = params;\n    if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n      hide(progressStepsContainer);\n      return;\n    }\n    show(progressStepsContainer);\n    progressStepsContainer.textContent = '';\n    if (currentProgressStep >= progressSteps.length) {\n      warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n    }\n    progressSteps.forEach((step, index) => {\n      const stepEl = createStepElement(step);\n      progressStepsContainer.appendChild(stepEl);\n      if (index === currentProgressStep) {\n        addClass(stepEl, swalClasses['active-progress-step']);\n      }\n      if (index !== progressSteps.length - 1) {\n        const lineEl = createLineElement(params);\n        progressStepsContainer.appendChild(lineEl);\n      }\n    });\n  };\n\n  /**\n   * @param {string} step\n   * @returns {HTMLLIElement}\n   */\n  const createStepElement = step => {\n    const stepEl = document.createElement('li');\n    addClass(stepEl, swalClasses['progress-step']);\n    setInnerHtml(stepEl, step);\n    return stepEl;\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLLIElement}\n   */\n  const createLineElement = params => {\n    const lineEl = document.createElement('li');\n    addClass(lineEl, swalClasses['progress-step-line']);\n    if (params.progressStepsDistance) {\n      applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n    }\n    return lineEl;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderTitle = (instance, params) => {\n    const title = getTitle();\n    if (!title) {\n      return;\n    }\n    showWhenInnerHtmlPresent(title);\n    toggle(title, params.title || params.titleText, 'block');\n    if (params.title) {\n      parseHtmlToContainer(params.title, title);\n    }\n    if (params.titleText) {\n      title.innerText = params.titleText;\n    }\n\n    // Custom class\n    applyCustomClass(title, params, 'title');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const render = (instance, params) => {\n    renderPopup(instance, params);\n    renderContainer(instance, params);\n    renderProgressSteps(instance, params);\n    renderIcon(instance, params);\n    renderImage(instance, params);\n    renderTitle(instance, params);\n    renderCloseButton(instance, params);\n    renderContent(instance, params);\n    renderActions(instance, params);\n    renderFooter(instance, params);\n    const popup = getPopup();\n    if (typeof params.didRender === 'function' && popup) {\n      params.didRender(popup);\n    }\n    globalState.eventEmitter.emit('didRender', popup);\n  };\n\n  /*\n   * Global function to determine if SweetAlert2 popup is shown\n   */\n  const isVisible = () => {\n    return isVisible$1(getPopup());\n  };\n\n  /*\n   * Global function to click 'Confirm' button\n   */\n  const clickConfirm = () => {\n    var _dom$getConfirmButton;\n    return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n  };\n\n  /*\n   * Global function to click 'Deny' button\n   */\n  const clickDeny = () => {\n    var _dom$getDenyButton;\n    return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n  };\n\n  /*\n   * Global function to click 'Cancel' button\n   */\n  const clickCancel = () => {\n    var _dom$getCancelButton;\n    return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n  };\n\n  /** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n  /** @type {Record<DismissReason, DismissReason>} */\n  const DismissReason = Object.freeze({\n    cancel: 'cancel',\n    backdrop: 'backdrop',\n    close: 'close',\n    esc: 'esc',\n    timer: 'timer'\n  });\n\n  /**\n   * @param {GlobalState} globalState\n   */\n  const removeKeydownHandler = globalState => {\n    if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n      globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = false;\n    }\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {*} dismissWith\n   */\n  const addKeydownHandler = (globalState, innerParams, dismissWith) => {\n    removeKeydownHandler(globalState);\n    if (!innerParams.toast) {\n      globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n      globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n      globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n      globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = true;\n    }\n  };\n\n  /**\n   * @param {number} index\n   * @param {number} increment\n   */\n  const setFocus = (index, increment) => {\n    var _dom$getPopup;\n    const focusableElements = getFocusableElements();\n    // search for visible elements and select the next possible match\n    if (focusableElements.length) {\n      index = index + increment;\n\n      // rollover to first item\n      if (index === focusableElements.length) {\n        index = 0;\n\n        // go to last item\n      } else if (index === -1) {\n        index = focusableElements.length - 1;\n      }\n      focusableElements[index].focus();\n      return;\n    }\n    // no visible focusable elements, focus the popup\n    (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n  };\n  const arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\n  const arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {KeyboardEvent} event\n   * @param {Function} dismissWith\n   */\n  const keydownHandler = (innerParams, event, dismissWith) => {\n    if (!innerParams) {\n      return; // This instance has already been destroyed\n    }\n\n    // Ignore keydown during IME composition\n    // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n    // https://github.com/sweetalert2/sweetalert2/issues/720\n    // https://github.com/sweetalert2/sweetalert2/issues/2406\n    if (event.isComposing || event.keyCode === 229) {\n      return;\n    }\n    if (innerParams.stopKeydownPropagation) {\n      event.stopPropagation();\n    }\n\n    // ENTER\n    if (event.key === 'Enter') {\n      handleEnter(event, innerParams);\n    }\n\n    // TAB\n    else if (event.key === 'Tab') {\n      handleTab(event);\n    }\n\n    // ARROWS - switch focus between buttons\n    else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n      handleArrows(event.key);\n    }\n\n    // ESC\n    else if (event.key === 'Escape') {\n      handleEsc(event, innerParams, dismissWith);\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handleEnter = (event, innerParams) => {\n    // https://github.com/sweetalert2/sweetalert2/issues/2386\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      return;\n    }\n    const input = getInput$1(getPopup(), innerParams.input);\n    if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n      if (['textarea', 'file'].includes(innerParams.input)) {\n        return; // do not submit\n      }\n      clickConfirm();\n      event.preventDefault();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   */\n  const handleTab = event => {\n    const targetElement = event.target;\n    const focusableElements = getFocusableElements();\n    let btnIndex = -1;\n    for (let i = 0; i < focusableElements.length; i++) {\n      if (targetElement === focusableElements[i]) {\n        btnIndex = i;\n        break;\n      }\n    }\n\n    // Cycle to the next button\n    if (!event.shiftKey) {\n      setFocus(btnIndex, 1);\n    }\n\n    // Cycle to the prev button\n    else {\n      setFocus(btnIndex, -1);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  };\n\n  /**\n   * @param {string} key\n   */\n  const handleArrows = key => {\n    const actions = getActions();\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!actions || !confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n    /** @type HTMLElement[] */\n    const buttons = [confirmButton, denyButton, cancelButton];\n    if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n      return;\n    }\n    const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n    let buttonToFocus = document.activeElement;\n    if (!buttonToFocus) {\n      return;\n    }\n    for (let i = 0; i < actions.children.length; i++) {\n      buttonToFocus = buttonToFocus[sibling];\n      if (!buttonToFocus) {\n        return;\n      }\n      if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n        break;\n      }\n    }\n    if (buttonToFocus instanceof HTMLButtonElement) {\n      buttonToFocus.focus();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const handleEsc = (event, innerParams, dismissWith) => {\n    if (callIfFunction(innerParams.allowEscapeKey)) {\n      event.preventDefault();\n      dismissWith(DismissReason.esc);\n    }\n  };\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateMethods = {\n    swalPromiseResolve: new WeakMap(),\n    swalPromiseReject: new WeakMap()\n  };\n\n  // From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n  // Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n  // elements not within the active modal dialog will not be surfaced if a user opens a screen\n  // reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\n  const setAriaHidden = () => {\n    const container = getContainer();\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.contains(container)) {\n        return;\n      }\n      if (el.hasAttribute('aria-hidden')) {\n        el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n      }\n      el.setAttribute('aria-hidden', 'true');\n    });\n  };\n  const unsetAriaHidden = () => {\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.hasAttribute('data-previous-aria-hidden')) {\n        el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n        el.removeAttribute('data-previous-aria-hidden');\n      } else {\n        el.removeAttribute('aria-hidden');\n      }\n    });\n  };\n\n  // @ts-ignore\n  const isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n  /**\n   * Fix iOS scrolling\n   * http://stackoverflow.com/q/39626302\n   */\n  const iOSfix = () => {\n    if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n      const offset = document.body.scrollTop;\n      document.body.style.top = `${offset * -1}px`;\n      addClass(document.body, swalClasses.iosfix);\n      lockBodyScroll();\n    }\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1246\n   */\n  const lockBodyScroll = () => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    /** @type {boolean} */\n    let preventTouchMove;\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchstart = event => {\n      preventTouchMove = shouldPreventTouchMove(event);\n    };\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchmove = event => {\n      if (preventTouchMove) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    };\n  };\n\n  /**\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const shouldPreventTouchMove = event => {\n    const target = event.target;\n    const container = getContainer();\n    const htmlContainer = getHtmlContainer();\n    if (!container || !htmlContainer) {\n      return false;\n    }\n    if (isStylus(event) || isZoom(event)) {\n      return false;\n    }\n    if (target === container) {\n      return true;\n    }\n    if (!isScrollable(container) && target instanceof HTMLElement && target.tagName !== 'INPUT' &&\n    // #1603\n    target.tagName !== 'TEXTAREA' &&\n    // #2266\n    !(isScrollable(htmlContainer) &&\n    // #1944\n    htmlContainer.contains(target))) {\n      return true;\n    }\n    return false;\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1786\n   *\n   * @param {*} event\n   * @returns {boolean}\n   */\n  const isStylus = event => {\n    return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1891\n   *\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const isZoom = event => {\n    return event.touches && event.touches.length > 1;\n  };\n  const undoIOSfix = () => {\n    if (hasClass(document.body, swalClasses.iosfix)) {\n      const offset = parseInt(document.body.style.top, 10);\n      removeClass(document.body, swalClasses.iosfix);\n      document.body.style.top = '';\n      document.body.scrollTop = offset * -1;\n    }\n  };\n\n  /**\n   * Measure scrollbar width for padding body during modal show/hide\n   * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n   *\n   * @returns {number}\n   */\n  const measureScrollbar = () => {\n    const scrollDiv = document.createElement('div');\n    scrollDiv.className = swalClasses['scrollbar-measure'];\n    document.body.appendChild(scrollDiv);\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    return scrollbarWidth;\n  };\n\n  /**\n   * Remember state in cases where opening and handling a modal will fiddle with it.\n   * @type {number | null}\n   */\n  let previousBodyPadding = null;\n\n  /**\n   * @param {string} initialBodyOverflow\n   */\n  const replaceScrollbarWithPadding = initialBodyOverflow => {\n    // for queues, do not do this more than once\n    if (previousBodyPadding !== null) {\n      return;\n    }\n    // if the body has overflow\n    if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n    ) {\n      // add padding so the content doesn't shift after removal of scrollbar\n      previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n      document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n    }\n  };\n  const undoReplaceScrollbarWithPadding = () => {\n    if (previousBodyPadding !== null) {\n      document.body.style.paddingRight = `${previousBodyPadding}px`;\n      previousBodyPadding = null;\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  function removePopupAndResetState(instance, container, returnFocus, didClose) {\n    if (isToast()) {\n      triggerDidCloseAndDispose(instance, didClose);\n    } else {\n      restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n      removeKeydownHandler(globalState);\n    }\n\n    // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n    // for some reason removing the container in Safari will scroll the document to bottom\n    if (isSafariOrIOS) {\n      container.setAttribute('style', 'display:none !important');\n      container.removeAttribute('class');\n      container.innerHTML = '';\n    } else {\n      container.remove();\n    }\n    if (isModal()) {\n      undoReplaceScrollbarWithPadding();\n      undoIOSfix();\n      unsetAriaHidden();\n    }\n    removeBodyClasses();\n  }\n\n  /**\n   * Remove SweetAlert2 classes from body\n   */\n  function removeBodyClasses() {\n    removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n  }\n\n  /**\n   * Instance method to close sweetAlert\n   *\n   * @param {any} resolveValue\n   */\n  function close(resolveValue) {\n    resolveValue = prepareResolveValue(resolveValue);\n    const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n    const didClose = triggerClosePopup(this);\n    if (this.isAwaitingPromise) {\n      // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n      if (!resolveValue.isDismissed) {\n        handleAwaitingPromise(this);\n        swalPromiseResolve(resolveValue);\n      }\n    } else if (didClose) {\n      // Resolve Swal promise\n      swalPromiseResolve(resolveValue);\n    }\n  }\n  const triggerClosePopup = instance => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n      return false;\n    }\n    removeClass(popup, innerParams.showClass.popup);\n    addClass(popup, innerParams.hideClass.popup);\n    const backdrop = getContainer();\n    removeClass(backdrop, innerParams.showClass.backdrop);\n    addClass(backdrop, innerParams.hideClass.backdrop);\n    handlePopupAnimation(instance, popup, innerParams);\n    return true;\n  };\n\n  /**\n   * @param {any} error\n   */\n  function rejectPromise(error) {\n    const rejectPromise = privateMethods.swalPromiseReject.get(this);\n    handleAwaitingPromise(this);\n    if (rejectPromise) {\n      // Reject Swal promise\n      rejectPromise(error);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleAwaitingPromise = instance => {\n    if (instance.isAwaitingPromise) {\n      delete instance.isAwaitingPromise;\n      // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n      if (!privateProps.innerParams.get(instance)) {\n        instance._destroy();\n      }\n    }\n  };\n\n  /**\n   * @param {any} resolveValue\n   * @returns {SweetAlertResult}\n   */\n  const prepareResolveValue = resolveValue => {\n    // When user calls Swal.close()\n    if (typeof resolveValue === 'undefined') {\n      return {\n        isConfirmed: false,\n        isDenied: false,\n        isDismissed: true\n      };\n    }\n    return Object.assign({\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: false\n    }, resolveValue);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handlePopupAnimation = (instance, popup, innerParams) => {\n    var _globalState$eventEmi;\n    const container = getContainer();\n    // If animation is supported, animate\n    const animationIsSupported = hasCssAnimation(popup);\n    if (typeof innerParams.willClose === 'function') {\n      innerParams.willClose(popup);\n    }\n    (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n    if (animationIsSupported) {\n      animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n    } else {\n      // Otherwise, remove immediately\n      removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  const animatePopup = (instance, popup, container, returnFocus, didClose) => {\n    globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n    /**\n     * @param {AnimationEvent | TransitionEvent} e\n     */\n    const swalCloseAnimationFinished = function (e) {\n      if (e.target === popup) {\n        var _globalState$swalClos;\n        (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n        delete globalState.swalCloseEventFinishedCallback;\n        popup.removeEventListener('animationend', swalCloseAnimationFinished);\n        popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n      }\n    };\n    popup.addEventListener('animationend', swalCloseAnimationFinished);\n    popup.addEventListener('transitionend', swalCloseAnimationFinished);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} didClose\n   */\n  const triggerDidCloseAndDispose = (instance, didClose) => {\n    setTimeout(() => {\n      var _globalState$eventEmi2;\n      if (typeof didClose === 'function') {\n        didClose.bind(instance.params)();\n      }\n      (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n      // instance might have been destroyed already\n      if (instance._destroy) {\n        instance._destroy();\n      }\n    });\n  };\n\n  /**\n   * Shows loader (spinner), this is useful with AJAX requests.\n   * By default the loader be shown instead of the \"Confirm\" button.\n   *\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const showLoading = buttonToReplace => {\n    let popup = getPopup();\n    if (!popup) {\n      new Swal();\n    }\n    popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const loader = getLoader();\n    if (isToast()) {\n      hide(getIcon());\n    } else {\n      replaceButton(popup, buttonToReplace);\n    }\n    show(loader);\n    popup.setAttribute('data-loading', 'true');\n    popup.setAttribute('aria-busy', 'true');\n    popup.focus();\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const replaceButton = (popup, buttonToReplace) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n    if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n      buttonToReplace = getConfirmButton();\n    }\n    show(actions);\n    if (buttonToReplace) {\n      hide(buttonToReplace);\n      loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n      actions.insertBefore(loader, buttonToReplace);\n    }\n    addClass([popup, actions], swalClasses.loading);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptionsAndValue = (instance, params) => {\n    if (params.input === 'select' || params.input === 'radio') {\n      handleInputOptions(instance, params);\n    } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n      showLoading(getConfirmButton());\n      handleInputValue(instance, params);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} innerParams\n   * @returns {SweetAlertInputValue}\n   */\n  const getInputValue = (instance, innerParams) => {\n    const input = instance.getInput();\n    if (!input) {\n      return null;\n    }\n    switch (innerParams.input) {\n      case 'checkbox':\n        return getCheckboxValue(input);\n      case 'radio':\n        return getRadioValue(input);\n      case 'file':\n        return getFileValue(input);\n      default:\n        return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {number}\n   */\n  const getCheckboxValue = input => input.checked ? 1 : 0;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {string | null}\n   */\n  const getRadioValue = input => input.checked ? input.value : null;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {FileList | File | null}\n   */\n  const getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptions = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    /**\n     * @param {Record<string, any>} inputOptions\n     */\n    const processInputOptions = inputOptions => {\n      if (params.input === 'select') {\n        populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n      } else if (params.input === 'radio') {\n        populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n      }\n    };\n    if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n      showLoading(getConfirmButton());\n      asPromise(params.inputOptions).then(inputOptions => {\n        instance.hideLoading();\n        processInputOptions(inputOptions);\n      });\n    } else if (typeof params.inputOptions === 'object') {\n      processInputOptions(params.inputOptions);\n    } else {\n      error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputValue = (instance, params) => {\n    const input = instance.getInput();\n    if (!input) {\n      return;\n    }\n    hide(input);\n    asPromise(params.inputValue).then(inputValue => {\n      input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    }).catch(err => {\n      error(`Error in inputValue promise: ${err}`);\n      input.value = '';\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    });\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateSelectOptions(popup, inputOptions, params) {\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    if (!select) {\n      return;\n    }\n    /**\n     * @param {HTMLElement} parent\n     * @param {string} optionLabel\n     * @param {string} optionValue\n     */\n    const renderOption = (parent, optionLabel, optionValue) => {\n      const option = document.createElement('option');\n      option.value = optionValue;\n      setInnerHtml(option, optionLabel);\n      option.selected = isSelected(optionValue, params.inputValue);\n      parent.appendChild(option);\n    };\n    inputOptions.forEach(inputOption => {\n      const optionValue = inputOption[0];\n      const optionLabel = inputOption[1];\n      // <optgroup> spec:\n      // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n      // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n      // check whether this is a <optgroup>\n      if (Array.isArray(optionLabel)) {\n        // if it is an array, then it is an <optgroup>\n        const optgroup = document.createElement('optgroup');\n        optgroup.label = optionValue;\n        optgroup.disabled = false; // not configurable for now\n        select.appendChild(optgroup);\n        optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n      } else {\n        // case of <option>\n        renderOption(select, optionLabel, optionValue);\n      }\n    });\n    select.focus();\n  }\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateRadioOptions(popup, inputOptions, params) {\n    const radio = getDirectChildByClass(popup, swalClasses.radio);\n    if (!radio) {\n      return;\n    }\n    inputOptions.forEach(inputOption => {\n      const radioValue = inputOption[0];\n      const radioLabel = inputOption[1];\n      const radioInput = document.createElement('input');\n      const radioLabelElement = document.createElement('label');\n      radioInput.type = 'radio';\n      radioInput.name = swalClasses.radio;\n      radioInput.value = radioValue;\n      if (isSelected(radioValue, params.inputValue)) {\n        radioInput.checked = true;\n      }\n      const label = document.createElement('span');\n      setInnerHtml(label, radioLabel);\n      label.className = swalClasses.label;\n      radioLabelElement.appendChild(radioInput);\n      radioLabelElement.appendChild(label);\n      radio.appendChild(radioLabelElement);\n    });\n    const radios = radio.querySelectorAll('input');\n    if (radios.length) {\n      radios[0].focus();\n    }\n  }\n\n  /**\n   * Converts `inputOptions` into an array of `[value, label]`s\n   *\n   * @param {Record<string, any>} inputOptions\n   * @typedef {string[]} InputOptionFlattened\n   * @returns {InputOptionFlattened[]}\n   */\n  const formatInputOptions = inputOptions => {\n    /** @type {InputOptionFlattened[]} */\n    const result = [];\n    if (inputOptions instanceof Map) {\n      inputOptions.forEach((value, key) => {\n        let valueFormatted = value;\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    } else {\n      Object.keys(inputOptions).forEach(key => {\n        let valueFormatted = inputOptions[key];\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {string} optionValue\n   * @param {SweetAlertInputValue} inputValue\n   * @returns {boolean}\n   */\n  const isSelected = (optionValue, inputValue) => {\n    return !!inputValue && inputValue.toString() === optionValue.toString();\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleConfirmButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.input) {\n      handleConfirmOrDenyWithInput(instance, 'confirm');\n    } else {\n      confirm(instance, true);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleDenyButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.returnInputValueOnDeny) {\n      handleConfirmOrDenyWithInput(instance, 'deny');\n    } else {\n      deny(instance, false);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} dismissWith\n   */\n  const handleCancelButtonClick = (instance, dismissWith) => {\n    instance.disableButtons();\n    dismissWith(DismissReason.cancel);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleConfirmOrDenyWithInput = (instance, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams.input) {\n      error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n      return;\n    }\n    const input = instance.getInput();\n    const inputValue = getInputValue(instance, innerParams);\n    if (innerParams.inputValidator) {\n      handleInputValidator(instance, inputValue, type);\n    } else if (input && !input.checkValidity()) {\n      instance.enableButtons();\n      instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertInputValue} inputValue\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleInputValidator = (instance, inputValue, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableInput();\n    const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n    validationPromise.then(validationMessage => {\n      instance.enableButtons();\n      instance.enableInput();\n      if (validationMessage) {\n        instance.showValidationMessage(validationMessage);\n      } else if (type === 'deny') {\n        deny(instance, inputValue);\n      } else {\n        confirm(instance, inputValue);\n      }\n    });\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const deny = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnDeny) {\n      showLoading(getDenyButton());\n    }\n    if (innerParams.preDeny) {\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n      const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n      preDenyPromise.then(preDenyValue => {\n        if (preDenyValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          instance.close({\n            isDenied: true,\n            value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n          });\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      instance.close({\n        isDenied: true,\n        value\n      });\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const succeedWith = (instance, value) => {\n    instance.close({\n      isConfirmed: true,\n      value\n    });\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {string} error\n   */\n  const rejectWith = (instance, error) => {\n    instance.rejectPromise(error);\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const confirm = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnConfirm) {\n      showLoading();\n    }\n    if (innerParams.preConfirm) {\n      instance.resetValidationMessage();\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n      const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n      preConfirmPromise.then(preConfirmValue => {\n        if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      succeedWith(instance, value);\n    }\n  };\n\n  /**\n   * Hides loader and shows back the button which was hidden by .showLoading()\n   */\n  function hideLoading() {\n    // do nothing if popup is closed\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      return;\n    }\n    const domCache = privateProps.domCache.get(this);\n    hide(domCache.loader);\n    if (isToast()) {\n      if (innerParams.icon) {\n        show(getIcon());\n      }\n    } else {\n      showRelatedButton(domCache);\n    }\n    removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n    domCache.popup.removeAttribute('aria-busy');\n    domCache.popup.removeAttribute('data-loading');\n    domCache.confirmButton.disabled = false;\n    domCache.denyButton.disabled = false;\n    domCache.cancelButton.disabled = false;\n  }\n  const showRelatedButton = domCache => {\n    const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n    if (buttonToReplace.length) {\n      show(buttonToReplace[0], 'inline-block');\n    } else if (allButtonsAreHidden()) {\n      hide(domCache.actions);\n    }\n  };\n\n  /**\n   * Gets the input DOM node, this method works with input parameter.\n   *\n   * @returns {HTMLInputElement | null}\n   */\n  function getInput() {\n    const innerParams = privateProps.innerParams.get(this);\n    const domCache = privateProps.domCache.get(this);\n    if (!domCache) {\n      return null;\n    }\n    return getInput$1(domCache.popup, innerParams.input);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {string[]} buttons\n   * @param {boolean} disabled\n   */\n  function setButtonsDisabled(instance, buttons, disabled) {\n    const domCache = privateProps.domCache.get(instance);\n    buttons.forEach(button => {\n      domCache[button].disabled = disabled;\n    });\n  }\n\n  /**\n   * @param {HTMLInputElement | null} input\n   * @param {boolean} disabled\n   */\n  function setInputDisabled(input, disabled) {\n    const popup = getPopup();\n    if (!popup || !input) {\n      return;\n    }\n    if (input.type === 'radio') {\n      /** @type {NodeListOf<HTMLInputElement>} */\n      const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n      for (let i = 0; i < radios.length; i++) {\n        radios[i].disabled = disabled;\n      }\n    } else {\n      input.disabled = disabled;\n    }\n  }\n\n  /**\n   * Enable all the buttons\n   * @this {SweetAlert}\n   */\n  function enableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n  }\n\n  /**\n   * Disable all the buttons\n   * @this {SweetAlert}\n   */\n  function disableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n  }\n\n  /**\n   * Enable the input field\n   * @this {SweetAlert}\n   */\n  function enableInput() {\n    setInputDisabled(this.getInput(), false);\n  }\n\n  /**\n   * Disable the input field\n   * @this {SweetAlert}\n   */\n  function disableInput() {\n    setInputDisabled(this.getInput(), true);\n  }\n\n  /**\n   * Show block with validation message\n   *\n   * @param {string} error\n   * @this {SweetAlert}\n   */\n  function showValidationMessage(error) {\n    const domCache = privateProps.domCache.get(this);\n    const params = privateProps.innerParams.get(this);\n    setInnerHtml(domCache.validationMessage, error);\n    domCache.validationMessage.className = swalClasses['validation-message'];\n    if (params.customClass && params.customClass.validationMessage) {\n      addClass(domCache.validationMessage, params.customClass.validationMessage);\n    }\n    show(domCache.validationMessage);\n    const input = this.getInput();\n    if (input) {\n      input.setAttribute('aria-invalid', 'true');\n      input.setAttribute('aria-describedby', swalClasses['validation-message']);\n      focusInput(input);\n      addClass(input, swalClasses.inputerror);\n    }\n  }\n\n  /**\n   * Hide block with validation message\n   *\n   * @this {SweetAlert}\n   */\n  function resetValidationMessage() {\n    const domCache = privateProps.domCache.get(this);\n    if (domCache.validationMessage) {\n      hide(domCache.validationMessage);\n    }\n    const input = this.getInput();\n    if (input) {\n      input.removeAttribute('aria-invalid');\n      input.removeAttribute('aria-describedby');\n      removeClass(input, swalClasses.inputerror);\n    }\n  }\n\n  const defaultParams = {\n    title: '',\n    titleText: '',\n    text: '',\n    html: '',\n    footer: '',\n    icon: undefined,\n    iconColor: undefined,\n    iconHtml: undefined,\n    template: undefined,\n    toast: false,\n    animation: true,\n    showClass: {\n      popup: 'swal2-show',\n      backdrop: 'swal2-backdrop-show',\n      icon: 'swal2-icon-show'\n    },\n    hideClass: {\n      popup: 'swal2-hide',\n      backdrop: 'swal2-backdrop-hide',\n      icon: 'swal2-icon-hide'\n    },\n    customClass: {},\n    target: 'body',\n    color: undefined,\n    backdrop: true,\n    heightAuto: true,\n    allowOutsideClick: true,\n    allowEscapeKey: true,\n    allowEnterKey: true,\n    stopKeydownPropagation: true,\n    keydownListenerCapture: false,\n    showConfirmButton: true,\n    showDenyButton: false,\n    showCancelButton: false,\n    preConfirm: undefined,\n    preDeny: undefined,\n    confirmButtonText: 'OK',\n    confirmButtonAriaLabel: '',\n    confirmButtonColor: undefined,\n    denyButtonText: 'No',\n    denyButtonAriaLabel: '',\n    denyButtonColor: undefined,\n    cancelButtonText: 'Cancel',\n    cancelButtonAriaLabel: '',\n    cancelButtonColor: undefined,\n    buttonsStyling: true,\n    reverseButtons: false,\n    focusConfirm: true,\n    focusDeny: false,\n    focusCancel: false,\n    returnFocus: true,\n    showCloseButton: false,\n    closeButtonHtml: '&times;',\n    closeButtonAriaLabel: 'Close this dialog',\n    loaderHtml: '',\n    showLoaderOnConfirm: false,\n    showLoaderOnDeny: false,\n    imageUrl: undefined,\n    imageWidth: undefined,\n    imageHeight: undefined,\n    imageAlt: '',\n    timer: undefined,\n    timerProgressBar: false,\n    width: undefined,\n    padding: undefined,\n    background: undefined,\n    input: undefined,\n    inputPlaceholder: '',\n    inputLabel: '',\n    inputValue: '',\n    inputOptions: {},\n    inputAutoFocus: true,\n    inputAutoTrim: true,\n    inputAttributes: {},\n    inputValidator: undefined,\n    returnInputValueOnDeny: false,\n    validationMessage: undefined,\n    grow: false,\n    position: 'center',\n    progressSteps: [],\n    currentProgressStep: undefined,\n    progressStepsDistance: undefined,\n    willOpen: undefined,\n    didOpen: undefined,\n    didRender: undefined,\n    willClose: undefined,\n    didClose: undefined,\n    didDestroy: undefined,\n    scrollbarPadding: true\n  };\n  const updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'willClose'];\n\n  /** @type {Record<string, string | undefined>} */\n  const deprecatedParams = {\n    allowEnterKey: undefined\n  };\n  const toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n  /**\n   * Is valid parameter\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isValidParameter = paramName => {\n    return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n  };\n\n  /**\n   * Is valid parameter for Swal.update() method\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isUpdatableParameter = paramName => {\n    return updatableParams.indexOf(paramName) !== -1;\n  };\n\n  /**\n   * Is deprecated parameter\n   *\n   * @param {string} paramName\n   * @returns {string | undefined}\n   */\n  const isDeprecatedParameter = paramName => {\n    return deprecatedParams[paramName];\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsValid = param => {\n    if (!isValidParameter(param)) {\n      warn(`Unknown parameter \"${param}\"`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfToastParamIsValid = param => {\n    if (toastIncompatibleParams.includes(param)) {\n      warn(`The parameter \"${param}\" is incompatible with toasts`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsDeprecated = param => {\n    const isDeprecated = isDeprecatedParameter(param);\n    if (isDeprecated) {\n      warnAboutDeprecation(param, isDeprecated);\n    }\n  };\n\n  /**\n   * Show relevant warnings for given params\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const showWarningsForParams = params => {\n    if (params.backdrop === false && params.allowOutsideClick) {\n      warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n    }\n    for (const param in params) {\n      checkIfParamIsValid(param);\n      if (params.toast) {\n        checkIfToastParamIsValid(param);\n      }\n      checkIfParamIsDeprecated(param);\n    }\n  };\n\n  /**\n   * Updates popup parameters.\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function update(params) {\n    const popup = getPopup();\n    const innerParams = privateProps.innerParams.get(this);\n    if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n      warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n      return;\n    }\n    const validUpdatableParams = filterValidParams(params);\n    const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n    render(this, updatedParams);\n    privateProps.innerParams.set(this, updatedParams);\n    Object.defineProperties(this, {\n      params: {\n        value: Object.assign({}, this.params, params),\n        writable: false,\n        enumerable: true\n      }\n    });\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const filterValidParams = params => {\n    const validUpdatableParams = {};\n    Object.keys(params).forEach(param => {\n      if (isUpdatableParameter(param)) {\n        validUpdatableParams[param] = params[param];\n      } else {\n        warn(`Invalid parameter to update: ${param}`);\n      }\n    });\n    return validUpdatableParams;\n  };\n\n  /**\n   * Dispose the current SweetAlert2 instance\n   */\n  function _destroy() {\n    const domCache = privateProps.domCache.get(this);\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n      return; // This instance has already been destroyed\n    }\n\n    // Check if there is another Swal closing\n    if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n      globalState.swalCloseEventFinishedCallback();\n      delete globalState.swalCloseEventFinishedCallback;\n    }\n    if (typeof innerParams.didDestroy === 'function') {\n      innerParams.didDestroy();\n    }\n    globalState.eventEmitter.emit('didDestroy');\n    disposeSwal(this);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeSwal = instance => {\n    disposeWeakMaps(instance);\n    // Unset this.params so GC will dispose it (#1569)\n    delete instance.params;\n    // Unset globalState props so GC will dispose globalState (#1569)\n    delete globalState.keydownHandler;\n    delete globalState.keydownTarget;\n    // Unset currentInstance\n    delete globalState.currentInstance;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeWeakMaps = instance => {\n    // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n    if (instance.isAwaitingPromise) {\n      unsetWeakMaps(privateProps, instance);\n      instance.isAwaitingPromise = true;\n    } else {\n      unsetWeakMaps(privateMethods, instance);\n      unsetWeakMaps(privateProps, instance);\n      delete instance.isAwaitingPromise;\n      // Unset instance methods\n      delete instance.disableButtons;\n      delete instance.enableButtons;\n      delete instance.getInput;\n      delete instance.disableInput;\n      delete instance.enableInput;\n      delete instance.hideLoading;\n      delete instance.disableLoading;\n      delete instance.showValidationMessage;\n      delete instance.resetValidationMessage;\n      delete instance.close;\n      delete instance.closePopup;\n      delete instance.closeModal;\n      delete instance.closeToast;\n      delete instance.rejectPromise;\n      delete instance.update;\n      delete instance._destroy;\n    }\n  };\n\n  /**\n   * @param {object} obj\n   * @param {SweetAlert} instance\n   */\n  const unsetWeakMaps = (obj, instance) => {\n    for (const i in obj) {\n      obj[i].delete(instance);\n    }\n  };\n\n  var instanceMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    _destroy: _destroy,\n    close: close,\n    closeModal: close,\n    closePopup: close,\n    closeToast: close,\n    disableButtons: disableButtons,\n    disableInput: disableInput,\n    disableLoading: hideLoading,\n    enableButtons: enableButtons,\n    enableInput: enableInput,\n    getInput: getInput,\n    handleAwaitingPromise: handleAwaitingPromise,\n    hideLoading: hideLoading,\n    rejectPromise: rejectPromise,\n    resetValidationMessage: resetValidationMessage,\n    showValidationMessage: showValidationMessage,\n    update: update\n  });\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handlePopupClick = (innerParams, domCache, dismissWith) => {\n    if (innerParams.toast) {\n      handleToastClick(innerParams, domCache, dismissWith);\n    } else {\n      // Ignore click events that had mousedown on the popup but mouseup on the container\n      // This can happen when the user drags a slider\n      handleModalMousedown(domCache);\n\n      // Ignore click events that had mousedown on the container but mouseup on the popup\n      handleContainerMousedown(domCache);\n      handleModalClick(innerParams, domCache, dismissWith);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleToastClick = (innerParams, domCache, dismissWith) => {\n    // Closing toast by internal click\n    domCache.popup.onclick = () => {\n      if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n        return;\n      }\n      dismissWith(DismissReason.close);\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const isAnyButtonShown = innerParams => {\n    return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n  };\n  let ignoreOutsideClick = false;\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleModalMousedown = domCache => {\n    domCache.popup.onmousedown = () => {\n      domCache.container.onmouseup = function (e) {\n        domCache.container.onmouseup = () => {};\n        // We only check if the mouseup target is the container because usually it doesn't\n        // have any other direct children aside of the popup\n        if (e.target === domCache.container) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleContainerMousedown = domCache => {\n    domCache.container.onmousedown = e => {\n      // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n      if (e.target === domCache.container) {\n        e.preventDefault();\n      }\n      domCache.popup.onmouseup = function (e) {\n        domCache.popup.onmouseup = () => {};\n        // We also need to check if the mouseup target is a child of the popup\n        if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleModalClick = (innerParams, domCache, dismissWith) => {\n    domCache.container.onclick = e => {\n      if (ignoreOutsideClick) {\n        ignoreOutsideClick = false;\n        return;\n      }\n      if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n        dismissWith(DismissReason.backdrop);\n      }\n    };\n  };\n\n  const isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\n  const isElement = elem => elem instanceof Element || isJqueryElement(elem);\n  const argsToParams = args => {\n    const params = {};\n    if (typeof args[0] === 'object' && !isElement(args[0])) {\n      Object.assign(params, args[0]);\n    } else {\n      ['title', 'html', 'icon'].forEach((name, index) => {\n        const arg = args[index];\n        if (typeof arg === 'string' || isElement(arg)) {\n          params[name] = arg;\n        } else if (arg !== undefined) {\n          error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n        }\n      });\n    }\n    return params;\n  };\n\n  /**\n   * Main method to create a new SweetAlert2 popup\n   *\n   * @param  {...SweetAlertOptions} args\n   * @returns {Promise<SweetAlertResult>}\n   */\n  function fire() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new this(...args);\n  }\n\n  /**\n   * Returns an extended version of `Swal` containing `params` as defaults.\n   * Useful for reusing Swal configuration.\n   *\n   * For example:\n   *\n   * Before:\n   * const textPromptOptions = { input: 'text', showCancelButton: true }\n   * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n   * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n   *\n   * After:\n   * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n   * const {value: firstName} = await TextPrompt('What is your first name?')\n   * const {value: lastName} = await TextPrompt('What is your last name?')\n   *\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlert}\n   */\n  function mixin(mixinParams) {\n    class MixinSwal extends this {\n      _main(params, priorityMixinParams) {\n        return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n      }\n    }\n    // @ts-ignore\n    return MixinSwal;\n  }\n\n  /**\n   * If `timer` parameter is set, returns number of milliseconds of timer remained.\n   * Otherwise, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const getTimerLeft = () => {\n    return globalState.timeout && globalState.timeout.getTimerLeft();\n  };\n\n  /**\n   * Stop timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const stopTimer = () => {\n    if (globalState.timeout) {\n      stopTimerProgressBar();\n      return globalState.timeout.stop();\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const resumeTimer = () => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.start();\n      animateTimerProgressBar(remaining);\n      return remaining;\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const toggleTimer = () => {\n    const timer = globalState.timeout;\n    return timer && (timer.running ? stopTimer() : resumeTimer());\n  };\n\n  /**\n   * Increase timer. Returns number of milliseconds of an updated timer.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @param {number} ms\n   * @returns {number | undefined}\n   */\n  const increaseTimer = ms => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.increase(ms);\n      animateTimerProgressBar(remaining, true);\n      return remaining;\n    }\n  };\n\n  /**\n   * Check if timer is running. Returns true if timer is running\n   * or false if timer is paused or stopped.\n   * If `timer` parameter isn't set, returns undefined\n   *\n   * @returns {boolean}\n   */\n  const isTimerRunning = () => {\n    return !!(globalState.timeout && globalState.timeout.isRunning());\n  };\n\n  let bodyClickListenerAdded = false;\n  const clickHandlers = {};\n\n  /**\n   * @param {string} attr\n   */\n  function bindClickHandler() {\n    let attr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'data-swal-template';\n    clickHandlers[attr] = this;\n    if (!bodyClickListenerAdded) {\n      document.body.addEventListener('click', bodyClickListener);\n      bodyClickListenerAdded = true;\n    }\n  }\n  const bodyClickListener = event => {\n    for (let el = event.target; el && el !== document; el = el.parentNode) {\n      for (const attr in clickHandlers) {\n        const template = el.getAttribute(attr);\n        if (template) {\n          clickHandlers[attr].fire({\n            template\n          });\n          return;\n        }\n      }\n    }\n  };\n\n  // Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\n  class EventEmitter {\n    constructor() {\n      /** @type {Events} */\n      this.events = {};\n    }\n\n    /**\n     * @param {string} eventName\n     * @returns {EventHandlers}\n     */\n    _getHandlersByEventName(eventName) {\n      if (typeof this.events[eventName] === 'undefined') {\n        // not Set because we need to keep the FIFO order\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n        this.events[eventName] = [];\n      }\n      return this.events[eventName];\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    on(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      if (!currentHandlers.includes(eventHandler)) {\n        currentHandlers.push(eventHandler);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    once(eventName, eventHandler) {\n      var _this = this;\n      /**\n       * @param {Array} args\n       */\n      const onceFn = function () {\n        _this.removeListener(eventName, onceFn);\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        eventHandler.apply(_this, args);\n      };\n      this.on(eventName, onceFn);\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {Array} args\n     */\n    emit(eventName) {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n      this._getHandlersByEventName(eventName).forEach(\n      /**\n       * @param {EventHandler} eventHandler\n       */\n      eventHandler => {\n        try {\n          eventHandler.apply(this, args);\n        } catch (error) {\n          console.error(error);\n        }\n      });\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    removeListener(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      const index = currentHandlers.indexOf(eventHandler);\n      if (index > -1) {\n        currentHandlers.splice(index, 1);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     */\n    removeAllListeners(eventName) {\n      if (this.events[eventName] !== undefined) {\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n        this.events[eventName].length = 0;\n      }\n    }\n    reset() {\n      this.events = {};\n    }\n  }\n\n  globalState.eventEmitter = new EventEmitter();\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const on = (eventName, eventHandler) => {\n    globalState.eventEmitter.on(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const once = (eventName, eventHandler) => {\n    globalState.eventEmitter.once(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} [eventName]\n   * @param {EventHandler} [eventHandler]\n   */\n  const off = (eventName, eventHandler) => {\n    // Remove all handlers for all events\n    if (!eventName) {\n      globalState.eventEmitter.reset();\n      return;\n    }\n    if (eventHandler) {\n      // Remove a specific handler\n      globalState.eventEmitter.removeListener(eventName, eventHandler);\n    } else {\n      // Remove all handlers for a specific event\n      globalState.eventEmitter.removeAllListeners(eventName);\n    }\n  };\n\n  var staticMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    argsToParams: argsToParams,\n    bindClickHandler: bindClickHandler,\n    clickCancel: clickCancel,\n    clickConfirm: clickConfirm,\n    clickDeny: clickDeny,\n    enableLoading: showLoading,\n    fire: fire,\n    getActions: getActions,\n    getCancelButton: getCancelButton,\n    getCloseButton: getCloseButton,\n    getConfirmButton: getConfirmButton,\n    getContainer: getContainer,\n    getDenyButton: getDenyButton,\n    getFocusableElements: getFocusableElements,\n    getFooter: getFooter,\n    getHtmlContainer: getHtmlContainer,\n    getIcon: getIcon,\n    getIconContent: getIconContent,\n    getImage: getImage,\n    getInputLabel: getInputLabel,\n    getLoader: getLoader,\n    getPopup: getPopup,\n    getProgressSteps: getProgressSteps,\n    getTimerLeft: getTimerLeft,\n    getTimerProgressBar: getTimerProgressBar,\n    getTitle: getTitle,\n    getValidationMessage: getValidationMessage,\n    increaseTimer: increaseTimer,\n    isDeprecatedParameter: isDeprecatedParameter,\n    isLoading: isLoading,\n    isTimerRunning: isTimerRunning,\n    isUpdatableParameter: isUpdatableParameter,\n    isValidParameter: isValidParameter,\n    isVisible: isVisible,\n    mixin: mixin,\n    off: off,\n    on: on,\n    once: once,\n    resumeTimer: resumeTimer,\n    showLoading: showLoading,\n    stopTimer: stopTimer,\n    toggleTimer: toggleTimer\n  });\n\n  class Timer {\n    /**\n     * @param {Function} callback\n     * @param {number} delay\n     */\n    constructor(callback, delay) {\n      this.callback = callback;\n      this.remaining = delay;\n      this.running = false;\n      this.start();\n    }\n\n    /**\n     * @returns {number}\n     */\n    start() {\n      if (!this.running) {\n        this.running = true;\n        this.started = new Date();\n        this.id = setTimeout(this.callback, this.remaining);\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    stop() {\n      if (this.started && this.running) {\n        this.running = false;\n        clearTimeout(this.id);\n        this.remaining -= new Date().getTime() - this.started.getTime();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @param {number} n\n     * @returns {number}\n     */\n    increase(n) {\n      const running = this.running;\n      if (running) {\n        this.stop();\n      }\n      this.remaining += n;\n      if (running) {\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    getTimerLeft() {\n      if (this.running) {\n        this.stop();\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {boolean}\n     */\n    isRunning() {\n      return this.running;\n    }\n  }\n\n  const swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const getTemplateParams = params => {\n    const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n    if (!template) {\n      return {};\n    }\n    /** @type {DocumentFragment} */\n    const templateContent = template.content;\n    showWarningsForElements(templateContent);\n    const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n    swalParams.forEach(param => {\n      showWarningsForAttributes(param, ['name', 'value']);\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      if (typeof defaultParams[paramName] === 'boolean') {\n        result[paramName] = value !== 'false';\n      } else if (typeof defaultParams[paramName] === 'object') {\n        result[paramName] = JSON.parse(value);\n      } else {\n        result[paramName] = value;\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalFunctionParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n    swalFunctions.forEach(param => {\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      result[paramName] = new Function(`return ${value}`)();\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalButtons = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n    swalButtons.forEach(button => {\n      showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n      const type = button.getAttribute('type');\n      if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n        return;\n      }\n      result[`${type}ButtonText`] = button.innerHTML;\n      result[`show${capitalizeFirstLetter(type)}Button`] = true;\n      if (button.hasAttribute('color')) {\n        result[`${type}ButtonColor`] = button.getAttribute('color');\n      }\n      if (button.hasAttribute('aria-label')) {\n        result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n   */\n  const getSwalImage = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const image = templateContent.querySelector('swal-image');\n    if (image) {\n      showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n      if (image.hasAttribute('src')) {\n        result.imageUrl = image.getAttribute('src') || undefined;\n      }\n      if (image.hasAttribute('width')) {\n        result.imageWidth = image.getAttribute('width') || undefined;\n      }\n      if (image.hasAttribute('height')) {\n        result.imageHeight = image.getAttribute('height') || undefined;\n      }\n      if (image.hasAttribute('alt')) {\n        result.imageAlt = image.getAttribute('alt') || undefined;\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalIcon = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const icon = templateContent.querySelector('swal-icon');\n    if (icon) {\n      showWarningsForAttributes(icon, ['type', 'color']);\n      if (icon.hasAttribute('type')) {\n        result.icon = icon.getAttribute('type');\n      }\n      if (icon.hasAttribute('color')) {\n        result.iconColor = icon.getAttribute('color');\n      }\n      result.iconHtml = icon.innerHTML;\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalInput = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const input = templateContent.querySelector('swal-input');\n    if (input) {\n      showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n      result.input = input.getAttribute('type') || 'text';\n      if (input.hasAttribute('label')) {\n        result.inputLabel = input.getAttribute('label');\n      }\n      if (input.hasAttribute('placeholder')) {\n        result.inputPlaceholder = input.getAttribute('placeholder');\n      }\n      if (input.hasAttribute('value')) {\n        result.inputValue = input.getAttribute('value');\n      }\n    }\n    /** @type {HTMLElement[]} */\n    const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n    if (inputOptions.length) {\n      result.inputOptions = {};\n      inputOptions.forEach(option => {\n        showWarningsForAttributes(option, ['value']);\n        const optionValue = option.getAttribute('value');\n        if (!optionValue) {\n          return;\n        }\n        const optionName = option.innerHTML;\n        result.inputOptions[optionValue] = optionName;\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @param {string[]} paramNames\n   * @returns {Record<string, any>}\n   */\n  const getSwalStringParams = (templateContent, paramNames) => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    for (const i in paramNames) {\n      const paramName = paramNames[i];\n      /** @type {HTMLElement | null} */\n      const tag = templateContent.querySelector(paramName);\n      if (tag) {\n        showWarningsForAttributes(tag, []);\n        result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   */\n  const showWarningsForElements = templateContent => {\n    const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n    Array.from(templateContent.children).forEach(el => {\n      const tagName = el.tagName.toLowerCase();\n      if (!allowedElements.includes(tagName)) {\n        warn(`Unrecognized element <${tagName}>`);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} el\n   * @param {string[]} allowedAttributes\n   */\n  const showWarningsForAttributes = (el, allowedAttributes) => {\n    Array.from(el.attributes).forEach(attribute => {\n      if (allowedAttributes.indexOf(attribute.name) === -1) {\n        warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n      }\n    });\n  };\n\n  const SHOW_CLASS_TIMEOUT = 10;\n\n  /**\n   * Open popup, add necessary classes and styles, fix scrollbar\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const openPopup = params => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (typeof params.willOpen === 'function') {\n      params.willOpen(popup);\n    }\n    globalState.eventEmitter.emit('willOpen', popup);\n    const bodyStyles = window.getComputedStyle(document.body);\n    const initialBodyOverflow = bodyStyles.overflowY;\n    addClasses(container, popup, params);\n\n    // scrolling is 'hidden' until animation is done, after that 'auto'\n    setTimeout(() => {\n      setScrollingVisibility(container, popup);\n    }, SHOW_CLASS_TIMEOUT);\n    if (isModal()) {\n      fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n      setAriaHidden();\n    }\n    if (!isToast() && !globalState.previousActiveElement) {\n      globalState.previousActiveElement = document.activeElement;\n    }\n    if (typeof params.didOpen === 'function') {\n      setTimeout(() => params.didOpen(popup));\n    }\n    globalState.eventEmitter.emit('didOpen', popup);\n    removeClass(container, swalClasses['no-transition']);\n  };\n\n  /**\n   * @param {AnimationEvent} event\n   */\n  const swalOpenAnimationFinished = event => {\n    const popup = getPopup();\n    if (event.target !== popup) {\n      return;\n    }\n    const container = getContainer();\n    popup.removeEventListener('animationend', swalOpenAnimationFinished);\n    popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n    container.style.overflowY = 'auto';\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   */\n  const setScrollingVisibility = (container, popup) => {\n    if (hasCssAnimation(popup)) {\n      container.style.overflowY = 'hidden';\n      popup.addEventListener('animationend', swalOpenAnimationFinished);\n      popup.addEventListener('transitionend', swalOpenAnimationFinished);\n    } else {\n      container.style.overflowY = 'auto';\n    }\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {boolean} scrollbarPadding\n   * @param {string} initialBodyOverflow\n   */\n  const fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n    iOSfix();\n    if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n      replaceScrollbarWithPadding(initialBodyOverflow);\n    }\n\n    // sweetalert2/issues/1247\n    setTimeout(() => {\n      container.scrollTop = 0;\n    });\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses = (container, popup, params) => {\n    addClass(container, params.showClass.backdrop);\n    if (params.animation) {\n      // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n      popup.style.setProperty('opacity', '0', 'important');\n      show(popup, 'grid');\n      setTimeout(() => {\n        // Animate popup right after showing it\n        addClass(popup, params.showClass.popup);\n        // and remove the opacity workaround\n        popup.style.removeProperty('opacity');\n      }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n    } else {\n      show(popup, 'grid');\n    }\n    addClass([document.documentElement, document.body], swalClasses.shown);\n    if (params.heightAuto && params.backdrop && !params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['height-auto']);\n    }\n  };\n\n  var defaultInputValidators = {\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    email: (string, validationMessage) => {\n      return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n    },\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    url: (string, validationMessage) => {\n      // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n      return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function setDefaultInputValidators(params) {\n    // Use default `inputValidator` for supported input types if not provided\n    if (params.inputValidator) {\n      return;\n    }\n    if (params.input === 'email') {\n      params.inputValidator = defaultInputValidators['email'];\n    }\n    if (params.input === 'url') {\n      params.inputValidator = defaultInputValidators['url'];\n    }\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function validateCustomTargetElement(params) {\n    // Determine if the custom target element is valid\n    if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n      warn('Target parameter is not valid, defaulting to \"body\"');\n      params.target = 'body';\n    }\n  }\n\n  /**\n   * Set type, text and actions on popup\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function setParameters(params) {\n    setDefaultInputValidators(params);\n\n    // showLoaderOnConfirm && preConfirm\n    if (params.showLoaderOnConfirm && !params.preConfirm) {\n      warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n    }\n    validateCustomTargetElement(params);\n\n    // Replace newlines with <br> in title\n    if (typeof params.title === 'string') {\n      params.title = params.title.split('\\n').join('<br />');\n    }\n    init(params);\n  }\n\n  /** @type {SweetAlert} */\n  let currentInstance;\n  var _promise = /*#__PURE__*/new WeakMap();\n  class SweetAlert {\n    /**\n     * @param {...any} args\n     * @this {SweetAlert}\n     */\n    constructor() {\n      /**\n       * @type {Promise<SweetAlertResult>}\n       */\n      _classPrivateFieldInitSpec(this, _promise, void 0);\n      // Prevent run in Node env\n      if (typeof window === 'undefined') {\n        return;\n      }\n      currentInstance = this;\n\n      // @ts-ignore\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n      /** @type {Readonly<SweetAlertOptions>} */\n      this.params = outerParams;\n\n      /** @type {boolean} */\n      this.isAwaitingPromise = false;\n      _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n    }\n    _main(userParams) {\n      let mixinParams = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      showWarningsForParams(Object.assign({}, mixinParams, userParams));\n      if (globalState.currentInstance) {\n        const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n        const {\n          isAwaitingPromise\n        } = globalState.currentInstance;\n        globalState.currentInstance._destroy();\n        if (!isAwaitingPromise) {\n          swalPromiseResolve({\n            isDismissed: true\n          });\n        }\n        if (isModal()) {\n          unsetAriaHidden();\n        }\n      }\n      globalState.currentInstance = currentInstance;\n      const innerParams = prepareParams(userParams, mixinParams);\n      setParameters(innerParams);\n      Object.freeze(innerParams);\n\n      // clear the previous timer\n      if (globalState.timeout) {\n        globalState.timeout.stop();\n        delete globalState.timeout;\n      }\n\n      // clear the restore focus timeout\n      clearTimeout(globalState.restoreFocusTimeout);\n      const domCache = populateDomCache(currentInstance);\n      render(currentInstance, innerParams);\n      privateProps.innerParams.set(currentInstance, innerParams);\n      return swalPromise(currentInstance, domCache, innerParams);\n    }\n\n    // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n    then(onFulfilled) {\n      return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n    }\n    finally(onFinally) {\n      return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {Promise}\n   */\n  const swalPromise = (instance, domCache, innerParams) => {\n    return new Promise((resolve, reject) => {\n      // functions to handle all closings/dismissals\n      /**\n       * @param {DismissReason} dismiss\n       */\n      const dismissWith = dismiss => {\n        instance.close({\n          isDismissed: true,\n          dismiss\n        });\n      };\n      privateMethods.swalPromiseResolve.set(instance, resolve);\n      privateMethods.swalPromiseReject.set(instance, reject);\n      domCache.confirmButton.onclick = () => {\n        handleConfirmButtonClick(instance);\n      };\n      domCache.denyButton.onclick = () => {\n        handleDenyButtonClick(instance);\n      };\n      domCache.cancelButton.onclick = () => {\n        handleCancelButtonClick(instance, dismissWith);\n      };\n      domCache.closeButton.onclick = () => {\n        dismissWith(DismissReason.close);\n      };\n      handlePopupClick(innerParams, domCache, dismissWith);\n      addKeydownHandler(globalState, innerParams, dismissWith);\n      handleInputOptionsAndValue(instance, innerParams);\n      openPopup(innerParams);\n      setupTimer(globalState, innerParams, dismissWith);\n      initFocus(domCache, innerParams);\n\n      // Scroll container to top on open (#1247, #1946)\n      setTimeout(() => {\n        domCache.container.scrollTop = 0;\n      });\n    });\n  };\n\n  /**\n   * @param {SweetAlertOptions} userParams\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlertOptions}\n   */\n  const prepareParams = (userParams, mixinParams) => {\n    const templateParams = getTemplateParams(userParams);\n    const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n    params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n    params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n    if (params.animation === false) {\n      params.showClass = {\n        backdrop: 'swal2-noanimation'\n      };\n      params.hideClass = {};\n    }\n    return params;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @returns {DomCache}\n   */\n  const populateDomCache = instance => {\n    const domCache = {\n      popup: getPopup(),\n      container: getContainer(),\n      actions: getActions(),\n      confirmButton: getConfirmButton(),\n      denyButton: getDenyButton(),\n      cancelButton: getCancelButton(),\n      loader: getLoader(),\n      closeButton: getCloseButton(),\n      validationMessage: getValidationMessage(),\n      progressSteps: getProgressSteps()\n    };\n    privateProps.domCache.set(instance, domCache);\n    return domCache;\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const setupTimer = (globalState, innerParams, dismissWith) => {\n    const timerProgressBar = getTimerProgressBar();\n    hide(timerProgressBar);\n    if (innerParams.timer) {\n      globalState.timeout = new Timer(() => {\n        dismissWith('timer');\n        delete globalState.timeout;\n      }, innerParams.timer);\n      if (innerParams.timerProgressBar) {\n        show(timerProgressBar);\n        applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n        setTimeout(() => {\n          if (globalState.timeout && globalState.timeout.running) {\n            // timer can be already stopped or unset at this point\n            animateTimerProgressBar(innerParams.timer);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * Initialize focus in the popup:\n   *\n   * 1. If `toast` is `true`, don't steal focus from the document.\n   * 2. Else if there is an [autofocus] element, focus it.\n   * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n   * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n   * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n   * 6. Else focus the first focusable element in a popup (if any).\n   *\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   */\n  const initFocus = (domCache, innerParams) => {\n    if (innerParams.toast) {\n      return;\n    }\n    // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      warnAboutDeprecation('allowEnterKey');\n      blurActiveElement();\n      return;\n    }\n    if (focusAutofocus(domCache)) {\n      return;\n    }\n    if (focusButton(domCache, innerParams)) {\n      return;\n    }\n    setFocus(-1, 1);\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @returns {boolean}\n   */\n  const focusAutofocus = domCache => {\n    const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n    for (const autofocusElement of autofocusElements) {\n      if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n        autofocusElement.focus();\n        return true;\n      }\n    }\n    return false;\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const focusButton = (domCache, innerParams) => {\n    if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n      domCache.denyButton.focus();\n      return true;\n    }\n    if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n      domCache.cancelButton.focus();\n      return true;\n    }\n    if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n      domCache.confirmButton.focus();\n      return true;\n    }\n    return false;\n  };\n  const blurActiveElement = () => {\n    if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n      document.activeElement.blur();\n    }\n  };\n\n  // Dear russian users visiting russian sites. Let's have fun.\n  if (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n    const now = new Date();\n    const initiationDate = localStorage.getItem('swal-initiation');\n    if (!initiationDate) {\n      localStorage.setItem('swal-initiation', `${now}`);\n    } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n      setTimeout(() => {\n        document.body.style.pointerEvents = 'none';\n        const ukrainianAnthem = document.createElement('audio');\n        ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n        ukrainianAnthem.loop = true;\n        document.body.appendChild(ukrainianAnthem);\n        setTimeout(() => {\n          ukrainianAnthem.play().catch(() => {\n            // ignore\n          });\n        }, 2500);\n      }, 500);\n    }\n  }\n\n  // Assign instance methods from src/instanceMethods/*.js to prototype\n  SweetAlert.prototype.disableButtons = disableButtons;\n  SweetAlert.prototype.enableButtons = enableButtons;\n  SweetAlert.prototype.getInput = getInput;\n  SweetAlert.prototype.disableInput = disableInput;\n  SweetAlert.prototype.enableInput = enableInput;\n  SweetAlert.prototype.hideLoading = hideLoading;\n  SweetAlert.prototype.disableLoading = hideLoading;\n  SweetAlert.prototype.showValidationMessage = showValidationMessage;\n  SweetAlert.prototype.resetValidationMessage = resetValidationMessage;\n  SweetAlert.prototype.close = close;\n  SweetAlert.prototype.closePopup = close;\n  SweetAlert.prototype.closeModal = close;\n  SweetAlert.prototype.closeToast = close;\n  SweetAlert.prototype.rejectPromise = rejectPromise;\n  SweetAlert.prototype.update = update;\n  SweetAlert.prototype._destroy = _destroy;\n\n  // Assign static methods from src/staticMethods/*.js to constructor\n  Object.assign(SweetAlert, staticMethods);\n\n  // Proxy to instance methods to constructor, for now, for backwards compatibility\n  Object.keys(instanceMethods).forEach(key => {\n    /**\n     * @param {...any} args\n     * @returns {any | undefined}\n     */\n    SweetAlert[key] = function () {\n      if (currentInstance && currentInstance[key]) {\n        return currentInstance[key](...arguments);\n      }\n      return null;\n    };\n  });\n  SweetAlert.DismissReason = DismissReason;\n  SweetAlert.version = '11.14.5';\n\n  const Swal = SweetAlert;\n  // @ts-ignore\n  Swal.default = Swal;\n\n  return Swal;\n\n}));\nif (typeof this !== 'undefined' && this.Sweetalert2){this.swal = this.sweetAlert = this.Swal = this.SweetAlert = this.Sweetalert2}\n\n\n//# sourceURL=webpack://Materialize/./node_modules/sweetalert2/dist/sweetalert2.js?")}},__webpack_module_cache__={};function __webpack_require__(n){var e=__webpack_module_cache__[n];if(void 0!==e)return e.exports;var t=__webpack_module_cache__[n]={exports:{}};return __webpack_modules__[n].call(t.exports,t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return __webpack_require__.d(e,{a:e}),e},__webpack_require__.d=function(n,e){for(var t in e)__webpack_require__.o(e,t)&&!__webpack_require__.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},__webpack_require__.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},__webpack_require__.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/sweetalert2/sweetalert2.js");return __webpack_exports__}()}));