/* Dropzone */
/* common styles */
.dropzone {
  position: relative;
  padding-bottom: 1rem;
  border: 2px dashed var(--bs-border-color);
  border-radius: 0.5rem;
  cursor: pointer;
  inline-size: 100%;
  /* Disabled */
  /* Hover */
  /* Fallback */
  /* Default message */
  /* Preview */
  /* File information */
  /* Progressbar */
  /* Thumbnail */
  /* Remove link */
  /* error/success states */
  /* Error state */
  /* Success state */
}
.dropzone:not(.dz-clickable) {
  cursor: not-allowed;
  opacity: 0.5;
}
.dropzone.dz-drag-hover {
  border-style: solid;
  border-color: var(--bs-primary);
}
.dropzone.dz-drag-hover .dz-message {
  opacity: 0.5;
}
.dropzone .dz-message {
  color: var(--bs-heading-color);
  font-size: 1.5rem;
  font-weight: 500;
  margin-block: 3rem;
  margin-inline: 0;
  text-align: center;
}
.dropzone .dz-message .note {
  display: block;
  color: var(--bs-body-color);
  font-size: 0.9375rem;
  font-weight: 400;
  margin-block-start: 0.5rem;
}
.dropzone .dz-browser-not-supported.dropzone-box {
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  cursor: default !important;
  inline-size: auto !important;
  min-block-size: auto !important;
  transition: none;
}
.dropzone .dz-browser-not-supported .dz-message {
  display: none !important;
}
.dropzone.dz-started .dz-message {
  display: none;
}
.dropzone .dz-preview {
  position: relative;
  box-sizing: content-box;
  border: 0 solid var(--bs-border-color);
  border-radius: 0.375rem;
  background: var(--bs-paper-bg);
  box-shadow: var(--bs-box-shadow);
  cursor: default;
  font-size: 0.8125rem;
  margin-block: calc(1.5rem - 1rem);
  margin-inline: calc(1.5rem - 1rem);
}
@media (min-width: 576px) {
  .dropzone .dz-preview {
    display: inline-block;
    inline-size: 11.25rem;
  }
}
.dropzone .dz-filename {
  position: absolute;
  overflow: hidden;
  background: var(--bs-paper-bg);
  inline-size: 100%;
  padding-block: 0.625rem 0;
  padding-inline: 0.625rem;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dropzone .dz-filename:hover {
  text-overflow: inherit;
  white-space: normal;
}
.dropzone .dz-size {
  color: var(--bs-secondary-color);
  font-size: 0.6875rem;
  font-style: italic;
  padding-block: 1.875rem 0.625rem;
  padding-inline: 0.625rem;
}
.dropzone .dz-preview .progress,
.dropzone .dz-preview .progess-bar {
  block-size: 0.5rem;
}
.dropzone .dz-preview .progress {
  position: absolute;
  z-index: 30;
  inset-block-start: 50%;
  inset-inline: 1.3rem;
  margin-block-start: -0.25rem;
}
.dropzone .dz-complete .progress {
  display: none;
}
.dropzone .dz-thumbnail {
  position: relative;
  box-sizing: content-box;
  padding: 0.625rem;
  background: var(--bs-gray-25);
  block-size: 7.5rem;
  border-block-end: 1px solid var(--bs-border-color);
  text-align: center;
  border-top-left-radius: calc(0.375rem - 1px);
  border-top-right-radius: calc(0.375rem - 1px);
}
.dropzone .dz-thumbnail > img,
.dropzone .dz-thumbnail .dz-nopreview {
  position: relative;
  display: block;
  inset-block-start: 50%;
  margin-block: 0;
  margin-inline: auto;
  transform: translateY(-50%) scale(1);
}
.dropzone .dz-thumbnail > img {
  max-block-size: 100%;
  max-inline-size: 100%;
}
@media (min-width: 576px) {
  .dropzone .dz-thumbnail {
    inline-size: 10rem;
  }
}
.dropzone .dz-nopreview {
  color: var(--bs-secondary-color);
  font-size: 0.6875rem;
  font-weight: 500;
  text-transform: uppercase;
}
.dropzone .dz-thumbnail img[src] ~ .dz-nopreview {
  display: none;
}
.dropzone .dz-remove {
  display: block;
  border-block-start: 1px solid var(--bs-border-color);
  color: var(--bs-body-color);
  font-size: 0.75rem;
  padding-block: 0.375rem;
  padding-inline: 0;
  text-align: center;
  border-bottom-right-radius: calc(0.375rem - 1px);
  border-bottom-left-radius: calc(0.375rem - 1px);
}
.dropzone .dz-remove:hover, .dropzone .dz-remove:focus {
  background: var(--bs-gray-100);
  border-block-start-color: transparent;
  color: var(--bs-body-color);
  text-decoration: none;
}
.dropzone .dz-error-mark,
.dropzone .dz-success-mark {
  position: absolute;
  display: none;
  border-radius: 50%;
  background-color: rgba(var(--bs-dark-rgb), 0.5);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 1.875rem 1.875rem;
  block-size: 3.75rem;
  box-shadow: 0 0 1.25rem rgba(var(--bs-pure-black), 0.06);
  inline-size: 3.75rem;
  inset-block-start: 50%;
  inset-inline-start: 50%;
  margin-block-start: -1.875rem;
  margin-inline-start: -1.875rem;
}
.dropzone .dz-success-mark {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%235cb85c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E");
}
.dropzone .dz-error-mark {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23d9534f' viewBox='-2 -2 7 7'%3E%3Cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3E%3Ccircle r='.5'/%3E%3Ccircle cx='3' r='.5'/%3E%3Ccircle cy='3' r='.5'/%3E%3Ccircle cx='3' cy='3' r='.5'/%3E%3C/svg%3E");
}
.dropzone .dz-error-message {
  position: absolute;
  z-index: 40;
  display: none;
  overflow: auto;
  padding: 0.75rem;
  background: rgba(var(--bs-danger-rgb), 0.8);
  color: var(--bs-white);
  font-weight: 500;
  inset: -1px;
  text-align: start;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.dropzone .dz-error .dz-error-message {
  display: none;
}
.dropzone .dz-error .dz-error-mark {
  display: block;
}
.dropzone .dz-error:hover .dz-error-message {
  display: block;
}
.dropzone .dz-error:hover .dz-error-mark {
  display: none;
}
.dropzone .dz-success .dz-success-mark {
  display: block;
}
