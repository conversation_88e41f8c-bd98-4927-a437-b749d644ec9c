/* Menu
******************************************************************************* */

.menu {
  display: flex;
  background-color: var(--#{$prefix}menu-bg);

  .app-brand {
    inline-size: 100%;
    @include transition(padding .3s ease-in-out);
    padding-inline: calc(calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} * 2) - .125rem) calc(calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} * 2) - .5rem);
    .app-brand-text {
      color: var(--#{$prefix}heading-color);
    }
  }

  // Sub menu item link bullet
  .menu-sub > .menu-item > .menu-link{
    &::before {
      position: absolute;
      @include border-radius(50%);
      background-color: var(--#{$prefix}secondary-color);
      block-size: .5rem;
      content: "";
      inline-size: .5rem;
      inset-inline-start: 1.1875rem;
      .layout-horizontal & {
        inset-inline-start: 1rem;
      }
    }
  }

  // ? Hide bullet from first child only as we display icon instead (horizontal)
  &.menu-horizontal .menu-inner > .menu-item > .menu-sub > .menu-item > .menu-link {
    padding-inline-start: $menu-horizontal-link-padding-x;
    &::before {
      display: none;
    }
  }
  &.menu-horizontal .menu-sub .menu-item .menu-link {
    padding-inline-start: $menu-horizontal-link-padding-x + $menu-horizontal-menu-level-spacer;
  }

  .ps.ps--active-y > .ps__rail-y {
    background: none;
  }
  .ps__rail-y {
    inset-inline: auto .25rem !important;
  }

  .ps .ps__thumb-y {
    inline-size: .125rem;
  }
  .ps__thumb-y,
  .ps__rail-y.ps--clicking > .ps__thumb-y {
    opacity: .3;
  }
}

.menu-inner {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0;
  margin: 0;
  block-size: 100%;

  > .menu-item.menu-item-closing .menu-item.open .menu-sub,
  > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
    background-color: transparent;
    color: var(--#{$prefix}menu-color);
  }

  > .menu-header::before {
    background-color: rgba(var(--#{$prefix}menu-color-rgb), .5);
  }
  .menu-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    inline-size: 100%;
    line-height: normal;
    white-space: nowrap;
    @include media-breakpoint-down(xl) {
      inline-size: 90%;
    }
    &::before,
    &::after {
      display: block;
      background-color: var(--#{$prefix}border-color);
      block-size: 1px;
      content: "";
    }
    &::before {
      inline-size: 8%;
      margin-inline: calc($menu-vertical-link-padding-x * -1) $menu-icon-expanded-spacer;
      :dir(rtl) & {
        inline-size: 15%;
      }
    }
    &::after {
      inline-size: 90%;
      margin-inline-start: $menu-icon-expanded-spacer;
    }
  }
}

.menu-inner-shadow {
  position: absolute;
  z-index: 2;
  display: none;
  background: linear-gradient(var(--#{$prefix}menu-bg) 41%, rgba(var(--#{$prefix}menu-bg-rgb), .11) 95%, rgba(var(--#{$prefix}menu-bg-rgb), 0));
  inline-size: 100%;
  inset-block-start: $navbar-height - (($navbar-height - 3rem) * .5);
  pointer-events: none;
  @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
    block-size: 3rem;
  }
  @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
    block-size: 1.5rem;
  }
  .layout-navbar-full & {
    inset-block-start: 0;
  }
}

/* Menu item */

.menu-item {
  align-items: flex-start;
  justify-content: flex-start;

  &.menu-item-animating {
    transition: block-size $menu-animation-duration ease-in-out;
  }
  &.active > .menu-link:not(.menu-toggle) {
    background-color: var(--#{$prefix}menu-active-bg);
    color: var(--#{$prefix}menu-active-color);
  }
}

/* Horizontal Menu
****************************************************************************** */
.menu.menu-horizontal {
  .menu-inner > .menu-item > .menu-link {
    @include border-radius($border-radius-lg);
    margin-inline: .125rem;
  }
  .menu-inner > .menu-item:first-child {
    margin-inline-start: 0;
  }
  .menu-inner > .menu-item:last-child {
    margin-inline-end: 0;
  }

  .menu-inner > .menu-item.active > .menu-link.menu-toggle{
    background: var(--#{$prefix}menu-active-bg);
    color: var(--#{$prefix}menu-active-color);
  }

  .menu-inner > .menu-item{
    margin-block: calc($menu-horizontal-link-padding-y + .1375rem);
    margin-inline: 0;
    .menu-sub {
      z-index: 1;
      box-shadow: $menu-horizontal-menu-box-shadow;
    }
  }
}

.menu-item,
.menu-header,
.menu-divider,
.menu-block {
  flex: 0 0 auto;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
}
.menu-header {
  opacity: 1;
  transition: opacity $menu-animation-duration ease-in-out;
  .menu-header-text {
    color: var(--#{$prefix}secondary-color);
    letter-spacing: .4px;
    text-transform: uppercase;
    white-space: nowrap;
  }
}

/* Menu Icon */
.menu-icon {
  flex-grow: 0;
  flex-shrink: 0;
  block-size: $menu-icon-expanded-font-size;
  font-size: $menu-icon-expanded-font-size;
  inline-size: $menu-icon-expanded-font-size;
  margin-inline-end: $menu-icon-expanded-spacer;

  .menu:not(.menu-no-animation) & {
    transition: margin-inline-end $menu-animation-duration ease;
  }

  .menu-link {
    transition-duration: $menu-animation-duration;
  }
  .menu-toggle::after {
    transition-duration: $menu-animation-duration;
    transition-property: -webkit-transform, transform;
  }
}

/* Menu link */
.menu-link {
  position: relative;
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  margin: 0;

  .menu-item.disabled & {
    cursor: not-allowed;
  }

  > :not(.menu-icon) {
    flex: 0 1 auto;
    opacity: 1;
  }
}

.menu-link,
.menu-horizontal-prev,
.menu-horizontal-next {
  color: var(--#{$prefix}menu-color);

  &:hover,
  &:focus {
    color: var(--#{$prefix}menu-hover-color);
  }
}

.menu-item.disabled .menu-link,
.menu-horizontal-prev.disabled,
.menu-horizontal-next.disabled {
  opacity: .6;
}

/* Sub menu */
.menu-sub {
  display: none;
  flex-direction: column;
  margin: 0;
  padding-inline: 0;

  .menu-item.open > & {
    display: flex;
  }
}

/* Menu toggle open/close arrow */
.menu-toggle::after {
  position: absolute;
  display: block;
  background-color: var(--#{$prefix}menu-item-icon-color);
  content: "";
  inset-block-start: 50%;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m13.172 12l-4.95-4.95l1.414-1.413L16 12l-6.364 6.364l-1.414-1.415z'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-size: 100% 100%;
  transform: translateY(-50%);
  @include icon-base($menu-icon-expanded-font-size);
  :dir(rtl) & {
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m10.828 12l4.95 4.95l-1.414 1.415L8 12l6.364-6.364l1.414 1.414z'/%3E%3C/svg%3E");
  }
}


/* Menu divider */
.menu-divider {
  border: 0;
  border-block-start: 1px solid;
  border-block-start-color: var(--#{$prefix}menu-divider-color);
  inline-size: 100%;
}


/* Vertical Menu
****************************************************************************** */

.menu-vertical {
  overflow: hidden;
  flex-direction: column;

  // menu expand collapse animation
  &:not(.menu-no-animation) {
    transition: inline-size $menu-animation-duration;
  }

  &,
  .menu-block,
  .menu-inner > .menu-item,
  .menu-inner > .menu-header {
    inline-size: var(--#{$prefix}menu-width);
  }

  .menu-inner {
    flex: 1 1 auto;
    flex-direction: column;

    > .menu-item {
      margin-block: var(--#{$prefix}menu-item-spacer) 0;
      margin-inline: 0;

      /* menu-link spacing */
      .menu-link {
        @include border-radius($border-radius-lg);
        margin-block: 0;
        margin-inline: $menu-vertical-link-margin-x;
      }
    }
  }

  .menu-item .menu-link,
  .menu-header,
  .menu-block {
    padding-block: var(--#{$prefix}menu-vertical-link-padding-y);
    padding-inline: var(--#{$prefix}menu-vertical-link-padding-x);
  }
  .menu-header {
    margin-inline: $menu-vertical-link-margin-x;
    &:has(.menu-header-text) {
      @include media-breakpoint-up(xl) {
        padding-inline-end: 1.65rem;
      }
      @include media-breakpoint-down(xl) {
        padding-inline-end: 0;
      }
    }
  }
  .menu-item .menu-link {
    font-size: $menu-font-size;
    letter-spacing: .15px;
    min-block-size: 38px;
    > div:not(.badge) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .menu-item {
    .menu-toggle {
      padding-inline-end: calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} + #{$caret-width * 3.0545});
      &::after {
        inset-inline-end: calc(#{$menu-vertical-link-padding-x} - #{.2rem});
        transition: transform $menu-animation-duration;
      }
    }
    &:not(.active):not(.open) .menu-link:hover {
      background-color: var(--#{$prefix}menu-hover-bg);
    }
    &.active {
      > .menu-toggle {
        background-color: var(--#{$prefix}menu-sub-active-bg);
      }
      > .menu-link:not(.menu-toggle) {
        box-shadow: var(--#{$prefix}box-shadow-xs);
      }
    }
    &.active:not(.open) > .menu-link:not(.menu-toggle)::before {
      background-color: var(--#{$prefix}menu-active-color);
    }
  }

  .menu-item.open:not(.menu-item-closing) > .menu-link::after {
    transform: translateY(-50%) rotate(90deg);
    :dir(rtl) & {
      transform: translateY(-50%) rotate(-90deg);
    }
  }

  .menu-divider {
    padding: 0;
    margin-block: $menu-link-spacer-x;
  }

  .menu-sub {
    .menu-item {
      margin-block: $menu-item-spacer 0;
      margin-inline: 0;
    }
    .menu-icon {
      .layout-horizontal & {
        @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
          display: none;
        }
      }
    }
  }

  .layout-horizontal & {
    @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
      box-shadow: none;
    }
  }

  .menu-icon {
    @include icon-base($menu-icon-expanded-font-size);
  }

  .menu-horizontal-wrapper {
    flex: none;
  }

  ~ .menu-mobile-toggler {
    display: none;
    .layout-navbar-hidden & {
      position: fixed;
      z-index: 1067;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--#{$prefix}secondary);
      inset-block-end: calc(#{$container-padding-x} * 2);
      inset-inline-start: $container-padding-x;
    }
  }

  // Levels
  $menu-first-level-spacer: $menu-vertical-link-padding-x + $menu-icon-expanded-width + $menu-icon-expanded-spacer - .2;
  .menu-sub .menu-link {
    padding-inline-start: $menu-first-level-spacer;
  }

  // Menu levels loop for padding left/right
  @for $i from 2 through $menu-max-levels {
    $selector: "";
    @for $l from 1 through $i {
      $selector: "#{$selector} .menu-sub";
    }
    .layout-wrapper:not(.layout-horizontal) & {
      .menu-inner > .menu-item {
        #{$selector} {
          .menu-link {
            padding-inline-start: $menu-first-level-spacer + ($menu-vertical-menu-level-spacer * ($i)) - 1.45;
            &::before {
              inset-inline-start: $menu-icon-expanded-left-spacer + ($menu-vertical-menu-level-spacer * $i) - 2.6;
            }
          }
        }
      }
    }
  }
}

/* Vertical Menu Collapsed
******************************************************************************* */

@mixin layout-menu-collapsed() {
  inline-size: var(--#{$prefix}menu-collapsed-width);
  .menu-inner > .menu-item {
    inline-size: var(--#{$prefix}menu-collapsed-width);
  }
  .menu-inner > .menu-header,
  .menu-block {
    position: relative;
    inline-size: var(--#{$prefix}menu-width);
    margin-inline-start: var(--#{$prefix}menu-collapsed-width);
    padding-inline: $menu-icon-expanded-spacer calc(calc(var(--#{$prefix}menu-vertical-link-padding-x) * 2) - $menu-icon-expanded-spacer);
    text-indent: -9999px;
    text-overflow: ellipsis;
    white-space: nowrap;
    .menu-header-text {
      overflow: hidden;
      opacity: 0;
    }
    &::before {
      position: absolute;
      display: block;
      content: "";
      inline-size: calc(var(--#{$prefix}menu-collapsed-width) * .25);
      inset-block: .5rem;
      inset-inline-start: calc(-1 * calc(var(--#{$prefix}menu-collapsed-width) * .635));
      text-align: center;
    }
  }

  .menu-inner > .menu-header {
    &::before {
      block-size: .125rem;
    }
  }

  .app-brand {
    padding-inline-start: $menu-vertical-link-padding-x + .25rem;
  }

  .menu-inner > .menu-item div:not(.menu-block) {
    overflow: hidden;
    opacity: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .menu-inner > .menu-item > .menu-sub,
  .menu-inner > .menu-item.open > .menu-sub {
    display: none;
  }
  .menu-inner > .menu-item > .menu-toggle::after {
    display: none;
  }

  &:not(.layout-menu-hover) {
    .menu-inner > .menu-item > .menu-link,
    .menu-inner > .menu-block,
    .menu-inner > .menu-header {
      padding-inline-end: calc(#{$menu-vertical-link-padding-x} + #{$caret-width * 1.2});
    }
  }

  .menu-inner > .menu-item > .menu-link .menu-icon {
    margin: 0;
    margin-inline-end: 0;
    text-align: center;
  }
}

/* Only for menu example */
.layout-menu-collapsed .layout-menu {
  &:not(:hover) {
    @include layout-menu-collapsed();
  }
  &:hover {
    box-shadow: var(--#{$prefix}menu-box-shadow);
  }
}

/* Horizontal
******************************************************************************* */

.menu-horizontal {
  flex-direction: row;
  inline-size: 100%;
  .menu-inner {
    overflow: hidden;
    flex: 0 1 100%;
    flex-direction: row;
    > .menu-item.active > .menu-link.menu-toggle {
      font-weight: $font-weight-medium;
    }
    .menu-item.active > .menu-link:not(.menu-toggle) {
      font-weight: $font-weight-medium;
    }
    .menu-item{
      &:not(.menu-item-closing) > .menu-sub,
      &.open > .menu-toggle {
        background-color: var(--#{$prefix}menu-bg);
      }
      &.active > .menu-link.menu-toggle{
        background-color: var(--#{$prefix}menu-sub-active-bg);
        box-shadow: var(--#{$prefix}box-shadow-xs);
        color: var(--#{$prefix}menu-sub-active-color);
        &::after{
          background-color: var(--#{$prefix}menu-active-color);
        }
      }
      &:hover > .menu-link{
        background-color: var(--#{$prefix}menu-hover-bg);
        color: var(--#{$prefix}menu-hover-color);
      }
    }
    .menu-sub .menu-item.active > .menu-link.menu-toggle::after {
      background-color: var(--#{$prefix}menu-color);
    }
  }

  .menu-item .menu-link {
    padding-block: $menu-horizontal-link-padding-y;
    padding-inline: $menu-horizontal-link-padding-x;
  }

  .menu-item.active > .menu-link:not(.menu-toggle) {
    background: var(--#{$prefix}menu-horizontal-active-bg);
    color: var(--#{$prefix}primary);
    &::before{
      background-color: var(--#{$prefix}primary);
    }
  }

  .menu-item .menu-toggle {
    padding-inline-end: calc(#{$menu-horizontal-link-padding-x} + #{$caret-width * 2.6});
    &::after {
      inset-inline-end: calc(#{$menu-horizontal-link-padding-x} - #{.2rem});
    }
  }

  .menu-inner > .menu-item > .menu-toggle {
    &::after {
      transform: translateY(-50%) rotate(90deg);
      :dir(rtl) & {
        transform: translateY(-50%) rotate(-90deg);
      }
    }
    &::before {
      position: absolute;
      z-index: 2;
      block-size: calc($menu-vertical-header-margin-y + .1375rem);
      content: "";
      inline-size: 100%;
      inset-block-start: 100%;
      inset-inline-start: 0;
      pointer-events: auto;
    }
  }
  .menu-inner > .menu-item > .menu-sub {
    margin-block-start: calc($menu-vertical-header-margin-y + .2rem);
    .menu-sub {
      margin-block: 0;
      margin-inline: $menu-horizontal-spacer-x;
    }
  }

  .menu-inner > .menu-item:not(.menu-item-closing).open .menu-item.open {
    position: relative;
  }

  .menu-sub {
    position: absolute;
    box-shadow: $box-shadow-lg;
    inline-size: $menu-sub-width;
    padding-block: calc($menu-horizontal-item-spacer + $menu-item-spacer);
    padding-inline: 0;
    .menu-item {
      &.open .menu-link > div::after {
        position: absolute;
        z-index: 2;
        block-size: 100%;
        content: "";
        inline-size: 1.0625rem;
        inset-inline-end: -1.0625rem;
        pointer-events: auto;
      }
      .menu-item {
        .menu-link {
          .icon-base {
            color: var(--#{$prefix}secondary-color);
          }
        }
      }
    }

    .menu-sub {
      position: absolute;
      inline-size: 100%;
      inset-block-start: 0;
      inset-inline-start: 100%;
    }

    .menu-link {
      padding-block: $menu-horizontal-menu-link-padding-y;
    }
  }

  .menu-inner > .menu-item {
    .menu-sub {
      @include border-radius($border-radius-lg);
    }
  }

  &:not(.menu-no-animation) .menu-inner .menu-item.open .menu-sub {
    animation: menuDropdownShow $menu-animation-duration ease-in-out;
  }

  @include media-breakpoint-down(lg) {
    & {
      display: none;
    }
  }
}

.menu-horizontal-wrapper {
  overflow: hidden;
  flex: 0 1 100%;
  inline-size: 0;
  .menu:not(.menu-no-animation) & .menu-inner {
    transition: margin $menu-animation-duration;
  }
}

.menu-horizontal-prev,
.menu-horizontal-next {
  position: relative;
  display: block;
  flex: 0 0 auto;
  inline-size: $menu-control-width;
  &::after {
    position: absolute;
    display: block;
    border: 1px solid;
    block-size: $menu-control-arrow-size;
    border-block-start: 0;
    content: "";
    inline-size: $menu-control-arrow-size;
    inset-block-start: 50%;
    inset-inline-start: 50%;
  }

  &.disabled {
    cursor: not-allowed;
  }
}

.menu-horizontal-prev::after {
  border-inline-end: 0;
  transform: translate(0, -50%) rotate(45deg);
  :dir(rtl) & {
    transform: translate(0, -50%) rotate(-45deg);
  }
}

.menu-horizontal-next::after {
  border-inline-start: 0;
  transform: translate(50%, -50%) rotate(315deg);
  :dir(rtl) & {
    transform: translate(-50%, -50%) rotate(-315deg);
  }
}

@include keyframes(menuDropdownShow) {
  0% {
    opacity: 0;
    transform: translateY(-.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
