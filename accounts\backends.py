"""
Backends d'authentification personnalisés pour Hospital SaaS
"""
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User
from django.db.models import Q


class EmailBackend(ModelBackend):
    """
    Backend d'authentification qui permet la connexion avec l'email
    au lieu du username
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authentifie un utilisateur avec son email et mot de passe
        
        Args:
            request: La requête HTTP
            username: L'email de l'utilisateur (on garde le nom 'username' pour compatibilité)
            password: Le mot de passe
            **kwargs: Arguments supplémentaires
            
        Returns:
            User: L'utilisateur authentifié ou None
        """
        if username is None:
            username = kwargs.get('email')
        
        if username is None or password is None:
            return None
        
        try:
            # Chercher l'utilisateur par email
            user = User.objects.get(
                Q(email__iexact=username) | Q(username__iexact=username)
            )
        except User.DoesNotExist:
            # Exécuter le hashage du mot de passe pour éviter les attaques de timing
            User().set_password(password)
            return None
        except User.MultipleObjectsReturned:
            # Si plusieurs utilisateurs ont le même email, prendre le premier actif
            user = User.objects.filter(
                Q(email__iexact=username) | Q(username__iexact=username),
                is_active=True
            ).first()
            if not user:
                return None
        
        # Vérifier le mot de passe
        if user.check_password(password) and self.user_can_authenticate(user):
            return user
        
        return None
    
    def get_user(self, user_id):
        """
        Récupère un utilisateur par son ID
        
        Args:
            user_id: L'ID de l'utilisateur
            
        Returns:
            User: L'utilisateur ou None
        """
        try:
            user = User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
        
        return user if self.user_can_authenticate(user) else None


class OrganisationBackend(EmailBackend):
    """
    Backend d'authentification qui vérifie aussi l'appartenance à une organisation
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authentifie un utilisateur et vérifie qu'il appartient à une organisation active
        """
        user = super().authenticate(request, username, password, **kwargs)
        
        if user is None:
            return None
        
        # Vérifier que l'utilisateur appartient à une organisation active
        from organisations.models import MembreOrganisation
        
        if not MembreOrganisation.objects.filter(
            utilisateur=user,
            actif=True,
            organisation__actif=True
        ).exists():
            return None
        
        return user
    
    def user_can_authenticate(self, user):
        """
        Vérifie si un utilisateur peut s'authentifier
        """
        if not super().user_can_authenticate(user):
            return False
        
        # Vérifier que l'utilisateur appartient à une organisation active
        from organisations.models import MembreOrganisation
        
        return MembreOrganisation.objects.filter(
            utilisateur=user,
            actif=True,
            organisation__actif=True
        ).exists()
