# Generated by Django 5.0.14 on 2025-06-27 15:39

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organisations", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="organisation",
            name="telephone",
            field=models.CharField(
                help_text="Numéro de téléphone principal au format international (ex: +225 01 23 45 67 89)",
                max_length=20,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Format de téléphone invalide. Utilisez le format international (+XXX XXXXXXXXX).",
                        regex="^\\+[1-9]\\d{1,14}$",
                    )
                ],
                verbose_name="Téléphone",
            ),
        ),
    ]
