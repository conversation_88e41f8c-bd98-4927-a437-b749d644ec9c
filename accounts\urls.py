from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    # Pages publiques
    path('', views.accueil, name='accueil'),
    path('onboarding/', views.onboarding, name='onboarding'),
    path('onboarding/confirmation/', views.onboarding_confirmation, name='onboarding_confirmation'),
    
    # Authentification
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    
    # Dashboard après connexion
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # Gestion du profil
    path('profil/', views.profil, name='profil'),
    path('profil/modifier/', views.modifier_profil, name='modifier_profil'),
    
    # Gestion des utilisateurs (pour admin organisation)
    path('utilisateurs/', views.liste_utilisateurs, name='liste_utilisateurs'),
    path('utilisateurs/inviter/', views.inviter_utilisateur, name='inviter_utilisateur'),
    path('utilisateurs/<int:pk>/modifier/', views.modifier_utilisateur, name='modifier_utilisateur'),
    path('utilisateurs/<int:pk>/desactiver/', views.desactiver_utilisateur, name='desactiver_utilisateur'),
]
