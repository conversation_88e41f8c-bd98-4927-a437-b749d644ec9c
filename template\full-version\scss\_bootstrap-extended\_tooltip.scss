/* Tooltips
******************************************************************************* */


/* Open modal tooltip z-index */
.modal-open .tooltip {
  z-index: $zindex-modal + 2;
}

.tooltip-inner {
  font-weight: $font-weight-medium;
}

[class^="tooltip-"],
[class^="tooltip-"] > .tooltip{
  &.bs-tooltip-auto {
    &[data-popper-placement="top"] .tooltip-arrow::before {
      border-block-start-color: var(--#{$prefix}tooltip-arrow-bg);
    }
    &[data-popper-placement="left"] .tooltip-arrow::before {
      border-inline-start-color: var(--#{$prefix}tooltip-arrow-bg);
    }
    &[data-popper-placement="bottom"] .tooltip-arrow::before {
      border-block-end-color: var(--#{$prefix}tooltip-arrow-bg);
    }

    &[data-popper-placement="right"] .tooltip-arrow::before {
      border-inline-end-color: var(--#{$prefix}tooltip-arrow-bg);
    }
  }
}

/* Dark theme
******************************************************************************* */
@if $enable-dark-mode {
  @include color-mode(dark) {
    .tooltip {
      --#{$prefix}tooltip-bg: #{$tooltip-bg-dark};
      --#{$prefix}tooltip-color: #{$tooltip-color-dark};
    }
  }
}

/* RTL
******************************************************************************* */

:dir(rtl) {

  .bs-tooltip-auto {
    &[data-popper-placement="right"] {
      .tooltip-arrow {
        &::before {
          border-width: ($tooltip-arrow-width * .5) $tooltip-arrow-height ($tooltip-arrow-width * .5) 0;
          inset-inline-start: -1px;
        }
      }
    }

    &[data-popper-placement="left"] {
      .tooltip-arrow {
        &::before {
          border-width: ($tooltip-arrow-width * .5) 0 ($tooltip-arrow-width * .5) $tooltip-arrow-height;
          inset-inline-end: -1px;
        }
      }
    }
  }
}

// scss-docs-start tooltip-modifiers

@each $state in map-keys($theme-colors) {
  .tooltip.tooltip-#{$state}, .tooltip-#{$state} > .tooltip {
    --#{$prefix}tooltip-bg: var(--#{$prefix}#{$state});
    --#{$prefix}tooltip-color: var(--#{$prefix}#{$state}-contrast);
  }
}

// scss-docs-end tooltip-modifiers
