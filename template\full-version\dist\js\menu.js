!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t=e();for(var i in t)("object"==typeof exports?exports:n)[i]=t[i]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./js/menu.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Menu: function() { return /* binding */ Menu; }\n/* harmony export */ });\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar TRANSITION_EVENTS = ['transitionend', 'webkitTransitionEnd', 'oTransitionEnd'];\nvar DELTA = 5;\nvar Menu = /*#__PURE__*/function () {\n  function Menu(el) {\n    var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var _PS = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    _classCallCheck(this, Menu);\n    this._el = el;\n    this._horizontal = config.orientation === 'horizontal';\n    this._animate = config.animate !== false;\n    this._accordion = config.accordion !== false;\n    this._showDropdownOnHover = Boolean(config.showDropdownOnHover);\n    this._closeChildren = Boolean(config.closeChildren);\n    this._rtl = document.documentElement.getAttribute('dir') === 'rtl' || document.body.getAttribute('dir') === 'rtl';\n    this._onOpen = config.onOpen || function () {};\n    this._onOpened = config.onOpened || function () {};\n    this._onClose = config.onClose || function () {};\n    this._onClosed = config.onClosed || function () {};\n    this._psScroll = null;\n    this._topParent = null;\n    this._menuBgClass = null;\n    el.classList.add('menu');\n    el.classList[this._animate ? 'remove' : 'add']('menu-no-animation');\n    if (!this._horizontal) {\n      el.classList.add('menu-vertical');\n      el.classList.remove('menu-horizontal');\n      var PerfectScrollbarLib = _PS || window.PerfectScrollbar;\n      if (PerfectScrollbarLib) {\n        this._scrollbar = new PerfectScrollbarLib(el.querySelector('.menu-inner'), {\n          suppressScrollX: true,\n          wheelPropagation: !Menu._hasClass('layout-menu-fixed layout-menu-fixed-offcanvas')\n        });\n        window.Helpers.menuPsScroll = this._scrollbar;\n      } else {\n        el.querySelector('.menu-inner').classList.add('overflow-auto');\n      }\n    } else {\n      el.classList.add('menu-horizontal');\n      el.classList.remove('menu-vertical');\n      this._inner = el.querySelector('.menu-inner');\n      var container = this._inner.parentNode;\n      this._prevBtn = el.querySelector('.menu-horizontal-prev');\n      if (!this._prevBtn) {\n        this._prevBtn = document.createElement('a');\n        this._prevBtn.href = '#';\n        this._prevBtn.className = 'menu-horizontal-prev';\n        container.appendChild(this._prevBtn);\n      }\n      this._wrapper = el.querySelector('.menu-horizontal-wrapper');\n      if (!this._wrapper) {\n        this._wrapper = document.createElement('div');\n        this._wrapper.className = 'menu-horizontal-wrapper';\n        this._wrapper.appendChild(this._inner);\n        container.appendChild(this._wrapper);\n      }\n      this._nextBtn = el.querySelector('.menu-horizontal-next');\n      if (!this._nextBtn) {\n        this._nextBtn = document.createElement('a');\n        this._nextBtn.href = '#';\n        this._nextBtn.className = 'menu-horizontal-next';\n        container.appendChild(this._nextBtn);\n      }\n      this._innerPosition = 0;\n      this.update();\n    }\n\n    // Switch to vertical menu on small screen for horizontal menu layout on page load\n    if (this._horizontal && window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT) {\n      this.switchMenu('vertical');\n    } else {\n      this._bindEvents();\n    }\n\n    // Link menu instance to element\n    el.menuInstance = this;\n    var semiDarkEl = localStorage.getItem(\"templateCustomizer-\".concat(templateName, \"--SemiDark\"));\n    if (semiDarkEl === 'true') {\n      document.querySelector('#layout-menu').setAttribute('data-bs-theme', 'dark');\n    }\n  }\n  return _createClass(Menu, [{\n    key: \"_bindEvents\",\n    value: function _bindEvents() {\n      var _this = this;\n      // Click Event\n      this._evntElClick = function (e) {\n        // Find top parent element\n        if (e.target.closest('ul') && e.target.closest('ul').classList.contains('menu-inner')) {\n          var menuItem = Menu._findParent(e.target, 'menu-item', false);\n\n          // eslint-disable-next-line prefer-destructuring\n          if (menuItem) _this._topParent = menuItem.childNodes[0];\n        }\n        var toggleLink = e.target.classList.contains('menu-toggle') ? e.target : Menu._findParent(e.target, 'menu-toggle', false);\n        if (toggleLink) {\n          e.preventDefault();\n          if (toggleLink.getAttribute('data-hover') !== 'true') {\n            _this.toggle(toggleLink);\n          }\n        }\n      };\n      if (!this._showDropdownOnHover && this._horizontal || !this._horizontal || window.Helpers.isMobileDevice) this._el.addEventListener('click', this._evntElClick);\n      this._evntWindowResize = function () {\n        _this.update();\n        if (_this._lastWidth !== window.innerWidth) {\n          _this._lastWidth = window.innerWidth;\n          _this.update();\n        }\n        var horizontalMenuTemplate = document.querySelector(\"[data-template^='horizontal-menu']\");\n        if (!_this._horizontal && !horizontalMenuTemplate) _this.manageScroll();\n      };\n      window.addEventListener('resize', this._evntWindowResize);\n      if (this._horizontal) {\n        this._evntPrevBtnClick = function (e) {\n          e.preventDefault();\n          if (_this._prevBtn.classList.contains('disabled')) return;\n          _this._slide('prev');\n        };\n        this._prevBtn.addEventListener('click', this._evntPrevBtnClick);\n        this._evntNextBtnClick = function (e) {\n          e.preventDefault();\n          if (_this._nextBtn.classList.contains('disabled')) return;\n          _this._slide('next');\n        };\n        this._nextBtn.addEventListener('click', this._evntNextBtnClick);\n        this._evntBodyClick = function (e) {\n          if (!_this._inner.contains(e.target) && _this._el.querySelectorAll('.menu-inner > .menu-item.open').length) _this.closeAll();\n        };\n        document.body.addEventListener('click', this._evntBodyClick);\n        if (this._showDropdownOnHover) {\n          /** ***********************************************\n           * Horizontal Menu Mouse Over Event\n           * ? e.target !== e.currentTarget condition to disable mouseover event on whole menu navbar\n           * ? !e.target.parentNode.classList.contains('open') to disable mouseover events on icon, text and dropdown arrow\n           */\n          this._evntElMouseOver = function (e) {\n            if (e.target !== e.currentTarget && !e.target.parentNode.classList.contains('open')) {\n              var toggleLink = e.target.classList.contains('menu-toggle') ? e.target : null;\n              if (toggleLink) {\n                e.preventDefault();\n                if (toggleLink.getAttribute('data-hover') !== 'true') {\n                  _this.toggle(toggleLink);\n                }\n              }\n            }\n            e.stopPropagation();\n          };\n          if (this._horizontal && window.screen.width > window.Helpers.LAYOUT_BREAKPOINT) {\n            this._el.addEventListener('mouseover', this._evntElMouseOver);\n          }\n\n          /** ***********************************************\n           * Horizontal Menu Mouse Out Event\n           * ? e.target !== e.currentTarget condition to disable mouseout event on whole menu navbar\n           * ? mouseOutEl.parentNode.classList.contains('open') to check if the mouseout element has open class or not\n           * ? !mouseOutEl.classList.contains('menu-toggle') to check if mouseout was from single menu item and not from the one which has submenu\n           * ? !mouseOverEl.parentNode.classList.contains('menu-link') to disable mouseout event for icon, text and dropdown arrow\n           */\n          this._evntElMouseOut = function (e) {\n            var mainEl = e.currentTarget;\n            var mouseOutEl = e.target;\n            var mouseOverEl = e.toElement || e.relatedTarget;\n\n            // Find absolute parent of any menu item from which mouseout event triggered\n            if (mouseOutEl.closest('ul') && mouseOutEl.closest('ul').classList.contains('menu-inner')) {\n              _this._topParent = mouseOutEl;\n            }\n            if (mouseOutEl !== mainEl && (mouseOutEl.parentNode.classList.contains('open') || !mouseOutEl.classList.contains('menu-toggle')) && mouseOverEl && mouseOverEl.parentNode && !mouseOverEl.parentNode.classList.contains('menu-link')) {\n              // When mouse goes totally out of menu items, check mouse over element to confirm it's not the child of menu, once confirmed close the menu\n              if (_this._topParent && !Menu.childOf(mouseOverEl, _this._topParent.parentNode)) {\n                var _toggleLink = _this._topParent.classList.contains('menu-toggle') ? _this._topParent : null;\n                if (_toggleLink) {\n                  e.preventDefault();\n                  if (_toggleLink.getAttribute('data-hover') !== 'true') {\n                    _this.toggle(_toggleLink);\n                    _this._topParent = null;\n                  }\n                }\n              }\n\n              // When mouse enter the sub menu, check if it's child of the initially mouse overed menu item(Actual Parent),\n              // if it's the parent do not close the sub menu else close the sub menu\n              if (Menu.childOf(mouseOverEl, mouseOutEl.parentNode)) {\n                return;\n              }\n              var toggleLink = mouseOutEl.classList.contains('menu-toggle') ? mouseOutEl : null;\n              if (toggleLink) {\n                e.preventDefault();\n                if (toggleLink.getAttribute('data-hover') !== 'true') {\n                  _this.toggle(toggleLink);\n                }\n              }\n            }\n            e.stopPropagation();\n          };\n          if (this._horizontal && window.screen.width > window.Helpers.LAYOUT_BREAKPOINT) {\n            this._el.addEventListener('mouseout', this._evntElMouseOut);\n          }\n        }\n      }\n    }\n  }, {\n    key: \"_unbindEvents\",\n    value: function _unbindEvents() {\n      if (this._evntElClick) {\n        this._el.removeEventListener('click', this._evntElClick);\n        this._evntElClick = null;\n      }\n      if (this._evntElMouseOver) {\n        this._el.removeEventListener('mouseover', this._evntElMouseOver);\n        this._evntElMouseOver = null;\n      }\n      if (this._evntElMouseOut) {\n        this._el.removeEventListener('mouseout', this._evntElMouseOut);\n        this._evntElMouseOut = null;\n      }\n      if (this._evntWindowResize) {\n        window.removeEventListener('resize', this._evntWindowResize);\n        this._evntWindowResize = null;\n      }\n      if (this._evntBodyClick) {\n        document.body.removeEventListener('click', this._evntBodyClick);\n        this._evntBodyClick = null;\n      }\n      if (this._evntInnerMousemove) {\n        this._inner.removeEventListener('mousemove', this._evntInnerMousemove);\n        this._evntInnerMousemove = null;\n      }\n      if (this._evntInnerMouseleave) {\n        this._inner.removeEventListener('mouseleave', this._evntInnerMouseleave);\n        this._evntInnerMouseleave = null;\n      }\n    }\n  }, {\n    key: \"open\",\n    value: function open(el) {\n      var _this2 = this;\n      var closeChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this._closeChildren;\n      var item = this._findUnopenedParent(Menu._getItem(el, true), closeChildren);\n      if (!item) return;\n      var toggleLink = Menu._getLink(item, true);\n      Menu._promisify(this._onOpen, this, item, toggleLink, Menu._findMenu(item)).then(function () {\n        if (!_this2._horizontal || !Menu._isRoot(item)) {\n          if (_this2._animate && !_this2._horizontal) {\n            window.requestAnimationFrame(function () {\n              return _this2._toggleAnimation(true, item, false);\n            });\n            if (_this2._accordion) _this2._closeOther(item, closeChildren);\n          } else if (_this2._animate) {\n            _this2._toggleDropdown(true, item, closeChildren);\n            // eslint-disable-next-line no-unused-expressions\n            _this2._onOpened && _this2._onOpened(_this2, item, toggleLink, Menu._findMenu(item));\n          } else {\n            item.classList.add('open');\n            // eslint-disable-next-line no-unused-expressions\n            _this2._onOpened && _this2._onOpened(_this2, item, toggleLink, Menu._findMenu(item));\n            if (_this2._accordion) _this2._closeOther(item, closeChildren);\n          }\n        } else {\n          _this2._toggleDropdown(true, item, closeChildren);\n          // eslint-disable-next-line no-unused-expressions\n          _this2._onOpened && _this2._onOpened(_this2, item, toggleLink, Menu._findMenu(item));\n        }\n      }).catch(function () {});\n    }\n  }, {\n    key: \"close\",\n    value: function close(el) {\n      var _this3 = this;\n      var closeChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this._closeChildren;\n      var _autoClose = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var item = Menu._getItem(el, true);\n      var toggleLink = Menu._getLink(el, true);\n      if (!item.classList.contains('open') || item.classList.contains('disabled')) return;\n      Menu._promisify(this._onClose, this, item, toggleLink, Menu._findMenu(item), _autoClose).then(function () {\n        if (!_this3._horizontal || !Menu._isRoot(item)) {\n          if (_this3._animate && !_this3._horizontal) {\n            window.requestAnimationFrame(function () {\n              return _this3._toggleAnimation(false, item, closeChildren);\n            });\n          } else {\n            item.classList.remove('open');\n            if (closeChildren) {\n              var opened = item.querySelectorAll('.menu-item.open');\n              for (var i = 0, l = opened.length; i < l; i++) opened[i].classList.remove('open');\n            }\n\n            // eslint-disable-next-line no-unused-expressions\n            _this3._onClosed && _this3._onClosed(_this3, item, toggleLink, Menu._findMenu(item));\n          }\n        } else {\n          _this3._toggleDropdown(false, item, closeChildren);\n          // eslint-disable-next-line no-unused-expressions\n          _this3._onClosed && _this3._onClosed(_this3, item, toggleLink, Menu._findMenu(item));\n        }\n      }).catch(function () {});\n    }\n  }, {\n    key: \"_closeOther\",\n    value: function _closeOther(item, closeChildren) {\n      var opened = Menu._findChild(item.parentNode, ['menu-item', 'open']);\n      for (var i = 0, l = opened.length; i < l; i++) {\n        if (opened[i] !== item) this.close(opened[i], closeChildren);\n      }\n    }\n  }, {\n    key: \"toggle\",\n    value: function toggle(el) {\n      var closeChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this._closeChildren;\n      var item = Menu._getItem(el, true);\n      if (item.classList.contains('open')) this.close(item, closeChildren);else this.open(item, closeChildren);\n    }\n  }, {\n    key: \"_toggleDropdown\",\n    value: function _toggleDropdown(show, item, closeChildren) {\n      var menu = Menu._findMenu(item);\n      var actualItem = item;\n      var subMenuItem = false;\n      if (show) {\n        if (Menu._findParent(item, 'menu-sub', false)) {\n          subMenuItem = true;\n          item = this._topParent ? this._topParent.parentNode : item;\n        }\n        var wrapperWidth = Math.round(this._wrapper.getBoundingClientRect().width);\n        var position = this._innerPosition;\n        var itemOffset = this._getItemOffset(item);\n        var itemWidth = Math.round(item.getBoundingClientRect().width);\n        if (itemOffset - DELTA <= -1 * position) {\n          this._innerPosition = -1 * itemOffset;\n        } else if (itemOffset + position + itemWidth + DELTA >= wrapperWidth) {\n          if (itemWidth > wrapperWidth) {\n            this._innerPosition = -1 * itemOffset;\n          } else {\n            this._innerPosition = -1 * (itemOffset + itemWidth - wrapperWidth);\n          }\n        }\n        actualItem.classList.add('open');\n        var menuWidth = Math.round(menu.getBoundingClientRect().width);\n        if (subMenuItem) {\n          if (itemOffset + this._innerPosition + menuWidth * 2 > wrapperWidth && menuWidth < wrapperWidth && menuWidth >= itemWidth) {\n            menu.style.left = [this._rtl ? '100%' : '-100%'];\n          }\n        } else if (itemOffset + this._innerPosition + menuWidth > wrapperWidth && menuWidth < wrapperWidth && menuWidth > itemWidth) {\n          menu.style[this._rtl ? 'marginRight' : 'marginLeft'] = \"-\".concat(menuWidth - itemWidth, \"px\");\n        }\n        this._closeOther(actualItem, closeChildren);\n        this._updateSlider();\n      } else {\n        var toggle = Menu._findChild(item, ['menu-toggle']);\n\n        // eslint-disable-next-line no-unused-expressions\n        toggle.length && toggle[0].removeAttribute('data-hover', 'true');\n        item.classList.remove('open');\n        menu.style[this._rtl ? 'marginRight' : 'marginLeft'] = null;\n        if (closeChildren) {\n          var opened = menu.querySelectorAll('.menu-item.open');\n          for (var i = 0, l = opened.length; i < l; i++) opened[i].classList.remove('open');\n        }\n      }\n    }\n  }, {\n    key: \"_slide\",\n    value: function _slide(direction) {\n      var wrapperWidth = Math.round(this._wrapper.getBoundingClientRect().width);\n      var innerWidth = this._innerWidth;\n      var newPosition;\n      if (direction === 'next') {\n        newPosition = this._getSlideNextPos();\n        if (innerWidth + newPosition < wrapperWidth) {\n          newPosition = wrapperWidth - innerWidth;\n        }\n      } else {\n        newPosition = this._getSlidePrevPos();\n        if (newPosition > 0) newPosition = 0;\n      }\n      this._innerPosition = newPosition;\n      this.update();\n    }\n  }, {\n    key: \"_getSlideNextPos\",\n    value: function _getSlideNextPos() {\n      var wrapperWidth = Math.round(this._wrapper.getBoundingClientRect().width);\n      var position = this._innerPosition;\n      var curItem = this._inner.childNodes[0];\n      var left = 0;\n      while (curItem) {\n        if (curItem.tagName) {\n          var curItemWidth = Math.round(curItem.getBoundingClientRect().width);\n          if (left + position - DELTA <= wrapperWidth && left + position + curItemWidth + DELTA >= wrapperWidth) {\n            if (curItemWidth > wrapperWidth && left === -1 * position) left += curItemWidth;\n            break;\n          }\n          left += curItemWidth;\n        }\n        curItem = curItem.nextSibling;\n      }\n      return -1 * left;\n    }\n  }, {\n    key: \"_getSlidePrevPos\",\n    value: function _getSlidePrevPos() {\n      var wrapperWidth = Math.round(this._wrapper.getBoundingClientRect().width);\n      var position = this._innerPosition;\n      var curItem = this._inner.childNodes[0];\n      var left = 0;\n      while (curItem) {\n        if (curItem.tagName) {\n          var curItemWidth = Math.round(curItem.getBoundingClientRect().width);\n          if (left - DELTA <= -1 * position && left + curItemWidth + DELTA >= -1 * position) {\n            if (curItemWidth <= wrapperWidth) left = left + curItemWidth - wrapperWidth;\n            break;\n          }\n          left += curItemWidth;\n        }\n        curItem = curItem.nextSibling;\n      }\n      return -1 * left;\n    }\n  }, {\n    key: \"_findUnopenedParent\",\n    value: function _findUnopenedParent(item, closeChildren) {\n      var tree = [];\n      var parentItem = null;\n      while (item) {\n        if (item.classList.contains('disabled')) {\n          parentItem = null;\n          tree = [];\n        } else {\n          if (!item.classList.contains('open')) parentItem = item;\n          tree.push(item);\n        }\n        item = Menu._findParent(item, 'menu-item', false);\n      }\n      if (!parentItem) return null;\n      if (tree.length === 1) return parentItem;\n      tree = tree.slice(0, tree.indexOf(parentItem));\n      for (var i = 0, l = tree.length; i < l; i++) {\n        tree[i].classList.add('open');\n        if (this._accordion) {\n          var openedItems = Menu._findChild(tree[i].parentNode, ['menu-item', 'open']);\n          for (var j = 0, k = openedItems.length; j < k; j++) {\n            if (openedItems[j] !== tree[i]) {\n              openedItems[j].classList.remove('open');\n              if (closeChildren) {\n                var openedChildren = openedItems[j].querySelectorAll('.menu-item.open');\n                for (var x = 0, z = openedChildren.length; x < z; x++) {\n                  openedChildren[x].classList.remove('open');\n                }\n              }\n            }\n          }\n        }\n      }\n      return parentItem;\n    }\n  }, {\n    key: \"_toggleAnimation\",\n    value: function _toggleAnimation(open, item, closeChildren) {\n      var _this4 = this;\n      var toggleLink = Menu._getLink(item, true);\n      var menu = Menu._findMenu(item);\n      Menu._unbindAnimationEndEvent(item);\n      var linkHeight = Math.round(toggleLink.getBoundingClientRect().height);\n      item.style.overflow = 'hidden';\n      var clearItemStyle = function clearItemStyle() {\n        item.classList.remove('menu-item-animating');\n        item.classList.remove('menu-item-closing');\n        item.style.overflow = null;\n        item.style.height = null;\n        if (!_this4._horizontal) _this4.update();\n      };\n      if (open) {\n        item.style.height = \"\".concat(linkHeight, \"px\");\n        item.classList.add('menu-item-animating');\n        item.classList.add('open');\n        Menu._bindAnimationEndEvent(item, function () {\n          clearItemStyle();\n          _this4._onOpened(_this4, item, toggleLink, menu);\n        });\n        setTimeout(function () {\n          item.style.height = \"\".concat(linkHeight + Math.round(menu.getBoundingClientRect().height), \"px\");\n        }, 50);\n      } else {\n        item.style.height = \"\".concat(linkHeight + Math.round(menu.getBoundingClientRect().height), \"px\");\n        item.classList.add('menu-item-animating');\n        item.classList.add('menu-item-closing');\n        Menu._bindAnimationEndEvent(item, function () {\n          item.classList.remove('open');\n          clearItemStyle();\n          if (closeChildren) {\n            var opened = item.querySelectorAll('.menu-item.open');\n            for (var i = 0, l = opened.length; i < l; i++) opened[i].classList.remove('open');\n          }\n          _this4._onClosed(_this4, item, toggleLink, menu);\n        });\n        setTimeout(function () {\n          item.style.height = \"\".concat(linkHeight, \"px\");\n        }, 50);\n      }\n    }\n  }, {\n    key: \"_getItemOffset\",\n    value: function _getItemOffset(item) {\n      var curItem = this._inner.childNodes[0];\n      var left = 0;\n      while (curItem !== item) {\n        if (curItem.tagName) {\n          left += Math.round(curItem.getBoundingClientRect().width);\n        }\n        curItem = curItem.nextSibling;\n      }\n      return left;\n    }\n  }, {\n    key: \"_updateSlider\",\n    value: function _updateSlider() {\n      var wrapperWidth = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      var innerWidth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      var position = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      var _wrapperWidth = wrapperWidth !== null ? wrapperWidth : Math.round(this._wrapper.getBoundingClientRect().width);\n      var _innerWidth = innerWidth !== null ? innerWidth : this._innerWidth;\n      var _position = position !== null ? position : this._innerPosition;\n      if (_innerWidth < _wrapperWidth || window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT) {\n        this._prevBtn.classList.add('d-none');\n        this._nextBtn.classList.add('d-none');\n      } else {\n        this._prevBtn.classList.remove('d-none');\n        this._nextBtn.classList.remove('d-none');\n      }\n      if (_innerWidth > _wrapperWidth && window.innerWidth > window.Helpers.LAYOUT_BREAKPOINT) {\n        if (_position === 0) this._prevBtn.classList.add('disabled');else this._prevBtn.classList.remove('disabled');\n        if (_innerWidth + _position <= _wrapperWidth) this._nextBtn.classList.add('disabled');else this._nextBtn.classList.remove('disabled');\n      }\n    }\n  }, {\n    key: \"_innerWidth\",\n    get: function get() {\n      var items = this._inner.childNodes;\n      var width = 0;\n      for (var i = 0, l = items.length; i < l; i++) {\n        if (items[i].tagName) {\n          width += Math.round(items[i].getBoundingClientRect().width);\n        }\n      }\n      return width;\n    }\n  }, {\n    key: \"_innerPosition\",\n    get: function get() {\n      return parseInt(this._inner.style[this._rtl ? 'marginRight' : 'marginLeft'] || '0px', 10);\n    },\n    set: function set(value) {\n      this._inner.style[this._rtl ? 'marginRight' : 'marginLeft'] = \"\".concat(value, \"px\");\n      return value;\n    }\n  }, {\n    key: \"closeAll\",\n    value: function closeAll() {\n      var closeChildren = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this._closeChildren;\n      var opened = this._el.querySelectorAll('.menu-inner > .menu-item.open');\n      for (var i = 0, l = opened.length; i < l; i++) this.close(opened[i], closeChildren);\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      if (!this._horizontal) {\n        if (this._scrollbar) {\n          this._scrollbar.update();\n        }\n      } else {\n        this.closeAll();\n        var wrapperWidth = Math.round(this._wrapper.getBoundingClientRect().width);\n        var innerWidth = this._innerWidth;\n        var position = this._innerPosition;\n        if (wrapperWidth - position > innerWidth) {\n          position = wrapperWidth - innerWidth;\n          if (position > 0) position = 0;\n          this._innerPosition = position;\n        }\n        this._updateSlider(wrapperWidth, innerWidth, position);\n      }\n    }\n  }, {\n    key: \"manageScroll\",\n    value: function manageScroll() {\n      var _window = window,\n        PerfectScrollbar = _window.PerfectScrollbar;\n      var menuInner = document.querySelector('.menu-inner');\n      if (window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT) {\n        if (this._scrollbar !== null) {\n          this._scrollbar.destroy();\n          this._scrollbar = null;\n        }\n        menuInner.classList.add('overflow-auto');\n      } else {\n        if (this._scrollbar === null) {\n          var menuScroll = new PerfectScrollbar(document.querySelector('.menu-inner'), {\n            suppressScrollX: true,\n            wheelPropagation: false\n          });\n          this._scrollbar = menuScroll;\n        }\n        menuInner.classList.remove('overflow-auto');\n      }\n    }\n  }, {\n    key: \"switchMenu\",\n    value: function switchMenu(menu) {\n      // Unbind Events\n      this._unbindEvents();\n\n      // const html = document.documentElement\n      var navbar = document.querySelector('nav.layout-navbar');\n      var navbarCollapse = document.querySelector('#navbar-collapse');\n      var asideMenuWrapper = document.querySelector('#layout-menu div');\n      var asideMenu = document.querySelector('#layout-menu');\n      var horzMenuClasses = ['layout-menu-horizontal', 'menu', 'menu-horizontal', 'container-fluid', 'flex-grow-0'];\n      var vertMenuClasses = ['layout-menu', 'menu', 'menu-vertical'];\n      var horzMenuWrapper = document.querySelector('.menu-horizontal-wrapper');\n      var menuInner = document.querySelector('.menu-inner');\n      var brand = document.querySelector('.app-brand');\n      var menuToggler = document.querySelector('.layout-menu-toggle');\n      var activeMenuItems = document.querySelectorAll('.menu-inner .active');\n      var _window2 = window,\n        PerfectScrollbar = _window2.PerfectScrollbar;\n      if (menu === 'vertical') {\n        var _asideMenu$classList, _asideMenu$classList2;\n        this._horizontal = false;\n        asideMenuWrapper.insertBefore(brand, horzMenuWrapper);\n        asideMenuWrapper.insertBefore(menuInner, horzMenuWrapper);\n        asideMenuWrapper.classList.add('flex-column', 'p-0');\n        (_asideMenu$classList = asideMenu.classList).remove.apply(_asideMenu$classList, _toConsumableArray(asideMenu.classList));\n        (_asideMenu$classList2 = asideMenu.classList).add.apply(_asideMenu$classList2, vertMenuClasses.concat([this._menuBgClass]));\n        brand.classList.remove('d-none', 'd-lg-flex');\n        menuToggler.classList.remove('d-none');\n        if (PerfectScrollbar !== undefined) {\n          this._psScroll = new PerfectScrollbar(document.querySelector('.menu-inner'), {\n            suppressScrollX: true,\n            wheelPropagation: !Menu._hasClass('layout-menu-fixed layout-menu-fixed-offcanvas')\n          });\n        }\n        menuInner.classList.add('overflow-auto');\n\n        // Add open class to active items\n        for (var i = 0; i < activeMenuItems.length - 1; ++i) {\n          activeMenuItems[i].classList.add('open');\n        }\n      } else {\n        var _asideMenu$classList3, _asideMenu$classList4;\n        this._horizontal = true;\n        navbar.children[0].insertBefore(brand, navbarCollapse);\n        brand.classList.add('d-none', 'd-lg-flex');\n        horzMenuWrapper.appendChild(menuInner);\n        asideMenuWrapper.classList.remove('flex-column', 'p-0');\n        (_asideMenu$classList3 = asideMenu.classList).remove.apply(_asideMenu$classList3, _toConsumableArray(asideMenu.classList));\n        (_asideMenu$classList4 = asideMenu.classList).add.apply(_asideMenu$classList4, horzMenuClasses.concat([this._menuBgClass]));\n        menuToggler.classList.add('d-none');\n        menuInner.classList.remove('overflow-auto');\n\n        // Remove open class from active items\n        for (var _i = 0; _i < activeMenuItems.length; ++_i) {\n          activeMenuItems[_i].classList.remove('open');\n        }\n      }\n      var semiDarkEl = localStorage.getItem(\"templateCustomizer-\".concat(templateName, \"--SemiDark\"));\n      if (semiDarkEl) {\n        asideMenu.setAttribute('data-bs-theme', 'dark');\n      }\n      this._bindEvents();\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (!this._el) return;\n      this._unbindEvents();\n      var items = this._el.querySelectorAll('.menu-item');\n      for (var i = 0, l = items.length; i < l; i++) {\n        Menu._unbindAnimationEndEvent(items[i]);\n        items[i].classList.remove('menu-item-animating');\n        items[i].classList.remove('open');\n        items[i].style.overflow = null;\n        items[i].style.height = null;\n      }\n      var menus = this._el.querySelectorAll('.menu-menu');\n      for (var i2 = 0, l2 = menus.length; i2 < l2; i2++) {\n        menus[i2].style.marginRight = null;\n        menus[i2].style.marginLeft = null;\n      }\n      this._el.classList.remove('menu-no-animation');\n      if (this._wrapper) {\n        this._prevBtn.parentNode.removeChild(this._prevBtn);\n        this._nextBtn.parentNode.removeChild(this._nextBtn);\n        this._wrapper.parentNode.insertBefore(this._inner, this._wrapper);\n        this._wrapper.parentNode.removeChild(this._wrapper);\n        this._inner.style.marginLeft = null;\n        this._inner.style.marginRight = null;\n      }\n      this._el.menuInstance = null;\n      delete this._el.menuInstance;\n      this._el = null;\n      this._horizontal = null;\n      this._animate = null;\n      this._accordion = null;\n      this._showDropdownOnHover = null;\n      this._closeChildren = null;\n      this._rtl = null;\n      this._onOpen = null;\n      this._onOpened = null;\n      this._onClose = null;\n      this._onClosed = null;\n      if (this._scrollbar) {\n        this._scrollbar.destroy();\n        this._scrollbar = null;\n      }\n      this._inner = null;\n      this._prevBtn = null;\n      this._wrapper = null;\n      this._nextBtn = null;\n    }\n  }], [{\n    key: \"childOf\",\n    value: function childOf(/* child node */c, /* parent node */p) {\n      // returns boolean\n      if (c.parentNode) {\n        while ((c = c.parentNode) && c !== p);\n        return !!c;\n      }\n      return false;\n    }\n  }, {\n    key: \"_isRoot\",\n    value: function _isRoot(item) {\n      return !Menu._findParent(item, 'menu-item', false);\n    }\n  }, {\n    key: \"_findParent\",\n    value: function _findParent(el, cls) {\n      var throwError = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n      if (el.tagName.toUpperCase() === 'BODY') return null;\n      el = el.parentNode;\n      while (el.tagName.toUpperCase() !== 'BODY' && !el.classList.contains(cls)) {\n        el = el.parentNode;\n      }\n      el = el.tagName.toUpperCase() !== 'BODY' ? el : null;\n      if (!el && throwError) throw new Error(\"Cannot find `.\".concat(cls, \"` parent element\"));\n      return el;\n    }\n  }, {\n    key: \"_findChild\",\n    value: function _findChild(el, cls) {\n      var items = el.childNodes;\n      var found = [];\n      for (var i = 0, l = items.length; i < l; i++) {\n        if (items[i].classList) {\n          var passed = 0;\n          for (var j = 0; j < cls.length; j++) {\n            if (items[i].classList.contains(cls[j])) passed += 1;\n          }\n          if (cls.length === passed) found.push(items[i]);\n        }\n      }\n      return found;\n    }\n  }, {\n    key: \"_findMenu\",\n    value: function _findMenu(item) {\n      var curEl = item.childNodes[0];\n      var menu = null;\n      while (curEl && !menu) {\n        if (curEl.classList && curEl.classList.contains('menu-sub')) menu = curEl;\n        curEl = curEl.nextSibling;\n      }\n      if (!menu) throw new Error('Cannot find `.menu-sub` element for the current `.menu-toggle`');\n      return menu;\n    }\n\n    // Has class\n  }, {\n    key: \"_hasClass\",\n    value: function _hasClass(cls) {\n      var el = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.Helpers.ROOT_EL;\n      var result = false;\n      cls.split(' ').forEach(function (c) {\n        if (el.classList.contains(c)) result = true;\n      });\n      return result;\n    }\n  }, {\n    key: \"_getItem\",\n    value: function _getItem(el, toggle) {\n      var item = null;\n      var selector = toggle ? 'menu-toggle' : 'menu-link';\n      if (el.classList.contains('menu-item')) {\n        if (Menu._findChild(el, [selector]).length) item = el;\n      } else if (el.classList.contains(selector)) {\n        item = el.parentNode.classList.contains('menu-item') ? el.parentNode : null;\n      }\n      if (!item) {\n        throw new Error(\"\".concat(toggle ? 'Toggable ' : '', \"`.menu-item` element not found.\"));\n      }\n      return item;\n    }\n  }, {\n    key: \"_getLink\",\n    value: function _getLink(el, toggle) {\n      var found = [];\n      var selector = toggle ? 'menu-toggle' : 'menu-link';\n      if (el.classList.contains(selector)) found = [el];else if (el.classList.contains('menu-item')) found = Menu._findChild(el, [selector]);\n      if (!found.length) throw new Error(\"`\".concat(selector, \"` element not found.\"));\n      return found[0];\n    }\n  }, {\n    key: \"_bindAnimationEndEvent\",\n    value: function _bindAnimationEndEvent(el, handler) {\n      var cb = function cb(e) {\n        if (e.target !== el) return;\n        Menu._unbindAnimationEndEvent(el);\n        handler(e);\n      };\n      var duration = window.getComputedStyle(el).transitionDuration;\n      duration = parseFloat(duration) * (duration.indexOf('ms') !== -1 ? 1 : 1000);\n      el._menuAnimationEndEventCb = cb;\n      TRANSITION_EVENTS.forEach(function (ev) {\n        return el.addEventListener(ev, el._menuAnimationEndEventCb, false);\n      });\n      el._menuAnimationEndEventTimeout = setTimeout(function () {\n        cb({\n          target: el\n        });\n      }, duration + 50);\n    }\n  }, {\n    key: \"_promisify\",\n    value: function _promisify(fn) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      var result = fn.apply(void 0, args);\n      if (result instanceof Promise) {\n        return result;\n      }\n      if (result === false) {\n        return Promise.reject();\n      }\n      return Promise.resolve();\n    }\n  }, {\n    key: \"_unbindAnimationEndEvent\",\n    value: function _unbindAnimationEndEvent(el) {\n      var cb = el._menuAnimationEndEventCb;\n      if (el._menuAnimationEndEventTimeout) {\n        clearTimeout(el._menuAnimationEndEventTimeout);\n        el._menuAnimationEndEventTimeout = null;\n      }\n      if (!cb) return;\n      TRANSITION_EVENTS.forEach(function (ev) {\n        return el.removeEventListener(ev, cb, false);\n      });\n      el._menuAnimationEndEventCb = null;\n    }\n  }, {\n    key: \"setDisabled\",\n    value: function setDisabled(el, disabled) {\n      Menu._getItem(el, false).classList[disabled ? 'add' : 'remove']('disabled');\n    }\n  }, {\n    key: \"isActive\",\n    value: function isActive(el) {\n      return Menu._getItem(el, false).classList.contains('active');\n    }\n  }, {\n    key: \"isOpened\",\n    value: function isOpened(el) {\n      return Menu._getItem(el, false).classList.contains('open');\n    }\n  }, {\n    key: \"isDisabled\",\n    value: function isDisabled(el) {\n      return Menu._getItem(el, false).classList.contains('disabled');\n    }\n  }]);\n}();\nwindow.Menu = Menu;\n\n\n//# sourceURL=webpack://Materialize/./js/menu.js?")}},__webpack_require__={d:function(n,e){for(var t in e)__webpack_require__.o(e,t)&&!__webpack_require__.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},o:function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},r:function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})}},__webpack_exports__={};return __webpack_modules__["./js/menu.js"](0,__webpack_exports__,__webpack_require__),__webpack_exports__}()}));