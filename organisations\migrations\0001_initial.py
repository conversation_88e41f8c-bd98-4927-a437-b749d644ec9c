# Generated by Django 5.0.14 on 2025-06-27 15:34

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="MembreOrganisation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("ADMIN", "Administrateur"),
                            ("MEDECIN", "Médecin"),
                            ("INFIRMIER", "Infirmier"),
                            ("RECEPTIONNISTE", "Réceptionniste"),
                            ("COMPTABLE", "Comptable"),
                        ],
                        default="RECEPTIONNISTE",
                        max_length=20,
                        verbose_name="Rôle dans l'organisation",
                    ),
                ),
                (
                    "date_ajout",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Date d'ajout"
                    ),
                ),
                (
                    "actif",
                    models.BooleanField(default=True, verbose_name="Membre actif"),
                ),
                (
                    "utilisateur",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Utilisateur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Membre d'organisation",
                "verbose_name_plural": "Membres d'organisation",
            },
        ),
        migrations.CreateModel(
            name="Organisation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "nom",
                    models.CharField(
                        help_text="Nom complet de l'établissement hospitalier",
                        max_length=200,
                        verbose_name="Nom de l'organisation",
                    ),
                ),
                (
                    "adresse",
                    models.TextField(
                        help_text="Adresse postale complète de l'organisation",
                        verbose_name="Adresse complète",
                    ),
                ),
                (
                    "telephone",
                    models.CharField(
                        help_text="Numéro de téléphone principal",
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Format de téléphone invalide. Utilisez le format français.",
                                regex="^(\\+33|0)[1-9](\\d{8})$",
                            )
                        ],
                        verbose_name="Téléphone",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        help_text="Adresse email principale de l'organisation",
                        max_length=254,
                        verbose_name="Email de contact",
                    ),
                ),
                (
                    "type_abonnement",
                    models.CharField(
                        choices=[("GRATUIT", "Gratuit"), ("PREMIUM", "Premium")],
                        default="GRATUIT",
                        help_text="Type d'abonnement de l'organisation",
                        max_length=10,
                        verbose_name="Type d'abonnement",
                    ),
                ),
                (
                    "date_creation",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        verbose_name="Date de création",
                    ),
                ),
                (
                    "date_modification",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Dernière modification"
                    ),
                ),
                (
                    "actif",
                    models.BooleanField(
                        default=True,
                        help_text="Indique si l'organisation est active",
                        verbose_name="Organisation active",
                    ),
                ),
                (
                    "utilisateurs",
                    models.ManyToManyField(
                        related_name="organisations",
                        through="organisations.MembreOrganisation",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Utilisateurs",
                    ),
                ),
            ],
            options={
                "verbose_name": "Organisation",
                "verbose_name_plural": "Organisations",
                "ordering": ["nom"],
            },
        ),
        migrations.AddField(
            model_name="membreorganisation",
            name="organisation",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="organisations.organisation",
                verbose_name="Organisation",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="membreorganisation",
            unique_together={("utilisateur", "organisation")},
        ),
    ]
