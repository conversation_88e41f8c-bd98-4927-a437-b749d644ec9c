!function(e,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t=n();for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(self,(function(){return function(){var __webpack_modules__={"./libs/bootstrap-select/bootstrap-select.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bootstrapSelect: function() { return /* reexport default from dynamic */ bootstrap_select_js_bootstrap_select__WEBPACK_IMPORTED_MODULE_0___default.a; },\n/* harmony export */   handleBootstrapSelectEvents: function() { return /* binding */ handleBootstrapSelectEvents; }\n/* harmony export */ });\n/* harmony import */ var bootstrap_select_js_bootstrap_select__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bootstrap-select/js/bootstrap-select */ \"./node_modules/bootstrap-select/js/bootstrap-select.js\");\n/* harmony import */ var bootstrap_select_js_bootstrap_select__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bootstrap_select_js_bootstrap_select__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n// Function to handle Bootstrap Select events for floating labels (firefox)\nfunction handleBootstrapSelectEvents() {\n  var selectElements = $('.form-floating:has(.selectpicker)');\n  selectElements.each(function () {\n    $(this).addClass('form-floating-bootstrap-select');\n    $(this).on('show.bs.select', function () {\n      $(this).find('label').addClass('form-floating-bootstrap-select-label');\n    });\n    $(this).on('hide.bs.select', function () {\n      $(this).find('label').removeClass('form-floating-bootstrap-select-label');\n    });\n  });\n}\n\n\n//# sourceURL=webpack://Materialize/./libs/bootstrap-select/bootstrap-select.js?")},"./node_modules/bootstrap-select/js/bootstrap-select.js":function(){eval("(function ($) {\n  'use strict';\n\n  var DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn'];\n\n  var uriAttrs = [\n    'background',\n    'cite',\n    'href',\n    'itemtype',\n    'longdesc',\n    'poster',\n    'src',\n    'xlink:href'\n  ];\n\n  var ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i;\n\n  var DefaultWhitelist = {\n    // Global attributes allowed on any supplied element below.\n    '*': ['class', 'dir', 'id', 'lang', 'role', 'tabindex', 'style', ARIA_ATTRIBUTE_PATTERN],\n    a: ['target', 'href', 'title', 'rel'],\n    area: [],\n    b: [],\n    br: [],\n    col: [],\n    code: [],\n    div: [],\n    em: [],\n    hr: [],\n    h1: [],\n    h2: [],\n    h3: [],\n    h4: [],\n    h5: [],\n    h6: [],\n    i: [],\n    img: ['src', 'alt', 'title', 'width', 'height'],\n    li: [],\n    ol: [],\n    p: [],\n    pre: [],\n    s: [],\n    small: [],\n    span: [],\n    sub: [],\n    sup: [],\n    strong: [],\n    u: [],\n    ul: []\n  };\n\n  /**\n   * A pattern that recognizes a commonly useful subset of URLs that are safe.\n   *\n   * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n   */\n  var SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n\n  /**\n   * A pattern that matches safe data URLs. Only matches image, video and audio types.\n   *\n   * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n   */\n  var DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;\n\n  var ParseableAttributes = ['title', 'placeholder']; // attributes to use as settings, can add others in the future\n\n  function allowedAttribute (attr, allowedAttributeList) {\n    var attrName = attr.nodeName.toLowerCase();\n\n    if ($.inArray(attrName, allowedAttributeList) !== -1) {\n      if ($.inArray(attrName, uriAttrs) !== -1) {\n        return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN));\n      }\n\n      return true;\n    }\n\n    var regExp = $(allowedAttributeList).filter(function (index, value) {\n      return value instanceof RegExp;\n    });\n\n    // Check if a regular expression validates the attribute.\n    for (var i = 0, l = regExp.length; i < l; i++) {\n      if (attrName.match(regExp[i])) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  function sanitizeHtml (unsafeElements, whiteList, sanitizeFn) {\n    if (sanitizeFn && typeof sanitizeFn === 'function') {\n      return sanitizeFn(unsafeElements);\n    }\n\n    var whitelistKeys = Object.keys(whiteList);\n\n    for (var i = 0, len = unsafeElements.length; i < len; i++) {\n      var elements = unsafeElements[i].querySelectorAll('*');\n\n      for (var j = 0, len2 = elements.length; j < len2; j++) {\n        var el = elements[j];\n        var elName = el.nodeName.toLowerCase();\n\n        if (whitelistKeys.indexOf(elName) === -1) {\n          el.parentNode.removeChild(el);\n\n          continue;\n        }\n\n        var attributeList = [].slice.call(el.attributes);\n        var whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || []);\n\n        for (var k = 0, len3 = attributeList.length; k < len3; k++) {\n          var attr = attributeList[k];\n\n          if (!allowedAttribute(attr, whitelistedAttributes)) {\n            el.removeAttribute(attr.nodeName);\n          }\n        }\n      }\n    }\n  }\n\n  function getAttributesObject ($select) {\n    var attributesObject = {},\n        attrVal;\n\n    ParseableAttributes.forEach(function (item) {\n      attrVal = $select.attr(item);\n      if (attrVal) attributesObject[item] = attrVal;\n    });\n\n    // for backwards compatibility\n    // (using title as placeholder is deprecated - remove in v2.0.0)\n    if (!attributesObject.placeholder && attributesObject.title) {\n      attributesObject.placeholder = attributesObject.title;\n    }\n\n    return attributesObject;\n  }\n\n  // Polyfill for browsers with no classList support\n  // Remove in v2\n  if (!('classList' in document.createElement('_'))) {\n    (function (view) {\n      if (!('Element' in view)) return;\n\n      var classListProp = 'classList',\n          protoProp = 'prototype',\n          elemCtrProto = view.Element[protoProp],\n          objCtr = Object,\n          classListGetter = function () {\n            var $elem = $(this);\n\n            return {\n              add: function (classes) {\n                classes = Array.prototype.slice.call(arguments).join(' ');\n                return $elem.addClass(classes);\n              },\n              remove: function (classes) {\n                classes = Array.prototype.slice.call(arguments).join(' ');\n                return $elem.removeClass(classes);\n              },\n              toggle: function (classes, force) {\n                return $elem.toggleClass(classes, force);\n              },\n              contains: function (classes) {\n                return $elem.hasClass(classes);\n              }\n            };\n          };\n\n      if (objCtr.defineProperty) {\n        var classListPropDesc = {\n          get: classListGetter,\n          enumerable: true,\n          configurable: true\n        };\n        try {\n          objCtr.defineProperty(elemCtrProto, classListProp, classListPropDesc);\n        } catch (ex) { // IE 8 doesn't support enumerable:true\n          // adding undefined to fight this issue https://github.com/eligrey/classList.js/issues/36\n          // modernie IE8-MSW7 machine has IE8 8.0.6001.18702 and is affected\n          if (ex.number === undefined || ex.number === -0x7FF5EC54) {\n            classListPropDesc.enumerable = false;\n            objCtr.defineProperty(elemCtrProto, classListProp, classListPropDesc);\n          }\n        }\n      } else if (objCtr[protoProp].__defineGetter__) {\n        elemCtrProto.__defineGetter__(classListProp, classListGetter);\n      }\n    }(window));\n  }\n\n  var testElement = document.createElement('_');\n\n  testElement.classList.add('c1', 'c2');\n\n  if (!testElement.classList.contains('c2')) {\n    var _add = DOMTokenList.prototype.add,\n        _remove = DOMTokenList.prototype.remove;\n\n    DOMTokenList.prototype.add = function () {\n      Array.prototype.forEach.call(arguments, _add.bind(this));\n    };\n\n    DOMTokenList.prototype.remove = function () {\n      Array.prototype.forEach.call(arguments, _remove.bind(this));\n    };\n  }\n\n  testElement.classList.toggle('c3', false);\n\n  // Polyfill for IE 10 and Firefox <24, where classList.toggle does not\n  // support the second argument.\n  if (testElement.classList.contains('c3')) {\n    var _toggle = DOMTokenList.prototype.toggle;\n\n    DOMTokenList.prototype.toggle = function (token, force) {\n      if (1 in arguments && !this.contains(token) === !force) {\n        return force;\n      } else {\n        return _toggle.call(this, token);\n      }\n    };\n  }\n\n  testElement = null;\n\n  // Polyfill for IE (remove in v2)\n  Object.values = typeof Object.values === 'function' ? Object.values : function (obj) {\n    return Object.keys(obj).map(function (key) {\n      return obj[key];\n    });\n  };\n\n  // shallow array comparison\n  function isEqual (array1, array2) {\n    return array1.length === array2.length && array1.every(function (element, index) {\n      return element === array2[index];\n    });\n  };\n\n  // <editor-fold desc=\"Shims\">\n  if (!String.prototype.startsWith) {\n    (function () {\n      'use strict'; // needed to support `apply`/`call` with `undefined`/`null`\n      var toString = {}.toString;\n      var startsWith = function (search) {\n        if (this == null) {\n          throw new TypeError();\n        }\n        var string = String(this);\n        if (search && toString.call(search) == '[object RegExp]') {\n          throw new TypeError();\n        }\n        var stringLength = string.length;\n        var searchString = String(search);\n        var searchLength = searchString.length;\n        var position = arguments.length > 1 ? arguments[1] : undefined;\n        // `ToInteger`\n        var pos = position ? Number(position) : 0;\n        if (pos != pos) { // better `isNaN`\n          pos = 0;\n        }\n        var start = Math.min(Math.max(pos, 0), stringLength);\n        // Avoid the `indexOf` call if no match is possible\n        if (searchLength + start > stringLength) {\n          return false;\n        }\n        var index = -1;\n        while (++index < searchLength) {\n          if (string.charCodeAt(start + index) != searchString.charCodeAt(index)) {\n            return false;\n          }\n        }\n        return true;\n      };\n      if (Object.defineProperty) {\n        Object.defineProperty(String.prototype, 'startsWith', {\n          'value': startsWith,\n          'configurable': true,\n          'writable': true\n        });\n      } else {\n        String.prototype.startsWith = startsWith;\n      }\n    }());\n  }\n\n  function toKebabCase (str) {\n    return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, function ($, ofs) {\n      return (ofs ? '-' : '') + $.toLowerCase();\n    });\n  }\n\n  function getSelectedOptions () {\n    var options = this.selectpicker.main.data;\n\n    if (this.options.source.data || this.options.source.search) {\n      options = Object.values(this.selectpicker.optionValuesDataMap);\n    }\n\n    var selectedOptions = options.filter(function (item) {\n      if (item.selected) {\n        if (this.options.hideDisabled && item.disabled) return false;\n        return true;\n      }\n\n      return false;\n    }, this);\n\n    // ensure only 1 option is selected if multiple are set in the data source\n    if (this.options.source.data && !this.multiple && selectedOptions.length > 1) {\n      for (var i = 0; i < selectedOptions.length - 1; i++) {\n        selectedOptions[i].selected = false;\n      }\n\n      selectedOptions = [ selectedOptions[selectedOptions.length - 1] ];\n    }\n\n    return selectedOptions;\n  }\n\n  // much faster than $.val()\n  function getSelectValues (selectedOptions) {\n    var value = [],\n        options = selectedOptions || getSelectedOptions.call(this),\n        opt;\n\n    for (var i = 0, len = options.length; i < len; i++) {\n      opt = options[i];\n\n      if (!opt.disabled) {\n        value.push(opt.value === undefined ? opt.text : opt.value);\n      }\n    }\n\n    if (!this.multiple) {\n      return !value.length ? null : value[0];\n    }\n\n    return value;\n  }\n\n  // set data-selected on select element if the value has been programmatically selected\n  // prior to initialization of bootstrap-select\n  // * consider removing or replacing an alternative method *\n  var valHooks = {\n    useDefault: false,\n    _set: $.valHooks.select.set\n  };\n\n  $.valHooks.select.set = function (elem, value) {\n    if (value && !valHooks.useDefault) $(elem).data('selected', true);\n\n    return valHooks._set.apply(this, arguments);\n  };\n\n  var changedArguments = null;\n\n  var EventIsSupported = (function () {\n    try {\n      new Event('change');\n      return true;\n    } catch (e) {\n      return false;\n    }\n  })();\n\n  $.fn.triggerNative = function (eventName) {\n    var el = this[0],\n        event;\n\n    if (el.dispatchEvent) { // for modern browsers & IE9+\n      if (EventIsSupported) {\n        // For modern browsers\n        event = new Event(eventName, {\n          bubbles: true\n        });\n      } else {\n        // For IE since it doesn't support Event constructor\n        event = document.createEvent('Event');\n        event.initEvent(eventName, true, false);\n      }\n\n      el.dispatchEvent(event);\n    }\n  };\n  // </editor-fold>\n\n  function stringSearch (li, searchString, method, normalize) {\n    var stringTypes = [\n          'display',\n          'subtext',\n          'tokens'\n        ],\n        searchSuccess = false;\n\n    for (var i = 0; i < stringTypes.length; i++) {\n      var stringType = stringTypes[i],\n          string = li[stringType];\n\n      if (string) {\n        string = string.toString();\n\n        // Strip HTML tags. This isn't perfect, but it's much faster than any other method\n        if (stringType === 'display') {\n          string = string.replace(/<[^>]+>/g, '');\n        }\n\n        if (normalize) string = normalizeToBase(string);\n        string = string.toUpperCase();\n\n        if (typeof method === 'function') {\n          searchSuccess = method(string, searchString);\n        } else if (method === 'contains') {\n          searchSuccess = string.indexOf(searchString) >= 0;\n        } else {\n          searchSuccess = string.startsWith(searchString);\n        }\n\n        if (searchSuccess) break;\n      }\n    }\n\n    return searchSuccess;\n  }\n\n  function toInteger (value) {\n    return parseInt(value, 10) || 0;\n  }\n\n  // Borrowed from Lodash (_.deburr)\n  /** Used to map Latin Unicode letters to basic Latin letters. */\n  var deburredLetters = {\n    // Latin-1 Supplement block.\n    '\\xc0': 'A',  '\\xc1': 'A', '\\xc2': 'A', '\\xc3': 'A', '\\xc4': 'A', '\\xc5': 'A',\n    '\\xe0': 'a',  '\\xe1': 'a', '\\xe2': 'a', '\\xe3': 'a', '\\xe4': 'a', '\\xe5': 'a',\n    '\\xc7': 'C',  '\\xe7': 'c',\n    '\\xd0': 'D',  '\\xf0': 'd',\n    '\\xc8': 'E',  '\\xc9': 'E', '\\xca': 'E', '\\xcb': 'E',\n    '\\xe8': 'e',  '\\xe9': 'e', '\\xea': 'e', '\\xeb': 'e',\n    '\\xcc': 'I',  '\\xcd': 'I', '\\xce': 'I', '\\xcf': 'I',\n    '\\xec': 'i',  '\\xed': 'i', '\\xee': 'i', '\\xef': 'i',\n    '\\xd1': 'N',  '\\xf1': 'n',\n    '\\xd2': 'O',  '\\xd3': 'O', '\\xd4': 'O', '\\xd5': 'O', '\\xd6': 'O', '\\xd8': 'O',\n    '\\xf2': 'o',  '\\xf3': 'o', '\\xf4': 'o', '\\xf5': 'o', '\\xf6': 'o', '\\xf8': 'o',\n    '\\xd9': 'U',  '\\xda': 'U', '\\xdb': 'U', '\\xdc': 'U',\n    '\\xf9': 'u',  '\\xfa': 'u', '\\xfb': 'u', '\\xfc': 'u',\n    '\\xdd': 'Y',  '\\xfd': 'y', '\\xff': 'y',\n    '\\xc6': 'Ae', '\\xe6': 'ae',\n    '\\xde': 'Th', '\\xfe': 'th',\n    '\\xdf': 'ss',\n    // Latin Extended-A block.\n    '\\u0100': 'A',  '\\u0102': 'A', '\\u0104': 'A',\n    '\\u0101': 'a',  '\\u0103': 'a', '\\u0105': 'a',\n    '\\u0106': 'C',  '\\u0108': 'C', '\\u010a': 'C', '\\u010c': 'C',\n    '\\u0107': 'c',  '\\u0109': 'c', '\\u010b': 'c', '\\u010d': 'c',\n    '\\u010e': 'D',  '\\u0110': 'D', '\\u010f': 'd', '\\u0111': 'd',\n    '\\u0112': 'E',  '\\u0114': 'E', '\\u0116': 'E', '\\u0118': 'E', '\\u011a': 'E',\n    '\\u0113': 'e',  '\\u0115': 'e', '\\u0117': 'e', '\\u0119': 'e', '\\u011b': 'e',\n    '\\u011c': 'G',  '\\u011e': 'G', '\\u0120': 'G', '\\u0122': 'G',\n    '\\u011d': 'g',  '\\u011f': 'g', '\\u0121': 'g', '\\u0123': 'g',\n    '\\u0124': 'H',  '\\u0126': 'H', '\\u0125': 'h', '\\u0127': 'h',\n    '\\u0128': 'I',  '\\u012a': 'I', '\\u012c': 'I', '\\u012e': 'I', '\\u0130': 'I',\n    '\\u0129': 'i',  '\\u012b': 'i', '\\u012d': 'i', '\\u012f': 'i', '\\u0131': 'i',\n    '\\u0134': 'J',  '\\u0135': 'j',\n    '\\u0136': 'K',  '\\u0137': 'k', '\\u0138': 'k',\n    '\\u0139': 'L',  '\\u013b': 'L', '\\u013d': 'L', '\\u013f': 'L', '\\u0141': 'L',\n    '\\u013a': 'l',  '\\u013c': 'l', '\\u013e': 'l', '\\u0140': 'l', '\\u0142': 'l',\n    '\\u0143': 'N',  '\\u0145': 'N', '\\u0147': 'N', '\\u014a': 'N',\n    '\\u0144': 'n',  '\\u0146': 'n', '\\u0148': 'n', '\\u014b': 'n',\n    '\\u014c': 'O',  '\\u014e': 'O', '\\u0150': 'O',\n    '\\u014d': 'o',  '\\u014f': 'o', '\\u0151': 'o',\n    '\\u0154': 'R',  '\\u0156': 'R', '\\u0158': 'R',\n    '\\u0155': 'r',  '\\u0157': 'r', '\\u0159': 'r',\n    '\\u015a': 'S',  '\\u015c': 'S', '\\u015e': 'S', '\\u0160': 'S',\n    '\\u015b': 's',  '\\u015d': 's', '\\u015f': 's', '\\u0161': 's',\n    '\\u0162': 'T',  '\\u0164': 'T', '\\u0166': 'T',\n    '\\u0163': 't',  '\\u0165': 't', '\\u0167': 't',\n    '\\u0168': 'U',  '\\u016a': 'U', '\\u016c': 'U', '\\u016e': 'U', '\\u0170': 'U', '\\u0172': 'U',\n    '\\u0169': 'u',  '\\u016b': 'u', '\\u016d': 'u', '\\u016f': 'u', '\\u0171': 'u', '\\u0173': 'u',\n    '\\u0174': 'W',  '\\u0175': 'w',\n    '\\u0176': 'Y',  '\\u0177': 'y', '\\u0178': 'Y',\n    '\\u0179': 'Z',  '\\u017b': 'Z', '\\u017d': 'Z',\n    '\\u017a': 'z',  '\\u017c': 'z', '\\u017e': 'z',\n    '\\u0132': 'IJ', '\\u0133': 'ij',\n    '\\u0152': 'Oe', '\\u0153': 'oe',\n    '\\u0149': \"'n\", '\\u017f': 's'\n  };\n\n  /** Used to match Latin Unicode letters (excluding mathematical operators). */\n  var reLatin = /[\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\xff\\u0100-\\u017f]/g;\n\n  /** Used to compose unicode character classes. */\n  var rsComboMarksRange = '\\\\u0300-\\\\u036f',\n      reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n      rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n      rsComboMarksExtendedRange = '\\\\u1ab0-\\\\u1aff',\n      rsComboMarksSupplementRange = '\\\\u1dc0-\\\\u1dff',\n      rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange + rsComboMarksExtendedRange + rsComboMarksSupplementRange;\n\n  /** Used to compose unicode capture groups. */\n  var rsCombo = '[' + rsComboRange + ']';\n\n  /**\n   * Used to match [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks) and\n   * [combining diacritical marks for symbols](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks_for_Symbols).\n   */\n  var reComboMark = RegExp(rsCombo, 'g');\n\n  function deburrLetter (key) {\n    return deburredLetters[key];\n  };\n\n  function normalizeToBase (string) {\n    string = string.toString();\n    return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');\n  }\n\n  // List of HTML entities for escaping.\n  var escapeMap = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#x27;',\n    '`': '&#x60;'\n  };\n\n  // Functions for escaping and unescaping strings to/from HTML interpolation.\n  var createEscaper = function (map) {\n    var escaper = function (match) {\n      return map[match];\n    };\n    // Regexes for identifying a key that needs to be escaped.\n    var source = '(?:' + Object.keys(map).join('|') + ')';\n    var testRegexp = RegExp(source);\n    var replaceRegexp = RegExp(source, 'g');\n    return function (string) {\n      string = string == null ? '' : '' + string;\n      return testRegexp.test(string) ? string.replace(replaceRegexp, escaper) : string;\n    };\n  };\n\n  var htmlEscape = createEscaper(escapeMap);\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  var keyCodeMap = {\n    32: ' ',\n    48: '0',\n    49: '1',\n    50: '2',\n    51: '3',\n    52: '4',\n    53: '5',\n    54: '6',\n    55: '7',\n    56: '8',\n    57: '9',\n    59: ';',\n    65: 'A',\n    66: 'B',\n    67: 'C',\n    68: 'D',\n    69: 'E',\n    70: 'F',\n    71: 'G',\n    72: 'H',\n    73: 'I',\n    74: 'J',\n    75: 'K',\n    76: 'L',\n    77: 'M',\n    78: 'N',\n    79: 'O',\n    80: 'P',\n    81: 'Q',\n    82: 'R',\n    83: 'S',\n    84: 'T',\n    85: 'U',\n    86: 'V',\n    87: 'W',\n    88: 'X',\n    89: 'Y',\n    90: 'Z',\n    96: '0',\n    97: '1',\n    98: '2',\n    99: '3',\n    100: '4',\n    101: '5',\n    102: '6',\n    103: '7',\n    104: '8',\n    105: '9'\n  };\n\n  var keyCodes = {\n    ESCAPE: 27, // KeyboardEvent.which value for Escape (Esc) key\n    ENTER: 13, // KeyboardEvent.which value for Enter key\n    SPACE: 32, // KeyboardEvent.which value for space key\n    TAB: 9, // KeyboardEvent.which value for tab key\n    ARROW_UP: 38, // KeyboardEvent.which value for up arrow key\n    ARROW_DOWN: 40 // KeyboardEvent.which value for down arrow key\n  };\n\n  // eslint-disable-next-line no-undef\n  var Dropdown = window.Dropdown || bootstrap.Dropdown;\n\n  function getVersion () {\n    var version;\n\n    try {\n      version = $.fn.dropdown.Constructor.VERSION;\n    } catch (err) {\n      version = Dropdown.VERSION;\n    }\n\n    return version;\n  }\n\n  var version = {\n    success: false,\n    major: '3'\n  };\n\n  try {\n    version.full = (getVersion() || '').split(' ')[0].split('.');\n    version.major = version.full[0];\n    version.success = true;\n  } catch (err) {\n    // do nothing\n  }\n\n  var selectId = 0;\n\n  var EVENT_KEY = '.bs.select';\n\n  var classNames = {\n    DISABLED: 'disabled',\n    DIVIDER: 'divider',\n    SHOW: 'open',\n    DROPUP: 'dropup',\n    MENU: 'dropdown-menu',\n    MENURIGHT: 'dropdown-menu-right',\n    MENULEFT: 'dropdown-menu-left',\n    // to-do: replace with more advanced template/customization options\n    BUTTONCLASS: 'btn-default',\n    POPOVERHEADER: 'popover-title',\n    ICONBASE: 'glyphicon',\n    TICKICON: 'glyphicon-ok'\n  };\n\n  var Selector = {\n    MENU: '.' + classNames.MENU,\n    DATA_TOGGLE: 'data-toggle=\"dropdown\"'\n  };\n\n  var elementTemplates = {\n    div: document.createElement('div'),\n    span: document.createElement('span'),\n    i: document.createElement('i'),\n    subtext: document.createElement('small'),\n    a: document.createElement('a'),\n    li: document.createElement('li'),\n    whitespace: document.createTextNode('\\u00A0'),\n    fragment: document.createDocumentFragment(),\n    option: document.createElement('option')\n  };\n\n  elementTemplates.selectedOption = elementTemplates.option.cloneNode(false);\n  elementTemplates.selectedOption.setAttribute('selected', true);\n\n  elementTemplates.noResults = elementTemplates.li.cloneNode(false);\n  elementTemplates.noResults.className = 'no-results';\n\n  elementTemplates.a.setAttribute('role', 'option');\n  elementTemplates.a.className = 'dropdown-item';\n\n  elementTemplates.subtext.className = 'text-muted';\n\n  elementTemplates.text = elementTemplates.span.cloneNode(false);\n  elementTemplates.text.className = 'text';\n\n  elementTemplates.checkMark = elementTemplates.span.cloneNode(false);\n\n  var REGEXP_ARROW = new RegExp(keyCodes.ARROW_UP + '|' + keyCodes.ARROW_DOWN);\n  var REGEXP_TAB_OR_ESCAPE = new RegExp('^' + keyCodes.TAB + '$|' + keyCodes.ESCAPE);\n\n  var generateOption = {\n    li: function (content, classes, optgroup) {\n      var li = elementTemplates.li.cloneNode(false);\n\n      if (content) {\n        if (content.nodeType === 1 || content.nodeType === 11) {\n          li.appendChild(content);\n        } else {\n          li.innerHTML = content;\n        }\n      }\n\n      if (typeof classes !== 'undefined' && classes !== '') li.className = classes;\n      if (typeof optgroup !== 'undefined' && optgroup !== null) li.classList.add('optgroup-' + optgroup);\n\n      return li;\n    },\n\n    a: function (text, classes, inline) {\n      var a = elementTemplates.a.cloneNode(true);\n\n      if (text) {\n        if (text.nodeType === 11) {\n          a.appendChild(text);\n        } else {\n          a.insertAdjacentHTML('beforeend', text);\n        }\n      }\n\n      if (typeof classes !== 'undefined' && classes !== '') a.classList.add.apply(a.classList, classes.split(/\\s+/));\n      if (inline) a.setAttribute('style', inline);\n\n      return a;\n    },\n\n    text: function (options, useFragment) {\n      var textElement = elementTemplates.text.cloneNode(false),\n          subtextElement,\n          iconElement;\n\n      if (options.content) {\n        textElement.innerHTML = options.content;\n      } else {\n        textElement.textContent = options.text;\n\n        if (options.icon) {\n          var whitespace = elementTemplates.whitespace.cloneNode(false);\n\n          // need to use <i> for icons in the button to prevent a breaking change\n          // note: switch to span in next major release\n          iconElement = (useFragment === true ? elementTemplates.i : elementTemplates.span).cloneNode(false);\n          iconElement.className = this.options.iconBase + ' ' + options.icon;\n\n          elementTemplates.fragment.appendChild(iconElement);\n          elementTemplates.fragment.appendChild(whitespace);\n        }\n\n        if (options.subtext) {\n          subtextElement = elementTemplates.subtext.cloneNode(false);\n          subtextElement.textContent = options.subtext;\n          textElement.appendChild(subtextElement);\n        }\n      }\n\n      if (useFragment === true) {\n        while (textElement.childNodes.length > 0) {\n          elementTemplates.fragment.appendChild(textElement.childNodes[0]);\n        }\n      } else {\n        elementTemplates.fragment.appendChild(textElement);\n      }\n\n      return elementTemplates.fragment;\n    },\n\n    label: function (options) {\n      var textElement = elementTemplates.text.cloneNode(false),\n          subtextElement,\n          iconElement;\n\n      textElement.innerHTML = options.display;\n\n      if (options.icon) {\n        var whitespace = elementTemplates.whitespace.cloneNode(false);\n\n        iconElement = elementTemplates.span.cloneNode(false);\n        iconElement.className = this.options.iconBase + ' ' + options.icon;\n\n        elementTemplates.fragment.appendChild(iconElement);\n        elementTemplates.fragment.appendChild(whitespace);\n      }\n\n      if (options.subtext) {\n        subtextElement = elementTemplates.subtext.cloneNode(false);\n        subtextElement.textContent = options.subtext;\n        textElement.appendChild(subtextElement);\n      }\n\n      elementTemplates.fragment.appendChild(textElement);\n\n      return elementTemplates.fragment;\n    }\n  };\n\n  var getOptionData = {\n    fromOption: function (option, type) {\n      var value;\n\n      switch (type) {\n        case 'divider':\n          value = option.getAttribute('data-divider') === 'true';\n          break;\n\n        case 'text':\n          value = option.textContent;\n          break;\n\n        case 'label':\n          value = option.label;\n          break;\n\n        case 'style':\n          value = option.style.cssText;\n          break;\n\n        case 'title':\n          value = option.title;\n          break;\n\n        default:\n          value = option.getAttribute('data-' + toKebabCase(type));\n          break;\n      }\n\n      return value;\n    },\n    fromDataSource: function (option, type) {\n      var value;\n\n      switch (type) {\n        case 'text':\n        case 'label':\n          value = option.text || option.value || '';\n          break;\n\n        default:\n          value = option[type];\n          break;\n      }\n\n      return value;\n    }\n  };\n\n  function showNoResults (searchMatch, searchValue) {\n    if (!searchMatch.length) {\n      elementTemplates.noResults.innerHTML = this.options.noneResultsText.replace('{0}', '\"' + htmlEscape(searchValue) + '\"');\n      this.$menuInner[0].firstChild.appendChild(elementTemplates.noResults);\n    }\n  }\n\n  function filterHidden (item) {\n    return !(item.hidden || this.options.hideDisabled && item.disabled);\n  }\n\n  var Selectpicker = function (element, options) {\n    var that = this;\n\n    // bootstrap-select has been initialized - revert valHooks.select.set back to its original function\n    if (!valHooks.useDefault) {\n      $.valHooks.select.set = valHooks._set;\n      valHooks.useDefault = true;\n    }\n\n    this.$element = $(element);\n    this.$newElement = null;\n    this.$button = null;\n    this.$menu = null;\n    this.options = options;\n    this.selectpicker = {\n      main: {\n        data: [],\n        optionQueue: elementTemplates.fragment.cloneNode(false),\n        hasMore: false\n      },\n      search: {\n        data: [],\n        hasMore: false\n      },\n      current: {}, // current is either equal to main or search depending on if a search is in progress\n      view: {},\n      // map of option values and their respective data (only used in conjunction with options.source)\n      optionValuesDataMap: {},\n      isSearching: false,\n      keydown: {\n        keyHistory: '',\n        resetKeyHistory: {\n          start: function () {\n            return setTimeout(function () {\n              that.selectpicker.keydown.keyHistory = '';\n            }, 800);\n          }\n        }\n      }\n    };\n\n    this.sizeInfo = {};\n\n    // Format window padding\n    var winPad = this.options.windowPadding;\n    if (typeof winPad === 'number') {\n      this.options.windowPadding = [winPad, winPad, winPad, winPad];\n    }\n\n    // Expose public methods\n    this.val = Selectpicker.prototype.val;\n    this.render = Selectpicker.prototype.render;\n    this.refresh = Selectpicker.prototype.refresh;\n    this.setStyle = Selectpicker.prototype.setStyle;\n    this.selectAll = Selectpicker.prototype.selectAll;\n    this.deselectAll = Selectpicker.prototype.deselectAll;\n    this.destroy = Selectpicker.prototype.destroy;\n    this.remove = Selectpicker.prototype.remove;\n    this.show = Selectpicker.prototype.show;\n    this.hide = Selectpicker.prototype.hide;\n\n    this.init();\n  };\n\n  Selectpicker.VERSION = '1.14.0-beta3';\n\n  // part of this is duplicated in i18n/defaults-en_US.js. Make sure to update both.\n  Selectpicker.DEFAULTS = {\n    noneSelectedText: 'Nothing selected',\n    noneResultsText: 'No results matched {0}',\n    countSelectedText: function (numSelected, numTotal) {\n      return (numSelected == 1) ? '{0} item selected' : '{0} items selected';\n    },\n    maxOptionsText: function (numAll, numGroup) {\n      return [\n        (numAll == 1) ? 'Limit reached ({n} item max)' : 'Limit reached ({n} items max)',\n        (numGroup == 1) ? 'Group limit reached ({n} item max)' : 'Group limit reached ({n} items max)'\n      ];\n    },\n    selectAllText: 'Select All',\n    deselectAllText: 'Deselect All',\n    source: {\n      pageSize: 40\n    },\n    chunkSize: 40,\n    doneButton: false,\n    doneButtonText: 'Close',\n    multipleSeparator: ', ',\n    styleBase: 'btn',\n    style: classNames.BUTTONCLASS,\n    size: 'auto',\n    title: null,\n    placeholder: null,\n    allowClear: false,\n    selectedTextFormat: 'values',\n    width: false,\n    container: false,\n    hideDisabled: false,\n    showSubtext: false,\n    showIcon: true,\n    showContent: true,\n    dropupAuto: true,\n    header: false,\n    liveSearch: false,\n    liveSearchPlaceholder: null,\n    liveSearchNormalize: false,\n    liveSearchStyle: 'contains',\n    actionsBox: false,\n    iconBase: classNames.ICONBASE,\n    tickIcon: classNames.TICKICON,\n    showTick: false,\n    template: {\n      caret: '<span class=\"caret\"></span>'\n    },\n    maxOptions: false,\n    mobile: false,\n    selectOnTab: true,\n    dropdownAlignRight: false,\n    windowPadding: 0,\n    virtualScroll: 600,\n    display: false,\n    sanitize: true,\n    sanitizeFn: null,\n    whiteList: DefaultWhitelist\n  };\n\n  Selectpicker.prototype = {\n\n    constructor: Selectpicker,\n\n    init: function () {\n      var that = this,\n          id = this.$element.attr('id'),\n          element = this.$element[0],\n          form = element.form;\n\n      selectId++;\n      this.selectId = 'bs-select-' + selectId;\n\n      element.classList.add('bs-select-hidden');\n\n      this.multiple = this.$element.prop('multiple');\n      this.autofocus = this.$element.prop('autofocus');\n\n      if (element.classList.contains('show-tick')) {\n        this.options.showTick = true;\n      }\n\n      this.$newElement = this.createDropdown();\n\n      this.$element\n        .after(this.$newElement)\n        .prependTo(this.$newElement);\n\n      // ensure select is associated with form element if it got unlinked after moving it inside newElement\n      if (form && element.form === null) {\n        if (!form.id) form.id = 'form-' + this.selectId;\n        element.setAttribute('form', form.id);\n      }\n\n      this.$button = this.$newElement.children('button');\n      if (this.options.allowClear) this.$clearButton = this.$button.children('.bs-select-clear-selected');\n      this.$menu = this.$newElement.children(Selector.MENU);\n      this.$menuInner = this.$menu.children('.inner');\n      this.$searchbox = this.$menu.find('input');\n\n      element.classList.remove('bs-select-hidden');\n\n      this.fetchData(function () {\n        that.render(true);\n        that.buildList();\n\n        requestAnimationFrame(function () {\n          that.$element.trigger('loaded' + EVENT_KEY);\n        });\n      });\n\n      if (this.options.dropdownAlignRight === true) this.$menu[0].classList.add(classNames.MENURIGHT);\n\n      if (typeof id !== 'undefined') {\n        this.$button.attr('data-id', id);\n      }\n\n      this.checkDisabled();\n      this.clickListener();\n\n      if (version.major > 4) this.dropdown = new Dropdown(this.$button[0]);\n\n      if (this.options.liveSearch) {\n        this.liveSearchListener();\n        this.focusedParent = this.$searchbox[0];\n      } else {\n        this.focusedParent = this.$menuInner[0];\n      }\n\n      this.setStyle();\n      this.setWidth();\n      if (this.options.container) {\n        this.selectPosition();\n      } else {\n        this.$element.on('hide' + EVENT_KEY, function () {\n          if (that.isVirtual()) {\n            // empty menu on close\n            var menuInner = that.$menuInner[0],\n                emptyMenu = menuInner.firstChild.cloneNode(false);\n\n            // replace the existing UL with an empty one - this is faster than $.empty() or innerHTML = ''\n            menuInner.replaceChild(emptyMenu, menuInner.firstChild);\n            menuInner.scrollTop = 0;\n          }\n        });\n      }\n      this.$menu.data('this', this);\n      this.$newElement.data('this', this);\n      if (this.options.mobile) this.mobile();\n\n      this.$newElement.on({\n        'hide.bs.dropdown': function (e) {\n          that.$element.trigger('hide' + EVENT_KEY, e);\n        },\n        'hidden.bs.dropdown': function (e) {\n          that.$element.trigger('hidden' + EVENT_KEY, e);\n        },\n        'show.bs.dropdown': function (e) {\n          that.$element.trigger('show' + EVENT_KEY, e);\n        },\n        'shown.bs.dropdown': function (e) {\n          that.$element.trigger('shown' + EVENT_KEY, e);\n        }\n      });\n\n      if (element.hasAttribute('required')) {\n        this.$element.on('invalid' + EVENT_KEY, function () {\n          that.$button[0].classList.add('bs-invalid');\n\n          that.$element\n            .on('shown' + EVENT_KEY + '.invalid', function () {\n              that.$element\n                .val(that.$element.val()) // set the value to hide the validation message in Chrome when menu is opened\n                .off('shown' + EVENT_KEY + '.invalid');\n            })\n            .on('rendered' + EVENT_KEY, function () {\n              // if select is no longer invalid, remove the bs-invalid class\n              if (this.validity.valid) that.$button[0].classList.remove('bs-invalid');\n              that.$element.off('rendered' + EVENT_KEY);\n            });\n\n          that.$button.on('blur' + EVENT_KEY, function () {\n            that.$element.trigger('focus').trigger('blur');\n            that.$button.off('blur' + EVENT_KEY);\n          });\n        });\n      }\n\n      if (form) {\n        $(form).on('reset' + EVENT_KEY, function () {\n          requestAnimationFrame(function () {\n            that.render();\n          });\n        });\n      }\n    },\n\n    createDropdown: function () {\n      // Options\n      // If we are multiple or showTick option is set, then add the show-tick class\n      var showTick = (this.multiple || this.options.showTick) ? ' show-tick' : '',\n          multiselectable = this.multiple ? ' aria-multiselectable=\"true\"' : '',\n          inputGroup = '',\n          autofocus = this.autofocus ? ' autofocus' : '';\n\n      if (version.major < 4 && this.$element.parent().hasClass('input-group')) {\n        inputGroup = ' input-group-btn';\n      }\n\n      // Elements\n      var drop,\n          header = '',\n          searchbox = '',\n          actionsbox = '',\n          donebutton = '',\n          clearButton = '';\n\n      if (this.options.header) {\n        header =\n          '<div class=\"' + classNames.POPOVERHEADER + '\">' +\n            '<button type=\"button\" class=\"close\" aria-hidden=\"true\">&times;</button>' +\n              this.options.header +\n          '</div>';\n      }\n\n      if (this.options.liveSearch) {\n        searchbox =\n          '<div class=\"bs-searchbox\">' +\n            '<input type=\"search\" class=\"form-control\" autocomplete=\"off\"' +\n              (\n                this.options.liveSearchPlaceholder === null ? ''\n                :\n                ' placeholder=\"' + htmlEscape(this.options.liveSearchPlaceholder) + '\"'\n              ) +\n              ' role=\"combobox\" aria-label=\"Search\" aria-controls=\"' + this.selectId + '\" aria-autocomplete=\"list\">' +\n          '</div>';\n      }\n\n      if (this.multiple && this.options.actionsBox) {\n        actionsbox =\n          '<div class=\"bs-actionsbox\">' +\n            '<div class=\"btn-group btn-group-sm\">' +\n              '<button type=\"button\" class=\"actions-btn bs-select-all btn ' + classNames.BUTTONCLASS + '\">' +\n                this.options.selectAllText +\n              '</button>' +\n              '<button type=\"button\" class=\"actions-btn bs-deselect-all btn ' + classNames.BUTTONCLASS + '\">' +\n                this.options.deselectAllText +\n              '</button>' +\n            '</div>' +\n          '</div>';\n      }\n\n      if (this.multiple && this.options.doneButton) {\n        donebutton =\n          '<div class=\"bs-donebutton\">' +\n            '<div class=\"btn-group\">' +\n              '<button type=\"button\" class=\"btn btn-sm ' + classNames.BUTTONCLASS + '\">' +\n                this.options.doneButtonText +\n              '</button>' +\n            '</div>' +\n          '</div>';\n      }\n\n      if (this.options.allowClear) {\n        clearButton = '<span class=\"close bs-select-clear-selected\" title=\"' + this.options.deselectAllText + '\"><span>&times;</span>';\n      }\n\n      drop =\n        '<div class=\"dropdown bootstrap-select' + showTick + inputGroup + '\">' +\n          '<button type=\"button\" tabindex=\"-1\" class=\"' +\n            this.options.styleBase +\n            ' dropdown-toggle\" ' +\n            (this.options.display === 'static' ? 'data-display=\"static\"' : '') +\n            Selector.DATA_TOGGLE +\n            autofocus +\n            ' role=\"combobox\" aria-owns=\"' +\n            this.selectId +\n            '\" aria-haspopup=\"listbox\" aria-expanded=\"false\">' +\n            '<div class=\"filter-option\">' +\n              '<div class=\"filter-option-inner\">' +\n                '<div class=\"filter-option-inner-inner\">&nbsp;</div>' +\n              '</div> ' +\n            '</div>' +\n            clearButton +\n            '</span>' +\n            (\n              version.major >= '4' ? ''\n              :\n              '<span class=\"bs-caret\">' +\n                this.options.template.caret +\n              '</span>'\n            ) +\n          '</button>' +\n          '<div class=\"' + classNames.MENU + ' ' + (version.major >= '4' ? '' : classNames.SHOW) + '\">' +\n            header +\n            searchbox +\n            actionsbox +\n            '<div class=\"inner ' + classNames.SHOW + '\" role=\"listbox\" id=\"' + this.selectId + '\" tabindex=\"-1\" ' + multiselectable + '>' +\n                '<ul class=\"' + classNames.MENU + ' inner ' + (version.major >= '4' ? classNames.SHOW : '') + '\" role=\"presentation\">' +\n                '</ul>' +\n            '</div>' +\n            donebutton +\n          '</div>' +\n        '</div>';\n\n      return $(drop);\n    },\n\n    setPositionData: function () {\n      this.selectpicker.view.canHighlight = [];\n      this.selectpicker.view.size = 0;\n      this.selectpicker.view.firstHighlightIndex = false;\n\n      for (var i = 0; i < this.selectpicker.current.data.length; i++) {\n        var li = this.selectpicker.current.data[i],\n            canHighlight = true;\n\n        if (li.type === 'divider') {\n          canHighlight = false;\n          li.height = this.sizeInfo.dividerHeight;\n        } else if (li.type === 'optgroup-label') {\n          canHighlight = false;\n          li.height = this.sizeInfo.dropdownHeaderHeight;\n        } else {\n          li.height = this.sizeInfo.liHeight;\n        }\n\n        if (li.disabled) canHighlight = false;\n\n        this.selectpicker.view.canHighlight.push(canHighlight);\n\n        if (canHighlight) {\n          this.selectpicker.view.size++;\n          li.posinset = this.selectpicker.view.size;\n          if (this.selectpicker.view.firstHighlightIndex === false) this.selectpicker.view.firstHighlightIndex = i;\n        }\n\n        li.position = (i === 0 ? 0 : this.selectpicker.current.data[i - 1].position) + li.height;\n      }\n    },\n\n    isVirtual: function () {\n      return (this.options.virtualScroll !== false) && (this.selectpicker.main.data.length >= this.options.virtualScroll) || this.options.virtualScroll === true;\n    },\n\n    createView: function (isSearching, setSize, refresh) {\n      var that = this,\n          scrollTop = 0;\n\n      this.selectpicker.isSearching = isSearching;\n      this.selectpicker.current = isSearching ? this.selectpicker.search : this.selectpicker.main;\n\n      this.setPositionData();\n\n      if (setSize) {\n        if (refresh) {\n          scrollTop = this.$menuInner[0].scrollTop;\n        } else if (!that.multiple) {\n          var element = that.$element[0],\n              selectedIndex = (element.options[element.selectedIndex] || {}).liIndex;\n\n          if (typeof selectedIndex === 'number' && that.options.size !== false) {\n            var selectedData = that.selectpicker.main.data[selectedIndex],\n                position = selectedData && selectedData.position;\n\n            if (position) {\n              scrollTop = position - ((that.sizeInfo.menuInnerHeight + that.sizeInfo.liHeight) / 2);\n            }\n          }\n        }\n      }\n\n      scroll(scrollTop, true);\n\n      this.$menuInner.off('scroll.createView').on('scroll.createView', function (e, updateValue) {\n        if (!that.noScroll) scroll(this.scrollTop, updateValue);\n        that.noScroll = false;\n      });\n\n      function scroll (scrollTop, init) {\n        var size = that.selectpicker.current.data.length,\n            chunks = [],\n            chunkSize,\n            chunkCount,\n            firstChunk,\n            lastChunk,\n            currentChunk,\n            prevPositions,\n            positionIsDifferent,\n            previousElements,\n            menuIsDifferent = true,\n            isVirtual = that.isVirtual();\n\n        that.selectpicker.view.scrollTop = scrollTop;\n\n        chunkSize = that.options.chunkSize; // number of options in a chunk\n        chunkCount = Math.ceil(size / chunkSize) || 1; // number of chunks\n\n        for (var i = 0; i < chunkCount; i++) {\n          var endOfChunk = (i + 1) * chunkSize;\n\n          if (i === chunkCount - 1) {\n            endOfChunk = size;\n          }\n\n          chunks[i] = [\n            (i) * chunkSize + (!i ? 0 : 1),\n            endOfChunk\n          ];\n\n          if (!size) break;\n\n          if (currentChunk === undefined && scrollTop - 1 <= that.selectpicker.current.data[endOfChunk - 1].position - that.sizeInfo.menuInnerHeight) {\n            currentChunk = i;\n          }\n        }\n\n        if (currentChunk === undefined) currentChunk = 0;\n\n        prevPositions = [that.selectpicker.view.position0, that.selectpicker.view.position1];\n\n        // always display previous, current, and next chunks\n        firstChunk = Math.max(0, currentChunk - 1);\n        lastChunk = Math.min(chunkCount - 1, currentChunk + 1);\n\n        that.selectpicker.view.position0 = isVirtual === false ? 0 : (Math.max(0, chunks[firstChunk][0]) || 0);\n        that.selectpicker.view.position1 = isVirtual === false ? size : (Math.min(size, chunks[lastChunk][1]) || 0);\n\n        positionIsDifferent = prevPositions[0] !== that.selectpicker.view.position0 || prevPositions[1] !== that.selectpicker.view.position1;\n\n        if (that.activeElement !== undefined) {\n          if (init) {\n            if (that.activeElement !== that.selectedElement) {\n              that.defocusItem(that.activeElement);\n            }\n            that.activeElement = undefined;\n          }\n\n          if (that.activeElement !== that.selectedElement) {\n            that.defocusItem(that.selectedElement);\n          }\n        }\n\n        if (that.prevActiveElement !== undefined && that.prevActiveElement !== that.activeElement && that.prevActiveElement !== that.selectedElement) {\n          that.defocusItem(that.prevActiveElement);\n        }\n\n        if (init || positionIsDifferent || that.selectpicker.current.hasMore) {\n          previousElements = that.selectpicker.view.visibleElements ? that.selectpicker.view.visibleElements.slice() : [];\n\n          if (isVirtual === false) {\n            that.selectpicker.view.visibleElements = that.selectpicker.current.elements;\n          } else {\n            that.selectpicker.view.visibleElements = that.selectpicker.current.elements.slice(that.selectpicker.view.position0, that.selectpicker.view.position1);\n          }\n\n          that.setOptionStatus();\n\n          // if searching, check to make sure the list has actually been updated before updating DOM\n          // this prevents unnecessary repaints\n          if (isSearching || (isVirtual === false && init)) menuIsDifferent = !isEqual(previousElements, that.selectpicker.view.visibleElements);\n\n          // if virtual scroll is disabled and not searching,\n          // menu should never need to be updated more than once\n          if ((init || isVirtual === true) && menuIsDifferent) {\n            var menuInner = that.$menuInner[0],\n                menuFragment = document.createDocumentFragment(),\n                emptyMenu = menuInner.firstChild.cloneNode(false),\n                marginTop,\n                marginBottom,\n                elements = that.selectpicker.view.visibleElements,\n                toSanitize = [];\n\n            // replace the existing UL with an empty one - this is faster than $.empty()\n            menuInner.replaceChild(emptyMenu, menuInner.firstChild);\n\n            for (var i = 0, visibleElementsLen = elements.length; i < visibleElementsLen; i++) {\n              var element = elements[i],\n                  elText,\n                  elementData;\n\n              if (that.options.sanitize) {\n                elText = element.lastChild;\n\n                if (elText) {\n                  elementData = that.selectpicker.current.data[i + that.selectpicker.view.position0];\n\n                  if (elementData && elementData.content && !elementData.sanitized) {\n                    toSanitize.push(elText);\n                    elementData.sanitized = true;\n                  }\n                }\n              }\n\n              menuFragment.appendChild(element);\n            }\n\n            if (that.options.sanitize && toSanitize.length) {\n              sanitizeHtml(toSanitize, that.options.whiteList, that.options.sanitizeFn);\n            }\n\n            if (isVirtual === true) {\n              marginTop = (that.selectpicker.view.position0 === 0 ? 0 : that.selectpicker.current.data[that.selectpicker.view.position0 - 1].position);\n              marginBottom = (that.selectpicker.view.position1 > size - 1 ? 0 : that.selectpicker.current.data[size - 1].position - that.selectpicker.current.data[that.selectpicker.view.position1 - 1].position);\n\n              menuInner.firstChild.style.marginTop = marginTop + 'px';\n              menuInner.firstChild.style.marginBottom = marginBottom + 'px';\n            } else {\n              menuInner.firstChild.style.marginTop = 0;\n              menuInner.firstChild.style.marginBottom = 0;\n            }\n\n            menuInner.firstChild.appendChild(menuFragment);\n\n            // if an option is encountered that is wider than the current menu width, update the menu width accordingly\n            // switch to ResizeObserver with increased browser support\n            if (isVirtual === true && that.sizeInfo.hasScrollBar) {\n              var menuInnerInnerWidth = menuInner.firstChild.offsetWidth;\n\n              if (init && menuInnerInnerWidth < that.sizeInfo.menuInnerInnerWidth && that.sizeInfo.totalMenuWidth > that.sizeInfo.selectWidth) {\n                menuInner.firstChild.style.minWidth = that.sizeInfo.menuInnerInnerWidth + 'px';\n              } else if (menuInnerInnerWidth > that.sizeInfo.menuInnerInnerWidth) {\n                // set to 0 to get actual width of menu\n                that.$menu[0].style.minWidth = 0;\n\n                var actualMenuWidth = menuInner.firstChild.offsetWidth;\n\n                if (actualMenuWidth > that.sizeInfo.menuInnerInnerWidth) {\n                  that.sizeInfo.menuInnerInnerWidth = actualMenuWidth;\n                  menuInner.firstChild.style.minWidth = that.sizeInfo.menuInnerInnerWidth + 'px';\n                }\n\n                // reset to default CSS styling\n                that.$menu[0].style.minWidth = '';\n              }\n            }\n          }\n\n          if ((!isSearching && that.options.source.data || isSearching && that.options.source.search) && that.selectpicker.current.hasMore && currentChunk === chunkCount - 1) {\n            // Don't load the next chunk until scrolling has started\n            // This prevents unnecessary requests while the user is typing if pageSize is <= chunkSize\n            if (scrollTop > 0) {\n              // Chunks use 0-based indexing, but pages use 1-based. Add 1 to convert and add 1 again to get next page\n              var page = Math.floor((currentChunk * that.options.chunkSize) / that.options.source.pageSize) + 2;\n\n              that.fetchData(function () {\n                that.render();\n                that.buildList(size, isSearching);\n                that.setPositionData();\n                scroll(scrollTop);\n              }, isSearching ? 'search' : 'data', page, isSearching ? that.selectpicker.search.previousValue : undefined);\n            }\n          }\n        }\n\n        that.prevActiveElement = that.activeElement;\n\n        if (!that.options.liveSearch) {\n          that.$menuInner.trigger('focus');\n        } else if (isSearching && init) {\n          var index = 0,\n              newActive;\n\n          if (!that.selectpicker.view.canHighlight[index]) {\n            index = 1 + that.selectpicker.view.canHighlight.slice(1).indexOf(true);\n          }\n\n          newActive = that.selectpicker.view.visibleElements[index];\n\n          that.defocusItem(that.selectpicker.view.currentActive);\n\n          that.activeElement = (that.selectpicker.current.data[index] || {}).element;\n\n          that.focusItem(newActive);\n        }\n      }\n\n      $(window)\n        .off('resize' + EVENT_KEY + '.' + this.selectId + '.createView')\n        .on('resize' + EVENT_KEY + '.' + this.selectId + '.createView', function () {\n          var isActive = that.$newElement.hasClass(classNames.SHOW);\n\n          if (isActive) scroll(that.$menuInner[0].scrollTop);\n        });\n    },\n\n    focusItem: function (li, liData, noStyle) {\n      if (li) {\n        liData = liData || this.selectpicker.current.data[this.selectpicker.current.elements.indexOf(this.activeElement)];\n        var a = li.firstChild;\n\n        if (a) {\n          a.setAttribute('aria-setsize', this.selectpicker.view.size);\n          a.setAttribute('aria-posinset', liData.posinset);\n\n          if (noStyle !== true) {\n            this.focusedParent.setAttribute('aria-activedescendant', a.id);\n            li.classList.add('active');\n            a.classList.add('active');\n          }\n        }\n      }\n    },\n\n    defocusItem: function (li) {\n      if (li) {\n        li.classList.remove('active');\n        if (li.firstChild) li.firstChild.classList.remove('active');\n      }\n    },\n\n    setPlaceholder: function () {\n      var that = this,\n          updateIndex = false;\n\n      if ((this.options.placeholder || this.options.allowClear) && !this.multiple) {\n        if (!this.selectpicker.view.titleOption) this.selectpicker.view.titleOption = document.createElement('option');\n\n        // this option doesn't create a new <li> element, but does add a new option at the start,\n        // so startIndex should increase to prevent having to check every option for the bs-title-option class\n        updateIndex = true;\n\n        var element = this.$element[0],\n            selectTitleOption = false,\n            titleNotAppended = !this.selectpicker.view.titleOption.parentNode,\n            selectedIndex = element.selectedIndex,\n            selectedOption = element.options[selectedIndex],\n            firstSelectable = element.querySelector('select > *:not(:disabled)'),\n            firstSelectableIndex = firstSelectable ? firstSelectable.index : 0,\n            navigation = window.performance && window.performance.getEntriesByType('navigation'),\n            // Safari doesn't support getEntriesByType('navigation') - fall back to performance.navigation\n            isNotBackForward = (navigation && navigation.length) ? navigation[0].type !== 'back_forward' : window.performance.navigation.type !== 2;\n\n        if (titleNotAppended) {\n          // Use native JS to prepend option (faster)\n          this.selectpicker.view.titleOption.className = 'bs-title-option';\n          this.selectpicker.view.titleOption.value = '';\n\n          // Check if selected or data-selected attribute is already set on an option. If not, select the titleOption option.\n          // the selected item may have been changed by user or programmatically before the bootstrap select plugin runs,\n          // if so, the select will have the data-selected attribute\n          selectTitleOption = !selectedOption || (selectedIndex === firstSelectableIndex && selectedOption.defaultSelected === false && this.$element.data('selected') === undefined);\n        }\n\n        if (titleNotAppended || this.selectpicker.view.titleOption.index !== 0) {\n          element.insertBefore(this.selectpicker.view.titleOption, element.firstChild);\n        }\n\n        // Set selected *after* appending to select,\n        // otherwise the option doesn't get selected in IE\n        // set using selectedIndex, as setting the selected attr to true here doesn't work in IE11\n        if (selectTitleOption && isNotBackForward) {\n          element.selectedIndex = 0;\n        } else if (document.readyState !== 'complete') {\n          // if navigation type is back_forward, there's a chance the select will have its value set by BFCache\n          // wait for that value to be set, then run render again\n          window.addEventListener('pageshow', function () {\n            if (that.selectpicker.view.displayedValue !== element.value) that.render();\n          });\n        }\n      }\n\n      return updateIndex;\n    },\n\n    fetchData: function (callback, type, page, searchValue) {\n      page = page || 1;\n      type = type || 'data';\n\n      var that = this,\n          data = this.options.source[type],\n          builtData;\n\n      if (data) {\n        this.options.virtualScroll = true;\n\n        if (typeof data === 'function') {\n          data.call(\n            this,\n            function (data, more, totalItems) {\n              var current = that.selectpicker[type === 'search' ? 'search' : 'main'];\n              current.hasMore = more;\n              current.totalItems = totalItems;\n              builtData = that.buildData(data, type);\n              callback.call(that, builtData);\n              that.$element.trigger('fetched' + EVENT_KEY);\n            },\n            page,\n            searchValue\n          );\n        } else if (Array.isArray(data)) {\n          builtData = that.buildData(data, type);\n          callback.call(that, builtData);\n        }\n      } else {\n        builtData = this.buildData(false, type);\n        callback.call(that, builtData);\n      }\n    },\n\n    buildData: function (data, type) {\n      var that = this;\n      var dataGetter = data === false ? getOptionData.fromOption : getOptionData.fromDataSource;\n\n      var optionSelector = ':not([hidden]):not([data-hidden=\"true\"]):not([style*=\"display: none\"])',\n          mainData = [],\n          startLen = this.selectpicker.main.data ? this.selectpicker.main.data.length : 0,\n          optID = 0,\n          startIndex = this.setPlaceholder() && !data ? 1 : 0; // append the titleOption if necessary and skip the first option in the loop\n\n      if (type === 'search') {\n        startLen = this.selectpicker.search.data.length;\n      }\n\n      if (this.options.hideDisabled) optionSelector += ':not(:disabled)';\n\n      var selectOptions = data ? data.filter(filterHidden, this) : this.$element[0].querySelectorAll('select > *' + optionSelector);\n\n      function addDivider (config) {\n        var previousData = mainData[mainData.length - 1];\n\n        // ensure optgroup doesn't create back-to-back dividers\n        if (\n          previousData &&\n          previousData.type === 'divider' &&\n          (previousData.optID || config.optID)\n        ) {\n          return;\n        }\n\n        config = config || {};\n        config.type = 'divider';\n\n        mainData.push(config);\n      }\n\n      function addOption (item, config) {\n        config = config || {};\n\n        config.divider = dataGetter(item, 'divider');\n\n        if (config.divider === true) {\n          addDivider({\n            optID: config.optID\n          });\n        } else {\n          var liIndex = mainData.length + startLen,\n              cssText = dataGetter(item, 'style'),\n              inlineStyle = cssText ? htmlEscape(cssText) : '',\n              optionClass = (item.className || '') + (config.optgroupClass || '');\n\n          if (config.optID) optionClass = 'opt ' + optionClass;\n\n          config.optionClass = optionClass.trim();\n          config.inlineStyle = inlineStyle;\n\n          config.text = dataGetter(item, 'text');\n          config.title = dataGetter(item, 'title');\n          config.content = dataGetter(item, 'content');\n          config.tokens = dataGetter(item, 'tokens');\n          config.subtext = dataGetter(item, 'subtext');\n          config.icon = dataGetter(item, 'icon');\n\n          config.display = config.content || config.text;\n          config.value = item.value === undefined ? item.text : item.value;\n          config.type = 'option';\n          config.index = liIndex;\n\n          config.option = !item.option ? item : item.option; // reference option element if it exists\n          config.option.liIndex = liIndex;\n          config.selected = !!item.selected;\n          config.disabled = config.disabled || !!item.disabled;\n\n          if (data !== false) {\n            if (that.selectpicker.optionValuesDataMap[config.value]) {\n              config = $.extend(that.selectpicker.optionValuesDataMap[config.value], config);\n            } else {\n              that.selectpicker.optionValuesDataMap[config.value] = config;\n            }\n          }\n\n          mainData.push(config);\n        }\n      }\n\n      function addOptgroup (index, selectOptions) {\n        var optgroup = selectOptions[index],\n            // skip placeholder option\n            previous = index - 1 < startIndex ? false : selectOptions[index - 1],\n            next = selectOptions[index + 1],\n            options = data ? optgroup.children.filter(filterHidden, this) : optgroup.querySelectorAll('option' + optionSelector);\n\n        if (!options.length) return;\n\n        var config = {\n              display: htmlEscape(dataGetter(item, 'label')),\n              subtext: dataGetter(optgroup, 'subtext'),\n              icon: dataGetter(optgroup, 'icon'),\n              type: 'optgroup-label',\n              optgroupClass: ' ' + (optgroup.className || ''),\n              optgroup: optgroup\n            },\n            headerIndex,\n            lastIndex;\n\n        optID++;\n\n        if (previous) {\n          addDivider({ optID: optID });\n        }\n\n        config.optID = optID;\n\n        mainData.push(config);\n\n        for (var j = 0, len = options.length; j < len; j++) {\n          var option = options[j];\n\n          if (j === 0) {\n            headerIndex = mainData.length - 1;\n            lastIndex = headerIndex + len;\n          }\n\n          addOption(option, {\n            headerIndex: headerIndex,\n            lastIndex: lastIndex,\n            optID: config.optID,\n            optgroupClass: config.optgroupClass,\n            disabled: optgroup.disabled\n          });\n        }\n\n        if (next) {\n          addDivider({ optID: optID });\n        }\n      }\n\n      for (var len = selectOptions.length, i = startIndex; i < len; i++) {\n        var item = selectOptions[i],\n            children = item.children;\n\n        if (children && children.length) {\n          addOptgroup.call(this, i, selectOptions);\n        } else {\n          addOption.call(this, item, {});\n        }\n      }\n\n      switch (type) {\n        case 'data': {\n          if (!this.selectpicker.main.data) {\n            this.selectpicker.main.data = [];\n          }\n          Array.prototype.push.apply(this.selectpicker.main.data, mainData);\n          this.selectpicker.current.data = this.selectpicker.main.data;\n          break;\n        }\n        case 'search': {\n          Array.prototype.push.apply(this.selectpicker.search.data, mainData);\n          break;\n        }\n      }\n\n      return mainData;\n    },\n\n    buildList: function (size, searching) {\n      var that = this,\n          selectData = searching ? this.selectpicker.search.data : this.selectpicker.main.data,\n          mainElements = [],\n          widestOptionLength = 0;\n\n      if ((that.options.showTick || that.multiple) && !elementTemplates.checkMark.parentNode) {\n        elementTemplates.checkMark.className = this.options.iconBase + ' ' + that.options.tickIcon + ' check-mark';\n        elementTemplates.a.appendChild(elementTemplates.checkMark);\n      }\n\n      function buildElement (mainElements, item) {\n        var liElement,\n            combinedLength = 0;\n\n        switch (item.type) {\n          case 'divider':\n            liElement = generateOption.li(\n              false,\n              classNames.DIVIDER,\n              (item.optID ? item.optID + 'div' : undefined)\n            );\n\n            break;\n\n          case 'option':\n            liElement = generateOption.li(\n              generateOption.a(\n                generateOption.text.call(that, item),\n                item.optionClass,\n                item.inlineStyle\n              ),\n              '',\n              item.optID\n            );\n\n            if (liElement.firstChild) {\n              liElement.firstChild.id = that.selectId + '-' + item.index;\n            }\n\n            break;\n\n          case 'optgroup-label':\n            liElement = generateOption.li(\n              generateOption.label.call(that, item),\n              'dropdown-header' + item.optgroupClass,\n              item.optID\n            );\n\n            break;\n        }\n\n        if (!item.element) {\n          item.element = liElement;\n        } else {\n          item.element.innerHTML = liElement.innerHTML;\n        }\n        mainElements.push(item.element);\n\n        // count the number of characters in the option - not perfect, but should work in most cases\n        if (item.display) combinedLength += item.display.length;\n        if (item.subtext) combinedLength += item.subtext.length;\n        // if there is an icon, ensure this option's width is checked\n        if (item.icon) combinedLength += 1;\n\n        if (combinedLength > widestOptionLength) {\n          widestOptionLength = combinedLength;\n\n          // guess which option is the widest\n          // use this when calculating menu width\n          // not perfect, but it's fast, and the width will be updating accordingly when scrolling\n          that.selectpicker.view.widestOption = mainElements[mainElements.length - 1];\n        }\n      }\n\n      var startIndex = size || 0;\n\n      for (var len = selectData.length, i = startIndex; i < len; i++) {\n        var item = selectData[i];\n\n        buildElement(mainElements, item);\n      }\n\n      if (size) {\n        if (searching) {\n          Array.prototype.push.apply(this.selectpicker.search.elements, mainElements);\n        } else {\n          Array.prototype.push.apply(this.selectpicker.main.elements, mainElements);\n          this.selectpicker.current.elements = this.selectpicker.main.elements;\n        }\n      } else {\n        if (searching) {\n          this.selectpicker.search.elements = mainElements;\n        } else {\n          this.selectpicker.main.elements = this.selectpicker.current.elements = mainElements;\n        }\n      }\n    },\n\n    findLis: function () {\n      return this.$menuInner.find('.inner > li');\n    },\n\n    render: function (init) {\n      var that = this,\n          element = this.$element[0],\n          // ensure titleOption is appended and selected (if necessary) before getting selectedOptions\n          placeholderSelected = this.setPlaceholder() && element.selectedIndex === 0,\n          selectedOptions = getSelectedOptions.call(this),\n          selectedCount = selectedOptions.length,\n          selectedValues = getSelectValues.call(this, selectedOptions),\n          button = this.$button[0],\n          buttonInner = button.querySelector('.filter-option-inner-inner'),\n          multipleSeparator = document.createTextNode(this.options.multipleSeparator),\n          titleFragment = elementTemplates.fragment.cloneNode(false),\n          showCount,\n          countMax,\n          hasContent = false;\n\n      function createSelected (item) {\n        if (item.selected) {\n          that.createOption(item, true);\n        } else if (item.children && item.children.length) {\n          item.children.map(createSelected);\n        }\n      }\n\n      // create selected option elements to ensure select value is correct\n      if (this.options.source.data && init) {\n        selectedOptions.map(createSelected);\n        element.appendChild(this.selectpicker.main.optionQueue);\n\n        if (placeholderSelected) placeholderSelected = element.selectedIndex === 0;\n      }\n\n      button.classList.toggle('bs-placeholder', that.multiple ? !selectedCount : !selectedValues && selectedValues !== 0);\n\n      if (!that.multiple && selectedOptions.length === 1) {\n        that.selectpicker.view.displayedValue = selectedValues;\n      }\n\n      if (this.options.selectedTextFormat === 'static') {\n        titleFragment = generateOption.text.call(this, { text: this.options.placeholder }, true);\n      } else {\n        showCount = this.multiple && this.options.selectedTextFormat.indexOf('count') !== -1 && selectedCount > 0;\n\n        // determine if the number of selected options will be shown (showCount === true)\n        if (showCount) {\n          countMax = this.options.selectedTextFormat.split('>');\n          showCount = (countMax.length > 1 && selectedCount > countMax[1]) || (countMax.length === 1 && selectedCount >= 2);\n        }\n\n        // only loop through all selected options if the count won't be shown\n        if (showCount === false) {\n          if (!placeholderSelected) {\n            for (var selectedIndex = 0; selectedIndex < selectedCount; selectedIndex++) {\n              if (selectedIndex < 50) {\n                var option = selectedOptions[selectedIndex],\n                    titleOptions = {};\n\n                if (option) {\n                  if (this.multiple && selectedIndex > 0) {\n                    titleFragment.appendChild(multipleSeparator.cloneNode(false));\n                  }\n\n                  if (option.title) {\n                    titleOptions.text = option.title;\n                  } else if (option.content && that.options.showContent) {\n                    titleOptions.content = option.content.toString();\n                    hasContent = true;\n                  } else {\n                    if (that.options.showIcon) {\n                      titleOptions.icon = option.icon;\n                    }\n                    if (that.options.showSubtext && !that.multiple && option.subtext) titleOptions.subtext = ' ' + option.subtext;\n                    titleOptions.text = option.text.trim();\n                  }\n\n                  titleFragment.appendChild(generateOption.text.call(this, titleOptions, true));\n                }\n              } else {\n                break;\n              }\n            }\n\n            // add ellipsis\n            if (selectedCount > 49) {\n              titleFragment.appendChild(document.createTextNode('...'));\n            }\n          }\n        } else {\n          var optionSelector = ':not([hidden]):not([data-hidden=\"true\"]):not([data-divider=\"true\"]):not([style*=\"display: none\"])';\n          if (this.options.hideDisabled) optionSelector += ':not(:disabled)';\n\n          // If this is a multiselect, and selectedTextFormat is count, then show 1 of 2 selected, etc.\n          var totalCount = this.$element[0].querySelectorAll('select > option' + optionSelector + ', optgroup' + optionSelector + ' option' + optionSelector).length,\n              tr8nText = (typeof this.options.countSelectedText === 'function') ? this.options.countSelectedText(selectedCount, totalCount) : this.options.countSelectedText;\n\n          titleFragment = generateOption.text.call(this, {\n            text: tr8nText.replace('{0}', selectedCount.toString()).replace('{1}', totalCount.toString())\n          }, true);\n        }\n      }\n\n      // If the select doesn't have a title, then use the default, or if nothing is set at all, use noneSelectedText\n      if (!titleFragment.childNodes.length) {\n        titleFragment = generateOption.text.call(this, {\n          text: this.options.placeholder ? this.options.placeholder : this.options.noneSelectedText\n        }, true);\n      }\n\n      // if the select has a title, apply it to the button, and if not, apply titleFragment text\n      // strip all HTML tags and trim the result, then unescape any escaped tags\n      button.title = titleFragment.textContent.replace(/<[^>]*>?/g, '').trim();\n\n      if (this.options.sanitize && hasContent) {\n        sanitizeHtml([titleFragment], that.options.whiteList, that.options.sanitizeFn);\n      }\n\n      buttonInner.innerHTML = '';\n      buttonInner.appendChild(titleFragment);\n\n      if (version.major < 4 && this.$newElement[0].classList.contains('bs3-has-addon')) {\n        var filterExpand = button.querySelector('.filter-expand'),\n            clone = buttonInner.cloneNode(true);\n\n        clone.className = 'filter-expand';\n\n        if (filterExpand) {\n          button.replaceChild(clone, filterExpand);\n        } else {\n          button.appendChild(clone);\n        }\n      }\n\n      this.$element.trigger('rendered' + EVENT_KEY);\n    },\n\n    /**\n     * @param [style]\n     * @param [status]\n     */\n    setStyle: function (newStyle, status) {\n      var button = this.$button[0],\n          newElement = this.$newElement[0],\n          style = this.options.style.trim(),\n          buttonClass;\n\n      if (this.$element.attr('class')) {\n        this.$newElement.addClass(this.$element.attr('class').replace(/selectpicker|mobile-device|bs-select-hidden|validate\\[.*\\]/gi, ''));\n      }\n\n      if (version.major < 4) {\n        newElement.classList.add('bs3');\n\n        if (newElement.parentNode.classList && newElement.parentNode.classList.contains('input-group') &&\n            (newElement.previousElementSibling || newElement.nextElementSibling) &&\n            (newElement.previousElementSibling || newElement.nextElementSibling).classList.contains('input-group-addon')\n        ) {\n          newElement.classList.add('bs3-has-addon');\n        }\n      }\n\n      if (newStyle) {\n        buttonClass = newStyle.trim();\n      } else {\n        buttonClass = style;\n      }\n\n      if (status == 'add') {\n        if (buttonClass) button.classList.add.apply(button.classList, buttonClass.split(' '));\n      } else if (status == 'remove') {\n        if (buttonClass) button.classList.remove.apply(button.classList, buttonClass.split(' '));\n      } else {\n        if (style) button.classList.remove.apply(button.classList, style.split(' '));\n        if (buttonClass) button.classList.add.apply(button.classList, buttonClass.split(' '));\n      }\n    },\n\n    liHeight: function (refresh) {\n      if (!refresh && (this.options.size === false || Object.keys(this.sizeInfo).length)) return;\n\n      var newElement = elementTemplates.div.cloneNode(false),\n          menu = elementTemplates.div.cloneNode(false),\n          menuInner = elementTemplates.div.cloneNode(false),\n          menuInnerInner = document.createElement('ul'),\n          divider = elementTemplates.li.cloneNode(false),\n          dropdownHeader = elementTemplates.li.cloneNode(false),\n          li,\n          a = elementTemplates.a.cloneNode(false),\n          text = elementTemplates.span.cloneNode(false),\n          header = this.options.header && this.$menu.find('.' + classNames.POPOVERHEADER).length > 0 ? this.$menu.find('.' + classNames.POPOVERHEADER)[0].cloneNode(true) : null,\n          search = this.options.liveSearch ? elementTemplates.div.cloneNode(false) : null,\n          actions = this.options.actionsBox && this.multiple && this.$menu.find('.bs-actionsbox').length > 0 ? this.$menu.find('.bs-actionsbox')[0].cloneNode(true) : null,\n          doneButton = this.options.doneButton && this.multiple && this.$menu.find('.bs-donebutton').length > 0 ? this.$menu.find('.bs-donebutton')[0].cloneNode(true) : null,\n          firstOption = this.$element[0].options[0];\n\n      this.sizeInfo.selectWidth = this.$newElement[0].offsetWidth;\n\n      text.className = 'text';\n      a.className = 'dropdown-item ' + (firstOption ? firstOption.className : '');\n      newElement.className = this.$menu[0].parentNode.className + ' ' + classNames.SHOW;\n      newElement.style.width = 0; // ensure button width doesn't affect natural width of menu when calculating\n      if (this.options.width === 'auto') menu.style.minWidth = 0;\n      menu.className = classNames.MENU + ' ' + classNames.SHOW;\n      menuInner.className = 'inner ' + classNames.SHOW;\n      menuInnerInner.className = classNames.MENU + ' inner ' + (version.major >= '4' ? classNames.SHOW : '');\n      divider.className = classNames.DIVIDER;\n      dropdownHeader.className = 'dropdown-header';\n\n      text.appendChild(document.createTextNode('\\u200b'));\n\n      if (this.selectpicker.current.data.length) {\n        for (var i = 0; i < this.selectpicker.current.data.length; i++) {\n          var data = this.selectpicker.current.data[i];\n          if (data.type === 'option' && $(data.element.firstChild).css('display') !== 'none') {\n            li = data.element;\n            break;\n          }\n        }\n      } else {\n        li = elementTemplates.li.cloneNode(false);\n        a.appendChild(text);\n        li.appendChild(a);\n      }\n\n      dropdownHeader.appendChild(text.cloneNode(true));\n\n      if (this.selectpicker.view.widestOption) {\n        menuInnerInner.appendChild(this.selectpicker.view.widestOption.cloneNode(true));\n      }\n\n      menuInnerInner.appendChild(li);\n      menuInnerInner.appendChild(divider);\n      menuInnerInner.appendChild(dropdownHeader);\n      if (header) menu.appendChild(header);\n      if (search) {\n        var input = document.createElement('input');\n        search.className = 'bs-searchbox';\n        input.className = 'form-control';\n        search.appendChild(input);\n        menu.appendChild(search);\n      }\n      if (actions) menu.appendChild(actions);\n      menuInner.appendChild(menuInnerInner);\n      menu.appendChild(menuInner);\n      if (doneButton) menu.appendChild(doneButton);\n      newElement.appendChild(menu);\n\n      document.body.appendChild(newElement);\n\n      var liHeight = li.offsetHeight,\n          dropdownHeaderHeight = dropdownHeader ? dropdownHeader.offsetHeight : 0,\n          headerHeight = header ? header.offsetHeight : 0,\n          searchHeight = search ? search.offsetHeight : 0,\n          actionsHeight = actions ? actions.offsetHeight : 0,\n          doneButtonHeight = doneButton ? doneButton.offsetHeight : 0,\n          dividerHeight = $(divider).outerHeight(true),\n          menuStyle = window.getComputedStyle(menu),\n          menuWidth = menu.offsetWidth,\n          menuPadding = {\n            vert: toInteger(menuStyle.paddingTop) +\n                  toInteger(menuStyle.paddingBottom) +\n                  toInteger(menuStyle.borderTopWidth) +\n                  toInteger(menuStyle.borderBottomWidth),\n            horiz: toInteger(menuStyle.paddingLeft) +\n                  toInteger(menuStyle.paddingRight) +\n                  toInteger(menuStyle.borderLeftWidth) +\n                  toInteger(menuStyle.borderRightWidth)\n          },\n          menuExtras = {\n            vert: menuPadding.vert +\n                  toInteger(menuStyle.marginTop) +\n                  toInteger(menuStyle.marginBottom) + 2,\n            horiz: menuPadding.horiz +\n                  toInteger(menuStyle.marginLeft) +\n                  toInteger(menuStyle.marginRight) + 2\n          },\n          scrollBarWidth;\n\n      menuInner.style.overflowY = 'scroll';\n\n      scrollBarWidth = menu.offsetWidth - menuWidth;\n\n      document.body.removeChild(newElement);\n\n      this.sizeInfo.liHeight = liHeight;\n      this.sizeInfo.dropdownHeaderHeight = dropdownHeaderHeight;\n      this.sizeInfo.headerHeight = headerHeight;\n      this.sizeInfo.searchHeight = searchHeight;\n      this.sizeInfo.actionsHeight = actionsHeight;\n      this.sizeInfo.doneButtonHeight = doneButtonHeight;\n      this.sizeInfo.dividerHeight = dividerHeight;\n      this.sizeInfo.menuPadding = menuPadding;\n      this.sizeInfo.menuExtras = menuExtras;\n      this.sizeInfo.menuWidth = menuWidth;\n      this.sizeInfo.menuInnerInnerWidth = menuWidth - menuPadding.horiz;\n      this.sizeInfo.totalMenuWidth = this.sizeInfo.menuWidth;\n      this.sizeInfo.scrollBarWidth = scrollBarWidth;\n      this.sizeInfo.selectHeight = this.$newElement[0].offsetHeight;\n\n      this.setPositionData();\n    },\n\n    getSelectPosition: function () {\n      var that = this,\n          $window = $(window),\n          pos = that.$newElement.offset(),\n          $container = $(that.options.container),\n          containerPos;\n\n      if (that.options.container && $container.length && !$container.is('body')) {\n        containerPos = $container.offset();\n        containerPos.top += parseInt($container.css('borderTopWidth'));\n        containerPos.left += parseInt($container.css('borderLeftWidth'));\n      } else {\n        containerPos = { top: 0, left: 0 };\n      }\n\n      var winPad = that.options.windowPadding;\n\n      this.sizeInfo.selectOffsetTop = pos.top - containerPos.top - $window.scrollTop();\n      this.sizeInfo.selectOffsetBot = $window.height() - this.sizeInfo.selectOffsetTop - this.sizeInfo.selectHeight - containerPos.top - winPad[2];\n      this.sizeInfo.selectOffsetLeft = pos.left - containerPos.left - $window.scrollLeft();\n      this.sizeInfo.selectOffsetRight = $window.width() - this.sizeInfo.selectOffsetLeft - this.sizeInfo.selectWidth - containerPos.left - winPad[1];\n      this.sizeInfo.selectOffsetTop -= winPad[0];\n      this.sizeInfo.selectOffsetLeft -= winPad[3];\n    },\n\n    setMenuSize: function (isAuto) {\n      this.getSelectPosition();\n\n      var selectWidth = this.sizeInfo.selectWidth,\n          liHeight = this.sizeInfo.liHeight,\n          headerHeight = this.sizeInfo.headerHeight,\n          searchHeight = this.sizeInfo.searchHeight,\n          actionsHeight = this.sizeInfo.actionsHeight,\n          doneButtonHeight = this.sizeInfo.doneButtonHeight,\n          divHeight = this.sizeInfo.dividerHeight,\n          menuPadding = this.sizeInfo.menuPadding,\n          menuInnerHeight,\n          menuHeight,\n          divLength = 0,\n          minHeight,\n          _minHeight,\n          maxHeight,\n          menuInnerMinHeight,\n          estimate,\n          isDropup;\n\n      if (this.options.dropupAuto) {\n        // Get the estimated height of the menu without scrollbars.\n        // This is useful for smaller menus, where there might be plenty of room\n        // below the button without setting dropup, but we can't know\n        // the exact height of the menu until createView is called later\n        estimate = liHeight * this.selectpicker.current.data.length + menuPadding.vert;\n\n        isDropup = this.sizeInfo.selectOffsetTop - this.sizeInfo.selectOffsetBot > this.sizeInfo.menuExtras.vert && estimate + this.sizeInfo.menuExtras.vert + 50 > this.sizeInfo.selectOffsetBot;\n\n        // ensure dropup doesn't change while searching (so menu doesn't bounce back and forth)\n        if (this.selectpicker.isSearching === true) {\n          isDropup = this.selectpicker.dropup;\n        }\n\n        this.$newElement.toggleClass(classNames.DROPUP, isDropup);\n        this.selectpicker.dropup = isDropup;\n      }\n\n      if (this.options.size === 'auto') {\n        _minHeight = this.selectpicker.current.data.length > 3 ? this.sizeInfo.liHeight * 3 + this.sizeInfo.menuExtras.vert - 2 : 0;\n        menuHeight = this.sizeInfo.selectOffsetBot - this.sizeInfo.menuExtras.vert;\n        minHeight = _minHeight + headerHeight + searchHeight + actionsHeight + doneButtonHeight;\n        menuInnerMinHeight = Math.max(_minHeight - menuPadding.vert, 0);\n\n        if (this.$newElement.hasClass(classNames.DROPUP)) {\n          menuHeight = this.sizeInfo.selectOffsetTop - this.sizeInfo.menuExtras.vert;\n        }\n\n        maxHeight = menuHeight;\n        menuInnerHeight = menuHeight - headerHeight - searchHeight - actionsHeight - doneButtonHeight - menuPadding.vert;\n      } else if (this.options.size && this.options.size != 'auto' && this.selectpicker.current.elements.length > this.options.size) {\n        for (var i = 0; i < this.options.size; i++) {\n          if (this.selectpicker.current.data[i].type === 'divider') divLength++;\n        }\n\n        menuHeight = liHeight * this.options.size + divLength * divHeight + menuPadding.vert;\n        menuInnerHeight = menuHeight - menuPadding.vert;\n        maxHeight = menuHeight + headerHeight + searchHeight + actionsHeight + doneButtonHeight;\n        minHeight = menuInnerMinHeight = '';\n      }\n\n      this.$menu.css({\n        'max-height': maxHeight + 'px',\n        'overflow': 'hidden',\n        'min-height': minHeight + 'px'\n      });\n\n      this.$menuInner.css({\n        'max-height': menuInnerHeight + 'px',\n        'overflow': 'hidden auto',\n        'min-height': menuInnerMinHeight + 'px'\n      });\n\n      // ensure menuInnerHeight is always a positive number to prevent issues calculating chunkSize in createView\n      this.sizeInfo.menuInnerHeight = Math.max(menuInnerHeight, 1);\n\n      if (this.selectpicker.current.data.length && this.selectpicker.current.data[this.selectpicker.current.data.length - 1].position > this.sizeInfo.menuInnerHeight) {\n        this.sizeInfo.hasScrollBar = true;\n        this.sizeInfo.totalMenuWidth = this.sizeInfo.menuWidth + this.sizeInfo.scrollBarWidth;\n      }\n\n      if (this.options.dropdownAlignRight === 'auto') {\n        this.$menu.toggleClass(classNames.MENURIGHT, this.sizeInfo.selectOffsetLeft > this.sizeInfo.selectOffsetRight && this.sizeInfo.selectOffsetRight < (this.sizeInfo.totalMenuWidth - selectWidth));\n      }\n\n      if (this.dropdown && this.dropdown._popper) this.dropdown._popper.update();\n    },\n\n    setSize: function (refresh) {\n      this.liHeight(refresh);\n\n      if (this.options.header) this.$menu.css('padding-top', 0);\n\n      if (this.options.size !== false) {\n        var that = this,\n            $window = $(window);\n\n        this.setMenuSize();\n\n        if (this.options.liveSearch) {\n          this.$searchbox\n            .off('input.setMenuSize propertychange.setMenuSize')\n            .on('input.setMenuSize propertychange.setMenuSize', function () {\n              return that.setMenuSize();\n            });\n        }\n\n        if (this.options.size === 'auto') {\n          $window\n            .off('resize' + EVENT_KEY + '.' + this.selectId + '.setMenuSize' + ' scroll' + EVENT_KEY + '.' + this.selectId + '.setMenuSize')\n            .on('resize' + EVENT_KEY + '.' + this.selectId + '.setMenuSize' + ' scroll' + EVENT_KEY + '.' + this.selectId + '.setMenuSize', function () {\n              return that.setMenuSize();\n            });\n        } else if (this.options.size && this.options.size != 'auto' && this.selectpicker.current.elements.length > this.options.size) {\n          $window.off('resize' + EVENT_KEY + '.' + this.selectId + '.setMenuSize' + ' scroll' + EVENT_KEY + '.' + this.selectId + '.setMenuSize');\n        }\n      }\n\n      this.createView(false, true, refresh);\n    },\n\n    setWidth: function () {\n      var that = this;\n\n      if (this.options.width === 'auto') {\n        requestAnimationFrame(function () {\n          that.$menu.css('min-width', '0');\n\n          that.$element.on('loaded' + EVENT_KEY, function () {\n            that.liHeight();\n            that.setMenuSize();\n\n            // Get correct width if element is hidden\n            var $selectClone = that.$newElement.clone().appendTo('body'),\n                btnWidth = $selectClone.css('width', 'auto').children('button').outerWidth();\n\n            $selectClone.remove();\n\n            // Set width to whatever's larger, button title or longest option\n            that.sizeInfo.selectWidth = Math.max(that.sizeInfo.totalMenuWidth, btnWidth);\n            that.$newElement.css('width', that.sizeInfo.selectWidth + 'px');\n          });\n        });\n      } else if (this.options.width === 'fit') {\n        // Remove inline min-width so width can be changed from 'auto'\n        this.$menu.css('min-width', '');\n        this.$newElement.css('width', '').addClass('fit-width');\n      } else if (this.options.width) {\n        // Remove inline min-width so width can be changed from 'auto'\n        this.$menu.css('min-width', '');\n        this.$newElement.css('width', this.options.width);\n      } else {\n        // Remove inline min-width/width so width can be changed\n        this.$menu.css('min-width', '');\n        this.$newElement.css('width', '');\n      }\n      // Remove fit-width class if width is changed programmatically\n      if (this.$newElement.hasClass('fit-width') && this.options.width !== 'fit') {\n        this.$newElement[0].classList.remove('fit-width');\n      }\n    },\n\n    selectPosition: function () {\n      this.$bsContainer = $('<div class=\"bs-container\" />');\n\n      var that = this,\n          $container = $(this.options.container),\n          pos,\n          containerPos,\n          actualHeight,\n          getPlacement = function ($element) {\n            var containerPosition = {},\n                // fall back to dropdown's default display setting if display is not manually set\n                display = that.options.display || (\n                  // Bootstrap 3 doesn't have $.fn.dropdown.Constructor.Default\n                  $.fn.dropdown.Constructor.Default ? $.fn.dropdown.Constructor.Default.display\n                  : false\n                );\n\n            that.$bsContainer.addClass($element.attr('class').replace(/form-control|fit-width/gi, '')).toggleClass(classNames.DROPUP, $element.hasClass(classNames.DROPUP));\n            pos = $element.offset();\n\n            if (!$container.is('body')) {\n              containerPos = $container.offset();\n              containerPos.top += parseInt($container.css('borderTopWidth')) - $container.scrollTop();\n              containerPos.left += parseInt($container.css('borderLeftWidth')) - $container.scrollLeft();\n            } else {\n              containerPos = { top: 0, left: 0 };\n            }\n\n            actualHeight = $element.hasClass(classNames.DROPUP) ? 0 : $element[0].offsetHeight;\n\n            // Bootstrap 4+ uses Popper for menu positioning\n            if (version.major < 4 || display === 'static') {\n              containerPosition.top = pos.top - containerPos.top + actualHeight;\n              containerPosition.left = pos.left - containerPos.left;\n            }\n\n            containerPosition.width = $element[0].offsetWidth;\n\n            that.$bsContainer.css(containerPosition);\n          };\n\n      this.$button.on('click.bs.dropdown.data-api', function () {\n        if (that.isDisabled()) {\n          return;\n        }\n\n        getPlacement(that.$newElement);\n\n        that.$bsContainer\n          .appendTo(that.options.container)\n          .toggleClass(classNames.SHOW, !that.$button.hasClass(classNames.SHOW))\n          .append(that.$menu);\n      });\n\n      $(window)\n        .off('resize' + EVENT_KEY + '.' + this.selectId + ' scroll' + EVENT_KEY + '.' + this.selectId)\n        .on('resize' + EVENT_KEY + '.' + this.selectId + ' scroll' + EVENT_KEY + '.' + this.selectId, function () {\n          var isActive = that.$newElement.hasClass(classNames.SHOW);\n\n          if (isActive) getPlacement(that.$newElement);\n        });\n\n      this.$element.on('hide' + EVENT_KEY, function () {\n        that.$menu.data('height', that.$menu.height());\n        that.$bsContainer.detach();\n      });\n    },\n\n    createOption: function (data, init) {\n      var optionData = !data.option ? data : data.option;\n\n      if (optionData && optionData.nodeType !== 1) {\n        var option = (init ? elementTemplates.selectedOption : elementTemplates.option).cloneNode(true);\n        if (optionData.value !== undefined) option.value = optionData.value;\n        option.textContent = optionData.text;\n\n        option.selected = true;\n\n        if (optionData.liIndex !== undefined) {\n          option.liIndex = optionData.liIndex;\n        } else if (!init) {\n          option.liIndex = data.index;\n        }\n\n        data.option = option;\n\n        this.selectpicker.main.optionQueue.appendChild(option);\n      }\n    },\n\n    setOptionStatus: function (selectedOnly) {\n      var that = this;\n\n      that.noScroll = false;\n\n      if (that.selectpicker.view.visibleElements && that.selectpicker.view.visibleElements.length) {\n        for (var i = 0; i < that.selectpicker.view.visibleElements.length; i++) {\n          var liData = that.selectpicker.current.data[i + that.selectpicker.view.position0],\n              option = liData.option;\n\n          if (option) {\n            if (selectedOnly !== true) {\n              that.setDisabled(liData);\n            }\n\n            that.setSelected(liData);\n          }\n        }\n\n        // append optionQueue (documentFragment with option elements for select options)\n        if (this.options.source.data) this.$element[0].appendChild(this.selectpicker.main.optionQueue);\n      }\n    },\n\n    /**\n     * @param {Object} liData - the option object that is being changed\n     * @param {boolean} selected - true if the option is being selected, false if being deselected\n     */\n    setSelected: function (liData, selected) {\n      selected = selected === undefined ? liData.selected : selected;\n\n      var li = liData.element,\n          activeElementIsSet = this.activeElement !== undefined,\n          thisIsActive = this.activeElement === li,\n          prevActive,\n          a,\n          // if current option is already active\n          // OR\n          // if the current option is being selected, it's NOT multiple, and\n          // activeElement is undefined:\n          //  - when the menu is first being opened, OR\n          //  - after a search has been performed, OR\n          //  - when retainActive is false when selecting a new option (i.e. index of the newly selected option is not the same as the current activeElement)\n          keepActive = thisIsActive || (selected && !this.multiple && !activeElementIsSet);\n\n      if (!li) return;\n\n      if (selected !== undefined) {\n        liData.selected = selected;\n        if (liData.option) liData.option.selected = selected;\n      }\n\n      if (selected && this.options.source.data) {\n        this.createOption(liData, false);\n      }\n\n      a = li.firstChild;\n\n      if (selected) {\n        this.selectedElement = li;\n      }\n\n      li.classList.toggle('selected', selected);\n\n      if (keepActive) {\n        this.focusItem(li, liData);\n        this.selectpicker.view.currentActive = li;\n        this.activeElement = li;\n      } else {\n        this.defocusItem(li);\n      }\n\n      if (a) {\n        a.classList.toggle('selected', selected);\n\n        if (selected) {\n          a.setAttribute('aria-selected', true);\n        } else {\n          if (this.multiple) {\n            a.setAttribute('aria-selected', false);\n          } else {\n            a.removeAttribute('aria-selected');\n          }\n        }\n      }\n\n      if (!keepActive && !activeElementIsSet && selected && this.prevActiveElement !== undefined) {\n        prevActive = this.prevActiveElement;\n\n        this.defocusItem(prevActive);\n      }\n    },\n\n    /**\n     * @param {number} index - the index of the option that is being disabled\n     * @param {boolean} disabled - true if the option is being disabled, false if being enabled\n     */\n    setDisabled: function (liData) {\n      var disabled = liData.disabled,\n          li = liData.element,\n          a;\n\n      if (!li) return;\n\n      a = li.firstChild;\n\n      li.classList.toggle(classNames.DISABLED, disabled);\n\n      if (a) {\n        if (version.major >= '4') a.classList.toggle(classNames.DISABLED, disabled);\n\n        if (disabled) {\n          a.setAttribute('aria-disabled', disabled);\n          a.setAttribute('tabindex', -1);\n        } else {\n          a.removeAttribute('aria-disabled');\n          a.setAttribute('tabindex', 0);\n        }\n      }\n    },\n\n    isDisabled: function () {\n      return this.$element[0].disabled;\n    },\n\n    checkDisabled: function () {\n      if (this.isDisabled()) {\n        this.$newElement[0].classList.add(classNames.DISABLED);\n        this.$button.addClass(classNames.DISABLED).attr('aria-disabled', true);\n      } else {\n        if (this.$button[0].classList.contains(classNames.DISABLED)) {\n          this.$newElement[0].classList.remove(classNames.DISABLED);\n          this.$button.removeClass(classNames.DISABLED).attr('aria-disabled', false);\n        }\n      }\n    },\n\n    clickListener: function () {\n      var that = this,\n          $document = $(document);\n\n      $document.data('spaceSelect', false);\n\n      this.$button.on('keyup', function (e) {\n        if (/(32)/.test(e.keyCode.toString(10)) && $document.data('spaceSelect')) {\n          e.preventDefault();\n          $document.data('spaceSelect', false);\n        }\n      });\n\n      this.$newElement.on('show.bs.dropdown', function () {\n        if (!that.dropdown && version.major === '4') {\n          that.dropdown = that.$button.data('bs.dropdown');\n          that.dropdown._menu = that.$menu[0];\n        }\n      });\n\n      function clearSelection (e) {\n        if (that.multiple) {\n          that.deselectAll();\n        } else {\n          var element = that.$element[0],\n              prevValue = element.value,\n              prevIndex = element.selectedIndex,\n              prevOption = element.options[prevIndex],\n              prevData = prevOption ? that.selectpicker.main.data[prevOption.liIndex] : false;\n\n          if (prevData) {\n            that.setSelected(prevData, false);\n          }\n\n          element.selectedIndex = 0;\n\n          changedArguments = [prevIndex, false, prevValue];\n          that.$element.triggerNative('change');\n        }\n\n        // remove selected styling if menu is open\n        if (that.$newElement.hasClass(classNames.SHOW)) {\n          if (that.options.liveSearch) {\n            that.$searchbox.trigger('focus');\n          }\n\n          that.createView(false);\n        }\n      }\n\n      this.$button.on('click.bs.dropdown.data-api', function (e) {\n        if (that.options.allowClear) {\n          var target = e.target,\n              clearButton = that.$clearButton[0];\n\n          // IE doesn't support event listeners on child elements of buttons\n          if (/MSIE|Trident/.test(window.navigator.userAgent)) {\n            target = document.elementFromPoint(e.clientX, e.clientY);\n          }\n\n          if (target === clearButton || target.parentElement === clearButton) {\n            e.stopImmediatePropagation();\n            clearSelection(e);\n          }\n        }\n\n        if (!that.$newElement.hasClass(classNames.SHOW)) {\n          that.setSize();\n        }\n      });\n\n      function setFocus () {\n        if (that.options.liveSearch) {\n          that.$searchbox.trigger('focus');\n        } else {\n          that.$menuInner.trigger('focus');\n        }\n      }\n\n      function checkPopperExists () {\n        if (that.dropdown && that.dropdown._popper && that.dropdown._popper.state) {\n          setFocus();\n        } else {\n          requestAnimationFrame(checkPopperExists);\n        }\n      }\n\n      this.$element.on('shown' + EVENT_KEY, function () {\n        if (that.$menuInner[0].scrollTop !== that.selectpicker.view.scrollTop) {\n          that.$menuInner[0].scrollTop = that.selectpicker.view.scrollTop;\n        }\n\n        if (version.major > 3) {\n          requestAnimationFrame(checkPopperExists);\n        } else {\n          setFocus();\n        }\n      });\n\n      // ensure posinset and setsize are correct before selecting an option via a click\n      this.$menuInner.on('mouseenter', 'li a', function (e) {\n        var hoverLi = this.parentElement,\n            position0 = that.isVirtual() ? that.selectpicker.view.position0 : 0,\n            index = Array.prototype.indexOf.call(hoverLi.parentElement.children, hoverLi),\n            hoverData = that.selectpicker.current.data[index + position0];\n\n        that.focusItem(hoverLi, hoverData, true);\n      });\n\n      this.$menuInner.on('click', 'li a', function (e, retainActive) {\n        var $this = $(this),\n            element = that.$element[0],\n            position0 = that.isVirtual() ? that.selectpicker.view.position0 : 0,\n            clickedData = that.selectpicker.current.data[$this.parent().index() + position0],\n            clickedElement = clickedData.element,\n            prevValue = getSelectValues.call(that),\n            prevIndex = element.selectedIndex,\n            prevOption = element.options[prevIndex],\n            prevData = prevOption ? that.selectpicker.main.data[prevOption.liIndex] : false,\n            triggerChange = true;\n\n        // Don't close on multi choice menu\n        if (that.multiple && that.options.maxOptions !== 1) {\n          e.stopPropagation();\n        }\n\n        e.preventDefault();\n\n        // Don't run if the select is disabled\n        if (!that.isDisabled() && !$this.parent().hasClass(classNames.DISABLED)) {\n          var option = clickedData.option,\n              $option = $(option),\n              state = option.selected,\n              optgroupData = that.selectpicker.current.data.find(function (datum) {\n                return datum.optID === clickedData.optID && datum.type === 'optgroup-label';\n              }),\n              optgroup = optgroupData ? optgroupData.optgroup : undefined,\n              dataGetter = optgroup instanceof Element ? getOptionData.fromOption : getOptionData.fromDataSource,\n              optgroupOptions = optgroup && optgroup.children,\n              maxOptions = parseInt(that.options.maxOptions),\n              maxOptionsGrp = optgroup && parseInt(dataGetter(optgroup, 'maxOptions')) || false;\n\n          if (clickedElement === that.activeElement) retainActive = true;\n\n          if (!retainActive) {\n            that.prevActiveElement = that.activeElement;\n            that.activeElement = undefined;\n          }\n\n          if (!that.multiple || maxOptions === 1) { // Deselect previous option if not multi select\n            if (prevData) that.setSelected(prevData, false);\n            that.setSelected(clickedData, true);\n          } else { // Toggle the clicked option if multi select.\n            that.setSelected(clickedData, !state);\n            that.focusedParent.focus();\n\n            if (maxOptions !== false || maxOptionsGrp !== false) {\n              var maxReached = maxOptions < getSelectedOptions.call(that).length,\n                  selectedGroupOptions = 0;\n\n              if (optgroup && optgroup.children) {\n                for (var i = 0; i < optgroup.children.length; i++) {\n                  if (optgroup.children[i].selected) selectedGroupOptions++;\n                }\n              }\n\n              var maxReachedGrp = maxOptionsGrp < selectedGroupOptions;\n\n              if ((maxOptions && maxReached) || (maxOptionsGrp && maxReachedGrp)) {\n                if (maxOptions && maxOptions === 1) {\n                  element.selectedIndex = -1;\n                  that.setOptionStatus(true);\n                } else if (maxOptionsGrp && maxOptionsGrp === 1) {\n                  for (var i = 0; i < optgroupOptions.length; i++) {\n                    var _option = optgroupOptions[i];\n                    that.setSelected(that.selectpicker.current.data[_option.liIndex], false);\n                  }\n\n                  that.setSelected(clickedData, true);\n                } else {\n                  var maxOptionsText = typeof that.options.maxOptionsText === 'string' ? [that.options.maxOptionsText, that.options.maxOptionsText] : that.options.maxOptionsText,\n                      maxOptionsArr = typeof maxOptionsText === 'function' ? maxOptionsText(maxOptions, maxOptionsGrp) : maxOptionsText,\n                      maxTxt = maxOptionsArr[0].replace('{n}', maxOptions),\n                      maxTxtGrp = maxOptionsArr[1].replace('{n}', maxOptionsGrp),\n                      $notify = $('<div class=\"notify\"></div>');\n                  // If {var} is set in array, replace it\n                  /** @deprecated */\n                  if (maxOptionsArr[2]) {\n                    maxTxt = maxTxt.replace('{var}', maxOptionsArr[2][maxOptions > 1 ? 0 : 1]);\n                    maxTxtGrp = maxTxtGrp.replace('{var}', maxOptionsArr[2][maxOptionsGrp > 1 ? 0 : 1]);\n                  }\n\n                  that.$menu.append($notify);\n\n                  if (maxOptions && maxReached) {\n                    $notify.append($('<div>' + maxTxt + '</div>'));\n                    triggerChange = false;\n                    that.$element.trigger('maxReached' + EVENT_KEY);\n                  }\n\n                  if (maxOptionsGrp && maxReachedGrp) {\n                    $notify.append($('<div>' + maxTxtGrp + '</div>'));\n                    triggerChange = false;\n                    that.$element.trigger('maxReachedGrp' + EVENT_KEY);\n                  }\n\n                  setTimeout(function () {\n                    that.setSelected(clickedData, false);\n                  }, 10);\n\n                  $notify[0].classList.add('fadeOut');\n\n                  setTimeout(function () {\n                    $notify.remove();\n                  }, 1050);\n                }\n              }\n            }\n          }\n\n          if (that.options.source.data) that.$element[0].appendChild(that.selectpicker.main.optionQueue);\n\n          if (!that.multiple || (that.multiple && that.options.maxOptions === 1)) {\n            that.$button.trigger('focus');\n          } else if (that.options.liveSearch) {\n            that.$searchbox.trigger('focus');\n          }\n\n          // Trigger select 'change'\n          if (triggerChange) {\n            if (that.multiple || prevIndex !== element.selectedIndex) {\n              // $option.prop('selected') is current option state (selected/unselected). prevValue is the value of the select prior to being changed.\n              changedArguments = [option.index, $option.prop('selected'), prevValue];\n              that.$element\n                .triggerNative('change');\n            }\n          }\n        }\n      });\n\n      this.$menu.on('click', 'li.' + classNames.DISABLED + ' a, .' + classNames.POPOVERHEADER + ', .' + classNames.POPOVERHEADER + ' :not(.close)', function (e) {\n        if (e.currentTarget == this) {\n          e.preventDefault();\n          e.stopPropagation();\n          if (that.options.liveSearch && !$(e.target).hasClass('close')) {\n            that.$searchbox.trigger('focus');\n          } else {\n            that.$button.trigger('focus');\n          }\n        }\n      });\n\n      this.$menuInner.on('click', '.divider, .dropdown-header', function (e) {\n        e.preventDefault();\n        e.stopPropagation();\n        if (that.options.liveSearch) {\n          that.$searchbox.trigger('focus');\n        } else {\n          that.$button.trigger('focus');\n        }\n      });\n\n      this.$menu.on('click', '.' + classNames.POPOVERHEADER + ' .close', function () {\n        that.$button.trigger('click');\n      });\n\n      this.$searchbox.on('click', function (e) {\n        e.stopPropagation();\n      });\n\n      this.$menu.on('click', '.actions-btn', function (e) {\n        if (that.options.liveSearch) {\n          that.$searchbox.trigger('focus');\n        } else {\n          that.$button.trigger('focus');\n        }\n\n        e.preventDefault();\n        e.stopPropagation();\n\n        if ($(this).hasClass('bs-select-all')) {\n          that.selectAll();\n        } else {\n          that.deselectAll();\n        }\n      });\n\n      this.$button\n        .on('focus' + EVENT_KEY, function (e) {\n          var tabindex = that.$element[0].getAttribute('tabindex');\n\n          // only change when button is actually focused\n          if (tabindex !== undefined && e.originalEvent && e.originalEvent.isTrusted) {\n            // apply select element's tabindex to ensure correct order is followed when tabbing to the next element\n            this.setAttribute('tabindex', tabindex);\n            // set element's tabindex to -1 to allow for reverse tabbing\n            that.$element[0].setAttribute('tabindex', -1);\n            that.selectpicker.view.tabindex = tabindex;\n          }\n        })\n        .on('blur' + EVENT_KEY, function (e) {\n          // revert everything to original tabindex\n          if (that.selectpicker.view.tabindex !== undefined && e.originalEvent && e.originalEvent.isTrusted) {\n            that.$element[0].setAttribute('tabindex', that.selectpicker.view.tabindex);\n            this.setAttribute('tabindex', -1);\n            that.selectpicker.view.tabindex = undefined;\n          }\n        });\n\n      this.$element\n        .on('change' + EVENT_KEY, function () {\n          that.render();\n          that.$element.trigger('changed' + EVENT_KEY, changedArguments);\n          changedArguments = null;\n        })\n        .on('focus' + EVENT_KEY, function () {\n          if (!that.options.mobile) that.$button[0].focus();\n        });\n    },\n\n    liveSearchListener: function () {\n      var that = this;\n\n      this.$button.on('click.bs.dropdown.data-api', function () {\n        if (!!that.$searchbox.val()) {\n          that.$searchbox.val('');\n          that.selectpicker.search.previousValue = undefined;\n        }\n      });\n\n      this.$searchbox.on('click.bs.dropdown.data-api focus.bs.dropdown.data-api touchend.bs.dropdown.data-api', function (e) {\n        e.stopPropagation();\n      });\n\n      this.$searchbox.on('input propertychange', function () {\n        var searchValue = that.$searchbox[0].value;\n\n        that.selectpicker.search.elements = [];\n        that.selectpicker.search.data = [];\n\n        if (searchValue) {\n          that.selectpicker.search.previousValue = searchValue;\n\n          if (that.options.source.search) {\n            that.fetchData(function (builtData) {\n              that.render();\n              that.buildList(undefined, true);\n              that.noScroll = true;\n              that.$menuInner.scrollTop(0);\n              that.createView(true);\n              showNoResults.call(that, builtData, searchValue);\n            }, 'search', 0, searchValue);\n          } else {\n            var i,\n                searchMatch = [],\n                q = searchValue.toUpperCase(),\n                cache = {},\n                cacheArr = [],\n                searchStyle = that._searchStyle(),\n                normalizeSearch = that.options.liveSearchNormalize;\n\n            if (normalizeSearch) q = normalizeToBase(q);\n\n            for (var i = 0; i < that.selectpicker.main.data.length; i++) {\n              var li = that.selectpicker.main.data[i];\n\n              if (!cache[i]) {\n                cache[i] = stringSearch(li, q, searchStyle, normalizeSearch);\n              }\n\n              if (cache[i] && li.headerIndex !== undefined && cacheArr.indexOf(li.headerIndex) === -1) {\n                if (li.headerIndex > 0) {\n                  cache[li.headerIndex - 1] = true;\n                  cacheArr.push(li.headerIndex - 1);\n                }\n\n                cache[li.headerIndex] = true;\n                cacheArr.push(li.headerIndex);\n\n                cache[li.lastIndex + 1] = true;\n              }\n\n              if (cache[i] && li.type !== 'optgroup-label') cacheArr.push(i);\n            }\n\n            for (var i = 0, cacheLen = cacheArr.length; i < cacheLen; i++) {\n              var index = cacheArr[i],\n                  prevIndex = cacheArr[i - 1],\n                  li = that.selectpicker.main.data[index],\n                  liPrev = that.selectpicker.main.data[prevIndex];\n\n              if (li.type !== 'divider' || (li.type === 'divider' && liPrev && liPrev.type !== 'divider' && cacheLen - 1 !== i)) {\n                that.selectpicker.search.data.push(li);\n                searchMatch.push(that.selectpicker.main.elements[index]);\n              }\n            }\n\n            that.activeElement = undefined;\n            that.noScroll = true;\n            that.$menuInner.scrollTop(0);\n            that.selectpicker.search.elements = searchMatch;\n            that.createView(true);\n            showNoResults.call(that, searchMatch, searchValue);\n          }\n        } else if (that.selectpicker.search.previousValue) { // for IE11 (#2402)\n          that.$menuInner.scrollTop(0);\n          that.createView(false);\n        }\n      });\n    },\n\n    _searchStyle: function () {\n      return this.options.liveSearchStyle || 'contains';\n    },\n\n    val: function (value) {\n      var element = this.$element[0];\n\n      if (typeof value !== 'undefined') {\n        var selectedOptions = getSelectedOptions.call(this),\n            prevValue = getSelectValues.call(this, selectedOptions);\n\n        changedArguments = [null, null, prevValue];\n\n        if (!Array.isArray(value)) value = [ value ];\n\n        value.map(String);\n\n        for (var i = 0; i < selectedOptions.length; i++) {\n          var item = selectedOptions[i];\n\n          if (item && value.indexOf(String(item.value)) === -1) {\n            this.setSelected(item, false);\n          }\n        }\n\n        // only update selected value if it matches an existing option\n        this.selectpicker.main.data.filter(function (item) {\n          if (value.indexOf(String(item.value)) !== -1) {\n            this.setSelected(item, true);\n            return true;\n          }\n\n          return false;\n        }, this);\n\n        if (this.options.source.data) element.appendChild(this.selectpicker.main.optionQueue);\n\n        this.$element.trigger('changed' + EVENT_KEY, changedArguments);\n\n        if (this.$newElement.hasClass(classNames.SHOW)) {\n          if (this.multiple) {\n            this.setOptionStatus(true);\n          } else {\n            var liSelectedIndex = (element.options[element.selectedIndex] || {}).liIndex;\n\n            if (typeof liSelectedIndex === 'number') {\n              this.setSelected(this.selectpicker.current.data[liSelectedIndex], true);\n            }\n          }\n        }\n\n        this.render();\n\n        changedArguments = null;\n\n        return this.$element;\n      } else {\n        return this.$element.val();\n      }\n    },\n\n    changeAll: function (status) {\n      if (!this.multiple) return;\n      if (typeof status === 'undefined') status = true;\n\n      var element = this.$element[0],\n          previousSelected = 0,\n          currentSelected = 0,\n          prevValue = getSelectValues.call(this);\n\n      element.classList.add('bs-select-hidden');\n\n      for (var i = 0, data = this.selectpicker.current.data, len = data.length; i < len; i++) {\n        var liData = data[i],\n            option = liData.option;\n\n        if (option && !liData.disabled && liData.type !== 'divider') {\n          if (liData.selected) previousSelected++;\n          option.selected = status;\n          liData.selected = status;\n          if (status === true) currentSelected++;\n        }\n      }\n\n      element.classList.remove('bs-select-hidden');\n\n      if (previousSelected === currentSelected) return;\n\n      this.setOptionStatus();\n\n      changedArguments = [null, null, prevValue];\n\n      this.$element\n        .triggerNative('change');\n    },\n\n    selectAll: function () {\n      return this.changeAll(true);\n    },\n\n    deselectAll: function () {\n      return this.changeAll(false);\n    },\n\n    toggle: function (e, state) {\n      var isActive,\n          triggerClick = state === undefined;\n\n      e = e || window.event;\n\n      if (e) e.stopPropagation();\n\n      if (triggerClick === false) {\n        isActive = this.$newElement[0].classList.contains(classNames.SHOW);\n        triggerClick = state === true && isActive === false || state === false && isActive === true;\n      }\n\n      if (triggerClick) this.$button.trigger('click.bs.dropdown.data-api');\n    },\n\n    open: function (e) {\n      this.toggle(e, true);\n    },\n\n    close: function (e) {\n      this.toggle(e, false);\n    },\n\n    keydown: function (e) {\n      var $this = $(this),\n          isToggle = $this.hasClass('dropdown-toggle'),\n          $parent = isToggle ? $this.closest('.dropdown') : $this.closest(Selector.MENU),\n          that = $parent.data('this'),\n          $items = that.findLis(),\n          index,\n          isActive,\n          liActive,\n          activeLi,\n          offset,\n          updateScroll = false,\n          downOnTab = e.which === keyCodes.TAB && !isToggle && !that.options.selectOnTab,\n          isArrowKey = REGEXP_ARROW.test(e.which) || downOnTab,\n          scrollTop = that.$menuInner[0].scrollTop,\n          isVirtual = that.isVirtual(),\n          position0 = isVirtual === true ? that.selectpicker.view.position0 : 0;\n\n      // do nothing if a function key is pressed\n      if (e.which >= 112 && e.which <= 123) return;\n\n      isActive = that.$menu.hasClass(classNames.SHOW);\n\n      if (\n        !isActive &&\n        (\n          isArrowKey ||\n          (e.which >= 48 && e.which <= 57) ||\n          (e.which >= 96 && e.which <= 105) ||\n          (e.which >= 65 && e.which <= 90)\n        )\n      ) {\n        that.$button.trigger('click.bs.dropdown.data-api');\n\n        if (that.options.liveSearch) {\n          that.$searchbox.trigger('focus');\n          return;\n        }\n      }\n\n      if (e.which === keyCodes.ESCAPE && isActive) {\n        e.preventDefault();\n        that.$button.trigger('click.bs.dropdown.data-api').trigger('focus');\n      }\n\n      if (isArrowKey) { // if up or down\n        if (!$items.length) return;\n\n        liActive = that.activeElement;\n        index = liActive ? Array.prototype.indexOf.call(liActive.parentElement.children, liActive) : -1;\n\n        if (index !== -1) {\n          that.defocusItem(liActive);\n        }\n\n        if (e.which === keyCodes.ARROW_UP) { // up\n          if (index !== -1) index--;\n          if (index + position0 < 0) index += $items.length;\n\n          if (!that.selectpicker.view.canHighlight[index + position0]) {\n            index = that.selectpicker.view.canHighlight.slice(0, index + position0).lastIndexOf(true) - position0;\n            if (index === -1) index = $items.length - 1;\n          }\n        } else if (e.which === keyCodes.ARROW_DOWN || downOnTab) { // down\n          index++;\n          if (index + position0 >= that.selectpicker.view.canHighlight.length) index = that.selectpicker.view.firstHighlightIndex;\n\n          if (!that.selectpicker.view.canHighlight[index + position0]) {\n            index = index + 1 + that.selectpicker.view.canHighlight.slice(index + position0 + 1).indexOf(true);\n          }\n        }\n\n        e.preventDefault();\n\n        var liActiveIndex = position0 + index;\n\n        if (e.which === keyCodes.ARROW_UP) { // up\n          // scroll to bottom and highlight last option\n          if (position0 === 0 && index === $items.length - 1) {\n            that.$menuInner[0].scrollTop = that.$menuInner[0].scrollHeight;\n\n            liActiveIndex = that.selectpicker.current.elements.length - 1;\n          } else {\n            activeLi = that.selectpicker.current.data[liActiveIndex];\n\n            // could be undefined if no results exist\n            if (activeLi) {\n              offset = activeLi.position - activeLi.height;\n\n              updateScroll = offset < scrollTop;\n            }\n          }\n        } else if (e.which === keyCodes.ARROW_DOWN || downOnTab) { // down\n          // scroll to top and highlight first option\n          if (index === that.selectpicker.view.firstHighlightIndex) {\n            that.$menuInner[0].scrollTop = 0;\n\n            liActiveIndex = that.selectpicker.view.firstHighlightIndex;\n          } else {\n            activeLi = that.selectpicker.current.data[liActiveIndex];\n\n            // could be undefined if no results exist\n            if (activeLi) {\n              offset = activeLi.position - that.sizeInfo.menuInnerHeight;\n\n              updateScroll = offset > scrollTop;\n            }\n          }\n        }\n\n        liActive = that.selectpicker.current.elements[liActiveIndex];\n\n        that.activeElement = (that.selectpicker.current.data[liActiveIndex] || {}).element;\n\n        that.focusItem(liActive);\n\n        that.selectpicker.view.currentActive = liActive;\n\n        if (updateScroll) that.$menuInner[0].scrollTop = offset;\n\n        if (that.options.liveSearch) {\n          that.$searchbox.trigger('focus');\n        } else {\n          $this.trigger('focus');\n        }\n      } else if (\n        (!$this.is('input') && !REGEXP_TAB_OR_ESCAPE.test(e.which)) ||\n        (e.which === keyCodes.SPACE && that.selectpicker.keydown.keyHistory)\n      ) {\n        var searchMatch,\n            matches = [],\n            keyHistory;\n\n        e.preventDefault();\n\n        that.selectpicker.keydown.keyHistory += keyCodeMap[e.which];\n\n        if (that.selectpicker.keydown.resetKeyHistory.cancel) clearTimeout(that.selectpicker.keydown.resetKeyHistory.cancel);\n        that.selectpicker.keydown.resetKeyHistory.cancel = that.selectpicker.keydown.resetKeyHistory.start();\n\n        keyHistory = that.selectpicker.keydown.keyHistory;\n\n        // if all letters are the same, set keyHistory to just the first character when searching\n        if (/^(.)\\1+$/.test(keyHistory)) {\n          keyHistory = keyHistory.charAt(0);\n        }\n\n        // find matches\n        for (var i = 0; i < that.selectpicker.current.data.length; i++) {\n          var li = that.selectpicker.current.data[i],\n              hasMatch;\n\n          hasMatch = stringSearch(li, keyHistory, 'startsWith', true);\n\n          if (hasMatch && that.selectpicker.view.canHighlight[i]) {\n            matches.push(li.element);\n          }\n        }\n\n        if (matches.length) {\n          var matchIndex = 0;\n\n          $items.removeClass('active').find('a').removeClass('active');\n\n          // either only one key has been pressed or they are all the same key\n          if (keyHistory.length === 1) {\n            matchIndex = matches.indexOf(that.activeElement);\n\n            if (matchIndex === -1 || matchIndex === matches.length - 1) {\n              matchIndex = 0;\n            } else {\n              matchIndex++;\n            }\n          }\n\n          searchMatch = matches[matchIndex];\n\n          activeLi = that.selectpicker.main.data[searchMatch];\n\n          if (scrollTop - activeLi.position > 0) {\n            offset = activeLi.position - activeLi.height;\n            updateScroll = true;\n          } else {\n            offset = activeLi.position - that.sizeInfo.menuInnerHeight;\n            // if the option is already visible at the current scroll position, just keep it the same\n            updateScroll = activeLi.position > scrollTop + that.sizeInfo.menuInnerHeight;\n          }\n\n          liActive = that.selectpicker.main.elements[searchMatch];\n\n          that.activeElement = liActive;\n\n          that.focusItem(liActive);\n\n          if (liActive) liActive.firstChild.focus();\n\n          if (updateScroll) that.$menuInner[0].scrollTop = offset;\n\n          $this.trigger('focus');\n        }\n      }\n\n      // Select focused option if \"Enter\", \"Spacebar\" or \"Tab\" (when selectOnTab is true) are pressed inside the menu.\n      if (\n        isActive &&\n        (\n          (e.which === keyCodes.SPACE && !that.selectpicker.keydown.keyHistory) ||\n          e.which === keyCodes.ENTER ||\n          (e.which === keyCodes.TAB && that.options.selectOnTab)\n        )\n      ) {\n        if (e.which !== keyCodes.SPACE) e.preventDefault();\n\n        if (!that.options.liveSearch || e.which !== keyCodes.SPACE) {\n          that.$menuInner.find('.active a').trigger('click', true); // retain active class\n          $this.trigger('focus');\n\n          if (!that.options.liveSearch) {\n            // Prevent screen from scrolling if the user hits the spacebar\n            e.preventDefault();\n            // Fixes spacebar selection of dropdown items in FF & IE\n            $(document).data('spaceSelect', true);\n          }\n        }\n      }\n    },\n\n    mobile: function () {\n      // ensure mobile is set to true if mobile function is called after init\n      this.options.mobile = true;\n      this.$element[0].classList.add('mobile-device');\n    },\n\n    refresh: function () {\n      var that = this;\n      // update options if data attributes have been changed\n      var config = $.extend({}, this.options, getAttributesObject(this.$element), this.$element.data()); // in this order on refresh, as user may change attributes on select, and options object is not passed on refresh\n      this.options = config;\n\n      if (this.options.source.data) {\n        this.render();\n        this.buildList();\n      } else {\n        this.fetchData(function () {\n          that.render();\n          that.buildList();\n        });\n      }\n\n      this.checkDisabled();\n      this.setStyle();\n      this.setWidth();\n\n      this.setSize(true);\n\n      this.$element.trigger('refreshed' + EVENT_KEY);\n    },\n\n    hide: function () {\n      this.$newElement.hide();\n    },\n\n    show: function () {\n      this.$newElement.show();\n    },\n\n    remove: function () {\n      this.$newElement.remove();\n      this.$element.remove();\n    },\n\n    destroy: function () {\n      this.$newElement.before(this.$element).remove();\n\n      if (this.$bsContainer) {\n        this.$bsContainer.remove();\n      } else {\n        this.$menu.remove();\n      }\n\n      if (this.selectpicker.view.titleOption && this.selectpicker.view.titleOption.parentNode) {\n        this.selectpicker.view.titleOption.parentNode.removeChild(this.selectpicker.view.titleOption);\n      }\n\n      this.$element\n        .off(EVENT_KEY)\n        .removeData('selectpicker')\n        .removeClass('bs-select-hidden selectpicker mobile-device');\n\n      $(window).off(EVENT_KEY + '.' + this.selectId);\n    }\n  };\n\n  // SELECTPICKER PLUGIN DEFINITION\n  // ==============================\n  function Plugin (option) {\n    // get the args of the outer function..\n    var args = arguments;\n    // The arguments of the function are explicitly re-defined from the argument list, because the shift causes them\n    // to get lost/corrupted in android 2.3 and IE9 #715 #775\n    var _option = option;\n\n    [].shift.apply(args);\n\n    // if the version was not set successfully\n    if (!version.success) {\n      // try to retreive it again\n      try {\n        version.full = (getVersion() || '').split(' ')[0].split('.');\n      } catch (err) {\n        // fall back to use BootstrapVersion if set\n        if (Selectpicker.BootstrapVersion) {\n          version.full = Selectpicker.BootstrapVersion.split(' ')[0].split('.');\n        } else {\n          version.full = [version.major, '0', '0'];\n\n          console.warn(\n            'There was an issue retrieving Bootstrap\\'s version. ' +\n            'Ensure Bootstrap is being loaded before bootstrap-select and there is no namespace collision. ' +\n            'If loading Bootstrap asynchronously, the version may need to be manually specified via $.fn.selectpicker.Constructor.BootstrapVersion.',\n            err\n          );\n        }\n      }\n\n      version.major = version.full[0];\n      version.success = true;\n    }\n\n    if (version.major >= '4') {\n      // some defaults need to be changed if using Bootstrap 4\n      // check to see if they have already been manually changed before forcing them to update\n      var toUpdate = [];\n\n      if (Selectpicker.DEFAULTS.style === classNames.BUTTONCLASS) toUpdate.push({ name: 'style', className: 'BUTTONCLASS' });\n      if (Selectpicker.DEFAULTS.iconBase === classNames.ICONBASE) toUpdate.push({ name: 'iconBase', className: 'ICONBASE' });\n      if (Selectpicker.DEFAULTS.tickIcon === classNames.TICKICON) toUpdate.push({ name: 'tickIcon', className: 'TICKICON' });\n\n      classNames.DIVIDER = 'dropdown-divider';\n      classNames.SHOW = 'show';\n      classNames.BUTTONCLASS = 'btn-light';\n      classNames.POPOVERHEADER = 'popover-header';\n      classNames.ICONBASE = '';\n      classNames.TICKICON = 'bs-ok-default';\n\n      for (var i = 0; i < toUpdate.length; i++) {\n        var option = toUpdate[i];\n        Selectpicker.DEFAULTS[option.name] = classNames[option.className];\n      }\n    }\n\n    if (version.major > '4') {\n      Selector.DATA_TOGGLE = 'data-bs-toggle=\"dropdown\"';\n    }\n\n    var value;\n    var chain = this.each(function () {\n      var $this = $(this);\n      if ($this.is('select')) {\n        var data = $this.data('selectpicker'),\n            options = typeof _option == 'object' && _option;\n\n        // for backwards compatibility\n        // (using title as placeholder is deprecated - remove in v2.0.0)\n        if (options.title) options.placeholder = options.title;\n\n        if (!data) {\n          var dataAttributes = $this.data();\n\n          for (var dataAttr in dataAttributes) {\n            if (Object.prototype.hasOwnProperty.call(dataAttributes, dataAttr) && $.inArray(dataAttr, DISALLOWED_ATTRIBUTES) !== -1) {\n              delete dataAttributes[dataAttr];\n            }\n          }\n\n          var config = $.extend({}, Selectpicker.DEFAULTS, $.fn.selectpicker.defaults || {}, getAttributesObject($this), dataAttributes, options); // this is correct order on initial render\n          config.template = $.extend({}, Selectpicker.DEFAULTS.template, ($.fn.selectpicker.defaults ? $.fn.selectpicker.defaults.template : {}), dataAttributes.template, options.template);\n          config.source = $.extend({}, Selectpicker.DEFAULTS.source, ($.fn.selectpicker.defaults ? $.fn.selectpicker.defaults.source : {}), options.source);\n          $this.data('selectpicker', (data = new Selectpicker(this, config)));\n        } else if (options) {\n          for (var i in options) {\n            if (Object.prototype.hasOwnProperty.call(options, i)) {\n              data.options[i] = options[i];\n            }\n          }\n        }\n\n        if (typeof _option == 'string') {\n          if (data[_option] instanceof Function) {\n            value = data[_option].apply(data, args);\n          } else {\n            value = data.options[_option];\n          }\n        }\n      }\n    });\n\n    if (typeof value !== 'undefined') {\n      // noinspection JSUnusedAssignment\n      return value;\n    } else {\n      return chain;\n    }\n  }\n\n  var old = $.fn.selectpicker;\n  $.fn.selectpicker = Plugin;\n  $.fn.selectpicker.Constructor = Selectpicker;\n\n  // SELECTPICKER NO CONFLICT\n  // ========================\n  $.fn.selectpicker.noConflict = function () {\n    $.fn.selectpicker = old;\n    return this;\n  };\n\n  // get Bootstrap's keydown event handler for either Bootstrap 4 or Bootstrap 3\n  function keydownHandler () {\n    if (version.major < 5) {\n      if ($.fn.dropdown) {\n        // wait to define until function is called in case Bootstrap isn't loaded yet\n        var bootstrapKeydown = $.fn.dropdown.Constructor._dataApiKeydownHandler || $.fn.dropdown.Constructor.prototype.keydown;\n        return bootstrapKeydown.apply(this, arguments);\n      }\n    } else {\n      return Dropdown.dataApiKeydownHandler;\n    }\n  }\n\n  $(document)\n    .off('keydown.bs.dropdown.data-api')\n    .on('keydown.bs.dropdown.data-api', ':not(.bootstrap-select) > [' + Selector.DATA_TOGGLE + ']', keydownHandler)\n    .on('keydown.bs.dropdown.data-api', ':not(.bootstrap-select) > .dropdown-menu', keydownHandler)\n    .on('keydown' + EVENT_KEY, '.bootstrap-select [' + Selector.DATA_TOGGLE + '], .bootstrap-select [role=\"listbox\"], .bootstrap-select .bs-searchbox input', Selectpicker.prototype.keydown)\n    .on('focusin.modal', '.bootstrap-select [' + Selector.DATA_TOGGLE + '], .bootstrap-select [role=\"listbox\"], .bootstrap-select .bs-searchbox input', function (e) {\n      e.stopPropagation();\n    });\n\n  // SELECTPICKER DATA-API\n  // =====================\n  document.addEventListener('DOMContentLoaded', function () {\n    $('.selectpicker').each(function () {\n      var $selectpicker = $(this);\n      Plugin.call($selectpicker, $selectpicker.data());\n    });\n  });\n})(jQuery);\n\n\n//# sourceURL=webpack://Materialize/./node_modules/bootstrap-select/js/bootstrap-select.js?")}},__webpack_module_cache__={};function __webpack_require__(e){var n=__webpack_module_cache__[e];if(void 0!==n)return n.exports;var t=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(n,{a:n}),n},__webpack_require__.d=function(e,n){for(var t in n)__webpack_require__.o(n,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},__webpack_require__.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/bootstrap-select/bootstrap-select.js");return __webpack_exports__}()}));