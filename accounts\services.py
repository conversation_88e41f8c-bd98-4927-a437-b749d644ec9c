"""
Services pour la gestion des demandes d'inscription
"""
from django.contrib.auth.models import User
from django.contrib.auth.hashers import check_password
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from organisations.models import Organisation, MembreOrganisation
from .models import DemandeInscription, StatutDemande


class ValidationService:
    """Service pour valider ou rejeter les demandes d'inscription"""
    
    @staticmethod
    def valider_demande(demande: DemandeInscription, super_admin: User, commentaire: str = ""):
        """
        Valide une demande d'inscription et crée l'organisation + utilisateur admin
        
        Args:
            demande: La demande d'inscription à valider
            super_admin: L'utilisateur super admin qui valide
            commentaire: Commentaire optionnel
        """
        if demande.statut != StatutDemande.EN_ATTENTE:
            raise ValueError(f"Cette demande a déjà été traitée (statut: {demande.get_statut_display()})")
        
        try:
            # 1. Créer l'utilisateur administrateur
            utilisateur_admin = ValidationService._creer_utilisateur_admin(demande)
            
            # 2. Créer l'organisation
            organisation = ValidationService._creer_organisation(demande)
            
            # 3. Associer l'utilisateur à l'organisation comme admin
            ValidationService._creer_membre_organisation(utilisateur_admin, organisation)
            
            # 4. Mettre à jour la demande
            demande.statut = StatutDemande.VALIDEE
            demande.date_traitement = timezone.now()
            demande.traite_par = super_admin
            demande.commentaire_admin = commentaire
            demande.organisation_creee = organisation
            demande.utilisateur_cree = utilisateur_admin
            demande.save()
            
            # 5. Envoyer l'email de bienvenue
            ValidationService._envoyer_email_validation(demande, utilisateur_admin, organisation)
            
            return organisation, utilisateur_admin
            
        except Exception as e:
            # En cas d'erreur, nettoyer les objets créés
            if hasattr(demande, 'organisation_creee') and demande.organisation_creee:
                demande.organisation_creee.delete()
            if hasattr(demande, 'utilisateur_cree') and demande.utilisateur_cree:
                demande.utilisateur_cree.delete()
            raise e
    
    @staticmethod
    def rejeter_demande(demande: DemandeInscription, super_admin: User, commentaire: str):
        """
        Rejette une demande d'inscription
        
        Args:
            demande: La demande d'inscription à rejeter
            super_admin: L'utilisateur super admin qui rejette
            commentaire: Raison du rejet
        """
        if demande.statut != StatutDemande.EN_ATTENTE:
            raise ValueError(f"Cette demande a déjà été traitée (statut: {demande.get_statut_display()})")
        
        # Mettre à jour la demande
        demande.statut = StatutDemande.REJETEE
        demande.date_traitement = timezone.now()
        demande.traite_par = super_admin
        demande.commentaire_admin = commentaire
        demande.save()
        
        # Envoyer l'email de rejet
        ValidationService._envoyer_email_rejet(demande)
    
    @staticmethod
    def _creer_utilisateur_admin(demande: DemandeInscription) -> User:
        """Crée l'utilisateur administrateur de l'organisation"""
        
        # Vérifier que l'email n'est pas déjà utilisé
        if User.objects.filter(email=demande.email_admin).exists():
            raise ValueError(f"L'email {demande.email_admin} est déjà utilisé par un autre utilisateur")
        
        # Créer l'utilisateur
        utilisateur = User.objects.create_user(
            username=demande.email_admin,  # Utiliser l'email comme username
            email=demande.email_admin,
            first_name=demande.prenom_admin,
            last_name=demande.nom_admin,
            password=None  # Le mot de passe est déjà hashé dans la demande
        )
        
        # Copier le mot de passe hashé depuis la demande
        utilisateur.password = demande.mot_de_passe
        utilisateur.save()
        
        return utilisateur
    
    @staticmethod
    def _creer_organisation(demande: DemandeInscription) -> Organisation:
        """Crée l'organisation"""
        
        organisation = Organisation.objects.create(
            nom=demande.nom_organisation,
            adresse=demande.adresse_organisation,
            telephone=demande.telephone_organisation,
            email=demande.email_organisation,
            type_abonnement=demande.type_abonnement_demande,
            actif=True
        )
        
        return organisation
    
    @staticmethod
    def _creer_membre_organisation(utilisateur: User, organisation: Organisation):
        """Crée la relation membre organisation avec le rôle admin"""
        
        MembreOrganisation.objects.create(
            utilisateur=utilisateur,
            organisation=organisation,
            role=MembreOrganisation.RoleMembre.ADMIN,
            actif=True
        )
    
    @staticmethod
    def _envoyer_email_validation(demande: DemandeInscription, utilisateur: User, organisation: Organisation):
        """Envoie l'email de validation à l'administrateur"""
        
        try:
            subject = f"Bienvenue sur Hospital SaaS - Votre organisation '{organisation.nom}' est activée !"
            
            # Contexte pour le template
            context = {
                'demande': demande,
                'utilisateur': utilisateur,
                'organisation': organisation,
                'login_url': f"{settings.SITE_URL}/login/" if hasattr(settings, 'SITE_URL') else "http://localhost:8000/login/"
            }
            
            # Rendu du template HTML
            html_message = render_to_string('accounts/emails/validation.html', context)
            plain_message = strip_tags(html_message)
            
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                recipient_list=[demande.email_admin],
                html_message=html_message,
                fail_silently=False
            )
            
        except Exception as e:
            # Log l'erreur mais ne fait pas échouer la validation
            print(f"Erreur lors de l'envoi de l'email de validation: {e}")
    
    @staticmethod
    def _envoyer_email_rejet(demande: DemandeInscription):
        """Envoie l'email de rejet à l'administrateur"""
        
        try:
            subject = f"Demande d'inscription Hospital SaaS - '{demande.nom_organisation}'"
            
            # Contexte pour le template
            context = {
                'demande': demande,
                'contact_url': f"{settings.SITE_URL}/contact/" if hasattr(settings, 'SITE_URL') else "mailto:<EMAIL>"
            }
            
            # Rendu du template HTML
            html_message = render_to_string('accounts/emails/rejet.html', context)
            plain_message = strip_tags(html_message)
            
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                recipient_list=[demande.email_admin],
                html_message=html_message,
                fail_silently=False
            )
            
        except Exception as e:
            # Log l'erreur mais ne fait pas échouer le rejet
            print(f"Erreur lors de l'envoi de l'email de rejet: {e}")


class NotificationService:
    """Service pour les notifications aux super admins"""
    
    @staticmethod
    def notifier_nouvelle_demande(demande: DemandeInscription):
        """Notifie les super admins d'une nouvelle demande"""
        
        try:
            # Récupérer tous les super admins
            super_admins = User.objects.filter(is_superuser=True, is_active=True)
            
            if not super_admins.exists():
                return
            
            subject = f"Nouvelle demande d'inscription - {demande.nom_organisation}"
            
            # Contexte pour le template
            context = {
                'demande': demande,
                'admin_url': f"{settings.SITE_URL}/admin/accounts/demandeinscription/{demande.pk}/change/" if hasattr(settings, 'SITE_URL') else f"http://localhost:8000/admin/accounts/demandeinscription/{demande.pk}/change/"
            }
            
            # Rendu du template HTML
            html_message = render_to_string('accounts/emails/nouvelle_demande.html', context)
            plain_message = strip_tags(html_message)
            
            # Envoyer à tous les super admins
            recipient_list = [admin.email for admin in super_admins if admin.email]
            
            if recipient_list:
                send_mail(
                    subject=subject,
                    message=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                    recipient_list=recipient_list,
                    html_message=html_message,
                    fail_silently=False
                )
                
        except Exception as e:
            # Log l'erreur mais ne fait pas échouer la création de la demande
            print(f"Erreur lors de l'envoi de la notification aux super admins: {e}")
