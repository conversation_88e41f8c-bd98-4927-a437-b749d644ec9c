!function(n,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e=t();for(var r in e)("object"==typeof exports?exports:n)[r]=e[r]}}(self,(function(){return function(){"use strict";var __webpack_modules__={"./libs/notyf/notyf.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notyf: function() { return /* reexport safe */ notyf__WEBPACK_IMPORTED_MODULE_0__.Notyf; }\n/* harmony export */ });\n/* harmony import */ var notyf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! notyf */ "./node_modules/notyf/notyf.es.js");\n\ntry {\n  window.Notyf = notyf__WEBPACK_IMPORTED_MODULE_0__.Notyf;\n} catch (e) {}\n\n\n//# sourceURL=webpack://Materialize/./libs/notyf/notyf.js?')},"./node_modules/notyf/notyf.es.js":function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_OPTIONS: function() { return /* binding */ DEFAULT_OPTIONS; },\n/* harmony export */   Notyf: function() { return /* binding */ Notyf; },\n/* harmony export */   NotyfArray: function() { return /* binding */ NotyfArray; },\n/* harmony export */   NotyfArrayEvent: function() { return /* binding */ NotyfArrayEvent; },\n/* harmony export */   NotyfEvent: function() { return /* binding */ NotyfEvent; },\n/* harmony export */   NotyfNotification: function() { return /* binding */ NotyfNotification; },\n/* harmony export */   NotyfView: function() { return /* binding */ NotyfView; }\n/* harmony export */ });\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar NotyfNotification = /** @class */ (function () {\r\n    function NotyfNotification(options) {\r\n        this.options = options;\r\n        this.listeners = {};\r\n    }\r\n    NotyfNotification.prototype.on = function (eventType, cb) {\r\n        var callbacks = this.listeners[eventType] || [];\r\n        this.listeners[eventType] = callbacks.concat([cb]);\r\n    };\r\n    NotyfNotification.prototype.triggerEvent = function (eventType, event) {\r\n        var _this = this;\r\n        var callbacks = this.listeners[eventType] || [];\r\n        callbacks.forEach(function (cb) { return cb({ target: _this, event: event }); });\r\n    };\r\n    return NotyfNotification;\r\n}());\r\nvar NotyfArrayEvent;\r\n(function (NotyfArrayEvent) {\r\n    NotyfArrayEvent[NotyfArrayEvent[\"Add\"] = 0] = \"Add\";\r\n    NotyfArrayEvent[NotyfArrayEvent[\"Remove\"] = 1] = \"Remove\";\r\n})(NotyfArrayEvent || (NotyfArrayEvent = {}));\r\nvar NotyfArray = /** @class */ (function () {\r\n    function NotyfArray() {\r\n        this.notifications = [];\r\n    }\r\n    NotyfArray.prototype.push = function (elem) {\r\n        this.notifications.push(elem);\r\n        this.updateFn(elem, NotyfArrayEvent.Add, this.notifications);\r\n    };\r\n    NotyfArray.prototype.splice = function (index, num) {\r\n        var elem = this.notifications.splice(index, num)[0];\r\n        this.updateFn(elem, NotyfArrayEvent.Remove, this.notifications);\r\n        return elem;\r\n    };\r\n    NotyfArray.prototype.indexOf = function (elem) {\r\n        return this.notifications.indexOf(elem);\r\n    };\r\n    NotyfArray.prototype.onUpdate = function (fn) {\r\n        this.updateFn = fn;\r\n    };\r\n    return NotyfArray;\r\n}());\n\nvar NotyfEvent;\r\n(function (NotyfEvent) {\r\n    NotyfEvent[\"Dismiss\"] = \"dismiss\";\r\n    NotyfEvent[\"Click\"] = \"click\";\r\n})(NotyfEvent || (NotyfEvent = {}));\r\nvar DEFAULT_OPTIONS = {\r\n    types: [\r\n        {\r\n            type: 'success',\r\n            className: 'notyf__toast--success',\r\n            backgroundColor: '#3dc763',\r\n            icon: {\r\n                className: 'notyf__icon--success',\r\n                tagName: 'i',\r\n            },\r\n        },\r\n        {\r\n            type: 'error',\r\n            className: 'notyf__toast--error',\r\n            backgroundColor: '#ed3d3d',\r\n            icon: {\r\n                className: 'notyf__icon--error',\r\n                tagName: 'i',\r\n            },\r\n        },\r\n    ],\r\n    duration: 2000,\r\n    ripple: true,\r\n    position: {\r\n        x: 'right',\r\n        y: 'bottom',\r\n    },\r\n    dismissible: false,\r\n};\n\nvar NotyfView = /** @class */ (function () {\r\n    function NotyfView() {\r\n        this.notifications = [];\r\n        this.events = {};\r\n        this.X_POSITION_FLEX_MAP = {\r\n            left: 'flex-start',\r\n            center: 'center',\r\n            right: 'flex-end',\r\n        };\r\n        this.Y_POSITION_FLEX_MAP = {\r\n            top: 'flex-start',\r\n            center: 'center',\r\n            bottom: 'flex-end',\r\n        };\r\n        // Creates the main notifications container\r\n        var docFrag = document.createDocumentFragment();\r\n        var notyfContainer = this._createHTMLElement({ tagName: 'div', className: 'notyf' });\r\n        docFrag.appendChild(notyfContainer);\r\n        document.body.appendChild(docFrag);\r\n        this.container = notyfContainer;\r\n        // Identifies the main animation end event\r\n        this.animationEndEventName = this._getAnimationEndEventName();\r\n        this._createA11yContainer();\r\n    }\r\n    NotyfView.prototype.on = function (event, cb) {\r\n        var _a;\r\n        this.events = __assign(__assign({}, this.events), (_a = {}, _a[event] = cb, _a));\r\n    };\r\n    NotyfView.prototype.update = function (notification, type) {\r\n        if (type === NotyfArrayEvent.Add) {\r\n            this.addNotification(notification);\r\n        }\r\n        else if (type === NotyfArrayEvent.Remove) {\r\n            this.removeNotification(notification);\r\n        }\r\n    };\r\n    NotyfView.prototype.removeNotification = function (notification) {\r\n        var _this = this;\r\n        var renderedNotification = this._popRenderedNotification(notification);\r\n        var node;\r\n        if (!renderedNotification) {\r\n            return;\r\n        }\r\n        node = renderedNotification.node;\r\n        node.classList.add('notyf__toast--disappear');\r\n        var handleEvent;\r\n        node.addEventListener(this.animationEndEventName, (handleEvent = function (event) {\r\n            if (event.target === node) {\r\n                node.removeEventListener(_this.animationEndEventName, handleEvent);\r\n                _this.container.removeChild(node);\r\n            }\r\n        }));\r\n    };\r\n    NotyfView.prototype.addNotification = function (notification) {\r\n        var node = this._renderNotification(notification);\r\n        this.notifications.push({ notification: notification, node: node });\r\n        // For a11y purposes, we still want to announce that there's a notification in the screen\r\n        // even if it comes with no message.\r\n        this._announce(notification.options.message || 'Notification');\r\n    };\r\n    NotyfView.prototype._renderNotification = function (notification) {\r\n        var _a;\r\n        var card = this._buildNotificationCard(notification);\r\n        var className = notification.options.className;\r\n        if (className) {\r\n            (_a = card.classList).add.apply(_a, className.split(' '));\r\n        }\r\n        this.container.appendChild(card);\r\n        return card;\r\n    };\r\n    NotyfView.prototype._popRenderedNotification = function (notification) {\r\n        var idx = -1;\r\n        for (var i = 0; i < this.notifications.length && idx < 0; i++) {\r\n            if (this.notifications[i].notification === notification) {\r\n                idx = i;\r\n            }\r\n        }\r\n        if (idx !== -1) {\r\n            return this.notifications.splice(idx, 1)[0];\r\n        }\r\n        return;\r\n    };\r\n    NotyfView.prototype.getXPosition = function (options) {\r\n        var _a;\r\n        return ((_a = options === null || options === void 0 ? void 0 : options.position) === null || _a === void 0 ? void 0 : _a.x) || 'right';\r\n    };\r\n    NotyfView.prototype.getYPosition = function (options) {\r\n        var _a;\r\n        return ((_a = options === null || options === void 0 ? void 0 : options.position) === null || _a === void 0 ? void 0 : _a.y) || 'bottom';\r\n    };\r\n    NotyfView.prototype.adjustContainerAlignment = function (options) {\r\n        var align = this.X_POSITION_FLEX_MAP[this.getXPosition(options)];\r\n        var justify = this.Y_POSITION_FLEX_MAP[this.getYPosition(options)];\r\n        var style = this.container.style;\r\n        style.setProperty('justify-content', justify);\r\n        style.setProperty('align-items', align);\r\n    };\r\n    NotyfView.prototype._buildNotificationCard = function (notification) {\r\n        var _this = this;\r\n        var options = notification.options;\r\n        var iconOpts = options.icon;\r\n        // Adjust container according to position (e.g. top-left, bottom-center, etc)\r\n        this.adjustContainerAlignment(options);\r\n        // Create elements\r\n        var notificationElem = this._createHTMLElement({ tagName: 'div', className: 'notyf__toast' });\r\n        var ripple = this._createHTMLElement({ tagName: 'div', className: 'notyf__ripple' });\r\n        var wrapper = this._createHTMLElement({ tagName: 'div', className: 'notyf__wrapper' });\r\n        var message = this._createHTMLElement({ tagName: 'div', className: 'notyf__message' });\r\n        message.innerHTML = options.message || '';\r\n        var mainColor = options.background || options.backgroundColor;\r\n        // Build the icon and append it to the card\r\n        if (iconOpts) {\r\n            var iconContainer = this._createHTMLElement({ tagName: 'div', className: 'notyf__icon' });\r\n            if (typeof iconOpts === 'string' || iconOpts instanceof String)\r\n                iconContainer.innerHTML = new String(iconOpts).valueOf();\r\n            if (typeof iconOpts === 'object') {\r\n                var _a = iconOpts.tagName, tagName = _a === void 0 ? 'i' : _a, className_1 = iconOpts.className, text = iconOpts.text, _b = iconOpts.color, color = _b === void 0 ? mainColor : _b;\r\n                var iconElement = this._createHTMLElement({ tagName: tagName, className: className_1, text: text });\r\n                if (color)\r\n                    iconElement.style.color = color;\r\n                iconContainer.appendChild(iconElement);\r\n            }\r\n            wrapper.appendChild(iconContainer);\r\n        }\r\n        wrapper.appendChild(message);\r\n        notificationElem.appendChild(wrapper);\r\n        // Add ripple if applicable, else just paint the full toast\r\n        if (mainColor) {\r\n            if (options.ripple) {\r\n                ripple.style.background = mainColor;\r\n                notificationElem.appendChild(ripple);\r\n            }\r\n            else {\r\n                notificationElem.style.background = mainColor;\r\n            }\r\n        }\r\n        // Add dismiss button\r\n        if (options.dismissible) {\r\n            var dismissWrapper = this._createHTMLElement({ tagName: 'div', className: 'notyf__dismiss' });\r\n            var dismissButton = this._createHTMLElement({\r\n                tagName: 'button',\r\n                className: 'notyf__dismiss-btn',\r\n            });\r\n            dismissWrapper.appendChild(dismissButton);\r\n            wrapper.appendChild(dismissWrapper);\r\n            notificationElem.classList.add(\"notyf__toast--dismissible\");\r\n            dismissButton.addEventListener('click', function (event) {\r\n                var _a, _b;\r\n                (_b = (_a = _this.events)[NotyfEvent.Dismiss]) === null || _b === void 0 ? void 0 : _b.call(_a, { target: notification, event: event });\r\n                event.stopPropagation();\r\n            });\r\n        }\r\n        notificationElem.addEventListener('click', function (event) { var _a, _b; return (_b = (_a = _this.events)[NotyfEvent.Click]) === null || _b === void 0 ? void 0 : _b.call(_a, { target: notification, event: event }); });\r\n        // Adjust margins depending on whether its an upper or lower notification\r\n        var className = this.getYPosition(options) === 'top' ? 'upper' : 'lower';\r\n        notificationElem.classList.add(\"notyf__toast--\" + className);\r\n        return notificationElem;\r\n    };\r\n    NotyfView.prototype._createHTMLElement = function (_a) {\r\n        var tagName = _a.tagName, className = _a.className, text = _a.text;\r\n        var elem = document.createElement(tagName);\r\n        if (className) {\r\n            elem.className = className;\r\n        }\r\n        elem.textContent = text || null;\r\n        return elem;\r\n    };\r\n    /**\r\n     * Creates an invisible container which will announce the notyfs to\r\n     * screen readers\r\n     */\r\n    NotyfView.prototype._createA11yContainer = function () {\r\n        var a11yContainer = this._createHTMLElement({ tagName: 'div', className: 'notyf-announcer' });\r\n        a11yContainer.setAttribute('aria-atomic', 'true');\r\n        a11yContainer.setAttribute('aria-live', 'polite');\r\n        // Set the a11y container to be visible hidden. Can't use display: none as\r\n        // screen readers won't read it.\r\n        a11yContainer.style.border = '0';\r\n        a11yContainer.style.clip = 'rect(0 0 0 0)';\r\n        a11yContainer.style.height = '1px';\r\n        a11yContainer.style.margin = '-1px';\r\n        a11yContainer.style.overflow = 'hidden';\r\n        a11yContainer.style.padding = '0';\r\n        a11yContainer.style.position = 'absolute';\r\n        a11yContainer.style.width = '1px';\r\n        a11yContainer.style.outline = '0';\r\n        document.body.appendChild(a11yContainer);\r\n        this.a11yContainer = a11yContainer;\r\n    };\r\n    /**\r\n     * Announces a message to screenreaders.\r\n     */\r\n    NotyfView.prototype._announce = function (message) {\r\n        var _this = this;\r\n        this.a11yContainer.textContent = '';\r\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\r\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\r\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\r\n        //   second time without clearing and then using a non-zero delay.\r\n        // (using JAWS 17 at time of this writing).\r\n        // https://github.com/angular/material2/blob/master/src/cdk/a11y/live-announcer/live-announcer.ts\r\n        setTimeout(function () {\r\n            _this.a11yContainer.textContent = message;\r\n        }, 100);\r\n    };\r\n    /**\r\n     * Determine which animationend event is supported\r\n     */\r\n    NotyfView.prototype._getAnimationEndEventName = function () {\r\n        var el = document.createElement('_fake');\r\n        var transitions = {\r\n            MozTransition: 'animationend',\r\n            OTransition: 'oAnimationEnd',\r\n            WebkitTransition: 'webkitAnimationEnd',\r\n            transition: 'animationend',\r\n        };\r\n        var t;\r\n        for (t in transitions) {\r\n            if (el.style[t] !== undefined) {\r\n                return transitions[t];\r\n            }\r\n        }\r\n        // No supported animation end event. Using \"animationend\" as a fallback\r\n        return 'animationend';\r\n    };\r\n    return NotyfView;\r\n}());\n\n/**\r\n * Main controller class. Defines the main Notyf API.\r\n */\r\nvar Notyf = /** @class */ (function () {\r\n    function Notyf(opts) {\r\n        var _this = this;\r\n        this.dismiss = this._removeNotification;\r\n        this.notifications = new NotyfArray();\r\n        this.view = new NotyfView();\r\n        var types = this.registerTypes(opts);\r\n        this.options = __assign(__assign({}, DEFAULT_OPTIONS), opts);\r\n        this.options.types = types;\r\n        this.notifications.onUpdate(function (elem, type) { return _this.view.update(elem, type); });\r\n        this.view.on(NotyfEvent.Dismiss, function (_a) {\r\n            var target = _a.target, event = _a.event;\r\n            _this._removeNotification(target);\r\n            // tslint:disable-next-line: no-string-literal\r\n            target['triggerEvent'](NotyfEvent.Dismiss, event);\r\n        });\r\n        // tslint:disable-next-line: no-string-literal\r\n        this.view.on(NotyfEvent.Click, function (_a) {\r\n            var target = _a.target, event = _a.event;\r\n            return target['triggerEvent'](NotyfEvent.Click, event);\r\n        });\r\n    }\r\n    Notyf.prototype.error = function (payload) {\r\n        var options = this.normalizeOptions('error', payload);\r\n        return this.open(options);\r\n    };\r\n    Notyf.prototype.success = function (payload) {\r\n        var options = this.normalizeOptions('success', payload);\r\n        return this.open(options);\r\n    };\r\n    Notyf.prototype.open = function (options) {\r\n        var defaultOpts = this.options.types.find(function (_a) {\r\n            var type = _a.type;\r\n            return type === options.type;\r\n        }) || {};\r\n        var config = __assign(__assign({}, defaultOpts), options);\r\n        this.assignProps(['ripple', 'position', 'dismissible'], config);\r\n        var notification = new NotyfNotification(config);\r\n        this._pushNotification(notification);\r\n        return notification;\r\n    };\r\n    Notyf.prototype.dismissAll = function () {\r\n        while (this.notifications.splice(0, 1))\r\n            ;\r\n    };\r\n    /**\r\n     * Assigns properties to a config object based on two rules:\r\n     * 1. If the config object already sets that prop, leave it as so\r\n     * 2. Otherwise, use the default prop from the global options\r\n     *\r\n     * It's intended to build the final config object to open a notification. e.g. if\r\n     * 'dismissible' is not set, then use the value from the global config.\r\n     *\r\n     * @param props - properties to be assigned to the config object\r\n     * @param config - object whose properties need to be set\r\n     */\r\n    Notyf.prototype.assignProps = function (props, config) {\r\n        var _this = this;\r\n        props.forEach(function (prop) {\r\n            // intentional double equality to check for both null and undefined\r\n            config[prop] = config[prop] == null ? _this.options[prop] : config[prop];\r\n        });\r\n    };\r\n    Notyf.prototype._pushNotification = function (notification) {\r\n        var _this = this;\r\n        this.notifications.push(notification);\r\n        var duration = notification.options.duration !== undefined ? notification.options.duration : this.options.duration;\r\n        if (duration) {\r\n            setTimeout(function () { return _this._removeNotification(notification); }, duration);\r\n        }\r\n    };\r\n    Notyf.prototype._removeNotification = function (notification) {\r\n        var index = this.notifications.indexOf(notification);\r\n        if (index !== -1) {\r\n            this.notifications.splice(index, 1);\r\n        }\r\n    };\r\n    Notyf.prototype.normalizeOptions = function (type, payload) {\r\n        var options = { type: type };\r\n        if (typeof payload === 'string') {\r\n            options.message = payload;\r\n        }\r\n        else if (typeof payload === 'object') {\r\n            options = __assign(__assign({}, options), payload);\r\n        }\r\n        return options;\r\n    };\r\n    Notyf.prototype.registerTypes = function (opts) {\r\n        var incomingTypes = ((opts && opts.types) || []).slice();\r\n        var finalDefaultTypes = DEFAULT_OPTIONS.types.map(function (defaultType) {\r\n            // find if there's a default type within the user input's types, if so, it means the user\r\n            // wants to change some of the default settings\r\n            var userTypeIdx = -1;\r\n            incomingTypes.forEach(function (t, idx) {\r\n                if (t.type === defaultType.type)\r\n                    userTypeIdx = idx;\r\n            });\r\n            var userType = userTypeIdx !== -1 ? incomingTypes.splice(userTypeIdx, 1)[0] : {};\r\n            return __assign(__assign({}, defaultType), userType);\r\n        });\r\n        return finalDefaultTypes.concat(incomingTypes);\r\n    };\r\n    return Notyf;\r\n}());\n\n\n\n\n//# sourceURL=webpack://Materialize/./node_modules/notyf/notyf.es.js?")}},__webpack_module_cache__={};function __webpack_require__(n){var t=__webpack_module_cache__[n];if(void 0!==t)return t.exports;var e=__webpack_module_cache__[n]={exports:{}};return __webpack_modules__[n](e,e.exports,__webpack_require__),e.exports}__webpack_require__.d=function(n,t){for(var e in t)__webpack_require__.o(t,e)&&!__webpack_require__.o(n,e)&&Object.defineProperty(n,e,{enumerable:!0,get:t[e]})},__webpack_require__.o=function(n,t){return Object.prototype.hasOwnProperty.call(n,t)},__webpack_require__.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./libs/notyf/notyf.js");return __webpack_exports__}()}));